Image /page/0/Picture/0 description: A square button with rounded corners has a circular icon and text. The icon is a circle with a red bookmark shape inside. The circle is divided into a yellow arc on the left and a teal arc on the right. Below the icon, the text reads "Check for updates" in a gray sans-serif font.

# Distributed Boosting: An Enhancing Method on Dataset **Distillation**

<PERSON><PERSON><PERSON> Chen Z<PERSON>jiang University Hangzhou, Zhejiang, China <EMAIL>

> <span id="page-0-0"></span><PERSON><PERSON><PERSON>hua University Beijing, China <EMAIL>

Wenchao Meng Zhejiang University Hangzhou, Zhejiang, China <EMAIL>

<PERSON><PERSON> University Hangzhou, Zhejiang, China <EMAIL>

Image /page/0/Picture/6 description: The image depicts a machine learning process involving data distribution, model distillation, and integration. The process begins with a dataset being distributed and encapsulated into multiple client models. Each client model then undergoes distillation, producing intermediate representations. These representations are then integrated and mixed with relabeling. The final stage involves combining a model list with soft labels to generate synthetic data and soft labels, denoted as . The diagram illustrates a multi-stage workflow common in federated learning or knowledge distillation scenarios.

Figure 1: Distributed Boosting.

## ABSTRACT

Dataset Distillation (DD) is a technique for synthesizing smaller, compressed datasets from large original datasets while retaining essential information to maintain efficacy. Efficient DD is a current research focus among scholars. Squeeze, Recover and Relabel (SRe<sup>2</sup>L) and Adversarial Prediction Matching (APM) are two advanced and efficient DD methods, yet their performance is moderate with lower volumes of distilled data. This paper proposes an ingenious improvement method, Distributed Boosting (DB), capable of significantly enhancing the performance of these two algorithms at low distillation volumes, leading to DB-SRe<sup>2</sup>L and DB-APM. Specifically, DB is divided into three stages: Distribute&Encapsulate, Distill, and Integrate&Mix-relabel.  $DB-SRe<sup>2</sup>L$ , compared to  $SRe<sup>2</sup>L$ , demonstrates performance improvements of 25.2%, 26.9%, and 26.2% on full 224×224 ImageNet-1k at Images Per Class (IPC) 10, CIFAR-10 at IPC 10, and CIFAR-10 at IPC 50, respectively. Meanwhile, DB-APM, in comparison to APM, exhibits performance enhancements of 21.2% and 20.9% on CIFAR-10 at IPC 10, CIFAR-100 at IPC 1, respectively. Additionally, we provide a theoretical proof of convergence for DB. To the best of our knowledge, DB is the first method suitable for distributed parallel computing scenarios.

CIKM '24, October 21–25, 2024, Boise, ID, USA.

© 2024 Copyright held by the owner/author(s). Publication rights licensed to ACM. ACM ISBN 979-8-4007-0436-9/24/10 <https://doi.org/10.1145/3627673.3679897>

## CCS CONCEPTS

• Computing methodologies  $\rightarrow$  Distributed artificial intelligence; Computer vision; Machine learning; Distributed algorithms; Parallel algorithms.

## KEYWORDS

Dataset Distillation, Distributed Dataset Distillation

#### ACM Reference Format:

Xuechao Chen, Wenchao Meng, Peiran Wang, and Qihang Zhou. 2024. Distributed Boosting: An Enhancing Method on Dataset Distillation. In Proceedings of the 33rd ACM International Conference on Information and Knowledge Management (CIKM '24), October 21–25, 2024, Boise, ID, USA. ACM, New York, NY, USA, [5](#page-4-0) pages.<https://doi.org/10.1145/3627673.3679897>

# 1 INTRODUCTION

In recent years, Dataset Distillation (DD) [\[24\]](#page-4-1) has attracted considerable attention in the realms of computer vision [\[2,](#page-4-2) [5,](#page-4-3) [12,](#page-4-4) [25\]](#page-4-5) and natural language processing [\[14,](#page-4-6) [22\]](#page-4-7). This endeavor seeks to refine the methodology of condensing voluminous datasets into more compact yet epitomizing subsets, maintaining quintessential attributes and features, thereby facilitating models to assimilate knowledge as efficaciously from the distilled datasets as they would from the expansive original datasets. These datasets have emerged as a popular selection for diverse downstream applications, including Federated Learning [\[7,](#page-4-8) [18\]](#page-4-9), Continual Learning [\[15,](#page-4-10) [27\]](#page-4-11) and Neural Architecture Search [\[21\]](#page-4-12).

Through the implementation of intricate algorithms, such as meta-model matching [\[24,](#page-4-1) [31\]](#page-4-13), gradient matching [\[8,](#page-4-14) [10,](#page-4-15) [29\]](#page-4-16), distribution matching [\[23,](#page-4-17) [28\]](#page-4-18), and trajectory matching [\[2,](#page-4-2) [5\]](#page-4-3), significant strides have been made in data condensation. These solutions

Permission to make digital or hard copies of all or part of this work for personal or classroom use is granted without fee provided that copies are not made or distributed for profit or commercial advantage and that copies bear this notice and the full citation on the first page. Copyrights for components of this work owned by others than the author(s) must be honored. Abstracting with credit is permitted. To copy otherwise, or republish, to post on servers or to redistribute to lists, requires prior specific permission and/or a fee. Request <NAME_EMAIL>.

primarily aim to extract smaller datasets and have exhibited commendable performance in instances such as CIFAR, small images [\[9\]](#page-4-19), downscaled low-resolution images [\[4\]](#page-4-20), or a subset of ImageNet [\[8\]](#page-4-14). Nevertheless, all these excellent methods still incur substantial training overhead burdens on the entire 224×224 ImageNet-1k dataset [\[19\]](#page-4-21).

In an effort to mitigate issues pertaining to inefficiency, a cohort of algorithms demonstrating enhanced efficiency has been introduced recently [\[3,](#page-4-22) [19,](#page-4-21) [25\]](#page-4-5). The first instance of DD on the complete  $224 \times 224$  ImageNet-1k dataset, Squeeze, Recover and Relabel (SRe<sup>2</sup>L) [\[25\]](#page-4-5), was proposed, wherein a Top-1 validation accuracy of 21.3% was realized using a ResNet18 at Image Per Class (IPC) 10. IPC refers to the number of images per class obtained by DD in the context of image classification tasks. By decoupling bi-level optimization,  $SRe<sup>2</sup>L$  boasts a 16 $\times$  speed enhancement and a 13.6% performance improvement over the state-of-the-art approach, TESLA [\[5\]](#page-4-3), which operates on a low-resolution version of ImageNet-1k. Meanwhile, Adversarial Prediction Matching (APM) represents another efficacious DD algorithm. It circumvents the nested optimization challenges prevalent in various popular methodologies [\[5,](#page-4-3) [6,](#page-4-23) [12\]](#page-4-4) by employing an adversarial framework that mines critical point samples within the distribution of real data.

However, there exists room for improvement in SRe<sup>2</sup>L [\[25\]](#page-4-5) and APM [\[3\]](#page-4-22) when it comes to the distillation of low data volumes (e.g. SRe<sup>2</sup>L only achieves 2.0% at IPC 1 on CIFAR-100 and APM achieves 10.9% at IPC 1 on CIFAR-100 as Tabel [2](#page-3-0) shows). Enhanced methods following SRe2L like G-VBSM [\[19\]](#page-4-21) and WMDD [\[11\]](#page-4-24) are proposed then. In contrast to the complex strategies of G-VBSM, we aspire to enhance the performance of  $SRe<sup>2</sup>L$  and APM through a more simplified and universally applicable method.

To attain this objective, we introduce a streamlined method for enhancing the performance of SRe2L and APM, Distributed Boosting (DB) as Fig[.1.](#page-0-0) DB firstly partitions the original dataset for several clients, wherein the teacher model is trained on each distinct client and subsequently generates corresponding data. Nevertheless, partitioning the original dataset for distributed training leads to a decline in the quality of data synthesized by each client, and directly training with these inferior datasets does not fulfill our objective of better learning the real data distribution through multiple clients. To address this issue, since the soft labels obtained through the integration of multiple teachers effectively enhance the generalization capabilities of the student model in knowledge distillation [\[26\]](#page-4-25), we utilized a method where all clients' trained models randomly participate to soft-label the aggregated data by FKD [\[20\]](#page-4-26), thereby encapsulating more of the real dataset's effective information within the distillation data. This process culminates in the realization of two higher-performing algorithms: DB-SRe<sup>2</sup>L and DB-APM. Specifically, DB-SRe<sup>2</sup>L, compared to SRe<sup>2</sup>L, demonstrates performance improvements of 25.2%, 26.9%, and 26.2% on ImageNet at Images Per Class (IPC) 10, CIFAR-10 at IPC 10, and CIFAR-10 at IPC 50, respectively. Meanwhile, DB-APM, in comparison to APM, exhibits performance enhancements of 21.2% and 20.9% on CIFAR-10 at IPC 10, CIFAR-100 at IPC 1, respectively. We also provide a theoretical proof of convergence for the method. The major contributions are listed as follows:

1) We introduce DB for  $SRe^2L$  and APM, leading to DB- $SRe^2L$ and DB-APM. To the best of our knowledge, DB is the first DD approach applicable to distributed parallel computing scenarios. Experiments demonstrate that  $DB-SRe<sup>2</sup>L$  and  $DB-APM$  exhibit significant performance improvements in low data volumes compared to SRe2L and APM.

2) DB-SRe<sup>2</sup>L and DB-APM have achieved SOTA performance across multiple metrics. In comparison, we achieved an accuracy of 46.5% under ImageNet IPC10 with ResNet18, surpassing the leading G-VBSM 15.1%.

3) We analyzed the impact of introducing DB on the efficiency, investigated cross-architecture generalization experiments and studied the impact of varying client numbers on the performance of DB. Furthermore, we provide a theoretical proof of convergence for the DB.

## 2 BACKGROUND

## 2.1 Dataset Distillation

The purpose of dataset condensation is to derive a compact synthetic dataset encapsulating a significant quantum of the information inherent in the original data. Given a voluminous labeled dataset  $\mathcal{T} = \{(\bm{x}_1, \bm{y}_1), ..., (\bm{x}_{|\mathcal{T}|}, \bm{y}_{|\mathcal{T}|})\}$  where  $\bm{x}$  is the data and  $\bm{y}$  is the label, the goal is to synthesize a diminutive condensed dataset  $S = \{(\tilde{x}_1, \tilde{y}_1), ..., (\tilde{x}_{|S|}, \tilde{y}_{|S|})\}(|S| \ll |\mathcal{T}|)$  that retains the pivotal information from the original  $\mathcal T$ . The learning objective with respect to this synthetic condensed data is:

$$
\theta_{\mathcal{S}} = \underset{\theta}{\arg\min} \mathcal{L}_{\mathcal{S}}(\theta) \tag{1}
$$

where  $\argmin_{\mathcal{L}_{\mathcal{S}}(\bm{\theta})} \mathcal{L}_{\mathcal{S}}(\bm{\theta}) = \mathbb{E}_{(\tilde{\bm{x}}, \tilde{\bm{y}}) \in \mathcal{S}} [\ell(\phi_{\bm{\theta}_{\mathcal{S}}}(\tilde{\bm{x}}), \tilde{\bm{y}})], \tilde{\bm{y}} \text{ is the soft label}$ corresponding to the synthetic data  $\tilde{\boldsymbol{x}}, \boldsymbol{\theta}$  is the parameters of the

model  $\phi$ .

Adhering to the conceptual framework of coresets [\[1\]](#page-4-27) and  $\epsilon$ approximate [\[16\]](#page-4-28), the objective of the data condensation task can be formulated as:

$$
\sup\{|\ell(\phi_{\theta_{\mathcal{T}}}(x),y) - \ell(\phi_{\theta_{\mathcal{S}}}(x),y)|\}_{(x,y)\sim\mathcal{T}} \leq \epsilon \tag{2}
$$

where  $\ell$  is the loss function,  $\phi_{\theta}$  is the model trained on the dataset  $\mathcal T$  or  $\mathcal S$ ,  $\epsilon$  represents the performance differential for models trained on the synthetic dataset versus the original comprehensive dataset. Consequently, our goal is to optimize the synthetic dataset  $S$  via:

$$
\underset{S,|S|}{\arg\min}(\sup\{|\ell(\phi_{\theta_{\mathcal{T}}}(x),y)-\ell(\phi_{\theta_{\mathcal{S}}}(x),y)|\}_{(x,y)\sim\mathcal{T}})
$$
(3)

Subsequently, we can acquire  $\langle$  data, label  $\rangle \in S$ , accompanied by the corresponding quantity of condensed data in each class.

# 3 APPROACH

#### 3.1 Distributed Boosting

To alleviate the inherent constraints in  $SRe^{2}L$  [\[25\]](#page-4-5) and APM [\[3\]](#page-4-22), specifically the suboptimal quality generation in low DD data volume scenarios, a distributed boosting methodology has been implemented, leading to DB-SRe<sup>2</sup>L and DB-APM. DB is consist of three stages as Distribute&Encapsulate, Distill and Integrate&Mixrelabel.

Stage.1 Distribute&Encapsulate. In this stage, the primordial dataset  $\mathcal{T} = \{(\bm{x}_1, \bm{y}_1), ..., (\bm{x}_{|\mathcal{T}|}, \bm{y}_{|\mathcal{T}|})\}$  is evenly allocated across N

Distributed Boosting: An Enhancing Method on Dataset Distillation CIKM '24, October 21–25, 2024, Boise, ID, USA.

clients, each affiliated with the predetermined model for encapsulating information purposes. To validate the efficacy and convenience of our method, we employed a random selection allocation approach at this stage. In subsequent experimental analyses, we hypothesize that employing a more rational and refined data allocation method, such as [\[30\]](#page-4-29), could further enhance the performance of DB.

Stage.2 Distill. Subsequent to the encapsulation phase on clients' respective data  $\mathcal{T}_n, n \in \{1, 2, ..., N\}, |\mathcal{T}_n| = \frac{|\mathcal{T}|}{N}$ , the encapsulated model  $\theta_{\mathcal{T}_n}$  of each client engenders distilled data  $\mathcal{S}_n$ . In this stage, DB-SRe2L executes the Recover stage in SRe2L as [\(4\)](#page-2-0) and DB-APM executes [\(5\)](#page-2-1) in APM to achieve distilled/synthetic data  $\tilde{\pmb{x}}_i.$ 

<span id="page-2-0"></span>
$$
\arg\min_{S,|S|} \ell(\phi_{\theta_{\mathcal{T}}}(\tilde{\mathbf{x}}), \mathbf{y}) + \mathcal{R}_{BN}(\tilde{\mathbf{x}}) \tag{4}
$$

where  $\mathcal{R}_{BN}(\tilde{\boldsymbol{x}})$  denotes the BN Trajectory Matching Loss.  $\phi_{\boldsymbol{\theta}_\mathcal{T}}$  represents the model pre-trained during the initial stage, which remains frozen in this phase.

$$
\mathcal{L}_{syn} = \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \sum_{e=0}^{E-1} (-\log[d(\phi_{\theta_{\mathcal{T}}}(\tilde{x}_i), \phi_{\theta_{\mathcal{S}}^e}(\tilde{x}_i))]
$$
$$
+ \alpha H(\tilde{y}_{\tilde{x}_i}, p(\tilde{y}_{\tilde{x}_i}|\tilde{x}_i; \theta_{\mathcal{T}})))
$$
(5)

Where  $H(\tilde{y}_{\tilde{x}_i}, p(\tilde{y}_{\tilde{x}_i} | \tilde{x}_i; \theta_{\mathcal{T}})) = -\log(p(\tilde{y}_{\tilde{x}_i} | \tilde{x}_i; \theta_{\mathcal{T}}))$  signifies the cross-entropy loss incurred by  $\theta_{\mathcal{T}}$  with respect to  $\tilde{x}_i$ , concerning the corresponding distilling class  $\tilde{y}_{\tilde{x}_i}$ ,  $\alpha$  denotes a hyperparameter facilitating a flexible equilibrium,  $E$  represents the total number of training epochs and  $d(.,.)$  symbolizes a metric for quantifying distance.

Stage.3 Integrate&Mix-relabel. Nonetheless, segmenting the initial data corpus into clients for training precipitates a diminution in the fidelity of the data synthesized by each computational unit. Direct engagement in training with these suboptimal data assemblages fails to achieve the quintessential goal of augmenting comprehension of the inherent data distribution via multiple algorithmic models. To mitigate this quandary, we implemented a stratagem wherein each participant's computational model is harnessed to bestow soft labels upon the distilled data  $\tilde{x}_i$  by FKD [\[20\]](#page-4-26). Specifically, following the regular random-crop resize training strategy, we randomly crop some regions from one image and employ other augmentations like flipping on them, then input these regions into the teachers to generate the corresponding soft label vectors. We store all the region coordinates and augmentation hyper-parameters with the soft label for the following training phase. This technique facilitates the encapsulation of a more substantial portion of the data corpus's efficacious information within the resultant compressed data compilations. In this stage, each client's data and encapsulated models are integrated. For Mix-relabel, subsequent to the aggregation of the distilled data generated at each client, we employ a random selection from the list of all clients' encapsulated models for bestowing soft labels by FKD upon each crop:

$$
\tilde{\boldsymbol{y}}_i^n = \phi_{\theta_{\mathcal{T}_k}}(\tilde{\boldsymbol{x}}_{R_i}^n)
$$
\n(6)

where  $\tilde{x}^n_{\vec{k}}$  $\frac{n}{R_i}$  represents the *i*-th crop of the synthetic image from  $S_n$ ,  $\tilde{\boldsymbol{y}}_i^n$  denotes the corresponding soft label and  $\theta_{\mathcal{T}_k}$  is randomly selected from  $\{\theta_{\mathcal{T}_1}, \theta_{\mathcal{T}_2}, ..., \theta_{\mathcal{T}_n}\}$ . Subsequently, the model  $\phi_{\bm{\theta}_{\mathcal{S}}}$  can be

CIKM '24, October 21–25, 2024, Boise, ID, USA.

trained on the integrated synthetic dataset, utilizing the subsequent objective:

$$
\mathcal{L}_{syn} = -\sum_{n} \sum_{i} \tilde{\boldsymbol{y}}_{i}^{n} \log \phi_{\theta_{S}}(\tilde{\boldsymbol{x}}_{R_{i}}^{n})
$$
(7)

Impact of DB on the efficiency of  $SRe<sup>2</sup>L$  and APM. Since we have partitioned the original dataset, the total data volume through all clients does not increase, making the efficiency impact of our Distribute & Encapsulate stage very minimal compared to the original algorithms. In multi-GPU distributed computing scenarios, the simplest form of distributed generation involves equipping each client with a complete dataset to independently perform dataset compression algorithms and then aggregate the results. Our DB offers higher efficiency compared to simply having each client independently perform dataset compression algorithms because the data volume for each client in our method is 1/N of that in the simple independent distribution approach. In summary, the impact of our DB on the efficiency of the original algorithms is negligible on a single GPU, while in multi-GPU distributed computing scenarios, DB can adapt to this context and enhance the computational efficiency of the original algorithms to the greatest extent.

#### <span id="page-2-1"></span>3.2 Theorem on Convergence

The purpose of this convergence proof is to prove the convergence of the relabel stage of this scheme, that is, the loss labeled in the relabel stage converges to the bound and has better convergence than local dist-squeeze.

We make the following assumptions:

**Assumption 1: Non-convex loss function**: Assume that  $\mathcal{L}_{\mathcal{T},c}(\theta)$ is non-convex for all  $n$ , but satisfies certain smoothness conditions.

Assumption 2: Bounded gradient: Keeping the null hypothesis unchanged, there is a constant G such that for all  $n$  and  $\theta$ , there is  $\|\nabla \mathcal{L}_{\mathcal{T},c}(\theta)\|$  ≤ G.

Assumption 3: Appropriate learning rate: The setting of the learning rate  $\eta_t$  remains unchanged and satisfies the Robbins-Monro condition.

**Theorem 1:** For DB, given a constant  $B$ , the total number of iterations  $T$ , the bound of the update on the distilled dataset is:

$$
\min_{t \in \{1, \dots, T\}} \mathbb{E}\left[\|\nabla \mathcal{L}_{\mathcal{T}}(\theta^{(t)})\|^2\right] \le \frac{B}{\sqrt{T}}\tag{8}
$$

## 4 EXPERIMENTS

### 4.1 Large-Scale Dataset Comparison

In this experiment, DataDAM [\[17\]](#page-4-30) and TESLA [\[5\]](#page-4-3) use the downsampled 64×64 ImageNet-1k. Except for G-VBSM [\[19\]](#page-4-21), 128-width ConvNet(CW128), ResNet18, and ResNet50 denote the model of data synthesis and evaluation. G-VBSM employs a set of models {ResNet18/50, MobileNetV2, EfficientNet-B0, ShuffleNetV2-0.5} for data synthesis. Experimental results are cited from G-VBSM [\[19\]](#page-4-21).

Full 224×224 ImageNet-1k. As depicted in Table [1,](#page-3-1) DB-SRe2L consistently outperforms both  $SRe^{2}L$  and G-VBSM at IPC {10, 50}. Notably, in scenarios of extremely low data compression volumes, DB-SRe<sup>2</sup>L surpasses SRe<sup>2</sup>L at IPC10 by 25.2% and 19.9%, respectively, and also exceeds the large-data SOTA method G-VBSM by 15.1% and 12.9%. It is important to note that G-VBSM employs multiple distinct models for encapsulating knowledge and generating

<span id="page-3-1"></span>

|             | $\text{IPC}$ | CW128                           |  | ResNet18 |                                                                                                   | ResNet50 |                         |                                  |  |
|-------------|--------------|---------------------------------|--|----------|---------------------------------------------------------------------------------------------------|----------|-------------------------|----------------------------------|--|
| Dataset     |              |                                 |  |          | $\begin{array}{ l c c c c c c c c c c c c c c c c c c $                                           |          |                         |                                  |  |
| ImageNet-1k | 10<br>50     | $6.3 \pm 0.0$<br>$15.5 \pm 0.2$ |  |          | 7.7±0.1 21.3±0.6 31.4±0.5 46.5±0.5 28.4±0.1 35.4±0.8<br>$-46.8\pm0.2$ 51.8 $\pm0.4$ 53.8 $\pm0.3$ |          | $55.6 \pm 0.3$ 58.7±0.3 | $48.3 \pm 0.4$<br>$60.1 \pm 0.3$ |  |

Table 1: Comparison in ImageNet-1k

Table 2: Comparison in CIFAR

<span id="page-3-0"></span>

| Dataset   | IPC | CW128            |           |            |          |          |          |          |          |          |          |                  | ResNet18 |          |                  |
|-----------|-----|------------------|-----------|------------|----------|----------|----------|----------|----------|----------|----------|------------------|----------|----------|------------------|
|           |     | <b>RCIG</b>      | <b>DM</b> | <b>DSA</b> | FRePo    | MTT      | TESLA    | CAFE     | FTD      | DataDAM  | APM      | <b>DB-APM</b>    | SRe2L    | G-VBSM   | <b>DB-SR2L</b>   |
| CIFAR-10  | 10  | <b>69.1</b> ±0.4 | 49.2±0.8  | 53.2±0.8   | 65.5±0.6 | 65.3±0.7 | 66.4±0.8 | 46.3±0.6 | 66.6±0.3 | 54.2±0.8 | 41.5±0.4 | <b>62.7</b> ±0.3 | 27.2±0.5 | 53.5±0.6 | <b>54.1</b> ±0.2 |
| CIFAR-10  | 50  | <b>73.5</b> ±0.3 | 63.7±0.5  | 66.8±0.4   | 71.7±0.2 | 71.6±0.2 | 72.6±0.7 | 55.5±0.6 | 73.8±0.2 | 67.0±0.4 | 73.4±0.2 | <b>74.3</b> ±0.2 | 47.5±0.6 | 59.2±0.4 | <b>73.7</b> ±0.3 |
| CIFAR-100 | 1   | <b>39.3</b> ±0.4 | 12.2±0.4  | 16.8±0.2   | 27.2±0.4 | 24.3±0.3 | 24.8±0.4 | 12.9±0.3 | 25.2±0.2 | 14.5±0.5 | 10.9±0.2 | 31.8±0.5         | 2.0±0.2  | 25.9±0.5 | 18.7±0.4         |
| CIFAR-100 | 10  | <b>44.1</b> ±0.4 | 29.7±0.3  | 32.3±0.3   | 41.3±0.2 | 40.6±0.4 | 41.7±0.3 | 27.8±0.3 | 43.4±0.3 | 34.8±0.5 | 41.1±0.3 | <b>44.8</b> ±0.4 | 31.6±0.5 | 59.5±0.4 | 44.7±0.8         |

soft labels, followed by validation on a single model. In contrast, our method utilizes only a single model throughout the DD and validation processes, thereby demonstrating the efficiency of our approach. In terms of client count N,  $DB-SRe^2L$  employs N=2 and N=3 at IPC10 and 50, respectively. The experiments validate our method's exceptional performance on large-scale datasets.

### 4.2 Small-Scale Dataset Comparison

In CIFAR (Small-Scale Dataset) experiments, experimental results of DM [\[28\]](#page-4-18), DSA [\[27\]](#page-4-11), FRePo [\[32\]](#page-4-31), MTT [\[2\]](#page-4-2), TESLA [\[5\]](#page-4-3) are cited from TESLA [\[5\]](#page-4-3). Experimental results of CAFE [\[23\]](#page-4-17), FTD [\[6\]](#page-4-23) are cited from FTD [\[6\]](#page-4-23). Experimental results of RCIG are cited from [\[13\]](#page-4-32). Experimental results of DataDAM [\[17\]](#page-4-30), SRe<sup>2</sup>L [\[25\]](#page-4-5) and G-VBSM [\[19\]](#page-4-21) are cited from G-VBSM [\[19\]](#page-4-21). Except for G-VBSM [\[19\]](#page-4-21), 128-width ConvNet(CW128) and ResNet18 denote the model of data synthesis and evaluation. G-VBSM employs a set of models {CW128, WRN-16-2, ResNet18, ShuffleNetV2-0.5, MobileNetV2-0.5} for data synthesis.

CIFAR-10. In Table [2,](#page-3-0) compared to APM, DB-APM exhibits an average precision increase of 21.2% and 0.9% at IPC10 and IPC50, respectively, where the synthesis and validation employed a 128 width ConvNet (CW128). Although DB-APM did not achieve the highest precision at IPC, it was only 3.7% below the best result of FTD. In contrast, DB-SRe<sup>2</sup>L showed a significant improvement over SRe2L, with average precision increases of 26.9% and 26.2% at IPC10 and IPC50, respectively, utilizing a ResNet18 model for synthesis and validation. Despite the more refined strategies and different synthesis model architectures of G-VBSM, DB-SRe<sup>2</sup>L still demonstrated considerable advantages, particularly at IPC50, where it held a 14.5% lead. DB-APM used N=4 and N=2 at IPC10 and IPC50, respectively, while DB-SRe<sup>2</sup>L used N=15 and N=7 at IPC10 and IPC50.

CIFAR-100. In Table [2,](#page-3-0) DB-APM surpasses APM with an average precision increment of 20.9% and 3.7% at IPC10 and IPC50, respectively, employing a CW128 model for both synthesis and validation. Concurrently, DB-APM achieves the optimal results compared to other SOTA algorithms at both IPC10 and IPC50. In a similar vein, DB-SRe<sup>2</sup>L demonstrates an enhanced average precision of 16.7% and  $13.1\%$  over  $SRe<sup>2</sup>L$  at IPC10 and IPC50, respectively, utilizing a ResNet18 model for synthesis and validation. Although DB-SRe<sup>2</sup>L exhibits a slightly lower reported precision compared to G-VBSM, it is noteworthy that G-VBSM employed multiple models with diverse architectures during the DD process, whereas our approach relied solely on a single model architecture. DB-APM utilized N=8 and N=2 at IPC10 and IPC50, respectively, while DB-SRe<sup>2</sup>L employed N=10 and N=4 at IPC10 and IPC50, respectively.

### 5 CONCLUSION

In this work, we address the issue of limited performance in low compression data volume for two advanced and highly efficient algorithms  $SRe<sup>2</sup>L$  and APM in the domain of dataset compression, by proposing a distributed enhancement method, DB, that is ingeniously suited for parallel computing environments. Our DB method encompasses three steps: Distribute&Encapsulate, Distill and Integrate&Mix-relabel. We have conducted a theoretical convergence proof for DB, analyzed the impact of DB on the operational efficiency of the original algorithms, and executed extensive experiments. Empirical results demonstrate that our improved methodologies, DB-APM and DB-SRe2L, not only significantly outperform the original algorithms across various metrics but also achieve state-of-the-art performance. Our work demonstrates the potential for combining two DD techniques and lays the groundwork for distributed DD. In future research, we aim to further explore more advanced methods of distributed dataset distillation.

### ACKNOWLEDGMENTS

This work was supported in part by the National Key RD Program of China under Grant 2022YFE0138600, in part by the National Natural Science Foundation of China under Grant 92367205, in part by the Zhejiang Provincial Natural Science Foundation of China under Grant LZ22F030011.

<span id="page-4-0"></span>Distributed Boosting: An Enhancing Method on Dataset Distillation CIKM '24, October 21–25, 2024, Boise, ID, USA.

CIKM ’24, October 21–25, 2024, Boise, ID, USA.

## REFERENCES

- <span id="page-4-27"></span>[1] Olivier Bachem, Mario Lucic, and Andreas Krause. 2017. Practical coreset constructions for machine learning. arXiv preprint arXiv:1703.06476 (2017).
- <span id="page-4-2"></span>[2] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. 2022. Dataset distillation by matching training trajectories. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 4750–4759.
- <span id="page-4-22"></span>[3] Mingyang Chen, Bo Huang, Junda Lu, Bing Li, Yi Wang, Minhao Cheng, and Wei Wang. 2023. Dataset Distillation via Adversarial Prediction Matching. arXiv preprint arXiv:2312.08912 (2023).
- <span id="page-4-20"></span>[4] Patryk Chrabaszcz, Ilya Loshchilov, and Frank Hutter. 2017. A downsampled variant of imagenet as an alternative to the cifar datasets. arXiv preprint arXiv:1707.08819 (2017).
- <span id="page-4-3"></span>[5] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. 2023. Scaling up dataset distillation to imagenet-1k with constant memory. In International Conference on Machine Learning. PMLR, 6565–6590.
- <span id="page-4-23"></span>[6] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. 2023. Minimizing the accumulated trajectory error to improve dataset distillation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 3749–3758.
- <span id="page-4-8"></span>[7] Shengyuan Hu, Jack Goetz, Kshitiz Malik, Hongyuan Zhan, Zhe Liu, and Yue Liu. 2022. Fedsynth: Gradient compression via synthetic data in federated learning. arXiv preprint arXiv:2204.01273 (2022).
- <span id="page-4-14"></span>[8] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. 2022. Dataset condensation via efficient synthetic-data parameterization. In International Conference on Machine Learning. PMLR, 11102–11118.
- <span id="page-4-19"></span>[9] Ya Le and Xuan Yang. 2015. Tiny imagenet visual recognition challenge. CS 231N 7, 7 (2015), 3.
- <span id="page-4-15"></span>[10] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. 2022. Dataset condensation with contrastive signals. In International Conference on Machine Learning. PMLR, 12352–12364.
- <span id="page-4-24"></span>[11] Haoyang Liu, Tiancheng Xing, Luwei Li, Vibhu Dalal, Jingrui He, and Haohan Wang. 2023. Dataset Distillation via the Wasserstein Metric. arXiv preprint arXiv:2311.18531 (2023).
- <span id="page-4-4"></span>[12] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. 2022. Efficient dataset distillation using random feature approximation. Advances in Neural Information Processing Systems 35 (2022), 13877–13891.
- <span id="page-4-32"></span>[13] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. 2023. Dataset distillation with convexified implicit gradients. In International Conference on Machine Learning. PMLR, 22649–22674.
- <span id="page-4-6"></span>[14] Aru Maekawa, Naoki Kobayashi, Kotaro Funakoshi, and Manabu Okumura. 2023. Dataset distillation with attention labels for fine-tuning bert. In Proceedings of the 61st Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 119–127.
- <span id="page-4-10"></span>[15] Wojciech Masarczyk and Ivona Tautkute. 2020. Reducing catastrophic forgetting with learning on synthetic data. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops. 252–253.
- <span id="page-4-28"></span>[16] Noveen Sachdeva and Julian McAuley. 2023. Data distillation: A survey. arXiv preprint arXiv:2301.04272 (2023).

- <span id="page-4-30"></span>[17] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. 2023. Datadam: Efficient dataset distillation with attention matching. In Proceedings of the IEEE/CVF International Conference on Computer Vision. 17097–17107.
- <span id="page-4-9"></span>[18] Mattia Sangermano, Antonio Carta, Andrea Cossu, and Davide Bacciu. 2022. Sample condensation in online continual learning. In 2022 International Joint Conference on Neural Networks (IJCNN). IEEE, 01–08.
- <span id="page-4-21"></span>[19] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. 2023. Generalized Large-Scale Data Condensation via Various Backbone and Statistical Matching. arXiv preprint arXiv:2311.17950 (2023).
- <span id="page-4-26"></span>[20] Zhiqiang Shen and Eric Xing. 2022. A fast knowledge distillation framework for visual recognition. In European Conference on Computer Vision. Springer, 673–690.
- <span id="page-4-12"></span>[21] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. 2020. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In International Conference on Machine Learning. PMLR, 9206–9216.
- <span id="page-4-7"></span>[22] Ilia Sucholutsky and Matthias Schonlau. 2021. Soft-label dataset distillation and text dataset distillation. In 2021 International Joint Conference on Neural Networks  $(ITCNN)$ . IEEE,  $1-8$ .
- <span id="page-4-17"></span>[23] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. 2022. Cafe: Learning to condense dataset by aligning features. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 12196–12205.
- <span id="page-4-1"></span>[24] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. 2018.
- <span id="page-4-5"></span>Dataset distillation. arXiv preprint arXiv:1811.10959 (2018). [25] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. 2023. Squeeze, Recover and Relabel: Dataset Condensation at ImageNet Scale From A New Perspective. arXiv preprint arXiv:2306.13092 (2023).
- <span id="page-4-25"></span>[26] Shan You, Chang Xu, Chao Xu, and Dacheng Tao. 2017. Learning from multiple teacher networks. In Proceedings of the 23rd ACM SIGKDD International Conference on Knowledge Discovery and Data Mining. 1285–1294.
- <span id="page-4-11"></span>Bo Zhao and Hakan Bilen. 2021. Dataset condensation with differentiable siamese augmentation. In International Conference on Machine Learning. PMLR, 12674– 12685.
- <span id="page-4-18"></span>[28] Bo Zhao and Hakan Bilen. 2023. Dataset condensation with distribution matching. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision. 6514–6523.
- <span id="page-4-16"></span>[29] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. 2020. Dataset condensation with gradient matching. arXiv preprint arXiv:2006.05929 (2020).
- <span id="page-4-29"></span>[30] Daquan Zhou, Kai Wang, Jianyang Gu, Xiangyu Peng, Dongze Lian, Yifan Zhang, Yang You, and Jiashi Feng. 2023. Dataset quantization. In Proceedings of the IEEE/CVF International Conference on Computer Vision. 17205–17216.
- <span id="page-4-13"></span>[31] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. 2022. Dataset distillation using neural feature regression. Advances in Neural Information Processing Systems 35 (2022), 9813–9827.
- <span id="page-4-31"></span>[32] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. 2022. Dataset distillation using neural feature regression. Advances in Neural Information Processing Systems 35 (2022), 9813–9827.
# Information Compensation: A Fix for Any-scale Dataset Distillation

Peng Sun<sup>1,2</sup> <PERSON><PERSON><sup>2,∗</sup> <PERSON><PERSON><PERSON><sup>3</sup> <PERSON><sup>2,†</sup> <sup>1</sup>Zhejiang University <sup>2</sup>Westlake University <sup>3</sup>University College London

<EMAIL>, <EMAIL> <EMAIL>, <EMAIL>

Reviewed on OpenReview: <https://openreview.net/forum?id=2SnmKd1JK4>

### Abstract

Dataset distillation, a recent machine learning paradigm, aims to compress large datasets into smaller, effective versions. In this paper, we introduce a near-Lossless Information Compression (LIC) approach that directly compresses the key information of original datasets into distilled forms with minimal information loss. Our LIC markedly surpasses existing solutions in both efficiency and effectiveness, demonstrating superior performance across a range of dataset sizes, from CIFAR-10 to ImageNet-1K. For instance, using a ResNet-18 backbone with  $IPC = 10$ , LIC distills the entire ImageNet-1K dataset in just 80 minutes, achieving a top-1 validation accuracy of 48%, significantly outperforming the SOTA method  $\text{SRe}^2$  [\(Yin et al., 2023\)](#page-10-0), which only attains 25% accuracy and requires five times longer to process. We will make our code publicly available.

## 1 Introduction

Dataset distillation [\(Wang et al., 2018\)](#page-10-1) aims to distill the knowledge from a large training dataset into a very small set of synthetic training images such that a model trained on it can achieve comparable test performance as one trained on the original dataset. Distilled data provides the significant benefits of accelerating model training and reducing storage costs. As a highly effective paradigm, dataset distillation has shown its potential to facilitate applications in multiple domains, such as continual learning [\(Zhao et al., 2020;](#page-10-2) [Rosasco](#page-9-0) [et al., 2021\)](#page-9-0), neural architecture search[\(Wang et al., 2021;](#page-10-3) [Such et al., 2020\)](#page-9-1), and privacy protection [\(Xiong et al., 2023;](#page-10-4) [Dong et al., 2022;](#page-8-0) [Chen et al., 2022\)](#page-8-1).

Though prior dataset distillation methods [\(Cazenavette et al., 2022,](#page-8-2) [2023;](#page-8-3) [Zhao et al., 2020;](#page-10-2) [Wang et al., 2022;](#page-9-2) [Zhao and Bilen, 2023;](#page-10-5) [Zhao et al., 2023\)](#page-10-6) have achieved great success, they mainly focus on small-scale and low-resolution datasets, such as CIFAR [\(Wang et al., 2018\)](#page-10-1), Tiny-ImageNet [\(Cazenavette et al., 2022\)](#page-8-2), while struggling with real-world datasets that are larger-scale and higher-resolution, e.g., ImageNet-1K [\(Deng et al., 2009\)](#page-8-4). An information extraction-based approach, like  $S\text{Re}^{2}L$  [\(Yin et al., 2023\)](#page-10-0), has recently been proposed and

<sup>1.</sup> † Corresponding author.

<sup>2.</sup> ∗ Equal contribution with the first author.

efficiently generalized on ImageNet-1K. Specifically, it involves a dual-compression process, where it condenses the information from a dataset into a pre-trained model via SQUEEZE, followed by further compressing the information from the model into distilled images and labels respectively using Recover and Relabel. However, this line of research struggles with two issues [\(Sun et al., 2023\)](#page-9-3): 1) poor cross-architecture generalization, and 2) significant performance drop on the distilled dataset under the setting of low IPC or low-resolution.

As our first contribution, we undermine the key factors behind these challenges and conjecture that they are primarily due to the *significant information loss* introduced by the dualcompression process (c.f. Appendix [B](#page-12-0) for detailed exploration). We instead propose to drop the conventional dual-compression approach and resort to leveraging a simple squeezing operation and Relabel strategy to distill information from both data and label space. Though efficient, its effectiveness is still largely limited by the information-compromised distilled images. As a remedy, we propose to compensate for the information loss in the distilled images by aligning the effective information between them and their original counterparts. Our approach demonstrates impressive adaptability and superior performance across various dataset sizes, model architectures, and image resolutions. Our contributions are summarized as follows:

- We propose LIC, a simple yet effective approach that compensates the information loss to enable near-Lossless Information Compression for dataset distillation.
- We demonstrate through extensive results that LIC notably outperforms all existing SOTA methods for any-scale original datasets. For example, it successfully distills a dataset with  $IPC = 10$  from the full ImageNet-1K dataset within 1 hour, achieving an impressive  $48\%$ top-1 validation accuracy using ResNet-18 [\(He et al., 2016\)](#page-8-5).

## 2 Methodology

To compensate for the lost information in compressed images, we introduce a three-stage efficient dataset distillation framework (c.f. Appendix [C\)](#page-15-0). This framework begins by selecting a set of key samples (c.f. Section [2.1\)](#page-1-0). Subsequently, it employs a squeezing-based technique to condense these chosen samples into a distilled dataset, followed by compensating information loss (c.f. Section [2.2\)](#page-2-0). Furthemore, the RELABEL method [\(Shen and Xing, 2022\)](#page-9-4) is employed, innovatively transferring the knowledge from the full original dataset into the more compact label space of the distilled dataset (c.f. Section [2.3\)](#page-4-0).

#### <span id="page-1-0"></span>2.1 Selecting Key Samples

Our goal is to devise an effective method for identifying the crucial samples that are instrumen-tal in benefiting the RELABEL process. Motivated by the assumption in Proposition [1—](#page-14-0)which suggests that each chosen sample  $(\mathbf{x}_i, y_i)$  is supposed to receive an informative and accurate label  $\phi_{\theta_{\mathcal{T}}}(\mathbf{x}_i)$  from the pre-trained model during the RELABEL phase—the goal then relaxes to find the samples that are relabeled most accurately by the pre-trained model  $\phi_{\theta_{\mathcal{T}}}$ . Thus, we introduce a loss-based importance score  $s_i$  for each sample pair  $(\mathbf{x}_i, y_i)$ , defined as

$$
s_i = -\ell(\phi_{\theta_{\mathcal{T}}}(\mathbf{x}_i), y_i). \tag{1}
$$

The key sample selection procedure can be detailed below. Let  $\mathcal{T}_c := \{(\mathbf{x}_i, y_i) \mid (\mathbf{x}_i, y_i) \in$  $\mathcal{T}, y_i = c$  represents the subset of the dataset  $\mathcal{T}$ , containing only those samples  $(\mathbf{x}_i, y_i)$  that are labeled with the class c. For each class data  $\mathcal{T}_c$ , we identify the key samples  $x_i$  based on their importance scores  $s_i$ . Specifically, we select those samples for which  $s_i \geq \overline{s}$ , where  $\overline{s}$ denotes a predetermined threshold<sup>[3](#page-2-1)</sup>.

We further simplify the whole procedure due to the computational overhead and diversity issue, namely 1) computing the importance score for every sample  $\mathbf{x}_i$  in an entire class data  $\mathcal{T}_c$ presents a significant computational challenge, and 2) focusing solely on samples that closely align with the true label can lead to a lack of diversity within the selected samples. In detail, we utilize a pre-selection strategy inspired by [Sun et al.](#page-9-3)  $(2023)$ , which involves selecting a subset<sup>[4](#page-2-2)</sup>  $\mathcal{T}'_c \subset \mathcal{T}_c$  uniformly at random to serve as a proxy for the entire  $\mathcal{T}_c$ . Such a pre-selection strategy not only promotes diversity in the data but also lessens the computational load [\(Sun et al.,](#page-9-3) [2023\)](#page-9-3), thereby laying the groundwork for our subsequent score-based sample selection process.

#### <span id="page-2-0"></span>2.2 Information Compensation for Compressed Data

In this subsection, we first formally define the squeezing operation for images and then detail the case of effective information and the corresponding information loss. A method is further devised based on information compensation to ensure the preservation of information integrity in the squeezed images relative to their original versions.

On the loss of effective information. For the selected key samples (images), we compress them into a more compact pixel space. However, given the constraints of limited pixel space storage, compressing multiple images directly into one can result in a significant reduction in the fineness and detail of the original information (c.f. Appendix [B.2\)](#page-13-0). We start with the definition of information compression below.

Definition 1 (Image Squeezing and Expanding) Let  $\{x_i\}_{i=1}^N$  be a set of N images, where each image  $\mathbf{x}_i \in \mathbb{R}^d$  resides in a d-dimensional space. We define a squeezing operation<sup>[5](#page-2-3)</sup> f that transforms this set of images into a single squeezed image  $\mathbf{x}_j^* \in \mathbb{R}^d$  as follows:

$$
\mathbf{x}_{j}^{*} = \mathcal{F}(\{\mathbf{x}_{i}\}_{i=1}^{N}),\tag{2}
$$

where  $\mathbf{x}_j^*$  shares the same dimensional space as each  $\mathbf{x}_i$ . Conversely, the expanding operation  $\mathcal{F}^{-1}$  reconstructs a set of images  $\{\hat{\mathbf{x}}_i\}_{i=1}^N$  from the squeezed image  $\mathbf{x}_j^*$ , each in the same d-dimensional space:

$$
\{\hat{\mathbf{x}}_i\}_{i=1}^N = \mathcal{F}^{-1}(\mathbf{x}_j^*).
$$
\n(3)

Note that the expanded set of images, denoted as  $\{\hat{\mathbf{x}}_i\}_{i=1}^N$ , closely resemble the original set  ${x_i}_{i=1}^N$ , albeit typically with a certain level of information degradation.

<span id="page-2-1"></span><sup>3.</sup> This threshold is determined by the number of samples necessitated for further use, i.e., the IPC.

<span id="page-2-2"></span><sup>4.</sup> We use the default size of this selected subset in [Sun et al.](#page-9-3) [\(2023\)](#page-9-3), see details in Section [3.1.](#page-4-1)

<span id="page-2-3"></span><sup>5.</sup> In practical terms, the squeezing operation for images involves resizing and concatenating them. For instance, when handling 4 images, each with a resolution of  $224 \times 224$  pixels, we first downscale each image to  $112 \times 112$  pixels. Then, we concatenate these resized images into a single image, restoring the resolution to  $224 \times 224$  pixels.

<span id="page-3-0"></span>We then define the effective information of a data sample.

Definition 2 (Observation-based Effective Information) Let  $x_i$  represent a sample from any domain (e.g., image). Define an observer group  $\mathcal{R} = \{r_j\}$ , where each observer  $r_j$  is capable of extracting or interpreting features from  $\mathbf{x}_i$ , and  $|\mathcal{R}| \geq 1$ . The effective information of the sample  $\mathbf{x}_i$ , as observed by the group R, is conceptualized as the distribution  $p_{\mathbf{x}_i|R}(z)$ . This distribution is formulated as:

$$
p_{\mathbf{x}_i|\mathcal{R}}(z) := \{ z|z = r_j(\mathbf{x}_i), \forall r_j \in \mathcal{R} \}.
$$
\n<sup>(4)</sup>

Here, z denotes the set of features or interpretations extracted from  $x_i$  by an observer  $r_j$ within R.

The Definition [2](#page-3-0) posits that the effective information of a sample encompasses the set of its features as perceived or extracted by a diverse set of observers. Samples are considered to have similar effective information if they result in comparable feature sets across the observers in R.

Compensating the loss of effective information. For a given squeezed sample  $\mathbf{x}_j^*$ , we aim to find  $\Delta x$  that restores the effective information in the compensated squeezed image  $(\mathbf{x}_j^* + \Delta \mathbf{x})$  to match that of the original images  $\{\mathbf{x}_i\}_{i=1}^N$ . Specifically, for a compensated squeezed image  $(\mathbf{x}_j^* + \Delta \mathbf{x})$ , the corresponding expanded images are given by

$$
\{\hat{\mathbf{x}}_i\}_{n=1}^N = \mathcal{F}^{-1}(\mathbf{x}_j^* + \Delta \mathbf{x}).\tag{5}
$$

To align the effective information in these expanded images  $\{\hat{\mathbf{x}}_i\}_{n=1}^N$  with the original images  ${x_i}_{i=1}^N$ , we seek an optimal  $\Delta$ **x** through the following minimization:

<span id="page-3-1"></span>
$$
\underset{\Delta \mathbf{x}}{\arg \min} \ \mathbb{E}_{\mathbf{x}_i, \hat{\mathbf{x}}_i} D_{\mathrm{KL}}(p_{\mathbf{x}_i|\mathcal{R}} || p_{\hat{\mathbf{x}}_i|\mathcal{R}}).
$$
\n(6)

Given the direct correlation between  $\hat{\mathbf{x}}_i$  and  $\mathbf{x}_i$ , along with the shared observer group R employed to assess their effective information, [\(6\)](#page-3-1) can be further simplified to:

<span id="page-3-3"></span>
$$
\underset{\Delta \mathbf{x}}{\arg \min} \ \mathbb{E}_{\mathbf{x}_i, \hat{\mathbf{x}}_i} \mathbb{E}_{r_j} \| r_j(\mathbf{x}_i) - r_j(\hat{\mathbf{x}}_i) \| \,. \tag{7}
$$

To reduce computational complexity, we focus on a specific scenario where the observer group consists of only one pre-trained model across various transformations<sup>[6](#page-3-2)</sup>. Let  $\mathcal{R} = \{r_j \mid r_j = 1\}$  $g_j \circ \phi_{\theta_{\mathcal{T}}}, \forall g_j \sim \mathcal{G}\},\$  where  $\mathcal G$  denotes the transformation group. Therefore, to achieve [\(7\)](#page-3-3), our loss function is defined as

<span id="page-3-4"></span>
$$
\mathcal{L}_{\Delta \mathbf{x}} = \mathbb{E}_{\mathbf{x}_i, \hat{\mathbf{x}}_i} \mathbb{E}_{g_j} \| \phi_{\boldsymbol{\theta}_{\mathcal{T}}}(g_j(\mathbf{x}_i)) - \phi_{\boldsymbol{\theta}_{\mathcal{T}}}(g_j(\hat{\mathbf{x}}_i)) \|_2^2.
$$
 (8)

By minimizing [\(8\)](#page-3-4), we find the optimal compensation  $\Delta x^*$  and capture the compensated squeezed image  $\widetilde{\mathbf{x}}_j = \mathbf{x}_j^* + \Delta \mathbf{x}^*$  as the distilled image.

<span id="page-3-2"></span><sup>6.</sup> In the context of image data, these transformations encompass a range of nonlinear and linear operations, including techniques for image data augmentation.

Balancing the semantic and textural information. Directly generating the compensated squeezed image  $(\mathbf{x}_j^* + \Delta \mathbf{x})$  through aligning its effective information with the original image set  $\{\mathbf x_i\}_{i=1}^N$  may lead to a substantial loss of texture information. The reason is that the model  $\phi_{\theta_{\mathcal{T}}}$  tends to extract semantic information from input images, which often results in a notable drop in the texture details. Therefore, to achieve a balance between semantic richness and texture preservation in the distilled image  $\tilde{\mathbf{x}}_j$ , we leverage intermediate model features instead of the last-layer logits, which helps in retaining more textural details (see Section [3.3](#page-6-0) for more details).

#### <span id="page-4-0"></span>2.3 Relabel with Observer Model

Instead of using the basic one-shot label, we propose a re-label strategy for our multi-subfigure distilled images  $\widetilde{\mathbf{x}}_i$ , which provides more informative and diverse knowledge. The proposed module is inspired by [Yun et al.](#page-10-7) [\(2021\)](#page-10-7) that a random image crop might contain a different object than the one originally labeled and then lead to inaccurate or misleading training data, which illustrates that the one-shot label strategy is challenging to express enough knowledge for cropped images.

Re-label strategy can be implemented by using the soft labeling approach [\(Shen and](#page-9-4) [Xing, 2022\)](#page-9-4) to generate region-level soft labels  $\tilde{y}_j^k = \ell\left(\phi_{\theta_{\mathcal{T}}}(\tilde{\mathbf{x}}_j^k)\right)$ , where  $\tilde{\mathbf{x}}_j^k$  is the k-th region in the distilled image  $\widetilde{\mathbf{x}}_j$  and  $\widetilde{y}_j^k$  is the soft label.

Therefore, we can train the model  $\phi_{\theta_{\mathcal{S}}}$  on the distilled data by achieving:

$$
\mathcal{L} = -\sum_{j} \sum_{k} \tilde{y}_{j}^{k} \log \phi_{\theta_{\mathcal{S}}}(\tilde{\mathbf{x}}_{j}^{k}). \tag{9}
$$

#### 3 Experiment

In this section, we evaluate the performance of our proposed LIC over various datasets and neural network architectures. First, we demonstrate the superior results of LIC on real-world datasets, cross-architecture generalization and efficiency. Next, we conduct extensive ablation experiments to investigate the effect of each component of our method.

## <span id="page-4-1"></span>3.1 Experimental Setting

Datasets and neural network architectures. We conduct experiments on varying scales and resolutions of images.

- **Small-scale:** we evaluate on two datasets, including CIFAR-10  $(32 \times 32)$  [\(Krizhevsky](#page-9-5) [et al., 2009b\)](#page-9-5) and CIFAR-100  $(32 \times 32)$  [\(Krizhevsky et al., 2009a\)](#page-9-6).
- Large-scale: we also use two large-scale high-resolution datasets including Tiny-ImageNet  $(64 \times 64)$  [\(Le and Yang, 2015\)](#page-9-7) and ImageNet-1K  $(224 \times 224)$  [\(Deng et al., 2009\)](#page-8-4).

Similar to the prior dataset distillation works [\(Yin et al., 2023;](#page-10-0) [Zhao et al., 2023;](#page-10-6) [Guo et al.,](#page-8-6) [2023\)](#page-8-6), we employ ConvNet [\(Guo et al., 2023\)](#page-8-6), ResNet-18 [\(He et al., 2016\)](#page-8-5), MobileNet-V2 [\(Sandler et al., 2018\)](#page-9-8), as our backbone networks on all datasets. Specifically, for ConvNet, we

| Architecture  |     | ConvNet         |                 |                 |                 |                 |                 | ResNet-18       |                 |                 |
|---------------|-----|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|
| Dataset       | IPC | MTT             | IDM             | TESLA           | DATM            | DREAM           | SRe2L           | LIC (Ours)      | SRe2L           | LIC (Ours)      |
| CIFAR-10      | 1   | 46.3 	extpm 0.8 | 45.6 	extpm 0.7 | 48.5 	extpm 0.8 | 46.9 	extpm 0.5 | 51.1 	extpm 0.3 | 19.2 	extpm 1.1 | 51.7 	extpm 0.6 | 18.8 	extpm 0.5 | 40.7 	extpm 0.4 |
|               | 10  | 65.3 	extpm 0.7 | 58.6 	extpm 0.1 | 66.4 	extpm 0.8 | 66.8 	extpm 0.2 | 69.4 	extpm 0.4 | 34.7 	extpm 1.1 | 72.2 	extpm 0.2 | 38.0 	extpm 0.3 | 80.9 	extpm 0.4 |
|               | 50  | 71.6 	extpm 0.2 | 67.5 	extpm 0.1 | 72.6 	extpm 0.7 | 76.1 	extpm 0.3 | 74.8 	extpm 0.1 | 54.8 	extpm 0.6 | 78.3 	extpm 0.1 | 65.6 	extpm 1.0 | 89.2 	extpm 0.0 |
| CIFAR-100     | 1   | 24.3 	extpm 0.3 | 20.1 	extpm 0.3 | 24.8 	extpm 0.5 | 27.9 	extpm 0.2 | 29.5 	extpm 0.3 | 14.4 	extpm 0.1 | 42.7 	extpm 0.3 | 11.8 	extpm 0.5 | 48.1 	extpm 0.7 |
|               | 10  | 40.1 	extpm 0.4 | 45.1 	extpm 0.1 | 41.7 	extpm 0.3 | 47.2 	extpm 0.4 | 46.8 	extpm 0.7 | 40.3 	extpm 0.3 | 54.8 	extpm 0.2 | 46.0 	extpm 0.4 | 64.7 	extpm 0.1 |
|               | 50  | 47.7 	extpm 0.2 | 50.0 	extpm 0.2 | 47.9 	extpm 0.3 | 55.0 	extpm 0.2 | 52.6 	extpm 0.4 | 56.3 	extpm 0.1 | 56.6 	extpm 0.1 | 60.2 	extpm 0.1 | 67.6 	extpm 0.1 |
| Tiny-ImageNet | 1   | 8.8 	extpm 0.3  | 10.1 	extpm 0.2 | -               | 17.1 	extpm 0.3 | 10.0 	extpm 0.4 | 4.6 	extpm 0.3  | 31.7 	extpm 0.4 | 5.7 	extpm 0.2  | 36.5 	extpm 0.2 |
|               | 10  | 23.2 	extpm 0.2 | 21.9 	extpm 0.3 | -               | 31.1 	extpm 0.3 | -               | 22.5 	extpm 0.3 | 46.3 	extpm 0.2 | 33.8 	extpm 0.6 | 51.7 	extpm 1.0 |
|               | 50  | 28.0 	extpm 0.3 | 27.7 	extpm 0.3 | -               | 39.7 	extpm 0.3 | 29.5 	extpm 0.3 | 42.3 	extpm 0.3 | 47.4 	extpm 0.1 | 51.0 	extpm 0.4 | 55.8 	extpm 0.9 |
| ImageNet-1K   | 1   | -               | -               | 7.7 	extpm 0.2  | -               | -               | 1.3 	extpm 0.1  | 14.5 	extpm 0.3 | 1.4 	extpm 0.1  | 6.8 	extpm 0.2  |
|               | 10  | -               | -               | 17.8 	extpm 1.3 | -               | 18.4 	extpm 0.9 | 9.3 	extpm 0.4  | 24.0 	extpm 0.5 | 25.3 	extpm 0.4 | 48.5 	extpm 0.2 |
|               | 50  | -               | -               | 27.9 	extpm 1.2 | -               | -               | 38.8 	extpm 0.4 | 39.1 	extpm 0.2 | 45.7 	extpm 0.4 | 60.0 	extpm 0.2 |

<span id="page-5-0"></span>Table 1: Comparison with baseline models. In the table, bold means the best result, and entries with "-" are absent due to scalability problems. See Appendix [D](#page-15-1) for more details.

use Conv-3 on CIFAR-10/100, and use Conv-4 on Tiny-ImageNet and ImageNet-1K. More details about the used datasets and architectures can be found in Appendix [D.](#page-15-1)

Baselines. We compare our method with several optimization-based distillation methods that can scale to large high-resolution datasets, including MTT [\(Cazenavette et al., 2022\)](#page-8-2), IDM [\(Zhao et al., 2023\)](#page-10-6), TESLA [\(Cui et al., 2023\)](#page-8-7), DATM [\(Guo et al., 2023\)](#page-8-6), ADD [\(Zhang](#page-10-8) [et al., 2023\)](#page-10-8), DREAM [\(Liu et al., 2023b\)](#page-9-9), and  $\text{SRe}^2$ L [\(Yin et al., 2023\)](#page-10-0). To the best of our knowledge,  $S\text{Re}^2L$  is the only published work that can efficiently scale to any-scale dataset, so we consider it as our closest baseline. More details about these methods can be found in Appendix [D.](#page-15-1)

Implementation details. All the hyper-parameters used in our Algorithm [1](#page-16-0) are general, insensitive and easy-implemented for all datasets and network architectures (c.f. Section [3.3](#page-6-0) and Appendix [F](#page-18-0) for validation). We employ a generalized configuration for  $\mathcal{T}'$  (c.f. Section [2.1](#page-1-0)) for definition), where the size  $|\mathcal{T}'|$  is set as 300. We set the number  $N = 4$  of images squeezed in a distilled image (c.f. Section [2.2](#page-2-0) for definition) and number  $M = 200$  of compression iteration (c.f. Algorithm [1](#page-16-0) for definition). More implementation details are provided in Appendix [D.](#page-15-1)

#### <span id="page-5-1"></span>3.2 Comparison with the SOTA Methods

Results on CIFAR and ImageNet. Following previous research[\(Cazenavette et al., 2022;](#page-8-2) [Cui et al., 2023;](#page-8-7) [Zhao et al., 2023\)](#page-10-6), we set IPC to 1, 10, and 50 to compare with baselines on varying datasets and networks. As the results reported in Table [1,](#page-5-0) our method LIC outperforms other methods on varying datasets and neural networks with different IPC. It is noteworthy that prior information extraction-based solutions like  $\text{SRe}^2L$  struggle in scenarios involving small distilled datasets such as CIFAR-100 with IPC = 1 or CIFAR-10 with all IPC, further verifying our claims proposed in Section [B.](#page-12-0) Detailed comparison among more datasets and concurrent baselines, is in Appendix [E.](#page-17-0)

Cross-architecture generalization. An important property of the distilled datasets is their good generalization capability across unseen architectural models. Here we evaluate

<span id="page-6-1"></span>Image /page/6/Figure/0 description: This image contains four line graphs labeled (a), (b), (c), and (d). All graphs plot Top-1 Accuracy (%) on the y-axis against different parameters on the x-axis. Graph (a) shows accuracy against iteration number M, with data points at 0, 100, 200, 300, and 400. Three lines are plotted: a blue line around 45-47%, an orange line around 55%, and a yellow line starting at 67% and rising to about 72%. A pink shaded region covers iterations 100-300. Graph (b) shows accuracy against number N (IPC=1), with data points at 1, 4, 9, 16, and 25. The blue line starts at 33% and decreases to 20%. The orange line starts at 41% and peaks at 46% before decreasing to 32%. The yellow line starts at 47% and peaks at 47% before decreasing to 41%. A pink shaded region covers N=1 to N=4. Graph (c) shows accuracy against number N, with data points at 1, 4, 9, 16, and 25. The blue line starts at 47% and decreases to 19%. The orange line starts at 55% and decreases to 50%. The yellow line starts at 72% and decreases to 58%. A pink shaded region covers N=1 to N=4. Graph (d) shows accuracy against alignment layer, with categories Shallow, Mid, Deep, and Logit. The blue line is around 45-47%. The orange line is around 55%. The yellow line is around 70-72%. A pink shaded region covers the Mid and Deep layers.

Figure 1: Ablation study on each component in our LIC. We evaluate the distilled dataset of our LIC with different number M of compression iterations [\(1a\)](#page-6-1), number N of images squeezed in one distilled image [\(1b](#page-6-1) & [1c\)](#page-6-1), feature alignment layer [\(1d\)](#page-6-1). The yellow  $\bullet$ , red  $\bullet$ , and blue  $\bullet$  denote CIFAR-10, CIFAR-100, and Tiny-ImageNet respectively.

<span id="page-6-2"></span>Table 2: Tiny-ImageNet top-1 accuracy on cross-architecture generalization. We use Conv-4, ResNet-18, and MobileNet-V2 to distill the original dataset, and then transfer distilled data to each other architecture.

| Verifier     | Observer | Observer          |                   |                   |
|--------------|----------|-------------------|-------------------|-------------------|
|              |          | Conv-4            | ResNet-18         | MobileNet-V2      |
| Conv-4       | SRe2L    | 23.0 ± 0.3        | 14.7 ± 0.3        | 19.5 ± 0.3        |
|              | Ours     | <b>46.6 ± 0.3</b> | <b>26.0 ± 1.0</b> | <b>25.9 ± 0.3</b> |
| ResNet-18    | SRe2L    | 32.5 ± 0.6        | 33.6 ± 0.3        | 36.4 ± 0.2        |
|              | Ours     | <b>47.4 ± 0.3</b> | <b>51.9 ± 0.2</b> | <b>44.9 ± 0.2</b> |
| MobileNet-V2 | SRe2L    | 8.9 ± 0.4         | 8.9 ± 0.1         | 20.3 ± 0.4        |
|              | Ours     | <b>36.7 ± 0.5</b> | <b>34.6 ± 0.8</b> | <b>44.5 ± 0.3</b> |

the generalizability of our distilled datasets when IPC  $=10$ . As reported in Table [2,](#page-6-2) our distilled dataset performs best on unseen networks, which reflects the good generalizability of the data and labels distilled by our method. Furthermore, our success stems from that our LIC effectively keeps both textural and semantic information in distilled images, which is evidenced by [Cazenavette et al.](#page-8-3) [\(2023\)](#page-8-3).

**Efficiency comparison.** Efficiency is also a key factor during the process of distilling data. Here, We use a single RTX-4090 GPU for two methods to conduct experiments on Tiny-ImageNet. As evidenced in Table [3,](#page-7-0) our method LIC achieves superior efficiency in comparison to SOTA methods, demonstrating a notable advantage of efficacy and efficiency. Significantly, our algorithm offers a versatile peak memory capacity, enabling adjustments to batch size dynamically without sacrificing performance. This efficiency is attributed to the fact that our Algorithm [1](#page-16-0) can independently optimize images, allowing us to distill them one by one. More comparisons are in Appendix [E.](#page-17-0)

#### <span id="page-6-0"></span>3.3 Ablation Study

In this section, we set the default  $IPC = 10$  and employ ConvNet as the network backbone to examine how the components used in our LIC influence the quality of distilled dataset (see Appendix [F](#page-18-0) for more investigation).

<span id="page-7-0"></span>Table 3: Efficiency comparison with  $SRe^{2}L$  [\(Yin et al., 2023\)](#page-10-0) on varying networks on **Tiny-ImageNet.** Following  $\text{SRe}^2L$ , Time Cost represents the consumption when generating 100 images simultaneously, and the peak value of GPU memory is measured with a batch size of 100.

| Architecture |       | Time Cost (s) | Peak Memory (GB) |
|--------------|-------|---------------|------------------|
| Conv-4       | SRe2L | 51.68         | 1.36             |
|              | Ours  | <b>13.02</b>  | <b>0.65</b>      |
| ResNet-18    | SRe2L | 191.14        | 3.62             |
|              | Ours  | <b>25.34</b>  | <b>1.56</b>      |
| MobileNet-V2 | SRe2L | 114.05        | 1.27             |
|              | Ours  | <b>18.81</b>  | <b>0.64</b>      |

**Influence of compression iteration number M.** The number of iterations, denoted as  $M$ , impacts two aspects: 1) A higher iteration count  $M$  enhances the ability of our algorithm LIC to generate images of superior quality; 2) A lower iteration count M ensures a faster execution of our Algorithm [1.](#page-16-0) Consequently, choosing an optimal iteration number M represents a balance between quality and speed. As illustrated in Figure [1a,](#page-6-1) an iteration count of  $M = 200$  offers a well-rounded compromise for various datasets. Additionally, it is noteworthy that our LIC exhibits robustness to variations in  $M$ . Specifically, setting  $M$ beyond 200 yields negligible differences in performance.

**Influence of size of squeezed images N.** Specifically, though we can squeeze more images from  $\mathcal T$  into a distilled dataset S by increasing N increases to benefit the data diversity, it also results in a lower resolution for the source images (see the definition of squeezing in Footnote [5\)](#page-2-3), thus hurting the textural information. Figure [1c](#page-6-1)  $\&$  [1b](#page-6-1) showcases that the validation performance rises to the highest on selected three datasets when  $N = 4$ .

Why and how to choose the feature alignment layer? The experimental results in Figure [1d](#page-6-1) intuitively demonstrate the impact of the feature alignment layer, alongside the discussion in Section [2.2.](#page-2-0) As depicted in Figure [1d,](#page-6-1) optimal performance is often achieved through alignment at the deep layer. A plausible explanation for this observation is the differing information encoded at various network depths: shallow layers tend to capture more textural details, whereas logit layers are more adept at encoding semantic information. Consequently, to effectively infuse the distilled dataset with a suitable balance of information, we harness the feature alignment capabilities of the deep layer.

## 4 Conclusion

We demonstrate that LIC markedly surpasses existing SOTA techniques in the aspects of effectiveness and efficiency across a range of dataset sizes and network architectures. Additionally, we highlight the efficiency of LIC by showcasing its ability to distill the ImageNet-1k dataset in just 80 minutes. We apply our LIC to a continual learning task, with detailed results presented in Appendix [G.](#page-19-0) We also visualize the distilled images in Appendix [H.](#page-19-1)

# References

- <span id="page-8-2"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 4750–4759, 2022.
- <span id="page-8-3"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 3739–3748, 2023.
- <span id="page-8-1"></span>Dingfan Chen, Raouf Kerkouche, and Mario Fritz. Private set generation with discriminative information. Advances in Neural Information Processing Systems, 35:14678–14690, 2022.
- <span id="page-8-9"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. Advances in Neural Information Processing Systems, 35:810–822, 2022.
- <span id="page-8-7"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In International Conference on Machine Learning, pages 6565–6590. PMLR, 2023.
- <span id="page-8-4"></span>Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In 2009 IEEE conference on computer vision and pattern recognition, pages 248–255. Ieee, 2009.
- <span id="page-8-0"></span>Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In International Conference on Machine Learning, pages 5378–5396. PMLR, 2022.
- <span id="page-8-10"></span>Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 3749–3758, 2023.
- <span id="page-8-6"></span>Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. arXiv preprint arXiv:2310.05773, 2023.
- <span id="page-8-5"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 770–778, 2016.
- <span id="page-8-11"></span>Sergey Ioffe and Christian Szegedy. Batch normalization: Accelerating deep network training by reducing internal covariate shift. In International conference on machine learning, pages 448–456. pmlr, 2015.
- <span id="page-8-8"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In International Conference on Machine Learning, pages 11102–11118. PMLR, 2022.

- <span id="page-9-6"></span>Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009a.
- <span id="page-9-5"></span>Alex Krizhevsky, Vinod Nair, and Geoffrey Hinton. Cifar-10 and cifar-100 datasets. URl: https://www.cs.toronto.edu/kriz/cifar.html,  $6(1)$ :1, 2009b.
- <span id="page-9-7"></span>Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. CS 231N, 7(7):3, 2015.
- <span id="page-9-12"></span>Haoyang Liu, Tiancheng Xing, Luwei Li, Vibhu Dalal, Jingrui He, and Haohan Wang. Dataset distillation via the wasserstein metric. *arXiv preprint arXiv:2311.18531*, 2023a.
- <span id="page-9-9"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching.  $arXiv$  preprint  $arXiv:2302.14416$ , 2023b.
- <span id="page-9-10"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. Advances in Neural Information Processing Systems, 35:13877–13891, 2022.
- <span id="page-9-0"></span>Andrea Rosasco, Antonio Carta, Andrea Cossu, Vincenzo Lomonaco, and Davide Bacciu. Distilled replay: Overcoming forgetting through synthetic samples. In International Workshop on Continual Semi-Supervised Learning, pages 104–117, 2021.
- <span id="page-9-13"></span>Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 17097–17107, 2023.
- <span id="page-9-8"></span>Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 4510–4520, 2018.
- <span id="page-9-11"></span>Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. arXiv preprint arXiv:2311.17950, 2023.
- <span id="page-9-4"></span>Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In European Conference on Computer Vision, pages 673–690. Springer, 2022.
- <span id="page-9-1"></span>Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In International Conference on Machine Learning, pages 9206–9216, 2020.
- <span id="page-9-3"></span>Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. arXiv preprint arXiv:2312.03526, 2023.
- <span id="page-9-2"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 12196–12205, 2022.

- <span id="page-10-3"></span>Ruochen Wang, Minhao Cheng, Xiangning Chen, Xiaocheng Tang, and Cho-Jui Hsieh. Rethinking architecture selection in differentiable nas. 2021.
- <span id="page-10-1"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. arXiv preprint arXiv:1811.10959, 2018.
- <span id="page-10-4"></span>Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. Feddm: Iterative distribution matching for communication-efficient federated learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 16323–16332, 2023.
- <span id="page-10-11"></span>Zeyuan Yin and Zhiqiang Shen. Dataset distillation in large data era. arXiv preprint arXiv:2311.18838, 2023.
- <span id="page-10-0"></span>Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. arXiv preprint arXiv:2306.13092, 2023.
- <span id="page-10-10"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. arXiv preprint arXiv:2301.07014, 2023.
- <span id="page-10-7"></span>Sangdoo Yun, Seong Joon Oh, Byeongho Heo, Dongyoon Han, Junsuk Choe, and Sanghyuk Chun. Re-labeling imagenet: from single to multi-labels, from global to localized labels. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 2340–2350, 2021.
- <span id="page-10-8"></span>Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 11950–11959, 2023.
- <span id="page-10-12"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In International Conference on Machine Learning, 2021.
- <span id="page-10-5"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision, pages 6514–6523, 2023.
- <span id="page-10-2"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. arXiv preprint arXiv:2006.05929, 2020.
- <span id="page-10-6"></span>Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 7856–7865, 2023.
- <span id="page-10-9"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. Advances in Neural Information Processing Systems, 35:9813–9827, 2022.

## Appendix A. Related Work

[Wang et al.](#page-10-1) [\(2018\)](#page-10-1) first introduces the dataset distillation as a bi-level meta-learning optimization problem. The outer loop aims at optimizing the meta-dataset, while the inner loop focuses on training models using the distilled dataset. Existing methods can be roughly divided into three paradigms for solving this bi-level optimization problem.

Uni-level optimization-based paradigm. Tackling this bi-level problem is complex, especially when optimizing proxy models via gradient descent, which involves unraveling an intricate computational graph. Recent studies [\(Zhou et al., 2022;](#page-10-9) [Loo et al., 2022\)](#page-9-10) have proposed approximating model training using kernel ridge regression, which provides a closedform solution for optimal weights, thereby reducing training costs and improving performance. Despite these advancements, such methods still struggle with extensive computational demands or limitations due to the approximations in convex relaxation.

Matching-based paradigm. Another strategy involves emulating behaviors of the original dataset in the distilled one. They focus on minimizing disparities between surrogate models trained on both synthetic and original datasets. The key metrics for this are matching gradients [\(Zhao et al., 2020;](#page-10-2) [Kim et al., 2022;](#page-8-8) [Zhang et al., 2023;](#page-10-8) [Liu et al., 2023b\)](#page-9-9), features [\(Wang et al., 2022\)](#page-9-2), distribution [\(Zhao and Bilen, 2023;](#page-10-5) [Zhao et al., 2023\)](#page-10-6), and training trajectories [\(Cazenavette et al., 2022;](#page-8-2) [Cui et al., 2022;](#page-8-9) [Du et al., 2023;](#page-8-10) [Cui et al., 2023;](#page-8-7) [Yu et al., 2023;](#page-10-10) [Guo et al., 2023\)](#page-8-6). Trajectory and gradient matching, in particular, has shown impressive results with low IPC. However, these methods often tailor the distilled dataset to specific network architectures, limiting their generalizability. [Cazenavette et al.](#page-8-3) [\(2023\)](#page-8-3) address this by proposing the GLaD that synthesizes more realistic images to enhance generalization. Nonetheless, computational and memory challenges remain, particularly when scaling to large, high-resolution datasets like ImageNet-1K [\(Deng et al., 2009\)](#page-8-4).

Information extraction-based paradigm. The  $SRe<sup>2</sup>L$  framework [\(Yin et al., 2023\)](#page-10-0), the first work efficiently scaleable to ImageNet-1K, introduces a novel decoupled bi-level learning paradigm. This involves three stages: 1) Squeeze relevant information from the original dataset into a pre-trained model, 2) RECOVER this information into the image space, 3) RELABEL the distilled images by using the pre-trained model to further distill knowledge into the label space (c.f. Section [2.3](#page-4-0) for definition). Its efficiency and effectiveness have garnered community attention, spurring a series of research efforts. [Shao et al.](#page-9-11) [\(2023\)](#page-9-11) note that  $S\text{Re}^2L$  is limited to specific backbones and layers, impacting the generalization of the distilled dataset. They advocate for using diverse backbones for more precise and effective distillation. [Yin and Shen](#page-10-11) [\(2023\)](#page-10-11) further enhance  $\text{SRe}^2L$  with curriculum data augmentation. [Sun et al.](#page-9-3) [\(2023\)](#page-9-3) introduce an optimization-free approach that achieves notable diversity and realism in distilled datasets. However, inheriting the information loss issue from  $S\text{Re}^2L$ limits the efficacy of these methods, especially in small-scale datasets.

#### <span id="page-12-0"></span>Appendix B. Motivation and Intuitive Exploration

In this section, we explore the information extraction-based dataset distillation approaches. We highlight two critical aspects: 1) the primary challenge of significant information loss. which hurts the quality of distilled dataset, and 2) the pivotal role of RELABEL in enhancing the effectiveness of these methods. Consequently, we investigate the critical attributes necessary for retaining the efficacy of RELABEL and seek an alternative approach to effectively distill information from datasets while adhering to these attributes.

## B.1 Preliminary

We begin by formally defining the task of dataset distillation and subsequently unveil the key challenges in this domain, thereby motivating our exploration.

**Dataset distillation.** Given a large-scale dataset  $\mathcal{T} = {\mathbf{x}_i, y_i}_{i=1}^{|\mathcal{T}|}$  which consists of  $|\mathcal{T}|$ samples, dataset distillation aims to synthesize a smaller dataset  $S = {\{\tilde{\mathbf{x}}_j, \tilde{y}_j\}_{j=1}^{|S|}}$  with  $|S|$ <br>synthetic samples such that models trained on  $\mathcal{T}$  will have similar test performance as models synthetic samples such that models trained on  $\mathcal T$  will have similar test performance as models trained on  $S$ :

$$
\mathbb{E}_{\mathbf{x} \sim P_{\mathcal{D}}} [\ell (\phi_{\theta_{\mathcal{T}}}(\mathbf{x}), y)] \simeq \mathbb{E}_{\mathbf{x} \sim P_{\mathcal{D}}} [\ell (\phi_{\theta_{\mathcal{S}}}(\mathbf{x}), y)], \qquad (10)
$$

where  $P_D$  is the test real distribution, **x** is a data sample,  $\ell$  is the loss function, i.e., crossentropy loss. Here,  $\theta_{\mathcal{T}}$  and  $\theta_{\mathcal{S}}$  denotes the parameters of the neural network  $\phi$  trained on  $\mathcal T$  and  $\mathcal S$ , respectively.

A closer look at information extraction paradigm. Dataset distillation methods recently introduce the information extraction idea [\(Yin et al., 2023\)](#page-10-0)—as the first effective yet efficient solution—to allow the dataset distillation on diverse-scale datasets such as ImageNet-1K [\(Deng et al., 2009\)](#page-8-4). These methods typically employ a three-stage distillation process, indirectly transferring information from the original to the distilled images. The first stage involves condensing information from the complete dataset into pre-trained neural network models via SQUEEZE, followed by the extraction of this information into distilled images using RECOVER [\(Yin et al., 2023;](#page-10-0) [Liu et al., 2023a\)](#page-9-12). Upon the distilled data samples, RELABEL applies a pre-trained model to further distill knowledge into the label space. However, this paradigm encounters several key challenges:

- The RECOVER stage *necessitates batch normalization* in pre-trained models [\(Ioffe and](#page-8-11) [Szegedy, 2015\)](#page-8-11) to align the statistical features between distilled and original images [\(Yin](#page-10-0) [et al., 2023;](#page-10-0) [Liu et al., 2023a\)](#page-9-12).
- Significant information loss during both SQUEEZE and RECOVER stages leads to distilled images with minimal content, adversely affecting performance, particularly in low IPC settings [\(Sun et al., 2023\)](#page-9-3).
- Distilled images often *exhibit unrealistic textures or semantics*, tailored to specific networks, thereby limiting their generalization ability [\(Shao et al., 2023\)](#page-9-11).
- Despite outperforming other paradigms in terms of efficiency [\(Yin et al., 2023\)](#page-10-0), the RECOVER stage is still *computationally demanding*, requiring numerous optimization iterations [\(Yin and Shen, 2023\)](#page-10-11).

<span id="page-13-1"></span>Image /page/13/Figure/0 description: The image is a line graph showing the Top-1 Accuracy (%) on the y-axis against the Distillation Process (%) on the x-axis. There are four lines representing different methods: 'ADD w/ Relabel' (orange solid line with circles), 'ADD w/o Relabel' (red dashed line with diamonds), 'DataDAM w/ Relabel' (cyan solid line with circles), and 'DataDAM w/o Relabel' (blue dashed line with diamonds). The graph shows that 'ADD w/o Relabel' consistently has the highest accuracy, starting around 65% and increasing slightly to about 67% by the end of the distillation process. 'ADD w/ Relabel' starts at approximately 59% and increases to about 62%. 'DataDAM w/o Relabel' starts at about 35% and increases to around 50%, then fluctuates between 48% and 51%. 'DataDAM w/ Relabel' starts at about 50% and decreases to around 39% by the end of the distillation process. The graph is highlighted with a red shaded region at the beginning (0% distillation) and a blue shaded region at the end (100% distillation).

Figure 2: Apply RELABEL with the SOTA ADD [\(Zhang et al., 2023\)](#page-10-8), DataDAM [\(Sajedi](#page-9-13) [et al., 2023\)](#page-9-13). We evaluate the distilled images with  $IPC = 10$  during the distillation process. The results indicate that Relabel only assists the early-stage distilled datasets from SOTA methods in achieving improved performance. We use ConvNet as the backbone architecture for CIFAR-10.

In the meanwhile, the importance of RELABEL usually is under-estimated [\(Yin et al.,](#page-10-0) [2023;](#page-10-0) [Shao et al., 2023\)](#page-9-11). Until the recent study in [Sun et al.](#page-9-3) [\(2023\)](#page-9-3), they suspect the application of Relabel contributes to the primary efficacy of distilled datasets.

Motivation. To solve the aforementioned four issues simultaneously while maintaining the effectiveness of RELABEL, we aim to explore a method to distill images by directly condensing the information from the original dataset, instead of SQUEEZE and RECOVER.

#### <span id="page-13-0"></span>B.2 Exploring Image Distillation with RELABEL

To investigate a distillation approach aimed at generating high-quality images and retaining the efficacy of RELABEL, this section first unravels the potentials of RELABEL by empirically applying it to images distilled from various methods. We then identify the characteristics of images that influence the effectiveness of the Relabel, which motivates us to further examine a method with RELABEL that satisfies these constraints in Section [B.3.](#page-14-1)

Applying RELABEL to alternative distillation methods. To further explore the potential of Relabel, a natural idea is to directly apply Relabel, a versatile plug-and-play component, to the distilled images from different SOTA approaches. Specifically, by using RELABEL, we can substitute the labels in the originally distilled dataset with new ones. effectively creating an updated distilled dataset.

Figure [2](#page-13-1) illustrates that, compared to the initial real images<sup>[7](#page-13-2)</sup>, namely those with zero distillation iterations, the application of RELABEL to distilled and disturbed images yields only a marginal improvement or even harm. The observation demonstrates that the model used for Relabel, which was pre-trained solely on the original full dataset comprising real

<span id="page-13-2"></span><sup>7.</sup> Conventional dataset distillation approaches typically commence by selecting a set of initial real images, denoted as  $\{x_j\}_{j=1}^{|S|}$ , from the original full dataset  $\mathcal{T}$ . These images are then progressively transformed into distilled images, represented as  ${\{\tilde{\mathbf{x}}_j = \mathbf{x}_j + \epsilon_j\}}_{j=1}^{|S|}$ , through successive distillation iterations. Here,  $\epsilon_j$ signifies the learned shift applied to each image.

<span id="page-14-3"></span>Image /page/14/Figure/0 description: The image displays two line graphs side-by-side, both plotting "Top-1 Accuracy (%)" on the y-axis against "Number N for Squeezing" on the x-axis. The x-axis ranges from 1 to 25, with tick marks at 1, 4, 9, 16, and 25. The y-axis ranges from 0 to 45 in the left graph and 0 to 60 in the right graph, with tick marks every 5 or 10 units. The left graph is titled "IPC = 1" and the right graph is titled "IPC = 10". Each graph contains four lines: two solid lines in orange and blue, and two dashed lines in orange and blue. In the "IPC = 1" graph, the solid orange line starts at approximately 38%, decreases to about 30% at N=9, and ends around 24% at N=25. The solid blue line starts at approximately 33%, decreases to about 25% at N=9, and ends around 16% at N=25. The dashed orange line starts at about 6%, peaks at around 16% at N=16, and ends at about 7% at N=25. The dashed blue line starts at about 4%, increases to about 8% at N=9, and ends around 7% at N=25. In the "IPC = 10" graph, the solid orange line starts at approximately 54%, decreases to about 48% at N=9, and ends around 45% at N=25. The solid blue line starts at approximately 47%, decreases to about 41% at N=9, and ends around 40% at N=25. The dashed orange line starts at about 20%, peaks at around 29% at N=4, drops to about 18% at N=9, rises to about 29% at N=16, and ends around 8% at N=25. The dashed blue line starts at about 7%, increases to about 13% at N=4, stays around 13% until N=16, and ends around 8% at N=25.

Figure 3: Apply RELABEL into a squeezing-based method. We squeeze every N real randomsampled original images into one distilled image, thus forming a distilled dataset. We utilize ConvNet as the backbone. The red • and blue • respectively denote CIFAR-100 and Tiny-ImageNet. The solid line '—' and dashed line '--' respectively represent  $w/$  and  $w/$  Relabel.

images, struggles to accurately relabel the distilled data. We conjecture that it might be caused by disturbed distilled images, which have contained altered semantic and textural information and will diverge from the characteristics of real images (see Appendix [H](#page-19-1) for a detailed visualization).

Beyond the above empirical observation, Proposition [1](#page-14-0) below further posits that a model well-trained on original and undisturbed samples might only provide sub-optimal labels for disturbed ones (e.g., distilled images).

<span id="page-14-0"></span>Proposition 1 (Model Labeling under Perturbation)  $\emph{Consider a model}$   $\phi_{\boldsymbol{\theta}_{\mathcal{T}}}$  that has been effectively trained on a dataset  $\mathcal T$ , such that it accurately assigns a label  $\phi_{\theta_{\mathcal T}}(\mathbf x)$  to each sample  $(x, y)$  drawn from T. It is proposed that for any sample  $(x + \epsilon)$ , where  $\epsilon$  represents a disturbance added to x (e.g., random noise), the model  $\phi_{\theta_{\tau}}$  will yield a label that is less precise than the label assigned to the undisturbed sample  $x$ . Formally, this can be expressed as:

$$
\|\phi_{\theta_{\mathcal{T}}}(\mathbf{x} + \epsilon) - \phi_{\theta_{\mathcal{T}}}(\mathbf{x})\| \ge 0,\tag{11}
$$

implying that the deviation in the model's output due to the disturbance  $\epsilon$  is non-negative.

#### <span id="page-14-1"></span>B.3 Examining Squeezing Strategy with RELABEL

The analysis in Section [B.2](#page-13-0) indicates that disturbed images would hurt the performance of RELABEL. Therefore, we intuitively shift our attention to the squeezing-based operation (c.f. Footnote [5](#page-2-3) for definition), a simple distillation method inspired by [Kim et al.](#page-8-8) [\(2022\)](#page-8-8) that can approximately retain the semantic and textural details of the original real images<sup>[8](#page-14-2)</sup>.

We find that directly applying image distillation through squeezing negatively impacts the preservation of information, which consequently undermines the efficacy of RELABEL when applied to the distilled images. As shown in Figure [3,](#page-14-3) integrating RELABEL with our squeezing-based method boosts performance. Yet, the benefit diminishes when the

<span id="page-14-2"></span><sup>8.</sup> We defer the corresponding visualizations in Appendix [H.](#page-19-1)

number  $N$  of images squeezed into a single distilled image grows. This reduction likely stems from the increased loss of semantic and textural information with higher N, undermining both model training and the labeling accuracy of RELABEL for these distilled images. This suggests a trade-off between the quantity of images squeezed and the preservation of essential information within each image for effective distillation.

In the following sections, we justify how to compensate for the lost information in these compressed images, a crucial aspect of our proposed LIC.

# <span id="page-15-0"></span>Appendix C. Framework

The whole distillation process for an entire dataset by using our proposed LIC is illustrated in Algorithm [1](#page-16-0) and Figure [4.](#page-15-2)

<span id="page-15-2"></span>Image /page/15/Figure/4 description: This is a diagram illustrating a process involving image selection, compensation, and relabeling. The process begins with a 'Select & Squeeze' step, showing a grid of four small images, one of which is highlighted and magnified with a 'Crop & Resize' label. This leads to a 'Siamese Select' step, which processes a stack of images, focusing on a cropped and resized image of a dog's face. An arrow indicates 'Compensate Info. Loss' with a neural network icon, suggesting a process of information compensation. The next stage shows 'Compensated Images,' with blurred images of dogs, followed by a 'Distilled Image' section displaying a grid of four smaller, blurred images. Finally, a 'Relabel' step leads to 'Soft Label,' represented by a bar chart with labels like '[tiger, cat, dog, car, ...]' indicating a distribution of probabilities for different classes.

Figure 4: Overview of our proposed three-stage dataset distillation framework. Stage 1: select and squeeze the top  $N \times \text{IPC}$  impactful images per class into IPC composite image each, where  $N = 4$  and IPC  $= 1$  in this case. Stage 2: refine these composite images to produce distilled images, compensating the information loss. Stage 3: relabel distilled images for accurate, informative annotations.

## <span id="page-15-1"></span>Appendix D. Experiment Details

Datasets. In addition to the datasets described in Section [3.1,](#page-4-1) we note that prevalent dataset distillation techniques struggle to scale to large, high-resolution datasets. To this end, we evaluate the baselines and our proposed LIC using two representative subsets of ImageNet-1K, specifically ImageNet-100 and ImageNet-10, as documented in [\(Kim et al.,](#page-8-8) [2022\)](#page-8-8).

Baselines. We benchmark our proposed LIC against a range of SOTA distillation techniques capable of handling large, high-resolution datasets.

• MTT [\(Cazenavette et al., 2022\)](#page-8-2) pioneers a trajectory matching-based strategy, effective across both low and high-resolution datasets.

<span id="page-16-0"></span>Algorithm 1 An efficient framework for dataset distillation

**Input:** Original full dataset  $\mathcal{T}$ , a corresponding pre-trained observer model  $\phi_{\theta_{\mathcal{T}}}$  and initial  $S = \emptyset$ .

**Parameters:** The number  $N$  for squeezing images, the number  $M$  of compression iterations, the size of  $\mathcal{T}'_c$ .

```
for \mathcal{T}'_c \subset \mathcal{T}_c \subset \mathcal{T} do
    {Stage 1. Selecting Key Samples}
     for (\mathbf{x}_i, y_i) \in \mathcal{T}'_c do
         Calculate s_i = -\ell(\phi_{\theta_{\mathcal{T}}}(\mathbf{x}_i), y_i)end for
     Select top-(N \times \text{IPC}) images \{\mathbf{x}_i\}_{i=1}^{N \times \text{IPC}} via s_i{Stage 2. Compressing Effective Information}
    for j = 1 to IPC do
         Squeeze N selected images as \mathbf{x}_j^* = \mathcal{F}(\{\mathbf{x}_i\}_{i=1}^N)for m = 1 to M do
             \Delta \mathbf{x} \leftarrow \Delta \mathbf{x} + \nabla_{\Delta \mathbf{x}} \mathcal{L}_{\Delta \mathbf{x}}end for
         {Stage 3. Relabel with Observer Model}
         Relabel \widetilde{\mathbf{x}}_j = \mathbf{x}_j^* + \Delta \mathbf{x}^* with \widetilde{y}_j<br>\mathbf{x} = \mathbf{x} + f(\widetilde{\mathbf{x}}, \widetilde{\mathbf{x}})\mathcal{S} = \mathcal{S} \cup \{(\widetilde{\mathbf{x}}_i, \widetilde{y}_i)\}\end for
end for
Output: Small distilled dataset S
```

- IDM [\(Zhao et al., 2023\)](#page-10-6) presents an efficient dataset condensation technique utilizing distribution matching, offering a scalable alternative to computationally demanding optimization-focused methods [\(Zhao et al., 2020;](#page-10-2) [Cazenavette et al., 2022\)](#page-8-2), notably adapting to ImageNet-100.
- TESLA [\(Cui et al., 2023\)](#page-8-7) marks the first distillation approach that extends to the full  $ImageNet-1K$ , circumventing the extensive memory demands associated with MTT-derived methods through a constant memory footprint.
- ADD [\(Zhang et al., 2023\)](#page-10-8) demonstrates *effective scalability across varying dataset resolu*tions, enhancing distillation speed through model augmentation.
- DataDAM [\(Sajedi et al., 2023\)](#page-9-13) *efficiently distills images across multiple resolutions and* scales by matching spatial attention maps between real and distilled samples at various layers within families of randomly initialized neural networks.
- DATM [\(Guo et al., 2023\)](#page-8-6) stands out by occasionally surpassing the training performance of the full original dataset, for instance, achieving  $IPC = 100$  on CIFAR-100.
- DREAM [\(Liu et al., 2023b\)](#page-9-9) introduces an *efficient technique while also delivering the most* remarkable results.
- $SRe^{2}L$  [\(Yin et al., 2023\)](#page-10-0) is a novel entrant that *efficiently handles ImageNet-1K*, significantly outpacing other methods in managing large, high-resolution datasets and serving as our primary comparison point.

Evaluating main results. For both dataset distillation and performance evaluation, we employ identical neural network architectures. Consistent with previous studies [\(Cazenavette](#page-8-2) [et al., 2022;](#page-8-2) [Cui et al., 2023;](#page-8-7) [Zhao et al., 2023\)](#page-10-6), we use Conv-3 for CIFAR-10 and CIFAR-100 distillation tasks, Conv-4 for Tiny-ImageNet (with the exception of DREAM, which utilizes Conv-3) and ImageNet-1K, Conv-5 for ImageNet-10, and Conv-6 for ImageNet-100 distillation. In line with [Cazenavette et al.](#page-8-2) [\(2022\)](#page-8-2); [Cui et al.](#page-8-7) [\(2023\)](#page-8-7), MTT and TESLA apply a reduced resolution for distilling  $224 \times 224$  images. According to [Yin et al.](#page-10-0) [\(2023\)](#page-10-0), for retrieving and evaluating distilled datasets,  $SRe<sup>2</sup>L$  and LIC adopt ResNet-18.

Evaluating the distilled dataset. We detail the hyperparameter configurations for our distilled dataset evaluation in Table [4.](#page-17-1) Consistent with recent works [\(Yin and Shen, 2023;](#page-10-11) [Yin et al., 2023;](#page-10-0) [Shao et al., 2023\)](#page-9-11), the evaluation on the ImageNet-1K dataset follows the parameters outlined in Table [4a.](#page-17-1) For other datasets, their assessments are guided by the parameters specified in Table [4b.](#page-17-1) Furthermore, we implement Differentiable Siamese Augmentation (DSA) as described by [Zhao and Bilen](#page-10-12) [\(2021\)](#page-10-12) to enhance images during both the distillation and evaluation phases of our experiments.

<span id="page-17-1"></span>

| config        | value       |
|---------------|-------------|
| epochs        | 300         |
| optimizer     | AdamW       |
| learning rate | 0.001       |
| weight decay  | $1e-4$      |
| scheduler     | MultiStepLR |

| config        | value       |
|---------------|-------------|
| epochs        | 1000        |
| optimizer     | AdamW       |
| learning rate | 0.001       |
| weight decay  | $1e-4$      |
| scheduler     | MultiStepLR |

(a) ImageNet-1K evaluation setting.

(b) Default evaluation setting.

Table 4: Hyperparameter setting.

# <span id="page-17-0"></span>Appendix E. Experiment Results

Comparison with more datasets and baselines. In addition to the experiments discussed in Section [3.2,](#page-5-1) we further benchmark our proposed LIC against a broader set of baselines, encompassing recent contributions [\(Yu et al., 2023;](#page-10-10) [Shao et al., 2023;](#page-9-11) [Sun et al.,](#page-9-3) [2023;](#page-9-3) [Liu et al., 2023a\)](#page-9-12). Our analysis also spans additional subsets of the ImageNet-1K dataset, namely ImageNet-100 and ImageNet-10 [\(Kim et al., 2022\)](#page-8-8). The outcomes, presented in Table [5,](#page-18-1) consistently affirm the superior performance of LIC in dataset distillation tasks.

# E.1 Efficiency Comparison

Beyond assessing performance in Section [3.2,](#page-6-2) we expand our evaluation to include additional baselines. Results presented in Table [6](#page-18-2) underscore the exceptional efficiency of our proposed LIC, which also requires the least GPU memory.

<span id="page-18-1"></span>

| Architecture   |              |                          | ConvNet                  |                |                          | $ResNet-18$      |                |                          |                          |                |                  |
|----------------|--------------|--------------------------|--------------------------|----------------|--------------------------|------------------|----------------|--------------------------|--------------------------|----------------|------------------|
| Dataset        | $_{\rm IPC}$ | DataDAM ADD              |                          | IDM            | <b>RDED</b>              | $LIC$ (Ours)     | G-VBSM         | CDA                      | <b>WMDD</b>              | <b>RDED</b>    | $LIC$ (Ours)     |
|                | $\mathbf{1}$ | $32.0 \pm 1.2$           | 49.2                     | $45.6 \pm 0.7$ | $23.5 \pm 0.3$           | $50.0 \pm 0.3$   |                |                          | $\overline{\phantom{0}}$ | $22.9 \pm 0.4$ | $40.7 \pm 0.4$   |
| $CIFAR-10$     | 10           | $54.2 \pm 0.8$           | 67.1                     | $58.6 \pm 0.1$ | $50.2 \pm 0.3$           | $72.2 \pm 0.2$   | $53.5 \pm 0.6$ | $\equiv$                 | $\overline{\phantom{a}}$ | $37.1 \pm 0.3$ | $80.9 \pm 0.4$   |
|                | 50           | $67.0 \pm 0.4$           | 73.8                     | $67.5 \pm 0.1$ | $68.4 \pm 0.1$           | $78.3\,\pm\,0.1$ | $59.2 \pm 0.4$ | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $62.1 \pm 0.1$ | $89.2 \pm 0.0$   |
|                | $\mathbf{1}$ | $14.5 \pm 0.5$           | 29.8                     | $20.1 \pm 0.3$ | $19.6 \pm 0.3$           | $42.7\,\pm\,0.3$ | $25.9 \pm 0.5$ | $\equiv$                 | ÷.                       | $11.0 \pm 0.3$ | $48.1 \pm 0.7$   |
| $CIFAR-100$    | 10           | $34.8 \pm 0.5$           | 45.6                     | $45.1 \pm 0.1$ | $48.1 \pm 0.3$           | $54.8 \pm 0.2$   | $59.5 \pm 0.4$ | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $42.6 \pm 0.2$ | $64.7 \pm 0.1$   |
|                | 50           | $49.4 \pm 0.3$           | 52.6                     | $50.0 \pm 0.2$ | $57.0 \pm 0.1$           | $56.6 \pm 0.1$   | $65.0 \pm 0.5$ | $\overline{\phantom{a}}$ | ۰                        | $62.6 \pm 0.1$ | $67.6\,\pm\,0.1$ |
|                | $\mathbf{1}$ | $8.3 \pm 0.4$            | $\overline{a}$           | $10.1 \pm 0.2$ | $12.0 \pm 0.1$           | $31.7 \pm 0.4$   |                | $\equiv$                 | $7.6 \pm 0.2$            | $9.7 \pm 0.4$  | $36.5 \pm 0.2$   |
| Tiny ImageNet  | 10           | $18.7 \pm 0.3$           | $\bar{a}$                | $21.9 \pm 0.2$ | $39.6 \pm 0.1$           | $46.3 \pm 0.2$   |                | $\equiv$                 | $41.8 \pm 0.1$           | $41.9 \pm 0.2$ | $51.7 \pm 1.0$   |
|                | 50           | $28.7 \pm 0.3$           | $\bar{a}$                | $27.7 \pm 0.3$ | $47.6 \pm 0.2$           | $47.4 \pm 0.1$   |                | 48.7                     | $59.4 \pm 0.5$           | $58.2 \pm 0.1$ | $55.8 \pm 0.9$   |
|                | $\mathbf{1}$ | $\bar{\phantom{a}}$      |                          |                |                          | $39.7 \pm 0.4$   |                |                          | ÷.                       | $24.9 \pm 1.1$ | $43.9 \pm 0.7$   |
| $ImageNet-10$  | 10           | $\sim$                   | 74.6                     |                | $\blacksquare$           | $71.5 \pm 0.8$   |                |                          | $\overline{\phantom{a}}$ | $53.3 \pm 0.1$ | $76.3\,\pm\,0.8$ |
|                | 50           | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |                | $\overline{\phantom{a}}$ | $81.1 \pm 0.1$   |                |                          | ۰                        | $75.5 \pm 0.5$ | $85.8 \pm 0.8$   |
|                | $\mathbf{1}$ | $\overline{\phantom{a}}$ | $\blacksquare$           | $11.2 \pm 0.5$ | $7.1 \pm 0.2$            | $17.8 \pm 0.3$   |                |                          | $\equiv$                 | $8.1 \pm 0.3$  | $20.8 \pm 0.3$   |
| $ImageNet-100$ | 10           |                          | 48.4                     | $17.1 \pm 0.6$ | $29.6 \pm 0.1$           | $50.2 \pm 0.4$   |                |                          | $\bar{a}$                | $36.0 \pm 0.3$ | $65.0 \pm 0.1$   |
|                | 50           |                          |                          | $26.3 \pm 0.4$ | $50.2 \pm 0.2$           | $64.3 \pm 0.5$   |                |                          | ۰                        | $61.6 \pm 0.1$ | $79.8 \pm 0.2$   |
| ImageNet-1k    | $\mathbf{1}$ | $2.0 \pm 0.1$            | $\overline{\phantom{a}}$ |                | $\equiv$                 | $14.5 \pm 0.3$   |                | $\equiv$                 | $3.2 \pm 0.3$            | $6.6 \pm 0.2$  | $6.8\,\pm\,0.2$  |
|                | 10           | $6.3 \pm 0.0$            | $\sim$                   |                | $\equiv$                 | $24.0\,\pm\,0.5$ | $31.4 \pm 0.5$ | $\sim$                   | $38.2 \pm 0.2$           | $42.0 \pm 0.1$ | $48.5 \pm 0.2$   |
|                | 50           | $15.5 \pm 0.2$           |                          |                | $\overline{\phantom{a}}$ | $37.3 \pm 0.1$   | $51.8 \pm 0.4$ | 53.5                     | $57.6 \pm 0.5$           | $56.5 \pm 0.1$ | $60.0 \pm 0.2$   |

<span id="page-18-2"></span>Table 5: Comparison with SOTA dataset distillation methods.

| Architecture |             | Time Cost (s) | Peak Memory (GB) |
|--------------|-------------|---------------|------------------|
| Conv-4       | DREAM       | 33906.17      | 15.92            |
|              | DATM        | 12470.90      | 20.16            |
|              | SRe2L       | 51.68         | 1.36             |
|              | <b>Ours</b> | <b>13.02</b>  | <b>0.65</b>      |
| ResNet-18    | SRe2L       | 191.14        | 3.62             |
|              | <b>Ours</b> | <b>25.34</b>  | <b>1.56</b>      |
| MobileNet-V2 | SRe2L       | 114.05        | 1.27             |
|              | <b>Ours</b> | <b>18.81</b>  | <b>0.64</b>      |

Table 6: Efficiency comparison with SOTA methods on varying networks on Tiny-ImageNet.

# <span id="page-18-0"></span>Appendix F. Ablation

Ablation study on ImageNet-1K. The results depicted in Figure [5](#page-19-2) demonstrate that, despite exhibiting different behaviors when applied to Conv-4 and ResNet-18 architectures, the parameters of our LIC maintain their robustness against variations. Consequently, the configuration outlined in Section [3.1](#page-4-1) is applicable across all datasets.

Effectiveness of each technique. To assess the efficacy of the individual components within LIC, we decompose it into three core techniques. The term "Selection" denotes the synthesis of samples based on their importance scores, "Compensation" pertains to the refinement of data through image alignment, and "Relabel" involves the use of labels furnished by a pre-trained model.

<span id="page-19-2"></span>Image /page/19/Figure/0 description: The image contains four line graphs, each plotting Top-1 Accuracy (%) against a different parameter. Graph (a) shows Top-1 Accuracy (%) versus iteration number M, with two lines: a blue line starting at approximately 45% and increasing to about 47% at 100 iterations, then leveling off around 47-48% up to 400 iterations; and an orange line starting at approximately 22% and staying relatively flat around 23-24% from 100 to 400 iterations. Graph (b) shows Top-1 Accuracy (%) versus number N (IPC = 1). The blue line peaks at approximately 24% at N=4, then decreases to about 12% at N=25. The orange line starts at approximately 17% at N=1, decreases to about 5% at N=9, and further decreases to about 2% at N=25. Graph (c) shows Top-1 Accuracy (%) versus number N. The blue line peaks at approximately 53% at N=4, then decreases to about 23% at N=25. The orange line starts at approximately 31% at N=1, decreases to about 7% at N=9, and further decreases to about 2% at N=25. Graph (d) shows Top-1 Accuracy (%) versus alignment layer. The blue line peaks at approximately 53% at N=4, then decreases to about 10% at N=25. The orange line starts at approximately 17% at N=1, decreases to about 7% at N=9, and further decreases to about 2% at N=25.

Figure 5: Ablation study on each component in our LIC. We evaluate the distilled dataset of our LIC with different number  $M$  of compression iterations  $(5a)$ , number  $N$  of images squeezed in one distilled image [\(5b](#page-19-2) & [5c\)](#page-19-2), feature alignment layer [\(5d\)](#page-19-2). The yellow  $\bullet$ , and blue  $\bullet$  denote ConvNet, and ResNet respectively.

| Dataset       | Original                    | +Selection                  | +Compensation               | +Relabel                    |
|---------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|
| CIFAR-10      | $47.0 	extrm{ 	extpm } 0.1$ | $52.5 	extrm{ 	extpm } 1.1$ | $58.7 	extrm{ 	extpm } 0.4$ | $72.2 	extrm{ 	extpm } 0.2$ |
| CIFAR-100     | $27.8 	extrm{ 	extpm } 0.6$ | $31.8 	extrm{ 	extpm } 0.4$ | $40.8 	extrm{ 	extpm } 0.5$ | $54.8 	extrm{ 	extpm } 0.2$ |
| Tiny-ImageNet | $7.2 	extrm{ 	extpm } 0.5$  | $11.7 	extrm{ 	extpm } 0.6$ | $20.4 	extrm{ 	extpm } 0.8$ | $46.3 	extrm{ 	extpm } 0.2$ |
| ImageNet-1K   | $7.7 	extrm{ 	extpm } 0.1$  | $12.3 	extrm{ 	extpm } 0.1$ | $10.5 	extrm{ 	extpm } 0.1$ | $24.0 	extrm{ 	extpm } 0.5$ |

Table 7: Effectiveness of accumulated techniques in LIC. The validation accuracy experiences a progressive improvement as we incrementally apply the four techniques within our LIC.

# <span id="page-19-0"></span>Appendix G. Continual Learning

In comparison to  $\text{SRe}^2L$ , our study implements a five-step class-incremental learning approach using ResNet-18 across CIFAR-10, CIFAR-100, and Tiny-ImageNet datasets, each with an IPC setting of 10. The results of these experiments are depicted in Figures [6,](#page-20-0) [7,](#page-20-1) and [8](#page-20-2) for CIFAR-10, CIFAR-100, and Tiny-ImageNet, respectively.

# <span id="page-19-1"></span>Appendix H. Visualization

**Baselines.** Within the scope of CIFAR-10 distillation under the IPC  $= 10$  setting, we illustrate the visual representations of distilled datasets. This includes visualizations for ADD [\(Zhang et al., 2023\)](#page-10-8) in Figure [11,](#page-23-0) DataDAM [\(Sajedi et al., 2023\)](#page-9-13) in Figure [12,](#page-24-0) SRe2L [\(Yin et al., 2023\)](#page-10-0) in Figure [14,](#page-26-0) and DREAM [\(Liu et al., 2023b\)](#page-9-9) in Figure [13.](#page-25-0) Distilled images of each method are generated starting from actual images, showcased in Figures [9](#page-21-0) and [10.](#page-22-0)

A simple squeezing-based method. The image squeezing process entails resizing and concatenating images to facilitate dataset distillation. For example, consider the manipulation of 4 images, each originally sized at  $224 \times 224$  pixels. The initial step involves downsizing each image to  $112 \times 112$  pixels. Subsequently, these reduced images are merged into a single composite image, effectively reverting to the original resolution of  $224 \times 224$  pixels. This approach underpins a simplistic, squeezing-based dataset distillation method, whereby  $N$ randomly selected original images are compressed into one distilled image to compose a

<span id="page-20-0"></span>Image /page/20/Figure/0 description: This is a line graph showing the Top-1 Accuracy (%) on the y-axis against the Number of Classes on the x-axis. The graph displays two lines: a dashed blue line with diamond markers representing 'SRe2L' and a solid red line with square markers representing 'LIC'. The 'SRe2L' line starts at approximately 70% accuracy with 40 classes, decreases to about 35% with 120 classes, and further drops to around 30% with 200 classes. The 'LIC' line starts at approximately 83% accuracy with 40 classes, decreases sharply to about 50% with 120 classes, and then slightly increases to about 51% with 160 classes and 52% with 200 classes.

<span id="page-20-1"></span>Figure 6: Visualization of continual learning on CIFAR-10 with IPC = 10.

Image /page/20/Figure/2 description: The image is a line graph showing the Top-1 Accuracy (%) on the y-axis against the Number of Classes on the x-axis. The x-axis ranges from 40 to 200 in increments of 20. The y-axis ranges from 25 to 50 in increments of 5. There are two lines plotted: a dashed blue line with diamond markers representing 'SRe2L' and a solid red line with square markers representing 'LIC'. The 'LIC' line starts at approximately 50% accuracy at 40 classes and decreases slightly to about 45% accuracy at 200 classes. The 'SRe2L' line starts at approximately 24% accuracy at 40 classes and increases steadily to about 29% accuracy at 200 classes. The 'LIC' line consistently shows higher accuracy than the 'SRe2L' line across all tested numbers of classes.

<span id="page-20-2"></span>Figure 7: Visualization of continual learning on CIFAR-100 with  $IPC = 10$ .

Image /page/20/Figure/4 description: The image is a line graph showing the Top-1 Accuracy (%) on the y-axis against the Number of Classes on the x-axis. The x-axis ranges from 40 to 200 in increments of 20. The y-axis ranges from 25.0 to 37.5 in increments of 2.5. There are two lines plotted: a dashed blue line with diamond markers representing 'SRe2L' and a solid red line with square markers representing 'LIC'. The 'LIC' line starts at approximately 34% accuracy for 40 classes, rises to about 36.5% for 80 classes, and then plateaus around 36.5% to 37.5% for 160 to 200 classes. The 'SRe2L' line starts at approximately 23% accuracy for 40 classes, steadily increases to about 34.5% for 200 classes. The 'LIC' line consistently shows higher accuracy than the 'SRe2L' line across all tested numbers of classes.

Figure 8: Visualization of continual learning on Tiny-ImageNet with  $IPC = 10$ .

condensed dataset. In the context of distilling CIFAR-10 with an  $IPC = 10$  configuration, we exhibit the visual outcomes of this process for diverse settings:  $N = 1$  in Figure [15,](#page-27-0)  $N = 4$ in Figure [16,](#page-28-0)  $N = 9$  in Figure [17,](#page-29-0)  $N = 16$  in Figure [18,](#page-30-0) and  $N = 25$  in Figure [19.](#page-31-0)

Our proposed LIC. With an IPC setting of 10, we illustrate the distilled datasets generated by our proposed LIC. These include visualizations for CIFAR-10 in Figure [20,](#page-32-0) CIFAR-100 in Figure [21,](#page-33-0) Tiny-ImageNet in Figure [22,](#page-34-0) and ImageNet-1k in Figure [23.](#page-35-0)

<span id="page-21-0"></span>Image /page/21/Picture/0 description: This is a grid of 80 images, arranged in 8 rows and 10 columns. The images are of various objects and animals, including airplanes, cars, birds, cats, dogs, deer, horses, boats, and trucks. The images are all square and have a black border around them. The grid appears to be a visualization of a dataset, possibly for machine learning or image recognition.

Figure 9: Visualization of initialized images before distilling on CIFAR-10.

<span id="page-22-0"></span>Image /page/22/Picture/0 description: This is a grid of 100 images, arranged in 10 rows and 10 columns. The images are small and pixelated, and they appear to be samples from the CIFAR-10 dataset. The dataset contains images of 10 different classes: airplanes, cars, birds, cats, deer, dogs, frogs, horses, ships, and trucks. The images are arranged in a seemingly random order, with multiple examples of each class present in the grid. Some rows appear to be dominated by a single class, while others are more mixed. For example, the first row contains mostly airplanes, the second row contains mostly cars, and the third row contains mostly birds. The remaining rows contain a mix of cats, deer, dogs, frogs, horses, ships, and trucks.

Figure 10: Visualization of initialized data before distilling on CIFAR-10 data showcases the mixture of 4 images per initial instance.

<span id="page-23-0"></span>Image /page/23/Picture/0 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image is a square and appears to be a photograph of an object or animal. The grid contains a variety of subjects, including airplanes, cars, birds, cats, dogs, horses, deer, ships, and trucks. The overall impression is a collage of diverse photographic samples, likely from a dataset used in machine learning or computer vision research, as suggested by the caption below the image which mentions "Synthetic data visualization, CIFAR-10 from ADD (Zhang et al., 2023)".

Figure 11: Synthetic data visualization on CIFAR-10 from ADD [\(Zhang et al., 2023\)](#page-10-8)

<span id="page-24-0"></span>Image /page/24/Picture/0 description: A grid of 100 small, blurry images, arranged in 10 rows and 10 columns. The images appear to be generated by a machine learning model, possibly for a dataset like CIFAR-10, as suggested by the caption below. The individual images are abstract and colorful, with some resembling objects like cars, animals, or patterns, but all are highly pixelated and lack clear definition. The overall impression is a mosaic of abstract, low-resolution visual samples.

Figure 12: Synthetic data visualization on CIFAR-10 from DataDAM [\(Sajedi et al., 2023\)](#page-9-13)

<span id="page-25-0"></span>Image /page/25/Picture/0 description: This is a grid of 100 images, arranged in 10 rows and 10 columns. The images are of various objects and animals, including airplanes, cars, birds, cats, dogs, horses, ships, and trucks. The images are all small and square, and they are arranged in a grid pattern. The overall impression is a collection of diverse images, likely from a dataset used for machine learning or computer vision tasks.

Figure 13: Synthetic data visualization on CIFAR-10 from DREAM [\(Liu et al., 2023b\)](#page-9-9)

<span id="page-26-0"></span>Figure 14: Synthetic data visualization on CIFAR-10 from  ${\rm SRe^2L}$  [\(Yin et al., 2023\)](#page-10-0)

<span id="page-27-0"></span>Image /page/27/Picture/0 description: A grid of 100 images, arranged in 10 rows and 10 columns. The images depict a variety of subjects, including airplanes, cars, birds, cats, dogs, horses, frogs, ships, and trucks. The grid appears to be a dataset or a collection of sample images, possibly for a machine learning task, as indicated by the caption below the grid which reads 'Figure 15. Sample label detection on CIFAR-10 with N=1'.

Figure 15: Squeezed real data visualization on CIFAR-10 with  ${\cal N}=1$ 

<span id="page-28-0"></span>Image /page/28/Picture/0 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image is a photograph of an object or animal. The top rows appear to contain images of airplanes and cars. The middle rows contain images of birds, cats, dogs, and horses. The bottom rows contain images of ships and trucks. The overall impression is a collage of diverse subjects, likely representing a dataset of images for machine learning or computer vision tasks.

Figure 16: Squeezed real data visualization on CIFAR-10 with  ${\cal N}=4$ 

<span id="page-29-0"></span>Image /page/29/Picture/0 description: The image is a grid of many small images, likely from a dataset like CIFAR-10. The grid is organized into rows and columns, with each cell containing a distinct image. The images appear to depict a variety of objects and scenes, including vehicles (cars, airplanes, ships), animals (dogs, cats, birds, horses), and possibly some natural landscapes or other miscellaneous items. The overall impression is a collage of diverse visual content, possibly used for machine learning or image recognition demonstrations.

Figure 17: Squeezed real data visualization on CIFAR-10 with  ${\cal N}=9$ 

<span id="page-30-0"></span>Image /page/30/Figure/0 description: A grid of 100 images, each 32x32 pixels, is displayed. The images are arranged in 10 rows and 10 columns. The overall grid is bordered by a thick black line. The images themselves are separated by thin black lines. The content of the images varies, with many depicting animals such as dogs, cats, and birds, as well as vehicles like cars and airplanes, and some natural scenes with greenery. The bottom of the image contains text that is partially visible, reading "Figure 10. Sampled data visualization - CIFAR-10 with N=10".

Figure 18: Squeezed real data visualization on CIFAR-10 with  ${\cal N}=16$ 

<span id="page-31-0"></span>Image /page/31/Figure/0 description: The image displays a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image appears to be a photograph of various objects or scenes. The overall grid is presented against a white background. Below the grid, there is a caption that reads 'Figure 10. Squeezed real data visualization on CIFAR-10 with N=25'.

Figure 19: Squeezed real data visualization on CIFAR-10 with  ${\cal N}=25$ 

<span id="page-32-0"></span>Image /page/32/Picture/0 description: This is a grid of images, likely from a dataset like CIFAR-10, showcasing various categories. The top rows display airplanes and cars. Subsequent rows feature birds, cats, deer, and dogs. The lower rows contain images of frogs, horses, ships, and trucks. The overall arrangement is a large grid of small, distinct images, each representing one of the dataset's classes.

Figure 20: Synthetic data visualization on CIFAR-10 from LIC(Ours)

<span id="page-33-0"></span>Image /page/33/Picture/0 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. The top row displays various types of apples. The second row shows different kinds of fish. The third row features images of babies. The fourth and fifth rows are filled with pictures of bears. The sixth row contains images of beds. The seventh row displays flowers and insects. The eighth row is dedicated to images of beetles and ladybugs. The ninth row shows various bicycles. The bottom row consists of images of beer bottles.

Figure 21: Synthetic data visualization on CIFAR-100 from LIC(Ours)

<span id="page-34-0"></span>Image /page/34/Picture/0 description: A grid of 100 images, each depicting a different animal. The animals are arranged in rows and columns, with each row featuring a different type of animal. The top row displays various colorful fish. The second row shows salamanders. The third and fourth rows feature different species of frogs. The fifth row contains lizards, and the sixth row shows snakes. The seventh row displays scorpions, and the bottom row is filled with spiders. The images are small and square, creating a mosaic-like effect.

Figure 22: Synthetic data visualization on Tiny-ImageNet from LIC(Ours)

<span id="page-35-0"></span>Image /page/35/Picture/0 description: The image is a grid of many small pictures of animals. The top section of the grid contains pictures of people holding fish. The next section contains pictures of various fish, including goldfish, sharks, and stingrays. The bottom section of the grid contains pictures of chickens and ostriches.

Figure 23: Synthetic data visualization on ImageNet-1k from LIC(Ours)
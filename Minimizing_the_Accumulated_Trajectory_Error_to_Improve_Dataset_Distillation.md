# <span id="page-0-1"></span>Minimizing the Accumulated Trajectory Error to Improve Dataset Distillation

Jiawei Du $^{1,5,2}$ †, <PERSON><PERSON> $^{2}$ †, <PERSON> $^{3,2}$ , <PERSON> $^{1,5\ast}$ , Haizhou Li $^{4,2}$ 

<sup>1</sup>Centre for Frontier AI Research (CFAR), Agency for Science, Technology and Research (A\*STAR), Singapore

<sup>2</sup>Department of Electrical and Computer Engineering, National University of Singapore

<sup>3</sup>Department of Mathematics, National University of Singapore

<sup>4</sup>SRIBD, School of Data Science, The Chinese University of Hong Kong, Shenzhen, China

<sup>5</sup> Institute of High Performance Computing (IHPC), Agency for Science, Technology and Research (A\*STAR), Singapore

{dujiawei,yidi jiang}@u.nus.edu, <EMAIL>, <EMAIL>

## Abstract

*Model-based deep learning has achieved astounding successes due in part to the availability of large-scale real-world data. However, processing such massive amounts of data comes at a considerable cost in terms of computations, storage, training and the search for good neural architectures.* Dataset distillation *has thus recently come to the fore. This paradigm involves distilling information from large realworld datasets into tiny and compact synthetic datasets such that processing the latter ideally yields similar performances as the former. State-of-the-art methods primarily rely on learning the synthetic dataset by* matching the gradients *obtained during training* between *the real and synthetic data. However, these* gradient-matching *methods suffer from the so-called* accumulated trajectory error *caused by the discrepancy between the distillation and subsequent evaluation. To mitigate the adverse impact of this accumulated trajectory error, we propose a novel approach that encourages the optimization algorithm to seek a flat trajectory. We show that the weights trained on synthetic data are robust against the accumulated errors perturbations with the regularization towards the flat trajectory. Our method, called Flat Trajectory Distillation (FTD), is shown to boost the performance of gradient-matching methods by up to 4.7% on a subset of images of the ImageNet dataset with higher resolution images. We also validate the effectiveness and generalizability of our method with datasets of different resolutions and demonstrate its applicability to neural architecture search. Code is available at [.h](https://github.com/AngusDujw/FTD-distillation)ttps://github.com/AngusDujw/FTDdistillation.*

# 1. Introduction

Modern deep learning has achieved astounding successes in achieving ever better performances in a wide range of

<span id="page-0-0"></span>Image /page/0/Figure/13 description: The image is a line graph titled "ConvNet on CIFAR 100, IPC=10". The x-axis represents "Epoch" and ranges from 0 to 50. The left y-axis is labeled "LT\_test(f\_θ) - LT\_test(f\*\_)" and ranges from 0.0 to 0.6. The right y-axis is labeled "LT\_test(f\_θ)" and ranges from 2.5 to 4.5. There are three lines plotted: "MTT in Distillation" (blue line with circles), "MTT in Evaluation" (blue line with triangles), and "Ours in Evaluation" (pink line with diamonds). The "MTT in Distillation" line starts at approximately 0.62 on the left y-axis at Epoch 0 and decreases to around 0.02 at Epoch 50, with fluctuations. The "MTT in Evaluation" line starts at approximately 0.08 on the left y-axis at Epoch 0, rises to about 0.45 at Epoch 20, and then fluctuates between 0.02 and 0.08 from Epoch 20 to 50. The "Ours in Evaluation" line starts at approximately 0.08 on the left y-axis at Epoch 0, gradually increases to about 0.42 at Epoch 50. The right y-axis shows the values for the "MTT in Evaluation" and "Ours in Evaluation" lines, which are generally increasing over time.

Figure 1. The change of the loss difference  $L_{\mathcal{T}_{\text{Test}}} (f_{\theta}) - L_{\mathcal{T}_{\text{Test}}} (f_{\theta^*})$ , in which  $\theta$  and  $\theta^*$  denote the weights optimized by synthetic dataset S and real dataset  $\mathcal T$ , respectively. The gray line represents  $L_{\mathcal T_{\rm Test}}(f_{\theta^*})$  and is associated with the gray y-axis of the plot with two  $y$ -axes. The lines indicated by "Evaluation" represent the networks that are initialized at epoch 0 and trained with the synthetic dataset for 50 epochs. The line indicated by "Distillation" represents the network that is initialized at epochs  $2, 4, \ldots, 48$ and trained with the synthetic dataset for 2 epochs. The former lines have much higher loss difference compared to the latter; this is caused by the accumulated trajectory error. And we try to minimize it in the evaluation phase, so that the loss difference line of our method is lower and tends to converge than that of MTT [\[1\]](#page-8-0).

fields by exploiting large-scale real-world data and wellconstructed Deep Neural Networks (DNN) [\[4,](#page-8-1)[5,](#page-8-2)[9\]](#page-8-3). Unfortunately, these achievements have come at a prohibitively high cost in terms of computation, particularly when it relates to the tasks of data storage, network training, hyperparameter tuning, and architectural search.

A series of model distillation studies [\[2,](#page-8-4) [14,](#page-8-5) [16,](#page-8-6) [22\]](#page-8-7) has thus been proposed to condense the scale of models by distilling the knowledge from a large-scale teacher model into a compact student one. Recently, a similar but distinct task, *dataset distillation* [\[1,](#page-8-0) [3,](#page-8-8) [26,](#page-8-9) [35,](#page-9-0) [36,](#page-9-1) [42–](#page-9-2)[45,](#page-9-3) [47,](#page-9-4) [49,](#page-9-5) [50\]](#page-9-6) has been considered to condense the size of real-world datasets. This task aims to synthesize a large-scale real-world dataset into a tiny synthetic one, such that a model trained with the synthetic dataset is comparable to the one trained with the

<sup>\*</sup>Corresponding Author. † Equal Contribution.

<span id="page-1-1"></span>real dataset. Dataset distillation can expedite model training and reduce cost. It plays an important role in some machine learning tasks such as continual learning [\[39,](#page-9-7) [48–](#page-9-8)[50\]](#page-9-6), neural architecture search [\[19,](#page-8-10)[37,](#page-9-9)[38,](#page-9-10)[48,](#page-9-8)[50\]](#page-9-6), and privacy-preserving tasks [\[8,](#page-8-11) [13,](#page-8-12) [27\]](#page-8-13), etc.

Wang et al. [\[45\]](#page-9-3) was the first to formally study dataset distillation. The authors proposed a method DD that models a regular optimizer as the function that treats the synthetic dataset as the inputs, and uses an additional optimizer to update the synthetic dataset pixel-by-pixel. Although the performance of DD degrades significantly compared to training on the real dataset, [\[45\]](#page-9-3) revealed a promising solution for condensing datasets. In contrast to conventional methods, they introduced an evaluation standard for synthetic datasets that uses the learned distilled set to train randomly initialized neural networks and the authors evaluate their performance on the real test set. Following that, Such et al. [\[41\]](#page-9-11) employed a generative model to generate the synthetic dataset. Nguyen et al. [\[34\]](#page-9-12) reformulated the inner regular optimization of DD into a kernel-ridge regression problem, which admits closed-form solution.

In particular, Zhao and Bilen [\[50\]](#page-9-6) pioneered a gradientmatching approach DC, which learns the synthetic dataset by minimizing the distance between two segments of gradients calculated from the real dataset  $\mathcal T$ , and the synthetic dataset  $S$ . Instead of learning a synthetic dataset through a bi-level optimization as DD does, DC [\[50\]](#page-9-6) optimizes the synthetic dataset explicitly and yields much better performance compared to DD. Along the lines of DC [\[50\]](#page-9-6), more gradientmatching methods have been proposed to further enhance DC from the perspectives of data augmentation [\[48\]](#page-9-8), feature alignment [\[44\]](#page-9-13), and long-range trajectory matching [\[1\]](#page-8-0).

However, these follow-up studies on gradient-matching methods fail to address a serious weakness that results from the discrepancy between training and testing phases. In the training phase, the trajectory of the weights generated by S is optimized to reproduce the trajectory of  $\mathcal T$  which commenced from a set of weights that were progressively updated by  $\mathcal T$ . However, in the testing phase, the weights are no longer initialized by the weights with respect to  $\mathcal{T}$ , but the weights that are continually updated by  $S$  in previous iterations. The discrepancy of the starting points of the training and testing phases results in an error on the converged weights. Such inaccuracies will accumulate and have an adverse impact on the starting weight for subsequent iterations. As demonstrated in [Figure 1,](#page-0-0) we observe the loss difference between the weights updated by  $S$  and  $T$ . We refer to the error as the *accumulated trajectory error*, because it grows as the optimization algorithm progresses along its iterations.

The synthetic dataset  $S$  optimized by the gradientmatching methods is able to generalize to various starting weights, but is not sufficiently robust to mitigate the perturbation caused by the accumulated trajectory error of the

starting weights. To minimize this source of error, the most straightforward approach is to employ robust learning, which adds perturbations to the starting weights intentionally during training to make  $S$  robust to errors. However, such a robust learning procedure will increase the amount of information of the real dataset to be distilled. Given a fixed size of  $S$ , the distillation from the increased information results in convergence issues and will degrade the final performance. We demonstrate this via empirical studies in [subsection 3.2.](#page-3-0)

In this paper, we propose a novel approach to minimize the accumulated trajectory error that results in improved performance. Specifically, we regularize the training on the real dataset to a flat trajectory that is robust to the perturbation of the weights. Without increasing the information to be distilled in the real dataset, the synthetic dataset will enhance its robustness to the accumulated trajectory error at no cost. Thanks to the improved tolerance to the perturbation of the starting weights, the synthetic dataset is also able to ameliorate the accumulation of inaccuracies and improves the generalization during the testing phase. It can also be applied to cross-architecture scenarios. Our proposed method is compatible with the gradient-matching methods and boost their performances. Extensive experiments demonstrate that our solution minimizes the accumulated error and outperforms the vanilla trajectory matching method on various datasets, including CIFAR-10, CIFAR-100, subsets of the TinyImageNet, and ImageNet. For example, we achieve performance accuracies of 43.2% with only 10 images per class and 50.7% with 50 images per class on CIFAR-100, compared to the previous state-of-the-art work from [\[1\]](#page-8-0) (which yields accuracies of only 40.1% and 47.7% respectively). In particular, we significantly improve the performance on a subset of the ImageNet dataset which contains higher resolution images by more than 4%.

## <span id="page-1-0"></span>2. Preliminaries and Related Work

Problem Statement. We start by briefly overviewing the problem statement of Dataset Distillation. We are given a real dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|},$  where the examples  $x_i \in \mathbb{R}^d$ and the class labels  $y_i \in \mathcal{Y} = \{0, 1, \ldots, C-1\}$  and C is the number of classes. *Dataset Distillation* refers to the problem of synthesizing a new dataset  $S$  whose size is much smaller than that of  $T$  (i.e., it contains much fewer pairs of synthetic examples and their class labels), such that a model  $f$  trained on the synthetic dataset  $S$  is able to achieve a comparable performance over the real data distribution  $P_{\mathcal{D}}$  as the model f trained with the original dataset  $\mathcal{T}$ .

We denote the synthetic dataset S as  $\{(s_i, y_i)\}_{i=1}^{|\mathcal{S}|}$  where  $s_i \in \mathbb{R}^d$  and  $y_i \in \mathcal{Y}$ . Each class of S contains ipc (images per class) examples. In this case,  $|S| = \text{ipc} \times C$  and  $|\mathcal{S}| \ll |\mathcal{T}|$ . We denote the optimized weight parameters obtained by minimizing an empirical loss term over the <span id="page-2-6"></span>synthetic training set  $S$  as

$$
\theta^{\mathcal{S}} = \underset{\theta}{\arg\min} \sum_{(s_i, y_i) \in \mathcal{S}} \ell(f_{\theta}, s_i, y_i),
$$

where  $\ell$  can be an arbitrary loss function which is taken to be the cross entropy loss in this paper. *Dataset Distillation* aims at synthesizing a synthetic dataset  $S$  to be an approximate solution of the following optimization problem

$$
S_{\rm DD} = \underset{S \subset \mathbb{R}^d \times \mathcal{Y}, |S| = \text{ipc} \times C}{\arg \min} L_{\mathcal{T}_{\rm Test}}(f_{\theta S}). \tag{1}
$$

Wang et al.  $[45]$  proposed DD to solve S by optimizing [Equation 1](#page-2-0) after replacing  $\mathcal{T}_{\text{Test}}$  with  $\mathcal{T}$ , i.e., minimizing  $L_{\mathcal{T}}(f_{\theta S})$  directly because  $\mathcal{T}_{\text{Test}}$  is inaccessible.

Gradient-Matching Methods. Unfortunately, DD's [\[45\]](#page-9-3) performance is poor because optimizing [Equation 1](#page-2-0) only provides limited information for distilling the real dataset  $\mathcal T$ into the synthetic dataset S. This motivated Zhao et al. [\[50\]](#page-9-6) to propose a so-called *gradient-matching method* DC to *match* the informative gradients calculated by  $T$  and  $S$  at each iteration to enhance the overall performance. Namely, they considered solving

$$
\mathcal{S}_{\rm DC} = \underset{\substack{S \subset \mathbb{R}^d \times \mathcal{Y} \\ |S| = \text{ipc} \times C}}{\arg \min} \mathbb{E} \left[ \sum_{m=1}^M \mathcal{L}(\mathcal{S}) \right], \quad \text{where} \quad (2)
$$

$$
\mathcal{L}(\mathcal{S}) = D(\nabla_{\theta_m} L_{\mathcal{S}}(f_{\theta_m}), \nabla_{\theta_m} L_{\mathcal{T}}(f_{\theta_m})).
$$
\n(3)

In the definition of  $\mathcal{L}(\mathcal{S})$ ,  $\theta_m$  contains the weights updated from the initialization  $\theta_0$  with  $\mathcal T$  at iteration m. The initial set of weights  $\theta_0$  is randomly sampled from an initialization distribution  $P_{\theta}$  and M in [Equation 2](#page-2-1) is the total number of update steps. Finally,  $D(\cdot, \cdot)$  in [Equation 3](#page-2-2) denotes a (cosine similarity-based) distance function measuring the discrepancy between two matrices and is defined as  $D(X,Y) = \sum_{i=1}^{I} \left(1 - \frac{\langle X_i, Y_i \rangle}{\|X_i\| \|Y_i\|} \right)$  $\frac{\langle X_i, Y_i \rangle}{\|X_i\| \|Y_i\|}$ , where  $X, Y \in \mathbb{R}^{I \times J}$ and  $X_i, Y_i \in \mathbb{R}^J$  are the i<sup>th</sup> columns of X and Y respectively. At each distillation (training) iteration, DC [\[50\]](#page-9-6) minimizes the  $\mathcal{L}(\mathcal{S})$  as defined in [Equation 3.](#page-2-2) The Gradient-Matching Method regularizes the distillation of  $S$  by matching the gradients of single-step (DC  $[50]$ ), or multiple-step (MTT  $[1]$ ) for improved performance. More related works can be found in [Appendix B.](#page-12-0)

# 3. Methodology

The gradient-matching methods as discussed in [section 2](#page-1-0) constitute a reliable and state-of-the-art approach for dataset distillation. These methods match a short range of gradients with respect to a sets of the weights trained with the real dataset in the distillation (training) phase. However, the gradients calculated in the evaluation (testing) phase

<span id="page-2-5"></span>Image /page/2/Figure/11 description: This image displays three diagrams illustrating a process involving teacher and student trajectories. Diagram (a), labeled 'Buffer', shows a dotted blue line representing the teacher's trajectory with points labeled \( heta\_{M-1, m}^\*\) down to \( heta\_{0,0}^\*\). Diagram (b), labeled 'Distillation', shows the student's trajectory in a solid red line, closely following the teacher's trajectory. It highlights the difference \(\delta\) between the teacher's point \(\theta\_{t,m}^\*\) and the student's estimated point \(\hat{\theta}\_{0,n}\), and shows that \(\hat{\theta}\_{t,0} = \theta\_{t,0}^\*\). Diagram (c), labeled 'Evaluation', also shows the student's trajectory following the teacher's. It illustrates error metrics \(\mathcal{E}\_t\) and \(\mathcal{E}\_{t+1}\) as the difference between the two trajectories at different points, along with a difference \(\delta\) and an interval \(\mathcal{I}\_t\).

<span id="page-2-0"></span>Figure 2. Illustration of trajectory matching: (a) A teacher trajectory is obtained by recording the intermediate network parameters at every epoch trained on the real dataset  $T$  in the buffer phase. (b) The synthetic dataset  $S$ is optimized to match the segments of the student trajectory with the teacher trajectory in the distillation phase. (c) The entire student trajectory and the accumulated trajectory error  $\epsilon_t$  in the evaluation phase is shown. We aim to minimize this accumulated trajectory error.

<span id="page-2-1"></span>are with respect to the recurrent weights from previous iterations, instead of the exact weights from the teacher's trajectory. Unfortunately, this discrepancy between the distillation (training) and evaluation (testing) phases result in a so-called *accumulated trajectory error*. We take MTT [\[1\]](#page-8-0) as an instance of a gradient-matching method to explain the existence of such an error in [subsection 3.2.](#page-3-0) We then propose a novel and effective method to mitigate the accumulated trajectory error in [subsection 3.3.](#page-3-1)

### <span id="page-2-2"></span>3.1. Matching Training Trajectories (MTT)

In contrast to DC [\[50\]](#page-9-6), MTT [\[1\]](#page-8-0) matches the *accumulated gradients* over several steps (i.e., over a segment of the trajectory of updated weights), to further improve the overall performance. Therefore, MTT [\[1\]](#page-8-0) solves for  $S$  as follows

$$
\mathcal{S}_{\text{MTT}} = \underset{\mathcal{S} \subset \mathbb{R}^d \times \mathcal{Y}, |\mathcal{S}| = \text{ipc} \times C}{\text{arg min}} \mathop{\mathbb{E}}_{\theta_0 \sim P_{\theta_0}} [\Delta \mathcal{A}], \quad \text{where} \quad (4)
$$

$$
\Delta \mathcal{A} = ||\mathcal{A}[\nabla_{\theta} L_{\mathcal{S}}(f_{\theta_0}), n] - \mathcal{A}[\nabla_{\theta} L_{\mathcal{T}}(f_{\theta_0}), m]||_2^2.
$$
 (5)

### 3.3. Flat Trajectory helps reduce the accumulated trajectory error

<span id="page-2-4"></span><span id="page-2-3"></span>
$$
\mathcal{A}[\nabla_{\theta}L_{\mathcal{S}}(f_{\theta_0}), n] = \theta_n - \theta_0.
$$

We model  $A$  as a function with input being the gradient  $\nabla_{\theta}L_{\mathcal{S}}(f_{\theta_0})$ , which is run over a number of iterations n, and whose output is the accumulated change of weights after  $n$ iterations. Note that n, m are set so that  $n < m$  because  $|\mathcal{S}| \ll |\mathcal{T}|$ . [Equation 4](#page-2-4) particularizes to [Equation 2](#page-2-1) when  $n = m = 1$ .

<span id="page-3-4"></span>Intuitively, MTT [\[1\]](#page-8-0) learns an informative synthetic dataset  $S$  so that it can provide sufficiently reliable information to the optimizer  $A$ . Then,  $A$  utilizes the information from S to map the weights  $\theta_0$  sampled from its (initialization) distribution  $P_{\theta_0}$  into an approximately-optimal parameter space  $W = \{\theta | L_{\mathcal{T}_{\text{Test}}}(f_{\theta}) \leq L_{\text{tol}}\}\$ , where  $L_{\text{tol}} > 0$ denotes an "tolerable minimum value".

In the actual implementation, the ground truth trajectories, also known as the *teacher trajectories*, are prerecorded in the *buffer* phase as  $(\theta_{0,0}^*, \ldots, \theta_{0,m}^*, \theta_{1,0}^*, \ldots, \theta_{M-1,m}^*)$ . As illustrated in Figure  $2(a)$ , the teacher trajectories are trained until convergence on the real dataset  $T$  with a random initialization  $\theta_{0,0}^*$ . The long teacher trajectories are then partitioned into M segments  ${\{\Theta_t^*\}}_{t=0}^{M-1}$  and each segment  $\Theta_t^* = (\theta_{t,0}^*, \theta_{t,1}^*, \dots, \theta_{t,m}^*)$ . Note that  $\theta_{t,0}^* = \theta_{t-1,m}^*$  since the last set of weights of the segment will be used to initialize the first set of weights of the next one.

As shown in Figure  $2(b)$ , in the distillation phase, a segment of the weights  $\Theta_t^*$  is randomly sampled from  $\{\Theta_t^*\}_{t=0}^{M-1}$  and used to initialize the *student trajectory*  $(\hat{\theta}_{t,0}, \hat{\theta}_{t,1}, \dots, \hat{\theta}_{t,n})$  which satisfies  $\hat{\theta}_{t,0} = \theta_{t,0}^*$ . In summary,

$$
\begin{aligned} \theta_{t,m}^* &= \theta_{t,0}^* + \mathcal{A}[\nabla_\theta L_{\mathcal{T}}(f_{\theta_{t,0}^*}),m], \quad \text{and} \\ \hat{\theta}_{t,n} &= \hat{\theta}_{t,0} + \mathcal{A}[\nabla_\theta L_{\mathcal{S}}(f_{\hat{\theta}_{t,0}}),n]. \end{aligned}
$$

Subsequently, MTT [\[1\]](#page-8-0) solves [Equation 4](#page-2-4) by minimizing, at each distillation iteration, the following loss over  $S$ :

$$
\mathcal{L}(S) = \frac{\|\hat{\theta}_{t,n} - \theta_{t,m}^*\|_2^2}{\|\theta_{t,0}^* - \theta_{t,m}^*\|_2^2}
$$
  
= 
$$
\frac{\|\theta_{t,0}^* + \mathcal{A}[\nabla_{\theta} L_{S}(f_{\theta_{t,0}^*}) , n] - \theta_{t,m}^*\|_2^2}{\|\theta_{t,0}^* - \theta_{t,m}^*\|_2^2}
$$
  
= 
$$
\frac{\|\mathcal{A}[\nabla_{\theta} L_{S}(f_{\theta_{t,0}^*}), n] - \mathcal{A}[\nabla_{\Theta} L_{\mathcal{T}}(f_{\theta_{t,0}^*}), m] \|_2^2}{\|\theta_{t,0}^* - \theta_{t,m}^*\|_2^2}
$$

The synthetic dataset S is obtained by minimizing  $\mathcal{L}(\mathcal{S})$ to be informative to guide the optimizer to update weights initialized at  $\theta_{t,0}^*$  to eventually reach the target weights  $\theta_{t,m}^*$ .

.

### <span id="page-3-0"></span>3.2. Accumulated Trajectory Error

The student trajectory, to be matched in the distillation phase, is only one segment from  $\hat{\theta}_{t,0}$  to  $\hat{\theta}_{t,n}$  initialized from a precise  $\theta_{t,0}^*$  from the teacher trajectory, i.e.,  $\hat{\theta}_{t,0} = \theta_{t,0}^*$ . In the distillation phase, the *matching error* is defined as

$$
\delta_t = \mathcal{A}[\nabla_{\theta} L_{\mathcal{S}}(f_{\theta_{t-1,m}^*}), n] - \mathcal{A}[\nabla_{\theta} L_{\mathcal{T}}(f_{\theta_{t-1,m}^*}), m].
$$
 (6)

and  $\delta_t$  can be minimized in the distillation phase. However, in the actual evaluation phase, the optimization procedure of student trajectory is extended, and each segment is no longer initialized from the teacher trajectory but rather the last set of weights in the previous segments, i.e.,  $\hat{\theta}_{t,0} = \hat{\theta}_{t-1,n}$ . This

discrepancy will result in a so-called *accumulated trajectory error*, which is the difference between the weights from the teacher and student trajectory in  $t<sup>th</sup>$  segment, i.e.,

$$
\epsilon_t = \hat{\theta}_{t+1,0} - \theta_{t+1,0}^* = \hat{\theta}_{t,n} - \theta_{t,m}^*
$$

The initialization discrepancy between the distillation phase and the evaluation phase will incur an *initialization error*  $\mathcal{I}_t = \mathcal{I}(\theta_{t,0}^*, \epsilon_t)$ , representing the difference in accumulated gradients. It can be represented mathematically as:

<span id="page-3-5"></span>
$$
\mathcal{I}_t = \mathcal{A}[\nabla_{\theta} L_{\mathcal{S}}(f_{\theta_{t,0}^* + \epsilon_t}), n] - \mathcal{A}[\nabla_{\theta} L_{\mathcal{S}}(f_{\theta_{t,0}^*}), n], \quad (7)
$$

In the next segment,  $\epsilon_{t+1}$  can be derived as follows

$$
\epsilon_{t+1} = \hat{\theta}_{t+2,0} - \theta_{t+2,0}^* = \hat{\theta}_{t+1,n} - \theta_{t+1,m}^*
$$
  
\n
$$
= (\hat{\theta}_{t,n} + \mathcal{A}[\nabla_{\theta}L_{S}(f_{\hat{\theta}_{t,n}}), n])
$$
  
\n
$$
- (\theta_{t,m}^* + \mathcal{A}[\nabla_{\theta}L_{T}(f_{\theta_{t,m}^*}), m])
$$
  
\n
$$
= (\mathcal{A}[\nabla_{\theta}L_{S}(f_{\hat{\theta}_{t,n}}), n] - \mathcal{A}[\nabla_{\theta}L_{T}(f_{\theta_{t,m}^*}), m])
$$
  
\n
$$
+ (\hat{\theta}_{t,n} - \theta_{t,m}^*)
$$
  
\n
$$
= (\mathcal{A}[\nabla_{\theta}L_{S}(f_{\theta_{t,m}^* + \epsilon_t}), n] - \mathcal{A}[\nabla_{\theta}L_{S}(f_{\theta_{t,m}^*}), n])
$$
  
\n
$$
+ (\mathcal{A}[\nabla_{\theta}L_{S}(f_{\theta_{t,m}^*}), n] - \mathcal{A}[\nabla_{\theta}L_{T}(f_{\theta_{t,m}^*}), m] + \epsilon_t)
$$
  
\n
$$
= \epsilon_t + \mathcal{I}(\theta_{t,m}^*, \epsilon_t) + \delta_{t+1}.
$$
 (8)

<span id="page-3-2"></span>The accumulated trajectory error  $\epsilon_{t+1}$  continues to accumulate the initialization error  $\mathcal{I}(\theta^*_{t,m}, \epsilon_t)$ , the matching error  $\epsilon_t$ , and the  $\epsilon_t$  in previous segment. It also impacts the accumulation of errors in subsequent segments, and thereby degrading the final performance. This is illustrated in Figure  $2(c)$ . We conduct experiments to verify the existence of the accumulated trajectory error, which are demonstrated in [Figure 1,](#page-0-0) more exploring about accumulated trajectory error can be found in Appendix [A.1.](#page-10-0)

## <span id="page-3-1"></span>3.3. Flat Trajectory helps reduce the accumulated trajectory error

From [Equation 8,](#page-3-2) we seek to minimize  $\Delta \epsilon_{t+1} = \epsilon_{t+1}$  –  $\epsilon_t = \mathcal{I}_t + \delta_{t+1}$  where  $\delta_{t+1}$  is the matching error of gradientmatching methods, which has been optimized to a small value in the distillation phase. However, the initialization error  $\mathcal{I}_t$  is not optimized in the distillation phase. The existence of  $\mathcal{I}_t$  results from the gap between the distillation and evaluation phases. To minimize it, a straightforward approach is to design the synthetic dataset  $S$  which is robust to the perturbation  $\epsilon$  in the distillation phase. This is done by adding random noise to initialize the weights, i.e.,

<span id="page-3-3"></span>
$$
S = \underset{\substack{S \subset \mathbb{R}^d \times \mathcal{Y}, \quad \theta_0 \sim P_{\theta_0} \\ |S| = \operatorname{ipc} \times C \quad \epsilon \sim \mathcal{N}(0, \sigma^2 \mathbf{I})}}{\arg \min} \mathbb{E} [\mathcal{L}(S, \theta_0, \epsilon)], \text{ where}
$$

$$
\mathcal{L}(S, \theta_0, \epsilon) = ||\mathcal{A}[L_S(f_{\theta_0 + \epsilon}), n] - \mathcal{A}[L_\mathcal{T}(f_{\theta_0}), m]||_2^2, \quad (9)
$$

<span id="page-4-2"></span>and  $\mathcal{N}(\mathbf{0}, \sigma^2 \mathbf{I})$  is a Gaussian with mean 0 and covariance  $\sigma^2$ I. However, we find that solving [Equation 9](#page-3-3) results in a degradation of the final performance when the number of images per class of S is not large (e.g., ipc  $\in \{1, 10\}$ ). It only can improve the final performance when  $\rm ipc = 50$ . These experimental results are reported in [Table 1](#page-5-0) and labelled as "Robust Learning". A plausible explanation is that adding random noise to the initialized weights  $\theta_0 + \epsilon$ in the distillation phase is equivalent to mapping a more dispersed (spread out) distribution  $P_{\theta_0+\epsilon}$  into the parameter space  $W = \{ \theta \mid L_{\mathcal{T}_{\text{Test}}}(f_{\theta}) \leq L_{\text{tol}} \}$ , which necessitates more information per class (i.e., larger  $ipc$ ) from S in order to ensure convergence, hence degrading the distilling effectiveness when  $\text{ipc} \in \{1, 10\}$  is relatively small.

We thus propose an alternative approach to regularize the teacher trajectory to a *Flat Trajectory for Distillation* (FTD). Our goal is to distill a synthetic dataset whose standard training trajectory is flat; in other words, it is robust to the weight perturbations with the guidance of the teacher trajectory. Without exceeding the capacity of information per class (ipc), FTD improves the buffer phase to make the teacher trajectory robust to weight perturbation. As such, the flat teacher trajectory will guide the distillation gradient update to synthesize a dataset with the flat trajectory characteristic in a standard optimization procedure.

We aim to minimize  $\mathcal{I}_t$  to ameliorate the adverse effect caused by  $\epsilon_t$ . Assuming that  $||\epsilon_t||_2^2$  is small, we can first rewrite the accumulated trajectory error [Equation 8](#page-3-2) using a first-order Taylor series approximation as  $\mathcal{I}_t = \mathcal{I}(\theta_t^*, \epsilon_t)$  $\langle \frac{\partial A}{\partial \epsilon_t}, \epsilon_t \rangle + O(||\epsilon_t||^2)$ 1 (where 1 is the all-ones vector). To solve for  $\theta_t^*$  that approximately minimizes the  $\ell_2$  norm of  $\mathcal{I}(\theta_t^*, \epsilon_t)$  in the buffer phase, we note that

$$
\theta_t^* = \underset{\theta_t}{\arg\min} \|\mathcal{I}(\theta_t^*, \epsilon_t)\|_2^2 \approx \underset{\theta_t}{\arg\min} \left\|\frac{\partial \mathcal{A}}{\partial \epsilon_t}\right\|_2^2
$$
$$
= \underset{\theta_t}{\arg\min} \left\|\frac{\partial \mathcal{A}}{\partial \nabla_{\theta} L_S(f_{\theta_t^*})} \cdot \frac{\partial \nabla_{\theta} L_S(f_{\theta_t^*})}{\partial \theta} \cdot \frac{\partial \theta}{\partial \epsilon_t}\right\|_2^2. (10)
$$

Since  $A$  is the first-order optimizer sans momentum, which has been modeled as a function as discussed after [Equation 4.](#page-2-4) Therefore,  $\frac{\partial A}{\partial \nabla_{\theta} L_S(f_{\theta_i^*})} = \eta$ , where  $\eta$  is the learning rate used in A. Because  $\theta = \theta_t^* + \epsilon$ , we have  $\frac{\partial \theta}{\partial \epsilon} = 1$ . Substituting these derivatives into [Equation 10,](#page-4-0) we obtain

$$
\arg\min_{\theta_t} \|\mathcal{I}(\theta_t^*, \epsilon_t)\|_2^2 \approx \arg\min_{\theta_t} \left\|\frac{\partial \nabla_{\theta} L_{\mathcal{S}}(f_{\theta_t^*})}{\partial \theta}\right\|_2^2
$$
$$
= \arg\min_{\theta_t} \left\|\nabla_{\theta}^2 L_{\mathcal{S}}(f_{\theta_t^*})\right\|_2^2. \quad (11)
$$

Minimizing  $\|\nabla_{\theta}^2 L_{\mathcal{S}}(f_{\theta_t^*})\|_2^2$  is obviously equivalent to minimizing the largest eigenvalue of the Hessian  $\nabla_{\theta}^2 L_{\mathcal{S}}(f_{\theta_t^*})$ . Unfortunately, the computation of the largest eigenvalue is expensive. Fortunately, the largest eigenvalue of  $\nabla_{\theta}^2 L_{\mathcal{S}}(f_{\theta_t^*})$ 

has also be regarded as the sharpness of the loss landscape, which has been well-studied by many works such as SAM  $[11]$  and GSAM  $[51]$ . In our work, we employ GSAM to help solve [Equation 11](#page-4-1) to find a teacher trajectory that is as flat as possible. The sharpness  $S(\theta)$ , can be quantified using

<span id="page-4-3"></span>
$$
S(\theta) \triangleq \max_{\epsilon \in \Psi} \left[ L_{\mathcal{T}}(f_{\theta + \epsilon}) - L_{\mathcal{T}}(f_{\theta}) \right] \tag{12}
$$

where  $\Psi = \{\epsilon : ||\epsilon||_2 \leq \rho\}$  and  $\rho > 0$  is a given constant that determines the permissible norm of  $\epsilon$ . Then,  $\theta^*$  is obtained in the buffer phase by solving a minimax problem as follows,

<span id="page-4-4"></span>
$$
\theta^* = \underset{\theta}{\arg\min} \left\{ L_{\mathcal{T}}(f_{\theta}) + \alpha S(\theta) \right\},\tag{13}
$$

where  $\alpha$  is the coefficient that balances the robustness of  $\theta^*$ to the perturbation. From the above derivation, we see that a different teacher trajectory is proposed. This trajectory is robust to the perturbation of the weights in the buffer phase so as to reduce the accumulated trajectory error in the evaluation phase. The details about our algorithm and the optimization of  $\theta^*$  can be found in Appendix [A.3.2.](#page-11-0)

#### 4. Experiments

In this section, we verify the effectiveness of FTD through extensive experiments. We conduct experiments to compare FTD to state-of-the-art baseline methods evaluated on datasets with different resolutions. We emphasize the crossarchitecture performance and generalization capabilities of the generated synthetic datasets. We also conduct extensive ablation studies to exhibit the enhanced performance and study the influence of hyperparameters. Finally, we apply our synthetic dataset to neural architecture search and demonstrate its reliability in performing this important task.

#### <span id="page-4-0"></span>4.1. Experimental Setup

We follow up the conventional procedure used in the literature on dataset distillation. Every experiment involves two phases—distillation and evaluation. First, we synthesize a small synthetic set (e.g., 10 images per class) from a given large real training set. We investigate three settings  $\text{ipc} =$ 1, 10, 50, which means that the distilled set contains 1, 10 or 50 images per class respectively. Second, in the evaluation phase on the synthetic data, we utilize the learnt synthetic set to train randomly initialized neural networks and test their performance on the real test set. For each synthetic set, we use it to train five networks with random initializations and report the mean accuracy and its standard deviation for 1000 iterations with a standard training procedure.

<span id="page-4-1"></span>Datasets. We evaluate our method on various resolution datasets. We consider the CIFAR10 and CIFAR100 [\[23\]](#page-8-15) datasets which consist of tiny colored natural images with

<span id="page-5-2"></span><span id="page-5-0"></span>Table 1. Comparison of the performances trained with ConvNet [\[12\]](#page-8-16) to other distillation methods on the CIFAR [\[23\]](#page-8-15) and Tiny ImageNet [\[25\]](#page-8-17) datasets. We reproduce the results of MTT [\[1\]](#page-8-0). We cite the reported results of other baselines from Cazenavette et al. [\[1\]](#page-8-0). We only provide our reproduced results of DC and MTT on the Tiny ImageNet dataset as previous works did not report their results on this dataset.

|                     | $CIFAR-10$     |                               |                               |                               | CIFAR-100                     | Tiny ImageNet                                                                                            |               |                |
|---------------------|----------------|-------------------------------|-------------------------------|-------------------------------|-------------------------------|----------------------------------------------------------------------------------------------------------|---------------|----------------|
| ipc                 |                | 10                            | 50                            |                               | 10                            | 50                                                                                                       |               | 10             |
| real dataset        | $84.8 \pm 0.1$ |                               |                               |                               | $56.2 \pm 0.3$                | $37.6 \pm 0.4$                                                                                           |               |                |
| DC [50]             | $28.3 \pm 0.5$ |                               | $44.9 \pm 0.5$ 53.9 $\pm$ 0.5 | $12.8 \pm 0.3$ $25.2 \pm 0.3$ |                               |                                                                                                          |               |                |
| DM [49]             | $26.0 + 0.8$   |                               | $48.9+0.6$ 63.0+0.4           | $11.4 \pm 0.3$                | $29.7 \pm 0.3$                | $43.6 \pm 0.4$                                                                                           | $3.9 + 0.2$   | $12.9 \pm 0.4$ |
| DSA [48]            | $28.8 \pm 0.7$ | $52.1 \pm 0.5$                | $60.6 \pm 0.5$                | $13.9 \pm 0.3$                | $32.3 \pm 0.3$                | $42.8 \pm 0.4$                                                                                           |               |                |
| <b>CAFE</b> [44]    | $30.3 + 1.1$   |                               | $46.3 \pm 0.6$ 55.5 $\pm$ 0.6 | $12.9 \pm 0.3$                | $27.8 \pm 0.3$                | $37.9 + 0.3$                                                                                             |               |                |
| CAFE+DSA            | $31.6 + 0.8$   | $50.9 \pm 0.5$                | $62.3 \pm 0.4$                | $14.0 \pm 0.3$                | $31.5 \pm 0.2$                | $42.9 \pm 0.2$                                                                                           |               |                |
| PP [28]             |                | $46.4 \pm 0.6$ $65.5 \pm 0.3$ | $71.9 \pm 0.2$                | $24.6 \pm 0.1$                | $43.1 \pm 0.3$                | $48.4 \pm 0.3$                                                                                           |               |                |
| MTT[1]              | $46.2 \pm 0.8$ | $65.4 \pm 0.7$                | $71.6 \pm 0.2$                | $24.3 \pm 0.3$                | $39.7 \pm 0.4$                | $47.7 \pm 0.2$                                                                                           | $8.8 \pm 0.3$ | $23.2 + 0.2$   |
| MTT+Robust Learning | $45.8 \pm 0.7$ | $63.2 \pm 0.7$                | 72.7±0.2                      |                               | $24.1 \pm 0.3$ 39.4 $\pm$ 0.4 | $47.9 \pm 0.2$                                                                                           |               |                |
| <b>FTD</b>          | $46.8 \pm 0.3$ |                               |                               |                               |                               | 66.6 $\pm$ 0.3 73.8 $\pm$ 0.2 25.2 $\pm$ 0.2 43.4 $\pm$ 0.3 50.7 $\pm$ 0.3 10.4 $\pm$ 0.3 24.5 $\pm$ 0.2 |               |                |

<span id="page-5-1"></span>Table 2. The performance comparison trained with ConvNet on the  $128 \times 128$  resolution ImageNet subset. We only cite the results of MTT [\[1\]](#page-8-0), which is the only and first distillation method among the baselines to apply their method on the high-resolution ImageNet subsets.

|              | ImageNette<br>ImageWoof |  |                | ImageFruit |  |                | ImageMeow                                                                                               |  |
|--------------|-------------------------|--|----------------|------------|--|----------------|---------------------------------------------------------------------------------------------------------|--|
| ipc          |                         |  |                | 10         |  | 10             |                                                                                                         |  |
| Real dataset | $87.4 \pm 1.0$          |  | $67.0 \pm 1.3$ |            |  | $63.9 \pm 2.0$ | $66.7 \pm 1.1$                                                                                          |  |
| <b>MTT</b>   |                         |  |                |            |  |                | $47.7\pm0.9$ $63.0\pm1.3$ $28.6\pm0.8$ $35.8\pm1.8$ $26.6\pm0.8$ $40.3\pm1.3$ $30.7\pm1.6$ $40.4\pm2.2$ |  |
| <b>FTD</b>   |                         |  |                |            |  |                | 52.2±1.0 67.7±0.7 30.1±1.0 38.8±1.4 29.1±0.9 44.9±1.5 33.8±1.5 43.3±0.6                                 |  |

the resolution of  $32 \times 32$  from 10 and 100 categories, respectively. We conduct experiments on the Tiny ImageNet [\[25\]](#page-8-17) dataset with the resolution of  $64 \times 64$ . We also evaluate our proposed FTD on the ImageNet subsets with the resolution of  $128 \times 128$ . These subsets are selected 10 categories by Cazenavette et al. [\[1\]](#page-8-0) from the ImageNet dataset [\[4\]](#page-8-1).

Baselines and Models. We compare our method to a series of baselines including Dataset Condensation [\[50\]](#page-9-6) (DC), Differentiable Siamese Augmentation [\[48\]](#page-9-8) (DSA), and gradient-matching methods Distribution Matching [\[49\]](#page-9-5) (DM), Aligning Features [\[44\]](#page-9-13) (CAFE), Parameter Pruning [\[28\]](#page-8-18) (PP), and trajectory matching method [\[1\]](#page-8-0) (MTT).

Following the settings of Cazenavette et al. [\[1\]](#page-8-0), we distill and evaluate the synthetic set corresponding to CIFAR-10 and CIFAR-100 using 3-layer convolutional networks (ConvNet-3) while we move up to a depth-4 ConvNet for the images with a higher resolution ( $64 \times 64$ ) for the Tiny ImageNet dataset and a depth-5 ConvNet for the ImageNet subsets ( $128 \times 128$ ). We evaluate the cross-architecture classification performance of distilled images on four standard deep network architectures: ConvNet (3-layer) [\[12\]](#page-8-16), ResNet [\[15\]](#page-8-19), VGG [\[40\]](#page-9-15) and AlexNet [\[24\]](#page-8-20).

**Implementation Details.** We use  $\rho = 0.01$ ,  $\alpha = 1$  as the default values while implementing FTD. The same suite of differentiable augmentations [\[48\]](#page-9-8) has been implemented as

in previous studies  $[1, 49]$  $[1, 49]$  $[1, 49]$ . We use the Exponential Moving Average (EMA) [\[46\]](#page-9-16) for faster convergence in the distillation phase for the synthetic image optimization procedure. The details of the hyperparameters used in buffer phase, distillation phase of each setting (real epochs per iteration, synthetic updates per iteration, image learning rate, etc.) are reported in Appendix [A.3.3.](#page-12-1) Our experiments were run on two RTX3090 and four Tesla V100 GPUs.

#### 4.2. Results

CIFAR and Tiny ImageNet. As demonstrated in [Ta](#page-5-0)[ble 1,](#page-5-0) FTD surpasses all baselines among the CIFAR-10/100 and Tiny ImageNet dataset. In particular, our proposed FTD achieves significant improvement with  $\text{ipc} = 10, 50$  on the CIFAR-10/100 datasets. For example, our method improves MTT [\[1\]](#page-8-0) by 2.2% on the CIFAR-10 dataset with  $\text{ipc} = 50$ , and achieves 3.5% improvement on the CIFAR-100 dataset with  $\text{ipc} = 10$ . Besides, the results under "MTT+Robust learning" are obtained by using [Equation 9](#page-3-3) as the objective function of MTT during the distillation phase. "MTT+Robust learning" boosts the performance of MTT by 1.1% and 0.2% with  $\text{ipc} = 50$  on the CIFAR-10/100 datasets, respectively; However, it will incur a performance degradation with  $\text{ipc} = 1, 10$ . We have introduced "MTT+Robust learning" in [subsection 3.3.](#page-3-1)

<span id="page-6-3"></span>We visualize part of the synthetic sets for  $\text{ipc} = 1, 10$  of the CIFAR-100 and Tiny ImageNet datasets in [Figure 3.](#page-7-0) Our images look easily identifiable and highly realistic, which are akin to combinations of semantic features. We provide more additional visualizations in Appendix [A.3.5](#page-12-2) .

ImageNet Subsets. The ImageNet subsets are significantly more challenging than the CIFAR-10/100 [\[23\]](#page-8-15) and Tiny ImageNet [\[25\]](#page-8-17) datasets, because their resolutions are much higher. This characteristic of the images makes it difficult for the distillation procedure to converge. In addition, the majority of the existing dataset distillation methods may result an out-of-memory issue when distilling highresolution data. The ImageNet subsets contains 10 categories selected from ImageNet-1k [\[4\]](#page-8-1) following the setting of MTT [\[1\]](#page-8-0), which is the first distillation method which is capable of distilling higher-resolution ( $128 \times 128$ ) images. These subsets include ImageNette (assorted objects), Image-Woof (dog breeds), ImageFruits (fruits), and ImageMeow (cats) in conjunction with a depth-5 ConvNet.

As shown in [Table 2,](#page-5-1) FTD outperforms MTT in every subset with a significant improvement.<sup>[1](#page-6-0)</sup> For example, we significantly improve the performance on the ImageNette subset when  $\text{ipc} = 1, 10$  by more than 4.5%.

Cross-Architecture Generalization The ability to generalize well across different architectures of the synthetic dataset is crucial in the real application of dataset distillation. However, the existing dataset distillation methods suffer from a performance degradation when the synthetic dataset is trained by the network with a different architecture than the one used in distillation [\[1,](#page-8-0) [44\]](#page-9-13).

Here, we study the cross-architecture performance of FTD, compare it with three baselines, and report the results in [Table 3.](#page-6-1) We evaluate FTD on CIFAR-10 with  $\text{ipc} = 50$ . We use three more different neural network architectures for evaluation: ResNet [\[15\]](#page-8-19), VGG [\[40\]](#page-9-15) and AlexNet [\[24\]](#page-8-20). The synthetic dataset is distilled with ConvNet (3-layer) [\[12\]](#page-8-16). The results show that synthetic images learned with FTD perform and generalize well to different convolutional networks. The performances of synthetic data on architectures distinct from the one used to distill should be utilized to validate that the distillation method is able to identify essential features for learning, other than merely the matching of parameters.

### <span id="page-6-4"></span>4.3. Ablation and Parameter Studies

Exploring the Flat Trajectory Many studies [\[6,](#page-8-21) [17,](#page-8-22) [18,](#page-8-23) [21,](#page-8-24) [29\]](#page-8-25) have revealed that DNNs with flat minima can generalize better than ones with sharp minima. Although FTD encourages dataset distillation to seek a flat trajectory which terminates a flat minimum, the progress along a flat teacher trajectory, which minimizes the accumulated trajectory error, contributes primarily to the performance gain

<span id="page-6-1"></span>Table 3. Cross-Architecture Results trained with ConvNet on CIFAR-10 with  $\text{ipc} = 50$ . We reproduce the results of MTT, and cite the results of DC and CAFE reported in Wang et al. [\[44\]](#page-9-13).

|             | <b>Evaluation Model</b> |              |              |                |  |  |  |
|-------------|-------------------------|--------------|--------------|----------------|--|--|--|
| Method      | ConvNet                 | ResNet18     | VGG11        | AlexNet        |  |  |  |
| DC          | $53.9 + 0.5$            | $20.8 + 1.0$ | $38.8 + 1.1$ | $28.7 \pm 0.7$ |  |  |  |
| <b>CAFE</b> | $55.5 + 0.4$            | $25.3 + 0.9$ | $40.5 + 0.8$ | $34.0 \pm 0.6$ |  |  |  |
| <b>MTT</b>  | $71.6 + 0.2$            | $61.9 + 0.7$ | $55.4 + 0.8$ | $48.2 + 1.0$   |  |  |  |
| <b>FTD</b>  | $73.8 \pm 0.2$          | $65.7 + 0.3$ | $58.4 + 1.6$ | $53.8 + 0.9$   |  |  |  |

of FTD. To verify this, we design experiments to demonstrate that the attainment of a flat minimum does not enhance the accuracy of the synthetic dataset. We implement Sharpness-Aware Minimization (SAM) [\[11\]](#page-8-14) to bias the training over the synthetic dataset obtained from MTT to converge at a flat minimum. We term this as "MTT + Flat Minimum" and compare the results to FTD. A set values of  $\rho \in \{0.005, 0.01, 0.03, 0.05, 0.1\}$  is tested for a thorough comparison. We report the comparison in [Figure 4.](#page-7-1) It can be seen that a flatter minimum does not help the synthetic dataset to generalize well. We provide more theoretical explanation about it in Appendix [A.2.](#page-10-1) Therefore, FTD's chief advantage lies in the suppression of the accumulated trajectory error to improve dataset distillation.

Effect of EMA. We implement the Exponential Moving Average (EMA) with  $\beta = 0.999$  in the distillation phase of FTD for enhanced convergence. While EMA contributes to the improvement, it is not the primary driver. The results of our proposed approach with and without EMA are presented in [Table 4.](#page-6-2) We observe that EMA enhances the evaluation accuracies. However, our proposed regularization in the buffer phase for a flatter teacher trajectory contributes most significantly to the performance improvement.

We have also conducted a parameter study on the coefficient  $\rho$  and observed that  $\rho = 0.01$  is the optimal value for each dataset considered. See Appendix [A.3.1.](#page-11-1)

|                |                | CIFAR-100      | Tiny ImageNet |                |  |
|----------------|----------------|----------------|---------------|----------------|--|
| ipc            | 10             | 50             |               | 10             |  |
| MTT            | $39.7 + 0.4$   | $47.7 + 0.2$   | $8.8 + 0.3$   | $23.2 + 0.2$   |  |
| FTD (w.o. EMA) | $43.4 + 0.3$   | $49.8 + 0.3$   | $9.8 + 0.2$   | $24.1 + 0.3$   |  |
| FTD.           | $43.2 \pm 0.3$ | $50.7 \pm 0.3$ | $10.0 + 0.2$  | $24.5 \pm 0.2$ |  |

<span id="page-6-2"></span>Table 4. Ablation study of FTD. FTD without EMA still significantly surpasses MTT.

#### 4.4. Neural Architecture Search (NAS)

To better demonstrate the substantial practical benefits of our proposed method FTD, we evaluate our method in *neural architecture search* (NAS). NAS is one of the important down-stream task of dataset distillation. It aims to find the best network architecture for a given dataset among a variety of architecture candidates. Dataset distillation uses

<span id="page-6-0"></span><sup>&</sup>lt;sup>1</sup>The results of ImageNet subsets are cited exactly from  $[1]$ .

<span id="page-7-3"></span><span id="page-7-0"></span>Image /page/7/Picture/0 description: The image displays a grid of 24 images on the left and 12 images on the right. The left grid is arranged in four rows and six columns, featuring various objects such as insects, bicycles, bottles, and bowls. The right grid is arranged in two rows and six columns, showing close-ups of what appear to be natural textures or patterns, possibly flowers or food items.

Figure 3. Visualization example of synthetic images distilled from  $32 \times 32$  CIFAR-100 (ipc = 10), and  $64 \times 64$  Tiny ImageNet (ipc = 1).

<span id="page-7-1"></span>Image /page/7/Figure/2 description: The image is a line graph showing the accuracy of three different methods: MTT, MTT+Flat Minimum, and FTD, plotted against the parameter rho (ρ). The y-axis represents accuracy in percentage, ranging from 39% to 44%. The x-axis represents rho (ρ), with values ranging from 0.005 to 0.1. The MTT method is represented by a dashed blue line, showing an accuracy of approximately 39.7% across all rho values. The MTT+Flat Minimum method is represented by a solid yellow line with triangle markers, showing an increasing accuracy from approximately 39.6% at rho=0.005 to about 39.9% at rho=0.01, and then remaining relatively flat around 39.9% for higher rho values. The FTD method is represented by a dashed orange line, showing a constant accuracy of approximately 43.4% across all rho values. The graph indicates that FTD consistently achieves the highest accuracy, followed by MTT+Flat Minimum, and then MTT.

Figure 4. We apply SAM with different values of  $\rho$  on the synthetic dataset obtained from MTT to train the networks, which is termed as "MTT + Flat Minimum". "MTT" and "FTD" represent the standard results of MTT and FTD on CIFAR-100 with ipc=10, respectively. A "flat" minimum does not help the synthetic dataset to generalize better.

the synthetic dataset as the proxy to efficiently search for the optimal architecture, which reduces the computational cost in a linear fashion. We show that FTD can synthesize a better and practical proxy dataset, which has a stronger correlation with the real dataset.

Following [\[50\]](#page-9-6), we implement NAS on the CIFAR-10 dataset on a search space of 720 ConvNets that differ in network depth, width, activation, normalization, and pooling layers. More details can be found in Appendix [A.3.4.](#page-12-3) We train these different architecture models on the MTT synthetic dataset, our synthetic dataset, and the real CIFAR-10 dataset for 200 epochs. Additionally, the accuracy on the test set of real data determines the overall architecture. The Spearman's rank correlation between the searched rankings of the synthetic dataset and the real dataset training is used as the evaluation metric. Since the top-ranking architectures are more essential, only the rankings of the top 5, 10 and 20 architectures will be used for evaluation, respectively.

Our results are displayed in [Table 5.](#page-7-2) FTD achieves much higher rank correlation than MTT in every top- $k$  ranking. In particular, FTD achieves a 0.87 correlation in the top-5 ranking, which is very close to the value of 1.0 in real dataset, while MTT's correlation is  $0.41$ . FTD is thus able to obtain a reliable synthetic dataset, which generalizes well for NAS.

# 5. Conclusion and Future Work

We studied a flat trajectory distillation technique, that is able to effectively mitigate the adverse effect of the accumu-

<span id="page-7-2"></span>Table 5. We implement NAS on CIFAR-10 with a search over 720 ConvNets. We present the Spearman's rank correlation (1.00 is the best) of the top 5, 10, and 20 architectures between the rankings searched by the synthetic and real datasets. The Time column records the entire time to search for each dataset.

|      | Top 5 | Top 10 | Top 20 | Time(min) | Images No. |
|------|-------|--------|--------|-----------|------------|
| Real | 1.00  | 1.00   | 1.00   | 6,804     | 50,000     |
| MTT  | 0.41  | 0.36   | -0.04  | 360       | 500        |
| FTD  | 0.87  | 0.68   | 0.54   | 360       | 500        |

lated trajectory error leading to significant performance gain. The cross-architecture and NAS experiments also confirmed FTD's ability to generalize well across different architectures and downstream tasks of dataset distillation.

We note that the performance of the teacher trajectories in the existing gradient-matching methods doesn't represent the state-of-the-art. This is because the optimization of the teacher trajectories has to be simplified to improve the convergence of distillation. The accumulation of the trajectory error, for instance, is a possible reason to limit the total number of training epochs of the teacher trajectories, that calls for further research.

# Acknowledgements

This work is support by Joey Tianyi Zhou's A\*STAR SERC Central Research Fund (Use-inspired Basic Research) and the Singapore Government's Research, Innovation and Enterprise 2020 Plan (Advanced Manufacturing and Engineering domain) under Grant A18A1b0045.

This work is also supported by 1) National Natural Science Foundation of China (Grant No. 62271432); 2) Guangdong Provincial Key Laboratory of Big Data Computing, The Chinese University of Hong Kong, Shenzhen (Grant No. B10120210117-KP02); 3) Human-Robot Collaborative AI for Advanced Manufacturing and Engineering (Grant No. A18A2b0046), Agency of Science, Technology and Research (A\*STAR), Singapore; 4) Advanced Research and Technology Innovation Centre (ARTIC), the National University of Singapore (project number: A-0005947-21-00); and 5) the Singapore Ministry of Education (Tier 2 grant: A-8000423-00-00).

# References

- <span id="page-8-0"></span>[1] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-6) [4,](#page-3-4) [6,](#page-5-2) [7,](#page-6-3) [13](#page-12-4)
- <span id="page-8-4"></span>[2] Jang Hyun Cho and Bharath Hariharan. On the efficacy of knowledge distillation. In *Proceedings of the IEEE/CVF international conference on computer vision*, pages 4794– 4802, 2019. [1](#page-0-1)
- <span id="page-8-8"></span>[3] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dcbench: Dataset condensation benchmark. *arXiv preprint arXiv:2207.09639*, 2022. [1](#page-0-1)
- <span id="page-8-1"></span>[4] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [1,](#page-0-1) [6,](#page-5-2) [7](#page-6-3)
- <span id="page-8-2"></span>[5] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint arXiv:1810.04805*, 2018. [1](#page-0-1)
- <span id="page-8-21"></span>[6] Laurent Dinh, Razvan Pascanu, Samy Bengio, and Yoshua Bengio. Sharp minima can generalize for deep nets. In *International Conference on Machine Learning*, pages 1019– 1028. PMLR, 2017. [7](#page-6-3)
- <span id="page-8-27"></span>[7] Laurent Dinh, Razvan Pascanu, Samy Bengio, and Yoshua Bengio. Sharp minima can generalize for deep nets. In *International Conference on Machine Learning*, pages 1019– 1028. PMLR, 2017. [13](#page-12-4)
- <span id="page-8-11"></span>[8] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? *arXiv preprint arXiv:2206.00240*, 2022. [2](#page-1-1)
- <span id="page-8-3"></span>[9] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020. [1](#page-0-1)
- <span id="page-8-26"></span>[10] Jiawei Du, Hanshu Yan, Jiashi Feng, Joey Tianyi Zhou, Liangli Zhen, Rick Siow Mong Goh, and Vincent YF Tan. Efficient sharpness-aware minimization for improved training of neural networks. *arXiv preprint arXiv:2110.03141*, 2021. [13](#page-12-4)
- <span id="page-8-14"></span>[11] Pierre Foret, Ariel Kleiner, Hossein Mobahi, and Behnam Neyshabur. Sharpness-aware minimization for efficiently improving generalization. In *International Conference on Learning Representations*, 2020. [5,](#page-4-2) [7,](#page-6-3) [11,](#page-10-2) [12,](#page-11-2) [13](#page-12-4)
- <span id="page-8-16"></span>[12] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4367–4375, 2018. [6,](#page-5-2) [7,](#page-6-3) [11](#page-10-2)
- <span id="page-8-12"></span>[13] Jack Goetz and Ambuj Tewari. Federated learning via synthetic data. *arXiv preprint arXiv:2008.04489*, 2020. [2](#page-1-1)
- <span id="page-8-5"></span>[14] Jianping Gou, Baosheng Yu, Stephen J Maybank, and Dacheng Tao. Knowledge distillation: A survey. *International Journal of Computer Vision*, 129(6):1789–1819, 2021. [1](#page-0-1)

- <span id="page-8-19"></span>[15] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [6,](#page-5-2) [7](#page-6-3)
- <span id="page-8-6"></span>[16] Geoffrey Hinton, Oriol Vinyals, Jeff Dean, et al. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2(7), 2015. [1](#page-0-1)
- <span id="page-8-22"></span>[17] Sepp Hochreiter and Jürgen Schmidhuber. Simplifying neural nets by discovering flat minima. In *Proceedings of the 8th International Conference on Neural Information Processing Systems*, pages 529–536, 1995. [7,](#page-6-3) [13](#page-12-4)
- <span id="page-8-23"></span>[18] Yiding Jiang, Behnam Neyshabur, Hossein Mobahi, Dilip Krishnan, and Samy Bengio. Fantastic generalization measures and where to find them. In *International Conference on Learning Representations*, 2019. [7,](#page-6-3) [13](#page-12-4)
- <span id="page-8-10"></span>[19] Haifeng Jin, Qingquan Song, and Xia Hu. Auto-keras: An efficient neural architecture search system. In *Proceedings of the 25th ACM SIGKDD international conference on knowledge discovery & data mining*, pages 1946–1956, 2019. [2](#page-1-1)
- <span id="page-8-28"></span>[20] Nitish Shirish Keskar, Dheevatsa Mudigere, Jorge Nocedal, Mikhail Smelyanskiy, and Ping Tak Peter Tang. On largebatch training for deep learning: Generalization gap and sharp minima. *arXiv preprint arXiv:1609.04836*, 2016. [13](#page-12-4)
- <span id="page-8-24"></span>[21] Nitish Shirish Keskar, Jorge Nocedal, Ping Tak Peter Tang, Dheevatsa Mudigere, and Mikhail Smelyanskiy. On largebatch training for deep learning: Generalization gap and sharp minima. In *International Conference on Learning Representations*, 2017. [7](#page-6-3)
- <span id="page-8-7"></span>[22] Yoon Kim and Alexander M Rush. Sequence-level knowledge distillation. *arXiv preprint arXiv:1606.07947*, 2016. [1](#page-0-1)
- <span id="page-8-15"></span>[23] Alex Krizhevsky, Vinod Nair, and Geoffrey Hinton. CIFAR-10 and CIFAR-100 datasets. *URl: https://www. cs. toronto. edu/kriz/cifar. html*, 6(1):1, 2009. [5,](#page-4-2) [6,](#page-5-2) [7](#page-6-3)
- <span id="page-8-20"></span>[24] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Advances in neural information processing systems*, 25, 2012. [6,](#page-5-2) [7](#page-6-3)
- <span id="page-8-17"></span>[25] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [6,](#page-5-2) [7](#page-6-3)
- <span id="page-8-9"></span>[26] Shiye Lei and Dacheng Tao. A comprehensive survey to dataset distillation. *arXiv preprint arXiv:2301.05603*, 2023. [1](#page-0-1)
- <span id="page-8-13"></span>[27] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Soft-label anonymous gastric x-ray image distillation. In *2020 IEEE International Conference on Image Processing (ICIP)*, pages 305–309. IEEE, 2020. [2](#page-1-1)
- <span id="page-8-18"></span>[28] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Dataset distillation using parameter pruning. *arXiv preprint arXiv:2209.14609*, 2022. [6](#page-5-2)
- <span id="page-8-25"></span>[29] Hao Li, Zheng Xu, Gavin Taylor, Christoph Studer, and Tom Goldstein. Visualizing the loss landscape of neural nets. *Proceedings of the 32nd International Conference on Neural Information Processing Systems*, 31, 2018. [7](#page-6-3)
- <span id="page-8-29"></span>[30] Tengyuan Liang, Tomaso Poggio, Alexander Rakhlin, and James Stokes. Fisher–Rao metric, geometry, and complexity of neural networks. In *The 22nd International Conference on Artificial Intelligence and Statistics*, pages 888–896. PMLR, 2019. [13](#page-12-4)

- <span id="page-9-19"></span>[31] Chen Liu, Mathieu Salzmann, Tao Lin, Ryota Tomioka, and Sabine Süsstrunk. On the loss landscape of adversarial training: Identifying challenges and how to overcome them. *Proceedings of the 34th International Conference on Neural Information Processing Systems*, 33:21476–21487, 2020. [13](#page-12-4)
- <span id="page-9-18"></span>[32] Dougal Maclaurin, David Duvenaud, and Ryan Adams. Gradient-based hyperparameter optimization through reversible learning. In *International conference on machine learning*, pages 2113–2122. PMLR, 2015. [13](#page-12-4)
- <span id="page-9-17"></span>[33] David A McAllester. Pac-bayesian model averaging. In *Proceedings of the twelfth annual conference on Computational learning theory*, pages 164–170, 1999. [11](#page-10-2)
- <span id="page-9-12"></span>[34] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [2,](#page-1-1) [13](#page-12-4)
- <span id="page-9-0"></span>[35] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [1](#page-0-1)
- <span id="page-9-1"></span>[36] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [1,](#page-0-1) [13](#page-12-4)
- <span id="page-9-9"></span>[37] Hieu Pham, Melody Guan, Barret Zoph, Quoc Le, and Jeff Dean. Efficient neural architecture search via parameters sharing. In *International conference on machine learning*, pages 4095–4104. PMLR, 2018. [2](#page-1-1)
- <span id="page-9-10"></span>[38] Pengzhen Ren, Yun Xiao, Xiaojun Chang, Po-Yao Huang, Zhihui Li, Xiaojiang Chen, and Xin Wang. A comprehensive survey of neural architecture search: Challenges and solutions. *ACM Computing Surveys (CSUR)*, 54(4):1–34, 2021. [2](#page-1-1)
- <span id="page-9-7"></span>[39] Andrea Rosasco, Antonio Carta, Andrea Cossu, Vincenzo Lomonaco, and Davide Bacciu. Distilled replay: Overcoming forgetting through synthetic samples. *arXiv preprint arXiv:2103.15851*, 2021. [2](#page-1-1)
- <span id="page-9-15"></span>[40] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [6,](#page-5-2) [7](#page-6-3)
- <span id="page-9-11"></span>[41] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *International Conference on Machine Learning*, pages 9206–9216. PMLR, 2020. [2](#page-1-1)
- <span id="page-9-2"></span>[42] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pages 1–8. IEEE, 2021. [1](#page-0-1)
- [43] Paul Vicol, Jonathan P Lorraine, Fabian Pedregosa, David Duvenaud, and Roger B Grosse. On implicit bias in overparameterized bilevel optimization. In *International Conference on Machine Learning*, pages 22234–22259. PMLR, 2022. [1](#page-0-1)
- <span id="page-9-13"></span>[44] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196– 12205, 2022. [1,](#page-0-1) [2,](#page-1-1) [6,](#page-5-2) [7](#page-6-3)

- <span id="page-9-3"></span>[45] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-6) [13](#page-12-4)
- <span id="page-9-16"></span>[46] Ross Wightman. Pytorch image models. https: [/ / github . com / rwightman / pytorch - image](https://github.com/rwightman/pytorch-image-models)  [models](https://github.com/rwightman/pytorch-image-models), 2019. [6](#page-5-2)
- <span id="page-9-4"></span>[47] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023. [1](#page-0-1)
- <span id="page-9-8"></span>[48] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [2,](#page-1-1) [6,](#page-5-2) [13](#page-12-4)
- <span id="page-9-5"></span>[49] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021. [1,](#page-0-1) [2,](#page-1-1) [6](#page-5-2)
- <span id="page-9-6"></span>[50] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-6) [6,](#page-5-2) [8,](#page-7-3) [13](#page-12-4)
- <span id="page-9-14"></span>[51] Juntang Zhuang, Boqing Gong, Liangzhe Yuan, Yin Cui, Hartwig Adam, Nicha Dvornek, Sekhar Tatikonda, James Duncan, and Ting Liu. Surrogate gap minimization improves sharpness-aware training. *arXiv preprint arXiv:2203.08065*, 2022. [5,](#page-4-2) [13](#page-12-4)

# <span id="page-10-2"></span>Author Contributions

In this paper, the authors made the following contributions:

- Jiawei Du developed the theoretical framework, and proposed FTD. He also designed the experiments, analyzed the results, plotted the figures, and wrote the majority of the manuscript.
- Yidi Jiang implemented FTD and conducted the experiments. She recorded the experimental logs and analyzed the results. She also wrote the experimental and related works sections.
- Vincent Y. F. Tan guided the formulation of FTD. He also helped develop the theoretical framework and revised the manuscript.
- Joey Tianyi Zhou and Haizhou Li supervised the project and provided critical feedback on the research.

### A. More Discussions and Experiments

<span id="page-10-0"></span>

##### A.1. Exploring the Accumulated Trajectory Error

We design experiments on the CIFAR-100 dataset with  $\text{inc} = 10$  to verify the existence and observe the adverse effect of the accumulation of the trajectory error (as defined in [Equation 8\)](#page-3-2) of MTT.

We present the loss difference  $L_{\mathcal{T}_\text{Test}}(f_\theta) - L_{\mathcal{T}_\text{Test}}(f_{\theta^*}),$ which quantifies how well the student trajectory matches the teacher trajectory along the epochs during evaluation phase, in [Figure 1.](#page-0-0) We also present the loss difference during the distillation phase that serves as a baseline. It can be seen that the loss difference of MTT (blue line) in the evaluation phase accumulates as the evaluation progresses, and is much higher than the one in the distillation phase (cyan line). These results demonstrate the existence the accumulation of the trajectory error  $\epsilon_t$ . Moreover, the loss difference of FTD (purple line) is shown to be much lower than that of MTT (blue line), which suggests that our proposed FTD reduces the accumulated trajectory error  $\epsilon_t$  effectively.

<span id="page-10-3"></span>Table 6. Ablation results of the initialization discrepancy. The start epoch indicates that the  $n<sup>th</sup>$  epoch's set of weights from the teacher trajectories is used to initialize the network. The epochs to train indicates the remaining epochs to train the initialized network (1  $epoch = 20$  synthetic steps).

| <b>Start Epoch</b> | <b>Epochs to train</b> | <b>MTT Accuracy</b> | <b>Our Accuracy</b> |
|--------------------|------------------------|---------------------|---------------------|
| 0                  | 50                     | 35.4                | 37.7                |
| 10                 | 40                     | 37.0                | 39.5                |
| 20                 | 30                     | 38.6                | 41.6                |
| 30                 | 20                     | 40.2                | 43.5                |
| 40                 | 10                     | 42.1                | 44.4                |
| 45                 | 5                      | 42.3                | 46.2                |

We also design experiments to show the existence of the initialization error  $\mathcal{I}_t$  in [Equation 7.](#page-3-5) Recall that this is the dominant factor leading to the accumulation of the trajectory error as shown in [Equation 8.](#page-3-2) We compare the accuracies of several 3-layer ConvNets [\[12\]](#page-8-16) trained using the same synthetic dataset  $S$  but initialized with different weights. These networks are initialized by the sets of weights in epochs  $0, 5, 10, \ldots, 40, 45$  of the teacher trajectories, and are trained until the  $50<sup>th</sup>$  epoch. Specifically, the network initialized by the sets of weights in epoch 0 serves as the baseline. These weights are equivalent to being initialized from the student trajectories in epochs  $5, 10, \ldots, 40, 45$ , respectively. Note that training over the 50 epochs of the teacher trajectories is equal to doing the same over 100 iterations of the student trajectories (1 epoch  $= 20$  synthetic steps), which is much fewer than the 1000 iterations trained in the evaluation phase. Thus the accuracy is degraded as compared to [Table 1.](#page-5-0) Following the above settings, we evaluate MTT and FTD and report the results in [Table 6.](#page-10-3) It can be seen that the networks initialized by the sets of weights from the teacher trajectories always outperform the baseline. In fact, the fewer epochs used to train, the better the accuracy. The results clearly show the adverse effect of the initialization discrepancy. A more precise initialization (closer to the initialization used in distillation) will have a more significant impact on the final performance. However, FTD is as expected to suppress the initialization error  $\mathcal{I}_t$  so that it eventually surpasses the performance of MTT.

<span id="page-10-1"></span>

#### A.2. Exploring the Flat Trajectory

### A.3. Implementation Details

We denote D as the natural distribution,  $L_{\mathcal{D}}(f_{\theta})$  is equivalent to the expected loss over test set. Each sample in the real training dataset  $T$  is drawn i.i.d. from  $D$ . For simplicity, we consider Gaussian priors and likelihoods, in which case the posterior is also Gaussian. Hence, we assume that over the parameter space,  $\mathscr{P} = \mathcal{N}(\mu_P, \sigma_P^2 \mathbf{I})$  is the prior distribution and  $W = \mathcal{N}(\mu_W, \sigma_W^2 \mathbf{I})$  is the posterior distribution trained on  $\mathcal T$ , where  $\mu_P, \mu_W \in \mathbb R^k$  and  $\mathbf I$  is the  $k \times k$  identity matrix. We assume that the matching error  $\delta \sim \mathcal{N}(\mathbf{0}, \sigma_{\epsilon}^2 \mathbf{I}).$ Pierre et al. [\[11\]](#page-8-14) states a generalization bound based on the sharpness to theoretically justify the benefit of flat minima derived from the PAC-Bayesian generalization bound [\[33\]](#page-9-17) as follows. For  $n = |\mathcal{T}|$  and with probability at least  $1-\delta$ , over the choice of the real training set  $\mathcal T$ , the following inequality <span id="page-11-2"></span>holds

$$
\mathbb{E}_{\theta \sim \mathcal{W}}[L_{\mathcal{D}}(f_{\theta})] \leq \mathbb{E}_{\theta \sim \mathcal{W}}[L_{\mathcal{T}}(f_{\theta})] + \Delta L(\mathscr{P}), \quad (14)
$$

where

$$
\Delta L(\mathscr{P}) = \sqrt{\frac{\text{KL}(\mathcal{W}||\mathscr{P}) + \log \frac{n}{\delta}}{2(n-1)}}.
$$

In this bound  $\Delta L$  quantifies the generalization error, i.e., the closeness between the test and training losses. As we stated in [subsection 3.3,](#page-3-1) the gradient-matching dataset distillation is equivalent to mapping a initialization distribution  $P_{\theta_0}$  into the posterior distribution W. However, due to the existence of the matching error, the posterior distribution  $W$ trained on the synthetic set S is more dispersed than  $W$ , i.e.,  $\tilde{W} = \mathcal{N}(\mu_W, \sigma_W^2 \mathbf{I} + \sigma_\epsilon^2 \mathbf{I})$  for some  $\sigma_\epsilon^2 \ge 0$ . Since the KL divergence can be written as [\[11\]](#page-8-14),

$$
KL(\mathcal{W} \| \mathscr{P}) \\ = \frac{1}{2} \bigg[ \frac{k \sigma_W^2 + \|\boldsymbol{\mu}_P - \boldsymbol{\mu}_W\|_2^2}{\sigma_P^2} - k + k \log \left( \frac{\sigma_P^2}{\sigma_W^2} \right) \bigg] \\ = \frac{k}{2} \bigg[ \frac{\sigma_W^2}{\sigma_P^2} - \log \frac{\sigma_W^2}{\sigma_P^2} \bigg] + \frac{1}{2} \bigg[ \frac{\|\boldsymbol{\mu}_P - \boldsymbol{\mu}_W\|_2^2}{\sigma_P^2} - k \bigg],
$$

where  $k$  is the number of parameters. Therefore, we have

$$
KL(\tilde{W} \| \mathscr{P}) - KL(W \| \mathscr{P})
$$

$$
= \frac{k}{2} \left[ \left( \frac{\sigma_W^2 + \sigma_{\epsilon}^2}{\sigma_P^2} - \log \frac{\sigma_W^2 + \sigma_{\epsilon}^2}{\sigma_P^2} \right) - \left( \frac{\sigma_W^2}{\sigma_P^2} - \log \frac{\sigma_W^2}{\sigma_P^2} \right) \right]
$$

$$
\geq 0.
$$

The final inequality holds as  $\sigma_{\epsilon}^2 \ge 0$  and  $\sigma_W^2 \ge \sigma_P^2$ . Consequently, the generalization error  $\Delta L(\mathcal{W})$  over the synthetic dataset S will be greater than  $\Delta L(\mathcal{W})$  over the real dataset  $\mathcal T$ . The experiments in [subsection 4.3](#page-6-4) verify that the flat minima of the synthetic dataset does not benefit generalization ability as the generalization bound in [Equation 14](#page-11-3) is loose.

##### A.3.1 Parameter Study

<span id="page-11-1"></span>

###### A.3.2 Parameter Study

The coefficient  $\rho$  in [Equation 12](#page-4-3) controls the amplitude of the perturbation  $\epsilon$ , which affects the flatness of the obtained teacher trajectories [\[11\]](#page-8-14). We study the effect of  $\rho$  by using grid searches from the set  $\{0.005, 0.01, 0.03, 0.05, 0.1\}$ during the buffer phase. We report the accuracies of the evaluated synthetic dataset in [Figure 5.](#page-11-4) We observe that  $\rho = 0.01$  achieves the best improvement, which is different from the suggested value  $\rho = 0.05$  [\[11\]](#page-8-14). Lastly, it is not sensitive to choose the value of  $\rho$  as FTD outperforms MTT with every evaluated value of  $\rho$ .

<span id="page-11-4"></span><span id="page-11-3"></span>Image /page/11/Figure/12 description: The figure is a line graph showing the accuracy (%) on the y-axis against rho on the x-axis. The x-axis ranges from 0.005 to 0.1, with tick marks at 0.005, 0.01, 0.03, 0.05, and 0.1. The y-axis ranges from 39 to 44, with tick marks at 39, 40, 41, 42, 43, and 44. There are two lines plotted: a dashed blue line labeled "MTT" and a solid orange line with circular markers labeled "FTD". The MTT line is a horizontal line at approximately 39.75%. The FTD line starts at approximately 42.75% at rho=0.005, rises to approximately 43.1% at rho=0.01, stays around 43% at rho=0.03 and 0.05, and then drops to approximately 41.75% at rho=0.1.

Figure 5. Parameter study of  $\rho$  on CIFAR-100 (ipc=10). We set the x-axis to be in log scale for better illustration. Blue dashed line is the result of MTT, which serves as the baseline.

<span id="page-11-0"></span>

##### A.3.3 Optimizing of the Flat Trajectory

As introduced in [subsection 3.3,](#page-3-1) FTD only regularizes the training in the buffer phase as in [Equation 13](#page-4-4) to obtain a flat teacher trajectory. We provide the pseudocode for reproducing our results in Algorithm [1.](#page-12-5) The optimization of the flat trajectory is solving a minimax problem. We follow Pierre et al. [\[11\]](#page-8-14) to approximate the solution  $\hat{\epsilon}$  of the maximization in [Equation 12](#page-4-3) as follows

$$
\hat{\epsilon} = \underset{\epsilon \in \Psi}{\arg \max} \left[ L_{\mathcal{T}}(f_{\theta + \epsilon}) - L_{\mathcal{T}}(f_{\theta}) \right]
$$
$$
= \rho \frac{\nabla_{\theta} L_{\mathcal{T}}(f_{\theta})}{\|\nabla_{\theta} L_{\mathcal{T}}(f_{\theta})\|_{2}}, \tag{15}
$$

where  $\Psi = {\epsilon : ||\epsilon||_2 \leq \rho}$  and  $\rho > 0$  is a given constant that determines the permissible norm of  $\epsilon$ . We denote  $g_L$  =  $\nabla_{\theta} L_{\mathcal{T}}(f_{\theta_t})$ , which is the gradient to optimize the vanilla loss function  $L_{\mathcal{T}}(f_{\theta})$ . Hence, from [Equation 15,](#page-11-5) we have that

<span id="page-11-6"></span><span id="page-11-5"></span>
$$
\hat{\epsilon} = \rho \frac{g_L}{\|g_L\|_2}.
$$

Suppose that  $\theta^{adv} = \theta + \hat{\epsilon}$ , we can rewrite [Equation 13](#page-4-4) as follows,

$$
\theta^* = \underset{\theta}{\arg\min} \{ L_{\mathcal{T}}(f_{\theta}) + \alpha S(\theta) \}
$$
  
= 
$$
\underset{\theta}{\arg\min} \{ L_{\mathcal{T}}(f_{\theta}) + \alpha [L_{\mathcal{T}}(f_{\theta^{\text{adv}}}) - L_{\mathcal{T}}(f_{\theta})] \}
$$
  
= 
$$
\underset{\theta}{\arg\min} \{ \alpha L_{\mathcal{T}}(f_{\theta^{\text{adv}}}) + (1 - \alpha) [L_{\mathcal{T}}(f_{\theta})] \}.
$$
 (16)

We denote  $g_{S+L} = \nabla_{\theta} L_{\mathcal{T}}(f_{\theta^{\text{adv}}})$ , which is the gradient to optimize  $L_{\mathcal{T}}(f_{\theta^{\text{adv}}})$ . Hence, from [Equation 16,](#page-11-6) the gradient to optimize  $\theta^*$  is  $g = \alpha \cdot g_{S+L} + (1 - \alpha) \cdot g_L$  as illustrated in Line [6](#page-12-6) of Algorithm [1.](#page-12-5) The parameter  $\alpha$  is found using a grid search, as described next.

<span id="page-12-5"></span><span id="page-12-4"></span>Algorithm 1 Training with FTD in buffer phase.

- **Input:** Real set  $\mathcal{T}$ ; A network f with weights  $\theta$ ; Learning rate  $\eta$ ; Epochs E; Iterations T per epoch; FTD hyperparameter  $α$ ,  $ρ$ .
- 1: for  $e = 1$  to E do
- 2: for  $t = 1$  to T, Sample a mini-batch  $\mathcal{B} \subset \mathcal{T}$  do
- 3: Compute gradients  $g_L = \nabla_{\theta} L_{\mathcal{B}}(f_{\theta_t})$
- 4:  $\theta_t^{\text{adv}} = \theta_t + \rho \cdot \frac{g_L}{\|g_L\|_2}$
- 5: Compute gradients  $g_{S+L} = \nabla_{\theta} L_{\mathcal{B}}(f_{\theta_t^{\text{adv}}})$
- <span id="page-12-6"></span>6: Compute  $g = \alpha \cdot g_{S+L} + (1 - \alpha) \cdot g_L$
- 7: Update weights  $\theta_{t+1} \leftarrow \theta_t \eta g$
- 8: Record weights  $\theta_T$   $\Rightarrow$  Record the trajectory at the end of each epoch

Output: A flat teacher trajectory.

<span id="page-12-1"></span>

##### A.3.4 Hyperparameter Details

The hyperparameters  $\alpha$  and  $\rho$  of FTD are obtained via grid searches in a validation set within the CIFAR-10 dataset. The hyperparameter  $\rho$  is searched within the set  ${0.005, 0.01, 0.03, 0.05, 0.1}$ . The hyperparameter  $\alpha$  is searched within the set  $\{0.1, 0.3, 0.5, 1.0, 3.0\}$ . For the rest of the hyperparamters, we report them in [Table 7.](#page-13-0)

<span id="page-12-3"></span>

##### A.3.5 Neural Architecture Search.

Following the search space construction of 720 ConvNets in [\[50\]](#page-9-6), we vary the different hyperparameters including the width  $W \in \{32, 64, 128, 256\}$ , depth  $D \in \{1, 2, 3, 4\}$ , normalization  $N \in \{None, Batch-$ Norm, LayerNorm, InstanceNorm, GroupNorm}, activation  $A \in \{Sigmoid, ReLU, LeakyReLU\}$ , pooling  $P \in$ {None, MaxPooling, AvgPooling}. Every candidate ConvNet is trained with the proxy dataset, and then evaluated on the whole test dataset. These candidate ConvNets are then ranked by their test performances. The architectures with the top 5, 10 and 20 test accuracies are selected and the Spearman's rank correlation coefficients between the searched rankings of the synthetic dataset and the real dataset are computed after training. We train each ConvNet for a total of 3 times to obtain averaged validation and test accuracies.

<span id="page-12-2"></span>

##### A.3.5 Visualizations

We provide more visualizations of the synthetic datasets for  $\text{ipc} = 1$  from the different resolution datasets:  $32 \times 32$ CIFAR-10 dataset in [Figure 6,](#page-13-1)  $64\times64$  Tiny ImageNet dataset in [Figure 7,](#page-14-0)  $128 \times 128$  ImageNette subset in [Figure 8.](#page-14-1) In addition, parts of the visualizations of synthetic images from the CIFAR-100 dataset are showed in [Figure 9.](#page-15-0)

<span id="page-12-0"></span>

## B. More Related Work

Dataset Distillation. Dataset distillation presented by [\[45\]](#page-9-3) aims to obtain a new, synthetic dataset that is much reduced in size which also performs almost as well as the original dataset. Similar to [\[45\]](#page-9-3), several approaches consider end-to-end training [\[34,](#page-9-12) [36\]](#page-9-1), however they frequently necessitate enormous computation and memory resources and suffer from inexact relaxations [\[34,](#page-9-12) [36\]](#page-9-1) or training instabilities caused by unrolling numerous iterations [\[32,](#page-9-18) [45\]](#page-9-3). Other strategies [\[48,](#page-9-8) [50\]](#page-9-6) lessen the difficulty of optimization by emphasizing short-term behavior, requiring a single training step on the distilled data to match that on the real data. Nevertheless, errors may accrue during evaluation, when the distilled data is used in multiple steps.

To address the difficulties of error accumulation in single training step matching algorithms [\[48,](#page-9-8) [50\]](#page-9-6), Cazenavette et al. [\[1\]](#page-8-0) propose to match segments of the parameter trajectories trained on synthetic data with long-range training trajectory segments of networks trained on the real datasets. However, the error accumulation of the parameters in particular segments is still inevitable. Instead, our strategy further mitigates the accumulated trajectory errors with the guidance of a flat teacher trajectory inspired by the heuristic of Sharpness-aware Minimization.

The geometry of the loss landscape. Minimizing the spectrum of the Hessian matrix  $\nabla_{\theta}^2 f_{\theta}$  as in [Equation 11](#page-4-1) is an difficult and expensive task. Fortunately, a series of sharpness-aware minimization methods [\[10,](#page-8-26) [11,](#page-8-14) [51\]](#page-9-14) have been proposed to perform the task implicitly with low cost for improved generalization. It has been argued in many studies [\[7,](#page-8-27)[17,](#page-8-22)[18,](#page-8-23)[20\]](#page-8-28) that the spectrum of the Hessian matrix constitutes a good characterization of the geometry of the loss landscape (sharpness), which then translates to having a strong relationship to the generalization abilities [\[7,](#page-8-27) [30,](#page-8-29) [31\]](#page-9-19) of the network. We leverage the approaches from  $[11, 51]$  $[11, 51]$  $[11, 51]$  to efficiently optimizing the spectrum of the Hessian matrix to minimize the accumulated trajectory error in this work.

<span id="page-13-0"></span>

| ipc                       | CIFAR-10 |        |        | CIFAR-100 |        |        | Tiny ImageNet |        |
|---------------------------|----------|--------|--------|-----------|--------|--------|---------------|--------|
|                           | 1        | 10     | 50     | 1         | 10     | 50     | 1             | 10     |
| Synthetic Step            | 50       | 30     | 30     | 40        | 20     | 80     | 30            | 20     |
| Expert Epoch              | 2        | 2      | 2      | 3         | 2      | 2      | 2             | 2      |
| Max Start Epoch           | 2        | 20     | 40     | 20        | 40     | 40     | 10            | 40     |
| Synthetic Batch Size      | -        | -      | -      | -         | -      | 1000   | -             | 500    |
| Learning Rate (Pixels)    | 100      | 100    | 1000   | 1000      | 1000   | 1000   | 10000         | 10000  |
| Learning Rate (Step Size) | $1e-7$   | $1e-5$ | $1e-5$ | $1e-5$    | $1e-5$ | $1e-5$ | $1e-4$        | $1e-4$ |
| Learning Rate (Teacher)   | 0.01     | 0.001  | 0.01   | 0.01      | 0.01   | 0.01   | 0.01          | 0.01   |
| $\alpha$                  | 0.3      | 0.3    | 1      | 1         | 1      | 1      | 1             | 1      |
| EMA Decay                 | 0.9999   | 0.9995 | 0.999  | 0.9995    | 0.9995 | 0.999  | 0.999         | 0.999  |

Table 7. Hyperparameter values we used for the main result table.

<span id="page-13-1"></span>Image /page/13/Picture/2 description: The image is a collage of nine small, pixelated images arranged in a horizontal row. The images appear to be of various objects, possibly from a dataset of everyday items. From left to right, the first image is blurry and blue. The second image shows a red and silver car. The third image is a close-up of a green object with a shiny, reflective surface, possibly a Christmas ornament or a ball. The fourth image is a blurry brown and white object. The fifth image is a blurry green object with a brown element. The sixth image is a blurry close-up of a person's face, showing an eye and part of the nose. The seventh image is a blurry green object. The eighth image is a blurry dark object with some lighter elements. The ninth image is a blurry blue object.

Figure 6. Visualizations of synthetic images distilled from the 32  $\times$  32 CIFAR-10 dataset with  $\text{ipc} = 1$ .

<span id="page-14-0"></span>Image /page/14/Picture/0 description: The image displays a grid of 64 synthetic images, arranged in an 8x8 format. Each small image is a colorful, abstract representation of various objects or scenes. The overall impression is a mosaic of diverse visual patterns, with some images appearing to depict textures, others suggesting architectural elements, and some showing more organic or object-like forms. The colors range from muted blues and grays to vibrant reds, greens, and yellows, creating a rich and varied visual tapestry.

Figure 7. Visualizations of part of synthetic images distilled from the  $64 \times 64$  Tiny ImageNet dataset with  $\text{ipc} = 1$ .

<span id="page-14-1"></span>Image /page/14/Figure/2 description: The image displays a grid of 12 blurry, abstract images arranged in a single row. Each image appears to be a close-up or a zoomed-out view of a natural scene, possibly outdoors. The colors are muted, with dominant tones of green, gray, brown, and hints of red and blue. Some images show textures resembling foliage, rocks, or water, while others have a more chaotic, swirling pattern. There are no discernible objects or clear subjects in any of the individual images, suggesting they might be artistic interpretations or heavily processed photographs.

Figure 8. Visualizations of synthetic images distilled from the  $128 \times 128$  ImageNette subset with  $\text{ipc} = 1$ .

<span id="page-15-0"></span>Image /page/15/Picture/0 description: This is a grid of 70 images, arranged in 10 rows and 7 columns. The images appear to be synthetic, with a variety of subjects including flowers, animals (such as seals and raccoons), landscapes with roads, and abstract patterns. The overall color palette is diverse, ranging from vibrant reds and oranges in the flower images to cool blues and greens in the animal and landscape images. The images are small and have a slightly blurry or pixelated quality, suggesting they are generated by a machine learning model.

Figure 9. Visualizations of part of synthetic images distilled from the  $32 \times 32$  CIFAR-100 dataset with  $\text{ipc} = 10$ .
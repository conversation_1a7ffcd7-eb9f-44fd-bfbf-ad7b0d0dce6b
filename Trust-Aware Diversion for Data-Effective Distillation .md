# Trust-Aware Diversion for Data-Effective Distillation

<PERSON><PERSON><PERSON><PERSON><sup>1</sup> <PERSON><PERSON><sup>2</sup> <PERSON><PERSON><sup>1</sup> <PERSON><PERSON><sup>3</sup> <PERSON><PERSON><sup>1</sup> <sup>1</sup>The University of Queensland  $\alpha$ <sup>2</sup>Auckland University of Technology  $\alpha$ <sup>3</sup>Jilin University

### Abstract

Dataset distillation compresses a large dataset into a small synthetic subset that retains essential information. Existing methods assume that all samples are perfectly labeled, limiting their real-world applications where incorrect labels are ubiquitous. These mislabeled samples introduce untrustworthy information into the dataset, which misleads model optimization in dataset distillation. To tackle this issue, we propose a Trust-Aware Diversion (TAD) dataset distillation method. Our proposed TAD introduces an iterative dual-loop optimization framework for data-effective distillation. Specifically, the outer loop divides data into trusted and untrusted spaces, redirecting distillation toward trusted samples to guarantee trust in the distillation process. This step minimizes the impact of mislabeled samples on dataset distillation. The inner loop maximizes the distillation objective by recalibrating untrusted samples, thus transforming them into valuable ones for distillation. This dual-loop iteratively refines and compensates for each other, gradually expanding the trusted space and shrinking the untrusted space. Experiments demonstrate that our method can significantly improve the performance of existing dataset distillation methods on three widely used benchmarks (CIFAR10, CIFAR100, and Tiny ImageNet) in three challenging mislabeled settings (symmetric, asymmetric, and real-world).

# 1. Introduction

In an era of massive datasets, data-efficient learning is pivotal for achieving high performance with a limited computation budget. Dataset Distillation (DD) presents a promising solution by synthesizing a small amount of highly informative data that summarizes large volumes of real data, allowing models to maintain high performance with less data [\(Zhao & Bilen,](#page-10-0) [2021b;](#page-10-0) [Cazenavette et al.,](#page-8-0) [2022;](#page-8-0) [Deng](#page-8-1) [et al.,](#page-8-1) [2024\)](#page-8-1).

The current success of DD relies on the assumption that all labels are completely correct. However, erroneously labeled

Image /page/0/Figure/7 description: The image illustrates a process involving an 'Untrusted Space' and a 'Trusted Space'. The Untrusted Space, labeled 'Shrinking', contains an 'Inner Loop' with red arrows indicating a cyclical process, and 'Recalibrated Samples' in a green sector. 'Low Reliable Samples' are shown in a yellow sector. An 'Outer Loop' connects the Untrusted Space to the Trusted Space. The Trusted Space, labeled 'Expanding', contains 'Low Reliable Samples' in a yellow sector, and 'Synthetic Images' and 'Real Images' feeding into a neural network labeled 'Distillation' in a green sector. Arrows indicate the flow of 'Low Reliable Samples' from the Untrusted to the Trusted Space and 'Recalibrated Samples' from the Trusted to the Untrusted Space.

Figure 1. Illustration of our proposed Trust-Aware Diversion (TAD) dataset distillation method. The outer loop separates data into trusted and untrusted spaces, rerouting distillation toward trusted samples. The inner loop recalibrates untrusted samples and transposes them to the trusted space. Through iterative interactions, the trusted space expands while the untrusted space shrinks, improving dataset distillation under noise conditions.

data is ubiquitous in real-world scenarios. State-of-the-art DD methods [\(Du et al.,](#page-8-2) [2023;](#page-8-2) [Guo et al.,](#page-8-3) [2024;](#page-8-3) [Liu et al.,](#page-9-0) [2024\)](#page-9-0) usually adopt a trajectory matching strategy, which aligns the parameters trained on distilled synthetic data (*i.e*., student model) with the parameters trained on real data (*i.e*., expert model). With the existence of noisy labels, an expert model tends to produce a misrepresented trajectory caused by the incorrectly labeled data. Consequently, if a student model attempts to match this expert trajectory, it would produce inferior synthetic data and suffer overall performance degradation.

In this paper, we explore a more realistic scenario, *i.e*., Dataset Distillation with Noisy Labels (DDNL). Notably, noisy labels introduce a unique challenge to DD: how to maintain a trustworthy distillation process without being misled by noisy samples. We observed that existing methods struggle to identify mislabeled or noisy data perfectly [\(Li](#page-9-1) [et al.,](#page-9-1) [2022;](#page-9-1) [Zhao et al.,](#page-10-1) [2022;](#page-10-1) [Karim et al.,](#page-8-4) [2022\)](#page-8-4). Thus, when these methods are applied, noisy data inevitably propagates into the dataset distillation, amplifying errors and compromising the performance of the distillation.

To tackle this issue, we propose a Trust-Aware Diversion (TAD) dataset distillation method, which introduces an iterative dual-loop optimization framework (*i.e*., an outer loop and an inner loop) for data-effective distillation. The outer loop is in charge of diverting data into trusted and untrusted

Image /page/1/Figure/1 description: The image displays two plots side-by-side. The left plot is titled "Training Loss Curve" and shows the training loss over 400 epochs for both "Clean" (blue line) and "Noisy (40%)" (orange line) data. The "Clean" data shows a rapid decrease in loss, stabilizing around 0.25. The "Noisy (40%)" data shows a slower decrease, stabilizing around 0.8. The right plot is titled "Test Accuracy Curve" and shows the test accuracy over 400 epochs for both "Clean" (blue line) and "Noisy (40%)" (orange line) data. The "Clean" data shows a steady increase in accuracy, reaching approximately 0.82. The "Noisy (40%)" data shows an initial rapid increase, peaking around 0.71 at epoch 50, followed by a gradual decrease to approximately 0.55 by epoch 400.

Figure 2. Training loss and test accuracy curves of an expert model on CIFAR-10 with clean and 40% symmetric noisy labels. Noise impedes convergence and significantly degrades performance.

spaces to ensure trustworthy distillation, while the inner loop recalibrates untrusted samples, refining them into valuable data for distillation. Through this iterative interaction, TAD can reliably refine the distinction between trusted and untrusted samples, progressively mitigating the impact of mislabeled data on data distillation.

To be specific, the outer loop separates data into trusted and untrusted spaces, and redirects distillation towards trusted samples, thus ensuring a trustworthy distillation process. The memorization effect [\(Arpit et al.,](#page-8-5) [2017;](#page-8-5) [Yao et al.,](#page-10-2) [2020\)](#page-10-2) of deep neural networks (DNNs) shows that DNNs first learn clean samples and then noisy samples. In addition, we observed a clear gap in loss values between clean and noisy samples during training. Clean samples usually have smaller losses, while noisy samples typically exhibit higher losses, as shown in Fig. [2.](#page-1-0) Motivated by this, data can be interpreted as a mixture of two distinct sample categories (*i.e*., trusted and untrusted). Thus, we employ the classwise mean posterior probability for the division criterion. In addition, we introduce a consistent regularization term that avoids overfitting to the wrongly divided samples, ensuring trusted samples contribute to distillation effectively.

Although data has been partitioned into trusted and untrusted spaces in the outer loop, some noisy samples may still be mistakenly classified as trusted ones. To address this issue, we introduce a reliability score in the inner loop to quantify the trustworthiness of each sample in the trusted space, providing a fine-grained evaluation of reliability. Since the distilled synthetic data captures a representative pattern of real data [\(Li et al.,](#page-9-2) [2024;](#page-9-2) [Guo et al.,](#page-8-3) [2024\)](#page-8-3), we measure the reliability of samples in the trusted space by computing their Mahalanobis distance [\(De Maesschalck et al.,](#page-8-6) [2000\)](#page-8-6) to the class distributions. Based on the reliability score, we rank trusted samples and select the most reliable ones from each class. Then, these highly-reliabile samples can be used to sieve less reliable trusted samples by measuring their similarities. Moreover, they can be employed to recall samples from the untrusted space, thus fully exploiting all the samples. The inner loop compensates for the outer loop, thus further preventing the propagation of unreliable

information into dataset distillation. Therefore, this iterative interaction between the inner and outer loops continuously refines the data partitioning, progressively expanding the trusted space while shrinking the untrusted space.

<span id="page-1-0"></span>To the best of our knowledge, we are the first to investigate the Dataset Distillation with Noisy Labels (DDNL). Extensive experiments validate the superior performance of our proposed Trust-Aware Diversion (TAD) dataset distillation method. For instance, on CIFAR-100 with 40% symmetric mislabeled data, our proposed TAD consistently outperforms ATT [\(Liu et al.,](#page-9-0) [2024\)](#page-9-0). When Images Per Class (IPC) is 10, TAD achieves 41.5% accuracy, significantly outperforming ATT (32.6%) and the two-stage baseline (36.5%), which first denoises the data followed by applying dataset distillation. Similarly, when IPC is 50, TAD attains 44.2% accuracy, outperforming ATT (37.1%) and the two-stage baseline (41.0%).

## 2. Related Work

Dataset Distillation. Dataset distillation aims to create a compact synthetic dataset that preserves the essential information in the large-scale original dataset, making it more efficient for training while achieving performance comparable to the original dataset. Dataset distillation was initially introduced by Wang *et al*. [\(Wang et al.,](#page-9-3) [2018\)](#page-9-3), drawing inspiration from Knowledge Distillation [\(Hinton,](#page-8-7) [2015\)](#page-8-7). Subsequent work has explored various methods to enhance the effectiveness of dataset distillation, focusing on different strategies to match the information in the original and synthetic datasets. *Gradient matching* aims to align gradients by performing a one-step distance-matching process between the network trained on the original dataset and the same network trained on the synthetic data [\(Zhao &](#page-10-0) [Bilen,](#page-10-0) [2021b;](#page-10-0) [Lee et al.,](#page-8-8) [2022;](#page-8-8) [Zhao & Bilen,](#page-10-3) [2021a\)](#page-10-3). *Distribution matching* directly aligns the data distributions of original and synthetic datasets through a single-level optimization process, offering an efficient yet effective strategy for dataset distillation [\(Sajedi et al.,](#page-9-4) [2023;](#page-9-4) [Liu et al.,](#page-9-5) [2023;](#page-9-5) [Zhao et al.,](#page-10-4) [2023;](#page-10-4) [Deng et al.,](#page-8-1) [2024\)](#page-8-1). *Training trajectory matching* aligns the training trajectories of models trained on the original versus synthetic data, allowing multi-step optimization to capture nuanced information about how model parameters evolve during training [\(Cazenavette et al.,](#page-8-0) [2022;](#page-8-0) [Yang et al.,](#page-10-5) [2024;](#page-10-5) [Du et al.,](#page-8-2) [2023;](#page-8-2) [Guo et al.,](#page-8-3) [2024;](#page-8-3) [Liu](#page-9-0) [et al.,](#page-9-0) [2024\)](#page-9-0). Other concurrent works have improved dataset distillation baselines with various approaches, including soft labels [\(Xiao & He,](#page-10-6) [2024;](#page-10-6) [Sucholutsky & Schonlau,](#page-9-6) [2021;](#page-9-6) [Qin et al.,](#page-9-7) [2024\)](#page-9-7), decoupled distillation [\(Yin et al.,](#page-10-7) [2023;](#page-10-7) [Shao et al.,](#page-9-8) [2024;](#page-9-8) [Sun et al.,](#page-9-9) [2024\)](#page-9-9), data parameterization [\(Wei et al.,](#page-9-10) [2023b;](#page-9-10) [Son et al.,](#page-9-11) [2024;](#page-9-11) [Kim et al.,](#page-8-9) [2022\)](#page-8-9), *etc*.. Dataset distillation has enabled various applications including neural architecture search [\(Medvedev &](#page-9-12) [D'yakonov,](#page-9-12) [2021;](#page-9-12) [Cui et al.,](#page-8-10) [2022;](#page-8-10) [Zhao & Bilen,](#page-10-3) [2021a\)](#page-10-3), continual learning [\(Sangermano et al.,](#page-9-13) [2022;](#page-9-13) [Yang et al.,](#page-10-8) [2023;](#page-10-8) [Gu et al.,](#page-8-11) [2024\)](#page-8-11), privacy protection [\(Chung et al.,](#page-8-12) [2024;](#page-8-12) [Li et al.,](#page-8-13) [2023;](#page-8-13) [Li & Kainz,](#page-9-14) [2024\)](#page-9-14), *etc*..

Practical Dataset Distillation. With the rapid advancement of deep learning techniques and optimization methods, dataset distillation has achieved remarkable progress. However, these methods assume that all labels are completely correct. This assumption may not hold in real-world scenarios, where mislabeled or noisy data is ubiquitous.

Imperfect data can significantly impact the effectiveness of dataset distillation. The synthetic dataset generated from such data often inherits the inconsistencies and errors of the original dataset. As a result, it struggles to accurately capture essential information from the original dataset. Specifically, surrogate-based methods [\(Zhao & Bilen,](#page-10-0) [2021b](#page-10-0)[;a;](#page-10-3) [Du et al.,](#page-8-2) [2023;](#page-8-2) [Guo et al.,](#page-8-3) [2024;](#page-8-3) [Liu et al.,](#page-9-0) [2024\)](#page-9-0) rely on accurate gradients derived from the original dataset. Noisy or mislabeled data can distort these gradients, leading to incorrect optimization. In addition, distribution-based methods [\(Sajedi et al.,](#page-9-4) [2023;](#page-9-4) [Zhao et al.,](#page-10-4) [2023;](#page-10-4) [Yin et al.,](#page-10-7) [2023;](#page-10-7) [Sun et al.,](#page-9-9) [2024\)](#page-9-9) aim to match distributions between the original and synthetic datasets in a randomly initialized or pretrained network. However, noisy data makes it difficult to capture the true data distribution.

A straightforward approach to addressing this issue involves first denoising the data and then applying dataset distillation. This includes identifying mislabeled data [\(Han et al.,](#page-8-14) [2018;](#page-8-14) [Li et al.,](#page-9-15) [2020;](#page-9-15) [Albert et al.,](#page-8-15) [2022;](#page-8-15) [Northcutt et al.,](#page-9-16) [2021\)](#page-9-16) and then either removing it or assigning a pseudo-label [\(Tanaka](#page-9-17) [et al.,](#page-9-17) [2018;](#page-9-17) [Yi & Wu,](#page-10-9) [2019\)](#page-10-9). However, the independent denoising step has side effects in dataset distillation. The denoising step, despite its effectiveness, may introduce biases by misidentifying noisy data, leading to the removal of useful samples or the assignment of incorrect labels. As a result, some erroneous labels or pseudo-labels inevitably propagate to the dataset distillation stage, amplifying errors and compromising its effectiveness. This motivates us to design a trustworthy data-effective dataset distillation method through an iterative dual-loop optimization framework.

# 3. Preliminary and Problem Setup

Dataset Distillation (DD). Given a large training set  $\mathcal{T} = \{(\boldsymbol{x}_i, \boldsymbol{y}_i)\}_{i=1}^{|\mathcal{T}|}$  containing  $|\mathcal{T}|$  images and their labels, dataset distillation aims to synthesize a smaller set  $S = \{(\tilde{x}_i, \tilde{y}_i)\}_{i=1}^{|S|}$ . The objective of dataset distillation is to optimize the synthetic set S, making sure when a *student* model  $f_{\theta^S}$  is trained on the synthetic dataset S, it can achieve comparable performance with an *expert* model  $f_{\theta}$ trained on the original dataset  $\mathcal{T}$ .

**Trajectory Matching (TM).** The expert model  $f_{\theta^T}$  gen-

erates a sequence of parameters to make up an expert trajectory  $\{\boldsymbol{\theta}_{i}^{\mathcal{T}}\}_{i=1}^{m}$ . Similarly, the student model generates a trajectory  $\{\boldsymbol{\theta}_i^{\mathcal{S}}\}_{i=1}^n$ . TM-based methods perform distillation by matching the student trajectory with the expert trajectory. Starting from step  $t$ , the matching objective is defined as:

<span id="page-2-1"></span>
$$
\mathcal{L}_{TM} = \frac{\|\boldsymbol{\theta}_{t+N}^S - \boldsymbol{\theta}_{t+M}^T\|_2^2}{\|\boldsymbol{\theta}_t^T - \boldsymbol{\theta}_{t+M}^T\|_2^2},
$$
(1)

where N and M are skipping steps ( $N \ll M$ ).  $\mathcal{L}_{TM}$  is adopted to optimize the synthetic set  $S$ .

Dataset Distillation with Noisy Labels (DDNL). Traditional dataset distillation assumes that labels in  $T$  are completely correct. However, noisy labels are ubiquitous in real-world scenarios and impair model performance. This work extends the dataset distillation to handle situations where the original training set  $T$  contains noisy data. Given  $\mathcal{T}' = \{(\boldsymbol{x}_i, \boldsymbol{y}'_i)\}_{i=1}^{|\mathcal{T}|}$ , the label  $\boldsymbol{y}'_i$  may be noisy.

To analyze the impact of noisy labels on dataset distillation, we investigate the behavior of cross-entropy (CE) loss and its gradient under noisy labels. CE is the most commonly used loss for trajectory matching in dataset distillation. Formally, CE loss and gradient are defined as:

$$
\mathcal{L}_{CE}\left(f_{\boldsymbol{\theta}}(\boldsymbol{x}), \boldsymbol{y}\right) = -\sum_{k=1}^{K} \boldsymbol{y}_{(k)} \log(\sigma(\boldsymbol{z})_k), \tag{2}
$$

<span id="page-2-0"></span>
$$
\frac{\partial \mathcal{L}_{\text{CE}}(f_{\boldsymbol{\theta}}(\boldsymbol{x}), \boldsymbol{y})}{\partial \boldsymbol{\theta}} = \sum_{k=1}^{K} (\sigma(\boldsymbol{z})_k - \boldsymbol{y}_{(k)}) \frac{\partial \boldsymbol{z}_k}{\partial \boldsymbol{\theta}}, \quad (3)
$$

where  $z = f_{\theta}(x)$  denotes the predicted logit value for a sample x,  $\sigma(z)_k$  denotes the k-th output of softmax function  $\sigma$ .  $\mathcal{L}_{CE}$  is unbounded and particularly vulnerable to noisy labels [\(Ghosh et al.,](#page-8-16) [2017;](#page-8-16) [Wei et al.,](#page-9-18) [2023a\)](#page-9-18). If y is mis-labeled for a true class k, then  $y_{(k)} = 0$ . But for a decent model,  $\sigma(z)_k \to 1$ . Consequently, the gradient updates in Eq. [3](#page-2-0) will be misled by noisy labels, causing the parameters to gradually deviate from the ideal trajectory derived from clean data.

In dataset distillation, noisy labels will drive the expert trajectory  $\{\boldsymbol{\theta}_i^{\mathcal{T}}\}_{i=1}^m$ , away from the ideal path derived from clean data. For trajectory matching (Eq. [1\)](#page-2-1), the student model will align with this distorted expert trajectory and inherit the noise distortions, thus producing suboptimal synthetic data that ultimately degrade the overall performance of dataset distillation.

# 4. Method

To tackle the challenging DDNL problem, we propose a Trust-Aware Diversion (TAD) dataset distillation method, as shown in Fig. [3.](#page-3-0) Specifically, our proposed TAD introduces an end-to-end dual-loop (*i.e*., outer loop and in-

Image /page/3/Figure/1 description: The image depicts a dual-loop framework for dataset distillation, comprising an outer loop and an inner loop. The outer loop begins with a 'Dataset with Noisy Labels' containing images of a dog, car, dog, cat, horse, cat, plane, deer, and plane. This dataset feeds into an 'Expert Model' (indicated by a snowflake icon). The inner loop involves 'Recalibration' where untrusted samples are compared to trusted samples, with arrows indicating 'Dissimilar' and 'Similar' relationships, leading to an 'Update Label' process. The recalibrated samples are then processed in an 'Untrusted Space' with a 'Shrinking' mechanism, and a 'Trusted Space' with an 'Expanding' mechanism. Both spaces feed into another 'Expert Model' (indicated by a flame icon) which performs 'Matching' between 'Expert Trajectory' and 'Student Trajectory'. The output of this matching process is a 'Student Model' that is trained using 'Synthetic Images' through 'Dataset Distillation'.

Figure 3. Overview of the proposed Trust-Aware Diversion (TAD) dataset distillation method. TAD introduces a dual-loop optimization framework for trustworthy dataset distillation. The outer loop divides data into trusted and untrusted spaces, rerouting distillation toward reliable samples, while the inner loop refines untrusted samples for potential reuse. Through iterative interaction, the two loops progressively expand the trusted space and mitigate the impact of noisy labels to achieve data-effective distillation.

ner loop) optimization framework for data-effective distillation. The outer loop (Sec. [4.1\)](#page-3-1) divides data into trusted and untrusted spaces, redirecting distillation toward trusted samples to minimize the impact of mislabeled samples on dataset distillation. The inner loop (Sec. [4.2\)](#page-4-0) maximizes the distillation objective by recalibrating untrusted samples, thus transforming them into valuable ones for distillation. This dual-loop iteratively refines and compensates for each other, gradually expanding the trusted space and shrinking the untrusted space.

# <span id="page-3-1"></span>4.1. Outer Loop: Diverting Distillation for Trustworthy Learning

Effectively distinguishing clean samples from noisy ones is essential to fully leverage high-quality information while mitigating the adverse effects of noisy labels. This distinction allows dataset distillation to prioritize learning from more reliable samples, ensuring a trustworthy distillation process. During model training, the loss associated with each sample can help indicate whether it is a noisy sample [\(Li et al.,](#page-9-15) [2020;](#page-9-15) [Wei et al.,](#page-9-19) [2020;](#page-9-19) [Yao et al.,](#page-10-2) [2020;](#page-10-2) [Zhou](#page-10-10) [et al.,](#page-10-10) [2020\)](#page-10-10), as shown in Fig. [2.](#page-1-0) Clean samples are mutually consistent, allowing the model to produce gradient updates more efficiently and to train faster. In contrast, noisy samples often contain conflicting information, causing persistent inconsistencies in training progress. Intuitively, samples with smaller loss values are highly likely to be clean samples during the training process.

Motivated by this, we interpret data as a mixture of two distinct sample categories: trusted and untrusted. To model this

distinction, we represent loss distribution using a two-mode Gaussian Mixture Model (GMM), where one mode denotes trusted samples with lower losses, and the other represents untrusted samples with higher losses. This formulation is expressed as follows:

<span id="page-3-0"></span>
$$
p(\ell_i) = \sum_{k=1}^{K} \pi_k \cdot \mathcal{N}(\ell_i \mid \mu_k, \sigma_k^2), \tag{4}
$$

where  $\ell_i = \mathcal{L}_{CE}(f_{\theta}(\boldsymbol{x}_i), \boldsymbol{y}'_i)$  is the cross-entropy loss of the *i*-th sample and  $\pi_k$  is the mixing coefficient. For each sample, its trusted sample confidence  $w_i$  is the posterior probability  $p(k = 1 | \ell_i)$ , where  $k = 1$  denotes the component of GMM with a smaller mean  $\mu_k$  (*i.e.*, smaller loss). The GMM parameters are estimated with the Expectation-Maximization algorithm. The trusted sample confidences  $\{w_i\}$  of the training dataset are then used to divide samples into trusted and untrusted spaces based on a threshold  $\tau$ .

A straightforward way is to adopt a constant threshold (*e.g*., 0.5) for all samples, as in [\(Arazo et al.,](#page-8-17) [2019;](#page-8-17) [Li et al.,](#page-9-15) [2020;](#page-9-15) [Yang et al.,](#page-10-11) [2022;](#page-10-11) [Zheltonozhskii et al.,](#page-10-12) [2022\)](#page-10-12). However, there are some issues:

- *The learning of the network is progressive. In the early stages, confidence in identifying trusted samples is low. A fixed threshold cannot adapt to the estimation of trusted and untrusted samples throughout training.*
- *Different classes have varying learning difficulty levels. Employing a unified threshold for all classes inadvertently causes class imbalance, particularly in datasets with numerous classes.*

To address the above issues, we propose a class-wise dynamic threshold to divide trusted samples and untrusted samples. For a class c, we define the threshold  $\tau_c$  based on the posterior probabilities of GMM, *i.e.*, the confidence  $w_i$ of  $x_i$  being a trusted sample. Per-class mean confidence score is calculated as the threshold:

$$
\tau_c = \frac{1}{N_c} \sum_{i=1}^{N_c} w_i, \ w_i = p(k = 1 | \ell_i) \text{ and } \mathbf{y}'_i = c,
$$
 (5)

where  $N_c$  is the number of samples in class c. The mean eliminates the effect of outliers, while the class-wise dynamic thresholds evolve as the training progresses to reflect the training dynamics and imbalanced class difficulties.

Building on existing dataset distillation approaches [\(Cazenavette et al.,](#page-8-0) [2022;](#page-8-0) [Du et al.,](#page-8-2) [2023;](#page-8-2) [Guo et al.,](#page-8-3) [2024;](#page-8-3) [Liu et al.,](#page-9-0) [2024\)](#page-9-0), we use CE loss to train expert networks solely on trusted space, preventing the model from learning misrepresented patterns in mislabeled data. Additionally, to avoid overfitting to outliers and improve model consistency, we introduce a consistent regularization term  $\mathcal{L}_C$  by reversing the roles of predicted probabilities and given labels. The training objective is formulated as follows:

$$
\mathcal{L} = \mathcal{L}_{CE} + \lambda \mathcal{L}_{C}
$$
  
= 
$$
-\sum_{i=1}^{N} {y'}_{i} \log(f_{\theta}(\boldsymbol{x}_{i}) + \lambda f_{\theta}(\boldsymbol{x}_{i}) \log({y'}_{i})), (6)
$$

where  $\lambda$  is a coefficient to balance the two loss items. For  $y'_i = 1$ , the regularization term  $\mathcal{L}_C = 0$  does not contribute to the loss L. For  $y'_i = 0$ ,  $\log(0)$  is defined as a negative constant to ensure numerical stability. For the mislabeled samples,  $\mathcal{L}_{CE}$  imposes a large loss, magnifying the effect of incorrect labels. This is counteracted by the  $\mathcal{L}_C$  term, which reduces excessive loss and stabilizes the training process.

<span id="page-4-0"></span>

### 4.2. Inner Loop: Trust-Aware Recalibration

Although data has been partitioned into trusted and untrusted spaces in the outer loop, some noisy samples may still be mistakenly classified as trusted ones. To address this issue, we introduce a reliability score in the inner loop to quantify the trustworthiness of each sample in the trusted space, providing a fine-grained evaluation of reliability. Synthetic data aligning only the initial training phases of expert networks (*e.g*., the first four epochs) effectively capture representative patterns in real data [\(Li et al.,](#page-9-2) [2024;](#page-9-2) [Guo et al.,](#page-8-3) [2024\)](#page-8-3). Thus, we use distilled data as stable anchors for assessing data quality. A reliability score of each trusted sample is then calculated by computing its Mahalanobis distance [\(De Maesschalck et al.,](#page-8-6) [2000\)](#page-8-6) to the class distributions. For accurate label calibration of untrusted samples, we reference only the top- $k$  reliable trusted samples based on reliability scores.

First, anchors are defined as the synthetic images  $\{(\hat{x}_i, \hat{y}_i)\}_{i=1}^{N_A}$  synthesized by only matching the early trajectory, which is feasible due to the memorization effects of deep networks [\(Xia et al.,](#page-9-20) [2020;](#page-9-20) [Arpit et al.,](#page-8-5) [2017;](#page-8-5) [Liu et al.,](#page-9-21) [2020\)](#page-9-21). Here  $N_A = n \times C$ , where *n* represents the number of synthetic images per class, and  $C$  is the total number of classes. Second, the anchor images and trusted samples are embedded into the feature space by a feature extractor f (*i.e*., ResNet [\(He et al.,](#page-8-18) [2016\)](#page-8-18)), which is pre-trained in a self-supervision manner (*e.g*., SimCLR [\(Chen et al.,](#page-8-19) [2020\)](#page-8-19), MoCo [\(He et al.,](#page-8-20) [2020\)](#page-8-20)). Third, we calculate the Mahalanobis distance [\(De Maesschalck et al.,](#page-8-6) [2000\)](#page-8-6) between trusted samples and the per-class anchor distribution. Mahalanobis distance is an effective multivariate distance metric used to calculate the distance between a point and a cluster, and it is widely used in multivariate anomaly detection, classification, and clustering analysis [\(Colombo et al.,](#page-8-21) [2022;](#page-8-21) [Goswami et al.,](#page-8-22) [2024\)](#page-8-22). For each class c, the Mahalanobis distance is defined as:

$$
D_M(\boldsymbol{x}_i, \boldsymbol{\mu}_c) = \sqrt{(\boldsymbol{x}_i - \boldsymbol{\mu}_c)^T \boldsymbol{\Sigma}_c^{-1} (\boldsymbol{x}_i - \boldsymbol{\mu}_c)},\quad (7)
$$

where  $x_i$  denotes the *i*-th trusted sample,  $\mu_c$  and  $\Sigma_c^{-1}$ are the estimated class mean and covariance from anchors  $\{(\hat{\boldsymbol{x}}_i, \hat{\boldsymbol{y}}_i)\}_{i=1}^{N_A}$  belonging to class c. We then apply min-max normalization to re-scale each  $D_M$  to the range [0, 1]. Then, we use the Mahalanobis distance to estimate the reliability of a trusted sample  $\boldsymbol{x}_i$  and define  $\boldsymbol{M}_i^c = \bar{D}_M(\boldsymbol{x}_i,\boldsymbol{\mu}_c)$ .  $\bar{M}_i^c$ reflects the overall proximity of the sample to the anchor distribution of the class c.

To skip the unreliable trusted samples, we only refer to the top- $k$  reliable samples in the trusted space to calibrate the samples in the untrusted space. As Mahalanobis distance requires an accurate covariance structure, it is not suitable for comparing distances between untrusted and trusted samples. Instead, we use cosine similarity to compare trusted samples  $\{x_i\}$  with an untrusted sample  $x'_j$ . The calibrated pseudo label of  $x'_j$  for each class c is computed as:

$$
p^{c}(\boldsymbol{x}_{j}') = \sum_{i=1}^{k} \boldsymbol{M}_{i}^{c} \cdot sim(\boldsymbol{x}_{j}', \boldsymbol{x}_{i}) \cdot \delta(y_{i} = c), \qquad (8)
$$

where  $\delta(y_i = c)$  is an indicator function equal to 1 if the label  $y_i$  of  $x_i$  is class c, and 0 otherwise. The pseudo label for  $x'_j$  is then assigned based on the class c with the highest score  $p^c(\boldsymbol{x}'_j)$ .

# 5. Experiments

## 5.1. Datasets

Generation of Simulated Datasets. Our experiments are conducted on CIFAR-10, CIFAR-100, and Tiny ImageNet with two types of synthetic noise (*i.e*., symmetric and asym-

|                      | <b>IPC</b>                     |                | $\mathbf{1}$   |                  |                                  | 10             |                | 50                                                                                                                                                                                                                                                               |                |                |
|----------------------|--------------------------------|----------------|----------------|------------------|----------------------------------|----------------|----------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------|----------------|
|                      | Noise Ratio                    | $0\%$          | 20%            | 40%              | $0\%$                            | 20%            | 40%            | $0\%$                                                                                                                                                                                                                                                            | 20%            | 40%            |
|                      |                                |                |                | CIFAR-10         |                                  |                |                |                                                                                                                                                                                                                                                                  |                |                |
|                      |                                |                |                |                  |                                  |                |                |                                                                                                                                                                                                                                                                  |                |                |
|                      | MTT (Cazenavette et al., 2022) | $46.3 \pm 0.8$ | $45.2 \pm 0.3$ | $44.2 \pm 0.7$   | $65.3 \pm 0.7$                   | $62.4 \pm 0.6$ |                | $71.6 \pm 0.2$                                                                                                                                                                                                                                                   | $68.6 \pm 0.5$ | $60.2 \pm 0.7$ |
|                      | $+$ TAD                        |                | $46.2 \pm 0.4$ | $46.1 \pm 0.6$   |                                  | $64.7 \pm 0.5$ | $64.6 \pm 0.6$ | $73.8 \pm 0.2$                                                                                                                                                                                                                                                   | $71.5 \pm 0.2$ | $71.2 \pm 0.3$ |
|                      | FTD (Du et al., 2023)          | $46.8 \pm 0.3$ | $45.3 \pm 1.0$ | 43.9 ± 0.7       | $66.6 \pm 0.3$                   | $61.3 \pm 0.5$ | $56.2 \pm 0.9$ |                                                                                                                                                                                                                                                                  | $67.9 \pm 0.7$ | $61.1 \pm 0.6$ |
| Sym.                 | $+$ TAD                        |                | $46.6 \pm 0.4$ | $46.2 \pm 1.2$   |                                  | $66.1 \pm 0.2$ | $65.4 \pm 0.5$ |                                                                                                                                                                                                                                                                  | $71.9 \pm 0.3$ | $71.7 \pm 0.2$ |
|                      | DATM (Guo et al., 2024)        | $46.9 \pm 0.5$ | $45.3 \pm 0.5$ | $42.9 \pm 0.3$   | $66.8 \pm 0.2$                   | $62.1 \pm 0.1$ | $58.2 \pm 0.4$ | $76.1 \pm 0.3$                                                                                                                                                                                                                                                   | $68.7 \pm 0.3$ | $61.5 \pm 0.4$ |
|                      | + TAD                          |                | $46.1 \pm 0.9$ | $45.8 \pm 0.2$   |                                  | $66.0 \pm 0.3$ | $65.5 \pm 0.6$ |                                                                                                                                                                                                                                                                  | $72.6 \pm 0.2$ | $71.6 \pm 0.2$ |
|                      | ATT (Liu et al., 2024)         | $48.3 \pm 1.0$ | $44.5 \pm 0.9$ | $41.8 \pm 0.9$   | $67.7 \pm 0.6$                   | $62.4 \pm 0.6$ | $58.1 \pm 0.6$ | $74.5 \pm 0.4$<br>$71.6 \pm 0.2$<br>$73.8 \pm 0.2$<br>$76.1 \pm 0.3$<br>$74.5 \pm 0.4$<br>$47.7 \pm 0.2$<br>$50.7 \pm 0.3$<br>55.0 ± 0.2<br>$51.2 \pm 0.3$<br>$47.7 \pm 0.2$<br>$50.7 \pm 0.3$<br>$55.0 \pm 0.2$<br>$51.2 \pm 0.3$<br>$28.0 \pm 0.3$<br>39.7±0.3 | $69.3 \pm 0.2$ | $63.8 \pm 0.3$ |
|                      | + TAD                          |                | $47.9 \pm 0.8$ | $46.8 \pm 0.5$   |                                  | $66.4 \pm 0.4$ | $66.2 \pm 0.2$ |                                                                                                                                                                                                                                                                  | $71.3 \pm 0.3$ | $71.1 \pm 0.4$ |
|                      | MTT (Cazenavette et al., 2022) | $46.3 \pm 0.8$ | 44.5±0.6       | $40.9 \pm 1.7$   | $65.3 \pm 0.7$                   | $58.7 \pm 0.3$ | $54.4 \pm 0.8$ |                                                                                                                                                                                                                                                                  | $67.5 \pm 0.4$ | $59.1 \pm 0.6$ |
|                      | $+$ TAD                        |                | $45.8 \pm 0.9$ | $45.6 \pm 1.4$   |                                  | $64.2 \pm 0.3$ | $63.9 \pm 0.3$ |                                                                                                                                                                                                                                                                  | $71.1 \pm 0.2$ | $70.0 \pm 0.3$ |
|                      | FTD (Du et al., 2023)          |                | $45.0 \pm 0.6$ | $42.5 \pm 1.0$   | $66.6 \pm 0.3$                   | $60.9 \pm 0.6$ | $53.4 \pm 0.6$ |                                                                                                                                                                                                                                                                  | $63.6 \pm 0.7$ | $53.3 \pm 0.8$ |
|                      | $+$ TAD                        | $46.8 \pm 0.3$ | $45.9 \pm 0.3$ | $45.8 \pm 0.8$   |                                  | $65.3 \pm 0.4$ | $64.9 \pm 0.5$ |                                                                                                                                                                                                                                                                  | $71.0 \pm 0.4$ | $69.9 \pm 0.2$ |
| Asym.                | DATM (Guo et al., 2024)        |                | $43.7 \pm 0.6$ | $38.7 \pm 0.3$   | $66.8 \pm 0.2$                   | $61.8 \pm 0.4$ | $57.3 \pm 0.4$ |                                                                                                                                                                                                                                                                  | $62.5 \pm 0.5$ | $55.0 \pm 0.4$ |
|                      | + TAD                          | $46.9 \pm 0.5$ | $46.0 \pm 0.3$ | $45.4 \pm 0.6$   |                                  | $64.8 \pm 0.5$ | $64.3 \pm 0.3$ |                                                                                                                                                                                                                                                                  | $72.4 \pm 0.2$ | $70.7 \pm 0.3$ |
|                      | ATT (Liu et al., 2024)         |                | $43.5 \pm 0.7$ | $41.9 \pm 1.2$   | $67.7 \pm 0.6$                   | $62.1 \pm 0.4$ |                |                                                                                                                                                                                                                                                                  | $67.3 \pm 0.5$ | $61.5 \pm 0.2$ |
|                      | + TAD                          | $48.3 \pm 1.0$ | $47.3 \pm 0.6$ | $46.4 \pm 0.4$   |                                  | $66.2 \pm 0.5$ |                |                                                                                                                                                                                                                                                                  | $70.6 \pm 0.2$ | $69.5 \pm 0.3$ |
|                      | <b>Full Dataset</b>            |                |                |                  |                                  | $84.8 \pm 0.1$ |                |                                                                                                                                                                                                                                                                  |                |                |
|                      |                                |                |                |                  |                                  |                |                |                                                                                                                                                                                                                                                                  |                |                |
|                      |                                |                |                | <b>CIFAR-100</b> |                                  |                |                |                                                                                                                                                                                                                                                                  |                |                |
|                      | MTT (Cazenavette et al., 2022) | $24.3 \pm 0.3$ | $23.3 \pm 0.4$ | $21.5 \pm 1.8$   | $40.1 \pm 0.4$                   | $36.5 \pm 0.2$ | $31.4 \pm 0.4$ |                                                                                                                                                                                                                                                                  | $43.8 \pm 0.4$ | $37.2 \pm 0.4$ |
|                      | $+$ TAD                        |                | $23.0 \pm 0.4$ | $21.7 \pm 0.1$   |                                  | $40.0 \pm 0.1$ | $39.4 \pm 0.1$ |                                                                                                                                                                                                                                                                  | $44.2 \pm 0.2$ | $43.2 \pm 0.1$ |
| Sym.                 | FTD (Du et al., 2023)          | $25.2 \pm 0.2$ | $23.8 \pm 0.5$ | $21.8 \pm 0.5$   | $43.4 \pm 0.3$                   | $38.9 \pm 0.5$ | $33.5 \pm 0.4$ |                                                                                                                                                                                                                                                                  | $43.5 \pm 0.3$ | $36.9 \pm 0.3$ |
|                      | + TAD                          |                | $24.1 \pm 0.2$ | $22.3 \pm 0.2$   |                                  | $40.4 \pm 0.4$ | $39.4 \pm 0.1$ |                                                                                                                                                                                                                                                                  | $45.6 \pm 0.1$ | $44.0 \pm 0.1$ |
|                      | DATM (Guo et al., 2024)        | 27.9 ± 0.2     | $23.1 \pm 0.2$ | $22.1 \pm 0.2$   | $47.2 \pm 0.4$                   | $39.7 \pm 0.4$ | $37.1 \pm 0.4$ | $57.5 \pm 1.2$<br>$54.6 \pm 0.6$<br>$65.6 \pm 0.2$<br>$31.9 \pm 0.2$<br>$39.5 \pm 0.1$<br>$\lambda$<br>J                                                                                                                                                         | $44.3 \pm 0.2$ | $40.2 \pm 0.2$ |
|                      | + TAD                          |                | $23.9 \pm 0.5$ | $22.3 \pm 0.3$   |                                  | $43.0 \pm 0.1$ | $41.5 \pm 0.1$ |                                                                                                                                                                                                                                                                  | $46.9 \pm 0.1$ | $46.1 \pm 0.1$ |
|                      | ATT (Liu et al., 2024)         | $26.1 \pm 0.3$ | $22.1 \pm 0.3$ | $19.9 \pm 0.8$   | $44.2 \pm 0.5$                   | $37.3 \pm 0.4$ | $32.6 \pm 0.3$ |                                                                                                                                                                                                                                                                  | $41.9 \pm 0.2$ | $37.1 \pm 0.2$ |
|                      | $+$ TAD                        |                | $22.9 \pm 0.2$ | $22.9 \pm 0.4$   |                                  | $42.8 \pm 0.1$ | $41.5 \pm 0.2$ |                                                                                                                                                                                                                                                                  | $45.5 \pm 0.1$ | $44.2 \pm 0.2$ |
|                      | MTT (Cazenavette et al., 2022) |                | $22.5 \pm 0.4$ | $20.0 \pm 0.6$   |                                  | $36.2 \pm 0.4$ | $30.9 \pm 0.3$ | $\sqrt{2}$<br>$\sqrt{2}$                                                                                                                                                                                                                                         | $43.3 \pm 0.2$ | $36.1 \pm 0.4$ |
|                      | + TAD                          | $24.3 \pm 0.3$ | $22.6 \pm 0.3$ | $20.2 \pm 0.3$   | $40.1 \pm 0.4$                   | $39.9 \pm 0.4$ | $37.3 \pm 0.4$ |                                                                                                                                                                                                                                                                  | $43.6 \pm 0.1$ | $41.3 \pm 0.1$ |
|                      | FTD (Du et al., 2023)          |                | $23.4 \pm 0.2$ | $21.0 \pm 0.5$   |                                  | $38.4 \pm 0.4$ | $32.8 \pm 0.3$ |                                                                                                                                                                                                                                                                  | $42.1 \pm 0.3$ | $35.6 \pm 0.3$ |
|                      | + TAD                          | $25.2 \pm 0.2$ | $23.2 \pm 0.2$ | $21.7 \pm 0.1$   | $43.4 \pm 0.3$                   | $39.6 \pm 0.5$ | $37.4 \pm 0.2$ |                                                                                                                                                                                                                                                                  | $44.2 \pm 0.1$ | $41.5 \pm 0.2$ |
| Asym.                | DATM (Guo et al., 2024)        |                | $22.9 \pm 0.1$ | $21.4 \pm 0.1$   |                                  | $39.0 \pm 0.2$ | $36.3 \pm 0.4$ |                                                                                                                                                                                                                                                                  | $43.5 \pm 0.1$ | $38.4 \pm 0.1$ |
|                      | + TAD                          | $27.9 \pm 0.2$ | $23.1 \pm 0.6$ | $21.7 \pm 0.5$   | $47.2 \pm 0.4$                   | $42.5 \pm 0.5$ | $39.6 \pm 0.2$ |                                                                                                                                                                                                                                                                  | $46.5 \pm 0.2$ | $43.7 \pm 0.2$ |
|                      | ATT (Liu et al., 2024)         |                | $21.7 \pm 0.2$ | $19.4 \pm 0.4$   |                                  | $36.7 \pm 0.5$ |                |                                                                                                                                                                                                                                                                  | $39.5 \pm 0.2$ | $33.6 \pm 0.3$ |
|                      | $+$ TAD                        | $26.1 \pm 0.3$ | $23.3 \pm 0.4$ | $20.3 \pm 0.3$   | $44.2 \pm 0.5$                   | $41.7 \pm 0.4$ |                |                                                                                                                                                                                                                                                                  | $44.5 \pm 0.3$ | $42.5 \pm 0.3$ |
|                      | <b>Full Dataset</b>            |                |                |                  |                                  | $56.2 \pm 0.3$ |                |                                                                                                                                                                                                                                                                  |                |                |
|                      |                                |                |                |                  |                                  |                |                |                                                                                                                                                                                                                                                                  |                |                |
| <b>Tiny ImageNet</b> |                                |                |                |                  |                                  |                |                |                                                                                                                                                                                                                                                                  |                |                |
|                      | MTT (Cazenavette et al., 2022) | $8.8 + 0.3$    | $8.0 \pm 0.3$  | $7.0 + 0.4$      | $23.2 \pm 0.2$                   | $18.5 \pm 0.2$ | $16.3 \pm 0.3$ |                                                                                                                                                                                                                                                                  | $22.8 \pm 0.2$ | $16.8 \pm 0.2$ |
|                      | $+$ TAD                        |                | $8.2 \pm 0.1$  | $7.5 \pm 0.5$    |                                  | $22.3 \pm 0.1$ | $21.9 \pm 0.5$ |                                                                                                                                                                                                                                                                  | $27.5 \pm 0.2$ | $26.7 \pm 0.2$ |
|                      | FTD (Du et al., 2023)          | $10.4 \pm 0.3$ | $9.1 \pm 0.4$  | $8.4 + 0.3$      | $24.5 \pm 0.2$<br>$31.1 \pm 0.3$ | $20.2 \pm 0.2$ | $16.4 \pm 0.5$ |                                                                                                                                                                                                                                                                  |                | Λ              |
| Sym.                 | + TAD                          |                | $10.4 \pm 0.4$ | $10.2 \pm 0.6$   |                                  | $24.3 \pm 0.4$ | $23.6 \pm 0.3$ |                                                                                                                                                                                                                                                                  |                | $\lambda$      |
|                      | DATM (Guo et al., 2024)        | $17.1 \pm 0.3$ | $10.4 \pm 0.3$ | $9.4 \pm 0.5$    |                                  | $24.6 \pm 0.3$ | $22.6 \pm 0.3$ |                                                                                                                                                                                                                                                                  | $30.3 \pm 0.4$ | $29.2 \pm 0.4$ |
|                      | + TAD                          |                | $14.2 \pm 0.1$ | $13.9 \pm 0.1$   |                                  | $27.2 \pm 0.3$ | $25.6 \pm 0.1$ |                                                                                                                                                                                                                                                                  | $33.9 \pm 0.1$ | $32.1 \pm 0.1$ |
|                      | ATT (Liu et al., 2024)         | $11.0 \pm 0.5$ | $7.1 \pm 0.3$  | $6.6 \pm 0.1$    | $25.8 \pm 0.4$                   | $20.1 \pm 0.2$ | $16.9 \pm 0.2$ |                                                                                                                                                                                                                                                                  |                | V              |
|                      | $+$ TAD                        |                | $10.2 \pm 0.2$ | $9.4 \pm 0.1$    |                                  | $24.9 \pm 0.2$ | $23.8 \pm 0.5$ |                                                                                                                                                                                                                                                                  |                | $\sqrt{2}$     |
|                      | <b>Full Dataset</b>            |                |                |                  |                                  | $37.6 \pm 0.4$ |                |                                                                                                                                                                                                                                                                  |                |                |

<span id="page-5-0"></span>Table 1. The test accuracy (%) of dataset distillation methods with various ratios of symmetric (*Sym.*) and asymmetric (*Asym.*) noisy labels. Results (accuracy ± std) are shown for *CIFAR-10*, *CIFAR-100*, and *Tiny ImageNet* datasets at different Image Per Class (IPC).

metric). *Symmetric noise* is generated by uniformly flipping labels in each class to incorrect labels from other classes. *Asymmetric noise* flips the labels within a specific set of classes. For CIFAR-10, labels are flipped as follows: TRUCK  $\rightarrow$  AUTOMOBILE, BIRD  $\rightarrow$  AIRPLANE, DEER  $\rightarrow$  HORSE, CAT  $\rightarrow$  DOG. For CIFAR-100, the 100 classes are grouped into 20 super-classes, and each has 5 sub-classes. Each class is then flipped to the next within

the same super-class. We use noise ratios of 20% and 40% in our experiments to evaluate the effectiveness of our proposed method.

Real-World Datasets. We validate our method on the realworld datasets CIFAR-10N and CIFAR-100N, which contain human-annotated noisy labels obtained through Amazon Mechanical Turk [\(Wei et al.,](#page-9-22) [2022\)](#page-9-22). Our evaluation

primarily considers the CIFAR-10N (Worst) and CIFAR-100N (Fine) label sets, with noise ratios of 40.21% and 40.20%, respectively.

#### 5.2. Experimental Setup

Our method builds upon trajectory matching, and we conduct comprehensive comparisons with several baseline approaches, including MTT [\(Cazenavette et al.,](#page-8-0) [2022\)](#page-8-0), FTD [\(Du et al.,](#page-8-2) [2023\)](#page-8-2), DATM [\(Guo et al.,](#page-8-3) [2024\)](#page-8-3), and ATT [\(Liu et al.,](#page-9-0) [2024\)](#page-9-0). The detailed configurations of hyperparameters in the buffer phase and distillation phase align with the settings described in the corresponding works. Experimental results for additional distillation methods (*e.g*., gradient matching, distribution matching, *etc*.) can be found in the appendix. Without explicit mention, we use ConvNet by default to conduct experiments. Following previous methods [\(Cazenavette et al.,](#page-8-0) [2022;](#page-8-0) [Du et al.,](#page-8-2) [2023;](#page-8-2) [Guo](#page-8-3) [et al.,](#page-8-3) [2024;](#page-8-3) [Liu et al.,](#page-9-0) [2024\)](#page-9-0), we use a 3-layer ConvNet for CIFAR-10 and CIFAR-100, and a 4-layer ConvNet for Tiny ImageNet.

#### 5.3. Results

<span id="page-6-0"></span>Table 2. Comparison of dataset distillation methods on *CIFAR-10N (Worst)* and *CIFAR-100N (Fine)* label sets. Performance results (accuracy ± standard deviation) are shown across different Images Per Class (IPC).

| Dataset / IPC | Method                         | 1              | 10             | 50             |
|---------------|--------------------------------|----------------|----------------|----------------|
|               | MTT (Cazenavette et al., 2022) | $43.8 \pm 0.5$ | $55.7 + 0.5$   | $62.1 \pm 0.4$ |
|               | $+$ TAD                        | $46.6 \pm 0.2$ | $64.4 \pm 0.5$ | $69.9 + 0.2$   |
| CIFAR-10N     | FTD (Du et al., 2023)          | $43.4 \pm 0.6$ | $55.4 \pm 0.6$ | $60.3 + 0.8$   |
|               | $+$ TAD                        | $45.6 \pm 0.4$ | $63.8 + 0.2$   | $69.2 \pm 0.3$ |
|               | ATT (Liu et al., 2024)         | $42.3 \pm 0.7$ | $58.3 \pm 0.4$ | $61.9 \pm 0.2$ |
|               | $+$ TAD                        | $45.9 \pm 0.4$ | $65.7 + 0.2$   | $68.9 + 0.2$   |
|               | MTT (Cazenavette et al., 2022) | $20.2 + 0.4$   | $32.8 + 0.3$   | $38.2 + 0.3$   |
|               | $+$ TAD                        | $20.1 + 0.3$   | $36.4 \pm 0.2$ | $42.1 \pm 0.1$ |
| $CIFAR-100N$  | FTD (Du et al., 2023)          | $20.3 + 0.3$   | $32.7 + 0.2$   | $37.8 \pm 0.2$ |
|               | $+$ TAD                        | $20.7 + 0.3$   | $35.1 \pm 0.2$ | $41.8 \pm 0.1$ |
|               | ATT (Liu et al., 2024)         | $20.1 \pm 0.4$ | $32.0 \pm 0.4$ | $36.3 \pm 0.2$ |
|               | $+$ TAD                        | $21.4 \pm 0.2$ | $35.9 + 0.1$   | $41.2 \pm 0.3$ |

Dataset Distillation on Simulated Datasets. We conduct comprehensive experiments to compare the performance of previous dataset distillation methods with noisy labels. In these experiments, we consider the effects of different noise types and noise ratios on various trajectory matching-based methods at different Images Per Class (IPC) configurations. This evaluation is performed on multiple datasets to ensure a thorough and reliable comparison. As shown in Table [1,](#page-5-0) noisy labels lead to a maximum accuracy drop of over 20% in dataset distillation (*e.g*., for CIFAR-10 with 40% asymmetric noise, FTD and DATM show significant accuracy drops when the IPC is set to 50). In general, dataset distillation methods exhibit poorer performance with asymmetric noisy labels compared to symmetric ones. This is because

<span id="page-6-1"></span>Table 3. The ablation study examines the effectiveness of different components, including consistent regularization (*Reg.*), Outer Loop (*OL.*), and inner loop (*IL.*). Each row represents a different combination of these components with checkmarks. The baseline is DATM [\(Guo et al.,](#page-8-3) [2024\)](#page-8-3). Experiments are conducted on CIFAR-10 with asymmetric noise ratios of 20% and 40%. IPC is set to 50.

| Config | Reg. | OL. | IL.<br>Asym. 20%<br>Asym. 40%         |
|--------|------|-----|---------------------------------------|
| A      |      |     | $62.5 \pm 0.5$<br>$55.0 \pm 0.4$      |
| B      | ✓    |     | $58.8 \pm 0.5$<br>$64.1 \pm 0.3$      |
| C      |      |     | $69.4 \pm 0.5$<br>$65.1 \pm 0.5$      |
| D      |      |     | $70.7 \pm 0.2$<br>$67.3 \pm 0.3$      |
| E      |      |     | $67.6 \pm 0.4$<br>$69.3 \pm 0.5$<br>✓ |
| F      |      |     | $72.4 \pm 0.2$<br>$70.7 \pm 0.3$      |

asymmetric noise flips labels to closely related classes, making it more challenging for the model to distinguish between correct and noisy labels. Additionally, as IPC increases, the influence of noisy labels on dataset distillation grows increasingly significant. Higher IPC requires the student model to align with the later stages of the training trajectory, where noisy labels exert a greater influence. Our method iteratively refines samples to minimize the impact of noise on training trajectories. As a result, our approach achieves significant performance improvements.

In our evaluation of CIFAR-10, CIFAR-100, and Tiny ImageNet, we observe that our method achieves the most noticeable improvement on CIFAR-10. This is because CIFAR-10 has fewer classes (10) and is relatively easy. In contrast, CIFAR-100 (100 classes) and Tiny ImageNet (200 classes) have more classes, which increases the difficulty and complexity. Our method demonstrates significant improvements on CIFAR-100 and Tiny ImageNet. Overall, our method adapts effectively to various levels of dataset complexity and noise, demonstrating its robustness and versatility. More results can be found in the appendix.

Dataset Distillation on Real-world Datasets. To further verify the effectiveness of our method in real-world scenarios with noisy labels, we conduct comparative experiments on CIFAR-10N (Worst) and CIFAR-100N (Fine). The experiments compare different dataset distillation methods, including MTT [\(Cazenavette et al.,](#page-8-0) [2022\)](#page-8-0), FTD [\(Du et al.,](#page-8-2) [2023\)](#page-8-2), and ATT [\(Liu et al.,](#page-9-0) [2024\)](#page-9-0), with and without our proposed method. As shown in Table [2,](#page-6-0) our proposed method (*i.e*., + Ours) generally enhances performance across different distillation methods on both CIFAR-10N (Worst) and CIFAR-100N (Fine) datasets. On CIFAR-10N (Worst), our method significantly improves the accuracy of all baseline methods, especially as IPC increases (*e.g*., IPC=10 and 50). For instance, the accuracy of FTD improves from 60.3% to 69.2% at IPC=50, demonstrating notable enhancement.

| IPC                                 | Noise Ratio   | 10            |               | 50            |          |
|-------------------------------------|---------------|---------------|---------------|---------------|----------|
|                                     |               | Sym. 20%      | Sym. 40%      | Sym. 20%      | Sym. 40% |
| MTT (Cazenavette et al., 2022)      | $36.5  0.2$ | $31.4  0.4$ | $43.8  0.4$ | $37.2  0.4$ |          |
| + C2D (Zheltonozhskii et al., 2022) | $36.3  0.1$ | $35.6  0.5$ | $42.6  0.5$ | $40.6  0.6$ |          |
| + L2B-DivideMix (Zhou et al., 2024) | $37.1  0.3$ | $36.0  0.4$ | $43.9  0.4$ | $41.4  0.5$ |          |
| + TAD                               | $40.0  0.1$ | $39.4  0.1$ | $44.2  0.2$ | $43.2  0.1$ |          |
| FTD (Du et al., 2023)               | $38.9  0.5$ | $33.5  0.4$ | $43.5  0.3$ | $36.9  0.3$ |          |
| + C2D (Zheltonozhskii et al., 2022) | $38.2  0.2$ | $36.7  0.3$ | $42.4  0.2$ | $41.7  0.3$ |          |
| + L2B-DivideMix (Zhou et al., 2024) | $39.0  0.5$ | $38.1  0.4$ | $44.3  0.3$ | $42.2  0.4$ |          |
| + TAD                               | $40.4  0.4$ | $39.4  0.1$ | $45.6  0.1$ | $44.0  0.1$ |          |
| ATT (Liu et al., 2024)              | $37.3  0.4$ | $32.6  0.3$ | $41.9  0.2$ | $37.1  0.2$ |          |
| + C2D (Zheltonozhskii et al., 2022) | $36.9  0.4$ | $35.8  0.2$ | $40.8  0.3$ | $39.8  0.3$ |          |
| + L2B-DivideMix (Zhou et al., 2024) | $38.2  0.2$ | $36.5  0.4$ | $43.1  0.2$ | $41.0  0.3$ |          |
| + TAD                               | $42.8  0.1$ | $41.5  0.2$ | $45.5  0.1$ | $44.2  0.2$ |          |

<span id="page-7-0"></span>Table 4. Comparison with learning from noisy labels methods. Experiments are conducted on CIFAR-100 with symmetric noise.

On CIFAR-100N (Fine), the improvement is consistent, especially at higher IPC. Overall, our method demonstrates robustness and effectiveness in addressing realistic noisy labels.

## 5.4. Ablation Study

We conduct ablation studies to validate the effectiveness of our components. The baseline for comparison is DATM [\(Guo et al.,](#page-8-3) [2024\)](#page-8-3). IPC is set to 50, and evaluations are conducted on CIFAR-10 with asymmetric noise ratios of 20% and 40%. As can be seen in Table [3,](#page-6-1) the consistent regularization term effectively mitigates overfitting to noisy samples, improving performance (*e.g*., Config B, D, and F), particularly at higher noise ratios. The outer loop (*i.e*., Config C) effectively filters out noisy samples. This keeps the expert model focused on accurate representations, minimizing the influence of noise and enabling high-quality distillation. For instance, at a noise ratio of 40%, the outer loop improves the performance of dataset distillation from 55.0% to 65.1%. Additionally, the inner loop effectively corrects noisy labels by leveraging reliable anchors, allowing more data to contribute to the training process. The collaboration of these modules proves effective in addressing dataset distillation with noisy labels, achieving superior performance.

### 5.5. Comparison with Learning from Noisy Labels

To further validate our method, we benchmark it against state-of-the-art LNL (Learning with Noisy Labels) methods, C2D [\(Zheltonozhskii et al.,](#page-10-12) [2022\)](#page-10-12) and L2B-DivideMix [\(Zhou et al.,](#page-10-13) [2024\)](#page-10-13). However, we find that using LNL methods to train the expert model creates a challenge. The student model struggles to align with the

expert trajectory. We speculate that LNL methods rely on semi-supervised learning, while the student model depends solely on CE. This difference causes a divergence in their optimization. To address this, we use a straightforward approach. We apply LNL methods to generate pseudo labels for the training set. Then, we perform dataset distillation directly using these pseudo-labeled data.

For the C2D, the pre-training model uses ResNet50 optimized with SimCLR [\(Chen et al.,](#page-8-19) [2020\)](#page-8-19). Experiments are conducted on CIFAR-100 with symmetric noise. As shown in Table [4,](#page-7-0) our proposed method outperforms LNL methods. Notably, at a noise ratio of 20%, LNL methods provide minimal improvement to dataset distillation performance and may even degrade it (*e.g*., C2D). The reason is that LNL methods cannot assign correct labels to all samples, and noisy samples greatly affect the expert trajectory. Our proposed method, in contrast, is conservative. We model the per-sample loss to distinguish clean and noisy samples. This approach helps us keep a training set with a high proportion of clean samples. Training the expert models exclusively on this clean subset significantly reduces the impact of noisy labels on their training.

# 6. Conclusion

In this work, we propose a Trust-Aware Diversion (TAD) dataset distillation method to tackle the realistic yet challenging task of Dataset Distillation with Noisy Labels (DDNL). TAD introduces an iterative dual-loop (*i.e*., outer loop and inner loop) optimization framework to effectively mitigate the negative impact of mislabeled samples. The outer loop partitions data into trusted and untrusted spaces, ensuring that distillation is guided by reliable samples, while the inner loop recalibrates untrusted samples, transforming them into valuable ones. Through this iterative refinement, TAD expands the trusted space while shrinking the untrusted space, leading to more robust and reliable dataset distillation. Extensive experiments on three benchmark datasets (CIFAR-10, CIFAR-100, and Tiny ImageNet) demonstrate that TAD achieves significant improvements under three noisy labeling settings: symmetric, asymmetric, and real-world noise. These results highlight the importance of handling noisy samples within dataset distillation. We believe this work will open up new avenues for practical and robust dataset distillation with noisy labels, making distilled datasets more applicable to a wide range of real-world applications.

#### Impact Statement

This paper presents work whose goal is to advance the field of Machine Learning. There are many potential societal consequences of our work, but we do not feel that any of them must be specifically highlighted here.

# References

- <span id="page-8-15"></span>Albert, P., Ortego, D., Arazo, E., O'Connor, N. E., and McGuinness, K. Addressing out-of-distribution label noise in webly-labelled data. In *Proceedings of the IEEE/CVF winter conference on applications of computer vision*, pp. 392–401, 2022.
- <span id="page-8-17"></span>Arazo, E., Ortego, D., Albert, P., O'Connor, N., and McGuinness, K. Unsupervised label noise modeling and loss correction. In *International conference on machine learning*, pp. 312–321. PMLR, 2019.
- <span id="page-8-5"></span>Arpit, D., Jastrz˛ebski, S., Ballas, N., Krueger, D., Bengio, E., Kanwal, M. S., Maharaj, T., Fischer, A., Courville, A., Bengio, Y., et al. A closer look at memorization in deep networks. In *International conference on machine learning*, pp. 233–242. PMLR, 2017.
- <span id="page-8-0"></span>Cazenavette, G., Wang, T., Torralba, A., Efros, A. A., and Zhu, J.-Y. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022.
- <span id="page-8-19"></span>Chen, T., Kornblith, S., Norouzi, M., and Hinton, G. A simple framework for contrastive learning of visual representations. In *International conference on machine learning*, pp. 1597–1607. PMLR, 2020.
- <span id="page-8-12"></span>Chung, M.-Y., Chou, S.-Y., Yu, C.-M., Chen, P.-Y., Kuo, S.-Y., and Ho, T.-Y. Rethinking backdoor attacks on dataset distillation: A kernel method perspective. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2024.
- <span id="page-8-21"></span>Colombo, P., Dadalto, E., Staerman, G., Noiry, N., and Piantanida, P. Beyond mahalanobis distance for textual ood detection. *Advances in Neural Information Processing Systems*, 35:17744–17759, 2022.
- <span id="page-8-10"></span>Cui, J., Wang, R., Si, S., and Hsieh, C.-J. Dc-bench: Dataset condensation benchmark. *Advances in Neural Information Processing Systems*, 35:810–822, 2022.
- <span id="page-8-6"></span>De Maesschalck, R., Jouan-Rimbaud, D., and Massart, D. L. The mahalanobis distance. *Chemometrics and intelligent laboratory systems*, 50(1):1–18, 2000.
- <span id="page-8-1"></span>Deng, W., Li, W., Ding, T., Wang, L., Zhang, H., Huang, K., Huo, J., and Gao, Y. Exploiting inter-sample and interfeature relations in dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 17057–17066, 2024.
- <span id="page-8-2"></span>Du, J., Jiang, Y., Tan, V. Y., Zhou, J. T., and Li, H. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 3749– 3758, 2023.

- <span id="page-8-16"></span>Ghosh, A., Kumar, H., and Sastry, P. S. Robust loss functions under label noise for deep neural networks. In *Proceedings of the AAAI conference on artificial intelligence*, volume 31, 2017.
- <span id="page-8-22"></span>Goswami, D., Liu, Y., Twardowski, B., and van de Weijer, J. Fecam: Exploiting the heterogeneity of class distributions in exemplar-free continual learning. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-8-11"></span>Gu, J., Wang, K., Jiang, W., and You, Y. Summarizing stream data for memory-restricted online continual learning. In *Proceedings of the AAAI Conference on Artificial Intelligence (AAAI)*, 2024.
- <span id="page-8-3"></span>Guo, Z., Wang, K., Cazenavette, G., Li, H., Zhang, K., and You, Y. Towards lossless dataset distillation via difficultyaligned trajectory matching. In *The Twelfth International Conference on Learning Representations*, 2024.
- <span id="page-8-14"></span>Han, B., Yao, Q., Yu, X., Niu, G., Xu, M., Hu, W., Tsang, I., and Sugiyama, M. Co-teaching: Robust training of deep neural networks with extremely noisy labels. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-8-18"></span>He, K., Zhang, X., Ren, S., and Sun, J. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 770–778, 2016.
- <span id="page-8-20"></span>He, K., Fan, H., Wu, Y., Xie, S., and Girshick, R. Momentum contrast for unsupervised visual representation learning. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 9729–9738, 2020.
- <span id="page-8-7"></span>Hinton, G. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2015.
- <span id="page-8-4"></span>Karim, N., Rizve, M. N., Rahnavard, N., Mian, A., and Shah, M. Unicon: Combating label noise through uniform selection and contrastive learning. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 9676–9686, 2022.
- <span id="page-8-9"></span>Kim, J.-H., Kim, J., Oh, S. J., Yun, S., Song, H., Jeong, J., Ha, J.-W., and Song, H. O. Dataset condensation via efficient synthetic-data parameterization. In *Proceedings of the International Conference on Machine Learning (ICML)*, pp. 11102–11118, 2022.
- <span id="page-8-8"></span>Lee, S., Chun, S., Jung, S., Yun, S., and Yoon, S. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pp. 12352–12364. PMLR, 2022.
- <span id="page-8-13"></span>Li, G., Togo, R., Ogawa, T., and Haseyama, M. Dataset distillation for medical dataset sharing. In *Proceedings*

*of the AAAI Conference on Artificial Intelligence (AAAI), Workshop*, pp. 1–6, 2023.

- <span id="page-9-15"></span>Li, J., Socher, R., and Hoi, S. C. Dividemix: Learning with noisy labels as semi-supervised learning. In *International Conference on Learning Representations*, 2020.
- <span id="page-9-1"></span>Li, J., Li, G., Liu, F., and Yu, Y. Neighborhood collective estimation for noisy label identification and correction. In *European Conference on Computer Vision*, pp. 128–145. Springer, 2022.
- <span id="page-9-14"></span>Li, Z. and Kainz, B. Image distillation for safe data sharing in histopathology. In *International Conference on Medical Image Computing and Computer-Assisted Intervention (MICCAI)*, 2024.
- <span id="page-9-2"></span>Li, Z., Guo, Z., Zhao, W., Zhang, T., Cheng, Z.-Q., Khaki, S., Zhang, K., Sajed, A., Plataniotis, K. N., Wang, K., et al. Prioritize alignment in dataset distillation. *arXiv preprint arXiv:2408.03360*, 2024.
- <span id="page-9-0"></span>Liu, D., Gu, J., Cao, H., Trinitis, C., and Schulz, M. Dataset distillation by automatic training trajectories. In *European Conference on Computer Vision*. Springer, 2024.
- <span id="page-9-5"></span>Liu, H., Li, Y., Xing, T., Dalal, V., Li, L., He, J., and Wang, H. Dataset distillation via the wasserstein metric. *arXiv preprint arXiv:2311.18531*, 2023.
- <span id="page-9-21"></span>Liu, S., Niles-Weed, J., Razavian, N., and Fernandez-Granda, C. Early-learning regularization prevents memorization of noisy labels. *Advances in neural information processing systems*, 33:20331–20342, 2020.
- <span id="page-9-12"></span>Medvedev, D. and D'yakonov, A. Learning to generate synthetic training data using gradient matching and implicit differentiation. In *Proceedings of the International Conference on Analysis of Images, Social Networks and Texts (AIST)*, pp. 138–150, 2021.
- <span id="page-9-16"></span>Northcutt, C., Jiang, L., and Chuang, I. Confident learning: Estimating uncertainty in dataset labels. *Journal of Artificial Intelligence Research*, 70:1373–1411, 2021.
- <span id="page-9-7"></span>Qin, T., Deng, Z., and Alvarez-Melis, D. A label is worth a thousand images in dataset distillation. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2024.
- <span id="page-9-4"></span>Sajedi, A., Khaki, S., Amjadian, E., Liu, L. Z., Lawryshyn, Y. A., and Plataniotis, K. N. DataDAM: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)*, pp. 17097–17107, 2023.
- <span id="page-9-13"></span>Sangermano, M., Carta, A., Cossu, A., and Bacciu, D. Sample condensation in online continual learning. In

*2022 International Joint Conference on Neural Networks (IJCNN)*, pp. 01–08. IEEE, 2022.

- <span id="page-9-8"></span>Shao, S., Yin, Z., Zhou, M., Zhang, X., and Shen, Z. Generalized large-scale data condensation via various backbone and statistical matching. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pp. 16709–16718, 2024.
- <span id="page-9-11"></span>Son, B., Oh, Y., Baek, D., and Ham, B. FYI: Flip your images for dataset distillation. In *Proceedings of the European Conference on Computer Vision (ECCV)*, 2024.
- <span id="page-9-6"></span>Sucholutsky, I. and Schonlau, M. Soft-label dataset distillation and text dataset distillation. In *Proceedings of the International Joint Conference on Neural Networks (IJCNN)*, pp. 1–8, 2021.
- <span id="page-9-9"></span>Sun, P., Shi, B., Yu, D., and Lin, T. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pp. 9390–9399, 2024.
- <span id="page-9-17"></span>Tanaka, D., Ikami, D., Yamasaki, T., and Aizawa, K. Joint optimization framework for learning with noisy labels. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 5552–5560, 2018.
- <span id="page-9-3"></span>Wang, T., Zhu, J.-Y., Torralba, A., and Efros, A. A. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-9-19"></span>Wei, H., Feng, L., Chen, X., and An, B. Combating noisy labels by agreement: A joint training method with coregularization. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 13726–13735, 2020.
- <span id="page-9-18"></span>Wei, H., Zhuang, H., Xie, R., Feng, L., Niu, G., An, B., and Li, Y. Mitigating memorization of noisy labels by clipping the model prediction. In *International Conference on Machine Learning*, pp. 36868–36886. PMLR, 2023a.
- <span id="page-9-22"></span>Wei, J., Zhu, Z., Cheng, H., Liu, T., Niu, G., and Liu, Y. Learning with noisy labels revisited: A study using realworld human annotations. In *International Conference on Learning Representations*, 2022.
- <span id="page-9-10"></span>Wei, X., Cao, A., Yang, F., and Ma, Z. Sparse parameterization for epitomic dataset distillation. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023b.
- <span id="page-9-20"></span>Xia, X., Liu, T., Han, B., Gong, C., Wang, N., Ge, Z., and Chang, Y. Robust early-learning: Hindering the memorization of noisy labels. In *International conference on learning representations*, 2020.

- <span id="page-10-6"></span>Xiao, L. and He, Y. Are large-scale soft labels necessary for large-scale dataset distillation? In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2024.
- <span id="page-10-8"></span>Yang, E., Shen, L., Wang, Z., Liu, T., and Guo, G. An efficient dataset condensation plugin and its application to continual learning. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-10-11"></span>Yang, M., Huang, Z., Hu, P., Li, T., Lv, J., and Peng, X. Learning with twin noisy labels for visible-infrared person re-identification. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 14308–14317, 2022.
- <span id="page-10-5"></span>Yang, S., Cheng, S., Hong, M., Fan, H., Wei, X., and Liu, S. Neural spectral decomposition for dataset distillation. In *Proceedings of the European Conference on Computer Vision (ECCV)*, 2024.
- <span id="page-10-2"></span>Yao, Q., Yang, H., Han, B., Niu, G., and Kwok, J. T.-Y. Searching to exploit memorization effect in learning with noisy labels. In *International Conference on Machine Learning*, pp. 10789–10798. PMLR, 2020.
- <span id="page-10-9"></span>Yi, K. and Wu, J. Probabilistic end-to-end noise correction for learning with noisy labels. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 7017–7025, 2019.
- <span id="page-10-7"></span>Yin, Z., Xing, E., and Shen, Z. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-10-3"></span>Zhao, B. and Bilen, H. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021a.
- <span id="page-10-0"></span>Zhao, B. and Bilen, H. Dataset condensation with gradient matching. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2021b.
- <span id="page-10-1"></span>Zhao, G., Li, G., Qin, Y., Liu, F., and Yu, Y. Centrality and consistency: two-stage clean samples identification for learning with instance-dependent noisy labels. In *European Conference on Computer Vision*, pp. 21–37. Springer, 2022.
- <span id="page-10-4"></span>Zhao, G., Li, G., Qin, Y., and Yu, Y. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 7856–7865, 2023.
- <span id="page-10-12"></span>Zheltonozhskii, E., Baskin, C., Mendelson, A., Bronstein, A. M., and Litany, O. Contrast to divide: Self-supervised pre-training for learning with noisy labels. In *Proceedings*

*of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pp. 1657–1667, 2022.

- <span id="page-10-10"></span>Zhou, T., Wang, S., and Bilmes, J. Robust curriculum learning: from clean label detection to noisy label selfcorrection. In *International Conference on Learning Representations*, 2020.
- <span id="page-10-13"></span>Zhou, Y., Li, X., Liu, F., Wei, Q., Chen, X., Yu, L., Xie, C., Lungren, M. P., and Xing, L. L2b: Learning to bootstrap robust models for combating label noise. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 23523–23533, 2024.
# Flexible Dataset Distillation: Learn Labels Instead of Images

<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> School of Informatics The University of Edinburgh {ondrej.bohdal, yongxin.yang, t.hospedales}@ed.ac.uk

## Abstract

We study the problem of dataset distillation – creating a small set of synthetic examples capable of training a good model. In particular, we study the problem of *label* distillation – creating synthetic labels for a small set of real images, and show it to be more effective than the prior *image*-based approach to dataset distillation. Methodologically, we introduce a more robust and flexible metalearning algorithm for distillation, as well as an effective first-order strategy based on convex optimization layers. Distilling labels with our new algorithm leads to improved results over prior image-based distillation. More importantly, it leads to clear improvements in flexibility of the distilled dataset in terms of compatibility with off-the-shelf optimizers and diverse neural architectures. Interestingly, label distillation can be applied across datasets, for example enabling learning Japanese character recognition by training only on synthetically labeled English letters.

## 1 Introduction

Distillation is a topical area of neural network research that initially began with the goal of extracting the knowledge of a large pre-trained model and compiling it into a smaller model, while retaining similar performance [\[13\]](#page-9-0). The notion of distillation has since found numerous applications and uses including the possibility of *dataset* distillation [\[38\]](#page-10-0): extracting the knowledge of a large dataset and compiling it into a small set of carefully crafted examples, such that a model trained on the small dataset alone achieves good performance. This is of scientific interest as a tool to study neural network generalisation under small sample conditions. More practically, it has the potential to address the large and growing logistical and energy hurdle of neural network training, if adequate neural networks can be quickly trained on small distilled datasets rather than massive raw datasets.

Nevertheless, progress towards the vision of dataset distillation has been limited as the performance of existing methods [\[38,](#page-10-0) [33\]](#page-10-1) trained from random initialization is far from that of full dataset supervised learning. More fundamentally, existing approaches are relatively *inflexible* in terms of the distilled data being over-fitted to the training conditions under which it was generated. While there is some robustness to the choice of initialization weights [\[38\]](#page-10-0), the distilled dataset is largely specific to the architecture used to train it (preventing its use to accelerate neural architecture search, for example), and must use a highly-customized learner (a specific image visitation sequence, a specific sequence of carefully chosen meta-learned learning rates, and a specific number of learning steps). Altogether these constraints mean that existing distilled datasets are not general purpose enough to be useful in practice, e.g. with off-the-shelf learning algorithms. We propose a more *flexible* approach to dataset distillation underpinned by both algorithmic improvements and changes to the problem definition.

Rather than creating synthetic images [\[38\]](#page-10-0) for arbitrary labels, or a combination of synthetic images and soft labels [\[33\]](#page-10-1), we focus on crafting synthetic labels for arbitrarily chosen standard images. Compared to these prior approaches focused on synthetic images, label distillation benefits from

<span id="page-1-0"></span>Image /page/1/Figure/0 description: This diagram illustrates a machine learning process involving two datasets, EMNIST (Source) and KMNIST (Target). On the left, a Convolutional Neural Network (CNN) processes sample images from EMNIST, labeled 'r' and 'j'. The output is described as 'Synthetic labels', represented by histograms. A blue arrow indicates a 'Copy model' operation from the EMNIST CNN to the KMNIST CNN. A red arrow labeled 'Backprop: Model update' connects the synthetic labels back to the EMNIST CNN, signifying model training. On the right, a similar CNN processes sample images from KMNIST, which appear to be distorted versions of digits. This CNN receives the copied model and is trained using 'True labels' via a red arrow labeled 'Backprop: Label update'.

Figure 1: Label distillation enables training a model that can classify Japanese characters after being trained only on English letters and synthetic labels. Labels are updated only during meta-training, after which a new model is trained, using only the few source examples and their synthetic labels.

exploiting the data statistics of natural images and the lower-dimensionality of labels compared to images as parameters for meta-learning. Practically, this leads to improved performance compared to prior image distillation approaches. As a byproduct, this enables a new kind of cross-dataset knowledge distillation (Figure [1\)](#page-1-0). One can learn solely on a source dataset (such as English characters) with synthetic distilled labels, and apply the learned model to recognise concepts in a disjoint target dataset (such as Japanese characters). Surprisingly, it turns out that models can make progress on learning to recognise Japanese only through exposure to English characters with synthetic labels.

Methodologically, we define a new meta-learning algorithm for distillation that does not require costly evaluation of multiple inner-loop (model-training) steps for each iteration of distillation. More importantly our algorithm leads to a more flexible distilled dataset that is better transferable across optimizers, architectures, learning iterations, etc. Furthermore, where existing dataset distillation algorithms rely on second-order gradients, we introduce an alternative learning strategy based on convex optimization layers that avoids high-order gradients and provides better optimization, thus improving the quality of the distilled dataset.

In summary, we contribute: (1) A dataset distillation method that produces flexible distilled datasets that exhibit transferability across learning algorithms. This brings us one step closer to producing useful general-purpose distilled datasets. (2) Our distilled datasets can be used to train higher performance models than those prior work. (3) We introduce the novel concept of cross-dataset distillation, and demonstrate proofs of concept, such as English→Japanese letter recognition.

# 2 Related work

Dataset distillation Most closely related to our work is Dataset [\[38\]](#page-10-0) and Soft-Label Dataset Distillation [\[33\]](#page-10-1). They focus on distilling a dataset or model [\[25\]](#page-9-1) into a small number of example images, which are then used to train a new model. This can be seen as solving a meta-learning problem with respect to model's training data [\[14\]](#page-9-2). The common approach is to initialise the distilled dataset randomly, use the distilled data to train a model, and then backpropagate through the model and its training updates to take gradient steps on the dataset. Since the 'inner' model training algorithm is gradient-based, this leads to high-order gradients. To make this process tractable, the original Dataset Distillation [\[38\]](#page-10-0) uses only a few gradient steps in its inner loop (as per other famous meta-learners [\[12\]](#page-9-3)). To ensure that sufficient learning progress is made with few updates, it also meta-learns a fixed set of optimal learning rates to use at each step. This balances tractability & efficacy, but causes the distilled dataset to be 'locked in' to the customized optimizer rather than serve as a general purpose dataset, which also prevents its use for NAS [\[32\]](#page-10-2). In this work we define an online meta-learning procedure that simultaneously learns the dataset and the base model. This enables us to tractably take more gradient steps and ultimately produce a performant yet flexible general purpose distilled dataset.

There are various motivations for dataset distillation, but the most practically intriguing is to summarize a dataset in a compressed form to accelerate model training. In this sense it is related to dataset pruning [\[2,](#page-8-0) [11,](#page-9-4) [17\]](#page-9-5), core-set construction [\[36,](#page-10-3) [3,](#page-8-1) [31\]](#page-9-6) and instance selection [\[27\]](#page-9-7) focusing on dataset summarization through a small number of examples. Summarization methods *select* a relatively large part of the data (e.g. at least 10%), while distillation extends down to using 10 images per category

 $(\approx 0.2\%$  of CIFAR-10 data) through example *synthesis*. We keep original data (like summarization methods), but synthesize labels (like distillation). This leads to a surprising observation – it is possible to synthesize labels for a few fixed examples so a model trained on these examples can directly (without any fine-tuning) solve a different problem with a different label space (Figure [1\)](#page-1-0).

Meta-learning Meta-learning algorithms can often be grouped [\[14\]](#page-9-2) into offline approaches (e.g. [\[38,](#page-10-0) [12\]](#page-9-3)) that do inner optimization at each step of outer optimization; and online approaches that solve the base and meta-learning problem simultaneously (e.g. [\[4,](#page-8-2) [20\]](#page-9-8)). Meta-learning relates to hyperparameter optimization, for example [\[24,](#page-9-9) [22\]](#page-9-10) efficiently unroll through many steps of optimization like offline meta-learning, while [\[23\]](#page-9-11) optimize hyperparameters and the base model like online meta-learning. Online approaches are typically faster, but optimize meta-parameters for a single problem. Offline approaches are slower and typically limit the length of the inner optimization for tractability, but can often find meta-parameters that solve a distribution of tasks (as different tasks are drawn in each outer-loop iteration). In dataset distillation, the notion of 'distribution over tasks' corresponds to finding a dataset that can successfully train a network in many settings, such as different initial conditions [\[38\]](#page-10-0). Our distillation algorithm is a novel hybrid of these two families. We efficiently solve the base and meta-tasks simultaneously like online approaches, and so are able to use more inner-loop steps. However, we also learn to solve many 'tasks' by detecting meta-overfitting and sampling a new 'task' when this occurs. This leads to a great combination of efficacy and efficiency.

Finally, most gradient-based meta-learning algorithms rely on costly and often unstable higher-order gradients [\[14,](#page-9-2) [12,](#page-9-3) [38\]](#page-10-0), or else make simple shortest-path first-order approximations [\[26\]](#page-9-12). Instability and large variance of higher-order gradients may make meta-learning less effective [\[21,](#page-9-13) [10\]](#page-9-14), so we found inspiration in recent approaches in few-shot learning [\[5,](#page-8-3) [19\]](#page-9-15) that avoid this issue through the use of convex optimization layers. We introduce the notion of a pseudo-gradient that enables this idea to scale beyond the few-shot setting to general meta-learning problems such as dataset distillation.

# 3 Methods

We aim to meta-learn soft synthetic labels for a small fixed set of real *base* examples that can be used to train a randomly initialized model. This corresponds to an objective such as Eq. [1:](#page-2-0)

<span id="page-2-0"></span>
$$
\tilde{\boldsymbol{Y}}_{\mathcal{S}}^* = \underset{\tilde{\boldsymbol{Y}}_{\mathcal{S}}}{\arg\min} \sum_{\boldsymbol{x}, \boldsymbol{y} \sim \mathcal{T}} L\left(f_{\boldsymbol{\Theta}'}\left(\boldsymbol{x}\right), \boldsymbol{y}\right), \quad \text{with} \quad \boldsymbol{\Theta}' = \boldsymbol{\Theta} - \alpha \nabla_{\boldsymbol{\Theta}} \sum_{\tilde{\boldsymbol{x}}, \tilde{\boldsymbol{y}} \sim \mathcal{S}} L\left(f_{\boldsymbol{\Theta}}\left(\tilde{\boldsymbol{x}}\right), \tilde{\boldsymbol{y}}\right), \quad (1)
$$

where  $\tilde{Y}_{\mathcal{S}}\in\mathbb{R}^{N\times C}$  are the distilled labels for  $N$  base examples  $\tilde{X}_{\mathcal{S}}\in\mathbb{R}^{N\times D}$  (together forming synthetic set S). Each image has dimensionality D, and there are C target classes. Further,  $T$  is the target set with real training data,  $f_{\Theta}(\cdot)$  is a model with parameters  $\Theta$ ,  $\alpha$  is the learning rate and L is the cross-entropy loss. We assume the loss is twice-differentiable, which is true for most current machine learning models and problems. One gradient step is shown above, but in general there may be multiple steps. Cross-entropy loss for predicted soft labels  $\hat{\bm{y}}=f_{\bm{\Theta}'}\left(\bm{x}\right)$  and true one-hot labels  $\bm{y}$ for an example x is defined as  $L(\hat{y}, y) = -\sum_{c=1}^{C} y_c \log(\hat{y}_c)$  (similar for synthetic labels).

One option to achieve objective in Eq. [1](#page-2-0) would be to follow Wang et al. [\[38\]](#page-10-0) and simulate the whole training procedure for multiple gradient steps  $\nabla_{\Theta}$  within the inner loop. However, this requires back-propagating through a long inner loop, and ultimately requires a fixed training schedule with optimized learning rates for strong performance. We aim to produce a dataset that can be used in a standard training pipeline downstream (e.g. Adam optimizer with the default parameters).

Our first modification to the standard pipeline is to perform gradient descent iteratively on the model and the distilled labels, rather than performing many inner (model) steps for each outer (dataset) step. This increases efficiency significantly due to a shorter compute graph for backpropagation. Nevertheless, when there are very few training examples, the model converges quickly to an overfitted local minimum, likely within a few hundred iterations. To manage this, our innovation is to detect overfitting when it occurs, reset the model to a new random initialization and keep training. Specifically, we measure the moving average of target problem accuracy, and when it has not improved for set number of iterations, we reset the model. This periodic reset of the model after varying number of iterations is helpful for learning labels that are useful for all stages of training and thus less sensitive to the number of iterations used. To ensure scalability to any number of examples, we sample a minibatch of base examples and synthetic labels and use those to update the model, which also better aligns with standard training practice. Once label distillation is done, we train a new model from

<span id="page-3-0"></span>Algorithm 1 Label distillation with ridge regression (RR)

| 1: <b>Input:</b> S: synthetic set with N initially unlabelled base examples $X_s$ ; T: labelled target set<br>with real training examples; $\beta$ : step size; $\alpha$ : pseudo-gradient step size; $N_o$ , $N_i$ : outer (resp.<br>inner) loop batch size; C: number of classes in the target set; $\lambda$ : RR regularization parameter<br>2: <b>Output:</b> distilled labels $Y_s$ and a reasonable number of training steps $T_i$<br>3: $Y_{\mathcal{S}} \leftarrow 1/C$<br>4: $\mathbf{\Theta} \sim p(\mathbf{\Theta})$<br>//Randomly initialize feature extractor parameters<br>5: $W \leftarrow 0$<br>//Initialize global RR classifier weights<br>6: while $Y_{\mathcal{S}}$ not converged do<br>$\left(\tilde{\boldsymbol{X}}, \tilde{\boldsymbol{Y}}\right) \sim \mathcal{S} = \left(\tilde{\boldsymbol{X}}_{\mathcal{S}}, \tilde{\boldsymbol{Y}}_{\mathcal{S}}\right).$<br>7:<br>//Sample a minibatch of $N_i$ synthetic examples<br>$(\boldsymbol{X},\boldsymbol{Y})\sim\mathcal{T}$<br>8:<br>//Sample a minibatch of $No$ real training examples<br>Calculate $W_l$ using Eq. 3<br>9:<br>//Calculate current minibatch RR classifier weights<br>$\boldsymbol{W} \leftarrow (1-\alpha)\boldsymbol{W} + \alpha \boldsymbol{W}_l$<br>$\tilde{\boldsymbol{Y}}_{\mathcal{S}} \leftarrow \tilde{\boldsymbol{Y}}_{\mathcal{S}} - \beta \nabla_{\tilde{\boldsymbol{Y}}_{\mathcal{S}}} \sum_{(\boldsymbol{x}, \boldsymbol{y}) \in (\boldsymbol{X}, \boldsymbol{Y})} L\left(f_{\boldsymbol{W} \circ \boldsymbol{\Theta}}\left(\boldsymbol{x}\right), \boldsymbol{y}\right)$<br>$\Theta \leftarrow \Theta - \beta \nabla_{\Theta} \sum_{(\tilde{\boldsymbol{x}}, \tilde{\boldsymbol{y}}) \in (\tilde{\boldsymbol{X}}, \tilde{\boldsymbol{Y}})} L(f_{\boldsymbol{W} \circ \Theta}(\tilde{\boldsymbol{x}}), \tilde{\boldsymbol{y}})$<br>if $\mathbb{E}_{x,y\sim\mathcal{T}}[L(f_{\mathbf{W}\circ\mathbf{\Theta}}(x),y)]$ did not improve then<br>$\mathbf{\Theta} \sim p(\mathbf{\Theta})$<br>$\boldsymbol{W} \leftarrow \boldsymbol{0}$<br>$T_i \leftarrow$ iterations since previous reset<br>end if |  |                                         |
|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|-----------------------------------------|
| 10:<br>11:<br>12:<br>13:<br>14:<br>15:<br>16:<br>17:<br>18: end while                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  | //Uniformly initialize synthetic labels |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  | //Update global RR classifier weights   |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  | //Update synthetic labels               |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  | //Update feature extractor              |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  | //Reset feature extractor               |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  | //Reset global RR classifier weights    |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  | //Record time to overfit                |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |  |                                         |

scratch using random initial weights, given the base examples and learned synthetic labels. Our algorithm returns a reasonable number of training steps  $T_i$ , which allows us to stop training the new model early and prevent over-fitting to the small synthetic dataset.

We propose and evaluate two label distillation algorithms: a second-order version that performs one update step within the inner loop; and a first-order version that uses a closed form solution of ridge regression to find optimal classifier weights for the base examples.

Vanilla second-order version The training includes both inner and outer loop. The inner loop consists of one update of the model parameters  $\vec{\Theta}' = \Theta - \alpha \nabla_{\Theta}\sum_{\tilde{x},\tilde{y}\sim\mathcal{S}} L(f_{\Theta}(\dot{\tilde{x}}), \tilde{y})$ , through which we then backpropagate to update the synthetic labels  $\tilde{\bm{Y}}_{\mathcal{S}} \leftarrow \tilde{\bm{Y}}_{\mathcal{S}} - \beta \nabla_{\tilde{\bm{Y}}_{\mathcal{S}}} \sum_{\bm{x},\bm{y}\sim\mathcal{T}} L\left(f_{\bm{\Theta}'}\left(\bm{x}\right),\bm{y}\right)$ (same notation as in Eq. [1\)](#page-2-0). We do a standard update of the model parameters  $\Theta$  using outer-loop learning rate  $\beta$  after updating the synthetic labels, which could in theory be combined with the inner-loop update. The method is otherwise similar to our first-order ridge regression method, which is summarized in Algorithm [1.](#page-3-0) Real training set examples are used when updating the synthetic labels  $\tilde{\boldsymbol{Y}}_{\mathcal{S}}$ , but for updating the model  $\boldsymbol{\Theta}$  we only use the synthetic labels and the base examples. After each update of the labels, we normalize them to represent a valid probability distribution. This makes them interpretable and has led to improvements compared to unnormalized labels.

Intuition We analysed how the synthetic labels are meta-learned by the second-order algorithm for a simple one-layer model  $\theta$ , with a sigmoid output unit  $\sigma$  and two classes (details are in the supplementary). Considering one example at a time for simplicity with  $(x, y) \sim \mathcal{T}$  and  $(\tilde{x}, \tilde{y}) \sim \mathcal{S}$ , the meta-gradient is  $\nabla_{\tilde{y}} L(\sigma(\theta^{T}\boldsymbol{x}), y) = \alpha (\sigma(\theta^{T}\boldsymbol{x}) - y) \boldsymbol{x}^{T}\tilde{\boldsymbol{x}}$ . This shows the synthetic label is updated proportionally to the similarity of the training set example x and base example  $\tilde{x}$  as well as the difference between the prediction and the true real training set label. Thus synthetic labels capture the different degrees of similarity between a base example and examples from different classes in the target dataset. For example, in cross-dataset, the KMNIST 'Ya'  $\mathbb{X}$  has no corresponding English symbol, but could be learned by partially assigning its label to similar looking English 'X's and 'R's.

First-order version with ridge regression To avoid second-order gradients, we propose a firstorder version that uses pseudo-gradient generated via a closed form solution to ridge regression (RR). We use a RR layer as the final output layer of our base network – we decompose the original model Θ used for the second-order version into feature extractor Θ and RR classifier W. RR layers have previously been used for few-shot learning [\[5\]](#page-8-3) *within* minibatch. We extend it to learn global weights

that persist across minibatches. Local ridge regression problem can be defined and solved as:

$$
W_{l} = \underset{W'}{\arg\min} \left\| \tilde{Z}W' - \tilde{Y} \right\|^{2} + \lambda \left\| W' \right\|^{2}
$$
  
= 
$$
\left( \tilde{Z}^{T} \tilde{Z} + \lambda I \right)^{-1} \tilde{Z}^{T} \tilde{Y},
$$
 (2)

where  $\tilde{Z}=f_{\Theta}\left(\tilde{X}\right)$  are the input embeddings for a minibatch of  $N_i$  base examples and synthetic labels  $\big(\tilde X,\tilde Y\big)\sim \big(\tilde X_\mathcal{S},\tilde Y_\mathcal{S}\big).$   $W_l$  represents the ridge regression weights,  $I$  is the identity matrix and  $\lambda$  is the regularization parameter. Following Bertinetto et al. [\[5\]](#page-8-3), we use Woodbury formula [\[29\]](#page-9-16):

<span id="page-4-0"></span>
$$
\boldsymbol{W}_{l} = \tilde{\boldsymbol{Z}}^{T} \left( \tilde{\boldsymbol{Z}} \tilde{\boldsymbol{Z}}^{T} + \lambda \boldsymbol{I} \right)^{-1} \tilde{\boldsymbol{Y}},
$$
\n(3)

which allows us to use matrix  $\tilde{Z} \tilde{Z}^T$  with dimensionality depending on the square of the number of inputs (minibatch size) rather than the square of the input embedding size. This makes the matrix inversion significantly less costly in practice. While ridge regression is usually oriented at regression problems, it has been shown [\[5\]](#page-8-3) to work well for classification when regressing label vectors.

**Ridge regression with pseudo-gradients** RR solves for the optimal local weights  $W_l$  that classify the features of the current minibatch examples. We exploit this local minibatch solution by taking a pseudo-gradient step that updates global weights  $\bm{W}$  as  $\bm{W} \leftarrow (1-\alpha)\bm{W} + \alpha \bm{W}_l,$  with  $\alpha$  being the pseudo-gradient step size. We can understand this as a pseudo-gradient as it corresponds to the step  $W \leftarrow W - \alpha(W - W_l)$ . We can then update the synthetic labels by back-propagating through local weights  $\boldsymbol{W}_l$ . Subsequent feature extractor updates on  $\boldsymbol{\Theta}$  avoid second-order gradients. The process is summarised in Algorithm [1.](#page-3-0)

# 4 Experiments

We perform two main types of experiments: (1) within-dataset distillation, when the base examples come from the target dataset and (2) cross-dataset distillation, when the base examples come from a different but related dataset. The dataset should be related because if there is a large shift in the domain (e.g. from characters to photos), then the feature extractor trained on the base examples would generalize poorly to the target dataset. We use MNIST, CIFAR-10 and CIFAR-100 for the task of within-dataset distillation, while for cross-dataset distillation we use EMNIST ("English letters"), KMNIST, Kuzushiji-49 (both "Japanese letters"), MNIST (digits), CUB (birds) and CIFAR-10 (general objects). Details of these datasets are in the supplementary.

## 4.1 Experimental settings

**Monitoring overfitting** We use parameters  $N_m$ ,  $N_w$  to say over how many iterations to calculate the moving average and how many iterations to wait before reset since the best moving average value. We select  $N_m = N_w$  and use a value of 50 steps in most cases, while we use 100 for CIFAR-100 and Kuzushiji-49, and 200 for other larger-scale experiments (more than 100 base examples). These parameters do not affect the total number of iterations.

Early stopping for learning synthetic labels We update the synthetic labels for a given number of epochs and then select the best labels to use based on the validation performance. For this, we train a new model from scratch using the current distilled labels and the associated base examples and then evaluate the model on the validation part of the real training set. We randomly set aside about 10-15% (depending on the dataset) of the training data for validation.

Models We use LeNet [\[18\]](#page-9-17) for MNIST and similar experiments, and AlexNet [\[16\]](#page-9-18) for CIFAR-10, CIFAR-100 and CUB. Both models are identical to the ones used in [\[38\]](#page-10-0). In a fully supervised setting they achieve about 99% and 80% test accuracy on MNIST and CIFAR-10.

Selection of base examples The base examples are selected randomly, using a shared random seed for consistency across scenarios. Our baseline models use the same random seed as the distillation models, so they share base examples for fair comparison. For within-dataset label distillation, we create a balanced set of base examples, so each class has the same number of base examples. For

<span id="page-5-0"></span>Table 1: Within-dataset distillation recognition accuracy (%). Our label distillation (LD) outperforms prior dataset distillation (DD) [\[38\]](#page-10-0) and soft-label dataset distillation (SLDD) [\[33\]](#page-10-1), while allowing significantly more flexible training of a new model as shown in Section [4.4.](#page-6-0)

|                 | Base examples        | 10                  | 20                  | 50                  | 100                                    | 200                 | 500                 |
|-----------------|----------------------|---------------------|---------------------|---------------------|----------------------------------------|---------------------|---------------------|
| <b>MNIST</b>    | LD                   | $60.89 	extpm 3.20$ | $74.37 	extpm 1.27$ | $82.26 	extpm 0.88$ | $87.27 	extpm 0.69$                    | $91.47 	extpm 0.53$ | $93.30 	extpm 0.31$ |
|                 | Baseline             | $48.35 	extpm 3.03$ | $62.60 	extpm 3.33$ | $75.07 	extpm 2.40$ | $82.06 	extpm 1.75$                    | $85.95 	extpm 0.98$ | $92.10 	extpm 0.43$ |
|                 | Baseline LS          | $51.22 	extpm 3.18$ | $64.14 	extpm 2.57$ | $77.94 	extpm 1.26$ | $85.95 	extpm 1.09$                    | $90.10 	extpm 0.60$ | $94.75 	extpm 0.29$ |
|                 | LD RR                | $64.57 	extpm 2.67$ | $75.98 	extpm 1.00$ | $82.49 	extpm 0.93$ | $87.85 	extpm 0.43$                    | $88.88 	extpm 0.28$ | $89.91 	extpm 0.33$ |
|                 | Baseline RR          | $52.53 	extpm 2.61$ | $60.44 	extpm 1.97$ | $74.85 	extpm 2.37$ | $81.40 	extpm 2.11$                    | $87.03 	extpm 0.69$ | $92.10 	extpm 0.80$ |
|                 | Baseline RR LS       | $51.53 	extpm 2.42$ | $60.91 	extpm 1.78$ | $76.26 	extpm 1.80$ | $83.13 	extpm 1.41$                    | $87.94 	extpm 0.67$ | $93.48 	extpm 0.61$ |
|                 | DD [38]<br>SLDD [33] |                     |                     |                     | $79.5 	extpm 8.1$<br>$82.7 	extpm 2.8$ |                     |                     |
| <b>CIFAR-10</b> | LD                   | $25.69 	extpm 0.72$ | $30.00 	extpm 0.86$ | $35.36 	extpm 0.64$ | $38.33 	extpm 0.44$                    | $41.05 	extpm 0.71$ | $42.45 	extpm 0.40$ |
|                 | Baseline             | $14.29 	extpm 1.40$ | $16.80 	extpm 0.72$ | $20.75 	extpm 1.05$ | $25.76 	extpm 1.04$                    | $31.53 	extpm 1.02$ | $38.33 	extpm 0.75$ |
|                 | Baseline LS          | $13.22 	extpm 1.22$ | $18.36 	extpm 0.65$ | $22.81 	extpm 0.71$ | $27.27 	extpm 0.68$                    | $33.62 	extpm 0.81$ | $39.22 	extpm 1.12$ |
|                 | LD RR                | $25.07 	extpm 0.69$ | $29.83 	extpm 0.46$ | $35.23 	extpm 0.64$ | $37.94 	extpm 1.22$                    | $41.17 	extpm 0.33$ | $43.16 	extpm 0.47$ |
|                 | Baseline RR          | $13.37 	extpm 0.79$ | $17.08 	extpm 0.31$ | $19.85 	extpm 0.51$ | $24.65 	extpm 0.47$                    | $28.97 	extpm 0.74$ | $36.31 	extpm 0.49$ |
|                 | Baseline RR LS       | $13.82 	extpm 0.85$ | $16.95 	extpm 0.52$ | $20.00 	extpm 0.57$ | $24.84 	extpm 0.60$                    | $29.28 	extpm 0.56$ | $35.73 	extpm 1.02$ |
|                 | DD [38]<br>SLDD [33] |                     |                     |                     | $36.8 	extpm 1.2$<br>$39.8 	extpm 0.8$ |                     |                     |

cross-dataset label distillation, we do not consider the original classes of base examples. The size of the label space and the labels are different in the source and the target problem. Our additional analysis (Tables [6](#page-14-0) and [7](#page-15-0) in the supplementary) has shown that the specific random set of base examples does not have a significant impact on the success of label distillation.

**Further details** Outer-loop minibatch uses  $N<sub>o</sub> = 1024$  examples, while the inner minibatch size  $N_i$  depends on the number of base examples. For 100 or more base examples, we use a minibatch of 50 examples, except for CIFAR-100 for which we use 100 examples. For 10, 20 and 50 base examples our minibatch sizes are 10, 10 and 25. We optimize the synthetic labels and the model using Adam optimizer with standard parameters ( $\beta = 0.001$ ). Most models are trained for 400 epochs, while larger-scale models (more than 100 base examples and CIFAR-100) are trained for 800 epochs. Smaller-scale Kuzushiji-49 experiments are trained for 100 epochs, while larger-scale ones use 200 epochs. Epochs are calculated based on the number of real training set examples, rather than base examples. In the second-order version, we do one inner-loop step update, using a learning rate of  $\alpha = 0.01$ . We back-propagate through the inner-loop update when updating the synthetic labels (meta-knowledge), but not when subsequently updating the model  $\theta$  with Adam optimizer. In the RR version, we use a pseudo-gradient step size  $\alpha$  of 0.01 and regularization parameter  $\lambda$  of 1.0. We calibrate the regression weights by scaling them with a value learned during training with the specific set of base examples and distilled labels. Our tables report the mean test accuracy and standard deviation (%) across 20 models trained from scratch using the base examples and synthetic labels.

## 4.2 Within-dataset distillation

We compare our label distillation (LD) to previous dataset distillation (DD) and soft-label dataset distillation (SLDD) on MNIST and CIFAR-10. We also establish new baselines that take true labels from the target dataset and otherwise are trained in the same way as LD models. RR baselines use RR and pseudo-gradient for consistency with LD RR (overall network architecture remains the same as in the second-order approach). In addition, we include baselines that use label smoothing (LS) [\[35\]](#page-10-4) with a smoothing parameter of 0.1 as suggested in [\[28\]](#page-9-19). The number of training steps for our baselines is optimized using the validation set, by training a model for various numbers of steps between 10 and 1000 and measuring the validation set accuracy (up to 1700 steps are used for cases with more than 100

<span id="page-5-1"></span>Table 2: CIFAR-100 withindataset distillation. One example per class.

| LD                 | $11.46 \pm 0.39$ |
|--------------------|------------------|
| Baseline           | $3.51 \pm 0.31$  |
| <b>Baseline LS</b> | $4.07 + 0.23$    |
| LD RR              | $10.80 + 2.36$   |
| <b>Baseline RR</b> | $3.00 + 0.39$    |
| Baseline RR LS     | $3.65 + 0.28$    |
|                    |                  |

base examples). Table [1](#page-5-0) shows that LD significantly outperforms previous work on MNIST. This is in part due to LD enabling the use of more steps (LD estimates  $T_i \approx 200 - 300$  steps vs fixed 3 epochs of 10 steps in LD and SLDD). Our improved baselines are also competitive, and outperform the prior baselines in [\[38\]](#page-10-0) due to taking more steps. The comparison is reasonable as it is very expensive to

| Base examples                | 10               | 20               | 50               | 100              | 200              | 500              |
|------------------------------|------------------|------------------|------------------|------------------|------------------|------------------|
| $E \rightarrow M(LD)$        | $36.13 + 5.51$   | $57.82 + 1.91$   | $69.21 + 1.82$   | $77.09 + 1.66$   | $83.67 + 1.43$   | $86.02 + 1.00$   |
| $E \rightarrow M$ (LD RR)    | $56.08 + 2.88$   | $67.62 + 3.03$   | $80.80 + 1.44$   | $82.70 + 1.33$   | $84.44 + 1.18$   | $86.79 + 0.76$   |
| $E \rightarrow K(LD)$        | $31.90 \pm 2.82$ | $39.88 \pm 1.98$ | $47.93 + 1.38$   | $51.91 + 0.85$   | $57.46 \pm 1.37$ | $59.84 + 0.80$   |
| $E \rightarrow K$ (LD RR)    | $34.35 + 3.37$   | $46.67 + 1.66$   | $53.13 + 1.88$   | $57.02 + 1.24$   | $58.01 + 1.28$   | $63.77 + 0.71$   |
| $B \rightarrow C (LD)$       | $26.86 \pm 0.97$ | $28.63 \pm 0.86$ | $31.21 + 0.74$   | $34.02 \pm 0.66$ | $38.39 \pm 0.56$ | $38.12 + 0.41$   |
| $B \rightarrow C$ (LD RR)    | $26.50 \pm 0.54$ | $28.95 \pm 0.47$ | $32.23 + 3.59$   | $32.19 + 7.89$   | $36.55 + 6.32$   | $38.46 \pm 6.97$ |
| $E \rightarrow K-49$ (LD)    | $7.37 + 1.01$    | $9.79 + 1.23$    | $17.80 \pm 0.78$ | $19.17 + 1.27$   | $22.78 + 0.98$   | $23.99 + 0.81$   |
| $E \rightarrow K-49$ (LD RR) | $10.48 \pm 1.26$ | $14.84 + 1.83$   | $21.59 + 1.87$   | $20.86 + 1.81$   | $24.59 \pm 2.26$ | $24.72 \pm 1.78$ |

<span id="page-6-1"></span>Table 3: Cross-dataset distillation recognition accuracy  $(\%)$ . Datasets: E = EMNIST, M = MNIST, K  $=$  KMNIST,  $B = CUB$ ,  $C = CIFAR-10$ ,  $K-49 = Kuzushiji-49$ .

use DD and SLDD with many more steps. The standard uniform label smoothing baseline works well on MNIST for a large number of base examples, where the problem anyway approaches one of conventional supervised learning. However, this strategy has not shown to be effective enough for CIFAR-10, where synthetic labels are the best. Importantly, in the most intensively distilled regime of 10 examples, LD clearly outperforms all competitors. We provide an analysis of the labels learned by our method in Section [4.5.](#page-7-0) For CIFAR-10 our results also improve on the original DD result. In this experiment, our second-order algorithm performs similarly to our RR pseudo-gradient strategy.

We further show in Table [2](#page-5-1) that our distillation approach scales to a significantly larger number of classes than 10 by application to the CIFAR-100 benchmark. As before, we establish new baseline results that use the original labels (or their smoother alternatives) of the same images as those used as base examples in distillation. We use the validation set to choose a suitable number of steps for training a baseline, allowing up to 1000 steps, which is significantly more than what is typically used by LD. The results show our distillation method leads to clear improvements over the baseline.

## 4.3 Cross-dataset task

For cross-dataset distillation, we considered four scenarios: from EMNIST letters to MNIST digits, from EMNIST letters ("English") to Kuzushiji-MNIST or Kuzushiji-49 characters ("Japanese") and from CUB bird species to CIFAR-10 general categories. Table [3](#page-6-1) shows we are able to distill labels on examples of a different source dataset and achieve surprisingly good performance on the target problem, given no target data is used when training these models. In contrast, directly applying a trained source-task model to the target without distillation unsurprisingly leads to chance performance (about 2% test accuracy for Kuzushiji-49 and 10% for all other cases). These results show we can indeed distill the knowledge of one dataset into base examples from a different but related dataset through crafting synthetic labels. Furthermore, our RR approach surpasses the second-order method in most cases, confirming its value. When using 10 and 20 base examples for Kuzushiji-49, the number of training examples is smaller than the number of classes (49), providing a novel example of *less-than-one-shot learning* where there are fewer examples than classes [\[34\]](#page-10-5).

<span id="page-6-0"></span>

## 4.4 Flexibility of distilled datasets

We verify the flexibility of our label-distilled dataset compared to image-distilled alternative by Wang et al. [\[38\]](#page-10-0). We look at: (1) How the number of steps used during meta-testing affects the accuracy of learning with distilled data, and in particular sensitivity to deviation from the number of steps used during meta-training of DD and LD. (2) Sensitivity of the models to changes in optimization parameters between meta-training and meta-testing. (3) How well the distilled datasets transfer to architectures different to those used for training. We used the DD implementation of Wang et al. [\[38\]](#page-10-0) for fair evaluation. Figure [2](#page-7-1) summarizes the key points, and detailed tables are in the supplementary.

Sensitivity to the number of meta-testing steps (Figure [2a](#page-7-1)) Our method is relatively insensitive to the number of steps used to train a model on the distilled data. Table [9](#page-15-1) shows that even if we do 50 steps less or 100 steps more than the number estimated during training ( $\approx 300$ ), test accuracy does not change significantly. However, previous DD and SLDD methods need to be trained for a specific number of steps with optimized learning rates. If the number of steps changes even by as little as 20%, they incur a significant cost in accuracy. Table [10](#page-15-2) provides a further sensitivity analysis of DD.

<span id="page-7-1"></span>Image /page/7/Figure/0 description: This image contains three bar charts. Chart (a) titled "Optimization steps" compares MNIST test accuracy (%) for "20% fewer" (blue bars), "Original" (red bars), and "20% more" (green bars) under "LD" and "DD" conditions. For LD, accuracies are 86.77%, 86.77%, and 86.98%. For DD, accuracies are 52.54%, 77.32%, and 53.89%. Chart (b) titled "Optimization parameters" compares MNIST test accuracy (%) for "Original" (blue bars), "General optimizer" (red bars), and "Shuffled examples" (green bars) under "LD" and "DD" conditions. For LD, accuracies are 86.77%, 86.77%, and 86.77%. For DD, accuracies are 77.32%, 50.2%, and 62.38%. Chart (c) titled "Cross-architecture transfer" compares CIFAR-10 test accuracy (%) for "AlexNet" (blue bars) and "LeNet" (red bars) under "LD" and "DD" conditions. For LD, accuracies are 38.39% and 32.65%. For DD, accuracies are 35.2% and 25.92%.

Figure 2: Datasets distilled with LD are more flexible than those distilled with DD.

Sensitivity to optimization parameters (Figure [2b](#page-7-1)) DD uses step-wise meta-optimized learning rates to maximize accuracy. Table [11](#page-15-3) shows using the average of the optimized learning rate rather than the specific value in each step (more general optimizer) leads to a significantly worse result. Original DD also relies on training the distilled data in a fixed sequence, and Table [12](#page-16-0) shows changing the order of examples leads to a large decrease in accuracy. Our LD method by design does not depend on the specific order of examples and can be used with off-the-shelf optimizers such as Adam.

Transferability of labels across architectures (Figure [2c](#page-7-1)) We study the impact of distilling the labels using AlexNet and then training AlexNet, LeNet and ResNet-18 using the distilled labels. Tables [13](#page-16-1) and [14](#page-16-2) suggest our labels are transferable in both within and across dataset distillation scenarios. Table [15](#page-16-3) further shows original DD images are somewhat transferable if using the specific order of examples and optimized learning rates (the reported baseline results are worse). However, the decrease in test accuracy is smaller for our LD, suggesting LD has better transferability.

<span id="page-7-0"></span>

## 4.5 Further analysis

Analysis of synthetic labels We have analysed to what extent the synthetic labels learn the true label (Figures [6](#page-17-0) and [7](#page-18-0) in the supplementary). Our RR method has typically led to more complex labels that recovered the true label to a smaller extent than the second-order version (e.g. 63% vs 84% on the same scenario). For cross-dataset LD, Figure [9](#page-20-0) suggests LD can be understood as learning labels such that the base examples' label-weighted combination resembles the images from the target class. Example synthetic labels are included in the supplementary.

Pseudo-gradient analysis Our pseudo-gradient RR method obtains a significantly lower variance of meta-knowledge gradients than the second-order method, as shown in Figure [3.](#page-7-2) This leads to more stable and effective training.

Discussion LD provides a more effective and more flexible distillation approach than prior work. This brings us one step closer to the vision of leveraging distilled data to accelerate model training, or design – such as architecture search [\[9\]](#page-9-20). Currently we only explicitly randomize over network initializations during training. In future work we believe our strategy of multistep training and reset at convergence could be used with other factors, such as randomly selected network architectures to further improve cross-network generalisation performance.

# 5 Conclusion

We have introduced a new label distillation algorithm for distilling the knowledge of a large dataset into synthetic labels of a few base examples from the same or a different dataset. Our

<span id="page-7-2"></span>Image /page/7/Figure/10 description: A line graph plots the logarithm of the variance of the gradient against the number of steps. The x-axis ranges from 0 to 1000 steps, with labels at 0, 200, 400, 600, 800, and 1000. The y-axis ranges from -24 to -10, labeled as "log(Var(gradient))". Two lines are plotted: a blue line labeled "Pseudo-gradient RR" and a green line labeled "Second order". The green line starts at approximately -24 and increases to around -12, showing periodic sharp drops around steps 200, 400, 600, and 800. The blue line starts at approximately -24 and increases to around -16, also showing periodic sharp drops around the same step intervals, but generally staying below the green line.

Second-order

Figure 3: RR-based LD reduces synthetic label meta-gradient variance Var  $[\nabla_{\tilde{\boldsymbol{Y}}_{\mathcal{S}}}L].$ 

method improves on prior dataset distillation results, scales well to larger problems, and enables novel settings such as cross-dataset distillation. Most importantly, it is significantly more flexible in terms of distilling general purpose datasets that can be used downstream with off-the-shelf optimizers.

# Broader Impact

We propose a flexible and efficient distillation scheme that gets us closer to the goal of practically useful dataset distillation. Dataset distillation could ultimately lead to a beneficial impact in terms of researcher's time efficiency by enabling faster experimentation when training on small distilled datasets. Perhaps more importantly, it could reduce the environmental impact of AI research and development by reduction of energy costs [\[30\]](#page-9-21). However, our results are still not strong enough yet. For this goal to be realised better distillation methods leading to more accurate downstream models need to be developed.

In the future, label distillation could speculatively provide a useful tool for privacy preserving learning [\[1\]](#page-8-4), for example in situations where organizations want to learn from user's data. A company could provide a small set of public (source) data to a user, who performs cross-dataset distillation using their private (target) data to train a model. The user would then return the distilled labels on the public data, which would allow the company to re-create the user's model. In this way the knowledge from the user's training could be obtained by the company in the form of distilled labels – without directly sending any private data, or trained model that could be a vector for memorization attacks [\[6\]](#page-8-5).

On the negative side, detecting and understanding the impact of bias in datasets is an important yet already very challenging issue for machine learning. The impact of dataset distillation on any underlying biases in the data is completely unclear. If people were to train models on distilled datasets in the future, it would be important to understand the impact of distillation on data biases.

## Source Code

We provide a PyTorch implementation of our approach at [https://github.com/ondrejbohdal/](https://github.com/ondrejbohdal/label-distillation) [label-distillation](https://github.com/ondrejbohdal/label-distillation).

# Acknowledgments and Disclosure of Funding

This work was supported in part by the EPSRC Centre for Doctoral Training in Data Science, funded by the UK Engineering and Physical Sciences Research Council (grant EP/L016427/1) and the University of Edinburgh.

## References

- <span id="page-8-4"></span>[1] Al-Rubaie, M. and Chang, J. M. (2019). Privacy-preserving machine learning: threats and solutions. *IEEE Security and Privacy*, 17(2):49–58.
- <span id="page-8-0"></span>[2] Angelova, A., Abu-Mostafa, Y., and Perona, P. (2005). Pruning training sets for learning of object categories. In *CVPR*.
- <span id="page-8-1"></span>[3] Bachem, O., Lucic, M., and Krause, A. (2017). Practical coreset constructions for machine learning. In *arXiv*.
- <span id="page-8-2"></span>[4] Balaji, Y., Sankaranarayanan, S., and Chellappa, R. (2018). MetaReg: towards domain generalization using meta-regularization. In *NeurIPS*.
- <span id="page-8-3"></span>[5] Bertinetto, L., Henriques, J., Torr, P. H. S., and Vedaldi, A. (2019). Meta-learning with differentiable closed-form solvers. In *ICLR*.
- <span id="page-8-5"></span>[6] Carlini, N., Liu, C., Erlingsson, U., Kos, J., and Song, D. (2019). The secret sharer: evaluating and testing unintended memorization in neural networks. In *USENIX Security Symposium*.
- <span id="page-8-7"></span>[7] Clanuwat, T., Kitamoto, A., Lamb, A., Yamamoto, K., and Ha, D. (2018). Deep learning for classical Japanese literature. In *NeurIPS Workshop on Machine Learning for Creativity and Design*.
- <span id="page-8-6"></span>[8] Cohen, G., Afshar, S., Tapson, J., and van Schaik, A. (2017). EMNIST: an extension of MNIST to handwritten letters. In *arXiv*.

- <span id="page-9-20"></span>[9] Elsken, T., Metzen, J. H., and Hutter, F. (2019). Neural architecture search: a survey. *Journal of Machine Learning Research*, 20:1–21.
- <span id="page-9-14"></span>[10] Farquhar, G., Whiteson, S., and Foerster, J. (2019). Loaded DiCE: Trading off Bias and Variance in Any-Order Score Function Estimators for Reinforcement Learning. In *NeurIPS*.
- <span id="page-9-4"></span>[11] Felzenszwalb, P. F., Girshick, R. B., McAllester, D., and Ramanan, D. (2010). Object detection with discriminatively trained part-based models. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 32(9):1627–1645.
- <span id="page-9-3"></span>[12] Finn, C., Abbeel, P., and Levine, S. (2017). Model-agnostic meta-learning for fast adaptation of deep networks. In *ICML*.
- <span id="page-9-0"></span>[13] Hinton, G., Vinyals, O., and Dean, J. (2014). Distilling the knowledge in a neural network. In *NIPS*.
- <span id="page-9-2"></span>[14] Hospedales, T., Antoniou, A., Micaelli, P., and Storkey, A. (2020). Meta-learning in neural networks: a survey. In *arXiv*.
- <span id="page-9-22"></span>[15] Krizhevsky, A. (2009). Learning multiple layers of features from tiny images. Technical report.
- <span id="page-9-18"></span>[16] Krizhevsky, A., Sutskever, I., and Hinton, G. E. (2012). ImageNet classification with deep convolutional neural networks. In *NIPS*.
- <span id="page-9-5"></span>[17] Lapedriza, A., Pirsiavash, H., Bylinskii, Z., and Torralba, A. (2013). Are all training examples equally valuable? In *arXiv*.
- <span id="page-9-17"></span>[18] LeCun, Y., Bottou, L., Bengio, Y., and Haffner, P. (1998). Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324.
- <span id="page-9-15"></span>[19] Lee, K., Maji, S., Ravichandran, A., and Soatto, S. (2019). Meta-learning with differentiable convex optimization. In *CVPR*.
- <span id="page-9-8"></span>[20] Li, Y., Yang, Y., Zhou, W., and Hospedales, T. M. (2019). Feature-critic networks for heterogeneous domain generalization. In *ICML*.
- <span id="page-9-13"></span>[21] Liu, H., Socher, R., and Xiong, C. (2019). Taming MAML: Efficient Unbiased Meta-Reinforcement Learning. In *ICML*.
- <span id="page-9-10"></span>[22] Lorraine, J., Vicol, P., and Duvenaud, D. (2020). Optimizing Millions of Hyperparameters by Implicit Differentiation. In *AISTATS*.
- <span id="page-9-11"></span>[23] Luketina, J., Berglund, M., Klaus Greff, A., and Raiko, T. (2016). Scalable Gradient-Based Tuning of Continuous Regularization Hyperparameters. In *ICML*.
- <span id="page-9-9"></span>[24] Maclaurin, D., Duvenaud, D., and Adams, R. P. (2015). Gradient-based Hyperparameter Optimization through Reversible Learning. In *ICML*.
- <span id="page-9-1"></span>[25] Micaelli, P. and Storkey, A. (2019). Zero-shot knowledge transfer via adversarial belief matching. In *NeurIPS*.
- <span id="page-9-12"></span>[26] Nichol, A., Achiam, J., and Schulman, J. (2018). On first-order meta-learning algorithms. In *arXiv*.
- <span id="page-9-7"></span>[27] Olvera-López, J. A., Carrasco-Ochoa, J. A., Martínez-Trinidad, J. F., and Kittler, J. (2010). A review of instance selection methods. *Artificial Intelligence Review*, 34(2):133–143.
- <span id="page-9-19"></span>[28] Pereyra, G., Tucker, G., Chorowski, J., Kaiser, L., and Hinton, G. (2017). Regularizing neural networks by penalizing confident output distributions. In *arXiv*.
- <span id="page-9-16"></span>[29] Petersen, K. B. and Pedersen, M. S. (2012). The matrix cookbook. Technical report.
- <span id="page-9-21"></span>[30] Schwartz, R., Dodge, J., Smith, N. A., and Etzioni, O. (2019). Green AI. In *arXiv*.
- <span id="page-9-6"></span>[31] Sener, O. and Savarese, S. (2018). Active learning for convolutional neural networks: A core-set approach. In *ICLR*.

- <span id="page-10-2"></span>[32] Shleifer, S. and Prokop, E. (2019). Proxy Datasets for Training Convolutional Neural Networks. In *arXiv*.
- <span id="page-10-1"></span>[33] Sucholutsky, I. and Schonlau, M. (2019). Soft-label dataset distillation and text dataset distillation. In *arXiv*.
- <span id="page-10-5"></span>[34] Sucholutsky, I. and Schonlau, M. (2020). 'Less Than One'-Shot Learning: Learning N Classes From M<N Samples. In *arXiv*.
- <span id="page-10-4"></span>[35] Szegedy, C., Vanhoucke, V., Ioffe, S., and Shlens, J. (2016). Rethinking the Inception architecture for computer vision. In *CVPR*.
- <span id="page-10-3"></span>[36] Tsang, I. W., Kwok, J. T., and Cheung, P.-M. (2005). Core vector machines: fast SVM training on very large data sets. *Journal of Machine Learning Research*, 6:363–392.
- <span id="page-10-6"></span>[37] Wah, C., Branson, S., Welinder, P., Perona, P., and Belongie, S. (2011). The Caltech-UCSD Birds-200-2011 dataset. Technical report.
- <span id="page-10-0"></span>[38] Wang, T., Zhu, J.-Y., Torralba, A., and Efros, A. A. (2018). Dataset distillation. In *arXiv*.

# A Datasets

We use MNIST [\[18\]](#page-9-17), EMNIST [\[8\]](#page-8-6), KMNIST and Kuzushiji-49 [\[7\]](#page-8-7), CIFAR-10 and CIFAR-100 [\[15\]](#page-9-22), and CUB [\[37\]](#page-10-6) datasets. Example images are shown in Figure [4.](#page-11-0) MNIST includes images of 70000 handwritten digits that belong into 10 classes. EMNIST dataset includes various characters, but we choose EMNIST letters split that includes only letters. Lowercase and uppercase letters are combined together into 26 balanced classes (145600 examples in total). KMNIST (Kuzushiji-MNIST) is a dataset that includes images of 10 classes of cursive Japanese (*Kuzushiji*) characters and is of the same size as MNIST. Kuzushiji-49 is a larger version of KMNIST with 270912 examples and 49 classes. CIFAR-10 includes 60000 colour images of various general objects, for example airplanes, frogs or ships. As the name indicates, there are 10 classes. CIFAR-100 is like CIFAR-100, but has 100 classes with 600 images for each of them. Every class belongs to one of 20 superclasses which represent more general concepts. CUB includes colour images of 200 bird species. The number of images is small, only 11788. All datasets except Kuzushiji-49 are balanced or almost balanced.

<span id="page-11-0"></span>Image /page/11/Figure/2 description: The image displays a grid of images categorized by dataset. The datasets listed are MNIST, EMNIST, Kuzushiji, CIFAR-10, and CUB. Each row corresponds to a dataset and shows five example images. The MNIST row shows handwritten digits 9, 5, 2, 3, and 4. The EMNIST row shows handwritten characters C, J, h, K, and F. The Kuzushiji row shows handwritten characters that appear to be Japanese calligraphy. The CIFAR-10 row shows a silver SUV, a frog, an airplane, a white boat, and a yellow airplane. The CUB row shows a bird perched on a finger, a bird on a branch, a bird on a sandy surface, a bird on a branch, and a bird on a leafy surface.

Figure 4: Example images from the different datasets that we use.

## B Analysis of simple one-layer case

In this section we analyse how synthetic labels are meta-learned in the case of a simple one-layer model with sigmoid output layer  $\sigma$ , second-order approach and binary classification problem. We will consider one example at a time for simplicity. The model has weights  $\theta$  and gives prediction  $\hat{y} = \sigma(\boldsymbol{\theta}^T \boldsymbol{x})$  for input image  $\boldsymbol{x}$  with true label y. We use binary cross-entropy loss L:

$$
L(\hat{y}, y) = -y \log \hat{y} - (1 - y) \log (1 - \hat{y}).
$$

As part of the algorithm, we first update the base model, using the current base example and synthetic label:

$$
\boldsymbol{\theta}' = \boldsymbol{\theta} - \alpha \nabla_{\boldsymbol{\theta}} L\left(\sigma(\boldsymbol{\theta}^T \tilde{\boldsymbol{x}}), \tilde{y}\right),
$$

after which we update the synthetic label:

$$
\tilde{y} \leftarrow \tilde{y} - \beta \nabla_{\tilde{y}} L\left(\sigma(\boldsymbol{\theta}^{\prime T} \boldsymbol{x}), y\right).
$$

Notation:  $\tilde{x}$  is the base example,  $\tilde{y}$  is the synthetic label,  $\alpha$  is the inner-loop learning rate,  $\beta$  is the outer-loop learning rate, x is an example from the target set, y is the label of the example and  $\theta$ describes the model weights.

Our goal is to intuitively interpret the update of the synthetic label, which uses the gradient  $\nabla_{\tilde{y}} L(\sigma(\boldsymbol{\theta}^{\prime T} \boldsymbol{x}), y)$ . We will repeatedly use the chain rule and the fact that

$$
\frac{\partial \sigma(x)}{\partial x} = \sigma(x) (1 - \sigma(x)).
$$

Moreover, we will use the following result (for binary cross-entropy loss  $L$  introduced earlier):

$$
\frac{\partial L(\sigma(\boldsymbol{\theta}^{T}\boldsymbol{x}),y)}{\partial \boldsymbol{\theta}} = \frac{\partial L(\sigma(\boldsymbol{\theta}^{T}\boldsymbol{x}),y)}{\partial \sigma(\boldsymbol{\theta}^{T}\boldsymbol{x})} \frac{\partial \sigma(\boldsymbol{\theta}^{T}\boldsymbol{x})}{\partial \boldsymbol{\theta}} \\ = (\sigma(\boldsymbol{\theta}^{T}\boldsymbol{x}) - y) \boldsymbol{x}
$$

Now we derive an intuitive formula for the gradient used for updating the synthetic label:

$$
\frac{\partial L(\sigma(\theta^{T}x), y)}{\partial \tilde{y}} = \frac{\partial L(\hat{y}', y)}{\partial \tilde{y}}
$$

$$
= \left(\frac{\partial L(\hat{y}', y)}{\partial \hat{y}'} \frac{\partial \hat{y}'}{\partial \theta'}\right)^{T} \frac{\partial \theta'}{\partial \tilde{y}}
$$

$$
= \left(\frac{\partial L(\hat{y}', y)}{\partial \hat{y}'} \frac{\partial \hat{y}'}{\partial \theta'}\right)^{T} \frac{\partial (\theta - \alpha \nabla_{\theta} L(\sigma(\theta^{T}\tilde{x}), \tilde{y}))}{\partial \tilde{y}}
$$

$$
= \left(\frac{\partial L(\hat{y}', y)}{\partial \hat{y}'} \frac{\partial \hat{y}'}{\partial \theta'}\right)^{T} \frac{\partial (\theta - \alpha (\sigma(\theta^{T}\tilde{x}) - \tilde{y}) \tilde{x})}{\partial \tilde{y}}
$$

$$
= \left(\frac{\partial L(\hat{y}', y)}{\partial \hat{y}'} \frac{\partial \hat{y}'}{\partial \theta'}\right)^{T} (\alpha \tilde{x}) = ((\hat{y}' - y) x)^{T} (\alpha \tilde{x})
$$

$$
= \alpha (\sigma(\theta'^{T}x) - y) x^{T} \tilde{x}
$$

The next step is to interpret the update rule. The update is proportional to the difference between the prediction on the real training set and the true label  $(\sigma(\theta^{T}x) - y)$  as well as to the similarity between the real training set example and the base example  $(x^T \tilde{x})$ . This suggests the synthetic labels are updated so that they capture the different amount of similarity of a base example to examples from different classes in the target dataset. A similar analysis can also be done for our RR method – in such case the result would be similar and would include a further proportionality constant dependent on the base examples (not affecting the intuitive interpretation).

## C Additional experimental details

Normalization We normalize greyscale images using the standardly used normalization for MNIST (mean of 0.1307 and standard deviation of 0.3081). All our greyscale images are of size  $28 \times 28$ . Colour images are normalized using CIFAR-10 normalization (means of about 0.4914, 0.4822, 0.4465, and standard deviations of about 0.247, 0.243, 0.261 across channels). All colour images are reshaped to be of size  $32 \times 32$ .

Computational resources Each experiment was done on a single GPU, in almost all cases NVIDIA 2080 Ti. Shorter (400 epochs) experiments took about 1 or 2 hours to run, while longer (800 epochs) experiments took between 2 and 4 hours.

Training time In Table [4](#page-12-0) we compare training times of our framework and the original DD (using the same settings and hardware). Besides evaluating LD, we also use our meta-learning algorithm to implement an image-distillation strategy for direct comparison with the original DD. The results show our online approach significantly accelerates training. Our DD is comparable to LD, and both are faster than original DD. However, we

<span id="page-12-0"></span>Table 4: Comparison of training times of DD [\[38\]](#page-10-0) and our LD (mins).

|             | <b>MNIST</b> | <b>CIFAR-10</b> |
|-------------|--------------|-----------------|
| DD          | 116          | 205             |
| LD          | 61           | 86              |
| LD (RR)     | 65           | 98              |
| Our DD      | 67           | 96              |
| Our DD (RR) | 72           | 90              |

focus on LD because our version of DD was relatively unstable

(Table [17\)](#page-17-1) and led to worse performance than LD, perhaps because learning synthetic images is more complex than synthetic labels. This shows we need both the labels and our re-initializing strategy.

<span id="page-13-0"></span>In addition, Figure [5](#page-13-0) illustrates the difference between a standard model used for second-order label distillation and a model that uses global ridge regression classifier weights (used for first-order RR label distillation). The two models are almost identical – only the final linear layer is different.

Image /page/13/Figure/3 description: This image is a flowchart comparing a standard model with a model that uses ridge regression weights. Both models start with an 'Input' layer, followed by a 'Feature extractor with CNN layers'. The standard model then proceeds through two 'Linear layer' and 'ReLU non-linearity' pairs, ending with a 'Linear layer' before the 'Prediction'. The model with ridge regression weights follows a similar path but replaces the final 'Linear layer' with 'Global RR classifier weights' before the 'Prediction'.

Figure 5: Comparison of a standard model used for second-order label distillation and a model that uses global ridge regression classifier weights (used for first-order RR label distillation).

# D Additional experiments

Stability and dependence on choice of base examples To evaluate the consistency of our results, we repeat the entire pipeline and report the results in Table [5.](#page-14-1) In the previous experiments, we used one randomly chosen but fixed set of base examples per source task. We investigate the impact of base example choice by drawing further random base example sets. The results in Table [6](#page-14-0) suggest that the impact of base example choice is slightly larger than that of the variability due to the distillation process, but still small overall. Note that the  $\pm$  standard deviations in all cases quantify the impact of retraining from different random initializations at meta-test, given a fixed base set and completed distillation. It is likely that if the base examples were not selected randomly, the impact of using specific base examples would be larger. In fact, future work could investigate how to choose base examples so that learning synthetic labels for them improves the result further. We have tried the following strategy, but the label distillation results remained similar to the previous results:

- Try 50 randomly selected sets of examples, train a model with each three times (for robustness) and measure the validation accuracy.
- The validation accuracy is measured for various numbers of steps, in most cases we evaluate every 50 steps up to 1000 steps (or 1700 steps when there are more than 100 base examples).
- Select the set with the largest mean validation accuracy at any point of training (across the three runs).

• This strategy maximizes the performance of the baselines, but could potentially also help the label distillation since these examples could be generally better for training.

The results for this strategy are in Table [7.](#page-15-0)

Dependence on target dataset size Our experiments use a relatively large target dataset (about 50000 examples) for meta-learning. We study the impact of reducing the amount of target data for distillation in Table [8.](#page-15-4) Using 5000 or more examples (about 10% of the original size) is enough to achieve comparable performance.

**Transferability of RR synthetic labels to standard model training** When using RR, we train a validation and test model with RR and global classifier weights obtained using pseudo-gradient. In this experiment we study what happens if we create synthetic labels with RR, but do validation and testing with standard models trained from scratch without RR. For a fair comparison, we use the same synthetic labels for training a new RR model and a new standard model. Validation for early stopping is done with a standardly trained model. The results in Table [16](#page-17-2) suggest RR labels are largely transferable (even in cross-dataset scenarios), but there is some decrease in performance. Consequently, it is better to learn the synthetic labels using second-order approach if we want to train a standard model without RR during testing (comparing with the results in Table [1,](#page-5-0) [2](#page-5-1) and [3\)](#page-6-1).

Intuition on cross-dataset distillation. To illustrate the mechanism behind cross-dataset distillation, we use the distilled labels to linearly combine base EMNIST example images weighted by their learned synthetic labels in order to estimate a prototypical KMNIST/MNIST target class example as implied by learned LD labels. Although the actual mechanism is more complex than this due to the non-linearity of the neural network, we can qualitatively see individual KMNIST/MNIST target classes are approximately encoded by their linear EMNIST LD prototypes as shown in Figure [9.](#page-20-0)

## E Results of analysis

Our tables report the mean test accuracy and standard deviation (%) across 20 models trained from scratch using the base examples and synthetic labels. When analysing the original DD, 200 randomly initialized models are used.

<span id="page-14-1"></span>Table 5: Repeatability. Label distillation is quite repeatable. Performance change from repeating the whole distillation learning and subsequent re-training is small. We used 100 base examples for these experiments. Datasets:  $E = EMNIST$ ,  $M = MNIST$ .

|                           | Trial 1          | Trial 2          | Trial 3          |
|---------------------------|------------------|------------------|------------------|
| MNIST (LD)                | $87.27 \pm 0.69$ | $87.49 \pm 0.44$ | $86.77 \pm 0.77$ |
| MNIST (LD RR)             | $87.85 \pm 0.43$ | $88.31 \pm 0.44$ | $88.07 \pm 0.46$ |
| $E \rightarrow M(LD)$     | $77.09 \pm 1.66$ | $76.81 \pm 1.47$ | $77.10 \pm 1.74$ |
| $E \rightarrow M$ (LD RR) | $82.70 \pm 1.33$ | $83.06 \pm 1.43$ | $81.46 \pm 1.70$ |

<span id="page-14-0"></span>Table 6: Base example sensitivity. Label distillation has some sensitivity to the specific set of base examples (chosen by a specific random seed), but the sensitivity is relatively low. We used 100 base examples for these experiments. It is likely that label distillation would be more sensitive for a smaller number of base examples.

|                           | Set 1                             | Set 2                                                                                | Set 3 | Set 4                                              | Set 5 |
|---------------------------|-----------------------------------|--------------------------------------------------------------------------------------|-------|----------------------------------------------------|-------|
| MNIST (LD)                |                                   | $84.91 \pm 0.92$ $87.38 \pm 0.81$                                                    |       | $87.49 + 0.44$ $87.12 + 0.47$ $85.16 + 0.48$       |       |
| MNIST (LD RR)             |                                   | $87.82 \pm 0.60$ $88.78 \pm 0.57$ $88.31 \pm 0.44$ $88.40 \pm 0.46$ $87.77 \pm 0.60$ |       |                                                    |       |
| $E \rightarrow M(LD)$     | $79.34 \pm 1.36$ $74.55 \pm 1.00$ |                                                                                      |       | $76.81 \pm 1.47$ $78.59 \pm 1.05$ $78.55 \pm 1.32$ |       |
| $E \rightarrow M$ (LD RR) |                                   | $81.67 \pm 1.39$ $83.30 \pm 1.38$ $83.06 \pm 1.43$ $82.62 \pm 1.70$ $83.43 \pm 0.98$ |       |                                                    |       |

<span id="page-15-0"></span>Table 7: Optimized base examples: within-dataset distillation recognition accuracy (%). Our label distillation (LD) outperforms prior Dataset Distillation [\[38\]](#page-10-0) (DD) and SLDD [\[33\]](#page-10-1), and scales to synthesizing more examples. The LD results remained similar to the original results even with optimized base examples.

|                 | Base examples      |                             |                             |                             |                             |                             |                             |
|-----------------|--------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|
|                 |                    | 10                          | 20                          | 50                          | 100                         | 200                         | 500                         |
| <b>MNIST</b>    | LD                 | $66.96 	ext{ 	extpm } 2.01$ | $74.37 	ext{ 	extpm } 1.65$ | $83.17 	ext{ 	extpm } 1.28$ | $86.66 	ext{ 	extpm } 0.44$ | $90.75 	ext{ 	extpm } 0.49$ | $93.22 	ext{ 	extpm } 0.41$ |
|                 | <b>Baseline</b>    | $56.60 	ext{ 	extpm } 3.10$ | $64.77 	ext{ 	extpm } 1.90$ | $77.33 	ext{ 	extpm } 2.51$ | $84.86 	ext{ 	extpm } 1.16$ | $88.33 	ext{ 	extpm } 1.04$ | $92.87 	ext{ 	extpm } 0.67$ |
|                 | Baseline LS        | $60.44 	ext{ 	extpm } 2.05$ | $66.41 	ext{ 	extpm } 2.14$ | $80.54 	ext{ 	extpm } 1.94$ | $86.98 	ext{ 	extpm } 0.99$ | $91.12 	ext{ 	extpm } 0.79$ | $95.56 	ext{ 	extpm } 0.18$ |
|                 | LD RR              | $71.34 	ext{ 	extpm } 2.19$ | $73.34 	ext{ 	extpm } 1.18$ | $84.66 	ext{ 	extpm } 0.89$ | $88.30 	ext{ 	extpm } 0.46$ | $88.91 	ext{ 	extpm } 0.36$ | $89.73 	ext{ 	extpm } 0.39$ |
|                 | <b>Baseline RR</b> | $59.20 	ext{ 	extpm } 2.18$ | $65.22 	ext{ 	extpm } 2.29$ | $77.34 	ext{ 	extpm } 1.68$ | $84.70 	ext{ 	extpm } 0.81$ | $87.87 	ext{ 	extpm } 0.68$ | $92.39 	ext{ 	extpm } 0.53$ |
|                 | Baseline RR LS     | $60.63 	ext{ 	extpm } 1.64$ | $65.61 	ext{ 	extpm } 1.08$ | $77.39 	ext{ 	extpm } 1.67$ | $85.63 	ext{ 	extpm } 0.95$ | $88.89 	ext{ 	extpm } 0.88$ | $94.33 	ext{ 	extpm } 0.44$ |
|                 | DD                 |                             |                             |                             | $79.5 	ext{ 	extpm } 8.1$   |                             |                             |
|                 | <b>SLDD</b>        |                             |                             |                             | $82.7 	ext{ 	extpm } 2.8$   |                             |                             |
| <b>CIFAR-10</b> | LD                 | $26.65 	ext{ 	extpm } 0.94$ | $29.07 	ext{ 	extpm } 0.62$ | $35.03 	ext{ 	extpm } 0.48$ | $38.17 	ext{ 	extpm } 0.36$ | $42.12 	ext{ 	extpm } 0.56$ | $41.90 	ext{ 	extpm } 0.28$ |
|                 | <b>Baseline</b>    | $17.57 	ext{ 	extpm } 1.63$ | $21.66 	ext{ 	extpm } 0.91$ | $23.59 	ext{ 	extpm } 0.80$ | $27.79 	ext{ 	extpm } 1.01$ | $33.49 	ext{ 	extpm } 0.77$ | $40.44 	ext{ 	extpm } 1.33$ |
|                 | <b>Baseline LS</b> | $18.57 	ext{ 	extpm } 0.68$ | $22.91 	ext{ 	extpm } 0.70$ | $24.57 	ext{ 	extpm } 0.83$ | $29.27 	ext{ 	extpm } 0.85$ | $34.83 	ext{ 	extpm } 0.75$ | $40.15 	ext{ 	extpm } 0.66$ |
|                 | LD RR              | $25.08 	ext{ 	extpm } 0.39$ | $28.17 	ext{ 	extpm } 0.34$ | $34.43 	ext{ 	extpm } 0.38$ | $37.59 	ext{ 	extpm } 1.68$ | $42.48 	ext{ 	extpm } 0.25$ | $44.81 	ext{ 	extpm } 0.26$ |
|                 | <b>Baseline RR</b> | $18.42 	ext{ 	extpm } 0.59$ | $21.00 	ext{ 	extpm } 0.73$ | $22.45 	ext{ 	extpm } 0.49$ | $24.46 	ext{ 	extpm } 1.67$ | $30.96 	ext{ 	extpm } 0.49$ | $39.17 	ext{ 	extpm } 0.47$ |
|                 | Baseline RR LS     | $18.22 	ext{ 	extpm } 0.67$ | $22.31 	ext{ 	extpm } 1.01$ | $22.27 	ext{ 	extpm } 0.75$ | $24.84 	ext{ 	extpm } 2.89$ | $30.74 	ext{ 	extpm } 0.80$ | $38.86 	ext{ 	extpm } 0.88$ |
|                 | DD                 |                             |                             |                             | $36.8 	ext{ 	extpm } 1.2$   |                             |                             |
|                 | <b>SLDD</b>        |                             |                             |                             | $39.8 	ext{ 	extpm } 0.8$   |                             |                             |

<span id="page-15-4"></span>Table 8: Dependence on real training set size. Around 5000 examples ( $\approx 10\%$  of all data) is sufficient. Similarly as before, we used 100 base examples. Using all examples means using 50000 examples.

| Target examples | 100            | 500            | 1000           | 5000           | 10000          | 20000          | All            |
|-----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
| E → M (LD)      | $50.70 ± 2.33$ | $61.92 ± 3.62$ | $57.39 ± 4.58$ | $75.44 ± 1.60$ | $76.79 ± 1.12$ | $77.27 ± 1.25$ | $77.09 ± 1.66$ |
| E → M (LD RR)   | $60.67 ± 3.17$ | $72.09 ± 2.40$ | $65.71 ± 3.77$ | $76.83 ± 2.33$ | $80.66 ± 1.97$ | $82.44 ± 1.64$ | $82.70 ± 1.33$ |

<span id="page-15-1"></span>Table 9: Sensitivity to number of training steps at meta-testing. We re-train the model with different numbers of steps than estimated during meta-training. The results show our method is relatively insensitive to the number of steps. The default number of steps  $T_i$  (+0 column) was estimated as 278 for MNIST (LD), 217 for MNIST (LD RR), 364 steps for  $E \rightarrow M$  (LD) and 311 steps for  $E \rightarrow M$ (LD RR). Scenario with 100 base examples is reported.

| Steps deviation           | $-50$          | $-20$                                                                                                                                   | - 10 | $+()$                                                                                                    | $+10$ | $+20$ | $+50$ | $+100$ |
|---------------------------|----------------|-----------------------------------------------------------------------------------------------------------------------------------------|------|----------------------------------------------------------------------------------------------------------|-------|-------|-------|--------|
| MNIST (LD)                | $86.67 + 0.51$ | $86.91 + 0.49$                                                                                                                          |      | $86.88 \pm 0.50$ $86.77 \pm 0.77$ $86.54 \pm 0.68$ $87.05 \pm 0.67$ $86.98 \pm 0.70$ $86.59 \pm 0.63$    |       |       |       |        |
| MNIST (LD RR)             |                | $88.21 + 0.50$ $88.03 + 0.51$                                                                                                           |      | $88.32 \pm 0.50$ $88.07 \pm 0.46$ $88.10 \pm 0.38$ $87.95 \pm 0.45$ $87.98 \pm 0.45$ $87.74 \pm 0.62$    |       |       |       |        |
| $E \rightarrow M(LD)$     | $77.28 + 1.01$ |                                                                                                                                         |      | $76.88 + 1.97$ $77.26 + 1.92$ $77.10 + 1.74$ $76.40 + 2.37$ $76.76 + 2.18$ $77.80 + 1.49$ $77.81 + 1.23$ |       |       |       |        |
| $E \rightarrow M$ (LD RR) |                | $81.84 \pm 1.75$ $81.64 \pm 1.76$ $81.45 \pm 2.01$ $81.46 \pm 1.70$ $81.55 \pm 1.80$ $80.96 \pm 1.74$ $81.34 \pm 1.64$ $80.73 \pm 2.45$ |      |                                                                                                          |       |       |       |        |

<span id="page-15-2"></span>Table 10: Sensitivity of original DD to number of steps. DD is very sensitive to using the specific number of steps. We take the first N steps, keep their original learning rates, and assign learning rates of 0 to the remaining steps. When we do 5 more steps than the original (30), we perform the final 5 steps with an average learning rate.

| <b>Steps</b> | 10 | 20                                                                                                                                                                                                               | 30 |  |
|--------------|----|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----|--|
| <b>MNIST</b> |    | $35.65 + 11.19$ $43.25 + 11.47$ $54.85 + 12.28$ $52.54 + 11.40$ $77.32 + 5.08$ $53.89 + 10.51$<br>CIFAR-10 21.14 $\pm$ 4.08 22.50 $\pm$ 4.24 27.08 $\pm$ 3.22 28.54 $\pm$ 2.41 35.20 $\pm$ 1.09 28.80 $\pm$ 3.33 |    |  |

<span id="page-15-3"></span>Table 11: Sensitivity of original DD to learning rates. DD is sensitive to using the specific learning rates.

| Learning rate | Optimized        | Average of optimized |
|---------------|------------------|----------------------|
| <b>MNIST</b>  | $77.32 \pm 5.08$ | $62.38 \pm 13.11$    |
| $CIFAR-10$    | $35.20 \pm 1.09$ | $30.59 + 3.95$       |

<span id="page-16-0"></span>Table 12: Sensitivity of original DD to order of examples. DD is sensitive to using the specific order of training examples.

| Order        | Original         |                   | Shuffled within epoch Shuffled across epochs |
|--------------|------------------|-------------------|----------------------------------------------|
| <b>MNIST</b> | $77.32 \pm 5.08$ | $50.20 \pm 12.83$ | $62.67 \pm 10.82$                            |
| CIFAR-10     | $35.20 \pm 1.09$ | $24.65 \pm 2.16$  | $22.59 \pm 3.23$                             |

<span id="page-16-1"></span>Table 13: Transferability of distilled labels across different architectures (second-order method). The upper part of the table shows performance of various test models when trained on distilled labels synthetised with AlexNet only. The middle part shows the baseline performance of training models with different architectures on true labels. The lower part shows that distilled labels work even in cross-dataset scenario (labels trained with AlexNet only). The results clearly suggest the distilled labels generalize across different architectures.

| Base examples      | 10               | 20               | 50               | 100              | 200              | 500              |
|--------------------|------------------|------------------|------------------|------------------|------------------|------------------|
| $CIFAR-10LD$       |                  |                  |                  |                  |                  |                  |
| AlexNet            | $26.09 \pm 0.58$ | $30.41 \pm 0.81$ | $35.21 \pm 0.50$ | $38.39 + 0.62$   | $40.98 \pm 0.50$ | $42.78 + 0.29$   |
| LeNet              | $19.33 \pm 2.50$ | $24.17 \pm 1.42$ | $28.14 \pm 1.37$ | $32.65 \pm 1.22$ | $36.60 \pm 1.46$ | $39.67 \pm 0.85$ |
| ResNet-18          | $17.97 \pm 1.23$ | $24.64 \pm 0.92$ | $27.36 + 1.07$   | $31.01 \pm 0.84$ | $35.33 \pm 0.97$ | $39.32 + 0.61$   |
| CIFAR-10 baseline  |                  |                  |                  |                  |                  |                  |
| AlexNet            | $14.35 \pm 1.39$ | $16.72 + 0.76$   | $21.08 \pm 0.93$ | $25.39 \pm 0.86$ | $31.39 \pm 1.12$ | $37.17 + 1.58$   |
| LeNet              | $13.20 \pm 1.86$ | $15.31 \pm 1.09$ | $18.15 + 0.82$   | $21.63 + 1.45$   | $25.87 \pm 1.26$ | $32.99 \pm 0.86$ |
| ResNet-18          | $13.80 \pm 1.19$ | $18.29 \pm 1.43$ | $20.56 \pm 0.75$ | $23.44 + 1.04$   | $28.98 \pm 1.05$ | $33.16 \pm 1.12$ |
| CUB to CIFAR-10 LD |                  |                  |                  |                  |                  |                  |
| AlexNet            | $25.95 \pm 0.90$ | $27.73 + 1.09$   | $31.00 + 0.78$   | $34.99 \pm 0.69$ | $37.83 + 0.65$   | $39.44 + 0.53$   |
| LeNet              | $20.18 \pm 1.56$ | $23.15 \pm 1.72$ | $26.16 \pm 1.55$ | $28.73 + 1.44$   | $30.71 \pm 1.80$ | $35.41 \pm 0.88$ |
| ResNet-18          | $17.12 \pm 1.32$ | $17.80 \pm 1.26$ | $21.22 \pm 1.04$ | $23.38 \pm 0.95$ | $23.39 \pm 0.90$ | $26.71 + 0.89$   |

<span id="page-16-2"></span>Table 14: Transferability of distilled labels across different architectures (RR method). The upper part of the table shows performance of various test models when trained on distilled labels synthetised with AlexNet only. The middle part shows the baseline performance of training models with different architectures on true labels. The lower part shows that distilled labels work even in cross-dataset scenario (labels trained with AlexNet only). The results clearly suggest the distilled labels generalize across different architectures. Note that lower RR results for ResNet-18 may be caused by significantly lower dimensionality of the RR layer (64 features + 1 for bias), while AlexNet and LeNet have 192 features + 1 for bias in the RR layer.

| Base examples      | 10               | 20               | 50               | 100              | 200              | 500              |
|--------------------|------------------|------------------|------------------|------------------|------------------|------------------|
| $CIFAR-10LD$       |                  |                  |                  |                  |                  |                  |
| AlexNet            | $26.78 \pm 0.84$ | $29.51 + 0.41$   | $34.71 + 0.45$   | $38.29 \pm 0.92$ | $41.14 \pm 0.37$ | $42.71 + 0.27$   |
| LeNet              | $20.24 \pm 2.06$ | $23.58 \pm 1.62$ | $28.05 \pm 1.66$ | $30.64 \pm 1.47$ | $34.52 + 1.16$   | $38.75 \pm 0.96$ |
| ResNet-18          | $16.63 \pm 0.88$ | $17.98 \pm 1.38$ | $23.03 \pm 1.21$ | $26.66 \pm 0.73$ | $31.08 \pm 0.92$ | $36.37 + 0.81$   |
| CIFAR-10 baseline  |                  |                  |                  |                  |                  |                  |
| AlexNet            | $13.37 + 0.75$   | $17.20 + 0.50$   | $19.07 \pm 0.75$ | $24.72 \pm 0.53$ | $29.94 + 0.65$   | $36.20 \pm 0.97$ |
| LeNet              | $12.33 \pm 0.88$ | $14.28 \pm 0.74$ | $17.31 + 0.97$   | $20.61 \pm 1.13$ | $24.15 \pm 0.98$ | $28.92 + 0.80$   |
| ResNet-18          | $14.04 + 1.20$   | $16.60 + 1.25$   | $18.75 + 1.17$   | $22.61 + 1.03$   | $28.03 + 0.72$   | $33.72 + 1.74$   |
| CUB to CIFAR-10 LD |                  |                  |                  |                  |                  |                  |
| AlexNet            | $26.08 + 1.14$   | $29.37 \pm 0.36$ | $31.46 + 3.94$   | $35.74 + 0.81$   | $37.26 \pm 1.63$ | $40.94 + 4.61$   |
| LeNet              | $22.69 + 2.09$   | $24.42 + 1.53$   | $26.35 \pm 1.26$ | $29.84 + 1.36$   | $31.68 \pm 1.09$ | $35.66 \pm 1.98$ |
| ResNet-18          | $16.23 \pm 1.44$ | $17.44 + 0.84$   | $20.06 + 0.74$   | $23.48 \pm 1.00$ | $21.31 + 0.82$   | $29.86 + 0.93$   |

<span id="page-16-3"></span>Table 15: Sensitivity of original DD to a change in architecture (trained with AlexNet). The same order of examples used as during training, with the optimized learning rates. We have not been able to easily integrate ResNet-18 to the implementation provided by the authors [\[38\]](#page-10-0).

| AlexNet                                    | LeNet |  |  |
|--------------------------------------------|-------|--|--|
| CIFAR-10 $35.20 \pm 1.09$ $25.92 \pm 2.35$ |       |  |  |

|                      | RR model         | Standard model   |  |  |  |
|----------------------|------------------|------------------|--|--|--|
| <b>MNIST</b>         | $88.25 \pm 0.37$ | $87.07 \pm 0.64$ |  |  |  |
| $CIFAR-10$           | $38.40 \pm 0.41$ | $37.16 \pm 0.59$ |  |  |  |
| $CIFAR-100$          | $10.96 \pm 1.08$ | $8.93 \pm 0.27$  |  |  |  |
| $E \rightarrow M$    | $80.09 \pm 1.84$ | $76.04 \pm 2.29$ |  |  |  |
| $E \rightarrow K$    | $58.14 \pm 0.91$ | $50.37 \pm 2.75$ |  |  |  |
| $B \to C$            | $34.81 \pm 6.46$ | $35.76 \pm 0.54$ |  |  |  |
| $E \rightarrow K-49$ | $17.56 + 1.62$   | $13.28 + 1.62$   |  |  |  |

<span id="page-17-2"></span>Table 16: Transferability of RR synthetic labels to standard model training (both within-dataset and cross-dataset scenarios evaluated). 100 base examples used. Datasets:  $E = EMNIST$ ,  $M = MNIST$ ,  $K=$  KMNIST,  $B = CUB$ ,  $C = CIFAR-10$ ,  $K-49 = Kuzushiji-49$ .

<span id="page-17-1"></span>Table 17: Dataset distillation of images. The results show we have not obtained strong and stable results when distilling synthetic images rather than labels (likely because of the complexity of the flexible task). It may be possible to obtain better results on distilling images with our approach, but it likely requires a lot more tuning than we have done.

| Base examples                    | 10                                   | 20                                 | 50                                | 100                                                 |
|----------------------------------|--------------------------------------|------------------------------------|-----------------------------------|-----------------------------------------------------|
| MNIST (DD)                       | $55.48 \pm 4.42$                     | $17.90 \pm 4.54$                   | $34.40 \pm 4.49$                  | $34.44 \pm 6.15$                                    |
| MNIST (DD RR)<br>$CIFAR-10 (DD)$ | $22.33 \pm 2.75$<br>$12.99 \pm 1.41$ | $49.30 + 2.72$<br>$12.09 \pm 0.99$ | $30.16 \pm 5.32$                  | $36.43 + 5.50$<br>$16.01 \pm 1.48$ $17.92 \pm 1.71$ |
| $CIFAR-10$ (DD RR)               | $20.20 \pm 0.38$                     | $22.71 \pm 0.79$                   | $26.04 \pm 1.22$ $27.05 \pm 1.86$ |                                                     |

<span id="page-17-0"></span>Image /page/17/Figure/4 description: The image displays two heatmaps side-by-side, titled "Second-order" and "RR". Both heatmaps have axes labeled "Base example hard label" on the y-axis and "Distilled soft-label vector" on the x-axis, ranging from 0 to 9. A color bar on the right indicates values from 0.0 to 1.0, with darker green representing higher values. The "Second-order" heatmap shows various numerical values within its cells, with notable high values like 0.45 at (1,1), 0.28 at (0,9), 0.27 at (6,6), and 0.35 at (8,8). The "RR" heatmap also contains numerical values, with high values such as 0.39 at (1,1), 0.37 at (8,8), and 0.31 at (9,9). The overall pattern suggests a comparison of distributions between the two methods.

Figure 6: Distribution of label values across base example hard labels and distilled soft-label vectors. Within-dataset CIFAR-10 scenario with 100 base examples is shown. We can see that to a certain extent the original classes are recovered, but a lot of non-trivial information is added that presumably leads to strong improvements over a baseline with true or smooth labels. Numbers are shown when the values are at least 0.05.

<span id="page-18-0"></span>Image /page/18/Figure/0 description: This figure displays two sets of confusion matrices, one labeled "Second-order" and the other "RR". Both matrices are heatmaps showing the distribution of label values across base example hard labels and distilled soft-label vectors. The matrices are square, with dimensions 10x10, and are colored using a gradient from dark green (high values) to light green (low values). The color bar on the right indicates values ranging from 0.0 to 1.0. The "Second-order" matrix shows values such as 0.87, 0.98, 0.94, 0.76, 0.91, 0.68, 0.09, 0.07, 0.78, 0.77, 0.95, 0.15, 0.14, 0.11, and 0.74. The "RR" matrix shows values such as 0.65, 0.77, 0.71, 0.05, 0.06, 0.07, 0.58, 0.05, 0.07, 0.05, 0.11, 0.05, 0.09, 0.05, 0.08, 0.06, 0.07, 0.67, 0.56, 0.12, 0.13, 0.1, 0.05, and 0.59. Below these matrices are two larger, 25x25 matrices, also presented as heatmaps with a similar color gradient and color bar. These larger matrices represent the same distribution but for a larger set of labels. The y-axis for these larger matrices is labeled "Base example hard label" and ranges from 0 to 25. The x-axis for both sets of matrices is labeled "Distilled soft-label vector" and ranges from 0 to 9. The caption below the figure reads "Figure 7: Distribution of label values across base example hard labels and distilled soft-label vectors."

Figure 7: Distribution of label values across base example hard labels and distilled soft-label vectors. The upper row shows the mean distilled labels for different original classes for within-dataset MNIST scenario with 100 base examples. We can see that to a large extent the original labels are preserved with an additional noise on visually similar classes such as 4 and 9. At the same time, some nontrivial information is learned, especially for our RR method. The lower row shows the mean distilled labels for different original EMNIST ("English") classes used to recognize KMNIST ("Japanese") characters. 100 base examples scenario. Numbers are shown when the values are at least 0.05.

# Second-order RR

|               | [0.00, 0.00, 0.01, 0.99, 0.00, |                              |                               | [0.00, 0.01, 0.01, 0.76, 0.00,               |                                              |  |
|---------------|--------------------------------|------------------------------|-------------------------------|----------------------------------------------|----------------------------------------------|--|
|               |                                |                              | 0.01, 0.00, 0.00, 0.00, 0.00] | 0.00, 0.00, 0.20, 0.01, 0.00]                |                                              |  |
| 9             | [0.00, 0.00, 0.00, 0.00, 0.02, |                              |                               | [0.00, 0.00, 0.00, 0.00, 0.36,               |                                              |  |
|               |                                |                              | 0.00, 0.00, 0.02, 0.00, 0.95] | 0.00, 0.00, 0.32, 0.06, 0.26                 |                                              |  |
|               | [0.00, 0.93, 0.00, 0.00, 0.00, |                              |                               | [0.00, 0.73, 0.00, 0.05, 0.02,               |                                              |  |
|               |                                |                              | 0.00, 0.00, 0.07, 0.00, 0.00] | 0.00, 0.01, 0.07, 0.04, 0.09                 |                                              |  |
|               | [0.00, 0.00, 0.02, 0.00, 0.00, |                              |                               | [0.00, 0.00, 0.13, 0.01, 0.01,               |                                              |  |
|               |                                |                              | 0.00, 0.00, 0.00, 0.97, 0.00] | 0.14, 0.17, 0.00, 0.54, 0.00]                |                                              |  |
| $\frac{8}{7}$ | [0.00, 0.00, 0.00, 0.00, 0.00, |                              |                               | [0.19, 0.07, 0.00, 0.00, 0.00,               |                                              |  |
|               |                                |                              | 0.00, 0.00, 0.99, 0.00, 0.00] | $0.05$ , $0.06$ , $0.56$ , $0.00$ , $0.06$ ] |                                              |  |
| p             | [0.35, 0.00, 0.02, 0.02, 0.03, |                              |                               | [0.26, 0.00, 0.01, 0.09, 0.04,               |                                              |  |
|               | 0.01, 0.00, 0.18, 0.04, 0.34]  |                              |                               | 0.00, 0.02, 0.19, 0.12, 0.27                 |                                              |  |
|               | [0.00, 0.42, 0.03, 0.01, 0.14, |                              |                               | [0.00, 0.23, 0.10, 0.00, 0.19,               |                                              |  |
|               |                                |                              | 0.01, 0.31, 0.00, 0.06, 0.03] | 0.02, 0.20, 0.01, 0.12, 0.13                 |                                              |  |
| نلا<br>م      | [0.00, 0.00, 0.08, 0.01, 0.00, |                              |                               | [0.01, 0.00, 0.07, 0.08, 0.00,               |                                              |  |
|               | 0.01, 0.00, 0.79, 0.00, 0.11   |                              |                               | 0.01, 0.29, 0.49, 0.00, 0.05                 |                                              |  |
| $\mathsf{x}$  | [0.00, 0.00, 0.00, 0.00, 0.00, |                              |                               | [0.06, 0.13, 0.22, 0.00, 0.03,               |                                              |  |
|               |                                |                              | 0.00, 0.00, 0.02, 0.01, 0.97  | 0.00, 0.00, 0.04, 0.19, 0.32]                |                                              |  |
| F             | [0.00, 0.01, 0.17, 0.29, 0.21, |                              |                               | [0.00, 0.11, 0.16, 0.16, 0.11,               |                                              |  |
|               |                                |                              | 0.01, 0.14, 0.16, 0.00, 0.01  | 0.00, 0.21, 0.22, 0.00, 0.04                 |                                              |  |
|               | [0.03, 0.47, 0.04, 0.03, 0.03, |                              |                               | [0.00, 0.48, 0.07, 0.01, 0.02,               |                                              |  |
|               |                                |                              | 0.04, 0.03, 0.02, 0.11, 0.21  | 0.11, 0.00, 0.01, 0.14, 0.17                 |                                              |  |
|               | [0.00, 0.01, 0.31, 0.00, 0.29, |                              |                               | [0.00, 0.04, 0.25, 0.05, 0.19,               |                                              |  |
|               |                                |                              | 0.01, 0.34, 0.03, 0.00, 0.01] | 0.03, 0.38, 0.06, 0.01, 0.00]                |                                              |  |
|               | [0.61, 0.02, 0.04, 0.00, 0.01, |                              |                               | [0.42, 0.09, 0.10, 0.01, 0.01,               |                                              |  |
|               | 0.00, 0.00, 0.00, 0.32, 0.00]  |                              |                               | 0.00, 0.00, 0.00, 0.35, 0.02]                |                                              |  |
|               | [0.03, 0.01, 0.25, 0.07, 0.24, |                              |                               | [0.00, 0.02, 0.15, 0.08, 0.10,               |                                              |  |
|               |                                |                              | 0.15, 0.10, 0.14, 0.00, 0.01] | 0.18, 0.16, 0.27, 0.00, 0.03                 |                                              |  |
|               | [0.01, 0.05, 0.02, 0.27, 0.07, |                              |                               | [0.03, 0.01, 0.08, 0.25, 0.12,               |                                              |  |
|               | 0.35, 0.10, 0.08, 0.02, 0.03   |                              |                               | 0.31, 0.02, 0.16, 0.02, 0.00]                |                                              |  |
|               | [0.04, 0.37, 0.01, 0.04, 0.01, |                              |                               | [0.03, 0.15, 0.04, 0.11, 0.04,               |                                              |  |
|               | 0.05, 0.07, 0.03, 0.11, 0.28   |                              |                               | 0.11, 0.11, 0.05, 0.16, 0.20                 |                                              |  |
|               | [0.01, 0.00, 0.24, 0.06, 0.32, |                              |                               | [0.04, 0.03, 0.16, 0.10, 0.23,               |                                              |  |
|               |                                |                              | 0.08, 0.13, 0.15, 0.00, 0.00] | 0.07, 0.11, 0.22, 0.02, 0.02]                |                                              |  |
|               | [0.18, 0.12, 0.08, 0.08, 0.06, |                              |                               | [0.20, 0.06, 0.12, 0.12, 0.06,               |                                              |  |
|               |                                |                              | 0.07, 0.03, 0.06, 0.17, 0.14  | 0.09, 0.05, 0.07, 0.13, 0.10                 |                                              |  |
|               | [0.00, 0.02, 0.13, 0.18, 0.13, |                              |                               | [0.02, 0.04, 0.18, 0.14, 0.10,               |                                              |  |
|               |                                | 0.17, 0.18, 0.18, 0.00, 0.01 |                               | 0.13, 0.19, 0.18, 0.00, 0.02                 |                                              |  |
|               |                                |                              |                               |                                              |                                              |  |
|               | [0.00, 0.01, 0.06, 0.09, 0.04, |                              | 0.20, 0.06, 0.52, 0.00, 0.01  | [0.06, 0.10, 0.12, 0.10, 0.09,               | $0.12$ , $0.05$ , $0.26$ , $0.00$ , $0.10$ ] |  |

Figure 8: Examples of distilled labels for both second-order and RR label distillation. Scenarios: 1) within-dataset MNIST, 2) cross-dataset EMNIST ("English") source to KMNIST ("Japanese") target, 3) within-dataset CIFAR-10, 4) cross-dataset CUB (birds) source to CIFAR-10 target. Five base examples from each scenario are shown in the order described.

<span id="page-20-0"></span>Image /page/20/Picture/0 description: The image displays two grids of handwritten characters, labeled (a) KMNIST and (b) MNIST. Each grid contains 5 rows and 5 columns of characters. The KMNIST grid shows Japanese hiragana characters, while the MNIST grid shows Arabic numerals. The characters in both grids appear to be generated or reconstructed, with some blurriness and variations in style. The overall presentation is a comparison of character datasets, likely for machine learning or pattern recognition purposes.

Figure 9: Cross-dataset task: reconstructed images from KMNIST (Japanese letters) and MNIST (digits) based on combination of EMNIST base examples (English letters) (100 base examples used). Each row corresponds to a separate class, while the leftmost column shows the reconstructed image and the other three columns show actual examples from the same class. One image is reconstructed for each target dataset class. The base example images are combined pixel-wise with proportions based on the element of the synthetic label vector corresponding to the class that is being reconstructed. Note that KMNIST images in the same class can look very different because of different ways of writing the character, which makes reconstruction more challenging. Some of the reconstructed images resemble images from the target dataset classes, which shows that LD learns labels that combine base examples so that their pixel-wise combination, weighted based on the synthetic class labels, looks similar to the target class images.
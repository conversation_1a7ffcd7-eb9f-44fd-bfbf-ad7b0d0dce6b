# <span id="page-0-0"></span>Reducing catastrophic forgetting with learning on synthetic data

Wojciech Masarczyk Institute of Theoretical and Applied Informatics, Polish Academy of Sciences Tooploox

<EMAIL>

Ivona Tautkute Polish-Japanese Academy of Information Technology Tooploox

<EMAIL>

## Abstract

*Catastrophic forgetting is a problem caused by neural networks' inability to learn data in sequence. After learning two tasks in sequence, performance on the first one drops significantly. This is a serious disadvantage that prevents many deep learning applications to real-life problems where not all object classes are known beforehand; or change in data requires adjustments to the model. To reduce this problem we investigate the use of synthetic data, namely we answer a question: Is it possible to generate such data synthetically which learned in sequence does not result in catastrophic forgetting? We propose a method to generate such data in two-step optimisation process via meta-gradients. Our experimental results on Split-MNIST dataset show that training a model on such synthetic data in sequence does not result in catastrophic forgetting. We also show that our method of generating data is robust to different learning scenarios.*

## 1. Introduction

Deep learning methods have succeeded in many different domains such as: scene understanding, image generation, natural language processing [\[2,](#page-4-0) [27,](#page-5-0) [25,](#page-5-1) [19\]](#page-5-2). While deep learning methods differ in architecture choice, objective function or optimization strategy, they all assume that the training data is independent and identically distributed (i.i.d). Methods built on this assumption are effective for fixed environments with stationary data distributions – where tasks to be solved do not change over time or classes present in the dataset are known from the beginning. However, in most real-life scenarios this assumption is violated and there is a need for methods that are able to handle such cases. Among many examples of such scenarios, a few can be highlighted: new object class is introduced, however the dataset used to train the baseline model is no longer available; the data characteristics seem to change seasonally and model needs to change its predictions accordingly to these trends. Continual learning  $[21]$  is a paradigm where data is presented sequentially to the algorithm without the ability to manipulate this sequence. Additionally, there is no assumption about the structure of the sequence. A successful continual learning algorithm needs to be able to learn a growing number of tasks, be resistant to catastrophic forgetting [\[17\]](#page-5-4) and be able to adapt do distribution shifts. The memory and computational requirements of such algorithm should scale reasonably with the incoming data.

Although the problem of continual learning is known for many years [\[21,](#page-5-3) [17\]](#page-5-4), only recently has the field gained significant traction and many interesting ideas have been proposed. Most of continual learning contributions can be divided into three categories  $[12, 20]$  $[12, 20]$  $[12, 20]$ : optimization, architecture and rehersal. Methods based on optimization modifications usually add additional regularization terms to objective function to dampen catastrophic forgetting [\[9,](#page-4-2) [11\]](#page-4-3). Second category gathers methods that propose various architectural modifications e.g. Progressive Net  $[22]$  where increasing capacity is obtained by initialising new network for each task. The last category – rehersal based methods – consists of methods that assume life-long presence of a subset of historical data that can be re-used to retain knowledge about past tasks  $[13, 5]$  $[13, 5]$  $[13, 5]$ .

This work proposes a new data-driven path that is orthogonal to existing approaches. Specifically, we would like to explore the possibility of creating input data artificially in a coordinated manner in such a way that it reduces

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image depicts a diagram illustrating a machine learning process. On the left, a 'Generator' is shown with an arrow pointing to a sequence of green boxes, each containing a grid of abstract, noisy images. Below this, a 'Dataset' cylinder is shown with an arrow pointing to a sequence of red boxes. These red boxes contain grids of handwritten digits, progressing from '69' to '18' to '30' to '42'. Arrows connect the boxes in both sequences, indicating a flow. On the right, a line graph compares two data series: 'Meta Learned' (green line) and 'Baseline' (red line). The graph has a y-axis labeled from 0.2 to 1.0 and an x-axis labeled from 1 to 5. Both lines show a decreasing trend, with the 'Meta Learned' line generally staying above the 'Baseline' line, indicating a better performance of the meta-learned approach.

Figure 1. Synthetic data created from generator is divided into five tasks according to classes and learner (green) learns tasks sequentially. The same procedure is applied to learner with real data (red). The right plot shows that accuracy at the end of each task does not decrease on learned data in contrast to real data where it deteriorates sharply.

the catastrophic forgetting phenomena. We achieve this by combining two separate neural networks connected by two-step optimisation. We use generative model to create synthetic dataset and form a sequence of tasks to evaluate learner model in continual learning scenario. The sequence of synthetic tasks is used to train the learner network. Then, the learner network is evaluated on real data. The loss obtained on real data is used to tune the parameters of the generative network. In the following step, the learning network is replaced with a new one.

Differently from existing approaches, our method is independent of training method and task and it can be easily incorporated to above-mentioned strategies providing additional gains.

## 2. Related Work

One line of research for continual learning focuses on optimization process. It draws inspiration from the biological phenomena known as synaptic plasticity [\[1\]](#page-4-6). It assumes that weights (connections) that are important for particular task become less plastic in order to retain the desired performance on previous tasks. An example of such approach is Elastic Weight Consolidation (EWC) [\[9\]](#page-4-2), where regularisation term based on Fisher Information matrix is used to slow down the change of important weights. However accumulation of these constrains prevents network from learning longer sequences of tasks. Another optimization based method is Learning without Forgetting (LwF) [\[11\]](#page-4-3). It tries to retain the knowledge of previous tasks by optimizing linear combination of current task loss and knowledge distillation loss. LwF is conceptually simple method that benefits from knowledge distillation phenomenon [\[6\]](#page-4-7). The downside of such approach is that applying LwF requires additional memory and computation resources for each optimization step.

Methods based on architectural modifications allow to

dynamically expand/shrink networks, select sub-networks, freeze weights or create additional networks to preserve knowledge. Authors of  $[22]$  propose algorithm that for each new task creates a separate network (a column) that is trained to solve particular task. Additionally, connections between previous columns and the current column are learned to enable forward transfer of knowledge. This algorithm avoids catastrophic forgetting completely and enables effective transfer learning. However the computational cost of this approach is prohibitive for longer sequences of tasks. Other methods [\[31,](#page-5-7) [29\]](#page-5-8) address the problem of computational cost by expanding single layers/neurons instead of whole networks, however these methods has less capacity to solve upcoming tasks. Different approaches that modify architectures are based on selecting sub-networks used for solving current task in such a way that only a fraction of network's parameters relevant to current task is changed [\[15,](#page-4-8) [16,](#page-5-9) [3\]](#page-4-9). The challenge here is to balance the number of frozen and active weights in such way that network is still able to learn new tasks and preserve current knowledge.

Rehearsal methods are based on the concept of memory replay. It is assumed that subset of previously processed data is stored in memory bank and interleaved with upcoming data in such a way that neural network learns to solve current task in addition to preserving current knowledge [\[13,](#page-4-4) [28,](#page-5-10) [5\]](#page-4-5). A naive rehearsal method would be to save random data samples that were present during training. However such approach is inefficient, since samples are not equally informative, hence the challenge of rehearsal methods is to choose the most representative samples for a given dataset, such that minimum storage is occupied. In [\[28\]](#page-5-10), authors apply method of dataset distillation based on meta-gradient optimization to reduce the size of memory bank. It is possible to represent whole class of examples just by storing one carefully-optimized example. Unfortunately, applying this meta-optimization method is computationally

<span id="page-2-2"></span>exhaustive. The biggest downside of using rehearsal based methods is the need to store the actual data which in some cases can violate data privacy rules or can be computationally prohibitive. To mitigate this issue solution based on Generative Networks was proposed [\[30,](#page-5-11) [23\]](#page-5-12). Namely, they use dual model architecture composed of learner network and generative network. Role of the generative network is to model data previously experienced by the learner network. Data sampled from the generator network is used as a rehersal data for learner network to reduce the effect of catastrophic forgetting.

Our method is also dual architecture model based on generative network, however the aim of generative network is radically different. In contrast to authors [\[30,](#page-5-11) [23\]](#page-5-12) we do not aim to capture the statistics of real data, instead we try to generate entirely synthetic data such that when learner does learn on a sequence of such data it does not suffer from catastrophic forgetting.

<span id="page-2-1"></span>

## 3. Method

The main idea of our approach is to generate data samples such that network trained on them in sequence would not suffer from catastrophic forgetting. One of many ways to generate artificial data is to use meta-optimization strategy introduced in [\[14\]](#page-4-10). It is shown that by applying metalearning it is possible to use gradient optimization both to hyperparameters and to input data. However, this approach is limited to small problems, since each data point must be optimised separately. To overcome this bottleneck, authors of Generative Teaching Networks (GTNs) [\[26\]](#page-5-13) use generative network to create artificial data samples instead of directly optimizing the data input. We adopt similar approach in our method, namely, we use generative network – green rectangle "Generator" in Fig. [2](#page-2-0) – to produce synthetic data from noise vectors sampled from a random distribution. Next, we split the data into separate tasks according to classes and form a continual learning task for the learner network – blue rectangle in Fig. [2.](#page-2-0) Learner network after completing whole sequence of tasks in evaluated on real training data. The loss from real data classification after learning all tasks in sequence is then backpropagated to generator network to tune the parameters as shown in Fig. [2.](#page-2-0)

Our approach is similar to one proposed in work [\[7\]](#page-4-11). Using two step meta-learning optimization they try to learn best representation of input data such that the model learned in with standard optimization does not suffer from catastrophic forgetting.

Differently from [\[26\]](#page-5-13), we do not use curriculum based learning as our goal is to have a realistic continual learning scenario where the order of data sequence is not known beforehand. To ensure that the Generator network does not generate data suitable for particular sequence of tasks at each meta-optimization we shuffle order of tasks. Precisely,

Image /page/2/Figure/6 description: This is a diagram illustrating a meta-learning process. A 'Generator' box outputs 'Data' labeled 'x' to a 'Learner' box. The 'Learner' box has an 'Inner Optimization' loop feeding back into itself. An arrow goes from the 'Learner' box to a 'Loss' box. Finally, a green arrow labeled 'Meta Gradient' goes from the 'Loss' box back to the 'Generator' box, indicating a meta-gradient update.

<span id="page-2-0"></span>Figure 2. Synthetic data from generator is passed to learner where the inner optimization is performed and meta-loss is backpropagated to G.

at each step we generate  $p$  samples for each class and then randomly create a sequence of binary classification tasks with particular data.

Precisely, let  $G$  be a generative neural network,  $S$  a standard convolutional network for classification,  $t =$  $(t_1, t_2, \ldots t_n)$  a sequence of tasks, where each tasks is binary classification task and classes in each task form mutually disjoint sets.

The inner training loop consists of sequence of tasks, where generated samples from previous tasks are not replayed once the task is finished. To achieve this, the sequence of tasks  $\mathbf{t} = (t_1, t_2, \dots, t_n)$  must be defined a priori and samples generated by network  $G$  are conditioned on the information of particular task. For each task  $t_i$  the network  $\mathcal G$  generates two batches of samples  $\mathbf x = \mathcal G(\mathbf z, \mathbf y_{\mathbf i_j})$  for  $j = 1, 2$ , where z is a batch of noise vectors sampled from Normal distribution and  $y_{i_j}$  is a class indicator for task  $t_i$ . Note that generator networks has access to class indicators since we aim to learn in continual learning scenario only the learner network.

Neural network  $S$  learns sequentially on following tasks using standard SGD optimizer with learning rate and momentum optimized through meta-gradients. At the end of the sequence t network  $S$  is evaluated on real dataset  $(x_r, y_r)$  obtaining meta-loss as shown in Fig. [2.](#page-2-0) This metaloss is backpropagated through all training inner-loops of model S to optimize network G. Parameters  $\theta$  of network G are updated according to the equation:

$$
\theta = \theta - \eta \nabla_{\theta} \mathcal{L}(\mathcal{S}(\mathbf{x}_{\mathbf{r}}; \mathbf{w}_{\mathbf{m}}), \mathbf{y}_{\mathbf{r}}),
$$
 (1)

where  $w_m$  are parameters of the network S after m optimization steps,  $\eta$  is fixed learning rate,  $\mathcal L$  is a cross entropy loss function,  $x_r$ ,  $y_r$  are real data samples and labels respectively.

### 4. Experiments

To test our hypothesis we use popular continual learning benchmark Split-MNIST [\[10,](#page-4-12) [24\]](#page-5-14). In first experiment, we use 5-fold split with two classes for each task to create a moderately difficult sequence of tasks. Network G

<span id="page-3-3"></span>

| Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern |
|-----------------------|-----------------------|-----------------------|-----------------------|-----------------------|-----------------------|-----------------------|-----------------------|-----------------------|-----------------------|
| Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern |
| Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern |
| Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern |
| Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern | Image: visual pattern |

<span id="page-3-1"></span>Figure 3. Samples generated by network  $G$  at the end of metaoptimisation. Starting from zero (leftmost), each sample to the right represents the following class.

generates 250 samples per each class. During inner optimisation learner network is optimized on batch size formed with 40 generated images (20 samples per class drawn randomly from the pool of 250 samples per class). We train the learner network on each task for 5 inner steps with batch size 40. Once the task is over, samples from this task are not shown to the network to the end of training. At test time, after learning on each task the network is evaluated on part of a test set composed of classes seen in previous taks. Both networks are simple convolutional neural networks with two convolutional layers with addition of one and two fully connected layers for classification and generative network respectively. Each layer is followed by a batch normalisation layer.

As a baseline to compare with, we use simple fully connected network proposed in  $[8]$  ('MLP' – red – in Fig. [4\)](#page-3-0). To further investigate the impact of generated data we use the same network architectures and optimizer settings with learning rate and momentum optimized with by a meta learning process as described in Section [3](#page-2-1) but for optimizing the learner network we use real data ('Real Data' – yellow – in Fig. [4\)](#page-3-0). We also compare our results with GANbased data samples. In this scenario we follow the setting of 'Real Data' scenario except for the source of data. We use Conditional-GAN [\[18\]](#page-5-15) to model the original data distribution and then sample 250 samples per each class ('GAN based' – blue – in Fig. [4\)](#page-3-0).

We implement experiments in PyTorch library, which is well suited for computing higher-order gradients [\[4\]](#page-4-14).

Results – obtained results support our hypothesis, that it is possible to generate synthetic data such that, even if networks learns this data in sequence (one time per sample), the learning process does not result in castastrophic forgetting.

Figure [4](#page-3-0) shows how learning on synthetic data in sequence results in less catastrophic forgetting compared to learning on a sequence of real data samples. Note that additional performance could be gained with careful hyperparameter tuning, however we did not want to compete for best performance and rather show the potential of this approach. Higher accuracy of 'Real data' scenario over 'MLP' can be attributed to the effectiveness of optimised learning rate and momentum parameters, however the main advantage comes from using meta learned data samples. Results obtained with data generated with GAN are almost identical to ones obtained with real data. This result is expected as the data modeled by a GAN resembles original data closely.

An example batch of generated samples is shown in Figure [3.](#page-3-1) The samples are ordered according to classes (starting from 0). In contrast to  $[26]$  the data samples are abstract blobs, rather than interpretable images. We verify experimentally that the reason for the lack of structure in generated samples is the lack of curriculum learning in our scenario. We skip it intentionally to provide more realistic continual learning scenario for the learner network.

Image /page/3/Figure/9 description: A line graph displays the performance of four different models: Meta Learned (green), GAN based (blue), Real Data (yellow), and MLP (red). The x-axis ranges from 1 to 5, and the y-axis ranges from 0.2 to 1.0. All models show a general downward trend in performance as the x-axis value increases. The Meta Learned model consistently performs the best, maintaining the highest values across the graph. The GAN based and Real Data models show similar performance, falling between the Meta Learned and MLP models. The MLP model demonstrates the lowest performance, with a sharp decline between x-axis values of 2 and 3.

<span id="page-3-0"></span>Figure 4. Overall accuracy measured on test data subset. After learning each task, test data subset is made of samples only from classes seen during recent and previous tasks.

Image /page/3/Figure/11 description: This is a line graph showing the performance of three different models: Meta Learned, Real Data, and MLP. The x-axis ranges from 10 to 40, and the y-axis ranges from 0.1 to 0.7. The Meta Learned model (green line) starts at approximately 0.65 and decreases to around 0.25. The Real Data model (yellow line) starts at approximately 0.55 and decreases to around 0.18. The MLP model (red line) starts at approximately 0.35 and decreases to around 0.18, then fluctuates slightly before ending around 0.19. Shaded regions around each line indicate a range of performance. The graph appears to be illustrating a comparison of learning curves or error rates over time or iterations.

<span id="page-3-2"></span>Figure 5. Overall accuracy measured on test set after learning network  $S$  with synthetic data for  $x$  inner steps on each task.

Fig. [5](#page-3-2) shows the impact of change of learning scenario of network  $S$  *after* network  $G$  is trained. In this experiment data generated by a network  $\mathcal G$  in first experiment is used. Here, we investigate how the final accuracy after learning five consecutive tasks changes with the number of inner optimization steps. Note that  $G$  was optimised to create samples that are robust to catastrophic forgetting with inner optimization loop of 5 steps. As we can see, in case of longer learning horizon, network learned on synthetic (green plot Fig. [5\)](#page-3-2) data suffers significantly less than the same network learned on real data (yellow plot Fig. [5\)](#page-3-2). Even though accuracy of the networks drops with increasing number of inner steps, the drop is smoother in case of synthetic data.

### 5. Conclusions

The aim of this work was to answer a question, whether it is possible to create data that would dampen the effect of catastrophic forgetting. Experiments show that this hypothesis is true – it is possible to generate such samples, however usually they do not visually resemble real data. Surprisingly, even applying the method alone can result in high performing network. Additional interesting advantage of this synthetic data is the robustness to changes of inner optimisation parameters – increasing 15-fold size of a batch and length on training still results in compelling performance. We believe that our experiments open a new and exciting path in continual learning research. As a future work we plan to adjust current method to datasets of higher complexity and test its effectiveness in online learning scenario.

### 6. Acknowledgements

Authors would like to thank Petr Hlubuek and GoodAI for publishing the code at [https://github.com/](https://github.com/GoodAI/GTN) [GoodAI/GTN](https://github.com/GoodAI/GTN).

### References

- <span id="page-4-6"></span>[1] Joseph Cichon and Wen-Biao Gan. Branch-specific dendritic ca2+ spikes cause persistent synaptic plasticity. *Nature*, 520, 03 2015. [2](#page-1-0)
- <span id="page-4-0"></span>[2] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. BERT: Pre-training of deep bidirectional transformers for language understanding. In *Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long and Short Papers)*, pages 4171–4186, Minneapolis, Minnesota, June 2019. Association for Computational Linguistics. [1](#page-0-0)
- <span id="page-4-9"></span>[3] Siavash Golkar, Michael Kagan, and Kyunghyun Cho. Continual learning via neural pruning. *CoRR*, abs/1903.04476, 2019. [2](#page-1-0)
- <span id="page-4-14"></span>[4] Edward Grefenstette, Brandon Amos, Denis Yarats, Phu Mon Htut, Artem Molchanov, Franziska Meier, Douwe Kiela, Kyunghyun Cho, and Soumith Chintala. Generalized

inner loop meta-learning. *arXiv preprint arXiv:1910.01727*, 2019. [4](#page-3-3)

- <span id="page-4-5"></span>[5] Tyler L. Hayes, Nathan D. Cahill, and Christopher Kanan. Memory efficient experience replay for streaming learning. In *International Conference on Robotics and Automation, ICRA 2019, Montreal, QC, Canada, May 20-24, 2019*, pages 9769–9776. IEEE, 2019. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-4-7"></span>[6] Geoffrey Hinton, Oriol Vinyals, and Jeffrey Dean. Distilling the knowledge in a neural network. In *NIPS Deep Learning and Representation Learning Workshop*, 2015. [2](#page-1-0)
- <span id="page-4-11"></span>[7] Khurram Javed and Martha White. Meta-learning representations for continual learning. In H. Wallach, H. Larochelle, A. Beygelzimer, F. d'Alché-Buc, E. Fox, and R. Garnett, editors, *Advances in Neural Information Processing Systems 32*, pages 1820–1830. Curran Associates, Inc., 2019. [3](#page-2-2)
- <span id="page-4-13"></span>[8] Ronald Kemker, Marc McClure, Angelina Abitino, Tyler L. Hayes, and Christopher Kanan. Measuring catastrophic forgetting in neural networks. In Sheila A. McIlraith and Kilian Q. Weinberger, editors, *Proceedings of the Thirty-Second AAAI Conference on Artificial Intelligence, (AAAI-18), the 30th innovative Applications of Artificial Intelligence (IAAI-18), and the 8th AAAI Symposium on Educational Advances in Artificial Intelligence (EAAI-18), New Orleans, Louisiana, USA, February 2-7, 2018*, pages 3390–3398. AAAI Press, 2018. [4](#page-3-3)
- <span id="page-4-2"></span>[9] James Kirkpatrick, Razvan Pascanu, Neil Rabinowitz, Joel Veness, Guillaume Desjardins, Andrei A. Rusu, Kieran Milan, John Quan, Tiago Ramalho, Agnieszka Grabska-Barwinska, Demis Hassabis, Claudia Clopath, Dharshan Kumaran, and Raia Hadsell. Overcoming catastrophic forgetting in neural networks. *Proceedings of the National Academy of Sciences*, 114(13):3521–3526, 2017. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-4-12"></span>[10] Sang-Woo Lee, Jin-Hwa Kim, Jaehyun Jun, Jung-Woo Ha, and Byoung-Tak Zhang. Overcoming catastrophic forgetting by incremental moment matching. In I. Guyon, U. V. Luxburg, S. Bengio, H. Wallach, R. Fergus, S. Vishwanathan, and R. Garnett, editors, *Advances in Neural Information Processing Systems 30*, pages 4652–4662. Curran Associates, Inc., 2017. [3](#page-2-2)
- <span id="page-4-3"></span>[11] Z. Li and D. Hoiem. Learning without forgetting. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 40(12):2935–2947, 2018. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-4-1"></span>[12] Vincenzo Lomonaco. *Continual Learning with Deep Architectures*. PhD thesis, 2018. [1](#page-0-0)
- <span id="page-4-4"></span>[13] David Lopez-Paz and Marc' Aurelio Ranzato. Gradient episodic memory for continual learning. In I. Guyon, U. V. Luxburg, S. Bengio, H. Wallach, R. Fergus, S. Vishwanathan, and R. Garnett, editors, *Advances in Neural Information Processing Systems 30*, pages 6467–6476. Curran Associates, Inc., 2017. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-4-10"></span>[14] Dougal Maclaurin, David Duvenaud, and Ryan P. Adams. Gradient-based hyperparameter optimization through reversible learning. In *Proceedings of the 32nd International Conference on International Conference on Machine Learning - Volume 37*, ICML15, page 21132122. JMLR.org, 2015. [3](#page-2-2)
- <span id="page-4-8"></span>[15] Arun Mallya, Dillon Davis, and Svetlana Lazebnik. Piggyback: Adapting a single network to multiple tasks by learn-

ing to mask weights. In Vittorio Ferrari, Martial Hebert, Cristian Sminchisescu, and Yair Weiss, editors, *Computer Vision - ECCV 2018 - 15th European Conference, Munich, Germany, September 8-14, 2018, Proceedings, Part IV*, volume 11208 of *Lecture Notes in Computer Science*, pages 72– 88. Springer, 2018. [2](#page-1-0)

- <span id="page-5-9"></span>[16] Arun Mallya and Svetlana Lazebnik. Packnet: Adding multiple tasks to a single network by iterative pruning. In *2018 IEEE Conference on Computer Vision and Pattern Recognition, CVPR 2018, Salt Lake City, UT, USA, June 18-22, 2018*, pages 7765–7773. IEEE Computer Society, 2018. [2](#page-1-0)
- <span id="page-5-4"></span>[17] Michael Mccloskey and Neil J. Cohen. Catastrophic interference in connectionist networks: The sequential learning problem. *The Psychology of Learning and Motivation*, 24:104–169, 1989. [1](#page-0-0)
- <span id="page-5-15"></span>[18] Mehdi Mirza and Simon Osindero. Conditional generative adversarial nets. *CoRR*, abs/1411.1784, 2014. [4](#page-3-3)
- <span id="page-5-2"></span>[19] Witold Oleszkiewicz, Peter Kairouz, Karol Piczak, Ram Rajagopal, and Tomasz Trzciński. Siamese generative adversarial privatizer for biometric data. In C.V. Jawahar, Hongdong Li, Greg Mori, and Konrad Schindler, editors, *Computer Vision – ACCV 2018*, pages 482–497, Cham, 2019. Springer International Publishing. [1](#page-0-0)
- <span id="page-5-5"></span>[20] German I. Parisi, Ronald Kemker, Jose L. Part, Christopher Kanan, and Stefan Wermter. Continual lifelong learning with neural networks: A review. *Neural Networks*, 113:54 – 71, 2019. [1](#page-0-0)
- <span id="page-5-3"></span>[21] Mark Bishop Ring. *Continual Learning in Reinforcement Environments*. PhD thesis, USA, 1994. [1](#page-0-0)
- <span id="page-5-6"></span>[22] Andrei A. Rusu, Neil C. Rabinowitz, Guillaume Desjardins, Hubert Soyer, James Kirkpatrick, Koray Kavukcuoglu, Razvan Pascanu, and Raia Hadsell. Progressive neural networks, 2016. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-5-12"></span>[23] Hanul Shin, Jung Kwon Lee, Jaehong Kim, and Jiwon Kim. Continual learning with deep generative replay. In I. Guyon, U. V. Luxburg, S. Bengio, H. Wallach, R. Fergus, S. Vishwanathan, and R. Garnett, editors, *Advances in Neural Information Processing Systems 30*, pages 2990–2999. Curran Associates, Inc., 2017. [3](#page-2-2)
- <span id="page-5-14"></span>[24] Rupesh K Srivastava, Jonathan Masci, Sohrob Kazerounian, Faustino Gomez, and Jürgen Schmidhuber. Compete to compute. In C. J. C. Burges, L. Bottou, M. Welling, Z. Ghahramani, and K. Q. Weinberger, editors, *Advances in Neural Information Processing Systems 26*, pages 2310–2318. Curran Associates, Inc., 2013. [3](#page-2-2)
- <span id="page-5-1"></span>[25] Wojciech Stokowiec, Tomasz Trzcinski, Krzysztof Wok, Krzysztof Marasek, and Przemyslaw Rokita. Shallow reading with deep learning: Predicting popularity of online content using only its title. pages 136–145, 07 2017. [1](#page-0-0)
- <span id="page-5-13"></span>[26] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeff Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data, 2020. [3,](#page-2-2) [4](#page-3-3)
- <span id="page-5-0"></span>[27] Ivona Tautkute, Tomasz Trzciski, Aleksander P. Skorupa, ukasz Brocki, and Krzysztof Marasek. Deepstyle: Multimodal search engine for fashion and interior design. *IEEE Access*, 7:84613–84628, 2018. [1](#page-0-0)

- <span id="page-5-10"></span>[28] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *CoRR*, abs/1811.10959, 2018. [2](#page-1-0)
- <span id="page-5-8"></span>[29] Yu-Xiong Wang, Deva Ramanan, and Martial Hebert. Growing a brain: Fine-tuning by increasing model capacity. *CoRR*, abs/1907.07844, 2019. [2](#page-1-0)
- <span id="page-5-11"></span>[30] Chenshen Wu, Luis Herranz, Xialei Liu, yaxing wang, Joost van de Weijer, and Bogdan Raducanu. Memory replay gans: Learning to generate new categories without forgetting. In S. Bengio, H. Wallach, H. Larochelle, K. Grauman, N. Cesa-Bianchi, and R. Garnett, editors, *Advances in Neural Information Processing Systems 31*, pages 5962–5972. Curran Associates, Inc., 2018. [3](#page-2-2)
- <span id="page-5-7"></span>[31] Jaehong Yoon, Eunho Yang, Jeongtae Lee, and Sung Ju Hwang. Lifelong learning with dynamically expandable networks. In *International Conference on Learning Representations*, 2018. [2](#page-1-0)
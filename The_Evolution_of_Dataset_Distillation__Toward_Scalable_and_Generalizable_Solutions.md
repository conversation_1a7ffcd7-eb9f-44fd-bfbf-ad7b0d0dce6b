## The Evolution of Dataset Distillation: Toward Scalable and Generalizable Solutions

Ping <PERSON>, *Senior Member, IEEE*, and Jiawei Du

**Abstract**—Dataset distillation, which condenses large-scale datasets into compact synthetic representations, has emerged as a critical solution for training modern deep learning models efficiently. While prior surveys focus on developments before 2023, this work comprehensively reviews recent advances (2023–2025), emphasizing scalability to large-scale datasets such as ImageNet-1K and ImageNet-21K. We categorize progress into a few key methodologies: trajectory matching, gradient matching, distribution matching, scalable generative approaches, and decoupling optimization mechanisms. As a comprehensive examination of recent dataset distillation advances, this survey highlights breakthrough innovations: the SRe2L framework for efficient and effective condensation, soft label strategies that significantly enhance model accuracy, and lossless distillation techniques that maximize compression while maintaining performance. Beyond these methodological advancements, we address critical challenges, including robustness against adversarial and backdoor attacks, effective handling of non-IID data distributions. Additionally, we explore emerging applications in video and audio processing, multi-modal learning, medical imaging, and scientific computing, highlighting its domain versatility. By offering extensive performance comparisons and actionable research directions, this survey equips researchers and practitioners with practical insights to advance efficient and generalizable dataset distillation, paving the way for future innovations.

✦

**Index Terms**—Dataset Distillation, Efficiency, Image Classification, ImageNet-1K, ImageNet-21K

## 1 INTRODUCTION

The rapid advancement of large-scale deep learning<br>models, such as Large Language Models (LLMs) [\[1\]](#page-17-0)<br>and Vision-Language Models (VLMs) [2] has drastically He rapid advancement of large-scale deep learning and Vision-Language Models (VLMs) [\[2\]](#page-17-1), has drastically increased the demand for large datasets to capture complex patterns and semantics. Although these models achieve state-of-the-art performance, their reliance on massive data poses significant challenges in terms of storage, computational cost, and energy efficiency, limiting accessibility and reproducibility. For instance, models like CLIP [\[3\]](#page-17-2) require over 400 million image-text pairs for pre-training, making dataset acquisition and processing prohibitively expensive. This raises concerns about the democratization of AI research, which only institutions with extensive computational resources can afford.

Dataset Distillation (DD) has emerged as a promising solution by condensing large datasets into compact synthetic representations that retain information critical for model training. First introduced by Wang et al. [\[4\]](#page-17-3), DD enables models to achieve performance comparable to full dataset training while significantly reducing storage and computational requirements. Beyond its practical benefits, recent theoretical breakthroughs in explaining the effectiveness of DD have further deepened the understanding of deep learning. For instance, Yang et al. [\[5\]](#page-17-4) demonstrated that distilled datasets primarily capture early training dynamics, closely resembling models that undergo early stopping on real data. By leveraging influence functions, they showed that individual distilled data points retain meaningful semantics unique to the target classes, offering critical insights into the fundamental mechanisms in dataset distillation. These findings not only validate the effectiveness of DD but also pave the way for more principled distillation techniques.

Despite these advancements, dataset distillation faces several pressing challenges that hinder its large-scale deployment. One major challenge is scalability. Existing methods often fail to maintain performance when applied to large-scale datasets such as ImageNet-1K [\[6\]](#page-17-5) and ImageNet-21K [\[7\]](#page-17-6). The nonlinear increase in computational overhead and the dramatic drop in performance on large-scale datasets pose significant challenges to scalability. Another critical limitation is cross-architecture generalization, where distilled datasets optimized for one model architecture often exhibit suboptimal performance on others. Furthermore, robustness concerns, including vulnerability to adversarial and backdoor attacks, must be addressed to ensure the security and reliability of distilled datasets. Beyond traditional image classification, DD is now being explored for other domains such as video, audio, multi-modal learning, and medical imaging, introducing new challenges that demand novel solutions. Given these evolving challenges and the rapid expansion of DD applications, a comprehensive survey is needed to consolidate recent innovations, identify key limitations, and outline future research directions.

As shown in Table [1,](#page-2-0) previous surveys [\[8\]](#page-17-7), [\[9\]](#page-17-8), [\[10\]](#page-17-9) focused primarily on early developments and small-scale datasets such as MNIST [\[11\]](#page-17-10), our work provides a timely and in-depth examination of the latest advances from 2023 to 2025. This survey differentiates itself by systematically addressing the scalability of DD methods to large datasets such as ImageNet-1K [\[6\]](#page-17-5) and ImageNet-21K [\[7\]](#page-17-6), analyzing the effectiveness of cutting-edge techniques such as

<sup>•</sup> *Ping Liu is with the Department of Computer Science and Engineering, University of Nevada, Reno, NV, 89512. E-mail: <EMAIL>*

<sup>•</sup> *Jiawei Du is with Centre for Frontier AI Research (CFAR), and Institute of High Performance Computing (IHPC), A\*STAR, Singapore. E-mail: <EMAIL>*

<sup>•</sup> *denotes the corresponding author.*

<span id="page-1-0"></span>Image /page/1/Figure/0 description: This diagram illustrates a machine learning process comparing training on an original dataset versus a synthetic dataset. The top path shows the original dataset being trained on a randomly initialized model, followed by testing. The bottom path shows the original dataset being condensed into a synthetic dataset, which is then trained on a randomly initialized model, followed by testing. The results of both testing phases are compared using a balance scale, with labels '1' and '2', and the question 'Comparable?' is posed.

Fig. 1: Illustration of dataset distillation [\[4\]](#page-17-3). The original dataset is condensed into a synthetic dataset through a condensation process. Both the original and synthetic datasets are used to train randomly initialized models, and their performance is expected to be comparable.

SRe2L series, soft-labeling strategies, and regularizationbased approaches for improving performance at high IPC settings (e.g., IPC=200). We provide an in-depth comparison of different methodological paradigms, including trajectory matching, gradient matching, distribution matching, generative approaches, and decoupling optimization techniques, highlighting their respective strengths and limitations. Additionally, we explore underrepresented yet critical aspects of dataset distillation, such as adversarial and backdoor robustness against various attacks, self-supervised learning applications, domain adaptations, and non Identical and Independent Distribution (IID).

This survey is organized as follows. Section 2 introduces the background concepts and fundamental approaches in dataset distillation, covering techniques such as trajectory matching, gradient matching, and distribution matching. Section 3 highlights applications of dataset distillation across various domains and tasks, including temporal data, multi-modal learning, medical imaging, and scientific computing. Section 4 provides a comprehensive performance comparison of different methods on various benchmarks. Finally, Section 5 discusses the current challenges in dataset distillation and outlines promising directions for future research. Throughout our analysis, we aim to provide researchers and practitioners with actionable guidelines for advancing the field.

## 2 FUNDAMENTAL DATASET DISTILLATION METHODS

Dataset distillation, first introduced by Wang et al. [\[4\]](#page-17-3), seeks to condense a large training dataset into a significantly smaller, yet highly effective synthetic set. The foundational formulation of dataset distillation is defined as:

$$
S = \arg\min_{S} \mathbb{E}_{\theta^{(0)} \sim \Theta}[l(\mathcal{T}; \theta_{S}^{(T)})],
$$
  
\n
$$
\theta_{S}^{(t)} = \theta_{S}^{(t-1)} - \alpha \nabla_{\theta_{S}^{(t-1)}} l(S; \theta_{S}^{(t-1)}),
$$
\n(1)

where  $S$  represents the synthetic training data to be learned,  $T$  denotes the original training dataset,  $\theta_{\mathcal{S}}^{(t)}$  represents model parameters at step t,  $\theta^{(0)}$  is initial parameters,  $\Theta$  is the distribution of initializations,  $\alpha$  is learning rate, and  $l(\cdot; \theta)$ represents the loss function.

As shown in Figure [1,](#page-1-0) the primary objective is to generate a condensed dataset that enables training new models with performance equivalent to those trained on the full original dataset. Wang et al. employed backpropagation through time (BPTT) [\[12\]](#page-17-11) as a core mechanism, emphasizing consistent model architectures across distillation and training phases to ensure alignment and stability. This seminal work laid the foundation for extensive research aimed at enhancing the efficiency, generalization, and scalability of dataset distillation techniques.

However, traditional BPTT suffers from challenges like gradient variance, computational inefficiency, and difficulty capturing long-term dependencies. To address these limitations, subsequent research introduced innovative approaches. Feng et al. [\[13\]](#page-17-12) proposed Random Truncated BPTT (RaT-BPTT), which combines truncation and randomization to stabilize gradients and improve efficiency. This method effectively handles long-term dependencies, leading to significant performance gains in dataset distillation.

Building upon this, Yu et al. [\[14\]](#page-17-13) developed the Teddy framework, which employs Taylor-approximated matching to simplify second-order optimization into a more computationally efficient first-order approach:

$$
l(\mathcal{T}; \theta_{\mathcal{S}}^{(T)}) = l(\mathcal{T}; \theta_{\mathcal{S}}^{(T-1)} - \alpha g_{\mathcal{S}}^{(T-1)})
$$
  

$$
\approx l(\mathcal{T}; \theta_{\mathcal{S}}^{(T-1)}) - \alpha g_{\mathcal{T}}^{(T-1)} \cdot g_{\mathcal{S}}^{(T-1)}
$$
  

$$
\approx l(\mathcal{T}; \theta^{(0)}) - \alpha \sum_{t=0}^{T-1} g_{\mathcal{T}}^{(t)} \cdot g_{\mathcal{S}}^{(t)}.
$$
 (2)

By applying first-order Taylor expansion to unfold the unrolled computation graph, it decouples the bi-level optimization process. This transformation reduces computational complexity while avoiding the need to store and backpropagate through multiple training steps.

Building on the foundational concepts and innovations, following research has diversified into three primary matching-based approaches: distribution matching, which focuses on aligning the statistical properties of datasets; gradient matching, which seeks to replicate the training dynamics over short-term optimization steps by aligning gradients; and trajectory matching, which extends this idea to longterm consistency by aligning the entire optimization path of models trained on distilled data. While these methods share a common goal of preserving the training behavior of the original dataset, their differing scopes, such as short-term versus long-term alignment, address unique challenges in dataset distillation. The subsequent sections provide an indepth exploration of these works, with emphasis on the advancements in the past two years.

### 2.1 Matching Based Approaches

#### 2.1.1 Gradient Matching

Dataset distillation through gradient matching represents one of the earliest and most fundamental approaches in matching-based methods. As shown in Figure [1,](#page-1-0) these methods aim to align the gradients of models trained on synthetic data with those trained on the original full dataset, ensuring

<span id="page-2-0"></span>TABLE 1: Comparison of recent dataset distillation surveys. Unlike previous surveys, which primarily focus on small datasets, low IPC settings, and supervised classification tasks, our work expands the scope to large-scale datasets, high IPC settings, generative approaches, decoupling-based solutions, and broader applications, such as self-supervised learning and federated learning.

| Survey          | Coverage Period | <b>Techniques Covered</b>                | Large-Scale | Large IPC | Bevond CLS | SSL/DA/Non-IID |
|-----------------|-----------------|------------------------------------------|-------------|-----------|------------|----------------|
| Geng et al. [8] | 2019-2023       | GM, TM, DM                               |             |           |            |                |
| Lei et al. [9]  | 2019-2023       | GM, TM, DM                               |             |           |            |                |
| Yu et al. [10]  | 2019-2023       | GM. TM. DM                               |             |           |            |                |
| Ours            | 2023-2025       | GM, TM, DM, Generative, SRe2L, Softlabel |             |           |            |                |

that the distilled dataset guides the model towards similar optimization trajectories as the original dataset.

Zhao et al. [\[15\]](#page-17-14) first formalized this approach through a gradient matching framework. The core idea is that a model trained on distilled data should achieve both comparable generalization performance and parameter alignment with a model trained on the original dataset. The objective of gradient matching can be defined as:

$$
\min_{\mathcal{S}} \mathbb{E}_{\theta_0 \sim p(\theta_0)} \left[ \sum_{t=0}^{T-1} D(\nabla_{\theta} \ell^{\mathcal{S}}(\theta_t), \nabla_{\theta} \ell^{\mathcal{T}}(\theta_t)) \right],\tag{3}
$$

where  $\theta$  is the model parameters,  $\ell(\cdot)$  is the loss function,  $D(\cdot, \cdot)$  measures the distance between gradients, typically implemented using cosine similarity or L2 distance. To further enhance the robustness and effectiveness of gradient matching, Zhao et al. [\[16\]](#page-17-15) introduced Differentiable Siamese Augmentation (DSA). This approach addresses the limited diversity of synthetic datasets by incorporating differentiable data augmentation into the matching process. DSA applies consistent transformations to both real and synthetic data while maintaining end-to-end gradient propagation, significantly improving the generalization capability.

**Stage Summary** However, while gradient matching effectively ensures step-wise optimization similarity, it faces several limitations. Focusing on matching individual gradient steps may not capture long-term dependencies in training and can be sensitive to learning rate schedules and optimization hyperparameters. Additionally, the computational cost of calculating and matching gradients at each step can be substantial, particularly for large models and datasets. These limitations motivated researchers to explore methods that consider the entire training trajectory rather than individual gradient steps, leading to the development of trajectory matching approaches.

#### 2.1.2 Trajectory Matching

Building upon the limitations of gradient matching, trajectory matching methods extend the optimization alignment from individual gradient steps to entire parameter trajectories during training. This holistic approach addresses both the long-term dependency issue and optimization instability inherent in gradient-based methods. As shown in Figure [2,](#page-2-1) by aligning complete training trajectories between networks trained on synthetic and real data, this approach enables more robust and stable distillation.

Cazenavette et al. [\[17\]](#page-17-16) formalized this trajectory-level matching through their Matching Training Trajectories (MTT) method. While gradient matching focuses on stepwise gradient alignment, MTT uses expert trajectories from

<span id="page-2-1"></span>Image /page/2/Figure/11 description: This image illustrates a process of matching an original dataset's weights trajectory with a synthetic dataset's weights trajectory. The original dataset side shows a stack of blue cylinders representing data, a graph network, and a dashed line with pins indicating a 'Weights Trajectory' during training, along with a graph network. The matching section displays two dotted lines representing trajectories, labeled with points like \u03b8\*\_M-1,m and \u03b8\*\_1,m. The right side shows a synthetic dataset with a green disc and a graph network. The training section for the synthetic dataset mirrors the original, showing a 'Weights Trajectory' and a graph network. A key part of the matching is shown with a curved red line representing the synthetic trajectory, starting from \u03b8\*\_t,0 = \u03b8\*\_t,0 and moving towards \u03b8\*\_t,n, with a difference labeled as \u03b4.

Fig. 2: Overview of trajectory matching-based dataset distillation. The method aligns parameter trajectories between models trained on synthetic and real data.

real datasets as benchmarks for the entire training process. The formulation of MTT is defined as:

$$
S^* = \arg\min_{S} \mathbb{E}_{\theta^{(0)} \sim \Theta} \sum_{t=0}^T \mathcal{D}(\theta_S^{(t)}, \theta_T^{(t)}), \tag{4}
$$

where  $\theta_{\mathcal{S}}^{(t)}$  and  $\theta_{\mathcal{T}}^{(t)}$  represent model parameters at step t when trained on synthetic and real data respectively, and  $\mathcal{D}(\cdot,\cdot)$  measures parameter-space distances rather than gradient differences. The initialization distribution  $\Theta$  and trajectory length  $T$  are crucial hyperparameters that influence the robustness of the matching process [\[18\]](#page-17-17). The development of trajectory matching methods has been driven by three primary challenges: *trajectory stability*, *parameter alignment*, and *computational scalability*. Each subsequent advancement has contributed to addressing these challenges in complementary ways.

Ensuring stable and robust trajectories is crucial for effective dataset distillation. While MTT effectively aligns trajectories, challenges like accumulated trajectory errors and sensitivity to weight perturbations persist. Du et al. [\[18\]](#page-17-17) proposed Flat Trajectory Distillation (FTD), which reduces sensitivity by encouraging flat parameter trajectories through regularization. This approach improves robustness and compactness, particularly on larger datasets like CIFAR-100 [\[19\]](#page-17-18). Similarly, Shen et al. [\[20\]](#page-17-19) introduced Adaptive Smooth Trajectories (AST), incorporating gradient clipping, penalties, and representative sample initialization to stabilize training dynamics and mitigate trajectory mismatches. Zhong et al. [\[21\]](#page-17-20) addressed trajectory instability and slow convergence with Matching Convexified Trajectories (MCT), which replaces oscillatory SGD paths with stable linear

trajectories inspired by Neural Tangent Kernel (NTK) theory [\[22\]](#page-17-21). By requiring only two checkpoints, MCT reduces memory overhead while improving distillation stability.

Tackling parameter mismatches is also essential for distillation across heterogeneous datasets. Li et al. [\[23\]](#page-17-22) tackled the issue of parameter mismatches between teacher and student models caused by data heterogeneity. Their method identifies mismatched parameters by evaluating the values of parameter magnitudes and prunes these selected parameters. The distilled dataset is then optimized over the remaining effective parameters, significantly enhancing distillation efficiency and performance.

To tackle scalability challenges with large-scale datasets, Cui et al. [\[24\]](#page-17-23) developed Trajectory Matching with Soft Label Assignment (TESLA). TESLA refines the MTT loss function by decomposing gradient computations into sequential batches, eliminating the need to store full computational graphs in memory. Additionally, TESLA incorporates a soft label assignment strategy, leveraging pre-trained teacher models to generate soft labels for synthetic data, which significantly boosts performance when scaling to datasets with a large number of classes, such as ImageNet-1K. Another innovation in this direction is the Automatic Training Trajectories (ATT) method introduced by Liu et al. [\[25\]](#page-17-24). Unlike most trajectory matching approaches that use fixed trajectory lengths, ATT dynamically adjusts trajectory lengths during the distillation process. By employing a minimum distance strategy to select optimal matching trajectories, ATT effectively reduces mismatching errors and ensures precise alignment of synthetic datasets.

**Stage Summary** Despite these advances, trajectory matching still faces important challenges. The high computational demands of tracking full parameter trajectories limit applications to complex architectures, while sensitivity to hyperparameters can affect robustness. Future research opportunities include developing adaptive hyperparameter selection mechanisms, designing more efficient algorithms for resource-constrained settings, and exploring hybrid approaches that combine the benefits of trajectory matching with other distillation techniques.

#### 2.1.3 Distribution Matching

Dataset distillation traditionally relied on gradient and trajectory matching, suffering from two fundamental limitations: the computational complexity of bi-level optimization and the challenge of capturing long-term dependencies. Distribution matching emerges as an alternative paradigm that addresses these limitations through direct alignment of feature distributions in selected embedding spaces.

As shown in Figure [3,](#page-3-0) the core objective of distribution matching is to optimize synthetic data such that its distribution aligns with the original data distribution across multiple embedding spaces. This approach differs fundamentally from gradient or trajectory matching methods, offering improved computational efficiency and interpretability. The formal optimization problem is expressed as:

$$
S^* = \arg\min_{S} \mathbb{E}_{\phi \sim \Phi} [\mathcal{D}(P_{\phi}(S), P_{\phi}(\mathcal{T}))],
$$
 (5)

where  $P_{\phi}(\cdot)$  denotes the distribution of features extracted by network  $\phi$ , and  $\mathcal{D}(\cdot, \cdot)$  measures the distance between

<span id="page-3-0"></span>Image /page/3/Figure/9 description: The image illustrates a process of feature extraction and matching between an original dataset and a synthetic dataset. On the left, an original dataset is represented by stacked blue cylinders, with an arrow pointing to a neural network diagram labeled 'Feature'. Below this, a synthetic dataset is depicted as a green disc, with an arrow pointing to 'Extraction'. The right side of the image shows two histograms within a dashed blue box. The top histogram, in blue, represents features from the original dataset, with a superimposed bell curve. The bottom histogram, in green, represents features extracted from the synthetic dataset, also with a superimposed bell curve. A vertical double-headed arrow with a horizontal line in the middle is positioned between the two histograms, labeled 'Matching', indicating a comparison or alignment process between the feature distributions of the original and synthetic datasets.

Fig. 3: Overview of distribution matching in dataset distillation, where feature distributions are aligned to ensure the synthetic dataset effectively preserves the key characteristics of the original data.

distributions. This formulation, first introduced by Zhao et al. [\[26\]](#page-17-25), avoids the costly bi-level optimization process prevalent in earlier methods such as [\[4\]](#page-17-3), [\[15\]](#page-17-14). The following developments in distribution matching have focused on three major areas: *feature refinement*, *advanced alignment*, and *higher-order matching*.

In the first area, the CAFE framework [\[27\]](#page-17-26) introduced multi-scale feature preservation to address overfitting to dominant features. This multi-scale approach significantly improves the discriminative power of synthetic datasets. Zhao et al. [\[28\]](#page-17-27) further refined this by introducing partitioning strategies and class-aware regularization to handle imbalanced feature distributions. Zhang et al.'s [\[29\]](#page-17-28) DANCE framework addresses both distribution shifts and training inefficiencies. By combining pseudo long-term distribution alignment with expert-driven distribution calibration, DANCE effectively aligns intra-class and inter-class distributions, achieving state-of-the-art performance. Rahimi et al. [\[30\]](#page-17-29) took a different approach by decomposing distributions into content and style components, enabling more nuanced optimization of feature diversity.

In the second area, advanced alignment mechanisms have emerged as a critical development in dataset distillation, going beyond traditional statistical matching by incorporating attention-based strategies, trajectory constraints, and pseudo-labeling techniques. Sajedi et al. [\[31\]](#page-17-30) proposed DataDAM, which integrates feature distribution alignment with spatial attention matching. By leveraging attention maps aggregated from feature representations, DataDAM effectively emphasizes informative regions while reducing redundant background information, leading to more compact yet expressive synthetic datasets.

In the third area, recent approaches utilized higherorder distribution matching, a crucial advancement beyond traditional mean distribution alignment. Zhang et al. [\[32\]](#page-17-31) pioneered embedding representations into a Reproducing Kernel Hilbert Space [\[33\]](#page-17-32), enabling alignment across multiple moments. Wei et al. [\[34\]](#page-17-33) complemented this with Latent Quantile Matching (LQM), aligning synthetic data with optimal quantiles determined via statistical tests. From a geometric perspective, Liu et al. [\[35\]](#page-17-34) leveraged the Wasserstein

distance [\[36\]](#page-17-35), capturing first and second-order statistics for distribution alignment. Similarly, Deng et al. [\[37\]](#page-17-36) proposed class centralization and covariance matching, incorporating second-order information to tighten intra-class clustering while maintaining inter-class separation.

**Stage Summary** Collectively, these methods highlight the transition from simple mean-matching techniques to statistically and geometrically informed alignment strategies. However, as shown in Table [2,](#page-15-0) distribution matching methods often lag behind gradient matching approaches in accuracy on large-scale datasets, revealing fundamental limitations in capturing dynamic training behaviors. To bridge this gap, future research could explore hybrid approaches that integrate distribution and gradient matching, develop adaptive higher-order constraints that evolve during optimization, and investigate causal relationships within feature distributions to gain deeper insights into the effectiveness of different matching strategies.

#### 2.1.4 Latent and Frequency Space Methods

Traditional dataset distillation methods typically operate in the pixel space, directly generating synthetic data in the spatial domain. However, these methods often face significant computational and memory challenges, especially when handling high-dimensional datasets. To address these limitations, recent approaches have explored alternative spaces, such as latent and frequency domains, to achieve more efficient and scalable distillation.

Operating in latent spaces has emerged as a promising direction for efficient distillation due to its ability to reduce dimensionality while preserving essential information. Duan et al. [\[38\]](#page-17-37) proposed LatentDD, which transitions from the pixel space to the latent space using a pretrained autoencoder. By condensing datasets into compact latent representations, LatentDD substantially reduces computation and memory requirements, enabling efficient dataset distillation without compromising performance. Building on this shift to latent spaces, George et al. [\[39\]](#page-17-38) proposed GlaD, which leverages the latent spaces of generative models like StyleGAN-XL [\[40\]](#page-17-39). By encoding high-dimensional images into low-dimensional feature vectors, GlaD not only reduces computational cost but also enhances generalization ability.

Complementary to latent space approaches, frequencydomain methods offer another efficient avenue for dataset distillation. Shin et al. [\[41\]](#page-17-40) introduced Frequency Domainbased Dataset Distillation (FreD), a method that operates in the frequency domain rather than the spatial domain. FreD optimizes a selective subset of important frequency dimensions based on explained variance ratios, avoiding the need to process the full dimensionality of the spatial domain. This approach drastically lowers the memory budget per synthetic instance while preserving critical information. Building upon frequency-domain techniques, Yang et al. [\[42\]](#page-17-41) introduced Neural Spectral Decomposition (NSD), leveraging the low-rank structure of datasets to decompose them into spectral tensors and kernel matrices, enhancing learning efficiency and integrating seamlessly with frameworks like MTT. Moving beyond traditional representations, Shin et al. [\[43\]](#page-17-42) introduces Distilling Dataset into Neural Field (DDiF), a novel parameterization framework that compresses large

datasets into synthetic neural fields under limited storage budgets, offering superior performance on various datasets. **Stage Summary** These methods highlight the potential of alternative spaces to overcome the computational and scalability challenges inherent in pixel-based approaches. As latent and frequency space techniques continue to evolve, integrating their strengths or combining them with other methodologies could further enhance the efficiency and generalization of dataset distillation frameworks.

#### 2.1.5 Plug-and-Play Approaches

Recent advancements in dataset distillation explored plugand-play techniques that seamlessly integrate into existing methods, enhancing both optimization efficiency and generalization. Unlike approaches that introduce entirely new optimization paradigms, these methods serve as *modular* enhancements, improving adaptability and scalability. However, it is important to note that current plug-and-play techniques are primarily designed for matching-based methods, such as gradient matching and trajectory matching.

One major limitation in synthetic dataset optimization is treating the dataset as a single, unified entity during training, which can hinder the ability to capture training dynamics effectively. To tackle the uniform optimization limitation of synthetic datasets, Du et al. [\[44\]](#page-17-43) proposed Sequential Subset Matching (SeqMatch), a strategy that partitions the dataset into smaller, manageable subsets. This approach addresses the inefficiencies associated with treating the entire dataset as a single unified whole, which often struggles to capture the dynamic nature of training processes. Instead of optimizing the dataset as an indivisible entity, SeqMatch divides it into K subsets, each sequentially optimized to capture knowledge relevant to specific stages of training. The optimization process for each subset is expressed as:

$$
\hat{S}_k = \arg\min_{\substack{S_k \subset \mathbb{R}^d \times \mathcal{Y} \\ |S_k| = \lfloor |S|/K \rfloor}} \mathbb{E}_{\theta_0 \sim P_{\theta_0}} \left[ \sum_{m=(k-1)n}^{kn} \mathcal{L}(S_k \cup S_{(k-1)}, \theta_m) \right],
$$
\n(6)

where K denotes the number of subsets, n is the number of iterations per subset. Here,  $\mathcal{L}(\cdot, \cdot)$  represents the loss function used for model optimization,  $S_{k-1}$  represents previously optimized subsets, and  $S_k$  is the current subset being optimized. The cumulative incorporation of subsets ensures that knowledge captured in earlier stages is preserved and further refined in subsequent iterations.

Recently, mutual information maximization [\[45\]](#page-17-44) has been introduced as a plug-and-play technique to enhance dataset distillation by preserving crucial feature representations. This approach seamlessly integrates with existing methods without requiring significant modifications to the core optimization framework, making it a flexible enhancement for various distillation techniques. Shang et al. [\[46\]](#page-17-45) introduced MIM4DD, reframing dataset distillation as a mutual information maximization problem. Using a contrastive learning framework [\[47\]](#page-17-46), MIM4DD maximizes mutual information [\[45\]](#page-17-44) between synthetic and real samples through positive pair attraction and negative pair repulsion within the representation space, while aligning samples from the same class and separating those from different classes. Building on the perspective of mutual information maximization [\[48\]](#page-17-47), Zhong et al. [\[49\]](#page-17-48) introduced class-aware conditional mutual information (CMI) as a plug-and-play regularization term. By concentrating synthetic datasets

around class centers in pre-trained feature spaces, CMI improves generalization across architectures. This approach integrates seamlessly with existing techniques, offering consistent performance improvements across datasets.

Addressing biases in synthetic dataset generation is another critical focus. Son et al. [\[50\]](#page-17-49) tackled the bilateral equivalence phenomenon, where synthetic images symmetrically replicate discriminative parts, hindering the ability to distinguish subtle object details. To resolve this issue, they introduced FYI, a method that embeds horizontal flipping into the distillation process to break symmetry bias. This adjustment allows the synthetic datasets to capture richer and more nuanced object details, enhancing performance across multiple existing distillation techniques.

Leveraging external knowledge from Pre-Trained Models (PTMs) presents another promising avenue for improving dataset distillation. Lu et al. [\[51\]](#page-17-50) highlighted the untapped potential of PTMs, proposing Classification Loss of pre-trained Model and Contrastive Loss of pre-trained Model to transfer PTM knowledge into the distillation process. These loss terms provide stable supervisory signals, leveraging diverse PTM architectures, parameters, and domain expertise. The study demonstrated the effectiveness of PTMs in enhancing cross-architecture generalization, even when using less-optimally trained models.

**Stage Summary** These plug-and-play approaches demonstrate the potential to seamlessly integrate advanced techniques into existing dataset distillation workflows. By improving efficiency, addressing biases, and leveraging external knowledge, these methods pave the way for scalable and generalizable distillation frameworks that can adapt to diverse datasets and architectures.

### 2.2 Scalable Dataset Distillation Methods

As the demand for large-scale and high-resolution datasets continues to grow, dataset distillation methods face mounting scalability challenges. To address these, recent advancements have introduced innovative frameworks that balance efficiency and performance, extending the applicability of dataset distillation to larger datasets. Key developments include the exploration of generative models such as GANs [\[52\]](#page-18-0) and Diffusion Models [\[53\]](#page-18-1), decoupling strategies of the SRe2L series [\[54\]](#page-18-2), and the soft label techniques [\[55\]](#page-18-3). The following subsubsections delve into these cutting-edge methods, emphasizing their contributions and the challenges they address in achieving scalability.

#### 2.2.1 Dataset Distillation via Generative Models

Recent advancements in dataset distillation have increasingly leveraged generative models to enhance scalability, efficiency, and generalization. Unlike traditional methods that rely on discriminative matching processes to create synthetic data, these approaches utilize generative mechanisms to synthesize high-quality, diverse datasets.

##### ******* GAN-based Methods

Early efforts in this direction focused on GANs. Zhao et al. [\[56\]](#page-18-4) introduced Informative Training GAN (IT-GAN), challenging the traditional focus on visual realism in GANgenerated data. Instead, IT-GAN prioritizes informative training samples by freezing a pre-trained GAN and optimizing latent vectors using condensation loss and diversity regularization. In IT-GAN, condensation loss is to minimize discrepancies between real and synthetic data, and regularization loss promotes diversity in the generated data.

Expanding this direction, Li et al. [\[57\]](#page-18-5) emphasized the importance of balancing global structures and local details in dataset distillation. Their method combines logits matching for capturing global structures with feature matching for refining local details, leveraging a model pool of diverse architectures to improve cross-architecture generalization. Similarly, Wang et al. [\[58\]](#page-18-6) introduced Distill into Model (DiM), a novel approach that distills datasets into a generative adversarial network instead of directly producing synthetic images. DiM dynamically synthesizes diverse training samples from random noise, enhancing scalability for large architectures and eliminating the need for repeated re-distillation. Building on these advancements, Zhang et al. [\[59\]](#page-18-7) proposed a codebook-generator framework, which condenses datasets into a compact codebook and generator. This approach incorporates intra-class diversity and interclass discrimination losses, enabling efficient synthesis of class-specific images and scalability to large datasets such as ImageNet-1K. These methods collectively demonstrate the versatility and scalability of generative models across architectures and datasets in dataset distillation.

##### 2.2.1.2 Diffusion-based Methods

Diffusion models, which are renowned for their superior image quality compared to GANs, have emerged as a powerful alternative. Gu et al. [\[60\]](#page-18-8) pioneered a diffusion-based framework leveraging a 'minimax' criterion to balance representativeness and diversity in surrogate datasets, ensuring both coverage and variability of training data. Similarly, Su et al. [\[61\]](#page-18-9) introduced Dataset Distillation via Disentangled Diffusion Model (D4M), which combines latent diffusion models with prototype learning. This approach enhances distillation efficiency and cross-architecture generalization while employing a novel training-time matching strategy. By eliminating architecture-specific optimization during image synthesis, D4M achieves high-resolution image generation, representing a substantial leap forward in dataset distillation methodologies.

Exploring the capabilities of DMs further, Abbasi et al. [\[62\]](#page-18-10) proposed Dataset Distillation using Diffusion Models (D3M), which compresses entire image categories into textual prompts using techniques like textual inversion and patch selection. This approach enables efficient dataset compression while maintaining high performance on large-scale benchmarks. In a complementary approach, Abbasi et al. [\[63\]](#page-18-11) combined coreset selection [\[64\]](#page-18-12) with latent diffusion models, enhancing patch diversity and realism, achieving significant improvements in large-scale benchmarks.

Recently, latent space techniques have offered innovative ways to optimize storage efficiency, scalability, and adaptability in this direction. Moser et al. [\[65\]](#page-18-13) introduced Latent Dataset Distillation using Diffusion Models (LD3M), which extends the distillation process into the latent space. By leveraging a pre-trained latent diffusion model, LD3M enhances gradient propagation through a modified diffusion process that integrates initial latent states, striking an optimal balance between speed and accuracy:

$$
z_{t-1} \leftarrow \left[ \left( 1 - \frac{t}{T} \right) \cdot \mu_{\theta}(c, z_t, \gamma_t) + \frac{t}{T} \cdot z_T \right] + \sigma_t^2 \varepsilon_t, \quad (7)
$$

where  $T$  denotes the total number of diffusion steps,  $t$ represents the current timestep in the backward process,  $\mu_{\theta}$ is the parameterized mean predictor network with learnable parameters  $\theta$ , and  $z_T$  is the initial noisy state. The linear interpolation coefficients  $(1 - \frac{t}{T})$  and  $\frac{t}{T}$  control the balance between the mean prediction and initial state, providing enhanced gradient flow during training without compromising the generation quality. This framework demonstrates superior performance for high-resolution tasks while integrating seamlessly with existing algorithms. Taking a different approach to latent space optimization, Qin et al. [\[66\]](#page-18-14) proposed Distributional Dataset Distillation (D3) and applied it into the federated learning setting [\[67\]](#page-18-15). In their method, D3 represents classes as Gaussian distributions in latent space, optimizing the parameters to generate images using a decoder network. To guide the distilled data generation process, in [\[68\]](#page-18-16), Chen et al. introduced Influence-Guided Diffusion (IGD) that uses a latent diffusion model based on a Diffusion Transformer [\[69\]](#page-18-17) to generate synthetic training datasets. They designed an influence guidance function to steer diffusions towards generating trainingeffective data and a deviation guidance function to enhance diversity, enabling the method to significantly improve performance across various datasets, particularly on highresolution ImageNet-1K benchmarks.

**Stage Summary** While current methods have demonstrated promising results in improving scalability and efficiency through various generative approaches, several critical research directions deserve further investigation. First, with the rapid advancement of generative models, particularly the emergence of flow matching [\[70\]](#page-18-18), [\[71\]](#page-18-19) and other innovative approaches demonstrating superior effectiveness and efficiency, incorporating these cutting-edge generative techniques into dataset distillation presents a promising avenue for exploration. Second, current dataset distillation methods face significant challenges in memory usage and computational efficiency when handling high-resolution images. In this context, distillation approaches based on generative models, might offer potential solutions for highresolution scenarios, inspiring further research into optimizing algorithmic architectures to enhance memory efficiency while maintaining distillation quality. Finally, the integration of generative approaches with traditional matchingbased methods warrants thorough investigation.

##### 2.2.2 Decoupling Optimization: SRe2L Series

The high computational costs associated with bilevel optimization have long hindered the scalability of dataset distillation methods, particularly when applied to largescale datasets or high-resolution images. To address these challenges, Yin et al. [\[54\]](#page-18-2) proposed the Squeeze, Recover, and Relabel (SRe2L) framework, a three-stage process that decouples dataset condensation into manageable steps. As shown in Figure [4,](#page-6-0) the Squeeze stage extracts essential information by training a model on the original dataset,

<span id="page-6-0"></span>Image /page/6/Figure/6 description: The image displays a three-stage process for model training and evaluation. The first stage, 'Squeeze (training) Stage', shows an 'Original Dataset' being minimized using a 'Cross-Entropy Loss' to produce a 'Randomly Initialized Model'. The second stage, 'Recover (distilling) Stage', takes a 'Synthetic Dataset' and optimizes it with 'Cross-Entropy Loss + BN loss' to yield a 'Well-converged Model'. The third stage, 'Relabel (evaluating) Stage', uses the 'Well-converged Model' to generate 'Soft Labels' from the 'Synthetic Dataset', which are then used for training with 'Cross-Entropy Loss' to produce a final model.

Fig. 4: Overview of the three-stage SRe2L work [\[54\]](#page-18-2). The Squeeze Stage trains a randomly initialized model on the original dataset by minimizing cross-entropy loss. The Recover Stage refines the synthetic dataset by optimizing a combined cross-entropy and batch normalization (BN) loss with a well-converged model. Finally, the Relabel Stage evaluates performance by training another model on the synthetic dataset with soft labels, using cross-entropy loss.

followed by the Recover stage, which generates synthetic data by aligning batch normalization (BN) statistics [\[72\]](#page-18-20) with the trained model. Finally, the Relabel stage assigns soft labels to the synthetic data using the model. This approach achieved promising performance on large-scale datasets such as ImageNet-1K at resolutions of  $224\times224$  pixels.

Building upon this foundational work, Yin et al. introduced Curriculum Data Augmentation (CDA) [\[73\]](#page-18-21), which employs a curriculum learning approach [\[74\]](#page-18-22) during the data synthesis process. By progressively increasing the difficulty of image crops through dynamic adjustments of RandomResizedCrop parameters, CDA captures global structures early in training and refines local details in later stages. This method set a milestone by successfully distilling the entire ImageNet-21K dataset [\[7\]](#page-17-6) at standard resolutions, marking an huge advance in large-scale dataset distillation.

Subsequent works systematically addressed the inherent limitations of SRe2L in scalability, generalization, and computational efficiency. Zhou et al. [\[75\]](#page-18-23) introduced the SC-DD framework, which overcomes SRe2L's challenges with larger models by enhancing BN statistics through self-supervised pretraining [\[76\]](#page-18-24). By enriching these statistics and employing linear probing during relabeling, SC-DD significantly improves data recovery and scalability for large datasets like ImageNet-1K. Shao et al. [\[77\]](#page-18-25) extended this progress with the Generalized Various Backbone and Statistical Matching (G-VBSM) method, which addresses SRe2L's reliance on single backbone models or specific statistics by introducing data densification, generalized statistical alignment, and multi-backbone consistency. G-VBSM achieved significant improvement across datasets and architectures, particularly in highly compressed settings. Further

refinement came with the Elucidating Dataset Condensation (EDC) [\[78\]](#page-18-26), which incorporated real-image initialization, category-aware matching, and flatness regularization. More recently, Cui et al. [\[79\]](#page-18-27) introduced Committee Voting for Dataset Distillation (CV-DD), synthesizing high-quality distilled datasets by integrating insights from multiple pretrained models. CV-DD introduces a Prior Performance Guided voting strategy that assigns greater weights to better-performing pre-trained models in the voting process, and proposes Batch-Specific Soft Labeling, which generates more accurate soft labels by recalculating BN statistics for each synthetic data batch.

**Stage Summary** Recent works have shifted from singlemodel approaches to leveraging model diversity, as seen in G-VBSM's multi-backbone architecture and CV-DD's committee voting mechanism. These methods emphasize improving batch normalization statistics, with SC-DD enhancing BN through pre-training and CV-DD introducing batchspecific BN recalculation, leading to more robust, generalizable condensed datasets.

##### 2.2.3 Soft Labels

In the SRe2L series and beyond, soft labels [\[54\]](#page-18-2) have emerged as a critical component for the significant performance improvement, encoding rich semantic information that surpasses the limitations of traditional one-hot labels. These nuanced labels enhance diversity, generalization, and scalability, addressing core challenges in large-scale dataset condensation. Highlighting the centrality of soft labels in efficient learning, Qin et al. [\[55\]](#page-18-3) proposed to explore the role of soft labels in dataset distillation. Through extensive experiments, they demonstrated that early-stopped expert models generate soft labels encoding structured semantic information crucial for data-efficient learning.

Subsequent research has advanced both understanding and implementations on soft labels. Sun et al. [\[64\]](#page-18-12) proposed the Realistic, Diverse, and Efficient Dataset Distillation (RDED) method, which employs Fast Knowledge Distillation [\[80\]](#page-18-28) to generate region-level soft labels. By aggregating diverse features and representations, RDED moves beyond traditional patch selection methods, capturing more intricate semantic information. Extending these principles, Hu et al. [\[81\]](#page-18-29) introduced Focused Dataset Distillation (FocusDD), leveraging pre-trained Vision Transformers (ViT) [\[82\]](#page-18-30) to identify critical image patches. Their approach combines these key regions with downsampled background information to create high-quality distilled datasets. Notably, FocusDD extends dataset distillation beyond classification tasks to object detection, showcasing the versatility of soft label approaches. Zhong et al. [\[83\]](#page-18-31) further advanced the field by incorporating DMs for soft label generation and synthetic image construction. Unlike earlier random region selection techniques, their method employs differential loss computation guided by text prompts to identify classrelevant regions, offering a powerful alternative on patch selection. Those advances show a clear progression from simple patch selection to sophisticated region processing using Vision Transformers and Diffusion Models, significantly enhancing the quality of distilled datasets.

Addressing practical implementation challenges, Shang et al. [\[84\]](#page-18-32) proposed the GIFT framework. This approach combines a label refinement module—merging smoothed hard labels with teacher-generated soft labels—and a mutual information-based loss function. GIFT significantly enhances the effectiveness of distilled datasets while maintaining minimal computational overhead, demonstrating its adaptability across diverse scenarios. Xiao et al. [\[85\]](#page-18-33) tackled the substantial storage demands of soft labels in large-scale distillation, which can exceed 30 times the size of the distilled dataset. Their solution leverages class-wise batching and supervision during image synthesis to enhance withinclass diversity, enabling effective soft label compression through simple random pruning. This strategy dramatically reduces storage requirements while improving performance on large-scale datasets, making soft label-based methods more practical for real-world applications.

**Stage Summary** This progression in soft label methodologies represents a fundamental advancement in dataset distillation. Future research directions include exploring efficient and adaptive data and/or patch generation, investigating multi-modal soft label fusion. These developments will be crucial for extending the applicability of dataset distillation to more challenging datasets with large-scale and high resolution, as well as emerging domains such as self-supervised learning and large foundation models.

## 2.3 Efficiency and Effectiveness in Dataset Distillation

Recent innovations have introduced strategies that reduce computational overhead, optimize resource utilization, and enhance representational diversity, effectively addressing challenges such as high memory requirements, overfitting, and scalability. These advancements significantly improve performance on large-scale datasets.

### 2.3.1 Selective Dataset Distillation

Recent advances highlight the necessity of identifying and optimizing key components that contribute most to model performance. We present a unified theoretical framework for selective approaches across multiple dimensions, demonstrating how strategic selection and weighting of elements, from low-level features to high-level representations, enhance distillation efficiency. Formally, let  $\mathcal{D} = (x_i, y_i)_{i=1}^N$ <br>denote the original dataset and  $\mathcal{S} = (\tilde{x}_j, \tilde{y}_j)_{j=1}^M$  represent the synthetic dataset, where  $M \ll N$ . The selective distillation objective can be formulated as:

$$
\min_{\mathcal{S}} \mathbb{E}_{\theta_0} \sum_{t=0}^{T-1} \omega_t \sum_{d \in \mathcal{D}} \omega_d \mathcal{L}_d(\mathcal{S}, \mathcal{T}) \tag{8}
$$

where  $\omega_t$  represents selection weights across different timesteps of the training trajectory,  $\omega_d$  denotes selection weights for different dimensions (e.g., pixel, color, parameter, original samples),  $\mathcal{L}_d$  denotes the dimension-specific loss function for measuring distillation quality.

**Selective Pixel Enhancement** Effectively identifying and enhancing discriminative pixels is crucial for dataset quality, especially when addressing inter-class variability and nondiscriminative regions. Recent efforts have refined pixelwise feature representation to emphasize informative pixels while suppressing redundant or misleading patterns. Wang et al. [\[86\]](#page-18-34) proposed the Emphasizing Discriminative Features (EDF) framework, which leverages Common Pattern

Dropout (CPD) to filter out non-salient pixels and Discriminative Area Enhancement (DAE) to intensify attention on essential regions using Grad-CAM [\[87\]](#page-18-35) activation maps. By adaptively selecting and enhancing discriminative regions, EDF ensures synthesized datasets prioritize high-salience regions, leading to improved generalization ability.

**Selective Color Optimization** Color redundancy often leads to inefficient dataset utilization, reducing the effectiveness of synthetic samples. Yuan et al. [\[88\]](#page-18-36) addressed this issue by introducing AutoPalette, a framework designed to selectively optimize color usage while preserving image features. AutoPalette condenses images into reduced color palettes via a palette network, balancing pixel distributions with maximum color loss and palette balance loss. A colorguided initialization strategy further ensures diverse, informative synthetic samples, improving overall distillation performance. This method uniquely balances color diversity with feature preservation. Compared to EDF [\[86\]](#page-18-34) focusing on spatial region selection, AutoPalette targets redundancy within the color space, providing a complementary approach. However, its effectiveness may be limited when color is not a critical feature in datasets.

**Selective Parameter Optimization** Conventional distillation methods often treat each network parameter equally, overlooking their varying contributions to the learning process. Li et al. [\[89\]](#page-18-37) highlighted this limitation and proposed the Adaptive Dataset Distillation (IADD) framework, which selectively assigns weights to parameters based on their contribution to learning. This approach prioritizes critical parameters while maintaining challenging ones, resulting in improved performance and robust parameter matching across iterations.

**Sample Selection Strategies** Dataset distillation methods often face significant challenges in effectively leveraging large-scale training data. Sample selection methods tackle this problem through two main strategies: *selection and pruning*, which reduce dataset size, and *prioritization*, which dynamically emphasizes the most informative samples.

Selection and pruning methods aim to retain samples that best represent the underlying data distribution, enhancing the diversity and informativeness of the synthetic dataset. Liu et al. [\[90\]](#page-18-38) introduced DREAM, a clusteringbased sample selection method designed to capture representative distributions, significantly enhancing training efficiency. Building on this, DREAM+ [\[91\]](#page-18-39) incorporates bidirectional matching to balance gradient and feature alignment, further improving stability and efficiency. Moser et al. [\[92\]](#page-18-40) proposed the "Prune First, Distill After" framework, which combines dataset pruning with loss-value-based sampling. This approach selects informative samples, reducing redundancy while maintaining performance. Similarly, Bi-level Data Pruning (BiLP) [\[93\]](#page-18-41) integrates preemptive pruning based on empirical loss and adaptive pruning leveraging causal effects, achieving up to significant data reduction with little degradation in performance.

Prioritization methods focus on dynamically ranking and weighting samples during training to emphasize their relative importance. Wang et al. [\[94\]](#page-18-42) introduced Sample Difficulty Correction (SDC), which prioritizes simpler samples based on gradient norms. To adapt dataset difficulty dynamically, Lee et al. [\[95\]](#page-18-43) proposed SelMatch, which employs

selection-based initialization and partial updates to finetune synthetic datasets. By tailoring the training process to specific architectures and datasets, SelMatch improves generalization and convergence. Chen et al. [\[96\]](#page-18-44) extended prioritization by introducing an adversarial prediction matching framework. This approach leverages teacher-student disagreements to identify informative samples, enhancing robustness across diverse architectures. Tukan et al. [\[97\]](#page-18-45) proposed adaptive sampling and initialization strategies. Focusing on data prioritization, Li et al. [\[98\]](#page-18-46) proposed Prioritize Alignment in Dataset Distillation (PAD), improving distillation effectiveness through two key strategies: Data Filtering, which dynamically selects high-complexity samples based on EL2N scores [\[99\]](#page-18-47) to retain the most informative data, and Layer Confinement, which restricts the distillation process to deeper model layers, refining the prioritization of critical features while reducing noise from low-level representations.

**Stage Summary** Sample selection methods enhance dataset distillation through two main approaches: reducing dataset size via selection and pruning, and optimizing training dynamics via prioritization. Selection and pruning methods, such as DREAM+ and BiLP, reduce redundancy while preserving diversity and representational value. In contrast, prioritization-based approaches, like SDC and SelMatch, dynamically adjust sample weights, improving generalization and training efficiency. Future research could explore hybrid frameworks integrating various selection strategies for large-scale dataset distillation.

### 2.3.2 Lossless Distillation

Conventional dataset distillation methods often struggle with achieving lossless performance on complex datasets due to the challenges posed by various factors, such as interclass variability and non-discriminative features. To address these issues, several recent approaches focus on achieving near-lossless or lossless performance by emphasizing critical patterns and refining trajectory alignment.

Guo et al. [\[100\]](#page-18-48) laid the foundation for lossless distillation by introducing DATM, which dynamically aligns the difficulty of generated patterns with the synthetic dataset size. Specifically, early trajectories (easy patterns) are optimal for low-IPC settings, while late trajectories (hard patterns) benefit larger synthetic datasets. By controlling trajectory ranges and integrating soft label learning with sequential generation, DATM achieves near-lossless results across benchmarks such as CIFAR-10 and CIFAR-100. Building on DATM, Zhou et al. [\[101\]](#page-18-49) proposed M-DATM to address label inconsistencies and further optimize trajectory matching for more challenging datasets like Tiny ImageNet. Through deliberate refinements, M-DATM demonstrated superior performance, securing first place in the Fixed IPC Track at the ECCV-2024 Data Distillation Challenge. Complementing trajectory-based approaches, Wang et al. [\[86\]](#page-18-34) extended dataset distillation to high-variability datasets through feature importance optimization. By refining discriminative patterns, their method, EDF, demonstrated lossless performance on challenging subsets of ImageNet-1K, setting a new benchmark for dataset distillation.

**Stage Summary** These advancements collectively demonstrate the efficacy of adaptive selection strategies in lossless

dataset distillation, where methods dynamically identify and prioritize the most informative and discriminative patterns while systematically minimizing the impact of redundant or less relevant information. Moving forward, dynamic and context-aware adaptation techniques are expected to further enhance scalability and interpretability, enabling effective lossless distillation for large-scale datasets.

### 2.3.3 Diversity of Distilled Data

While importance-guided methods focus on selecting and prioritizing original samples, maintaining diversity in synthetic data remains a critical challenge in dataset distillation. Recent approaches address this through curriculum learning, feature compensation, and semantic matching strategies. Wang et al. [\[102\]](#page-18-50) introduced CUDD, which progressively increases synthetic data complexity through curriculum learning. By focusing on samples misclassified by student models, CUDD optimizes classification, regularization, and adversarial objectives to maintain diversity across training stages. To address inter-class feature redundancy, Zhang et al. [\[103\]](#page-18-51) developed INFER with a Universal Feature Compensator (UFC). This "one instance for all classes" approach achieves superior performance on ImageNet through optimized feature diversity. Similarly, DELT [\[104\]](#page-18-52) enhances diversity through subtask partitioning with varying optimization schedules. Du et al. [\[105\]](#page-19-0) observed limitations in instance-wise synthesis and proposed DWA (Directed Weight Adjustment), using variancebased regularization for batch-level diversity. DSDM [\[106\]](#page-19-1) further preserves semantic diversity by matching prototypes and covariance matrices of class distributions through pretrained feature extractors.

**Stage Summary** These diversity-focused approaches complement original sample importance methods. While importance methods reduce redundancy at the original dataset level, diversity enhancement ensures richness at the distilled data level. Future research should explore hybrid frameworks that seamlessly integrate selection, prioritization, and diversity enhancement to create more generalizable, scalable dataset distillation pipelines.

#### 2.3.4 Augmentation Strategies

Augmentation techniques expand feature and label spaces to enhance the learning dynamics of distillation, promoting greater diversity and representational richness. These methods ensure that synthetic data captures a broad range of features while remaining adaptable to different factors.

**Model Augmentation** Building on diversity-focused methods, model augmentation techniques expand feature spaces during dataset distillation, directly addressing the need for richer representations. Zhang et al. [\[107\]](#page-19-2) proposed integrating two model augmentation techniques within a gradientmatching framework: early-stage models and weight perturbation. Early-stage models provide a diverse feature space with larger gradient magnitudes, offering enhanced flexibility and richer guidance for the distillation process. Weight perturbation further expands the feature space by introducing a normalized random vector, sampled from a Gaussian distribution, to the model weights. This augmentation fosters diversity by enabling the exploration of a broader range of feature representations, which, in turn, enhances the effectiveness of the distillation process. Together, these techniques allow for both efficient distillation and the generation of more representative synthetic datasets.

**Label Augmentation** Beyond feature spaces, label augmentation introduces diversity through enriched label representations. Kang et al. [\[108\]](#page-19-3) introduced Label-Augmented Dataset Distillation (LADD) to leverage label augmentation for improved diversity and efficiency. LADD operates in two stages: first, synthetic images are generated using existing distillation algorithms; second, an image sub-sampling algorithm generates multiple local views for each synthetic image. A pre-trained labeler then produces dense semantic labels for these local views. During deployment, LADD combines global view images with their original labels and local view images with the newly generated dense labels. This dual-label strategy enhances storage efficiency, reduces computational overhead, and provides diverse learning signals, leading to improved robustness and performance across a range of architectures.

**Stage Summary** The advancement of augmentation strategies in dataset distillation hinges on overcoming critical research challenges. A key open problem is the automatic discovery of optimal augmentation strategies, which could benefit from reinforcement learning approaches [\[109\]](#page-19-4) to dynamically tailor augmentation techniques to specific tasks and model architectures. Achieving this requires a deeper understanding of how different augmentation types interact with various model architectures, ensuring that strategies are not only effective but also generalizable and transferable across diverse domains and tasks.

### 2.3.5 Extreme Compression Techniques

Extreme compression techniques address storage constraints by drastically reducing dataset sizes while preserving training effectiveness. Shul et al. [\[110\]](#page-19-5) introduced Poster Dataset Distillation (PoDD), which compresses entire datasets into a single poster image, achieving extreme compactness with less than one image per class. By leveraging semantic class ordering and efficient label management, PoDD balances high compression with robust training performance, making it well-suited for resource-constrained applications. Future research could explore expanding extreme compression strategies to large-scale datasets while improving adaptability across different model architectures.

## 2.4 Distillation in Non-IID and Non-centralized Settings

As machine learning systems increasingly deploy in dynamic environments, the traditional assumptions of independent and identically distributed (IID) data become increasingly inadequate. Modern applications that are ranging from edge computing and mobile networks to decentralized systems, demand models that can adapt to heterogeneous, shifting data landscapes. Dataset distillation correspondingly evolve to address these complex challenges by handling non-IID data distributions, ensuring fairness and robustness in open-world scenarios, and accommodating decentralized learning architectures like federated learning.

### 2.4.1 Addressing Non-IID Challenges

Adapting dataset distillation methods to real-world applications requires addressing Non-IID challenges, including handling out-of-distribution (OOD) data [\[111\]](#page-19-6), mitigating biases [\[112\]](#page-19-7), ensuring fairness [\[113\]](#page-19-8), and supporting selfsupervised [\[76\]](#page-18-24), [\[114\]](#page-19-9) and transfer learning [\[115\]](#page-19-10). Recent advancements in these areas extend the applicability of dataset distillation beyond traditional IID settings, enabling robust performance in dynamic and decentralized environments.

**Out-Of-Distributions** In open-world scenarios, models must reliably identify and handle data samples that deviate from their training distribution. Traditional dataset distillation methods overlook this, focusing on IID data. To address this gap, Ma et al. proposed Trustworthy Dataset Distillation (TrustDD) [\[116\]](#page-19-11), which integrates OOD detection into the distillation process. By generating synthetic outliers through Pseudo-Outlier Exposure (POE), TrustDD eliminates the need for curated OOD datasets, enabling robust OOD detection without compromising in-distribution (InD) accuracy or computational efficiency.

**Biased DD and Fairness DD** Dataset biases, such as color or background amplification, can significantly undermine the representational quality of distilled datasets. Lu et al. [\[117\]](#page-19-12) analyzed how biases propagate through the distillation process and proposed mathematical frameworks for addressing biased dataset distillation. Cui et al. [\[118\]](#page-19-13) introduced a reweighting scheme combining supervised contrastive learning and kernel density estimation to effectively mitigate these biases. To ensure fairness in synthetic datasets, Zhou et al. proposed FairDD [\[119\]](#page-19-14), which aligns datasets with protected attribute groups through synchronized matching, mitigating majority group dominance. Similarly, Zhao et al. introduced Long-tailed Aware Dataset Distillation (LAD) [\[120\]](#page-19-15), which addresses imbalances in longtailed datasets by incorporating Weight Mismatch Avoidance and Adaptive Decoupled Matching. These methods demonstrate the potential for bias-aware dataset distillation to improve fairness and tail-class performance.

**Self-supervised and Transfer Learning** While most dataset distillation research has focused on supervised learning, self-supervised and transfer learning [\[76\]](#page-18-24), [\[114\]](#page-19-9), [\[115\]](#page-19-10) remain underexplored. Lee et al. [\[121\]](#page-19-16) proposed KRR-ST, which employs kernel ridge regression and mean squared error objectives to reduce randomness in the distillation process. This method facilitates efficient pretraining with unlabeled data, significantly lowering computational costs. Expanding on this, Yu et al. [\[122\]](#page-19-17) introduced an enhanced self-supervised DD framework that builds upon KRR-ST through three key innovations: image and representation parameterization, predefined augmentation, and approximation networks. These refinements improve crossarchitecture generalization, transfer learning performance, and storage efficiency. Furhter, Joshi et al. introduced MKDT [\[123\]](#page-19-18), a two-stage method that stabilizes SSL trajectories by first training student models via knowledge distillation from SSL-trained teachers. In alignment with these stabilized trajectories, MKDT achieves substantial performance gains in SSL pretraining and downstream tasks.

### 2.4.2 Addressing Non-centralized Challenges

In the era of distributed machine learning, federated learning (FL) enables collaborative, privacy-preserving model training across decentralized environments. However, this paradigm introduces unique challenges, such as communication overhead, non-IID data, privacy concerns, and decentralized setups. Recent advancements in dataset distillation have addressed these challenges, categorized into communication efficiency, heterogeneity handling, privacy preservation, and decentralized/edge-focused scenarios.

**Communication Efficiency** Minimizing communication overhead is critical for scalable FL, especially in resourceconstrained environments. Distilled One-Shot Federated Learning (DOSFL) [\[124\]](#page-19-19) introduced the idea of transmitting compact synthetic datasets in a single communication round, drastically reducing bandwidth usage. Building on this, FedSynth [\[125\]](#page-19-20) replaced model updates with synthetic datasets, ensuring compatibility with standard FL frameworks. DENSE [\[126\]](#page-19-21) and FedMK [\[127\]](#page-19-22) avoided model aggregation by generating synthetic data that encapsulates local knowledge, while FedD3 [\[128\]](#page-19-23) optimized one-shot communication for edge scenarios. Further advancements, such as FedCache 2.0 [\[129\]](#page-19-24), integrated distillation with knowledge caching to enhance efficiency in edge-device communication.

**Handling Heterogeneity** Data and model heterogeneity pose fundamental challenges in federated learning, where clients may have vastly different data distributions, model architectures, and learning characteristics. FedDM [\[130\]](#page-19-25) and DYNAFED [\[131\]](#page-19-26) tackled data heterogeneity by generating compact pseudo datasets that align with local distributions. While FedDM iteratively refined synthetic datasets via distribution matching, DYNAFED leveraged early pseudo datasets with trajectory matching, prioritizing efficiency and privacy. FedAF (Aggregation-Free) [\[132\]](#page-19-27) mitigated client drift in non-IID settings by employing sliced Wasserstein regularization and collaborative condensation to harmonize client contributions. HFLDD [\[133\]](#page-19-28) grouped clients into heterogeneous clusters, allowing cluster headers to aggregate distilled datasets and create approximately IID data, improving training efficiency in non-IID environments.

**Privacy Preservation** Privacy concerns in FL have driven the integration of dataset distillation techniques that limit information sharing. FedDGM (Federated Dataset Distillation with Deep Generative Models) [\[134\]](#page-19-29) utilized serverside pre-trained generators to distill datasets in latent space, avoiding the need to share raw data or updates while maintaining high performance. QUICKDROP [\[135\]](#page-19-30) extended this concept by embedding dataset distillation into federated unlearning, reducing computational costs without sacrificing privacy or performance. Xu et al. [\[136\]](#page-19-31) applied the principle of least privilege (PoLP), where clients share only essential knowledge through local-global dataset distillation, further safeguarding sensitive information.

**Decentralized and Edge-Focused Scenarios** In fully decentralized setups and resource-constrained edge environments, new frameworks have been developed to tackle the challenges of operating *without* a central server and the limitations of on-device training resources. DESA (Decentralized Federated Learning with Synthetic Anchors) [\[137\]](#page-19-32) introduced synthetic anchors—generated datasets approx-

Image /page/11/Figure/0 description: This figure illustrates a three-stage process for training and evaluating a neural network, likely for image classification. The first stage, 'Distillation Stage,' shows an 'Original Train Set' with various images, which are then processed through 'Distillation' (mentioning DSA, TESLA, AR21, etc.) to create a 'Distilled Train Set.' This stage also lists datasets like CIFAR10/100, TinyImageNet, and ImageNet-1K. The second stage, 'Training Stage,' begins with 'Vanilla Training' using a 'Random Initialized Network' and specifies training parameters like 'SGD Optimizer' and 'LR Decay.' This network is then trained to become a 'Trained Network,' with examples of architectures like 'ConvNet' and 'ResNet18.' The third stage, 'Attacking Stage,' evaluates the trained network on an 'Original Test Set' to determine 'Clean Accuracy' and on a 'Perturbed Test Set' to assess 'Robust Accuracy.' The 'Perturbed Test Set' is generated through an 'Adversarial Attack,' listing methods such as 'FGSM,' 'PGD,' 'CW,' and 'Auto.' The figure also includes a formula for epsilon: |epsilon| = 2/255, 4/255, 8/255, ...

Fig. 5: Overview of DD-RobustBench. Image from [\[143\]](#page-19-33).

imating global data distributions—to align local features across clients, enabling knowledge sharing without a central server. For incremental learning in edge environments, Rub et al. [\[138\]](#page-19-34) proposed integrating dataset distillation with adaptive model sizing, meeting the challenges of TinyML and on-device training while maintaining performance.

**Others** Additional advancements include DKKT [\[139\]](#page-19-35), which enriched distilled datasets using deep support vectors, achieving robust performance with just 1% of the original dataset. Distributed Boosting (DB) [\[140\]](#page-19-36) enhanced the performance of prior methods through partitioning, soft-labeling, and integration strategies in distributed computing environments, setting new benchmarks on largescale datasets such as ImageNet-1K. HFLDD [\[133\]](#page-19-28) integrates dataset distillation into a federated framework by organizing clients into heterogeneous clusters. Within each cluster, clients transmit distilled datasets to cluster headers, which aggregate the distilled data to construct approximately IID datasets, significantly improving training efficiency and addressing non-IID data challenges.

### 2.5 Robustness in Dataset Distillation

As dataset distillation becomes increasingly applied in various scenarios, ensuring its robustness has become a critical focus. Synthetic datasets derived through distillation must be resilient to adversarial attacks [\[141\]](#page-19-37), backdoor threats [\[142\]](#page-19-38), and privacy breaches to ensure their reliability and effectiveness in real-world applications. However, those requirements introduce unique vulnerabilities; for example, small perturbations or manipulations can disproportionately affect their performance. To address these challenges, recent advancements have introduced robust evaluation benchmarks, innovative defense strategies, and in-depth explorations of vulnerabilities in distillation pipelines.

#### 2.5.1 Adversarial Attack

Adversarial robustness [\[141\]](#page-19-37) in dataset distillation remains a relatively underexplored topic. Recent studies have laid the groundwork for benchmarking and developing robust distillation techniques to enhance security.

To assess the vulnerability of dataset distillation, Wu et al. [\[143\]](#page-19-33) introduced DD-RobustBench, a comprehensive benchmark for evaluating the robustness of dataset distillation methods, including techniques like TESLA, DREAM, SRe2L, and D4M, across datasets such as CIFAR-10 and ImageNet-1K. Their findings revealed that models trained on distilled datasets could exhibit superior robustness compared to those trained on original data under low IPC (images per class) settings, though an inverse relationship between robustness and IPC challenged the assumption that more distilled images always improve security. Following this, Zhou et al. [\[144\]](#page-19-39) proposed BEARD, a unified evaluation framework introducing metrics like Robustness Ratio (RR), Attack Efficiency Ratio (AE), and Comprehensive Robustness-Efficiency Index (CREI) to assess robustness and efficiency holistically. Together, these benchmarks provide critical insights into balancing robustness and efficiency in adversarially robust distillation. Together, these benchmarks establish a foundation for evaluating and improving the security of distillation techniques.

Beyond benchmarking, recent methods have focused on actively enhancing the adversarial robustness of distilled datasets. Xue et al. [\[145\]](#page-19-40) proposed Geometric regUlarization for Adversarial Robust Dataset (GUARD), a novel method incorporating curvature regularization to improve resilience against adversarial attacks. By minimizing the curvature of the loss function, GUARD reduces the upper bound of adversarial loss without significant computational overhead. This method achieves enhanced adversarial robustness with minimal computational overhead, paving the way for more secure and reliable distillation processes.

#### 2.5.2 Backdoor Attack

Dataset distillation has been revealed to be vulnerable to backdoor attacks [\[142\]](#page-19-38), where attackers can embed hidden triggers that cause targeted misclassifications even after the dataset distillation process [\[146\]](#page-19-41). To handle this challenge, Chung et al. [\[147\]](#page-19-42) analyzed backdoor attacks using a kernel method framework, explaining the resilience of certain backdoors in distilled datasets. They proposed two novel trigger generation strategies: simple-trigger, which exploits the observation that larger triggers reduce the generalization gap, and relax-trigger, which minimizes both conflict and projection losses while maintaining small generalization gaps. Empirical evaluations demonstrated that both methods evade existing backdoor detection and defense techniques effectively, with relax-trigger exhibiting superior robustness across eight tested defenses.

Extending backdoor attack risks beyond Euclidean data, Wu et al. [\[148\]](#page-19-43) introduced the first backdoor attack framework for graph condensation, named BGC. BGC injects malicious triggers into graph structures and optimizes them iteratively throughout the condensation process. To maximize attack effectiveness under constrained resources, BGC employs a representative node selection mechanism to identify and poison key nodes strategically. Experiments confirmed BGC's high attack success rates and robust performance, even against multiple defense strategies, highlighting the critical need for developing secure graph condensation.

#### 2.5.3 Beyond Adversarial and Backdoor Attacks

While adversarial and backdoor attacks represent critical security concerns in dataset distillation, recent studies have uncovered additional vulnerabilities that demand attention. These include privacy-related threats such as membership inference attacks, which jeopardize the confidentiality of the training process and expose sensitive data. Chen et al. [\[149\]](#page-19-44) conducted the first comprehensive study on the security properties of dataset distillation methods, specifically analyzing their vulnerability to membership inference attacks

[\[150\]](#page-19-45). These attacks expose significant privacy concerns, as adversaries can infer whether a particular sample was used in the training process.

## 2.6 Model-agnostic Solutions

A major challenge in dataset distillation is the limited crossarchitecture generalization capability of synthetic datasets. This issue arises when the distillation process becomes overly specialized to the architecture used during training, limiting the versatility of distilled datasets. Recent efforts have tried addressing this limitation by developing modelagnostic solutions that enhance the adaptability and transferability of synthetic datasets across diverse architectures. **Gradient Balancing and Semantic Alignment** Moon et al. [\[151\]](#page-19-46) introduced Heterogeneous Model Dataset Condensation (HMDC), a pioneering approach to creating universally applicable condensed datasets. HMDC tackles the gradient imbalance problem, where contributions from different architectures vary due to gradient magnitude discrepancies, by employing a Gradient Balance Module (GBM). This module normalizes gradients to ensure equal contributions from heterogeneous models. To address misalignment, HMDC incorporates Mutual Distillation (MD) with Spatial-Semantic Decomposition (SSD), aligning both semantic and spatial features across models. These innovations ensure consistent feature representation and robust generalization, making HMDC a benchmark for cross-architecture adaptability.

**Mitigating Inductive Bias** To combat architecture-specific inductive biases, Zhao et al. proposed two complementary methods: ELF [\[152\]](#page-19-47) and MetaDD [\[153\]](#page-19-48). ELF leverages external supervision by using bias-free intermediate features extracted from original datasets to guide the training of evaluation models. In contrast, MetaDD takes an internal approach, disentangling features into architecture-invariant meta features and architecture-variant heterogeneous features. This ensures that generalizable features dominate the distillation process. While ELF emphasizes external supervisory signals to counteract overfitting, MetaDD focuses on directly optimizing the feature space. Together, these approaches significantly enhance the cross-architecture generalization of synthetic datasets.

**Architectural Diversity and Ensemble Techniques** Recent advances have focused on leveraging multiple architectures to enhance robustness. Zhou et al. [\[154\]](#page-19-49) proposed a model pool framework to balance architectural diversity and training stability. By combining multiple similar architectures and selecting a primary model most of the time, this method ensures robust performance across various architectures. Knowledge distillation further aligns student and teacher models, improving generalization. Similarly, Zhong et al. [\[155\]](#page-19-50) addressed architectural overfitting with a set of complementary techniques. These include a DropPath variant for implicit subnetwork ensembling, reverse knowledge distillation where smaller models teach larger ones, and optimizations like periodic learning rate schedules and advanced data augmentations.

**Stage Summary** While these works have made significant progress in addressing cross-architecture generalization, challenges persist in the field. The scalability to larger and more diverse architectures, such as Transformers [\[156\]](#page-19-51), remains a crucial concern. Future research in this area might explore adaptive architecture-aware distillation strategies that dynamically adjust to different model architectures.

## 3 EMERGING APPLICATIONS AND DOMAINS

Dataset distillation has extended beyond image classification to complex data types such as temporal sequences, multi-modal data, and domain-specific challenges like medical imaging. These advancements underscore its adaptability across fields, including video understanding, natural language processing, and multi-modal learning. The following subsections summarize key applications, demonstrating how distillation techniques tackle domain-specific challenges while leveraging core methodologies.

### 3.1 Temporal Domain

The temporal domain introduces unique challenges for dataset distillation due to the sequential and timedependent nature of video and audio data. Recent advancements have focused on designing frameworks that effectively capture temporal dynamics, ensuring robust representation and efficient compression across these modalities. **Video Domain** Wang et al. [\[157\]](#page-19-52) conducted the first systematic study on video dataset distillation, classifying temporal compression methods into four dimensions: synthetic frames, real frames, segments, and interpolation algorithms. They proposed a two-stage framework that disentangles static and dynamic information, integrating single-frame static memory with dynamic memory blocks to synthesize realistic video data. Building on this, Chen et al. [\[158\]](#page-20-0) introduced an efficient temporal condensation strategy incorporating slide-window sampling and adaptive trajectory matching, significantly improving performance on action recognition benchmarks while reducing training costs.

**Audio Domain** Jiang et al. [\[159\]](#page-20-1) introduced DDFAD for audio data, leveraging Fused Differential MFCC (FD-MFCC) to combine traditional MFCC features with firstand second-order derivatives, improving feature richness. Using the Matching Training Trajectory technique, DDFAD achieves performance comparable to full datasets while reducing computational and storage demands.

**Sequential Data** Zhang et al. [\[160\]](#page-20-2) introduce Tucker Decomposition based Dataset Distillation (TD3), a novel Tucker Decomposition-based framework for dataset distillation in sequential recommendation, demonstrating an innovative approach to generating compact yet expressive synthetic sequence summaries.

### 3.2 Multi-modal Dataset Distillation

Recent advancements have extended dataset distillation to multi-modal tasks, addressing the unique challenges of integrating vision, language, and audio modalities. Visionlanguage learning [\[161\]](#page-20-3), [\[162\]](#page-20-4) and audio-visual learning [\[163\]](#page-20-5) have emerged as prominent areas of exploration. Wu et al. [\[161\]](#page-20-3) introduced a bi-trajectory matching framework for vision-language distillation, aligning image-text correspondences using contrastive loss and trajectory matching. By incorporating Low-Rank Adaptation (LoRA) [\[164\]](#page-20-6), this method enhances efficiency while maintaining robust

cross-modal representations. Complementing this, Xu et al. [\[162\]](#page-20-4) proposed Low-Rank Similarity Mining (LoRS), which distills similarity matrices alongside image-text pairs. Using low-rank factorization, LoRS efficiently approximates similarity matrices while refining anchors for contrastive learning, improving representation and compression.

Expanding on these developments, Kushwaha et al. [\[163\]](#page-20-5) introduced audio-visual dataset distillation, a technique that compresses large audio-visual datasets into compact synthetic datasets while preserving cross-modal relationships. Their framework builds on vanilla distribution matching by incorporating two novel losses: implicit cross-matching and cross-modal gap matching, ensuring better alignment between synthetic and real data.

### 3.3 Medical Domain

In medical imaging, Li et al. [\[165\]](#page-20-7) examined dataset distillation's feasibility across nine diverse medical datasets, demonstrating its ability to preserve diagnostic details while reducing dataset sizes. They observed that larger interclass variations yield better results and proposed random selection as a heuristic predictor of distillation efficacy. Yu et al. [\[166\]](#page-20-8) addressed training instability caused by SGD oscillations with a progressive trajectory matching strategy. By gradually increasing trajectory step size and introducing a dynamic overlap mitigation module with Maximum Mean Discrepancy [\[167\]](#page-20-9), Yu et al. achieved stable training and notable performance improvements, especially under low IPC conditions. Complementing these efforts, Li et al. [\[168\]](#page-20-10) proposed InfoDist, a privacy-preserving framework using class-conditional latent diffusion models to generate synthetic histopathology datasets. By selecting informative images based on modular centrality and enhancing performance through contrastive learning, InfoDist safeguards data privacy while ensuring competitive accuracy.

### 3.4 Other Applications

**Scientific Discovery** Dataset distillation has demonstrated its versatility in tackling scientific challenges, such as galaxy morphology analysis [\[169\]](#page-20-11). Guan et al. [\[169\]](#page-20-11) applied dataset distillation to galaxy morphology analysis, proposing Self-Adaptive Trajectory Matching (STM) to improve upon MTT. STM uses statistical hypothesis testing to adaptively monitor validation loss, ensuring efficient stopping criteria and reduced hyperparameter tuning. Their work includes a curated high-confidence version of the Galaxy Zoo 2 dataset [\[170\]](#page-20-12), successfully distilling morphological features and achieving superior performance.

**Object Detection and Super-Resolution** For object detection, Qi et al. [\[171\]](#page-20-13) introduced DCOD, the first dataset condensation framework for this task, delivering competitive results on benchmarks like Pascal VOC [\[172\]](#page-20-14) and MS COCO [\[173\]](#page-20-15). Dietz et al. [\[174\]](#page-20-16) demonstrate the first comprehensive exploration of dataset distillation for image super-resolution (SR) [\[175\]](#page-20-17), successfully reducing the original dataset size by 91.12% while maintaining comparable model performance through both pixel-space and latentspace distillation techniques, thereby offering a promising memory- and computation-efficient approach.

**Enhancing Data Quality** Dataset distillation has shown promise in noise-related challenges and improving dataset quality. Cheng et al. [\[176\]](#page-20-18) proposed leveraging dataset distillation as a novel denoising tool for learning with noisy labels, addressing challenges like feedback loops in traditional noise evaluation strategies. Through extensive experiments with methods like DATM, DANCE, and RCIG, they demonstrate the effectiveness of dataset distillation in removing random and natural noise, though it struggles with structured asymmetric noise. This approach also improves training efficiency and privacy by avoiding direct use of noisy data and enabling offline processing. Building upon the insights into noisy label handling through distillation, Wu et al. [\[177\]](#page-20-19) proposed Trust-Aware Diversion (TAD). Rather than just using distillation as a denoising tool [\[176\]](#page-20-18), TAD introduces a dual-loop optimization framework that actively manages noisy labels during the distillation process itself. In TAD, an outer loop separates data into trusted and untrusted spaces to guide distillation, while an inner loop recalibrates untrusted samples to maximize their utility. UniDetox [\[178\]](#page-20-20) utilized dataset distillation approach to universally mitigate toxic content in large language models by systematically refining training datasets, thereby reducing harmful biases while preserving model performance across different domains.

## 4 PERFORMANCE COMPARISON

Dataset distillation methods have advanced significantly, as demonstrated in Table [1,](#page-2-0) which compares state-ofthe-art techniques across CIFAR-10, CIFAR-100, Tiny ImageNet, and ImageNet-1K under different IPC settings (IPC=1,10,50,100,200), as well as Table [3,](#page-15-1) which compares state-of-the-art techniques across ImageNet-21K under different IPC settings. These evaluations provide insights into efficiency and scalability, offering a comprehensive view of dataset distillation capabilities. Over the past two years, the field has seen rapid progress, particularly in low-IPC scenarios, where newer approaches substantially improve upon early methods. For instance, on CIFAR-10 with IPC=1, early techniques such as DC [\[15\]](#page-17-14) achieved only 28.3% accuracy, whereas recent advances like AutoPalette [\[88\]](#page-18-36) have pushed this performance to 58.6%, demonstrating a significant 30.3% absolute improvement. Similar trends are observed across larger-scale datasets. On CIFAR-100, the performance has improved from 12.8% (DC) to 38.0% (AutoPalette) with IPC=1, while on ImageNet-1K, recent methods like D4M [\[61\]](#page-18-9) achieve 66.5% accuracy with IPC=100, showcasing the scalability of modern distillation approaches. Furthermore, the emergence of diverse methodologies, including SRe2L, Selective-based, and Diversity-driven approaches, has contributed to these improvements. As shown in Table [3,](#page-15-1) even on the challenging ImageNet-21K dataset, modern methods like CUDD [\[102\]](#page-18-50) achieve promising results, reaching 34.9% accuracy with IPC=20. These advancements demonstrate the field's progress in handling increasingly complex and large-scale datasets while maintaining efficiency through low IPC settings.

### 4.1 Impact of IPC Settings and Practical Considerations

The relationship between IPC settings and model performance exhibits distinct patterns across different scales of datasets and methodologies. Analysis of recent results reveals three characteristic regions of IPC impact:

(1) Low IPC regime (IPC  $\leq$  10) demonstrates the most dramatic improvements in performance. On CIFAR-10, D3M [\[62\]](#page-18-10) shows a substantial improvement from 35.9% (IPC=1) to 58.6% (IPC=10), while DANCE [\[29\]](#page-17-28) improves from 47.1% to 70.8%. Similar patterns are observed on CIFAR-100, where methods like EDC [\[78\]](#page-18-26) achieve significant gains from IPC=1 to IPC=10. (2) Mid-range IPC values (10-50) show continued but moderated improvements. For instance, INFER [\[103\]](#page-18-51) on CIFAR-100 improves from 50.2% (IPC=10) to 65.1% (IPC=50). This range often represents the optimal trade-off between compression and performance for practical applications. (3) High IPC settings (50-100) exhibit diminishing returns across datasets. On CIFAR-100, PAD [\[98\]](#page-18-46) shows only marginal gains from IPC=50 (55.9%) to IPC=100 (58.5%). This pattern is consistently observed in larger datasets like ImageNet-1K, as demonstrated by methods such as CDA [\[73\]](#page-18-21) and G-VBSM [\[77\]](#page-18-25).

The systematic evaluation of high IPC settings (IPC>50) on large-scale datasets such as ImageNet-1k and ImageNet-21K was largely unfeasible two years ago due to computational and methodological constraints. Recent advances have overcome these limitations, enabling comprehensive analysis of IPC scaling effects across diverse datasets and methods. For example, methods like D4M [\[61\]](#page-18-9) and DELT [\[104\]](#page-18-52) now demonstrate strong performance even at high IPC values on ImageNet-1K, with D4M achieving 66.5% accuracy at IPC=100, 68.1% accuracy at IPC=200. While higher IPC values can achieve better performance, the increased storage and computational requirements may outweigh the marginal gains in many practical scenarios. For efficient pretraining and real-world applications, IPC values between 10 and 50 typically offer the most practical balance of performance and resource efficiency.

### 4.2 Performance Analysis Across Dataset Scales and Complexities

Performance varies significantly across datasets, revealing clear trends as dataset scale and complexity increase. Recent methods have achieved remarkable progress on CIFAR-10, with several approaches exceeding 50% accuracy at IPC=1, a substantial improvement over earlier methods that reached around 30%. However, this success diminishes as complexity grows, such as in the transition from CIFAR-10 to CIFAR-100, where the increased class number and greater inter-class variability pose significant challenges

Quantitative analysis reveals consistent performance degradation across dataset scales. For instance, DATM [\[100\]](#page-18-48) achieves 46.9% accuracy on CIFAR-10 with IPC=1 but drops to 27.9% on CIFAR-100. Similarly, RDED [\[64\]](#page-18-12) shows a dramatic decrease from 22.9% on CIFAR-10 to 11.0% on CIFAR-100 at IPC=1. This pattern becomes more pronounced with larger-scale datasets. DATM's performance drops from 66.8% on CIFAR-10 to 31.1% on Tiny ImageNet at IPC=10, highlighting the challenges of preserving semantic information under limited IPC settings as the complexity increases.

The transition to high-resolution, large-scale datasets like ImageNet-1K and ImageNet-21K introduces challenges beyond increased class counts. These datasets feature more complex visual characteristics, including varied perspectives and extensive intra-class variations. Recent methods address these challenges through innovative approaches. For example, INFER [\[103\]](#page-18-51) and DWA [\[105\]](#page-19-0) leverage diversity-driven strategies and advanced regularization techniques to enhance representational quality. On ImageNet-1K, these methods demonstrate promising scalability, with DWA achieving 55.2% accuracy at IPC=50 and INFER showing robust performance across different IPCs.

The scaling to ImageNet-21K presents an even greater challenge with its massive 21, 000 classes. Nevertheless, recent methods have shown encouraging results. CUDD [\[102\]](#page-18-50) achieves 34.9% accuracy at IPC=20, while CDA [\[73\]](#page-18-21) reaches 26.4% at the same IPC setting. Even with limited IPC (IPC=10), methods like EDC [\[78\]](#page-18-26) and RDED [\[64\]](#page-18-12) maintain reasonable performance at 26.8% and 25.6% respectively. These results on ImageNet-21K, though lower than those on smaller datasets, represent significant progress in scaling dataset distillation to extremely large-scale scenarios.

These advancements represent significant progress in scaling dataset distillation to complex, real-world scenarios. However, the persistent performance gap between smaller and larger datasets indicates that maintaining distillation quality across varying dataset scales remains a key challenge in the field. The success of diversity-focused and regularization-enhanced approaches suggests promising directions for future research in handling large-scale datasets.

### 4.3 Performance and Scalability Across Techniques

Different methodological approaches exhibit varying levels of success depending on dataset scale and IPC settings. Early methods primarily focused on matching-based approaches, including gradient matching (GM) and trajectory matching (TM). While these methods demonstrated strong performance on smaller datasets (e.g., DC [\[15\]](#page-17-14) and DSA [\[16\]](#page-17-15) achieving over 50% accuracy on CIFAR-10), their effectiveness diminishes on larger-scale datasets like ImageNet-1K and ImageNet-21K. This scalability limitation stems from their difficulty in handling the increased complexity and high dimensionality of larger datasets.

Recent advances have shown that methods integrating SRe2L frameworks with diversity-enhancing strategies achieve superior performance on large-scale datasets. For instance, on ImageNet-1K, CUDD [\[102\]](#page-18-50) achieves 65.0% accuracy at IPC=200, while D4M [\[61\]](#page-18-9) reaches 68.1% under the same setting. These methods effectively address the complexity of larger datasets through their de-coupled optimization mechanisms and robust regularization strategies. Furthermore, their success extends to extremely large-scale scenarios, with methods like CUDD achieving 34.9% accuracy on ImageNet-21K at IPC=20, demonstrating unprecedented scalability.

Emerging paradigms in dataset distillation include latent and frequency-based methods, such as FreD [\[41\]](#page-17-40) and NSD [\[42\]](#page-17-41). These approaches show promising results on smaller

<span id="page-15-0"></span>TABLE 2: Performance comparison of dataset distillation methods across four datasets (CIFAR-10/100, Tiny ImageNet, and ImageNet-1K) under different IPCs. R18 denotes ResNet18 architecture. Methods without explicit R18 notation use ConvNet as the default architecture.

|                          |                  |                                  | CIFAR-10                 |                          |              | <b>CIFAR-100</b>         |                          |                          | <b>Tiny ImageNet</b> |                          |                          |                          | ImageNet-1K              |                          |                          |                          |        |          |                          |
|--------------------------|------------------|----------------------------------|--------------------------|--------------------------|--------------|--------------------------|--------------------------|--------------------------|----------------------|--------------------------|--------------------------|--------------------------|--------------------------|--------------------------|--------------------------|--------------------------|--------|----------|--------------------------|
| Methods                  | Schemes          | Venue                            | 1                        | 10                       | 50           | 100                      | 1                        | 10                       | 50                   | 100                      | 1                        | 10                       | 50                       | 100                      | 1                        | 10                       | 50     | 100      | 200                      |
| DD[4]                    | <b>META</b>      | arXiv/2018                       | 42.8                     | $\sim$                   | $\sim$       | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$               | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$                   |        |          | $\sim$                   |
| RaT-BPTT [13]            | <b>META</b>      | <b>ICLR/2023</b>                 | 53.2                     | 69.4                     | 75.3         | $\overline{\phantom{a}}$ | 35.3                     | 47.5                     | 50.6                 | ÷,                       | 20.1                     | 24.4                     | $\sim$                   |                          |                          |                          |        |          |                          |
| Teddy [14]               | <b>META</b>      | <b>ECCV/2024</b>                 | 30.1                     | 53.0                     | 66.1         |                          | 13.5                     | 33.4                     | 49.4                 |                          | $\overline{\phantom{a}}$ |                          | 45.2                     | 52.0                     |                          | 34.1                     | 52.5   | 56.5     |                          |
|                          | GM               |                                  | 28.3                     | 44.9                     | 53.9         | $\overline{\phantom{a}}$ | 12.8                     | 25.2                     |                      | $\overline{\phantom{a}}$ |                          |                          | 11.2                     |                          |                          |                          |        |          |                          |
| DC [15]                  |                  | <b>ICLR/2021</b>                 |                          |                          |              | $\sim$                   |                          |                          | $\sim$               | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |                          | $\sim$                   | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $\sim$ | $\sim$   | $\overline{\phantom{a}}$ |
| <b>DSA</b> [16]          | <b>GM</b>        | ICML/2021                        | 28.8                     | 52.1                     | 60.6         | $\sim$                   | 13.9                     | 32.3                     | $\sim$               | $\sim$                   | $\sim$                   | $\sim$                   | 25.3                     | in 19                    | $\sim$                   | $\overline{\phantom{a}}$ | ÷.     |          |                          |
| <b>MTT</b> [17]          | <b>TM</b>        | <b>CVPR/2022</b>                 | 46.3                     | 65.3                     | 71.6         | $\overline{\phantom{a}}$ | 24.3                     | 40.1                     | 47.7                 | $\overline{\phantom{a}}$ | 8.8                      | 23.2                     | 28.0                     | 33.7                     |                          |                          |        |          |                          |
| FTD [18]                 | TM               | CVPR/2023                        | 46.8                     | 66.6                     | 73.8         | $\sim$                   | 25.2                     | 43.4                     | 50.7                 | $\overline{\phantom{a}}$ | 10.4                     | 24.5                     | $\overline{\phantom{a}}$ | $\sim$                   | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |        |          |                          |
| TESLA [24]               | TM               | <b>ICML/2023</b>                 | 48.5                     | 66.4                     | 72.6         | $\sim$                   | 24.8                     | 41.7                     | 47.9                 | $\overline{a}$           |                          |                          |                          |                          | 7.7                      | 17.8                     | 27.9   |          |                          |
| AST [20]                 | <b>TM</b>        | arXiv/2023                       | 48.8                     | 67.1                     | 74.6         | $\overline{\phantom{a}}$ | 26.6                     | 44.4                     | 51.7                 | $\overline{\phantom{m}}$ | 13.7                     | 25.7                     | $\sim$                   | $\overline{\phantom{a}}$ |                          |                          |        |          |                          |
| <b>MCT</b> [21]          | <b>TM</b>        | arXiv/2024                       | 48.5                     | 66.0                     | 72.3         | $\sim$                   | 24.5                     | 42.5                     | 46.8                 |                          | 9.6                      | 22.6                     | 27.6                     |                          |                          |                          |        |          |                          |
| Li et al. [23]           | <b>TM</b>        | <b>IEICE/2024</b>                | 46.4                     | 65.5                     | 71.9         | $\sim$                   | 24.6                     | 43.1                     | 48.4                 | $\sim$                   | $\sim$                   |                          | $\sim$                   |                          |                          |                          |        |          |                          |
| ATT [25]                 | TM               | <b>ECCV/2024</b>                 | 48.3                     | 67.7                     | 74.5         | $\overline{\phantom{a}}$ | 26.1                     | 44.2                     | 51.2                 | $\overline{a}$           | 11.0                     | 25.8                     | L.                       |                          |                          |                          |        |          |                          |
| <b>CAFE</b> [27]         | DM               | <b>CVPR/2022</b>                 | 30.3                     | 46.3                     | 55.5         | $\sim$                   | 12.9                     | 27.8                     | 37.9                 | ÷,                       | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $\bar{\phantom{a}}$      | $\bar{z}$                |                          | ÷.                       |        |          |                          |
| DM [26]                  | <b>DM</b>        | <b>WACV/2023</b>                 | 26.0                     | 48.9                     | 63.0         | $\sim$                   | 11.4                     | 29.7                     | 43.6                 |                          | 3.9                      | 12.9                     | 24.1                     | $\sim$                   | 1.3                      | 5.7                      | 11.4   |          |                          |
| DataDAM [31]             | DM               | <b>ICCV/2023</b>                 | 32.0                     | 54.2                     | 67.0         | $\sim$                   | 14.5                     | 34.8                     | 49.4                 | $\sim$                   | 8.3                      | 18.7                     | 28.7                     | $\sim$                   | 2.0                      | 6.3                      | 15.5   |          |                          |
| <b>IDM</b> [28]          | <b>DM</b>        | <b>CVPR/2023</b>                 | 45.6                     | 58.6                     | 67.5         | $\overline{\phantom{a}}$ | 20.1                     | 45.1                     | 50.0                 |                          | 10.1                     | 21.9                     | 27.7                     | $\sim$                   |                          | $\overline{\phantom{a}}$ | $\sim$ |          |                          |
| <b>WMDD</b> [35]         | DM               | arXiv/2023                       | $\sim$                   | $\sim$                   | $\sim$       | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$               | ٠                        | 7.6                      | 41.8                     | 59.4                     | 61.0                     | 3.2                      | 38.2                     | 57.6   | 60.7     |                          |
| Rahimi et al. [30]       | <b>DM</b>        | arXiv/2024                       | 27.9                     | 53.0                     | 65.6         | $\sim$                   | 13.5                     | 33.9                     | 45.3                 |                          | 4.9                      | 17.2                     | 27.4                     | $\sim$                   | 2.1                      | 7.5                      | 15.6   |          |                          |
| DANCE <sup>[29]</sup>    | <b>DM</b>        | <b>IJCAI/2024</b>                | 47.1                     | 70.8                     | 76.1         | $\overline{\phantom{a}}$ | 27.9                     | 49.8                     | 52.8                 | ÷,                       | 11.6                     | 26.4                     | 28.9                     | $\sim$                   |                          | ÷.                       |        |          |                          |
| M3D [32]                 | <b>DM</b>        | AAAI/2024                        | 45.3                     | 63.5                     | 69.9         | $\bar{ }$                | 26.2                     | 42.4                     | 50.9                 |                          | $\sim$                   | $\sim$                   | $\sim$                   |                          |                          |                          |        |          |                          |
|                          |                  |                                  |                          |                          |              |                          |                          |                          |                      |                          |                          |                          |                          |                          |                          |                          |        |          |                          |
| LQM [34]                 | <b>DM</b>        | <b>CVPRW/2024</b>                | 45.9                     | 60.9                     | 70.2         | $\sim$                   | 27.2                     | 47.7                     | 52.4                 | $\sim$                   | 10.4                     | 20.8                     | 24.3                     | $\sim$                   | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |        |          |                          |
| Deng et al. [37]         | <b>DM</b>        | <b>CVPR/2024</b>                 | 47.1                     | 59.9                     | 69.0         | $\sim$                   | 24.6                     | 45.7                     | 51.3                 | $\overline{\phantom{a}}$ | 10.0                     | 23.3                     | 27.5                     | $\sim$                   |                          |                          |        |          |                          |
| FreD [41]                | Latent           | NeurIPS/2023                     | 60.6                     | 70.3                     | 75.8         | $\sim$                   | 34.6                     | 42.7                     | 47.8                 | $\sim$                   | $\overline{\phantom{a}}$ | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$                   | $\overline{\phantom{a}}$ |        |          | $\sim$                   |
| <b>NSD</b> [42]          | Freq             | <b>ECCV/2024</b>                 | 68.5                     | 73.4                     | 75.2         | $\sim$                   | 36.5                     | 46.1                     | $\sim$               | $\sim$                   | 21.3                     | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |                          |                          |        |          |                          |
| <b>MIM4DD</b> [46]       | PP               | NeurIPS/2023                     | 51.9                     | 70.8                     | 74.7         | ÷.                       | 31.1                     | 47.4                     | $\sim$               | $\sim$                   | $\sim$                   | $\sim$                   | ÷.                       | ÷,                       |                          |                          |        |          |                          |
| SeqMatch [44]            | PP               | NeurIPS/2024                     | $\sim$                   | 68.3                     | 75.3         | $\overline{\phantom{a}}$ | $\bar{\phantom{a}}$      | 45.1                     | 51.9                 | $\sim$                   | $\sim$                   | 23.8                     | $\sim$                   |                          |                          |                          |        |          |                          |
| FYI [50]                 | PP               | <b>ECCV/2024</b>                 | 52.5                     | 68.2                     | 75.2         | $\sim$                   | 28.9                     | 45.8                     | 50.8                 | $\overline{\phantom{a}}$ | 11.6                     | 26.8                     | 30.1                     | $\sim$                   | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |        |          |                          |
| CMI [49]                 | PP               | <b>ICLR/2025</b>                 | $\sim$                   | 70.0                     | 76.6         | $\sim$                   | $\sim$                   | 46.6                     | 53.8                 | $\sim$                   | 10.4                     | 25.7                     | 30.1                     | $\sim$                   |                          | 24.2                     | 49.1   | 54.6     |                          |
| DiM [58]                 | Gen              | arXiv/2023                       | 51.3                     | 66.2                     | 72.6         | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$               | $\sim$                   | $\overline{\phantom{a}}$ | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$ | $\sim$   | ÷                        |
| Zhang et al. [59]        | Gen              | arXiv/2023                       | 48.2                     | 66.2                     | 73.8         | $\overline{\phantom{a}}$ | 26.1                     | 41.9                     | 48.5                 |                          |                          |                          | $\sim$                   |                          | 7.9                      | 17.6                     | 27.2   |          |                          |
| D3M [62]                 | Gen              | arXiv/2024                       | 35.9                     | 58.6                     | 70.5         | $\sim$                   | 30.8                     | 49.1                     | 54.5                 | $\overline{\phantom{a}}$ | 11.4                     | 38.8                     | 51.4                     | $\sim$                   | 5.0                      | 23.6                     | 32.2   | $\sim$   |                          |
| Li et al. [57]           | Gen              | <b>CVPRW/2024</b>                | 52.3                     | 66.7                     | 73.1         |                          |                          |                          |                      |                          |                          |                          |                          |                          |                          |                          | $\sim$ |          |                          |
| Gu et al. [60]           | Gen              | <b>CVPR/2024</b>                 | $\overline{\phantom{a}}$ |                          | $\sim$       |                          | $\overline{\phantom{a}}$ | $\sim$                   | ÷.                   |                          | $\overline{\phantom{a}}$ |                          | $\sim$                   | п.                       | $\overline{\phantom{a}}$ | 44.3                     | 58.6   | $\sim$   |                          |
| D <sub>4</sub> M [61]    | Gen              | <b>CVPR/2024</b>                 | $\sim$                   | 56.2                     | 72.8         | $\sim$                   | $\overline{\phantom{a}}$ | 45.0                     | 48.8                 |                          |                          |                          | 51.0                     | 55.3                     |                          | 34.2                     | 63.4   | 66.5     | 68.1                     |
| IGD [68]                 | Gen              | <b>ICLR/2025</b>                 | $\sim$                   | $\sim$                   | 66.8         | $\sim$                   | $\overline{\phantom{a}}$ | 45.8                     | 53.9                 | 55.9                     | $\overline{\phantom{a}}$ | $\sim$                   | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | 46.2                     | 60.3   | $\sim$   | $\sim$                   |
| <b>SRe2L</b> [54]        | SRe2L/R18        | NeurIPS/2024                     | 16.6                     | 29.3                     | 45.0         | $\sim$                   | 6.6                      | 27.0                     | 50.2                 | $\overline{\phantom{a}}$ | 2.62                     | 16.1                     | 41.1                     | 49.7                     | 0.4                      | 21.3                     | 46.8   | 52.8     | 65.9                     |
| <b>EDC</b> [78]          | SRe2L/R18        | NeurIPS/2024                     | 32.6                     | 79.1                     | 87.0         | $\sim$                   | 39.7                     | 63.7                     | 68.6                 | $\sim$                   | 39.2                     | 51.2                     | 57.2                     | $\sim$                   | 12.8                     | 48.6                     | 58.0   | $\sim$   |                          |
| Xiao et al. [85]         | SRe2L/R18        | NeurIPS/2024                     | $\sim$                   | $\sim$                   | $\sim$       | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$               |                          | $\sim$                   | $\sim$                   | 48.8                     | 53.6                     | $\sim$                   | 34.6                     | 55.4   | 59.4     | 62.6                     |
|                          | SRe2L/R18        | <b>CVPR/2024</b>                 | 22.9                     | 37.1                     | 62.1         | $\sim$                   | 11.0                     | 42.6                     | 62.6                 | $\sim$                   | 9.7                      | 41.9                     | 58.2                     | $\sim$                   |                          | 42.0                     | 56.5   | $\sim$   |                          |
| RDED <sup>[64]</sup>     |                  |                                  |                          |                          |              |                          |                          |                          |                      |                          |                          |                          |                          | $\sim$                   | 6.6                      |                          |        |          | $\sim$                   |
| G-VBSM [77]              | SRe2L/R18        | <b>CVPR/2024</b>                 | $\sim$                   | 53.5                     | 59.2         | $\overline{\phantom{a}}$ | 25.9                     | 59.5                     | 65.0                 | $\overline{a}$           | $\overline{\phantom{a}}$ | $\sim$                   | 47.6                     |                          |                          | 31.4                     | 51.8   | 55.7     |                          |
| CDA [73]                 | SRe2L            | <b>TMLR/2024</b>                 | $\sim$                   | $\overline{\phantom{a}}$ | $\sim$       |                          | $\overline{\phantom{a}}$ | 49.8                     | 64.4                 | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | 21.3                     | 48.7                     | 53.2                     | 0.5                      | 33.5                     | 53.5   | 58.0     | 63.3                     |
| <b>SC-DD</b> [75]        | SRe2L/R18        | arXiv/2024                       |                          |                          | $\sim$       |                          | $\overline{a}$           | $\overline{\phantom{a}}$ | 53.4                 |                          |                          | 31.6                     | 45.9                     | $\sim$                   |                          | 32.1                     | 53.1   | 57.9     | 63.5                     |
| Zhong et al. [83]        | SRe2L/R18        | arXiv/2024                       | $\sim$                   | 36.4                     | 61.0         | $\sim$                   | $\sim$                   | 41.5                     | 63.8                 | ÷,                       | $\sim$                   | 40.2                     | 58.5                     | $\sim$                   | $\sim$                   | 42.1                     | 59.4   | 61.8     | $\sim$                   |
| <b>GIFT</b> [84]         | SRe2L/R18        | arXiv/2024                       |                          |                          | $\sim$       |                          | $\overline{\phantom{a}}$ | 49.5                     | 57.0                 |                          | $\overline{\phantom{a}}$ | 42.9                     | 47.5                     | $\overline{\phantom{a}}$ |                          | 21.7                     | 39.5   | 42.5     |                          |
| CV-DD [79]               | SRe2L/R18        | arXiv/2025                       | $\equiv$                 | 64.1                     | 74.0         | $\sim$                   | 28.3                     | 62.7                     | 67.1                 | $\sim$                   | 10.1                     | 47.8                     | 54.1                     | $\sim$                   | $\sim$                   | 46.0                     | 59.5   | $\sim$   |                          |
| FocusDD [81]             | SRe2L/R18        | arXiv/2025                       | $\sim$                   | $\overline{\phantom{a}}$ | $\sim$       | $\overline{\phantom{a}}$ | $\sim$                   | $\sim$                   | $\sim$               | $\overline{\phantom{a}}$ | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$                   | 8.8                      | 45.3                     | 61.7   | 62.0     | $\sim$                   |
| DREAM [90]               | Selective        | <b>ICCV/2023</b>                 | 51.1                     | 69.4                     | 74.8         | $\sim$                   | 29.5                     | 46.8                     | 52.6                 | $\overline{\phantom{a}}$ | 10.0                     | 29.5                     | $\sim$                   | $\sim$                   | $\sim$                   | 18.5                     | $\sim$ | $\equiv$ |                          |
| DREAM+ [91]              | Selective        | arXiv/2023                       | 52.5                     | 69.9                     | 75.3         | $\overline{\phantom{a}}$ | 29.7                     | 47.4                     | 52.6                 | $\sim$                   | 10.5                     | 24.0                     | 29.5                     | $\sim$                   |                          | 18.5                     | $\sim$ | $\sim$   |                          |
| APM [96]                 | Selective        | arXiv/2023                       |                          |                          | 75.0         | $\sim$                   |                          | 44.6                     | 53.3                 | 55.2                     | $\sim$                   | 30.0                     | 38.2                     | 39.6                     |                          | 24.8                     | 30.7   | 32.6     |                          |
| <b>RFAD</b> [97]         | Selective        | arXiv/2023                       | 64.4                     | 74.3                     | 77.0         | $\sim$                   | 38.5                     | 45.8                     | $\sim$               | $\overline{\phantom{a}}$ | $\sim$                   | $\sim$                   | $\sim$                   | $\overline{\phantom{a}}$ |                          |                          |        |          |                          |
| PAD [98]                 | Selective        | arXiv/2024                       | 47.2                     | 67.4                     | 77.0         | $\sim$                   | 28.4                     | 47.8                     | 55.9                 | 58.5                     | 17.7                     | 32.3                     | 41.6                     | $\sim$                   |                          |                          |        |          |                          |
| SDC [94]                 | Selective        | arXiv/2024                       | 47.9                     | 65.3                     | 71.8         | $\sim$                   | 28.0                     | 47.8                     | 52.5                 | $\sim$                   | 17.4                     | 30.7                     | 39.9                     |                          |                          |                          |        |          |                          |
| AutoPalette [88]         | Selective        | NeurIPS/2024                     | 58.6                     | 74.3                     | 79.4         | $\sim$                   | 38.0                     | 52.6                     | 53.3                 | $\sim$                   |                          |                          | $\sim$                   |                          |                          |                          |        |          |                          |
| <b>IADD</b> [89]         | Selective        | NN/2024                          | 46.5                     | 66.7                     | 72.6         | $\bar{ }$                | 25.2                     | 42.7                     | 49.0                 | $\overline{\phantom{a}}$ | 9.6                      | 24.1                     | $\sim$                   |                          |                          |                          |        |          |                          |
| BiLP+IDC [93]            | Selective        | <b>ECCV/2024</b>                 | 55.9                     | 69.8                     | 76.9         | $\sim$                   | 34.0                     | 48.0                     | $\sim$               |                          | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |                          |                          |                          |        |          |                          |
| SelMatch <sup>[95]</sup> | Selective        | ICML/2024                        | $\sim$                   | $\sim$                   | $\sim$       | $\sim$                   | $\sim$                   | $\sim$                   | 54.5                 | 62.4                     | $\sim$                   | $\overline{\phantom{a}}$ | 44.7                     | 50.4                     |                          |                          |        | $\sim$   |                          |
| DATM [100]               | Lossless         | <b>ICLR/2024</b>                 | 46.9                     | 66.8                     | 76.1         | $\sim$                   | 27.9                     | 47.2                     | 55.0                 | 57.5                     | 17.1                     | 31.1                     | 39.7                     | $\sim$                   | $\overline{a}$           | $\sim$                   | ÷.     | $\sim$   | $\sim$                   |
| <b>CUDD</b> [102]        | Diversity/R18    | arXiv/2024                       | $\sim$                   | 56.2                     | 84.5         | $\overline{\phantom{a}}$ | $\bar{\phantom{a}}$      | 60.3                     | 65.7                 | $\sim$                   | $\sim$                   | $\omega$                 | 55.6                     | 56.8                     | $\overline{\phantom{a}}$ | 39.0                     | 57.4   | 61.3     | 65.0                     |
| <b>DELT</b> [104]        | Diversity/R18    | arXiv/2024                       | 24.0                     | 43.0                     | 64.9         | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$               |                          | 9.3                      | 43.0                     | 55.7                     | $\sim$                   |                          | 45.8                     | 59.2   | 62.4     | $\sim$                   |
|                          |                  |                                  |                          |                          |              | $\overline{\phantom{a}}$ |                          |                          | 60.9                 | $\sim$                   |                          | $\overline{\phantom{a}}$ |                          |                          |                          |                          |        |          |                          |
| DWA [105]                | Diversity/R18    | NeurIPS/2024<br><b>ICLR/2025</b> | $\sim$                   | 32.6<br>33.5             | 53.1<br>59.2 |                          | $\overline{a}$           | 39.6<br>50.2             | 65.1                 |                          | $\overline{\phantom{a}}$ |                          | 52.8<br>55.0             | 56.0                     |                          | 37.9<br>37.0             | 55.2   | 59.2     |                          |
| <b>INFER [103]</b>       | Diversity/R18    |                                  |                          |                          |              |                          |                          |                          |                      | 68.6                     |                          | 38.6                     |                          |                          |                          |                          | 54.3   | $\sim$   |                          |
| Zhang et al. [107]       | Augmentation     | <b>CVPR/2023</b>                 | 49.2                     | 67.1                     | 73.8         | $\sim$                   | 29.8                     | 45.6                     | 52.6                 | $\sim$                   | $\overline{\phantom{a}}$ | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$                   | $\sim$ | $\sim$   |                          |
| <b>LADD</b> [108]        | Augmentation/R18 | arXiv/2023                       |                          |                          |              |                          |                          |                          |                      |                          |                          |                          |                          |                          |                          | 28.8                     |        |          |                          |

<span id="page-15-1"></span>TABLE 3: Performance comparison of dataset distillation methods on ImageNet-21K under different IPCs.

| Methods    | Schemes       | Venue        | ImageNet-21K |      |      |    |
|------------|---------------|--------------|--------------|------|------|----|
|            |               |              | 1            | 10   | 20   | 50 |
| SRe2L [54] | SRe2L/R18     | NeurIPS/2024 | -            | 18.5 | 21.8 | -  |
| EDC [78]   | SRe2L/R18     | NeurIPS/2024 | -            | 26.8 | -    | -  |
| CDA [73]   | SRe2L         | TMLR/2024    | -            | 22.6 | 26.4 | -  |
| RDED [64]  | SRe2L/R18     | CVPR/2024    | -            | 25.6 | -    | -  |
| CUDD [102] | Diversity/R18 | arXiv/2024   | -            | 28.0 | 34.9 | -  |

datasets, with FreD achieving 60.6% and NSD reaching 68.5% accuracy on CIFAR-10 at IPC=1, significantly outperforming traditional matching-based methods. On CIFAR-100, they maintain strong performance with FreD achieving 34.6% and NSD reaching 36.5% at IPC=1. However, their scalability to larger datasets remains largely unexplored, as comprehensive results on ImageNet-1K and ImageNet-21K are yet to be reported.

Recent methods have also explored importance-based sampling and diversity-driven approaches. Notable examples include AutoPalette [\[88\]](#page-18-36), which achieves 58.6% accuracy on CIFAR-10 at IPC=1, and EDC [\[78\]](#page-18-26), which demonstrates strong performance across different scales, reaching 87.0% on CIFAR-10 at IPC=50. These innovations suggest that combining multiple strategies, such as diversity enhancement, importance sampling, and efficient optimization, may be key for both scalability and performance. In summary, while traditional matching-based methods remain effective for smaller datasets, the field has evolved towards more sophisticated approaches that combine de-coupling mechanisms like SRe2L, diversityenhancing strategies, and novel paradigms such as latent and frequency-based methods. To advance the scalability and robustness of dataset distillation, future research must continue to address the challenges of large-scale datasets, particularly focusing on methods that can maintain high performance while scaling to datasets like ImageNet-21K.

## 5 CHALLENGES AND FUTURE DIRECTIONS

Despite remarkable advancements in dataset distillation, critical challenges remain, particularly as the field shifts toward larger datasets and more complex applications.

### 5.1 Challenges

**Scalability to Large-Scale Datasets** While recent methods have demonstrated significant progress on ImageNet-1K, with approaches like D4M [\[61\]](#page-18-9) achieving 68.1% accuracy at IPC=200 and CUDD [\[102\]](#page-18-50) reaching 65.0%, scaling to larger datasets like ImageNet-21K remains challenging. On ImageNet-21K, even state-of-the-art methods like CUDD achieve only 34.9% accuracy at IPC=20, highlighting the substantial performance gap that exists when scaling to ultra-large datasets. These challenges stem from increased class counts, intra-class variability, and computational demands of large-scale datasets.

**Balancing Compression and Performance** The relationship between IPC and performance exhibits distinct patterns across different scales. While significant gains are observed between IPC=1 and IPC=10 (e.g., DATM [\[100\]](#page-18-48) improves from 46.9% to 66.8% on CIFAR-10), performance improvements diminish at higher IPC values. For instance, on CIFAR-100, PAD [\[98\]](#page-18-46) shows only marginal gains from IPC=50 (55.9%) to IPC=100 (58.5%). This non-linear relationship presents a critical trade-off between dataset compression and model performance, particularly crucial for practical applications.

**Limited Cross-Architecture Generalization** Current methods, particularly those based on SRe2L frameworks, often show architecture-specific performance patterns. While some methods demonstrate strong performance, their effectiveness across different architectures, especially emerging ones like Vision Transformers, remains less explored. The challenge of creating synthetic datasets that generalize well across diverse architectures persists.

**Underexplored Domains and Modalities** The majority of dataset distillation research has focused on vision tasks, with limited exploration in audio and multimodal domains. Emerging methods for temporal and multimodal data distillation demonstrate potential but require further development to match the maturity of image-based techniques.

**Fair Evaluation of Dataset Distillation Methods** Recent DD methods are shifting from hard labels to soft labels to enhance model accuracy [\[54\]](#page-18-2). However, soft labels inherently transfer additional knowledge from a teacher model, making it difficult to isolate the true informativeness of distilled data [\[55\]](#page-18-3), [\[179\]](#page-20-21). Additionally, the use of diverse data augmentation techniques during evaluation further skews results, making it unclear whether performance gains stem from the quality of the distilled data or external enhancements [\[179\]](#page-20-21). Furthermore, different dataset distillation methods employ varied evaluation strategies, such as distinct loss functions, leading to inconsistencies in performance comparison.

#### 5.2 Future Directions

**Advanced Scaling Techniques** Future research should focus on designing algorithms capable of scaling to ultra-large datasets without sacrificing performance. Techniques like hybrid approaches that combine distillation with coreset selection or data pruning could be explored to tackle computational bottlenecks.

**Adaptive IPC Optimization** Developing adaptive IPC optimization strategies that dynamically adjust IPC settings based on dataset complexity and downstream tasks could provide a practical solution to the compression-performance trade-off. Meta-learning and reinforcement learning frameworks offer promising avenues for automated IPC tuning.

**Theoretical Understanding** While dataset distillation methods have demonstrated impressive empirical success, their theoretical foundations remain relatively unexplored. A rigorous theoretical framework is needed to understand the fundamental limits of distillation, including convergence guarantees, optimal compression ratios, and generalization bounds. Such theoretical insights could provide principled guidance for designing more effective distillation algorithms and help understand the relationship between distillation size, model architecture, and performance guarantees. **Architecture-Agnostic Distillation** Developing techniques that effectively transfer knowledge across different model architectures remains a critical challenge. Techniques that disentangle architecture-invariant features from architecture-specific biases could be further explored to enable broad applicability across architectures.

**Domain Expansion** Dataset distillation's applicability should be extended to emerging domains, such as medical imaging, scientific analysis, and 3D point cloud data. Tailored frameworks that account for domain-specific characteristics, such as temporal dynamics in video or frequencyspace representations in audio, are critical for broadening the scope of distillation techniques.

**Enhancing Fair Evaluation and Comparisons** In this survey, we present two comparative tables summarizing key aspects of various dataset distillation methods, including their use of soft labels, architectures, and other relevant factors, providing researchers with a reference to determine the most suitable approach for their needs. Additionally, Li et al. introduced DD-Ranking [\[179\]](#page-20-21), a benchmark designed to decouple the effects of knowledge distillation and data augmentation, ensuring a fair evaluation framework to assess the true informativeness of distilled data. By combining structured comparisons with insights from existing benchmarks such as DD-Ranking [\[179\]](#page-20-21), we aim to help researchers navigate the landscape of dataset distillation methods while promoting more standardized evaluation practices.

## 6 CONCLUSION

This survey provides a comprehensive overview of the rapid advancements in dataset distillation from 2023 to 2025, with a particular focus on scaling to large-scale datasets and emerging methodological innovations. The field has witnessed significant progress across multiple dimensions, from achieving unprecedented performance on ImageNetscale datasets to expanding into new domains like video, audio, and multi-modal processing.

Several key trends have emerged during this period. First, the development of more sophisticated optimization strategies, such as SRe2L frameworks and diversity-driven approaches, has enabled effective distillation of large-scale datasets like ImageNet-1K and ImageNet-21K. Second, the introduction of soft labels and decoupling mechanisms has significantly improved both efficiency and performance. Third, the emergence of generative models, particularly diffusion-based approaches, has opened new possibilities for high-quality synthetic data generation. Despite these advances, important challenges remain. The scalability to ultra-large datasets, the trade-off between compression and performance, and cross-architecture generalization continue to be active areas of research. Additionally, the extension to multi-modal and temporal domains presents both opportunities and challenges that require innovative solutions. We hope that this comprehensive survey of recent dataset distillation advances will serve as a valuable resource for researchers and practitioners, providing insights into the latest progress, existing challenges, and promising future directions in this rapidly evolving field.

## ACKNOWLEDGMENT

The authors would like to express their gratitude to the contributors of the Awesome-Dataset-Distillation repository<sup>[1](#page-17-51)</sup>, which has served as a valuable resource in compiling recent advancements.

#### REFERENCES

- <span id="page-17-0"></span>[1] S. Minaee, T. Mikolov, N. Nikzad, M. Chenaghlu, R. Socher, X. Amatriain, and J. Gao, "Large language models: A survey," *arXiv preprint arXiv:2402.06196*, 2024.
- <span id="page-17-1"></span>[2] J. Zhang, J. Huang, S. Jin, and S. Lu, "Vision-language models for vision tasks: A survey," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2024.
- <span id="page-17-2"></span>[3] A. Radford, J. W. Kim, C. Hallacy, A. Ramesh, G. Goh, S. Agarwal, G. Sastry, A. Askell, P. Mishkin, J. Clark *et al.*, "Learning transferable visual models from natural language supervision, in *ICML*, 2021.
- <span id="page-17-3"></span>[4] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-17-4"></span>[5] W. Yang, Y. Zhu, Z. Deng, and O. Russakovsky, "What is dataset distillation learning?" in *ICML*, 2024.
- <span id="page-17-5"></span>[6] O. Russakovsky, J. Deng, H. Su, J. Krause, S. Satheesh, S. Ma, Z. Huang, A. Karpathy, A. Khosla, M. Bernstein *et al.*, "Imagenet large scale visual recognition challenge," *International Journal of Computer Vision*, 2015.
- <span id="page-17-6"></span>[7] T. Ridnik, E. Ben-Baruch, A. Noy, and L. Zelnik-Manor, "Imagenet-21k pretraining for the masses," in *NeurIPS Datasets and Benchmarks*, 2021.
- <span id="page-17-7"></span>J. Geng, Z. Chen, Y. Wang, H. Woisetschlaeger, S. Schimmler, R. Mayer, Z. Zhao, and C. Rong, "A survey on dataset distillation: Approaches, applications and future directions," in *IJCAI*, 2023.
- <span id="page-17-8"></span>[9] S. Lei and D. Tao, "A comprehensive survey of dataset distillation," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023.
- <span id="page-17-9"></span>[10] R. Yu, S. Liu, and X. Wang, "Dataset distillation: A comprehensive review," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023.
- <span id="page-17-10"></span>[11] L. Deng, "The mnist database of handwritten digit images for machine learning research [best of the web]," *IEEE signal processing magazine*, 2012.
- <span id="page-17-11"></span>[12] P. J. Werbos, "Backpropagation through time: what it does and how to do it," *Proceedings of the IEEE*, 1990.
- <span id="page-17-12"></span>[13] Y. Feng, S. R. Vedantam, and J. Kempe, "Embarrassingly simple dataset distillation," in *ICLR*, 2023.
- <span id="page-17-13"></span>[14] R. Yu, S. Liu, J. Ye, and X. Wang, "Teddy: Efficient large-scale dataset distillation via taylor-approximated matching," in *ECCV*, 2024.
- <span id="page-17-14"></span>[15] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," in *ICLR*, 2021.
- <span id="page-17-15"></span>[16] B. Zhao and H. Bilen, "Dataset condensation with differentiable siamese augmentation," in *ICML*, 2021.
- <span id="page-17-16"></span>[17] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *CVPR*, 2022.
- <span id="page-17-17"></span>[18] J. Du, Y. Jiang, V. Y. Tan, J. T. Zhou, and H. Li, "Minimizing the accumulated trajectory error to improve dataset distillation," in *CVPR*, 2023.
- <span id="page-17-18"></span>[19] A. Krizhevsky, G. Hinton et al., "Convolutional deep belief networks on cifar-10," *Unpublished manuscript*, 2010.
- <span id="page-17-19"></span>[20] J. Shen, W. Yang, and K.-Y. Lam, "Ast: Effective dataset distillation through alignment with smooth and high-quality expert trajectories," *arXiv preprint arXiv:2310.10541*, 2023.
- <span id="page-17-20"></span>[21] W. Zhong, H. Tang, Q. Zheng, M. Xu, Y. Hu, and L. Nie, "Towards stable and storage-efficient dataset distillation: Matching convexified trajectory," *arXiv preprint arXiv:2406.19827*, 2024.

<span id="page-17-51"></span>1.<https://github.com/Guang000/Awesome-Dataset-Distillation>

- <span id="page-17-21"></span>[22] A. Jacot, F. Gabriel, and C. Hongler, "Neural tangent kernel: Convergence and generalization in neural networks," in *NeurIPS*, 2018.
- <span id="page-17-22"></span>[23] G. Li, R. Togo, T. Ogawa, and M. Haseyama, "Dataset distillation using parameter pruning," *IEICE Transactions on Fundamentals of Electronics, Communications and Computer Sciences*, 2024.
- <span id="page-17-23"></span>[24] J. Cui, R. Wang, S. Si, and C.-J. Hsieh, "Scaling up dataset distillation to imagenet-1k with constant memory," in *ICML*, 2023.
- <span id="page-17-24"></span>[25] D. Liu, J. Gu, H. Cao, C. Trinitis, and M. Schulz, "Dataset distillation by automatic training trajectories," in *ECCV*, 2024.
- <span id="page-17-25"></span>[26] B. Zhao and H. Bilen, "Dataset condensation with distribution matching," in *WACV*, 2023.
- <span id="page-17-26"></span>[27] K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You, "Cafe: Learning to condense dataset by aligning features," in *CVPR*, 2022.
- <span id="page-17-27"></span>[28] G. Zhao, G. Li, Y. Qin, and Y. Yu, "Improved distribution matching for dataset condensation," in *CVPR*, 2023.
- <span id="page-17-28"></span>[29] H. Zhang, S. Li, F. Lin, W. Wang, Z. Qian, and S. Ge, "Dance: Dual-view distribution alignment for dataset condensation," in *IJCAI*, 2024.
- <span id="page-17-29"></span>[30] S. R. Malakshan, "Decomposed distribution matching in dataset condensation," *arXiv preprint arXiv:2412.04748*, 2024.
- <span id="page-17-30"></span>[31] A. Sajedi, S. Khaki, E. Amjadian, L. Z. Liu, Y. A. Lawryshyn, and K. N. Plataniotis, "Datadam: Efficient dataset distillation with attention matching," in *ICCV*, 2023.
- <span id="page-17-31"></span>[32] H. Zhang, S. Li, P. Wang, D. Zeng, and S. Ge, "M3d: Dataset condensation by minimizing maximum mean discrepancy," in *AAAI*, 2024.
- <span id="page-17-32"></span>[33] A. Berlinet and C. Thomas-Agnan, *Reproducing kernel Hilbert spaces in probability and statistics*. Springer Science & Business Media, 2011.
- <span id="page-17-33"></span>[34] W. Wei, T. De Schepper, and K. Mets, "Dataset condensation with latent quantile matching," in *CVPR Workshop*, 2024.
- <span id="page-17-34"></span>[35] H. Liu, Y. Li, T. Xing, V. Dalal, L. Li, J. He, and H. Wang, "Dataset distillation via the wasserstein metric," *arXiv preprint arXiv:2311.18531*, 2023.
- <span id="page-17-35"></span>[36] V. M. Panaretos and Y. Zemel, "Statistical aspects of wasserstein distances," *Annual review of statistics and its application*, 2019.
- <span id="page-17-36"></span>[37] W. Deng, W. Li, T. Ding, L. Wang, H. Zhang, K. Huang, J. Huo, and Y. Gao, "Exploiting inter-sample and inter-feature relations in dataset distillation," in *CVPR*, 2024.
- <span id="page-17-37"></span>[38] Y. Duan, J. Zhang, and L. Zhang, "Dataset distillation in latent space," *arXiv preprint arXiv:2311.15547*, 2023.
- <span id="page-17-38"></span>[39] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Generalizing dataset distillation via deep generative prior," in *CVPR*, 2023.
- <span id="page-17-39"></span>[40] A. Sauer, K. Schwarz, and A. Geiger, "Stylegan-xl: Scaling stylegan to large diverse datasets," in *ACM SIGGRAPH*, 2022.
- <span id="page-17-40"></span>[41] D. Shin, S. Shin, and I.-C. Moon, "Frequency domain-based dataset distillation," in *NeurIPS*, 2023.
- <span id="page-17-41"></span>[42] S. Yang, S. Cheng, M. Hong, H. Fan, X. Wei, and S. Liu, "Neural spectral decomposition for dataset distillation," in *ECCV*, 2024.
- <span id="page-17-42"></span>[43] D. Shin, H. Bae, G. Sim, W. Kang, and I. chul Moon, "Distilling dataset into neural field," in *ICLR*, 2025.
- <span id="page-17-43"></span>[44] J. Du, Q. Shi, and J. T. Zhou, "Sequential subset matching for dataset distillation," in *NeurIPS*, 2023.
- <span id="page-17-44"></span>[45] M. I. Belghazi, A. Baratin, S. Rajeshwar, S. Ozair, Y. Bengio, A. Courville, and D. Hjelm, "Mutual information neural estimation," in *ICML*, 2018.
- <span id="page-17-45"></span>[46] Y. Shang, Z. Yuan, and Y. Yan, "Mim4dd: Mutual information maximization for dataset distillation," in *NeurIPS*, 2023.
- <span id="page-17-46"></span>[47] P. Khosla, P. Teterwak, C. Wang, A. Sarna, Y. Tian, P. Isola, A. Maschinot, C. Liu, and D. Krishnan, "Supervised contrastive learning," in *NeurIPS*, 2020.
- <span id="page-17-47"></span>[48] K. Torkkola, "Feature extraction by non-parametric mutual information maximization," *Journal of machine learning research*, 2003.
- <span id="page-17-48"></span>[49] X. Zhong, B. Chen, H. Fang, X. Gu, S.-T. Xia, and E.-H. Yang, "Going beyond feature similarity: Effective dataset distillation based on class-aware conditional mutual information," in *ICLR*, 2025.
- <span id="page-17-49"></span>[50] B. Son, Y. Oh, D. Baek, and B. Ham, "Fyi: Flip your images for dataset distillation," in *ECCV*, 2024.
- <span id="page-17-50"></span>[51] Y. Lu, X. Chen, Y. Zhang, J. Gu, T. Zhang, Y. Zhang, X. Yang, Q. Xuan, K. Wang, and Y. You, "Can pre-trained models assist in dataset distillation?" *arXiv preprint arXiv:2310.03295*, 2023.

- <span id="page-18-0"></span>[52] I. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair, A. Courville, and Y. Bengio, "Generative adversarial nets," in *NeurIPS*, 2014.
- <span id="page-18-1"></span>[53] F.-A. Croitoru, V. Hondru, R. T. Ionescu, and M. Shah, "Diffusion models in vision: A survey," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023.
- <span id="page-18-2"></span>[54] Z. Yin, E. Xing, and Z. Shen, "Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective," in *NeurIPS*, 2024.
- <span id="page-18-3"></span>[55] T. Qin, Z. Deng, and D. Alvarez-Melis, "A label is worth a thousand images in dataset distillation," in *NeurIPS*, 2024.
- <span id="page-18-4"></span>[56] B. Zhao and H. Bilen, "Synthesizing informative training samples with gan," in *NeurIPS* Workshop, 2022.
- <span id="page-18-5"></span>[57] L. Li, G. Li, R. Togo, K. Maeda, T. Ogawa, and M. Haseyama, "Generative dataset distillation: Balancing global structure and local details," in *CVPR Workshop*, 2024.
- <span id="page-18-6"></span>[58] K. Wang, J. Gu, D. Zhou, Z. Zhu, W. Jiang, and Y. You, "Dim: Distilling dataset into generative model," *arXiv preprint arXiv:2303.04707*, 2023.
- <span id="page-18-7"></span>[59] D. J. Zhang, H. Wang, C. Xue, R. Yan, W. Zhang, S. Bai, and M. Z. Shou, "Dataset condensation via generative model," *arXiv preprint arXiv:2309.07698*, 2023.
- <span id="page-18-8"></span>[60] J. Gu, S. Vahidian, V. Kungurtsev, H. Wang, W. Jiang, Y. You, and Y. Chen, "Efficient dataset distillation via minimax diffusion," in *CVPR*, 2024.
- <span id="page-18-9"></span>[61] D. Su, J. Hou, W. Gao, Y. Tian, and B. Tang, "Dˆ 4: Dataset distillation via disentangled diffusion model," in *CVPR*, 2024.
- <span id="page-18-10"></span>[62] A. Abbasi, A. Shahbazi, H. Pirsiavash, and S. Kolouri, "One category one prompt: Dataset distillation using diffusion models," *arXiv preprint arXiv:2403.07142*, 2024.
- <span id="page-18-11"></span>[63] A. Abbasi, S. Imani, C. An, G. Mahalingam, H. Shrivastava, M. Diesendruck, H. Pirsiavash, P. Sharma, and S. Kolouri, "Diffusion-Augmented Coreset Expansion for Scalable Dataset Distillation," *arXiv preprint arXiv:2412.04668*, 2024.
- <span id="page-18-12"></span>[64] P. Sun, B. Shi, D. Yu, and T. Lin, "On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm," in *CVPR*, 2024.
- <span id="page-18-13"></span>[65] B. B. Moser, F. Raue, S. Palacio, S. Frolov, and A. Dengel, "Latent dataset distillation with diffusion models," *arXiv preprint arXiv:2403.03881*, 2024.
- <span id="page-18-14"></span>[66] T. Qin, Z. Deng, and D. Alvarez-Melis, "Distributional dataset distillation with subtask decomposition," *arXiv preprint arXiv:2403.00999*, 2024.
- <span id="page-18-15"></span>[67] W. Huang, M. Ye, Z. Shi, G. Wan, H. Li, B. Du, and Q. Yang, "Federated learning for generalization, robustness, fairness: A survey and benchmark," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2024.
- <span id="page-18-16"></span>[68] M. Chen, J. Du, B. Huang, Y. Wang, X. Zhang, and W. Wang, "Influence-guided diffusion for dataset distillation," in *ICLR*, 2025.
- <span id="page-18-17"></span>[69] W. Peebles and S. Xie, "Scalable diffusion models with transformers," in *ICCV*, 2023.
- <span id="page-18-18"></span>[70] Y. Lipman, R. T. Chen, H. Ben-Hamu, M. Nickel, and M. Le, "Flow matching for generative modeling," in *ICLR*, 2023.
- <span id="page-18-19"></span>[71] R. T. Chen and Y. Lipman, "Flow matching on general geometries," in *ICLR*, 2024.
- <span id="page-18-20"></span>[72] Z. Cai, H. Zhu, Q. Shen, X. Wang, and X. Cao, "Batch normalization alleviates the spectral bias in coordinate networks," in *CVPR*, 2024.
- <span id="page-18-21"></span>[73] Z. Yin and Z. Shen, "Dataset distillation in large data era," *Transactions on Machine Learning Research*, 2024.
- <span id="page-18-22"></span>[74] L. Jiang, D. Meng, Q. Zhao, S. Shan, and A. Hauptmann, "Selfpaced curriculum learning," in *AAAI*, 2015.
- <span id="page-18-23"></span>[75] M. Zhou, Z. Yin, S. Shao, and Z. Shen, "Self-supervised dataset distillation: A good compression is all you need," *arXiv preprint arXiv:2404.07976*, 2024.
- <span id="page-18-24"></span>[76] Y. Zong, O. Mac Aodha, and T. Hospedales, "Self-supervised multimodal learning: A survey," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2024.
- <span id="page-18-25"></span>[77] S. Shao, Z. Yin, M. Zhou, X. Zhang, and Z. Shen, "Generalized large-scale data condensation via various backbone and statistical matching," in *CVPR*, 2024.
- <span id="page-18-26"></span>[78] S. Shao, Z. Zhou, H. Chen, and Z. Shen, "Elucidating the design space of dataset condensation," in *NeurIPS*, 2024.
- <span id="page-18-27"></span>[79] J. Cui, Z. Li, X. Ma, X. Bi, Y. Luo, and Z. Shen, "Dataset distillation via committee voting," *arXiv preprint arXiv:2501.07575*, 2025.

- <span id="page-18-28"></span>[80] Z. Shen and E. Xing, "A fast knowledge distillation framework for visual recognition," in *ECCV*, 2022.
- <span id="page-18-29"></span>[81] Y. Hu, Y. Cheng, O. Saukh, F. Ozdemir, A. Lu, Z. Cao, and Z. Li, "Focusdd: Real-world scene infusion for robust dataset distillation," *arXiv preprint arXiv:2501.06405*, 2025.
- <span id="page-18-30"></span>[82] A. Kolesnikov, A. Dosovitskiy, D. Weissenborn, G. Heigold, J. Uszkoreit, L. Beyer, M. Minderer, M. Dehghani, N. Houlsby, S. Gelly, T. Unterthiner, and X. Zhai, "An image is worth 16x16 words: Transformers for image recognition at scale," in *ICLR*, 2021.
- <span id="page-18-31"></span>[83] X. Zhong, S. Sun, X. Gu, Z. Xu, Y. Wang, J. Wu, and B. Chen, "Efficient dataset distillation via diffusion-driven patch selection for improved generalization," *arXiv preprint arXiv:2412.09959*, 2024.
- <span id="page-18-32"></span>[84] X. Shang, P. Sun, and T. Lin, "Gift: Unlocking full potential of labels in distilled dataset at near-zero cost," in *ICLR*, 2025.
- <span id="page-18-33"></span>[85] L. Xiao and Y. He, "Are large-scale soft labels necessary for largescale dataset distillation?" in *NeurIPS*, 2024.
- <span id="page-18-34"></span>[86] K. Wang, Z. Li, Z.-Q. Cheng, S. Khaki, A. Sajedi, R. Vedantam, K. N. Plataniotis, A. Hauptmann, and Y. You, "Emphasizing discriminative features for dataset distillation in complex scenarios," *arXiv preprint arXiv:2410.17193*, 2024.
- <span id="page-18-35"></span>[87] R. R. Selvaraju, M. Cogswell, A. Das, R. Vedantam, D. Parikh, and D. Batra, "Grad-cam: Visual explanations from deep networks via gradient-based localization," in *ICCV*, 2017.
- <span id="page-18-36"></span>[88] B. Yuan, Z. Wang, M. Baktashmotlagh, Y. Luo, and Z. Huang, "Color-oriented redundancy reduction in dataset distillation," in *NeurIPS*, 2024.
- <span id="page-18-37"></span>[89] G. Li, R. Togo, T. Ogawa, and M. Haseyama, "Importance-aware adaptive dataset distillation," *Neural Networks*, 2024.
- <span id="page-18-38"></span>[90] Y. Liu, J. Gu, K. Wang, Z. Zhu, W. Jiang, and Y. You, "Dream: Efficient dataset distillation by representative matching," in *ICCV*, 2023.
- <span id="page-18-39"></span>[91] Y. Liu, J. Gu, K. Wang, Z. Zhu, K. Zhang, W. Jiang, and Y. You, "Dream+: Efficient dataset distillation by bidirectional representative matching," *arXiv preprint arXiv:2310.15052*, 2023.
- <span id="page-18-40"></span>[92] B. B. Moser, F. Raue, T. C. Nauen, S. Frolov, and A. Dengel, "Distill the best, ignore the rest: Improving dataset distillation with lossvalue-based pruning," *arXiv preprint arXiv:2411.12115*, 2024.
- <span id="page-18-41"></span>[93] Y. Xu, Y.-L. Li, K. Cui, Z. Wang, C. Lu, Y.-W. Tai, and C.-K. Tang, "Distill gold from massive ores: Efficient dataset distillation via critical samples selection," in *ECCV*, 2024.
- <span id="page-18-42"></span>[94] S. Wang, Y. Yang, Q. Wang, K. Li, L. Zhang, and Y. Junchi, "Not all samples should be utilized equally: Towards understanding and improving dataset distillation," *arXiv*, 2024.
- <span id="page-18-43"></span>[95] Y. Lee and H. W. Chung, "Selmatch: Effectively scaling up dataset distillation via selection-based initialization and partial updates by trajectory matching," in *ICML*, 2024.
- <span id="page-18-44"></span>[96] M. Chen, B. Huang, J. Lu, B. Li, Y. Wang, M. Cheng, and W. Wang, "Dataset distillation via adversarial prediction matching," *arXiv preprint arXiv:2312.08912*, 2023.
- <span id="page-18-45"></span>[97] M. Tukan, A. Maalouf, and M. Osadchy, "Dataset distillation meets provable subset selection," *arXiv preprint arXiv:2307.08086*, 2023.
- <span id="page-18-46"></span>[98] Z. Li, Z. Guo, W. Zhao, T. Zhang, Z.-Q. Cheng, S. Khaki, K. Zhang, A. Sajed, K. N. Plataniotis, K. Wang *et al.*, "Prioritize alignment in dataset distillation," *arXiv preprint arXiv:2408.03360*, 2024.
- <span id="page-18-47"></span>[99] M. Paul, S. Ganguli, and G. K. Dziugaite, "Deep learning on a data diet: Finding important examples early in training," in *NeurIPS*, 2021.
- <span id="page-18-48"></span>[100] Z. Guo, K. Wang, G. Cazenavette, H. Li, K. Zhang, and Y. You, "Towards lossless dataset distillation via difficulty-aligned trajectory matching," in *ICLR*, 2024.
- <span id="page-18-49"></span>[101] C. Zhou, C. Jiang, Y. Xie, H. Cao, and J. Yang, "Enhancing dataset distillation via label inconsistency elimination and learning pattern refinement," *arXiv preprint arXiv:2410.13311*, 2024.
- <span id="page-18-50"></span>[102] Z. Ma, A. Cao, F. Yang, and X. Wei, "Curriculum dataset distillation," *arXiv preprint arXiv:2405.09150*, 2024.
- <span id="page-18-51"></span>[103] X. Zhang, J. Du, P. Liu, and J. T. Zhou, "Breaking class barriers: Efficient dataset distillation via inter-class feature compensator," in *ICLR*, 2025.
- <span id="page-18-52"></span>[104] Z. Shen, A. Sherif, Z. Yin, and S. Shao, "Delt: A simple diversitydriven earlylate training for dataset distillation," *arXiv preprint arXiv:2411.19946*, 2024.

- <span id="page-19-0"></span>[105] J. Du, X. Zhang, J. Hu, W. Huang, and J. T. Zhou, "Diversitydriven synthesis: Enhancing dataset distillation through directed weight adjustment," in *NeurIPS*, 2024.
- <span id="page-19-1"></span>[106] H. Li, Y. Zhou, X. Gu, B. Li, and W. Wang, "Diversified semantic distribution matching for dataset distillation," in *ACM MM*, 2024.
- <span id="page-19-2"></span>[107] L. Zhang, J. Zhang, B. Lei, S. Mukherjee, X. Pan, B. Zhao, C. Ding, Y. Li, and D. Xu, "Accelerating dataset distillation via model augmentation," in *CVPR*, 2023.
- <span id="page-19-3"></span>[108] S. Kang, Y. Lim, and H. Shim, "Label-augmented dataset distillation," *arXiv preprint arXiv:2409.16239*, 2024.
- <span id="page-19-4"></span>[109] Z. Zhu, K. Lin, A. K. Jain, and J. Zhou, "Transfer learning in deep reinforcement learning: A survey," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023.
- <span id="page-19-5"></span>[110] A. Shul, E. Horwitz, and Y. Hoshen, "Distilling datasets into less than one image," *arXiv preprint arXiv:2403.12040*, 2024.
- <span id="page-19-6"></span>[111] J. Yang, K. Zhou, Y. Li, and Z. Liu, "Generalized out-ofdistribution detection: A survey," *International Journal of Computer Vision*, 2024.
- <span id="page-19-7"></span>[112] S. Jiang, Y. Zhu, C. Liu, X. Song, X. Li, and W. Min, "Dataset bias in few-shot image recognition," *IEEE transactions on pattern analysis and machine intelligence*, 2022.
- <span id="page-19-8"></span>[113] W. Huang, M. Ye, Z. Shi, G. Wan, H. Li, B. Du, and Q. Yang, "Federated learning for generalization, robustness, fairness: A survey and benchmark," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2024.
- <span id="page-19-9"></span>[114] J. Gui, T. Chen, J. Zhang, Q. Cao, Z. Sun, H. Luo, and D. Tao, "A survey on self-supervised learning: Algorithms, applications, and future trends," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2024.
- <span id="page-19-10"></span>[115] S. J. Pan and Q. Yang, "A survey on transfer learning," *IEEE Transactions on Knowledge and Data Engineering*, 2010.
- <span id="page-19-11"></span>[116] S. Ma, F. Zhu, Z. Cheng, and X.-Y. Zhang, "Towards trustworthy dataset distillation," *Pattern Recognition*, 2025.
- <span id="page-19-12"></span>[117] Y. Lu, J. Gu, X. Chen, S. Vahidian, and Q. Xuan, "Exploring the impact of dataset bias on dataset distillation," in *CVPR Workshop*, 2024.
- <span id="page-19-13"></span>[118] J. Cui, R. Wang, Y. Xiong, and C.-J. Hsieh, "Mitigating bias in dataset distillation," in *ICML*, 2024.
- <span id="page-19-14"></span>[119] Q. Zhou, S. Fang, S. He, W. Meng, and J. Chen, "Fairdd: Fair dataset distillation via synchronized matching," *arXiv preprint arXiv:2411.19623*, 2024.
- <span id="page-19-15"></span>[120] Z. Zhao, H. Wang, Y. Shang, K. Wang, and Y. Yan, "Distilling long-tailed datasets," *arXiv preprint arXiv:2408.14506*, 2024.
- <span id="page-19-16"></span>[121] D. B. Lee, S. Lee, J. Ko, K. Kawaguchi, J. Lee, and S. J. Hwang, "Self-supervised set representation learning for unsupervised meta-learning," in *ICLR*, 2024.
- <span id="page-19-17"></span>[122] S.-F. Yu, J.-J. Yao, and W.-C. Chiu, "Boost self-supervised dataset distillation via parameterization, predefined augmentation, and approximation," in *ICLR*, 2025.
- <span id="page-19-18"></span>[123] S. Joshi, J. Ni, and B. Mirzasoleiman, "Dataset distillation via knowledge distillation: Towards efficient self-supervised pretraining of deep networks," in *ICLR*, 2025.
- <span id="page-19-19"></span>[124] Y. Zhou, G. Pu, X. Ma, X. Li, and D. Wu, "Distilled one-shot federated learning," *arXiv preprint arXiv:2009.07999*, 2020.
- <span id="page-19-20"></span>[125] S. Hu, J. Goetz, K. Malik, H. Zhan, Z. Liu, and Y. Liu, "Fedsynth: Gradient compression via synthetic data in federated learning," in *NeurIPS Workshop*, 2022.
- <span id="page-19-21"></span>[126] J. Zhang, C. Chen, B. Li, L. Lyu, S. Wu, S. Ding, C. Shen, and C. Wu, "Dense: Data-free one-shot federated learning," in *NeurIPS*, 2022.
- <span id="page-19-22"></span>[127] P. Liu, X. Yu, and J. T. Zhou, "Meta knowledge condensation for federated learning," in *ICLR*, 2023.
- <span id="page-19-23"></span>[128] R. Song, D. Liu, D. Z. Chen, A. Festag, C. Trinitis, M. Schulz, and A. Knoll, "Federated learning via decentralized dataset distillation in resource-constrained edge environments," in *IJCNN*, 2023.
- <span id="page-19-24"></span>[129] Q. Pan, S. Sun, Z. Wu, Y. Wang, M. Liu, B. Gao, and J. Wang, "Fedcache 2.0: Federated edge learning with knowledge caching and dataset distillation," *Authorea Preprints*, 2024.
- <span id="page-19-25"></span>[130] Y. Xiong, R. Wang, M. Cheng, F. Yu, and C.-J. Hsieh, "Feddm: Iterative distribution matching for communication-efficient federated learning," in *CVPR*, 2023.
- <span id="page-19-26"></span>[131] R. Pi, W. Zhang, Y. Xie, J. Gao, X. Wang, S. Kim, and Q. Chen, "Dynafed: Tackling client data heterogeneity with global dynamics," in *CVPR*, 2023.

- <span id="page-19-27"></span>[132] Y. Wang, H. Fu, R. Kanagavelu, Q. Wei, Y. Liu, and R. S. M. Goh, "An aggregation-free federated learning for tackling data heterogeneity," in *CVPR*, 2024.
- <span id="page-19-28"></span>[133] X. Shi, W. Zhang, M. Wu, G. Liu, Z. Wen, S. He, T. Shah, and R. Ranjan, "Dataset distillation-based hybrid federated learning on non-iid data," *arXiv preprint arXiv:2409.17517*, 2024.
- <span id="page-19-29"></span>[134] Y. Jia, S. Vahidian, J. Sun, J. Zhang, V. Kungurtsev, N. Z. Gong, and Y. Chen, "Unlocking the potential of federated learning: The symphony of dataset distillation via deep generative latents," in *ECCV*, 2024.
- <span id="page-19-30"></span>[135] A. Dhasade, Y. Ding, S. Guo, A.-m. Kermarrec, M. De Vos, and L. Wu, "Quickdrop: Efficient federated unlearning by integrated dataset distillation," *arXiv preprint arXiv:2311.15603*, 2023.
- <span id="page-19-31"></span>[136] S. Xu, X. Ke, X. Su, S. Li, F. Xu, S. Zhong *et al.*, "Flip: Privacypreserving federated learning based on the principle of least privileg," *arXiv preprint arXiv:2410.19548*, 2024.
- <span id="page-19-32"></span>[137] C.-Y. Huang, K. Srinivas, X. Zhang, and X. Li, "Overcoming data and model heterogeneities in decentralized federated learning via synthetic anchors," in *ICML*, 2024.
- <span id="page-19-34"></span>[138] M. Rüb, P. Tuchel, A. Sikora, and D. Mueller-Gritschneder, "A continual and incremental learning approach for tinyml ondevice training using dataset distillation and model size adaption," in *ICPS*, 2024.
- <span id="page-19-35"></span>[139] H. Lee, J. Lee, and N. Kwak, "Practical dataset distillation based on deep support vectors," *arXiv preprint arXiv:2405.00348*, 2024.
- <span id="page-19-36"></span>[140] X. Chen, W. Meng, P. Wang, and O. Zhou, "Distributed boosting: An enhancing method on dataset distillation," in *CIKM*, 2024.
- <span id="page-19-37"></span>[141] H. Wei, H. Tang, X. Jia, Z. Wang, H. Yu, Z. Li, S. Satoh, L. Van Gool, and Z. Wang, "Physical adversarial attack meets computer vision: A decade survey," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2024.
- <span id="page-19-38"></span>[142] Y. Li, Y. Jiang, Z. Li, and S.-T. Xia, "Backdoor learning: A survey," *IEEE Transactions on Neural Networks and Learning Systems*, 2022.
- <span id="page-19-33"></span>[143] Y. Wu, J. Du, P. Liu, Y. Lin, W. Xu, and W. Cheng, "Ddrobustbench: An adversarial robustness benchmark for dataset distillation," *arXiv preprint arXiv:2403.13322*, 2024.
- <span id="page-19-39"></span>[144] Z. Zhou, W. Feng, S. Lyu, G. Cheng, X. Huang, and Q. Zhao, "Beard: Benchmarking the adversarial robustness for dataset distillation," *arXiv preprint arXiv:2411.09265*, 2024.
- <span id="page-19-40"></span>[145] E. Xue, Y. Li, H. Liu, Y. Shen, and H. Wang, "Towards adversarially robust dataset distillation by curvature regularization," *arXiv preprint arXiv:2403.10045*, 2024.
- <span id="page-19-41"></span>[146] Y. Liu, Z. Li, M. Backes, Y. Shen, and Y. Zhang, "Backdoor attacks against dataset distillation," in *ndss*, 2023.
- <span id="page-19-42"></span>[147] M.-Y. Chung, S.-Y. Chou, C.-M. Yu, P.-Y. Chen, S.-Y. Kuo, and T.-Y. Ho, "Rethinking backdoor attacks on dataset distillation: A kernel method perspective," in *ICLR*, 2024.
- <span id="page-19-43"></span>[148] J. Wu, N. Lu, Z. Dai, W. Fan, S. Liu, Q. Li, and K. Tang, "Backdoor graph condensation," *arXiv preprint arXiv:2407.11025*, 2024.
- <span id="page-19-44"></span>[149] Z. Chen, J. Geng, D. Zhu, H. Woisetschlaeger, Q. Li, S. Schimmler, R. Mayer, and C. Rong, "A comprehensive study on dataset distillation: Performance, privacy, robustness and fairness," *arXiv preprint arXiv:2305.03355*, 2023.
- <span id="page-19-45"></span>[150] H. Hu, Z. Salcic, L. Sun, G. Dobbie, P. S. Yu, and X. Zhang, "Membership inference attacks on machine learning: A survey," *ACM Computing Surveys (CSUR)*, 2022.
- <span id="page-19-46"></span>[151] J.-Y. Moon, J. U. Kim, and G.-M. Park, "Towards model-agnostic dataset condensation by heterogeneous models," in *ECCV*, 2024.
- <span id="page-19-47"></span>[152] L. Zhao, Y. Zhang, F. Chao, and R. Ji, "Boosting the crossarchitecture generalization of dataset distillation through an empirical study," *arXiv preprint arXiv:2312.05598*, 2023.
- <span id="page-19-48"></span>[153] Y. Zhao, X. Deng, X. Su, H. Xu, X. Li, Y. Liu, and S. You, "Metadd: Boosting dataset distillation with neural network architectureinvariant generalization," *arXiv preprint arXiv:2410.05103*, 2024.
- <span id="page-19-49"></span>[154] B. Zhou, L. Zhong, and W. Chen, "Improve cross-architecture generalization on dataset distillation," *arXiv preprint arXiv:2402.13007*, 2024.
- <span id="page-19-50"></span>[155] X. Zhong and C. Liu, "Towards mitigating architecture overfitting in dataset distillation," *arXiv preprint arXiv:2309.04195*, 2023.
- <span id="page-19-51"></span>[156] K. Han, Y. Wang, H. Chen, X. Chen, J. Guo, Z. Liu, Y. Tang, A. Xiao, C. Xu, Y. Xu et al., "A survey on vision transformer," *IEEE transactions on pattern analysis and machine intelligence*, 2022.
- <span id="page-19-52"></span>[157] Z. Wang, Y. Xu, C. Lu, and Y.-L. Li, "Dancing with still images: Video distillation via static-dynamic disentanglement," in *CVPR*, 2024.

- <span id="page-20-0"></span>[158] Y. Chen, S.-J. Guo, and L. Wang, "A large-scale study on video action dataset condensation," *arXiv preprint arXiv:2412.21197* , 2024.
- <span id="page-20-1"></span>[159] W. Jiang, R. Zhang, H. Li, X. Liu, H. Yang, and S. Yu, "Ddfad: Dataset distillation framework for audio data," *arXiv preprint arXiv:2407.10446*, 2024.
- <span id="page-20-2"></span>[160] J. Zhang, M. Yin, H. Wang, Y. Li, Y. Ye, X. Lou, J. Du, and E. Chen, "Td3: Tucker decomposition based dataset distillation method for sequential recommendation," in *WWW*, 2025.
- <span id="page-20-3"></span>[161] X. Wu, B. Zhang, Z. Deng, and O. Russakovsky, "Visionlanguage dataset distillation," 2024. [Online]. Available: [https:](https://openreview.net/forum?id=2y8XnaIiB8) [//openreview.net/forum?id=2y8XnaIiB8](https://openreview.net/forum?id=2y8XnaIiB8)
- <span id="page-20-4"></span>[162] Y. Xu, Z. Lin, Y. Qiu, C. Lu, and Y.-L. Li, "Low-rank similarity mining for multimodal dataset distillation," in *ICML*, 2024.
- <span id="page-20-5"></span>[163] S. S. Kushwaha, S. S. N. Vasireddy, K. Wang, and Y. Tian, "Audiovisual dataset distillation," *Transactions on Machine Learning Research*, 2024.
- <span id="page-20-6"></span>[164] E. J. Hu, Y. Shen, P. Wallis, Z. Allen-Zhu, Y. Li, S. Wang, L. Wang, and W. Chen, "Lora: Low-rank adaptation of large language models," in *ICLR*, 2022.
- <span id="page-20-7"></span>[165] M. Li, C. Cui, Q. Liu, R. Deng, T. Yao, M. Lionts, and Y. Huo, "Dataset distillation in medical imaging: A feasibility study," *arXiv preprint arXiv:2407.14429*, 2024.
- <span id="page-20-8"></span>[166] Z. Yu, Y. Liu, and Q. Chen, "Progressive trajectory matching for medical dataset distillation," *arXiv preprint arXiv:2403.13469* , 2024.
- <span id="page-20-9"></span>[167] A. J. Smola, A. Gretton, and K. Borgwardt, "Maximum mean discrepancy," in *ICONIP*, 2006.
- <span id="page-20-10"></span>[168] Z. Li and B. Kainz, "Image distillation for safe data sharing in histopathology," in *MICCAI*, 2024.
- <span id="page-20-11"></span>[169] H. Guan, X. Zhao, Z. Wang, Z. Li, and J. Kempe, "Discovering galaxy features via dataset distillation," in *NeurIPS Workshop* , 2023.
- <span id="page-20-12"></span>[170] K. W. Willett, C. J. Lintott, S. P. Bamford, K. L. Masters, B. D. Simmons, K. R. Casteels, E. M. Edmondson, L. F. Fortson, S. Kaviraj, W. C. Keel *et al.*, "Galaxy zoo 2: detailed morphological classifications for 304 122 galaxies from the sloan digital sky survey," *Monthly Notices of the Royal Astronomical Society*, 2013.
- <span id="page-20-13"></span>[171] D. Qi, J. Li, J. Peng, B. Zhao, S. Dou, J. Li, J. Zhang, Y. Wang, C. Wang, and C. Zhao, "Fetch and forge: Efficient dataset condensation for object detection," in *NeurIPS*, 2024.
- <span id="page-20-14"></span>[172] D. Hoiem, S. K. Divvala, and J. H. Hays, "Pascal voc 2008 challenge," *World Literature Today*, 2009.
- <span id="page-20-15"></span>[173] T.-Y. Lin, M. Maire, S. Belongie, J. Hays, P. Perona, D. Ramanan, P. Dollár, and C. L. Zitnick, "Microsoft coco: Common objects in context," in *ECCV*, 2014.
- <span id="page-20-16"></span>[174] T. Dietz, B. B. Moser, T. C. Nauen, F. Raue, S. Frolov, and A. Dengel, "A study in dataset distillation for image super-resolution," 2025. [Online]. Available: for image super-resolution," <https://api.semanticscholar.org/CorpusID:276161504>
- <span id="page-20-17"></span>[175] A. Liu, Y. Liu, J. Gu, Y. Qiao, and C. Dong, "Blind image superresolution: A survey and beyond," *IEEE transactions on pattern analysis and machine intelligence*, 2022.
- <span id="page-20-18"></span>[176] L. Cheng, K. Chen, J. Li, S. Tang, S. Zhang, and M. Wang, "Dataset distillers are good label denoisers in the wild," *arXiv preprint arXiv:2411.11924*, 2024.
- <span id="page-20-19"></span>[177] Z. Wu, Y. Liu, X. Shen, X. Cao, and X. Yu, "Trust-aware diversion for data-effective distillation," *arXiv preprint arXiv:2502.05027* , 2025.
- <span id="page-20-20"></span>[178] H. Lu, M. Isonuma, J. Mori, and I. Sakata, "Unidetox: Universal detoxification of large language models via dataset distillation," in *ICLR*, 2025.
- <span id="page-20-21"></span>[179] Z. Li, X. Zhong, Z. Liang, Y. Zhou, M. Shi, Z. Wang, W. Zhao, X. Zhao, H. Wang, Z. Qin, D. Liu, K. Zhang, T. Zhou, Z. Zhu, K. Wang, G. Li, J. Zhang, J. Liu, Y. Huang, L. Lyu, J. Lv, Y. Jin, Z. Akata, J. Gu, R. Vedantam, M. Shou, Z. Deng, Y. Yan, Y. Shang, G. Cazenavette, X. Wu, J. Cui, T. Chen, A. Yao, M. Kellis, K. N. Plataniotis, B. Zhao, Z. Wang, Y. You, and K. Wang, "Dd-ranking: Rethinking the evaluation of dataset distillation," GitHub repository, 2024. [Online]. Available: <https://github.com/NUS-HPC-AI-Lab/DD-Ranking>
# Beyond Modality Collapse: Representations Blending for Multimodal Dataset Distillation

 $\chi$ in Zhang<sup>1,2</sup> <PERSON><PERSON><PERSON><sup>3</sup> <PERSON><PERSON><PERSON><sup>1,2</sup> <PERSON><PERSON><PERSON><PERSON><sup>4</sup> <PERSON><sup>1,2</sup>

<sup>1</sup> Centre for Frontier AI Research, Agency for Science, Technology and Research, Singapore <sup>2</sup>Institute of High Performance Computing, Agency for Science, Technology and Research, Singapore  $3$ National University of Singapore, Singapore  $4$ Zhejiang University, China {zhangx7, dujw, <PERSON>\_<PERSON>}@cfar.astar.edu.sg <EMAIL> <EMAIL>

## Abstract

Multimodal Dataset Distillation (MDD) seeks to condense large-scale image-text datasets into compact surrogates while retaining their effectiveness for cross-modal learning. Despite recent progress, existing MDD approaches often suffer from *Modality Collapse*, characterized by over-concentrated intra-modal representations and enlarged distributional gap across modalities. In this paper, at the first time, we identify this issue as stemming from a fundamental conflict between the over-compression behavior inherent in dataset distillation and the cross-modal supervision imposed by contrastive objectives. To alleviate modality collapse, we introduce RepBlend, a novel MDD framework that weakens overdominant crossmodal supervision via representation blending, thereby significantly enhancing intra-modal diversity. Additionally, we observe that current MDD methods impose asymmetric supervision across modalities, resulting in biased optimization. To address this, we propose symmetric projection trajectory matching, which synchronizes the optimization dynamics using modality-specific projection heads, thereby promoting balanced supervision and enhancing cross-modal alignment. Experiments on Flickr-30K and MS-COCO show that RepBlend consistently outperforms prior state-of-the-art MDD methods, achieving significant gains in retrieval performance (e.g.,  $+9.4 \text{ IR} @ 10, +6.3 \text{ TR} @ 10$  under the 100-pair setting) and offering up to  $6.7\times$  distillation speedup.

# 1 Introduction

The unprecedented expansion of large-scale datasets has catalyzed recent breakthroughs in deep learning [\[6,](#page-10-0) [2,](#page-10-1) [1\]](#page-10-2), but has also introduced considerable storage and computational overhead [\[20,](#page-11-0) [22\]](#page-11-1). Thus, reducing dataset size to streamline the development process has emerged as an important research focus. Among various solutions, Dataset Distillation (DD) [\[48\]](#page-12-0) has emerged as a compelling strategy, achieving high compression ratios by synthesizing a compact surrogate dataset that approximates the training efficacy of the original dataset. The effectiveness of DD has been demonstrated across various modalities, including images [\[4,](#page-10-3) [54\]](#page-12-1), text [\[30,](#page-11-2) [32\]](#page-11-3), videos [\[11,](#page-10-4) [49\]](#page-12-2), and graphs [\[29,](#page-11-4) [55\]](#page-13-0). These unimodal successes motivate its extension to increasingly prominent multimodal scenarios [\[36,](#page-12-3) [28,](#page-11-5) [34,](#page-11-6) [5\]](#page-10-5).

The pioneering effort in multimodal dataset distillation (MDD) is MTT-VL [\[51\]](#page-12-4), which first validates the feasibility of extending existing vanilla DD techniques to the image-text setting. Building on this baseline, LoRS [\[52\]](#page-12-5) further proposes to mine cross-modal similarity to calibrate the supervision from matched and mismatched pairs, thereby achieving better adaptation to high-variance image-text data. Despite achieving promising results, existing studies remain confined to the data structure level,

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays three spheres, each representing a different method of embedding data. The first sphere, labeled "Random," shows a scattered distribution of red triangles representing image embeddings and blue circles representing text embeddings. The second sphere, labeled "LoRS," illustrates a "Modality Collapse" with a dashed line dividing the sphere, showing a concentration of blue circles (text embeddings) on one side and red triangles (image embeddings) on the other. The third sphere, labeled "Ours," depicts a more balanced distribution of both red triangles and blue circles across the sphere, suggesting a better alignment between image and text embeddings.

Figure 1: Multimodal embedding distributions across various distillation methods. We extract image and text embeddings from a finetuned CLIP [\[36\]](#page-12-3) and project them into a shared representation space using DOSNES [\[31\]](#page-11-7). Red triangles and blue circles denote image and text embeddings, respectively. Left: Embeddings from randomly sampled data in the original dataset exhibit a well-spread and modality-aligned distribution. **Middle**: The distilled dataset generated by a sota MDD method (LoRS [\[52\]](#page-12-5)) leads to *Modality Collapse*, where image and text embeddings are poorly aligned and concentrated in distinct regions. Right: Our method effectively mitigates modality collapse, yielding a distribution that better preserves cross-modal alignment and exhibits greater representational diversity.

without probing the underlying conflict between DD and contrastive learning. Specifically, to prevent significant performance deterioration, vanilla DD prioritizes capturing representative features under limited distillation budgets, often sacrificing diversity and distributional coverage [\[14,](#page-10-6) [18,](#page-11-8) [15\]](#page-10-7). While this compromise is tolerable in unimodal classification tasks, naively applying such strategies to multimodal contrastive learning, which places great importance on instance-level discriminability, inevitably leads to *Modality Collapse*. As illustrated in [Figure 1](#page-1-0) (middle), the distilled dataset exhibits pronounced intra-modality aggregation and inter-modality separation.

This modality collapse leads to two critical issues. First, *it induces excessive intra-modal similarity*, where embeddings within each modality become increasingly concentrated as distillation progresses. This over-concentration gradually suppresses representational diversity, making semantically distinct instances harder to separate, and eroding the fine-grained discrimination ability within each modality. Second, *it widens the inter-modal gap*, resulting in a large divergence between the feature distributions of different modalities. Insufficient cross-modal interaction fragments the embedding spaces and weakens semantic alignment, compromising the correct matching of positive pairs and the separation of negative pairs across modalities.

Recognizing these limitations, we propose **RepBlend**, a novel framework for MDD aimed at alleviating modality collapse. First, we theoretically identify that the collapse is induced by the over-compression nature of DD, where optimization converges toward a small set of dominant features. Cross-modal contrastive supervision further reinforces this convergence, leading to intra-modal collapse. To address this issue, RepBlend introduces Representation Blending within each modality to weaken the overly strong cross-modal supervision, thereby promoting intra-modal diversity.

Furthermore, we observe that existing MDD approaches exhibit asymmetric supervision between modalities, with the image branch receiving significantly weaker update signals than the text branch. To address this, we propose Symmetric Projection Trajectory Matching, a mechanism that aligns the optimization trajectories of both projection heads, thereby enhancing cross-modal alignment and improving overall distillation efficiency. Extensive evaluations on Flickr-30K and MS-COCO demonstrate that RepBlend consistently surpasses existing MDD methods. Notably, under the 100-pair setting on Flickr-30K, it achieves improvements of +9.4 in IR@10 and +6.3 in TR@10, along with a 6.7× distillation speedup over the state-of-the-art baseline. Beyond these benchmarks, RepBlend also exhibits strong generalization to other multimodal scenarios, such as audio-text.

Our contributions are summarized as follows:

• For the first time, we identify the modality collapse issue in current MDD solutions, where the distilled dataset exhibits high intra-modal similarity and a large inter-modal gap. Through theoretical analysis, we attribute this to a mutually reinforcing effect between the overcompression behavior of dataset distillation and the cross-modal supervision enforced by contrastive objectives.

<span id="page-2-3"></span>Image /page/2/Figure/0 description: The image displays a comparison of two methods, LoRS and Ours, in terms of intra-modal similarity over iterations. The left panel shows a line graph with the x-axis representing iterations and the y-axis representing intra-modal similarity. The LoRS method (red line with circles) shows a 'collapsing' trend, starting at approximately 0.512 and increasing to 0.522 at 3000 iterations, with a CR of 0.95. The Ours method (blue line with triangles) shows a 'maintaining' trend, starting at approximately 0.511, dipping slightly, and then increasing to approximately 0.5115 at 3000 iterations, with a CR of 0.57. A dashed grey line indicates a 'Random' baseline at approximately 0.5105. The right two panels show heatmap matrices. The middle panel, labeled 'LoRS', shows a heatmap of learned groundtruth matrix with image and text indices on the axes, indicating a strong diagonal pattern. The rightmost panel, labeled 'Ours', also shows a heatmap of the learned groundtruth matrix, with a similar diagonal pattern but appearing slightly more dispersed than the LoRS matrix.

Figure 2: Left: Increasing intra-modal similarity as distillation progresses. We run optimization for 3000 iterations and track the intra-modal cosine similarity, which increases from 0.512 to 0.522 (red curve). Though small in magnitude, this rise leads to a more than twofold increase in concentration ratio  $(CR)^2$  $(CR)^2$  due to the high dimensionality of the embedding space. **Right**: Modality collapse undermines the effectiveness of learned soft cross-modal correspondence. The non-matching imagetext pairs exhibit nearly uniform similarity scores, forming horizontal and vertical stripes.

• We propose Representation Blending to mitigate modality collapse by weakening the overly strong cross-modal supervision and enhancing intra-modal representational diversity. Furthermore, we introduce Symmetric Projection Trajectory Matching to enable more balanced multimodal distillation, which not only strengthens cross-modal alignment but also improves overall distillation efficiency.

## 2 Preliminaries and Related Works

Dataset Distillation (DD) [\[48\]](#page-12-0) aims to synthesize a compact surrogate dataset by emulating the key properties of the original large dataset. These properties include distributional characteristics, such as feature-level statistics [\[57,](#page-13-1) [46,](#page-12-6) [47\]](#page-12-7) and batch normalization parameters [\[54,](#page-12-1) [41,](#page-12-8) [15\]](#page-10-7), and training dynamics, including gradient [\[58,](#page-13-2) [56\]](#page-13-3) and optimization trajectories [\[4,](#page-10-3) [7,](#page-10-8) [14,](#page-10-6) [18,](#page-11-8) [25\]](#page-11-9). While DD achieves promising results on unimodal benchmarks, extending it to multimodal scenarios remains challenging due to unique data structure and learning strategy [\[51,](#page-12-4) [52\]](#page-12-5). We first formalize the problem of Multimodal Dataset Distillation (MDD).

**Problem Formulation.** Given a large-scale image-text dataset  $\mathcal{D} = \{(\boldsymbol{x}_i, \tau_i), \boldsymbol{y}_i\}_{i=1}^{|\mathcal{D}|}$ , where  $x_i \in \mathbb{R}^{d_{\text{img}}}$  and  $\tau_i \in \mathbb{R}^{d_{\text{text}}}$  denote the *i*-th image and its paired caption representation<sup>[1](#page-2-1)</sup>, and each pair is independently sampled from a natural data distribution  $\mathcal{P}.$  Each  $\bm{y}_i \in \{0,1\}^{|\mathcal{D}|}$  is a one-hot vector indicating the correspondence between  $x_i$  and the caption set  ${\{\tau_j\}}_{j=1}^{|\mathcal{D}|}$ , with the *i*-th entry activated. Similar to DD, MDD also aims to minimize the loss on original dataset using the model trained on its distilled synthetic counterpart  $\mathcal{S} = \{(\tilde{x}_i, \tilde{\tau}_i), \tilde{y}_i\}_{i=1}^{|S|}$ :

<span id="page-2-2"></span>
$$
\mathcal{S}^* = \underset{\mathcal{S}}{\arg\min} \underset{(\mathbf{x},\boldsymbol{\tau}) \sim \mathcal{P}}{\mathbb{E}} [\mathcal{L}(f_{\boldsymbol{\theta}_{\mathcal{S}}}(\mathbf{x},\boldsymbol{\tau}), \boldsymbol{y})] \quad \text{s.t.} \quad \boldsymbol{\theta}_{\mathcal{S}} = \underset{\boldsymbol{\theta}}{\arg\min} \underset{(\tilde{\mathbf{x}},\tilde{\boldsymbol{\tau}}) \sim \mathcal{S}}{\mathbb{E}} [\mathcal{L}(f_{\boldsymbol{\theta}}(\tilde{\mathbf{x}},\tilde{\boldsymbol{\tau}}), \tilde{\boldsymbol{y}})], \quad (1)
$$

where  $|S| \ll |\mathcal{D}|$ , and  $\mathcal L$  denotes the contrastive learning loss. The model  $f_{\theta}(\cdot)$  represents a CLIPstyle network parameterized by  $\theta$ . Each distilled sample consists of a synthetic image-text pair  $(\tilde{x}_i, \tilde{\tau}_i)$ , where  $\tilde{x}_i \in \mathbb{R}^{d_{\text{img}}}$  and  $\dot{\tilde{\tau}}_i \in \mathbb{R}^{d_{\text{text}}}$ , accompanied by a learned soft label  $\tilde{y}_i$ .

MDD vs. Vanilla DD. According to the [Equation 1,](#page-2-2) the generalization from vanilla DD to MDD involves two key modifications: 1) introducing soft ground-truth vectors  $\tilde{y}_i$ , and 2) optimizing under a contrastive learning loss  $\mathcal L$  for image-text alignment. While learning soft labels is common in vanilla DD [\[7\]](#page-10-8), optimizing  $\tilde{y}_i$  in MDD is more challenging, as both image and text representations are updated simultaneously. Besides, in practice, the contrastive loss  $\mathcal L$  is typically instantiated as InfoNCE [\[33\]](#page-11-10), extended InfoNCE (eNCE), or weighted BCE (wBCE) [\[52\]](#page-12-5), all aiming to strengthen positive alignments while penalizing mismatched pairs. However, these extensions only make the multimodal adaptation feasible, overlooking the essence of dataset distillation: effective information

<span id="page-2-1"></span><sup>&</sup>lt;sup>1</sup>Given the discrete nature of text, all subsequent analysis is conducted in the representation space, while images remain processed in the pixel space. Here,  $d_{\text{img}} = W \times H \times 3$  and  $d_{\text{text}} = 768$  (for BERT [\[10\]](#page-10-9)).

<span id="page-2-0"></span><sup>&</sup>lt;sup>2</sup>CR measures how tightly the features are clustered, based on how much of the hypersphere is covered at the given cosine similarity. (Refer to Appendix  $C$  for more calculation details).

condensation. More specifically, they prioritize cross-modal alignment, while failing to preserve intra-modal diversity and discriminability under severe data compression.

# 3 Methodology

In this section, we introduce **RepBlend**, a novel approach for MDD. We begin by identifying the phenomenon of *Modality Collapse*, which emerges when vanilla DD methods are naively applied to multimodal settings. Through theoretical and empirical analysis, we uncover its underlying causes. To address this issue, we propose Representation Blending to enhance intra-modal diversity. In addition, we introduce Symmetric Projection Trajectory Matching, which balances the distillation process across modalities and further strengthens cross-modal alignment. The overall pipeline of RepBlend is outlined in [Algorithm 1.](#page-6-0)

### 3.1 Modality Collapse

LoRS [\[52\]](#page-12-5) is a representative MDD method built upon [Equation 1,](#page-2-2) where  $\mathcal L$  is defined as:

<span id="page-3-3"></span>
$$
\mathcal{L}_{\text{wBCE}}^{\mathcal{B}} = \sum_{i,j}^{|\mathcal{B}|} w_{ij} \cdot \ell\left(\tilde{\mathbf{y}}_{ij}, \sigma\left(\hat{\mathbf{y}}_{ij}/\gamma\right)\right), \quad w_{ij} = \frac{\mathbb{I}[\tilde{\mathbf{y}}_{ij} > \beta]}{|\{(i,j) : \tilde{\mathbf{y}}_{ij} > \beta\}|} + \frac{\mathbb{I}[\tilde{\mathbf{y}}_{ij} \le \beta]}{|\{(i,j) : \tilde{\mathbf{y}}_{ij} \le \beta\}|}.
$$
 (2)

Here,  $\mathcal{B} \subset \mathcal{S}$  denotes a sampled batch.  $\hat{y}_{ij}$  represents the cosine similarity between the normalized image and text embeddings, where  $\tilde{x}'_i = \text{Normalize}(f^{\text{imgE}}(\tilde{x}_i))^3$  $\tilde{x}'_i = \text{Normalize}(f^{\text{imgE}}(\tilde{x}_i))^3$  and  $\tilde{\tau}'_j = \text{Normalize}(f^{\text{textP}}(\tilde{\tau}_j)),$ with  $f^{imgE}(\cdot)$  and  $f^{textP}( \cdot)$  denoting the image encoder and text projection head, respectively. The threshold  $\beta$  is used to determine positive and negative pairs,  $\sigma(\cdot)$  denotes the sigmoid function, and  $\gamma$ is the temperature.  $\ell(\cdot, \cdot)$  refers to the binary cross-entropy loss. While this supervision primarily aims to mine cross-modal relationships, it inadvertently reinforces intra-modal similarities, ultimately leading to *Modality Collapse*, as shown in [Figure 1,](#page-1-0) where instances within each modality excessively concentrate. Without loss of generality, the following analysis focuses on the image modality.

*Proposition: Cross-modal supervision reinforces intra-modal similarity.* During dataset distillation, if  $\{\tilde{x}_n, \tilde{\tau}_n\}$  and  $\{\tilde{x}_m, \tilde{\tau}_m\}$  exhibit some non-negligible similarity, i.e.,  $\tilde{y}_{nm} \approx \tilde{y}_{mn} > \beta$ , then the direction of their subsequent updates  $\frac{\partial \mathcal{L}}{\partial \tilde{\mathbf{x}}'_n}$  $\frac{\partial \mathcal{L}}{\partial \tilde{\mathbf{x}}'_m}$  is determined by

<span id="page-3-1"></span>
$$
\frac{w_{nm}w_{mn}}{\gamma^2}[\sigma(\hat{\mathbf{y}}_{nm})/t-\tilde{\mathbf{y}}_{nm}][\sigma(\hat{\mathbf{y}}_{mn})/t-\tilde{\mathbf{y}}_{mn}]\tilde{\boldsymbol{\tau}}_m^{\prime\top}\tilde{\boldsymbol{\tau}}_n^{\prime},
$$
\n(3)

which indicates that the optimization is guided by positive pairs  $\tilde{\tau}_m^{\prime \top} \tilde{\tau}_n^{\prime}$ , promoting concentration in similar directions. A detailed derivation is provided in [Appendix B.](#page-14-0) When distilling a large dataset into a compact one, the optimization process tends to be dominated by a few salient features [\[9,](#page-10-10) [15,](#page-10-7) [18,](#page-11-8) [42\]](#page-12-9). Once this convergence trend emerges, cross-modal supervision further reinforces it: modality-specific diversity is implicitly suppressed, and intra-modal representations are increasingly aligned toward a limited set of dominant directions. As illustrated in [Figure 2](#page-2-3) (left), *the intra-modal similarity consistently increases throughout the distillation process*.

In addition to the aggravated intra-modal similarity, modality collapse also exacerbates the cross-modal representation gap, as features from each modality become increasingly centralized within compact regions of the shared embedding

<span id="page-3-2"></span>Image /page/3/Figure/11 description: A bar chart displays intra-modal similarity and modality gap across different lambda values and a baseline (RB). The left y-axis represents intra-modal similarity, ranging from 0.5075 to 0.5250. The right y-axis represents modality gap, ranging from 0.31 to 0.36. For lambda = 0.0, intra-modal similarity is approximately 0.5215 and modality gap is approximately 0.5135. For lambda = 0.01, intra-modal similarity is approximately 0.5175 and modality gap is approximately 0.5185. For lambda = 0.05, intra-modal similarity is approximately 0.5175 and modality gap is approximately 0.5265. For RB, intra-modal similarity is approximately 0.5115 and modality gap is approximately 0.5100.

Figure 3: As the noise level  $\lambda$  increases, intramodal similarity (blue bars) shows a slight decline, while the modality gap (yellow bars) rises markedly. In contrast, our representation blending (RB) leverages in-distribution samples to simultaneously reduce intra-modal similarity and inter-modal gap, effectively mitigating modality collapse during distillation.

space. Consequently, the similarities between non-matching image-text pairs converge toward a

<span id="page-3-0"></span><sup>&</sup>lt;sup>3</sup>In LoRS [\[52\]](#page-12-5), no image projection head is used.

uniform distribution. Such behavior undermines the utility of soft label distributions, which are designed to encode fine-grained relational information beyond the binary supervision provided by one-hot labels. As illustrated in [Figure 2](#page-2-3) (right), *non-diagonal similarity values exhibit a near-uniform pattern*, where image embeddings produce nearly constant similarity scores across all non-matching text embeddings (manifesting as horizontal stripes), and vice versa for text samples (vertical stripes).

### 3.2 Mitigating Modality Collapse via Representation Blending

As analyzed in [Equation 3,](#page-3-1) modality collapse arises from overly strong cross-modal supervision, which implicitly encourages intra-modal concentration and undermines representational diversity. To alleviate this constraint, one potential approach is to inject directional signals that deviate from  $\tilde{\tau}'_m$ and  $\tilde{\tau}'_n$ . To empirically validate this hypothesis and explore a viable remedy, we conduct a controlled perturbation experiment on Flickr-30K [\[35\]](#page-11-11). In particular, we adopt two key metrics following [\[26\]](#page-11-12): the intra-modal similarity (Sim) and the modality gap (Gap), defined as,

$$
\text{Sim} = \frac{1}{|\mathcal{S}|(|\mathcal{S}|-1)} \sum_{i \neq j}^{|\mathcal{S}|} \tilde{x}'_i \tilde{x}'_j, \quad \text{Gap} = \frac{1}{|\mathcal{S}|} \|\sum_{i=1}^{|\mathcal{S}|} \tilde{x}'_i - \sum_{j=1}^{|\mathcal{S}|} \tilde{\tau}'_j\|_2. \tag{4}
$$

We inject Gaussian noise into the text representations,

 $\tilde{\tau}_m^{\prime\text{+noise}} = \text{Normalize}\left(f^{\text{textP}}((1-\lambda)\tilde{\tau}_m + \lambda \vec{\Delta}_m)\right), \quad \tilde{\tau}_n^{\prime\text{+noise}} = \text{Normalize}\left(f^{\text{textP}}((1-\lambda)\tilde{\tau}_n + \lambda \vec{\Delta}_n)\right),$ 

where  $\vec{\Delta}_m$  and  $\vec{\Delta}_n$  are independently sampled random noise from  $\mathcal{N}(0, 1)$ , and  $\lambda$  controls the noise level. We evaluate Sim and Gap under varying levels of λ. As shown in [Figure 3,](#page-3-2) a slight increase in noise reduces intra-modal similarity (blue bars), indicating enhanced modality-specific diversity. These results support our hypothesis that perturbing in the representation space can effectively counteract modality concentration.

However, as noise level continues to grow, the injected perturbation begins to introduce semantically meaningless signals, which hinders cross-modal alignment. This is evidenced by the growing modality gap (yellow bars), accompanied by a performance drop of 1.9% in IR@1 and 2.1% in TR@1 at  $\lambda = 0.01$  under 100 distilled pairs on Flickr-30K dataset. To mitigate this issue, we propose replacing the random perturbation with a structure-preserving variant using in-distribution samples. Specifically, we blends representations from different synthetic instances:

$$
\tilde{\tau}_m^{\text{bbend}} = \text{Normalize}\left(f^{\text{textP}}((1-\lambda)\tilde{\tau}_m + \lambda \tilde{\tau}_i)\right), \quad \tilde{\tau}_n^{\text{bbend}} = \text{Normalize}\left(f^{\text{textP}}((1-\lambda)\tilde{\tau}_n + \lambda \tilde{\tau}_j)\right), \quad (5)
$$

where  $1 \leq i, j \leq |\mathcal{S}|$ . This operation resembles the idea of MixUp, but is applied in the representation space. As shown in the last group of [Figure 3,](#page-3-2) we can maintain a low level of intra-modal similarity and small modality gap. Note that although here we illustrate the formulation on text, the same operation is also applied to image side in practice.

### 3.3 Enhancing Cross-modal Alignment via Symmetric Projection Trajectory Matching

In prior MDD practices, methods such as MTT-VL [\[51\]](#page-12-4) and LoRS [\[52\]](#page-12-5) follow a de facto protocol wherein the text encoder is frozen and the image projection layer is omitted. The image encoder and the text projection head are trained to generate expert trajectories for distillation. In this setup, the image encoder is initialized with pretrained weights from ImageNet-1K [\[8\]](#page-10-11), while the text projection head is trained from scratch. This design is motivated by two key considerations: 1) the prohibitive computational and memory cost of optimizing and storing expert trajectories for large-scale text encoders such as BERT [\[10\]](#page-10-9); and 2) the fact that text distillation operates in the representation space, where supervision is applied only through the projection head, thus, matching at the encoder level cannot propagate supervision to the representation space. LoRS [\[52\]](#page-12-5) minimize the objective in [Equation 1](#page-2-2) through trajectory matching, which is formulated as follows:

$$
\tilde{\boldsymbol{x}}^*, \tilde{\boldsymbol{\tau}}^*, \tilde{\boldsymbol{y}}^* = \argmin_{\tilde{\boldsymbol{x}}, \tilde{\boldsymbol{\tau}}, \tilde{\boldsymbol{y}}} \left( \left\| \boldsymbol{\theta}_{\mathcal{S}_{\text{imgE}}}^{t+T} - \boldsymbol{\theta}_{\mathcal{D}_{\text{imgE}}}^{t+M} \right\|_2^2 + \left\| \boldsymbol{\theta}_{\mathcal{S}_{\text{levIP}}}^{t+T} - \boldsymbol{\theta}_{\mathcal{D}_{\text{levIP}}}^{t+M} \right\|_2^2 \right) / \left( \left\| \boldsymbol{\theta}_{\mathcal{D}_{\text{imgE}}}^{t} - \boldsymbol{\theta}_{\mathcal{D}_{\text{imgE}}}^{t+M} \right\|_2^2 + \left\| \boldsymbol{\theta}_{\mathcal{D}_{\text{levIP}}}^{t} - \boldsymbol{\theta}_{\mathcal{D}_{\text{levIP}}}^{t+M} \right\|_2^2 \right),
$$

where  $\bm{\theta}^{t+T}_{\mathcal{S} \text{imgE}}$  and  $\bm{\theta}^{t+T}_{\mathcal{S} \text{text}}$  denote the  $T$ -step finetuned weights of the image encoder and text projection head using S, initialized from  $\theta_{\mathcal{D}_{\text{imgE}}}^t$  and  $\theta_{\mathcal{D}_{\text{text}}}^t$ , respectively. The objective is to align the T-step synthetic trajectory with the M-step real trajectory by minimizing the  $\ell_2$  distance between their terminal weights, given the same initialization.

<span id="page-5-0"></span>Image /page/5/Figure/0 description: The image displays three plots. The first plot, titled "LoRS", shows the trajectory matching loss for text and image over 3000 iterations. The text loss starts at approximately 0.95 and decreases to about 0.7, while the image loss starts at approximately 0.98 and increases to about 1.0. The second plot, titled "LoRS + SM", also shows the trajectory matching loss for text and image over 3000 iterations. Both text and image losses start at approximately 0.98 and decrease to around 0.72. The third plot is a bar chart comparing the "Update Norm Relative to Initialization" for LoRS and LoRS+SM for both image and text. For LoRS, the image update norm is 0.69 and the text update norm is 0.90, with a gap of 0.327. For LoRS+SM, the image update norm is 0.91 and the text update norm is 1.01, with a gap of 0.290.

Figure 4: Current MDD methods adopt asymmetric distillation. Left: The loss on the image side shows much smaller variation than that of the text side, fluctuating mildly around 1.0 without notable reduction. Right: The update norm relative to initialization is significantly lower for the image modality in LoRS (0.69) compared to the text modality (0.90), suggesting insufficient representation transfer. The update norm is computed in the shared representation space for both modalities. After incorporating symmetric matching (SM), both image and text modalities exhibit more balanced and synchronized update dynamics, leading to more effective cross-modal alignment (reduced Gap).

However, the aforementioned trajectory matching is asymmetric. As shown in [Figure 4](#page-5-0) (left), the trajectory matching losses of the image and text modalities exhibit divergent trends: the text-side loss decreases steadily, whereas the image-side loss quickly plateaus and remains relatively high. This is primarily because the image encoder contains significantly more parameters than the text projection head, thus, even small per-parameter errors can accumulate into a large overall mismatch. This imbalance is further evidenced in [Figure 4](#page-5-0) (right), the norm of updates relative to initialization for the image modality is significantly smaller than that of the text, indicating insufficient distillation on the image side. While the representation blending introduced in the previous section helps narrow the modality gap, its effect is still constrained by the inherently asymmetric distillation. To address this imbalance and further enhance cross-modal alignment, we propose a symmetric distillation strategy by matching trajectories of projection head for both modalities:

<span id="page-5-1"></span>
$$
\tilde{\boldsymbol{x}}^*, \tilde{\boldsymbol{\tau}}^*, \tilde{\boldsymbol{y}}^* = \underset{\tilde{\boldsymbol{x}}, \tilde{\boldsymbol{\tau}}, \tilde{\boldsymbol{y}}}{\arg \min} \left( \left\| \boldsymbol{\theta}_{\mathcal{S}_{\text{image}}}^{t+T} - \boldsymbol{\theta}_{\mathcal{D}_{\text{image}}}^{t+M} \right\|_2^2 + \left\| \boldsymbol{\theta}_{\mathcal{S}_{\text{level}}}^{t+T} - \boldsymbol{\theta}_{\mathcal{D}_{\text{level}}}^{t+M} \right\|_2^2 \right) / \left( \left\| \boldsymbol{\theta}_{\mathcal{D}_{\text{image}}}^{t} - \boldsymbol{\theta}_{\mathcal{D}_{\text{image}}}^{t+M} \right\|_2^2 + \left\| \boldsymbol{\theta}_{\mathcal{D}_{\text{text}}}^{t} - \boldsymbol{\theta}_{\mathcal{D}_{\text{text}}}^{t+M} \right\|_2^2 \right). \tag{6}
$$

Here, the image encoder is initialized with ImageNet-1K pretrained weights and kept frozen. While the added image projection head incurs slight computational overhead, it enables projection-based matching that significantly enhances the overall efficiency of the distillation process (as discussed in Section [4.4\)](#page-8-0). As shown in [Figure 4,](#page-5-0) symmetric projection matching leads to a more consistent decrease in loss for both image and text branches. Moreover, the increased magnitude of updates suggests stronger supervision signals across modalities, resulting in a more balanced and effective distillation process. With symmetric distillation, the modality gap is further narrowed from 0.318 (in [Figure 3\)](#page-3-2) to 0.290, indicating enhanced cross-modal alignment.

# 4 Experiments

In this section, we conduct extensive experiments on multiple benchmark datasets to demonstrate the effectiveness of the proposed RepBlend framework. We first present the experimental setup, including the datasets, baseline methods, and implementation details. The main results are summarized in [Table 1](#page-7-0) and [Table 2.](#page-7-1) In addition, we also provide detailed ablation studies to evaluate the individual contribution of each component. All experiments are conducted using two NVIDIA RTX 3090 GPUs and one NVIDIA H100 GPU.

### 4.1 Experimental Setup

Datasets and Networks. We evaluate our method on two widely-used image captioning datasets: Flickr-30K [\[35\]](#page-11-11) and MS-COCO [\[27\]](#page-11-13), which contain approximately 31k and 123k images respectively, with each image paired with five human-annotated captions. For the image encoder, we experiment with NFNet [\[3\]](#page-10-12), RegNet [\[37\]](#page-12-10), ResNet-50 [\[19\]](#page-11-14), and ViT [\[12\]](#page-10-13). For the text encoder, we consider both BERT [\[10\]](#page-10-9) and DistilBERT [\[39\]](#page-12-11). To further demonstrate the generalizability of our approach across modalities, we extend our evaluation to the AudioCaps [\[23\]](#page-11-15) audio-text benchmark, utilizing EfficientAT [\[40\]](#page-12-12) as the audio encoder. Model performance is primarily evaluated using Recall at K  $(R@K)$  in cross-modal retrieval tasks. Given a query from one modality, we retrieve the top- $K$  most similar samples from the other modality and measure the retrieval accuracy. We denote text-to-image retrieval as IR@K, and image-to-text retrieval as TR@K.

#### <span id="page-6-0"></span>Algorithm 1 Blending Representations to Mitigate Modality Collapse in MDD

**Require:** Original large dataset D; CLIP-style network  $\{f^{imgE}, f^{textE}}$ ,  $f^{textE}}$ ,  $f^{imgP}$ ,  $f^{textE}$ }; real trajectories set  $\Theta_{\mathcal{D}_{\text{inner}}}$  and  $\Theta_{\mathcal{D}_{\text{user}}}$ , real trajectory matching length M, synthetic trajectory matching length  $T$ ; total optimization iteration number  $Iter$ 1: Initialize S with  $|S|$  randomly sampled image-text pairs and one-hot groundtruth labels 2: Load pretrained weights into encoders (frozen); randomly initialize projection heads 3: for  $it = 1$  to Iter do 4: Sample  $\bm{\theta}_{\mathcal{D}_{\text{image}}}^t$ ,  $\bm{\theta}_{\mathcal{D}_{\text{text}}}^t$  and  $\bm{\theta}_{\mathcal{D}_{\text{image}}}^{t+M}$ ,  $\bm{\theta}_{\mathcal{D}_{\text{text}}}^{t+M}$  from  $\bm{\Theta}_{\mathcal{D}_{\text{image}}}$  and  $\bm{\Theta}_{\mathcal{D}_{\text{text}}}$ 5: Initialize  $\theta_{\mathcal{S}_{\text{image}}}^t$  and  $\theta_{\mathcal{S}_{\text{text}}^t}^t$  using  $\theta_{\mathcal{D}_{\text{image}}}^t$  and  $\theta_{\mathcal{D}_{\text{text}}}^t$ 6: **for**  $i = 1$  to  $\overline{T}$  **do** 7: **for** mini-batch  $B = \{(\tilde{x}_b, \tilde{\tau}_b), \tilde{y}_b\}_{b=1}^{|B|} \in S$  **do** 8: Calculate image representaion  $\{f^{imgE}(\tilde{x}_b)\}$ 9: **b** D Blending in representation space 10:  $\{f^{imgE}(\tilde{x}_b), \tilde{\tau}_b\} = \text{RepBlend}(\{f^{imgE}(\tilde{x}_b), \tilde{\tau}_b\})$ 11: Compute loss  $\mathcal{L}^B_{\text{wBCE}}$  using [Equation 2](#page-3-3) 12: Update projection head weights  $\theta_{S_{\text{image}}}^{t+i}$  and  $\theta_{S_{\text{text}}}^{t+i}$ 13: end for 14: ▷ Symmetric projection trajectory matching 15: Optimize  $S = \{(\tilde{x}_j, \tilde{\tau}_j), \tilde{y}_j\}_{j=1}^{|S|}$  according to [Equation 6](#page-5-1) 16: end for 17: end for Ensure: Synthetic dataset S

Baselines. The comparison encompasses a range of state-of-the-art approaches, including coreset selection methods such as Random sampling, Herding [\[50\]](#page-12-13), K-Center [\[16\]](#page-10-14), and Forgetting [\[45\]](#page-12-14), as well as recent advances in dataset distillation tailored for vision-language models, including MTT-VL [\[51\]](#page-12-4), TESLA-VL [\[52\]](#page-12-5), and LoRS [\[52\]](#page-12-5). A detailed description of these methods can be found in the [Appendix E.](#page-16-1)

Implementation Details. We construct a CLIP-style architecture using the aforementioned image and text encoders. The image encoder is initialized with ImageNet-pretrained weights [\[8\]](#page-10-11), while the text encoder is initialized with the official pretrained weights provided by the corresponding language model. After feature extraction, the outputs from both branches are passed through separate linear projection layers to obtain the final embeddings. During buffer generation, distillation, and evaluation training, the encoders are frozen and only the projection layers are optimized. We collect 20 expert trajectories, each consisting of 10 training epochs. The hyperparameter settings follow those used in LoRS [\[52\]](#page-12-5) and can be found in [Table 5](#page-17-0) and [Table 6](#page-17-1) in [Appendix F.](#page-17-2)

## 4.2 Main Results

The results on Flickr-30K [\[35\]](#page-11-11) and MS-COCO [\[27\]](#page-11-13) are presented in [Table 1](#page-7-0) and [Table 2,](#page-7-1) respectively. Our method consistently outperforms all baseline methods, across all distillation budgets and evaluation metrics. Notably, on Flickr-30k, under the extremely low-data regime of 100 training pairs  $(0.3\%)$ , our method achieves an IR@1 of 11.5%, substantially surpassing LoRS (8.3%) and MTT-VL (4.7%). Similarly, our TR@10 reaches 55.5%, a considerable gain over the best baseline LoRS (49.2%). These trends hold consistently across all pair settings. Under the 500-pair scenario (1.7%), our method improves the IR@10 from 41.6% (LoRS) to 55.9% and TR@10 from 53.7% to 66.7%, reflecting a relative gain of over 30%. On MS-COCO, a dataset known for higher complexity and variability, our method continues to exhibit superior performance. Under the 100-pair setting (0.8‰), our approach achieves IR  $@10 = 22.3\%$  and TR  $@10 = 28.0\%$ , substantially outperforming LoRS, which attains 12.2% and 19.6%, respectively. At a higher budget of 500 training pairs (4.4‰), our method maintains its advantage, achieving the highest IR@10 (30.6%) and TR@10 (32.9%) among all evaluated methods. The observed improvements are both substantial and consistent, demonstrating the effectiveness of our distillation framework in condensing multimodal datasets. Moreover, our

<span id="page-6-1"></span><sup>&</sup>lt;sup>4</sup>To offset the additional memory overhead introduced by soft labels.

<span id="page-7-0"></span>Table 1: Results on Flickr-30k [\[35\]](#page-11-11). Both distillation and validation are performed using NFNet+BERT. The model trained on full dataset performs: IR@1=23.16, IR@5=53.98, IR@10=66.62; TR@1=33.8, TR@5=65.7, TR@10=76.9. For fairness, both LoRS [\[52\]](#page-12-5) and ours synthesize one fewer pair under each distillation budget (e.g., 99 pairs for a budget of  $100)^4$  $100)^4$ .

| Pairs | Ratio   | Metric | <b>Dataset Distillation</b><br><b>Coreset Selection</b> |      |      |                                        |                  |                  |                              |                   |
|-------|---------|--------|---------------------------------------------------------|------|------|----------------------------------------|------------------|------------------|------------------------------|-------------------|
|       |         |        |                                                         |      |      | Rand Herd [50] K-Cent [16] Forget [45] | MTT-VL $[51]$    | TESLA-VL [52]    | LoRS $[52]$                  | Ours              |
|       |         | IR@1   | 1.0                                                     | 0.7  | 0.7  | 0.7                                    | $4.7_{\pm 0.2}$  | $0.5 + 0.2$      | $8.3_{\pm 0.2}$              | $11.5_{\pm 0.4}$  |
|       |         | IR@5   | 4.0                                                     | 2.8  | 3.1  | 2.4                                    | $15.7_{\pm 0.5}$ | $2.3_{\pm 0.2}$  | $24.1_{+0.2}$                | $32.0 + 0.7$      |
|       |         | IR@10  | 6.5                                                     | 5.3  | 6.1  | 5.6                                    | $24.6_{\pm 1.0}$ | $4.7_{\pm 0.4}$  | $35.1_{\pm 0.3}$             | 44.5 $\pm$ 0.6    |
| 100   | $0.3\%$ | TR@1   | 1.3                                                     | 1.1  | 0.6  | 1.2                                    | $9.9_{+0.3}$     | $5.5_{\pm 0.5}$  | $11.8 + 0.2$                 | $16.2_{\pm 0.8}$  |
|       |         | TR@5   | 5.9                                                     | 4.7  | 5.0  | 4.2                                    | $28.3_{\pm 0.5}$ | $19.5_{\pm 0.9}$ | $35.8_{\pm 0.6}$             | 41.7 $_{\pm 0.9}$ |
|       |         | TR@10  | 10.1                                                    | 7.9  | 7.6  | 9.7                                    | $39.1_{\pm 0.7}$ | $28.9_{\pm 1.0}$ | $49.2_{\pm 0.5}$             | $55.5_{\pm 0.4}$  |
|       |         | IR@1   | 1.1                                                     | 1.5  | 1.5  | 1.2                                    | $4.6_{\pm 0.9}$  | $0.2_{\pm 0.1}$  | $8.6_{\pm 0.3}$              | $12.7_{\pm 0.8}$  |
|       |         | IR@5   | 4.8                                                     | 5.5  | 5.4  | 3.1                                    | $16.0 + 1.6$     | $1.3_{\pm 0.2}$  | $25.3 + 0.2$                 | $34.7_{\pm 0.6}$  |
| 200   | 0.7%    | IR@10  | 9.2                                                     | 9.3  | 9.9  | 8.4                                    | $25.5_{\pm 2.6}$ | $2.5_{\pm 0.2}$  | $36.6_{\pm 0.3}$             | 47.6 $\pm$ 0.5    |
|       |         | TR@1   | 2.1                                                     | 2.3  | 2.2  | 1.5                                    | $10.2_{+0.8}$    | $2.8 + 0.5$      | $14.5 + 0.5$                 | $18.6 + 0.7$      |
|       |         | TR@5   | 8.7                                                     | 8.4  | 8.2  | 8.4                                    | $28.7_{\pm 1.0}$ | $10.4_{\pm 1.5}$ | $38.7_{\pm 0.5}$             | $46.0_{\pm 0.8}$  |
|       |         | TR@10  | 13.2                                                    | 14.4 | 13.5 | 10.2                                   | $41.9 + 1.9$     | $17.4_{\pm 1.6}$ | $53.4_{+0.5}$                | $60.0_{\pm 0.6}$  |
|       |         | IR@1   | 2.4                                                     | 3.0  | 3.5  | 1.8                                    | $6.6_{\pm 0.3}$  | $1.1_{\pm 0.2}$  | $10.0_{+0.2}$                | $17.0_{\pm 0.6}$  |
|       |         | IR@5   | 10.5                                                    | 10.0 | 10.4 | 9.0                                    | $20.2_{+1.2}$    | $7.3_{\pm 0.4}$  | $28.9 + 0.7$                 | $42.5 + 0.5$      |
| 500   | $1.7\%$ | IR@10  | 17.4                                                    | 17.0 | 17.3 | 15.9                                   | $30.0_{+2.1}$    | $12.6_{\pm 0.5}$ | $41.6 + 0.6$                 | $55.9_{\pm 0.6}$  |
|       |         | TR@1   | 5.2                                                     | 5.1  | 4.9  | 3.6                                    | $13.3 + 0.6$     | $5.1_{\pm 0.2}$  | $15.5 + 0.7$                 | $22.5_{\pm 0.4}$  |
|       |         | TR@5   | 18.3                                                    | 16.4 | 16.4 | 12.3                                   | $32.8_{\pm 1.8}$ | $15.3_{\pm 0.5}$ | $39.8{\scriptstyle \pm 0.4}$ | $53.2_{\pm 0.3}$  |
|       |         | TR@10  | 25.7                                                    | 24.3 | 23.3 | 19.3                                   | $46.8 + 0.8$     | $23.8_{\pm 0.3}$ | 53.7 $\pm$ 0.3               | $66.7_{\pm 0.3}$  |

method also demonstrates strong generalizability to other multimodal settings, such as audio-text benchmark. See [Appendix G](#page-17-3) for details.

<span id="page-7-1"></span>Table 2: Results on MS-COCO [\[27\]](#page-11-13). Both distillation and validation are performed using NFNet+BERT. The model trained on full dataset performs: IR@1=14.6, IR@5=38.9, IR@10=53.2; TR@1=20.6, TR@5=46.8, TR@10=61.3. For fairness, both LoRS [\[52\]](#page-12-5) and ours synthesize one fewer pair under each distillation budget (e.g., 99 pairs for a budget of 100).

| Pairs | Ratio  | Metric | <b>Coreset Selection</b> |           |             |             | <b>Dataset Distillation</b> |                  |                  |                  |
|-------|--------|--------|--------------------------|-----------|-------------|-------------|-----------------------------|------------------|------------------|------------------|
|       |        |        | Rand                     | Herd [50] | K-Cent [16] | Forget [45] | MTT-VL [51]                 | TESLA-VL [52]    | LoRS [52]        | Ours             |
| 100   | $0.8%$ | IR@1   | 0.3                      | 0.5       | 0.4         | 0.3         | $1.3_{\pm 0.1}$             | $0.3_{\pm 0.2}$  | $1.8_{\pm 0.1}$  | $4.1_{\pm 0.3}$  |
|       |        | IR@5   | 1.3                      | 1.4       | 1.4         | 1.5         | $5.4_{\pm 0.3}$             | $1.0_{\pm 0.4}$  | $7.1_{\pm 0.2}$  | $13.9_{\pm 0.8}$ |
|       |        | IR@10  | 2.7                      | 3.5       | 2.5         | 2.5         | $9.5_{\pm 0.5}$             | $1.8_{\pm 0.5}$  | $12.2_{\pm 0.2}$ | $22.3_{\pm 0.5}$ |
|       |        | TR@1   | 0.8                      | 0.8       | 1.4         | 0.7         | $2.5_{\pm 0.3}$             | $2.0_{\pm 0.2}$  | $3.3_{\pm 0.2}$  | $5.2_{\pm 0.5}$  |
|       |        | TR@5   | 3.0                      | 2.1       | 3.7         | 2.6         | $10.0_{\pm 0.5}$            | $7.7_{\pm 0.5}$  | $12.2_{\pm 0.3}$ | $17.9_{\pm 0.9}$ |
|       |        | TR@10  | 5.0                      | 4.9       | 5.5         | 4.8         | $15.7_{\pm 0.4}$            | $13.5_{\pm 0.3}$ | $19.6_{\pm 0.3}$ | $28.0_{\pm 0.3}$ |
| 200   | $1.7%$ | IR@1   | 0.6                      | 0.9       | 0.7         | 0.6         | $1.7_{\pm 0.1}$             | $0.1_{\pm 0.1}$  | $2.4_{\pm 0.1}$  | $6.1_{\pm 0.8}$  |
|       |        | IR@5   | 2.3                      | 2.4       | 2.1         | 2.8         | $6.5_{\pm 0.4}$             | $0.2_{\pm 0.1}$  | $9.3_{\pm 0.2}$  | $19.3_{\pm 0.7}$ |
|       |        | IR@10  | 4.4                      | 4.1       | 5.8         | 4.9         | $12.3_{\pm 0.8}$            | $0.5_{\pm 0.1}$  | $15.5_{\pm 0.2}$ | $29.8_{\pm 0.5}$ |
|       |        | TR@1   | 1.0                      | 1.0       | 1.2         | 1.1         | $3.3_{\pm 0.2}$             | $0.7_{\pm 0.2}$  | $4.3_{\pm 0.1}$  | $6.9_{\pm 0.6}$  |
|       |        | TR@5   | 4.0                      | 3.6       | 3.8         | 3.5         | $11.9_{\pm 0.6}$            | $3.1_{\pm 0.5}$  | $14.2_{\pm 0.3}$ | $21.8_{\pm 0.9}$ |
|       |        | TR@10  | 7.2                      | 7.7       | 7.5         | 7.0         | $19.4_{\pm 1.2}$            | $5.3_{\pm 0.8}$  | $22.6_{\pm 0.2}$ | $32.3_{\pm 0.7}$ |
| 500   | $4.4%$ | IR@1   | 1.1                      | 1.7       | 1.1         | 0.8         | $2.5_{\pm 0.5}$             | $0.8_{\pm 0.2}$  | $2.8_{\pm 0.2}$  | $6.2_{\pm 0.1}$  |
|       |        | IR@5   | 5.0                      | 5.3       | 6.3         | 5.8         | $8.9_{\pm 0.7}$             | $3.6_{\pm 0.6}$  | $9.9_{\pm 0.5}$  | $19.9_{\pm 0.3}$ |
|       |        | IR@10  | 8.7                      | 9.9       | 10.5        | 8.2         | $15.8_{\pm 1.5}$            | $6.7_{\pm 0.9}$  | $16.5_{\pm 0.7}$ | $30.6_{\pm 0.1}$ |
|       |        | TR@1   | 1.9                      | 1.9       | 2.5         | 2.1         | $5.0_{\pm 0.4}$             | $1.7_{\pm 0.4}$  | $5.3_{\pm 0.5}$  | $7.0_{\pm 0.2}$  |
|       |        | TR@5   | 7.5                      | 7.8       | 8.7         | 8.2         | $17.2_{\pm 1.3}$            | $5.9_{\pm 0.8}$  | $18.3_{\pm 1.5}$ | $22.0_{\pm 0.3}$ |
|       |        | TR@10  | 12.5                     | 13.7      | 14.3        | 13.0        | $26.0_{\pm 1.9}$            | $10.2_{\pm 1.0}$ | $27.9_{\pm 1.4}$ | $32.9_{\pm 0.6}$ |

### 4.3 Ablation Study

Representation Blending & Symmetric Matching. We conduct an ablation study on the Flickr-30K dataset using NFNet+BERT to evaluate the individual and combined contributions of the proposed components: Representation Blending (RB) and Symmetric Projection Trajectory Matching (SM). As shown in [Figure 5,](#page-8-1) removing either module leads to consistent performance degradation across all retrieval metrics (IR@1/5/10 and TR@1/5/10) and distillation budgets (100, 200, 500 pairs). RB contributes by mitigating intra-modal collapse; as illustrated in [Figure 3,](#page-3-2) it effectively reduces intra-modal similarity and enhances representational diversity. SM further balances the learning dynamics across modalities and improves cross-modal alignment, as evidenced in [Figure 4.](#page-5-0) When combined, RB and SM achieve the best overall performance, highlighting their complementary roles in enhancing intra-modal diversity and cross-modal alignment.

<span id="page-8-1"></span>Image /page/8/Figure/0 description: The image displays a grid of six bar charts, arranged in two rows and three columns. Each chart plots performance metrics against the 'Num of Pairs' on the x-axis, with values ranging from 100 to 500. The y-axis represents performance metrics, labeled as IR@1 (%), IR@5 (%), IR@10 (%), and TR@1 (%), TR@5 (%), TR@10 (%). Each bar within the charts represents a different method: RB with a red cross (RB X SM), RB with a green checkmark (RB SM), and SM with a green checkmark (SM). The top row charts show IR@1, IR@5, and IR@10, while the bottom row charts show TR@1, TR@5, and TR@10. The bars generally increase in height as the 'Num of Pairs' increases, indicating improved performance with more data. The legend at the top clarifies the color coding for each method.

Figure 5: Ablation study of Representation Blending (RB) and Symmetric Projection Trajectory Matching (SM) on Flickr-30K with NFNet+BERT.

<span id="page-8-2"></span>Table 3: Cross-architecture generalization. The distilled data are synthesized using NFNet+BERT and evaluated across different architectures. Evaluations are conducted on Flickr-30K under the 500-pair setting. For fairness, both LoRS [\[52\]](#page-12-5) and ours synthesize one fewer pair, e.g., 499 pairs.

| EVALUATE MODEL | METHODS       | IR @ 1                | IR @ 5        | IR @ 10       | TR @ 1       | TR @ 5        | TR @ 10       |
|----------------|---------------|-----------------------|---------------|---------------|--------------|---------------|---------------|
| ResNet+BERT    | TESLA-VL [52] | $3.0_{±0.2}$          | $10.8_{±0.5}$ | $17.0_{±0.8}$ | $6.0_{±0.9}$ | $18.8_{±0.7}$ | $27.7_{±1.2}$ |
|                | LoRS [52]     | $3.3_{±0.2}$          | $12.7_{±0.3}$ | $20.4_{±0.2}$ | $6.8_{±0.2}$ | $19.6_{±1.3}$ | $31.1_{±0.3}$ |
|                | OURS          | $\mathbf{4.2}_{±0.2}$ | $14.1_{±0.2}$ | $23.6_{±0.6}$ | $8.4_{±0.2}$ | $23.1_{±0.8}$ | $35.0_{±1.3}$ |
| RegNet+BERT    | TESLA-VL [52] | $3.2_{±0.8}$          | $11.1_{±1.8}$ | $17.5_{±1.3}$ | $5.8_{±0.1}$ | $18.6_{±0.6}$ | $28.1_{±1.0}$ |
|                | LoRS [52]     | $3.5_{±0.1}$          | $12.6_{±0.3}$ | $21.1_{±0.4}$ | $6.8_{±0.3}$ | $20.8_{±0.3}$ | $30.2_{±0.3}$ |
|                | OURS          | $3.9_{±0.2}$          | $13.9_{±0.3}$ | $24.0_{±0.6}$ | $7.9_{±0.3}$ | $24.2_{±0.3}$ | $36.2_{±1.1}$ |

Cross-Architecture Generalization. We further validate the generalization capability of RepBlend across diverse architectures. Following the protocol of LoRS [\[52\]](#page-12-5), we keep the text encoder fixed and evaluate the dataset distilled with NFNet+BERT using alternative image encoders, including ResNet-50 and RegNet. As shown in [Table 3,](#page-8-2) RepBlend consistently maintains strong performance across different encoder architectures. Moreover, we extend the evaluation to a broader set of architecture combinations, such as ResNet-50+BERT, ViT+BERT, RegNet+BERT, and NFNet+DistilBERT, as illustrated in [Figure 6](#page-19-0) and [Figure 7](#page-19-1) in [Appendix H.](#page-18-0) Across all architectures, datasets, and distillation budgets, RepBlend consistently outperforms the sota baseline, demonstrating its robustness and architectural adaptability.

<span id="page-8-0"></span>**4.4 Computational Efficiency** Table 4: Study of computational efficiency.

In the proposed method, the training trajectories of image and text projection layers are used for matching optimization. Although we introduce an additional image projection, it incurs negligible computational overhead. In fact, as shown in [Table 4,](#page-8-3) our method achieves significantly better computational efficiency compared to prior work. Specifically, the time required to construct expert trajectories is reduced from 70 minutes to 40 minutes

<span id="page-8-3"></span>

| Methods                              | LoRS [52]     | Ours          |
|--------------------------------------|---------------|---------------|
| (IR@1, TR@1) (%)                     | (8.3, 11.8)   | (11.5, 16.2)  |
| Buffer                               |               |               |
| Speed (min/traj)<br>Memory (GB/traj) | 70<br>1.63    | 40<br>0.73    |
| Distillation                         |               |               |
| Speed (s/iter)<br>Peak GPU VRAM (GB) | 11.5<br>21.78 | 1.71<br>10.17 |

per trajectory  $(1.75 \times \text{speedup})$ , and the corresponding memory footprint decreases from 1.63 GB to  $0.73$  GB ( $2.23 \times$  reduction). During the distillation phase, our method accelerates training iterations from 11.5 seconds to 1.71 seconds per iteration, yielding a  $6.7\times$  speedup. Moreover, it lowers the peak GPU memory usage from 21.78 GB to 10.17 GB (2.14× reduction). These results demonstrate that our projection-based design not only enables more effective multimodal distillation, but also leads to substantially improved computational efficiency.

# 5 Conclusion

In this work, we investigate the underexplored challenge of modality collapse in multimodal dataset distillation (MDD), where intra-modal similarity is excessively amplified and inter-modal alignment is degraded. Through theoretical analysis and empirical evidence, we attribute this phenomenon

to the inherent over-compression behavior of dataset distillation and its interplay with cross-modal contrastive supervision. To mitigate these issues, we propose RepBlend, a novel MDD framework incorporating two key components: Representation Blending for enhancing intra-modal diversity and Symmetric Projection Trajectory Matching for achieving balanced and effective supervision across modalities. Extensive experiments on Flickr-30K and MS-COCO confirm the superiority of RepBlend in both retrieval performance and distillation efficiency.

Limitations and Future work. Despite the promising results of RepBlend, current MDD frameworks, including ours, remain limited to pair-level modeling, which restricts fine-grained alignment between text tokens and visual objects. Additionally, insufficient cross-instance interaction hampers representation expressiveness and limits further gains in compression. In the future, we will explore instance-aware, relation-enhanced strategies to overcome these challenges.

# References

- <span id="page-10-2"></span>[1] Samira Abnar, Mostafa Dehghani, Behnam Neyshabur, and Hanie Sedghi. Exploring the limits of large scale pre-training. In *International Conference on Learning Representations*, 2022.
- <span id="page-10-1"></span>[2] Rishi Bommasani, Drew A Hudson, Ehsan Adeli, Russ Altman, Simran Arora, Sydney von Arx, Michael S Bernstein, Jeannette Bohg, Antoine Bosselut, Emma Brunskill, et al. On the opportunities and risks of foundation models. *arXiv preprint arXiv:2108.07258*, 2021.
- <span id="page-10-12"></span>[3] Andy Brock, Soham De, Samuel L Smith, and Karen Simonyan. High-performance large-scale image recognition without normalization. In *International Conference on Machine Learning*, pages 1059–1071. PMLR, 2021.
- <span id="page-10-3"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022.
- <span id="page-10-5"></span>[5] Keqin Chen, Zhao Zhang, Weili Zeng, Richong Zhang, Feng Zhu, and Rui Zhao. Shikra: Unleashing multimodal llm's referential dialogue magic. *arXiv preprint arXiv:2306.15195*, 2023.
- <span id="page-10-0"></span>[6] Aakanksha Chowdhery, Sharan Narang, Jacob Devlin, Maarten Bosma, Gaurav Mishra, Adam Roberts, Paul Barham, Hyung Won Chung, Charles Sutton, Sebastian Gehrmann, et al. Palm: Scaling language modeling with pathways. *Journal of Machine Learning Research*, 24(240):1– 113, 2023.
- <span id="page-10-8"></span>[7] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, 2023.
- <span id="page-10-11"></span>[8] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009.
- <span id="page-10-10"></span>[9] Wenxiao Deng, Wenbin Li, Tianyu Ding, Lei Wang, Hongguang Zhang, Kuihua Huang, Jing Huo, and Yang Gao. Exploiting inter-sample and inter-feature relations in dataset distillation. In *CVPR*, 2024.
- <span id="page-10-9"></span>[10] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint arXiv:1810.04805*, 2018.
- <span id="page-10-4"></span>[11] Guodong Ding, Rongyu Chen, and Angela Yao. Condensing action segmentation datasets via generative network inversion. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2025.
- <span id="page-10-13"></span>[12] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. In *ICLR*, 2021.
- <span id="page-10-15"></span>[13] Jiawei Du, Yidi Jiang, Vincent Y. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *CVPR*, 2023.
- <span id="page-10-6"></span>[14] Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. In *NeurIPS*, 2023.
- <span id="page-10-7"></span>[15] Jiawei Du, Xin Zhang, Juncheng Hu, Wenxin Huang, and Joey Tianyi Zhou. Diversity-driven synthesis: Enhancing dataset distillation through directed weight adjustment. In *Adv. Neural Inf. Process. Syst. (NeurIPS)*, 2024.
- <span id="page-10-14"></span>[16] Reza Zanjirani Farahani and Masoud Hekmatfar. *Facility location: concepts, models, algorithms and case studies*. Springer Science & Business Media, 2009.
- <span id="page-10-16"></span>[17] Jort F Gemmeke, Daniel PW Ellis, Dylan Freedman, Aren Jansen, Wade Lawrence, R Channing Moore, Manoj Plakal, and Marvin Ritter. Audio set: An ontology and human-labeled dataset for audio events. In *2017 IEEE international conference on acoustics, speech and signal processing (ICASSP)*, pages 776–780. IEEE, 2017.

- <span id="page-11-8"></span>[18] Ziyao Guo, Kai Wang, George Cazenavette, HUI LI, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *ICLR*, 2024.
- <span id="page-11-14"></span>[19] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, 2016.
- <span id="page-11-0"></span>[20] Jordan Hoffmann, Sebastian Borgeaud, Arthur Mensch, Elena Buchatskaya, Trevor Cai, Eliza Rutherford, Diego de Las Casas, Lisa Anne Hendricks, Johannes Welbl, Aidan Clark, et al. Training compute-optimal large language models. *arXiv preprint arXiv:2203.15556*, 2022.
- <span id="page-11-17"></span>[21] Andrew G Howard. Mobilenets: Efficient convolutional neural networks for mobile vision applications. *arXiv preprint arXiv:1704.04861*, 2017.
- <span id="page-11-1"></span>[22] Feiyang Kang, Hoang Anh Just, Yifan Sun, Himanshu Jahagirdar, Yuanzhi Zhang, Rongxing Du, Anit Kumar Sahu, and Ruoxi Jia. Get more for less: Principled data selection for warming up fine-tuning in LLMs. In *The Twelfth International Conference on Learning Representations*, 2024.
- <span id="page-11-15"></span>[23] Chris Dongjoo Kim, Byeongchang Kim, Hyunmin Lee, and Gunhee Kim. Audiocaps: Generating captions for audios in the wild. In *NAACL-HLT*, 2019.
- <span id="page-11-16"></span>[24] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *ICML*, 2022.
- <span id="page-11-9"></span>[25] Yongmin Lee and Hye Won Chung. Selmatch: Effectively scaling up dataset distillation via selection-based initialization and partial updates by trajectory matching. In *ICML*, 2024.
- <span id="page-11-12"></span>[26] Weixin Liang, Yuhui Zhang, Yongchan Kwon, Serena Yeung, and James Zou. Mind the gap: Understanding the modality gap in multi-modal contrastive representation learning. In Alice H. Oh, Alekh Agarwal, Danielle Belgrave, and Kyunghyun Cho, editors, *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-11-13"></span>[27] Tsung-Yi Lin, Michael Maire, Serge Belongie, James Hays, Pietro Perona, Deva Ramanan, Piotr Dollár, and C Lawrence Zitnick. Microsoft coco: Common objects in context. In *Computer Vision–ECCV 2014: 13th European Conference, Zurich, Switzerland, September 6-12, 2014, Proceedings, Part V 13*, pages 740–755. Springer, 2014.
- <span id="page-11-5"></span>[28] Haotian Liu, Chunyuan Li, Qingyang Wu, and Yong Jae Lee. Visual instruction tuning. *Advances in neural information processing systems*, 36:34892–34916, 2023.
- <span id="page-11-4"></span>[29] Yang Liu, Deyu Bo, and Chuan Shi. Graph distillation with eigenbasis matching. In *Forty-first International Conference on Machine Learning*, 2024.
- <span id="page-11-2"></span>[30] Huimin LU, Masaru Isonuma, Junichiro Mori, and Ichiro Sakata. Unidetox: Universal detoxification of large language models via dataset distillation. In *The Thirteenth International Conference on Learning Representations*, 2025.
- <span id="page-11-7"></span>[31] Yao Lu, Jukka Corander, and Zhirong Yang. Doubly stochastic neighbor embedding on spheres. *Pattern Recognition Letters*, 125:581–587, 2019.
- <span id="page-11-3"></span>[32] Aru Maekawa, Satoshi Kosugi, Kotaro Funakoshi, and Manabu Okumura. Dilm: Distilling dataset into language model for text-level dataset distillation. *Journal of Natural Language Processing*, 32(1):252–282, 2025.
- <span id="page-11-10"></span>[33] Aaron van den Oord, Yazhe Li, and Oriol Vinyals. Representation learning with contrastive predictive coding. *arXiv preprint arXiv:1807.03748*, 2018.
- <span id="page-11-6"></span>[34] Zhiliang Peng, Wenhui Wang, Li Dong, Yaru Hao, Shaohan Huang, Shuming Ma, Qixiang Ye, and Furu Wei. Grounding multimodal large language models to the world. In *The Twelfth International Conference on Learning Representations*, 2024.
- <span id="page-11-11"></span>[35] Bryan A Plummer, Liwei Wang, Chris M Cervantes, Juan C Caicedo, Julia Hockenmaier, and Svetlana Lazebnik. Flickr30k entities: Collecting region-to-phrase correspondences for richer image-to-sentence models. In *Proceedings of the IEEE international conference on computer vision*, pages 2641–2649, 2015.

- <span id="page-12-3"></span>[36] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, Gretchen Krueger, and Ilya Sutskever. Learning transferable visual models from natural language supervision. In *Proceedings of the 38th International Conference on Machine Learning*, pages 8748–8763, 2021.
- <span id="page-12-10"></span>[37] Ilija Radosavovic, Raj Prateek Kosaraju, Ross Girshick, Kaiming He, and Piotr Dollár. Designing network design spaces. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 10428–10436, 2020.
- <span id="page-12-16"></span>[38] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z. Liu, Yuri A. Lawryshyn, and Konstantinos N. Plataniotis. DataDAM: Efficient dataset distillation with attention matching. In *ICCV*, 2023.
- <span id="page-12-11"></span>[39] Victor Sanh, Lysandre Debut, Julien Chaumond, and Thomas Wolf. Distilbert, a distilled version of bert: smaller, faster, cheaper and lighter. *arXiv preprint arXiv:1910.01108*, 2019.
- <span id="page-12-12"></span>[40] Florian Schmid, Khaled Koutini, and Gerhard Widmer. Efficient large-scale audio tagging via transformer-to-cnn knowledge distillation. In *ICASSP 2023-2023 IEEE international Conference on acoustics, Speech and signal processing (ICASSP)*, pages 1–5. IEEE, 2023.
- <span id="page-12-8"></span>[41] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. In *CVPR*, 2024.
- <span id="page-12-9"></span>[42] Zhiqiang Shen, Ammar Sherif, Zeyuan Yin, and Shitong Shao. Delt: A simple diversity-driven earlylate training for dataset distillation. In *CVPR*, 2025.
- <span id="page-12-15"></span>[43] Seungjae Shin, Heesun Bae, Donghyeok Shin, Weonyoung Joo, and Il-Chul Moon. Losscurvature matching for dataset selection and condensation. In *AISTAS*, 2023.
- <span id="page-12-17"></span>[44] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *CVPR*, 2024.
- <span id="page-12-14"></span>[45] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018.
- <span id="page-12-6"></span>[46] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022.
- <span id="page-12-7"></span>[47] Shaobo Wang, Yicun Yang, Zhiyuan Liu, Chenghao Sun, Xuming Hu, Conghui He, and Linfeng Zhang. Dataset distillation with neural characteristic function: A minmax perspective. In *CVPR*, 2025.
- <span id="page-12-0"></span>[48] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-2"></span>[49] Ziyu Wang, Yue Xu, Cewu Lu, and Yong-Lu Li. Dancing with still images: video distillation via static-dynamic disentanglement. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 6296–6304, 2024.
- <span id="page-12-13"></span>[50] Max Welling. Herding dynamical weights to learn. In *Proceedings of the 26th Annual International Conference on Machine Learning*, pages 1121–1128, 2009.
- <span id="page-12-4"></span>[51] Xindi Wu, Byron Zhang, Zhiwei Deng, and Olga Russakovsky. Vision-language dataset distillation. In *TMLR*, 2024.
- <span id="page-12-5"></span>[52] Yue Xu, Zhilin Lin, Yusong Qiu, Cewu Lu, and Yong-Lu Li. Low-rank similarity mining for multimodal dataset distillation. In *ICML*, 2024.
- <span id="page-12-18"></span>[53] Zeyuan Yin and Zhiqiang Shen. Dataset distillation in large data era. 2023.
- <span id="page-12-1"></span>[54] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *NeurIPS*, 2024.

- <span id="page-13-0"></span>[55] Yuchen Zhang, Tianle Zhang, Kai Wang, Ziyao Guo, Yuxuan Liang, Xavier Bresson, Wei Jin, and Yang You. Navigating complexity: Toward lossless graph condensation via expanding window matching. In *Forty-first International Conference on Machine Learning*, 2024.
- <span id="page-13-3"></span>[56] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021.
- <span id="page-13-1"></span>[57] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023.
- <span id="page-13-2"></span>[58] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021.

## A More Related Works

Dataset distillation (DD), first proposed by Wang et al. [\[48\]](#page-12-0), aims to improve training efficiency by condensing information from large-scale datasets into a small set of synthetic samples. Building on this foundation, recent advancements have introduced a wide range of techniques for effectively and efficiently compressing representative knowledge into compact datasets. Depending on the underlying distillation objective, existing DD methods can be broadly categorized into gradient matching [\[58,](#page-13-2) [56,](#page-13-3) [24,](#page-11-16) [43\]](#page-12-15), trajectory matching [\[4,](#page-10-3) [7,](#page-10-8) [13,](#page-10-15) [14\]](#page-10-6), and distribution matching [\[46,](#page-12-6) [57,](#page-13-1) [38,](#page-12-16) [44,](#page-12-17) [9,](#page-10-10) [54,](#page-12-1) [53\]](#page-12-18). Among these, trajectory matching approaches demonstrate competitive performance without relying on additional label augmentation, making them particularly effective and efficient for practical distillation tasks.

While early efforts have predominantly focused on image data, recent works have extended DD to other domains such as text  $[30, 32]$  $[30, 32]$  $[30, 32]$ , video  $[11, 49]$  $[11, 49]$  $[11, 49]$ , and graph data  $[29, 55]$  $[29, 55]$  $[29, 55]$ . For example, DiLM [\[32\]](#page-11-3) leverages a generative language model to produce textual synthetic data, enabling model-agnostic distillation with strong generalization. Wang et al. [\[49\]](#page-12-2) address the underexplored challenge of temporal compression in videos by disentangling spatial and temporal information. In the graph domain, GDEM [\[29\]](#page-11-4) aligns the eigenbasis and node features of real and synthetic graphs, achieving efficient and architecture-agnostic distillation without relying on GNN-specific supervision. These promising achievements naturally motivate exploration into multimodal scenarios. MTT-VL [\[51\]](#page-12-4) is the first attempt in this direction, adapting trajectory matching for image-text datasets and demonstrating the feasibility of distilling multimodal information. Building upon this, LoRS [\[52\]](#page-12-5) further investigates the unique challenge in multimodal dataset distillation (MDD), i.e., high representational variance, and proposes to construct a similarity matrix to mine associations between all matched and mismatched pairs more effectively. Despite these advances, existing methods remain focused on data structures, overlooking the fundamental impact of contrastive objectives in multimodal optimization, which can lead to modality collapse. In this paper, we propose an effective and efficient MDD framework that explicitly addresses this issue.

<span id="page-14-0"></span>

## B Derivation of [Equation 3](#page-3-1)

As defined in [Equation 2,](#page-3-3)

$$
\mathcal{L}_{\text{wBCE}}^{\mathcal{B}} = \sum_{i,j}^{|\mathcal{B}|} w_{ij} \cdot \ell\left(\tilde{\boldsymbol{y}}_{ij}, \sigma\left(\hat{\boldsymbol{y}}_{ij}/\gamma\right)\right), \quad w_{ij} = \frac{\mathbb{I}[\tilde{\boldsymbol{y}}_{ij} > \beta]}{\left|\left\{\left(i,j\right): \tilde{\boldsymbol{y}}_{ij} > \beta\right\}\right|} + \frac{\mathbb{I}[\tilde{\boldsymbol{y}}_{ij} \leq \beta]}{\left|\left\{\left(i,j\right): \tilde{\boldsymbol{y}}_{ij} \leq \beta\right\}\right|},
$$

where  $\sigma(x)$  is the sigmoid function and  $\ell(y, p) = -y \log(p) - (1 - y) \log(1 - p)$  is the binary cross-entropy loss. Thus, we have:

$$
\ell(y, \sigma(x)) = -y \log \frac{1}{1 + e^{-x}} - (1 - y) \log \frac{e^{-x}}{1 + e^{-x}}
$$
  
=  $y \log(1 + e^{-x}) + (1 - y)x + (1 - y) \log(1 + e^{-x}) = \log(1 + e^{-x}) + (1 - y)x$ ,

whose derivative with respect to  $x$  is:

$$
\frac{\partial \ell(y, \sigma(x))}{\partial x} = \frac{-e^{-x}}{1 + e^{-x}} + (1 - y) = \sigma(x) - y.
$$

Given  $\hat{y}_{ij} = \tilde{x}_i'^\top \tilde{\tau}_j'$ , the overall gradient of wBCE is:

$$
\frac{\partial \mathcal{L}}{\partial \tilde{\boldsymbol{x}}'_n} = \sum_{j=1}^{|\mathcal{B}|} w_{nj} \frac{\partial}{\partial \tilde{\boldsymbol{x}}'_n} \ell\left(\tilde{\boldsymbol{y}}_{nj}, \sigma(\tilde{\boldsymbol{x}}'^\top_n \tilde{\boldsymbol{\tau}}'_j / \gamma)\right) = \sum_{j=1}^{|\mathcal{B}|} \frac{w_{nj}}{\gamma} (\sigma(\hat{\boldsymbol{y}}_{nj} / \gamma) - \tilde{\boldsymbol{y}}_{nj}) \tilde{\boldsymbol{\tau}}'_j.
$$

Similarly,

$$
\frac{\partial \mathcal{L}}{\partial \tilde{\boldsymbol{x}}'_m} = \sum_{j=1}^{|\mathcal{B}|} w_{mj} \frac{\partial}{\partial \tilde{\boldsymbol{x}}'_m} \ell\left(\tilde{\boldsymbol{y}}_{mj}, \sigma(\tilde{\boldsymbol{x}}_m'^\top \tilde{\boldsymbol{\tau}}'_j / \gamma)\right) = \sum_{j=1}^{|\mathcal{B}|} \frac{w_{mj}}{\gamma} (\sigma(\hat{\boldsymbol{y}}_{mj} / \gamma) - \tilde{\boldsymbol{y}}_{mj}) \tilde{\boldsymbol{\tau}}'_j.
$$

Thus,

$$
\frac{\partial \mathcal{L}}{\partial \tilde{\boldsymbol{x}}'_n} \frac{\partial \mathcal{L}}{\partial \tilde{\boldsymbol{x}}'_m} = \sum_{i,j=1}^{|\mathcal{B}|} \frac{w_{ni} w_{mj}}{\gamma^2} (\sigma(\hat{\boldsymbol{y}}_{ni}/\gamma) - \tilde{\boldsymbol{y}}_{ni})(\sigma(\hat{\boldsymbol{y}}_{mj}/\gamma) - \tilde{\boldsymbol{y}}_{mj}) \tilde{\boldsymbol{\tau}}'_i{}^{\top} \tilde{\boldsymbol{\tau}}'_j,
$$

which can be rewritten as:

which can be rewritten as:

$$
\frac{\partial \mathcal{L}}{\partial \tilde{\mathbf{x}}'_n} \frac{\partial \mathcal{L}}{\partial \tilde{\mathbf{x}}'_m} = \sum_{i,j 
eq n,m}^{|\mathcal{B}|} \frac{w_{ni}w_{mj}}{\gamma^2} (\sigma(\hat{\mathbf{y}}_{ni}/\gamma) - \tilde{\mathbf{y}}_{ni}) (\sigma(\hat{\mathbf{y}}_{mj}/\gamma) - \tilde{\mathbf{y}}_{mj}) \tilde{\mathbf{\tau}}'_i{}^{\top} \tilde{\mathbf{\tau}}'_j
$$

$$
+ \sum_{i 
eq n,m;j=n}^{|\mathcal{B}|} \frac{w_{ni}w_{mn}}{\gamma^2} (\sigma(\hat{\mathbf{y}}_{ni}/\gamma) - \tilde{\mathbf{y}}_{ni}) (\sigma(\hat{\mathbf{y}}_{mn}/\gamma) - \tilde{\mathbf{y}}_{mn}) \tilde{\mathbf{\tau}}'_i{}^{\top} \tilde{\mathbf{\tau}}'_n
$$

$$
+ \sum_{i 
eq n,m;j=m}^{|\mathcal{B}|} \frac{w_{ni}w_{mm}}{\gamma^2} (\sigma(\hat{\mathbf{y}}_{ni}/\gamma) - \tilde{\mathbf{y}}_{ni}) (\sigma(\hat{\mathbf{y}}_{mm}/\gamma) - \tilde{\mathbf{y}}_{mm}) \tilde{\mathbf{\tau}}'_i{}^{\top} \tilde{\mathbf{\tau}}'_m
$$

$$
+ \sum_{i=n;j 
eq n,m}^{|\mathcal{B}|} \frac{w_{nn}w_{mj}}{\gamma^2} (\sigma(\hat{\mathbf{y}}_{nn}/\gamma) - \tilde{\mathbf{y}}_{nn}) (\sigma(\hat{\mathbf{y}}_{mj}/\gamma) - \tilde{\mathbf{y}}_{mj}) \tilde{\mathbf{\tau}}'_n{}^{\top} \tilde{\mathbf{\tau}}'_j
$$

$$
+ \sum_{i=m;j 
eq n,m}^{|\mathcal{B}|} \frac{w_{nm}w_{mj}}{\gamma^2} (\sigma(\hat{\mathbf{y}}_{nm}/\gamma) - \tilde{\mathbf{y}}_{nm}) (\sigma(\hat{\mathbf{y}}_{mj}/\gamma) - \tilde{\mathbf{y}}_{mj}) \tilde{\mathbf{\tau}}'_m{}^{\top} \tilde{\mathbf{\tau}}'_j
$$

$$
+ \frac{w_{nn}w_{mn}}{\gamma^2} (\sigma(\hat{\mathbf{y}}_{nn}/\gamma) - \tilde{\mathbf{y}}_{nn}) (\sigma(\hat{\mathbf{y}}_{mn}/\gamma) - \tilde{\mathbf{y}}_{mn}) \tilde{\mathbf{\tau}}'_n{}^{\top} \tilde{\mathbf{\tau}}'_n
$$

$$
+ \frac{w_{nn}w_{mm}}{\gamma^2} (\sigma(\hat{\mathbf{y}}_{nn}/\gamma) - \tilde{\mathbf{y}}_{nn}) (\sigma(\hat{\mathbf{y}}_{mm}/\gamma) - \tilde{\mathbf{y}}_{mm}) \tilde{\mathbf{\tau}}'_n{}^{\top} \tilde{\mathbf{\tau}}'_m
$$

$$
+ \frac{w_{nm}w_{mn}}{\gamma^2} (\sigma(\hat{\mathbf{y}}_{nm}/\gamma) - \tilde{\mathbf{y}}_{nm}) (\sigma(\hat{\mathbf{y}}_{mn}/\gamma) - \tilde{\mathbf{y}}_{mn}) \tilde{\mathbf{\tau}}'_m{}^{\top} \tilde{\mathbf{\tau}}'_n
$$

$$
+ \frac{w_{nm}w_{mm}}{\gamma^2} (\sigma(\hat{\mathbf{y}}_{nm}/\gamma) - \tilde{\mathbf{y}}_{nm}) (\sigma(\hat{\mathbf{y}}_{mm}/\gamma) - \tilde{\mathbf{y}}_{mm}) \tilde{\mathbf{\tau}}'_m{}^{\top} \tilde{\mathbf{\tau}}'_m
$$
high-dimensional embedding spaces, both intra-modal and inter-modal negative pairs tend to

In high-dimensional embedding spaces, both intra-modal and inter-modal negative pairs tend to be mutually orthogonal. Specifically, for any negative pair  $(i, j)$ , where  $i \neq j$ ,

$$
\tilde{\boldsymbol{\tau}}_i'^\top \tilde{\boldsymbol{\tau}}_j' \approx 0.
$$

In our case, all pairs beyond  $(i, j) \in \{(m, n), (n, m), (i, i)\}\$ are negatives, thus we have,

$$
\frac{\partial \mathcal{L}}{\partial \tilde{\bm{x}}'_n} \frac{\partial \mathcal{L}}{\partial \tilde{\bm{x}}'_m} \approx \sum_{i=1}^{|\mathcal{B}|} \frac{w_{ni} w_{mi}}{\gamma^2} (\sigma(\hat{\bm{y}}_{ni}/\gamma) - \tilde{\bm{y}}_{ni}) (\sigma(\hat{\bm{y}}_{mi}/\gamma) - \tilde{\bm{y}}_{mi}) \tilde{\bm{\tau}}'_i{}^\top \tilde{\bm{\tau}}'_i \\ + \frac{w_{nn} w_{mm}}{\gamma^2} (\sigma(\hat{\bm{y}}_{nn}/\gamma) - \tilde{\bm{y}}_{nn}) (\sigma(\hat{\bm{y}}_{mm}/\gamma) - \tilde{\bm{y}}_{mm}) \tilde{\bm{\tau}}'^ op_n \tilde{\bm{\tau}}'_m \\ + \frac{w_{nm} w_{mn}}{\gamma^2} (\sigma(\hat{\bm{y}}_{nm}/\gamma) - \tilde{\bm{y}}_{nm}) (\sigma(\hat{\bm{y}}_{mn}/\gamma) - \tilde{\bm{y}}_{mn}) \tilde{\bm{\tau}}'^ op_m \tilde{\bm{\tau}}'_n.
$$

Because  $(n, n)$  and  $(m, m)$  are strictly aligned pairs, we have  $\sigma(\hat{\mathbf{y}}_{nn}/\gamma) \approx \tilde{\mathbf{y}}_{nn} \approx 1$  and  $\sigma(\hat{\bm{y}}_{mm}/\gamma)\approx\tilde{\bm{y}}_{mm}\approx1,$  hence  $\sigma(\hat{\bm{y}}_{nn}/\gamma)-\tilde{\bm{y}}_{nn}$  and  $\sigma(\hat{\bm{y}}_{mm}/\gamma)-\tilde{\bm{y}}_{mm}$  are close to zero. Therefore, we have:

$$
\frac{\partial \mathcal{L}}{\partial \tilde{\bm{x}}'_n} \frac{\partial \mathcal{L}}{\partial \tilde{\bm{x}}'_m} \approx \sum_{i \neq n,m}^{|\mathcal{B}|} \frac{w_{ni} w_{mi}}{\gamma^2} (\sigma(\hat{\bm{y}}_{ni}/\gamma) - \tilde{\bm{y}}_{ni}) (\sigma(\hat{\bm{y}}_{mi}/\gamma) - \tilde{\bm{y}}_{mi}) \tilde{\bm{\tau}}_i^{\prime \top} \tilde{\bm{\tau}}_i^{\prime} \\ + \frac{w_{nm} w_{mn}}{\gamma^2} (\sigma(\hat{\bm{y}}_{nm}/\gamma) - \tilde{\bm{y}}_{nm}) (\sigma(\hat{\bm{y}}_{mn}/\gamma) - \tilde{\bm{y}}_{mn}) \tilde{\bm{\tau}}_m^{\prime \top} \tilde{\bm{\tau}}_n^{\prime}.
$$

The first term captures the aggregated influence of shared negative examples on both  $\tilde{x}'_n$  and  $\tilde{x}'_m$ , which affect them similarly and thus contribute little to their relative update direction. In contrast, the second term reflects their mutual interaction and plays a dominant role in determining their representational divergence or alignment.

<span id="page-16-0"></span>

## C Calculation of Concentration Ratio (CR)

To compute the *concentration ratio* (CR), we use the surface area of a hyperspherical cap on the unit  $(d-1)$ -sphere, where d is the dimensionality of the embedding space. Given a normalized cosine similarity value  $c \in [0, 1]$ , we consider the set of all unit vectors that form this similarity with a fixed reference direction. These vectors define a hyperspherical cap, a region on the surface of the unit hypersphere bounded by a fixed similarity threshold. The surface area ratio of this cap is given by:

$$
A = \mathcal{I}_{1-c^2}\left(\frac{d-1}{2},\frac{1}{2}\right).
$$

Here,  $\mathcal{I}_x(a, b)$  denotes the regularized incomplete Beta function, defined as:

$$
\mathcal{I}_x(a,b) = \frac{\int_0^x t^{a-1} (1-t)^{b-1} dt}{\int_0^1 t^{a-1} (1-t)^{b-1} dt}.
$$

This function describes the cumulative distribution of the Beta distribution and is widely used in geometric probability. In our context, it measures the proportion of the unit hypersphere's surface that lies within a given angular range, equivalently, within a given cosine similarity of a fixed direction. Specifically, when computing hyperspherical cap areas, the variable substitution  $x = 1 - c^2$  arises naturally from the spherical-to-cartesian coordinate transformation.

We then define the concentration ratio as the complement of this surface ratio:

$$
CR=1-A.
$$

This value reflects the proportion of the hypersphere surface that lies outside the similarity-defined cone. A higher CR indicates that the given similarity corresponds to a narrower directional region on the hypersphere, implying stronger feature concentration in the high-dimensional embedding space.

In implementation, we compute this value using the scipy. special. betainc function in Python.

## D Implementation of Representation Blending

Algorithm 2 RepBlend:Representation Blending

**Require:** image and text representation  $\{f_{\text{img}}^{\text{imgE}}(\tilde{x}_b), \tilde{\tau}_b\}_{b=1}^{\mathcal{B}}$  of one batch, Parameter  $\alpha$  for MixUP 1: function REPBLEND( $\{f^{\text{imgE}}(\tilde{x}_b), \tilde{\tau}_b\}_{b=1}^{\mathcal{B}}, \alpha)$ 2:  $\{f^{\text{imgE}}(\tilde{x}_b)^{\text{shuf}}, \tilde{\tau}_b^{\text{shuf}}\}_{b=1}^{\mathcal{B}} \leftarrow \text{shuffle}\big(\{f^{\text{imgE}}(\tilde{x}_b), \tilde{\tau}_b\}_{b=1}^{\mathcal{B}}\big)$ 3: ▷ Shuffle image and text representations in one batch 4: Sample  $\lambda$  from Beta $(\alpha, \alpha)$  for the batch 5: for  $b = 1$  to  $|\mathcal{B}|$  do 6: ▷ Linear interpolation in representation space 7:  $f^{\text{imgE}}(\tilde{\bm{x}}_b) \leftarrow \lambda f^{\text{imgE}}(\tilde{\bm{x}}_b) + (1-\lambda)\overline{f}^{\text{imgE}}(\tilde{\bm{x}}_b)^{\text{shuf}}$ 8:  $\tilde{\boldsymbol{\tau}}_b \leftarrow \lambda \tilde{\boldsymbol{\tau}}_b + (1-\lambda)\tilde{\boldsymbol{\tau}}_b^{\text{shift}}$ 9: end forreturn  $\{f^{\text{imgE}}(\tilde{\bm{x}}_b), \tilde{\bm{\tau}}_b\}_{b=1}^{\mathcal{B}}$ 10: end function

<span id="page-16-1"></span>

## E Comparison Methods

### Coreset Selection Methods.

1) Random (Rand): Randomly selects a subset of samples from the full dataset to form a coreset. While this approach is unbiased, it may fail to capture the most informative or representative instances necessary for efficient training.

2) Herding (Herd) [\[50\]](#page-12-13): Selects samples based on herding dynamics to approximate the mean of the data distribution. It iteratively chooses instances that minimize the discrepancy between the coreset and the full dataset's feature distribution.

<span id="page-17-0"></span>Table 5: Hyperparameter settings for buffer.

|                | Flickr-30K      | MS-COCO         |
|----------------|-----------------|-----------------|
| epoch          | 10              | 10              |
| num_experts    | 20              | 20              |
| batch_size     | 128             | 128             |
| lr_teacher_img | 0.1             | 0.1             |
| lr_teacher_txt | 0.1             | 0.1             |
| image_size     | $224 	imes 224$ | $224 	imes 224$ |

Table 6: Hyperparameter settings for distillation.

<span id="page-17-1"></span>

|                   | Flickr-30K     |                |                | MS-COCO        |                |                |
|-------------------|----------------|----------------|----------------|----------------|----------------|----------------|
|                   | 100 pairs      | 200 pairs      | 500 pairs      | 100 pairs      | 200 pairs      | 500 pairs      |
| syn_steps         | 8              | 8              | 8              | 8              | 8              | 8              |
| expert_epochs     | 1              | 1              | 1              | 1              | 1              | 1              |
| max_start_epoch   | 2              | 2              | 3              | 2              | 2              | 2              |
| iteration         | 2000           | 2000           | 2000           | 2000           | 2000           | 2000           |
| lr_img            | 100            | 1000           | 1000           | 1000           | 1000           | 5000           |
| lr_txt            | 100            | 1000           | 1000           | 1000           | 1000           | 5000           |
| lr_lr             | $1e-2$         | $1e-2$         | $1e-2$         | $1e-2$         | $1e-2$         | $1e-2$         |
| lr_teacher_img    | 0.1            | 0.1            | 0.1            | 0.1            | 0.1            | 0.1            |
| lr_teacher_txt    | 0.1            | 0.1            | 0.1            | 0.1            | 0.1            | 0.1            |
| lr_sim            | 10.0           | 10.0           | 100.0          | 5.0            | 50.0           | 500.0          |
| sim_type          | lowrank        | lowrank        | lowrank        | lowrank        | lowrank        | lowrank        |
| sim_rank          | 10             | 5              | 20             | 10             | 20             | 40             |
| sim_alpha         | 3.0            | 1.0            | 0.01           | 1.0            | 1.0            | 1.0            |
| num_queries       | 99             | 199            | 499            | 99             | 199            | 499            |
| mini_batch_size   | 20             | 20             | 40             | 20             | 20             | 30             |
| loss_type         | WBCE           | WBCE           | WBCE           | WBCE           | WBCE           | WBCE           |
| beta_distribution | $\alpha = 1.0$ | $\alpha = 1.0$ | $\alpha = 1.0$ | $\alpha = 1.0$ | $\alpha = 1.0$ | $\alpha = 1.0$ |

3) K-Center (K-Cent) [\[16\]](#page-10-14): Selects samples that serve as representative centers in the feature space. It aims to maximize coverage by iteratively choosing points that are maximally distant from the already selected ones.

4) Forgetting (Forget) [\[45\]](#page-12-14): Selects samples based on how often they are forgotten during training, i.e., when correct predictions become incorrect. Samples with low forgetting counts are removed first, prioritizing the retention of harder and more informative examples.

### Dataset Distillation Methods.

1) MTT-VL [\[51\]](#page-12-4): The first MDD approach that extends the trajectory matching framework MTT [\[4\]](#page-10-3) to vision-language data, enabling dataset distillation in multimodal settings.

2) TESLA-VL [\[52\]](#page-12-5): An efficient variant of the MTT framework, TESLA [\[7\]](#page-10-8), implemented in LoRS [\[52\]](#page-12-5) as an ablation to evaluate the effectiveness of similarity mining in multimodal distillation.

3) LoRS [\[52\]](#page-12-5): A sota MDD method that distills both image-text pairs and their similarity matrix to enhance multimodal distillation, while leveraging low-rank factorization for improving efficiency.

<span id="page-17-2"></span>

## F Hyperparameter Settings

The hyperparameter settings, summarized in [Table 5](#page-17-0) and [Table 6,](#page-17-1) follow the configurations used in LoRS [\[52\]](#page-12-5) to ensure fair and consistent comparisons.

<span id="page-17-3"></span>

## G Generalization to Audio-Text Datasets

To explore the generalizability of our multimodal dataset distillation approach beyond image-text data, we extend our experiments to the audio-text domain using the AudioCaps [\[23\]](#page-11-15) dataset. AudioCaps is

a widely used dataset for audio-text contrastive learning, derived from AudioSet [\[17\]](#page-10-16). It comprises approximately 44,000 audio clips paired with human-annotated captions that vividly describe the auditory content. The distillation process follows a similar protocol to that used in the imagetext experiments. We employ BERT as the text encoder and EfficientAT (mn20\_as) [\[40\]](#page-12-12) as the audio encoder. EfficientAT is a state-of-the-art audio classification model based on MobileNet [\[21\]](#page-11-17), designed to achieve high representational quality with low computational overhead.

The results presented in [Table 7,](#page-18-1) compare our method against LoRS [\[52\]](#page-12-5) on the AudioCaps dataset for 100, 200, and 500 synthetic pairs. Our approach consistently outperforms LoRS across all metrics and data scales. In 500 pairs settings, our method achieves AR@10 of 46.8 and TR@10 of 54.1, compared to LoRS's 36.7 and 41.3, respectively. Notably, our method achieves around 65% of the full dataset's performance using only 1.13% of the data. Superior results demonstrate that our proposed approach successfully generalizes to audio-text datasets, extending beyond the image-text domain. By achieving significant performance gains over existing baseline, our method establishes a more robust framework for multimodal dataset distillation across diverse modality pairs.

<span id="page-18-1"></span>Table 7: Results on AudioCaps [\[23\]](#page-11-15). Both distillation and validation are performed using pretrained EfficientAT+BERT. The model trained on full dataset performs: AR@1=21.3, AR@5=53.2, AR@10=68.5; TR@1=25.2, TR@5=58.8, TR@10=71.6.

| Method      | Pairs | Ratio    |               | Audio to Text |              |               | Text to Audio |              |
|-------------|-------|----------|---------------|---------------|--------------|---------------|---------------|--------------|
|             |       |          | AR@1          | AR@5          | AR@10        | TR@1          | TR@5          | TR@10        |
| LoRS $[52]$ | 100   | 0.23%    | $2.7 + 0.3$   | $8.6 + 0.3$   | $14.7 + 0.4$ | $5.9 + 0.3$   | $13.0 + 0.4$  | $21.8 + 0.5$ |
|             | 200   | $0.45\%$ | $3.8 \pm 0.2$ | $14.8 + 0.2$  | $21.8 + 0.2$ | $8.0 + 0.2$   | $21.2+0.2$    | $33.1 + 0.2$ |
|             | 500   | $1.13\%$ | $7.1 + 0.1$   | $24.7 + 0.2$  | $36.7 + 0.2$ | $9.2 + 0.2$   | $27.4 + 0.3$  | $41.3 + 0.3$ |
| Ours        | 100   | $0.23\%$ | $4.1 \pm 0.2$ | $14.2 + 0.3$  | $23.7 + 0.4$ | $8.9 \pm 0.1$ | $24.3 + 0.2$  | $34.7 + 0.3$ |
|             | 200   | $0.45\%$ | $6.8 + 0.2$   | $20.6 + 0.2$  | $31.4 + 0.3$ | $9.7 \pm 0.2$ | $29.1 + 0.4$  | $41.2 + 0.4$ |
|             | 500   | $1.13\%$ | $9.7 + 0.1$   | $32.2 + 0.3$  | $46.8 + 0.2$ | $13.8 + 0.3$  | $38.6 + 0.3$  | $54.1 + 0.4$ |

<span id="page-18-0"></span>

## H More Experiments on Various Architectures

We further implement our method using different combinations of image encoders (e.g., ResNet-50 [\[19\]](#page-11-14), ViT [\[12\]](#page-10-13), RegNet [\[37\]](#page-12-10), NFNet [\[3\]](#page-10-12)) and text encoders (e.g., BERT [\[10\]](#page-10-9), DistilBERT [\[39\]](#page-12-11)) to assess the robustness and generality of our framework. The corresponding results are presented in [Figure 6](#page-19-0) and [Figure 7.](#page-19-1) Across all architecture combinations, our method consistently outperforms the baseline LoRS [\[52\]](#page-12-5), demonstrating its adaptability to various vision and language backbones.

## I Compare with SRe2L [\[54\]](#page-12-1) on Classification Tasks

To assess our dataset distillation method for classification task under low IPC<sup>[5](#page-18-2)</sup> settings, we experimented on ImageNet-100, a 100-class subset of ImageNet-1K [\[8\]](#page-10-11). We compared our method against SRe2L [\[54\]](#page-12-1), a leading distillation approach. As our method focuses on multimodal distillation, we assigned uniform text descriptions ("A picture of [ClassName]") to images of the same class. During evaluation, test images and class descriptions were processed by image and text branches to generate embeddings, with classification based on the highest similarity score. For SRe2L [\[54\]](#page-12-1), we followed its original setup, recovering data from ResNet-18 trained for 100 epochs on the full dataset, using 4k recovery iterations and a softmax temperature of 20.

[Table 8](#page-20-0) shows our method significantly outperforms SRe2L on ImageNet-100 at low IPC. Results were obtained by training models from scratch on distilled data and testing on the test set. At  $ipc=1$ , our method achieves 65.8% Top-1 and 89.9% Top-5 accuracy, compared to SRe2L's 2.5% and 9.2%. This significant improvement can be attributed to the inclusion of a text projection head, distilled text embeddings, and the learned similarity matrix. Meanwhile, this added complexity is on par with the soft label augmentation used in SRe2L. Some visualization of distilled data are shown in [Figure 8.](#page-20-1)

<span id="page-18-2"></span><sup>5</sup> IPC denotes image per class

<span id="page-19-0"></span>Image /page/19/Figure/0 description: The image displays two rows of line graphs, each comparing the performance of 'Ours' (green line) and 'LoRS' (blue line) across different numbers of pairs (100, 200, 500) on the x-axis. The top row uses a ResNet-50 image encoder and a BERT text encoder, showing IR@1, IR@5, IR@10, TR@1, TR@5, and TR@10 on the y-axis. The bottom row uses a ViT image encoder and a BERT text encoder, displaying the same metrics. In the top row with ResNet-50, IR@1 ranges from approximately 4.5% to 12%, IR@5 from 15% to 30%, IR@10 from 26% to 45%, TR@1 from 6% to 15%, TR@5 from 25% to 35%, and TR@10 from 28% to 55%. In the bottom row with ViT, IR@1 ranges from approximately 4% to 9%, IR@5 from 14% to 24%, IR@10 from 20% to 35%, TR@1 from 4% to 10%, TR@5 from 15% to 30%, and TR@10 from 20% to 40%. In all graphs, the 'Ours' line generally shows higher performance than the 'LoRS' line, especially at 200 pairs.

Image /page/19/Figure/1 description: The image displays a figure with six line graphs, comparing the performance of 'Ours' (green line with circles) and 'LoRS' (blue line with squares) across different numbers of pairs (100, 200, and 500). The figure is divided into two main sections based on the encoders used: 'Image encoder: RegNet' and 'Text encoder: BERT'. Under the 'Image encoder: RegNet' section, there are three graphs showing IR@1 (%), IR@5 (%), and IR@10 (%). For IR@1, 'Ours' ranges from approximately 7.5% to 9.3%, while 'LoRS' ranges from about 6.5% to 8.6%. For IR@5, 'Ours' ranges from about 23.5% to 27%, and 'LoRS' ranges from approximately 21% to 25.5%. For IR@10, 'Ours' ranges from about 34% to 39.5%, and 'LoRS' ranges from approximately 32% to 37.5%. Under the 'Text encoder: BERT' section, there are three graphs showing TR@1 (%), TR@5 (%), and TR@10 (%). For TR@1, 'Ours' ranges from about 14% to 13.5%, and 'LoRS' ranges from approximately 10.7% to 12.3%. For TR@5, 'Ours' ranges from about 34% to 36.2%, and 'LoRS' ranges from approximately 30% to 32.5%. For TR@10, 'Ours' ranges from about 45.5% to 49.8%, and 'LoRS' ranges from approximately 42.5% to 46.3%. Each graph includes shaded areas representing the confidence intervals for each method.

Image /page/19/Figure/2 description: This figure displays six line graphs comparing the performance of 'Ours' (green line) and 'LoRS' (gray line) across different numbers of pairs (100, 200, and 500) for image retrieval (IR) and text retrieval (TR) metrics. The top row shows IR@1, IR@5, and IR@10, while the bottom row shows TR@1, TR@5, and TR@10. For IR@1, 'Ours' ranges from approximately 11% to 15%, and 'LoRS' ranges from 8% to 11%. For IR@5, 'Ours' ranges from 30% to 41%, and 'LoRS' ranges from 25% to 30%. For IR@10, 'Ours' ranges from 42% to 55%, and 'LoRS' ranges from 37% to 40%. For TR@1, 'Ours' ranges from 16% to 21%, and 'LoRS' ranges from 15% to 14%. For TR@5, 'Ours' ranges from 40% to 48%, and 'LoRS' ranges from 38% to 42%. For TR@10, 'Ours' ranges from 54% to 64%, and 'LoRS' ranges from 52% to 56%. The shaded areas represent confidence intervals. The image encoder used is NFNet and the text encoder is DistilBERT.

Figure 6: Performance on Flickr-30K with different combinations of image and text encoders.

<span id="page-19-1"></span>Image /page/19/Figure/4 description: The image displays two rows of line graphs, each comparing the performance of "Ours" (red line with circles) and "LoRS" (blue line with squares) across different numbers of pairs (100, 200, and 500) on the x-axis. The top row shows results with "Image encoder: ViT" and "Text encoder: BERT". The graphs in the top row are labeled IR@1 (%), IR@5 (%), IR@10 (%), TR@1 (%), and TR@10 (%). The bottom row shows results with "Image encoder: NFNets" and "Text encoder: DistilBERT". The graphs in the bottom row are labeled IR@1 (%), IR@5 (%), IR@10 (%), TR@1 (%), and TR@5 (%). The y-axis scales vary for each graph, showing percentages or values ranging from approximately 1 to 30. The shaded areas around the lines represent confidence intervals.

Figure 7: Performance on MS-COCO with different combinations of image and text encoders.

| ipc | Ours              |                   | SRe2L [54]       |                  |
|-----|-------------------|-------------------|------------------|------------------|
|     | Acc1              | Acc5              | Acc1             | Acc5             |
| 1   | $65.8 	extpm 0.2$ | $89.9 	extpm 0.5$ | $2.5 	extpm 0.2$ | $9.2 	extpm 0.3$ |

<span id="page-20-0"></span>Table 8: Comparison of Our Method with SRe2L [\[54\]](#page-12-1) on ImageNet-100 Dataset

## J Visualization of Distilled Data

Here we provide visualizations of distilled image-text pairs. [Figure 9](#page-21-0) and [Figure 10](#page-22-0) present the original and distilled data on Flickr-30K and MS-COCO. The displayed texts are the closest matching sentences from the training set to the distilled text embeddings, following [\[51\]](#page-12-4).

<span id="page-20-1"></span>Image /page/20/Picture/4 description: The image displays a grid of generated images, comparing two methods labeled "Ours" and "SRe2L". The top section shows five images under "Ours" and five corresponding images under "SRe2L". The "Ours" images are labeled "White shark", "Electric ray", "Bulbul", "Banded gecko", and "Whiptail". The bottom section also shows five images under "Ours" and five under "SRe2L", labeled "Triceratops", "Hognose snake", "Garden spider", "Bee-eater", and "Hornbill". The "Ours" images appear to be clear, distinct photographs of the subjects, while the "SRe2L" images are abstract and distorted representations of the same subjects.

Figure 8: Synthetic data visualization on ImageNet-100 from our approach and SRe2L when IPC=1.

<span id="page-21-0"></span>Image /page/21/Picture/0 description: A young man in a t-shirt and baseball cap is captured mid-air on a skateboard, performing a trick over a concrete ledge. He is wearing dark pants and his arms are outstretched for balance. The background features a clear blue sky, lush green trees, and a traffic light, suggesting an outdoor urban setting.

a boy in a white t-shirt does skateboard tricks

Image /page/21/Picture/2 description: A young man in a t-shirt and jeans is performing a skateboarding trick, jumping over a ledge. He is wearing a black beanie and his arms are outstretched for balance. The background features trees, a street light, and a traffic signal, suggesting an urban park setting.

a skateboarder does a tailslide down the side of a railing over some stairs

Image /page/21/Picture/4 description: Two people are hiking up a snowy mountain with skis on their backs. The sky is clear and blue, and the sun is casting shadows on the snow. The landscape is vast and covered in snow, with some rocky outcrops visible.

the two people carrying skis are walking in the snow

Image /page/21/Picture/6 description: Two people are hiking up a snowy mountain with skis on their backs. The sun is casting shadows on the snow, and there is another person in the distance also hiking up the mountain.

three skiers are standing on a snowy hilltop

Image /page/21/Picture/8 description: A person is lying on their back in the snow, making a snow angel. They are wearing a red jacket, blue hat, and jeans. Their arms and legs are spread out, creating the impression of wings and a body.

the young girl makes a wonderful snow angel on the ground

Image /page/21/Picture/10 description: A person is lying on their back in the snow, making a snow angel. They are wearing a red jacket, blue jeans, and black gloves. Their arms and legs are spread out, creating the impression of wings. The snow is white and fluffy, and the person's hair is visible, falling around their head.

a child wearing a gray winter coat and blue snow boots is unhappy upon discovering their playhouse covered in snow

Image /page/21/Picture/12 description: A couple is cutting a large, rectangular cake decorated with a geometric pattern of white frosting and colorful fruit. The man, wearing a dark suit and tie, and the woman, in a light-colored jacket, are holding a knife together to slice the cake. They are both looking down at the cake. The background appears to be an indoor setting with wooden beams and chairs.

a man and a woman are at a restaurant cutting cake

Image /page/21/Picture/14 description: A bride and groom are cutting a large, rectangular cake decorated with a geometric pattern of white frosting, green leaves, and red and blue berries. The bride wears a cream-colored jacket and the groom wears a dark suit and tie. They are both smiling as they hold a knife together to cut the cake.

a woman wearing a burgundy shirt and a man wearing black prepare a tray of cups of wine

Image /page/21/Picture/16 description: A wide shot of a crowded indoor event with many people mingling. The room is brightly lit with overhead lights and has a modern feel with large pillars. The floor is covered in a bright blue carpet, and there are various booths or displays set up around the perimeter of the room, some with flags or banners. People are dressed casually and appear to be engaged in conversation or looking at the displays.

a group of people are gathered in an office, talking

Image /page/21/Picture/18 description: A wide shot of a crowded indoor event with many people mingling. The floor is covered in a bright blue carpet, and the ceiling is white with many lights. There are several pillars supporting the ceiling. In the background, there are booths with flags and displays. The people are dressed in casual and business casual attire, and they appear to be engaged in conversations and networking.

many people sit and socialize in groups inside a restaurant

Image /page/21/Picture/20 description: A man in a dark suit and striped tie stands at a podium, looking to his left and typing on a laptop. He has a white name tag clipped to his jacket.

a man giving a presentation while wearing a brown striped suit

Image /page/21/Picture/22 description: A man in a dark suit and striped tie stands at a podium, looking down at a laptop. He has a white name tag on his lapel. The background is a light-colored wall.

dark-haired man with beard reading while drinking coffee at a counter

Image /page/21/Picture/24 description: A man in a red t-shirt and grey shorts sits on a metal pole, looking to the left. He is wearing a black baseball cap. The background is blurred, showing buildings and a bright sky.

a guy on a city electric pole watching over

Image /page/21/Picture/26 description: A man wearing a red t-shirt and a black baseball cap is sitting on a metal pole. He is looking to his left and holding onto the pole with both hands. He is wearing grey shorts and black sneakers. The background is blurred and appears to be an urban setting with buildings and signs.

a man in a yellow shirt and helmet hanging from a huge beam on ropes

Image /page/21/Picture/28 description: A young man with dark hair and glasses is playing an acoustic guitar. He is wearing a dark suit jacket over a white shirt. The background is a deep red color, and the lighting is dramatic, casting shadows on the man and the guitar.

a young, asian man is seen playing his guitar

Image /page/21/Picture/30 description: A young man with dark hair and glasses is playing an acoustic guitar. He is wearing a dark suit jacket over a white shirt. The background is a deep red color, and the lighting is dramatic, casting shadows on the man and the guitar.

man playing a brown and white electric guitar

Figure 9: Flickr-30K before and after distillation. (*Left*) The original image-text pairs before the distillation. (*Right*) The image-text pairs after distillation.

<span id="page-22-0"></span>Image /page/22/Picture/0 description: A silver parking meter is covered in snow. The meter has a slot for coins and a display screen. There is a red sticker on the meter that says "3 HR TIME LIMIT" in white letters. The background shows a street with snow on the ground and a red car parked on the side of the road.

a coin meter that is used for parking

Image /page/22/Picture/2 description: A close-up, slightly blurry image of a silver parking meter on a snowy day. The meter has a slot for coins and a display showing "00-00". A red sticker on the meter reads "AH 3 HR LIMIT". In the background, a red car is parked on a street covered in snow.

an outdoor public restroom for men and a trash bin

Image /page/22/Picture/4 description: A beach scene at sunset with people silhouetted against the colorful sky and ocean waves. The sky is a gradient of blue, pink, and orange, with fluffy clouds. The ocean has white-capped waves rolling onto the shore. Several people are visible on the sandy beach, some standing and some sitting.

there are people enjoying the beach at dusk

Image /page/22/Picture/6 description: A beautiful sunset over the ocean with people on the beach. The sky is filled with colorful clouds in shades of pink, orange, and purple. The waves are crashing on the shore, and the sand is a warm golden color. There are several people on the beach, some standing and some sitting, enjoying the view.

there are two surfers walking along the coast line at the beach

Image /page/22/Picture/8 description: A man with a beard and blue eyes is smiling and holding a glass of red wine. He is wearing a gray and black striped sweater and has a black camera around his neck. The background appears to be a room with paintings on the wall and wine bottles on a shelf.

a man holding a glass of wine while wearing a camera around his neck

Image /page/22/Picture/10 description: A man with blue eyes, a beard, and brown hair is smiling and holding a glass of red wine in his right hand. He is wearing a gray and black striped sweater and has a black Canon camera around his neck. The background appears to be a restaurant with paintings on the wall and wooden furniture.

little boy with baseball glove waiting for a incoming ball

Image /page/22/Picture/12 description: A brown bear is in the water, with its front paws on the side of a white paddle boat. The bear is looking to the left and appears to be trying to climb into the boat.

smiling, and gripping a large object

Image /page/22/Picture/14 description: A brown bear is in the water, leaning over a white boat and appears to be trying to get into it.

a bear is in the water, one bear observes visitors at the zoo, while another bear sleeps

Image /page/22/Picture/16 description: A close-up shot of a Great Dane puppy with black and white markings. The puppy is looking directly at the camera with its ears perked up. Another puppy with similar markings is visible in the background.

there are two dogs on the back of a boat

Image /page/22/Picture/18 description: A close-up, slightly grainy photo shows a white Great Dane puppy with black patches over its left eye and ear. The puppy is looking directly at the camera with its front paws resting on a gray surface. Another puppy, also white with black patches, is visible in the background behind the main subject.

a brown black and white dog and another black and white dog

Image /page/22/Picture/20 description: A snowboarder is captured mid-air, performing a trick over a series of colorful barrels covered in snow. The snowboarder is wearing a yellow and black jacket, black pants, and a helmet. The sun is shining brightly in the clear blue sky, casting a lens flare. Two other people are visible in the background, one on the left appearing to cheer or guide, and another on the right watching the snowboarder.

a man riding a snowboard on top of barrels

Image /page/22/Picture/22 description: A snowboarder is performing a trick over a series of colorful barrels in the snow. The sun is shining brightly, casting a lens flare. Two people are watching the snowboarder from the sides.

surf boarder riding on the top of a wave

Image /page/22/Picture/24 description: A close-up shot of a whole pizza with one slice removed, sitting on a wooden pizza peel. The pizza has a golden-brown crust and is generously topped with melted mozzarella cheese and a red tomato sauce. The lighting is warm, highlighting the texture of the cheese and crust.

a small pizza on a cutting board with one slice displayed

Image /page/22/Picture/26 description: A close-up shot of a whole pizza with one slice removed, sitting on a wooden surface. The pizza has a thick crust and is topped with melted cheese and tomato sauce.

chicago style deep dish pizza with tomato sauce and sausage

Image /page/22/Picture/28 description: A man stands in front of a signpost with two signs. The top sign reads "Drygas Gate" and the bottom sign has a number 659 on it. The man is wearing a denim jacket, a black t-shirt, and blue jeans. He is smiling and standing with his legs apart. Behind him, there are trees and mountains. The sky is cloudy.

this is a man posing by a road sign that says dyrgas gate

Image /page/22/Picture/30 description: A man stands in front of a signpost that reads "Dryden's Gate" in a rural setting with mountains in the background.

sign at the corner of clinton st and sw 68th st indicating salem exit approaching

Figure 10: MS-COCO before and after distillation. (*Left*) The original image-text pairs before the distillation. (*Right*) The image-text pairs after distillation.
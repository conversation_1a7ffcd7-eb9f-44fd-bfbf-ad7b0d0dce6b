# LORA: LOW-<PERSON><PERSON><PERSON> ADAPTATION OF LARGE LAN-GUAGE MODELS

<PERSON><sup>∗</sup> <PERSON><PERSON><sup>∗</sup> <PERSON>-<PERSON>hu Chen Microsoft Corporation {edward<PERSON>, yeshe, p<PERSON>llis, zeyuan<PERSON>, yuan<PERSON><PERSON>, swang, luw, wzchen}@microsoft.com <EMAIL> (Version 2)

## ABSTRACT

An important paradigm of natural language processing consists of large-scale pretraining on general domain data and adaptation to particular tasks or domains. As we pre-train larger models, full fine-tuning, which retrains all model parameters, becomes less feasible. Using GPT-3 175B as an example – deploying independent instances of fine-tuned models, each with 175B parameters, is prohibitively expensive. We propose Low-Rank Adaptation, or LoRA, which freezes the pretrained model weights and injects trainable rank decomposition matrices into each layer of the Transformer architecture, greatly reducing the number of trainable parameters for downstream tasks. Compared to GPT-3 175B fine-tuned with <PERSON>, LoRA can reduce the number of trainable parameters by 10,000 times and the GPU memory requirement by 3 times. LoRA performs on-par or better than finetuning in model quality on RoBERTa, DeBERTa, GPT-2, and GPT-3, despite having fewer trainable parameters, a higher training throughput, and, unlike adapters, *no additional inference latency*. We also provide an empirical investigation into rank-deficiency in language model adaptation, which sheds light on the efficacy of LoRA. We release a package that facilitates the integration of LoRA with PyTorch models and provide our implementations and model checkpoints for RoBERTa, DeBERTa, and GPT-2 at <https://github.com/microsoft/LoRA>.

## 1 INTRODUCTION

Many applications in natural language processing rely on adapting *one* large-scale, pre-trained language model to *multiple* downstream applications. Such adaptation is usually done via *fine-tuning*, which updates all the parameters of the pre-trained model. The major downside of fine-tuning is that the new model contains as many parameters as in the original model. As larger models are trained every few months, this changes from a mere "inconvenience" for GPT-2 [\(Radford et al., b\)](#page-14-0) or RoBERTa large [\(Liu et al., 2019\)](#page-14-1) to a critical deployment challenge for GPT-3 [\(Brown et al., 2020\)](#page-12-0) with [1](#page-0-0)75 billion trainable parameters.<sup>1</sup>

Many sought to mitigate this by adapting only some parameters or learning external modules for new tasks. This way, we only need to store and load a small number of task-specific parameters in addition to the pre-trained model for each task, greatly boosting the operational efficiency when deployed. However, existing techniques

Figure 1: Our reparametrization. We only train  $A$  and  $B$ .

<span id="page-0-1"></span>h [ €  $\oslash$ Pretrained Weights r  $W \in \mathbb{R}^{d \times d}$  $2<sub>1</sub>$  $A = \mathcal{N}(0, \sigma^2)$  $\lambda$ d x [

<sup>∗</sup>Equal contribution.

<span id="page-0-0"></span> $0$ Compared to V1, this draft includes better baselines, experiments on GLUE, and more on adapter latency. <sup>1</sup>While GPT-3 175B achieves non-trivial performance with few-shot learning, fine-tuning boosts its perfor-

often introduce inference latency [\(Houlsby et al., 2019;](#page-13-0) [Rebuffi et al., 2017\)](#page-14-2) by extending model depth or reduce the model's usable sequence length [\(Li & Liang, 2021;](#page-13-1) [Lester et al., 2021;](#page-13-2) [Ham](#page-13-3)[bardzumyan et al., 2020;](#page-13-3) [Liu et al., 2021\)](#page-14-3) [\(Section 3\)](#page-2-0). More importantly, these method often fail to match the fine-tuning baselines, posing a trade-off between efficiency and model quality.

We take inspiration from [Li et al.](#page-13-4) [\(2018a\)](#page-13-4); [Aghajanyan et al.](#page-12-1) [\(2020\)](#page-12-1) which show that the learned over-parametrized models in fact reside on a low intrinsic dimension. We hypothesize that the change in weights during model adaptation also has a low "intrinsic rank", leading to our proposed Low-Rank Adaptation (LoRA) approach. LoRA allows us to train some dense layers in a neural network indirectly by optimizing rank decomposition matrices of the dense layers' change during adaptation instead, while keeping the pre-trained weights frozen, as shown in [Figure 1.](#page-0-1) Using GPT-3 175B as an example, we show that a very low rank (i.e., *r* in [Figure 1](#page-0-1) can be one or two) suffices even when the full rank (i.e., *d*) is as high as 12,288, making LoRA both storage- and compute-efficient.

LoRA possesses several key advantages.

- A pre-trained model can be shared and used to build many small LoRA modules for different tasks. We can freeze the shared model and efficiently switch tasks by replacing the matrices  $\hat{A}$  and  $\hat{B}$  in [Figure 1,](#page-0-1) reducing the storage requirement and task-switching overhead significantly.
- LoRA makes training more efficient and lowers the hardware barrier to entry by up to 3 times when using adaptive optimizers since we do not need to calculate the gradients or maintain the optimizer states for most parameters. Instead, we only optimize the injected, much smaller low-rank matrices.
- Our simple linear design allows us to merge the trainable matrices with the frozen weights when deployed, *introducing no inference latency* compared to a fully fine-tuned model, by construction.
- LoRA is orthogonal to many prior methods and can be combined with many of them, such as prefix-tuning. We provide an example in [Appendix E.](#page-19-0)

Terminologies and Conventions We make frequent references to the Transformer architecture and use the conventional terminologies for its dimensions. We call the input and output dimension size of a Transformer layer  $d_{model}$ . We use  $W_q$ ,  $W_k$ ,  $W_v$ , and  $W_o$  to refer to the query/key/value/output projection matrices in the self-attention module. W or  $W_0$  refers to a pretrained weight matrix and  $\Delta W$  its accumulated gradient update during adaptation. We use r to denote the rank of a LoRA module. We follow the conventions set out by [\(Vaswani et al., 2017;](#page-15-1) [Brown et al., 2020\)](#page-12-0) and use Adam [\(Loshchilov & Hutter, 2019;](#page-14-4) [Kingma & Ba, 2017\)](#page-13-5) for model optimization and use a Transformer MLP feedforward dimension  $d_{ffn} = 4 \times d_{model}$ .

## 2 PROBLEM STATEMENT

While our proposal is agnostic to training objective, we focus on language modeling as our motivating use case. Below is a brief description of the language modeling problem and, in particular, the maximization of conditional probabilities given a task-specific prompt.

Suppose we are given a pre-trained autoregressive language model  $P_{\Phi}(y|x)$  parametrized by  $\Phi$ . For instance,  $P_{\Phi}(y|x)$  can be a generic multi-task learner such as GPT [\(Radford et al., b;](#page-14-0) [Brown](#page-12-0) [et al., 2020\)](#page-12-0) based on the Transformer architecture [\(Vaswani et al., 2017\)](#page-15-1). Consider adapting this pre-trained model to downstream conditional text generation tasks, such as summarization, machine reading comprehension (MRC), and natural language to SQL (NL2SQL). Each downstream task is represented by a training dataset of context-target pairs:  $\mathcal{Z} = \{(x_i, y_i)\}_{i=1,\dots,N}$ , where both  $x_i$  and  $y_i$  are sequences of tokens. For example, in NL2SQL,  $x_i$  is a natural language query and  $y_i$  its corresponding SQL command; for summarization,  $x_i$  is the content of an article and  $y_i$  its summary.

During full fine-tuning, the model is initialized to pre-trained weights  $\Phi_0$  and updated to  $\Phi_0 + \Delta \Phi$ by repeatedly following the gradient to maximize the conditional language modeling objective:

$$
\max_{\Phi} \sum_{(x,y)\in\mathcal{Z}} \sum_{t=1}^{|y|} \log \left( P_{\Phi}(y_t | x, y_{
$$

One of the main drawbacks for full fine-tuning is that for *each* downstream task, we learn a *different* set of parameters  $\Delta\Phi$  whose dimension  $|\Delta\Phi|$  equals  $|\Phi_0|$ . Thus, if the pre-trained model is large (such as GPT-3 with  $|\Phi_0| \approx 175$  Billion), storing and deploying many independent instances of fine-tuned models can be challenging, if at all feasible.

In this paper, we adopt a more parameter-efficient approach, where the task-specific parameter increment  $\Delta \Phi = \Delta \Phi(\Theta)$  is further encoded by a much smaller-sized set of parameters  $\Theta$  with  $|\Theta| \ll |\Phi_0|$ . The task of finding  $\Delta\Phi$  thus becomes optimizing over  $\Theta$ :

$$
\max_{\Theta} \sum_{(x,y)\in\mathcal{Z}} \sum_{t=1}^{|y|} \log \left( p_{\Phi_0 + \Delta\Phi(\Theta)}(y_t | x, y_{
$$

In the subsequent sections, we propose to use a low-rank representation to encode  $\Delta\Phi$  that is both compute- and memory-efficient. When the pre-trained model is GPT-3 175B, the number of trainable parameters  $|\Theta|$  can be as small as  $0.01\%$  of  $|\Phi_0|$ .

<span id="page-2-0"></span>

## 3 AREN'T EXISTING SOLUTIONS GOOD ENOUGH?

The problem we set out to tackle is by no means new. Since the inception of transfer learning, dozens of works have sought to make model adaptation more parameter- and compute-efficient. See [Sec](#page-7-0)[tion 6](#page-7-0) for a survey of some of the well-known works. Using language modeling as an example, there are two prominent strategies when it comes to efficient adaptations: adding adapter layers [\(Houlsby](#page-13-0) [et al., 2019;](#page-13-0) [Rebuffi et al., 2017;](#page-14-2) [Pfeiffer et al., 2021;](#page-14-5) Rücklé et al., 2020) or optimizing some forms of the input layer activations [\(Li & Liang, 2021;](#page-13-1) [Lester et al., 2021;](#page-13-2) [Hambardzumyan et al., 2020;](#page-13-3) [Liu et al., 2021\)](#page-14-3). However, both strategies have their limitations, especially in a large-scale and latency-sensitive production scenario.

Adapter Layers Introduce Inference Latency There are many variants of adapters. We focus on the original design by [Houlsby et al.](#page-13-0) [\(2019\)](#page-13-0) which has two adapter layers per Transformer block and a more recent one by [Lin et al.](#page-13-6) [\(2020\)](#page-13-6) which has only one per block but with an additional LayerNorm [\(Ba et al., 2016\)](#page-12-2). While one can reduce the overall latency by pruning layers or exploit-ing multi-task settings (Rücklé et al., 2020; [Pfeiffer et al., 2021\)](#page-14-5), there is no direct ways to bypass the extra compute in adapter layers. This seems like a non-issue since adapter layers are designed to have few parameters (sometimes  $\langle 1\%$  of the original model) by having a small bottleneck dimension, which limits the FLOPs they can add. However, large neural networks rely on hardware parallelism to keep the latency low, and adapter layers have to be processed sequentially. This makes a difference in the online inference setting where the batch size is typically as small as one. In a generic scenario without model parallelism, such as running inference on GPT-2 [\(Radford et al., b\)](#page-14-0) medium on a single GPU, we see a noticeable increase in latency when using adapters, even with a very small bottleneck dimension [\(Table 1\)](#page-3-0).

This problem gets worse when we need to shard the model as done in [Shoeybi et al.](#page-14-7) [\(2020\)](#page-14-7); [Lep](#page-13-7)[ikhin et al.](#page-13-7) [\(2020\)](#page-13-7), because the additional depth requires more synchronous GPU operations such as AllReduce and Broadcast, unless we store the adapter parameters redundantly many times.

Directly Optimizing the Prompt is Hard The other direction, as exemplified by prefix tuning [\(Li](#page-13-1) [& Liang, 2021\)](#page-13-1), faces a different challenge. We observe that prefix tuning is difficult to optimize and that its performance changes non-monotonically in trainable parameters, confirming similar observations in the original paper. More fundamentally, reserving a part of the sequence length for adaptation necessarily reduces the sequence length available to process a downstream task, which we suspect makes tuning the prompt less performant compared to other methods. We defer the study on task performance to [Section 5.](#page-4-0)

<span id="page-3-0"></span>

| <b>Batch Size</b><br>Sequence Length<br>Θ | 32<br>512<br>0.5M                                    | 16<br>256<br>11M                                    | 128<br>11M                                         |
|-------------------------------------------|------------------------------------------------------|-----------------------------------------------------|----------------------------------------------------|
| Fine-Tune/LoRA                            | $1449.4 \pm 0.8$                                     | $338.0 \pm 0.6$                                     | $19.8 \pm 2.7$                                     |
| Adapter<br>Adapter <sup>H</sup>           | $1482.0 \pm 1.0$ (+2.2%)<br>$1492.2 \pm 1.0$ (+3.0%) | $354.8 \pm 0.5$ (+5.0%)<br>$366.3 \pm 0.5 (+8.4\%)$ | $23.9 \pm 2.1$ (+20.7%)<br>$25.8 \pm 2.2$ (+30.3%) |

Table 1: Infernece latency of a single forward pass in GPT-2 medium measured in milliseconds, averaged over 100 trials. We use an NVIDIA Quadro RTX8000. "|Θ|" denotes the number of trainable parameters in adapter layers. Adapter<sup>L</sup> and Adapter<sup>H</sup> are two variants of adapter tuning, which we describe in [Section 5.1.](#page-4-1) The inference latency introduced by adapter layers can be significant in an online, short-sequence-length scenario. See the full study in [Appendix B.](#page-16-0)

## 4 OUR METHOD

We describe the simple design of LoRA and its practical benefits. The principles outlined here apply to any dense layers in deep learning models, though we only focus on certain weights in Transformer language models in our experiments as the motivating use case.

### 4.1 LOW-RANK-PARAMETRIZED UPDATE MATRICES

A neural network contains many dense layers which perform matrix multiplication. The weight matrices in these layers typically have full-rank. When adapting to a specific task, [Aghajanyan et al.](#page-12-1) [\(2020\)](#page-12-1) shows that the pre-trained language models have a low "instrisic dimension" and can still learn efficiently despite a random projection to a smaller subspace. Inspired by this, we hypothesize the updates to the weights also have a low "intrinsic rank" during adaptation. For a pre-trained weight matrix  $W_0 \in \mathbb{R}^{d \times \tilde{k}}$ , we constrain its update by representing the latter with a low-rank decomposition  $W_0 + \Delta W = W_0 + BA$ , where  $B \in \mathbb{R}^{d \times r}$ ,  $A \in \mathbb{R}^{r \times k}$ , and the rank  $r \ll \min(d, k)$ . During training,  $W_0$  is frozen and does not receive gradient updates, while A and B contain trainable parameters. Note both  $W_0$  and  $\Delta W = BA$  are multiplied with the same input, and their respective output vectors are summed coordinate-wise. For  $h = W_0 x$ , our modified forward pass yields:

$$
h = W_0 x + \Delta W x = W_0 x + B A x \tag{3}
$$

We illustrate our reparametrization in [Figure 1.](#page-0-1) We use a random Gaussian initialization for A and zero for B, so  $\Delta W = BA$  is zero at the beginning of training. We then scale  $\Delta Wx$  by  $\frac{\alpha}{r}$ , where  $\alpha$ is a constant in r. When optimizing with Adam, tuning  $\alpha$  is roughly the same as tuning the learning rate if we scale the initialization appropriately. As a result, we simply set  $\alpha$  to the first r we try and do not tune it. This scaling helps to reduce the need to retune hyperparameters when we vary r [\(Yang & Hu, 2021\)](#page-15-2).

A Generalization of Full Fine-tuning. A more general form of fine-tuning allows the training of a subset of the pre-trained parameters. LoRA takes a step further and does not require the accumulated gradient update to weight matrices to have full-rank during adaptation. This means that when applying LoRA to all weight matrices and training all biases<sup>[2](#page-3-1)</sup>, we roughly recover the expressiveness of full fine-tuning by setting the LoRA rank  $r$  to the rank of the pre-trained weight matrices. In other words, as we increase the number of trainable parameters  $3$ , training LoRA roughly converges to training the original model, while adapter-based methods converges to an MLP and prefix-based methods to a model that cannot take long input sequences.

No Additional Inference Latency. When deployed in production, we can explicitly compute and store  $W = W_0 + BA$  and perform inference as usual. Note that both  $W_0$  and  $B\dot{A}$  are in  $\mathbb{R}^{d \times k}$ . When we need to switch to another downstream task, we can recover  $W_0$  by subtracting  $BA$  and then adding a different  $B'A'$ , a quick operation with very little memory overhead. Critically, this

<span id="page-3-1"></span><sup>&</sup>lt;sup>2</sup>They represent a negligible number of parameters compared to weights.

<span id="page-3-2"></span><sup>&</sup>lt;sup>3</sup>An inevitability when adapting to hard tasks.

guarantees that we do not introduce any additional latency during inference compared to a fine-tuned model by construction.

<span id="page-4-4"></span>

### 4.2 APPLYING LORA TO TRANSFORMER

In principle, we can apply LoRA to any subset of weight matrices in a neural network to reduce the number of trainable parameters. In the Transformer architecture, there are four weight matrices in the self-attention module  $(W_q, W_k, W_v, W_o)$  and two in the MLP module. We treat  $W_q$  (or  $W_k, W_v$ ) as a single matrix of dimension  $d_{model} \times d_{model}$ , even though the output dimension is usually sliced into attention heads. We limit our study to **only adapting the attention weights** for downstream tasks and freeze the MLP modules (so they are not trained in downstream tasks) both for simplicity and parameter-efficiency.We further study the effect on adapting different types of attention weight matrices in a Transformer in [Section 7.1.](#page-9-0) We leave the empirical investigation of adapting the MLP layers, LayerNorm layers, and biases to a future work.

Practical Benefits and Limitations. The most significant benefit comes from the reduction in memory and storage usage. For a large Transformer trained with Adam, we reduce that VRAM usage by up to  $2/3$  if  $r \ll d_{model}$  as we do not need to store the optimizer states for the frozen parameters. On GPT-3 175B, we reduce the VRAM consumption during training from 1.2TB to 350GB. With  $r = 4$  and only the query and value projection matrices being adapted, the checkpoint size is reduced by roughly  $10,000 \times$  (from 350GB to 35MB)<sup>[4](#page-4-2)</sup>. This allows us to train with significantly fewer GPUs and avoid I/O bottlenecks. Another benefit is that we can switch between tasks while deployed at a much lower cost by only swapping the LoRA weights as opposed to all the parameters. This allows for the creation of many customized models that can be swapped in and out on the fly on machines that store the pre-trained weights in VRAM. We also observe a 25% speedup during training on GPT-3 17[5](#page-4-3)B compared to full fine-tuning<sup>5</sup> as we do not need to calculate the gradient for the vast majority of the parameters.

LoRA also has its limitations. For example, it is not straightforward to batch inputs to different tasks with different A and B in a single forward pass, if one chooses to absorb A and B into W to eliminate additional inference latency. Though it is possible to not merge the weights and dynamically choose the LoRA modules to use for samples in a batch for scenarios where latency is not critical.

<span id="page-4-0"></span>

## 5 EMPIRICAL EXPERIMENTS

We evaluate the downstream task performance of LoRA on RoBERTa [\(Liu et al., 2019\)](#page-14-1), De-BERTa [\(He et al., 2021\)](#page-13-8), and GPT-2 [\(Radford et al., b\)](#page-14-0), before scaling up to GPT-3 175B [\(Brown](#page-12-0) [et al., 2020\)](#page-12-0). Our experiments cover a wide range of tasks, from natural language understanding (NLU) to generation (NLG). Specifically, we evaluate on the GLUE [\(Wang et al., 2019\)](#page-15-3) benchmark for RoBERTa and DeBERTa. We follow the setup of [Li & Liang](#page-13-1) [\(2021\)](#page-13-1) on GPT-2 for a direct comparison and add WikiSQL [\(Zhong et al., 2017\)](#page-15-4) (NL to SQL queries) and SAMSum [\(Gliwa et al.,](#page-13-9) [2019\)](#page-13-9) (conversation summarization) for large-scale experiments on GPT-3. See [Appendix C](#page-16-1) for more details on the datasets we use. We use NVIDIA Tesla V100 for all experiments.

<span id="page-4-1"></span>

### 5.1 BASELINES

To compare with other baselines broadly, we replicate the setups used by prior work and reuse their reported numbers whenever possible. This, however, means that some baselines might only appear in certain experiments.

Fine-Tuning (FT) is a common approach for adaptation. During fine-tuning, the model is initialized to the pre-trained weights and biases, and all model parameters undergo gradient updates.A simple variant is to update only some layers while freezing others. We include one such baseline reported in prior work [\(Li & Liang, 2021\)](#page-13-1) on GPT-2, which adapts just the last two layers ( $FT<sup>Top2</sup>$ ).

<span id="page-4-2"></span><sup>&</sup>lt;sup>4</sup>We still need the 350GB model during deployment; however, storing 100 adapted models only requires  $350GB + 35MB * 100 \approx 354GB$  as opposed to  $100 * 350GB \approx 35TB$ .

<span id="page-4-3"></span> ${}^{5}$ For GPT-3 175B, the training throughput for full fine-tuning is 32.5 tokens/s per V100 GPU; with the same number of weight shards for model parallelism, the throughput is 43.1 tokens/s per V100 GPU for LoRA.

<span id="page-5-0"></span>

| Model & Method    | # Trainable Parameters | MNLI            | SST-2           | MRPC        | CoLA        | QNLI        | QQP         | RTE       | STS-B           | Avg.        |
|-------------------|------------------------|-----------------|-----------------|-------------|-------------|-------------|-------------|-----------|-----------------|-------------|
| RoBbase (FT)*     | 125.0M                 | <b>87.6</b>     | 94.8            | 90.2        | <b>63.6</b> | 92.8        | <b>91.9</b> | 78.7      | 91.2            | 86.4        |
| RoBbase (BitFit)* | 0.1M                   | 84.7            | 93.7            | <b>92.7</b> | 62.0        | 91.8        | 84.0        | 81.5      | 90.8            | 85.2        |
| RoBbase (AdptD)*  | 0.3M                   | 87.1±.0         | 94.2±.1         | 88.5±.1     | 60.8±.4     | 93.1±.1     | 90.2±.0     | 71.5±.2.7 | 89.7±.3         | 84.4        |
| RoBbase (AdptD)*  | 0.9M                   | 87.3±.1         | 94.7±.3         | 88.4±.1     | 62.6±.9     | 93.0±.2     | 90.6±.0     | 75.9±.2.2 | 90.3±.1         | 85.4        |
| RoBbase (LoRA)    | 0.3M                   | 87.5±.3         | <b>95.1</b> ±.2 | 89.7±.7     | 63.4±.1.2   | 93.3±.3     | 90.8±.1     | 86.6±.7   | <b>91.5</b> ±.2 | <b>87.2</b> |
| RoBlarge (FT)*    | 355.0M                 | 90.2            | <b>96.4</b>     | <b>90.9</b> | 68.0        | 94.7        | <b>92.2</b> | 86.6      | 92.4            | 88.9        |
| RoBlarge (LoRA)   | 0.8M                   | <b>90.6</b> ±.2 | 96.2±.5         | 90.9±.1.2   | 68.2±.1.9   | 94.9±.3     | 91.6±.1     | 87.4±.2.5 | <b>92.6</b> ±.2 | <b>89.0</b> |
| RoBlarge (AdptP)† | 3.0M                   | 90.2±.3         | 96.1±.3         | 90.2±.7     | 68.3±.0     | 94.8±.2     | 91.9±.1     | 83.8±.2.9 | 92.1±.7         | 88.4        |
| RoBlarge (AdptP)† | 0.8M                   | 90.5±.3         | 96.6±.2         | 89.7±.1.2   | 67.8±.2.5   | 94.8±.3     | 91.7±.2     | 80.1±.2.9 | 91.9±.4         | 87.9        |
| RoBlarge (AdptH)† | 6.0M                   | 89.9±.5         | 96.2±.3         | 88.7±.2.9   | 66.5±.4.4   | 94.7±.2     | 92.1±.1     | 83.4±.1.1 | 91.0±.1.7       | 87.8        |
| RoBlarge (AdptH)† | 0.8M                   | 90.3±.3         | 96.3±.5         | 87.7±.1.7   | 66.3±.2.0   | 94.7±.2     | 91.5±.1     | 72.9±.2.9 | 91.5±.5         | 86.4        |
| RoBlarge (LoRA)†  | 0.8M                   | 90.6±.2         | 96.2±.5         | 90.2±.1.0   | 68.2±.1.9   | 94.8±.3     | 91.6±.2     | 85.2±.1.1 | 92.3±.5         | 88.6        |
| DeBXXL (FT)*      | 1500.0M                | 91.8            | <b>97.2</b>     | 92.0        | 72.0        | <b>96.0</b> | 92.7        | 93.9      | 92.9            | 91.1        |
| DeBXXL (LoRA)     | 4.7M                   | 91.9±.2         | 96.9±.2         | 92.6±.6     | 72.4±.1.1   | 96.0±.1     | 92.9±.1     | 94.9±.4   | <b>93.0</b> ±.2 | 91.3        |

Table 2: RoBERTa<sub>base</sub>, RoBERTa<sub>large</sub>, and DeBERTa<sub>XXL</sub> with different adaptation methods on the GLUE benchmark. We report the overall (matched and mismatched) accuracy for MNLI, Matthew's correlation for CoLA, Pearson correlation for STS-B, and accuracy for other tasks. Higher is better for all metrics. \* indicates numbers published in prior works. † indicates runs configured in a setup similar to [Houlsby et al.](#page-13-0) [\(2019\)](#page-13-0) for a fair comparison.

Bias-only or BitFit is a baseline where we only train the bias vectors while freezing everything else. Contemporarily, this baseline has also been studied by BitFit [\(Zaken et al., 2021\)](#page-15-5).

Prefix-embedding tuning (PreEmbed) inserts special tokens among the input tokens. These special tokens have trainable word embeddings and are generally not in the model's vocabulary. Where to place such tokens can have an impact on performance. We focus on "prefixing", which prepends such tokens to the prompt, and "infixing", which appends to the prompt; both are discussed in Li  $\&$ [Liang](#page-13-1) [\(2021\)](#page-13-1). We use  $l_p$  (resp.  $l_i$ ) denote the number of prefix (resp. infix) tokens. The number of trainable parameters is  $|\Theta| = d_{model} \times (l_p + l_i)$ .

Prefix-layer tuning (PreLayer) is an extension to prefix-embedding tuning. Instead of just learning the word embeddings (or equivalently, the activations after the embedding layer) for some special tokens, we learn the activations after every Transformer layer. The activations computed from previous layers are simply replaced by trainable ones. The resulting number of trainable parameters is  $|\Theta| = L \times d_{model} \times (l_p + l_i)$ , where L is the number of Transformer layers.

Adapter tuning as proposed in [Houlsby et al.](#page-13-0) [\(2019\)](#page-13-0) inserts adapter layers between the selfattention module (and the MLP module) and the subsequent residual connection. There are two fully connected layers with biases in an adapter layer with a nonlinearity in between. We call this original design Adapter<sup>H</sup>. Recently, [Lin et al.](#page-13-6) [\(2020\)](#page-13-6) proposed a more efficient design with the adapter layer applied only after the MLP module and after a LayerNorm. We call it  $\mathrm{Adapter}^{\mathrm L}.$  This is very similar to another deign proposed in [Pfeiffer et al.](#page-14-5) [\(2021\)](#page-14-5), which we call Adapter<sup>P</sup>. We also include another baseline call AdapterDrop (Rücklé et al., 2020) which drops some adapter layers for greater efficiency (Adapter<sup>D</sup>). We cite numbers from prior works whenever possible to maximize the number of baselines we compare with; they are in rows with an asterisk  $(*)$  in the first column. In all cases, we have  $|\Theta| = \hat{L}_{Adpt} \times (2 \times d_{model} \times r + r + d_{model}) + 2 \times \hat{L}_{LN} \times d_{model}$  where  $\hat{L}_{Adpt}$ is the number of adapter layers and  $\hat{L}_{LN}$  the number of trainable LayerNorms (e.g., in Adapter<sup>L</sup>).

LoRA adds trainable pairs of rank decomposition matrices in parallel to existing weight matrices. As mentioned in [Section 4.2,](#page-4-4) we only apply LoRA to  $W_q$  and  $W_v$  in most experiments for simplicity. The number of trainable parameters is determined by the rank  $r$  and the shape of the original weights:  $|\Theta| = 2 \times \hat{L}_{LoRA} \times d_{model} \times r$ , where  $\hat{L}_{LoRA}$  is the number of weight matrices we apply LoRA to.

<span id="page-6-0"></span>

| Model & Method      | # Trainable<br>Parameters | E2E NLG Challenge                |                                   |                                  |                                  |                                   |
|---------------------|---------------------------|----------------------------------|-----------------------------------|----------------------------------|----------------------------------|-----------------------------------|
|                     |                           | BLEU                             | NIST                              | MET                              | ROUGE-L                          | CIDEr                             |
| GPT-2 M (FT)*       | 354.92M                   | 68.2                             | 8.62                              | 46.2                             | 71.0                             | 2.47                              |
| GPT-2 M (AdapterL)* | 0.37M                     | 66.3                             | 8.41                              | 45.0                             | 69.8                             | 2.40                              |
| GPT-2 M (AdapterL)* | 11.09M                    | 68.9                             | 8.71                              | 46.1                             | 71.3                             | 2.47                              |
| GPT-2 M (AdapterH)  | 11.09M                    | $67.3_{\pm.6}$                   | $8.50_{\pm.07}$                   | $46.0_{\pm.2}$                   | $70.7_{\pm.2}$                   | $2.44_{\pm.01}$                   |
| GPT-2 M (FTTop2)*   | 25.19M                    | 68.1                             | 8.59                              | 46.0                             | 70.8                             | 2.41                              |
| GPT-2 M (PreLayer)* | 0.35M                     | 69.7                             | 8.81                              | 46.1                             | 71.4                             | 2.49                              |
| GPT-2 M (LoRA)      | 0.35M                     | <b><math>70.4_{\pm.1}</math></b> | <b><math>8.85_{\pm.02}</math></b> | <b><math>46.8_{\pm.2}</math></b> | <b><math>71.8_{\pm.1}</math></b> | <b><math>2.53_{\pm.02}</math></b> |
| GPT-2 L (FT)*       | 774.03M                   | 68.5                             | 8.78                              | 46.0                             | 69.9                             | 2.45                              |
| GPT-2 L (AdapterL)  | 0.88M                     | $69.1_{\pm.1}$                   | $8.68_{\pm.03}$                   | $46.3_{\pm.0}$                   | $71.4_{\pm.2}$                   | $2.49_{\pm.0}$                    |
| GPT-2 L (AdapterL)  | 23.00M                    | $68.9_{\pm.3}$                   | $8.70_{\pm.04}$                   | $46.1_{\pm.1}$                   | $71.3_{\pm.2}$                   | $2.45_{\pm.02}$                   |
| GPT-2 L (PreLayer)* | 0.77M                     | 70.3                             | 8.85                              | 46.2                             | 71.7                             | 2.47                              |
| GPT-2 L (LoRA)      | 0.77M                     | <b><math>70.4_{\pm.1}</math></b> | <b><math>8.89_{\pm.02}</math></b> | <b><math>46.8_{\pm.2}</math></b> | <b><math>72.0_{\pm.2}</math></b> | <b><math>2.47_{\pm.02}</math></b> |

Table 3: GPT-2 medium (M) and large (L) with different adaptation methods on the E2E NLG Challenge. For all metrics, higher is better. LoRA outperforms several baselines with comparable or fewer trainable parameters. Confidence intervals are shown for experiments we ran. \* indicates numbers published in prior works.

### 5.2 ROBERTA BASE/LARGE

RoBERTa [\(Liu et al., 2019\)](#page-14-1) optimized the pre-training recipe originally proposed in BERT [\(Devlin](#page-12-3) [et al., 2019a\)](#page-12-3) and boosted the latter's task performance without introducing many more trainable parameters. While RoBERTa has been overtaken by much larger models on NLP leaderboards such as the GLUE benchmark [\(Wang et al., 2019\)](#page-15-3) in recent years, it remains a competitive and popular pre-trained model for its size among practitioners. We take the pre-trained RoBERTa base (125M) and RoBERTa large (355M) from the HuggingFace Transformers library [\(Wolf et al., 2020\)](#page-15-6) and evaluate the performance of different efficient adaptation approaches on tasks from the GLUE benchmark. We also replicate [Houlsby et al.](#page-13-0) [\(2019\)](#page-13-0) and [Pfeiffer et al.](#page-14-5) [\(2021\)](#page-14-5) according to their setup. To ensure a fair comparison, we make two crucial changes to how we evaluate LoRA when comparing with adapters. First, we use the same batch size for all tasks and use a sequence length of 128 to match the adapter baselines. Second, we initialize the model to the pre-trained model for MRPC, RTE, and STS-B, not a model already adapted to MNLI like the fine-tuning baseline. Runs following this more restricted setup from [Houlsby et al.](#page-13-0) [\(2019\)](#page-13-0) are labeled with  $\dagger$ . The result is presented in [Table 2](#page-5-0) (Top Three Sections). See [Section D.1](#page-17-0) for details on the hyperparameters used.

### 5.3 DEBERTA XXL

DeBERTa [\(He et al., 2021\)](#page-13-8) is a more recent variant of BERT that is trained on a much larger scale and performs very competitively on benchmarks such as GLUE [\(Wang et al., 2019\)](#page-15-3) and SuperGLUE [\(Wang et al., 2020\)](#page-15-7). We evaluate if LoRA can still match the performance of a fully fine-tuned DeBERTa XXL (1.5B) on GLUE. The result is presented in [Table 2](#page-5-0) (Bottom Section). See [Section D.2](#page-17-1) for details on the hyperparameters used.

### 5.4 GPT-2 MEDIUM/LARGE

Having shown that LoRA can be a competitive alternative to full fine-tuning on NLU, we hope to answer if LoRA still prevails on NLG models, such as GPT-2 medium and large [\(Radford et al.,](#page-14-0) [b\)](#page-14-0). We keep our setup as close as possible to [Li & Liang](#page-13-1)  $(2021)$  for a direct comparison. Due to space constraint, we only present our result on E2E NLG Challenge [\(Table 3\)](#page-6-0) in this section. See [Section F.1](#page-20-0) for results on WebNLG [\(Gardent et al., 2017\)](#page-12-4) and DART [\(Nan et al., 2020\)](#page-14-8). We include a list of the hyperparameters used in [Section D.3.](#page-18-0)

<span id="page-7-1"></span>

| Model&Method     | # Trainable Parameters | WikiSQL Acc. (%) | MNLI-m Acc. (%) | SAMSum R1/R2/RL       |
|------------------|------------------------|------------------|-----------------|-----------------------|
| GPT-3 (FT)       | 175,255.8M             | <b>73.8</b>      | 89.5            | 52.0/28.0/44.5        |
| GPT-3 (BitFit)   | 14.2M                  | 71.3             | 91.0            | 51.3/27.4/43.5        |
| GPT-3 (PreEmbed) | 3.2M                   | 63.1             | 88.6            | 48.3/24.2/40.5        |
| GPT-3 (PreLayer) | 20.2M                  | 70.1             | 89.5            | 50.8/27.3/43.5        |
| GPT-3 (AdapterH) | 7.1M                   | 71.9             | 89.8            | 53.0/28.9/44.8        |
| GPT-3 (AdapterH) | 40.1M                  | 73.2             | <b>91.5</b>     | 53.2/29.0/45.1        |
| GPT-3 (LoRA)     | 4.7M                   | 73.4             | <b>91.7</b>     | <b>53.8/29.8/45.9</b> |
| GPT-3 (LoRA)     | 37.7M                  | <b>74.0</b>      | <b>91.6</b>     | 53.4/29.2/45.1        |

Table 4: Performance of different adaptation methods on GPT-3 175B. We report the logical form validation accuracy on WikiSQL, validation accuracy on MultiNLI-matched, and Rouge-1/2/L on SAMSum. LoRA performs better than prior approaches, including full fine-tuning. The results on WikiSQL have a fluctuation around  $\pm 0.5\%$ , MNLI-m around  $\pm 0.1\%$ , and SAMSum around  $\pm 0.2/\pm 0.2/\pm 0.1$  for the three metrics.

### 5.5 SCALING UP TO GPT-3 175B

As a final stress test for LoRA, we scale up to GPT-3 with 175 billion parameters. Due to the high training cost, we only report the typical standard deviation for a given task over random seeds, as opposed to providing one for every entry. See [Section D.4](#page-18-1) for details on the hyperparameters used.

As shown in [Table 4,](#page-7-1) LoRA matches or exceeds the fine-tuning baseline on all three datasets. Note that not all methods benefit monotonically from having more trainable parameters, as shown in [Fig](#page-7-2)[ure 2.](#page-7-2) We observe a significant performance drop when we use more than 256 special tokens for prefix-embedding tuning or more than 32 special tokens for prefix-layer tuning. This corroborates similar observations in [Li & Liang](#page-13-1) [\(2021\)](#page-13-1). While a thorough investigation into this phenomenon is out-of-scope for this work, we suspect that having more special tokens causes the input distribution to shift further away from the pre-training data distribution. Separately, we investigate the performance of different adaptation approaches in the low-data regime in [Section F.3.](#page-21-0)

<span id="page-7-2"></span>Image /page/7/Figure/5 description: This image contains two scatter plots side-by-side, both plotting Validation Accuracy on the y-axis against the log10 of the number of trainable parameters on the x-axis. The left plot is titled 'WikiSQL' and the right plot is titled 'MultiNLI-matched'. Both plots display data points for five different methods: Fine-Tune (blue circles), PrefixEmbed (orange crosses), PrefixLayer (green stars), Adapter(H) (orange crosses), and LoRA (purple inverted triangles). The WikiSQL plot shows validation accuracies ranging from approximately 0.55 to 0.75, with the Fine-Tune method having a single data point at around 0.74. The MultiNLI-matched plot shows validation accuracies ranging from approximately 0.84 to 0.92, with the Fine-Tune method having a single data point at around 0.89. The other methods in both plots show trends of increasing and then decreasing validation accuracy as the number of trainable parameters increases.

Figure 2: GPT-3 175B validation accuracy vs. number of trainable parameters of several adaptation methods on WikiSQL and MNLI-matched. LoRA exhibits better scalability and task performance. See [Section F.2](#page-21-1) for more details on the plotted data points.

<span id="page-7-0"></span>

## 6 RELATED WORKS

Transformer Language Models. Transformer [\(Vaswani et al., 2017\)](#page-15-1) is a sequence-to-sequence architecture that makes heavy use of self-attention. [Radford et al.](#page-14-9) [\(a\)](#page-14-9) applied it to autoregressive language modeling by using a stack of Transformer decoders. Since then, Transformer-based language models have dominated NLP, achieving the state-of-the-art in many tasks. A new paradigm emerged with BERT [\(Devlin et al., 2019b\)](#page-12-5) and GPT-2 [\(Radford et al., b\)](#page-14-0) – both are large Transformer lan-

guage models trained on a large amount of text – where fine-tuning on task-specific data after pretraining on general domain data provides a significant performance gain compared to training on task-specific data directly. Training larger Transformers generally results in better performance and remains an active research direction. GPT-3 [\(Brown et al., 2020\)](#page-12-0) is the largest single Transformer language model trained to-date with 175B parameters.

Prompt Engineering and Fine-Tuning. While GPT-3 175B can adapt its behavior with just a few additional training examples, the result depends heavily on the input prompt [\(Brown et al.,](#page-12-0) [2020\)](#page-12-0). This necessitates an empirical art of composing and formatting the prompt to maximize a model's performance on a desired task, which is known as prompt engineering or prompt hacking. Fine-tuning retrains a model pre-trained on general domains to a specific task [Devlin et al.](#page-12-5) [\(2019b\)](#page-12-5); [Radford et al.](#page-14-9) [\(a\)](#page-14-9). Variants of it include learning just a subset of the parameters [Devlin et al.](#page-12-5) [\(2019b\)](#page-12-5); [Collobert & Weston](#page-12-6) [\(2008\)](#page-12-6), yet practitioners often retrain all of them to maximize the downstream performance. However, the enormity of GPT-3 175B makes it challenging to perform fine-tuning in the usual way due to the large checkpoint it produces and the high hardware barrier to entry since it has the same memory footprint as pre-training.

Parameter-Efficient Adaptation. Many have proposed inserting *adapter* layers between existing layers in a neural network [\(Houlsby et al., 2019;](#page-13-0) [Rebuffi et al., 2017;](#page-14-2) [Lin et al., 2020\)](#page-13-6). Our method uses a similar bottleneck structure to impose a low-rank constraint on the weight updates. The key functional difference is that our learned weights can be merged with the main weights during inference, thus not introducing any latency, which is not the case for the adapter layers [\(Section 3\)](#page-2-0). A comtenporary extension of adapter is COMPACTER [\(Mahabadi et al., 2021\)](#page-14-10), which essentially parametrizes the adapter layers using Kronecker products with some predetermined weight sharing scheme. Similarly, combining LoRA with other tensor product-based methods could potentially improve its parameter efficiency, which we leave to future work. More recently, many proposed optimizing the input word embeddings in lieu of fine-tuning, akin to a continuous and differentiable generalization of prompt engineering [\(Li & Liang, 2021;](#page-13-1) [Lester et al., 2021;](#page-13-2) [Hambardzumyan et al.,](#page-13-3) [2020;](#page-13-3) [Liu et al., 2021\)](#page-14-3). We include comparisons with [Li & Liang](#page-13-1) [\(2021\)](#page-13-1) in our experiment section. However, this line of works can only scale up by using more special tokens in the prompt, which take up available sequence length for task tokens when positional embeddings are learned.

Low-Rank Structures in Deep Learning. Low-rank structure is very common in machine learning. A lot of machine learning problems have certain intrinsic low-rank structure [\(Li et al., 2016;](#page-13-10) [Cai et al., 2010;](#page-12-7) [Li et al., 2018b;](#page-13-11) [Grasedyck et al., 2013\)](#page-13-12). Moreover, it is known that for many deep learning tasks, especially those with a heavily over-parametrized neural network, the learned neural network will enjoy low-rank properties after training [\(Oymak et al., 2019\)](#page-14-11). Some prior works even explicitly impose the low-rank constraint when training the original neural network [\(Sainath](#page-14-12) [et al., 2013;](#page-14-12) [Povey et al., 2018;](#page-14-13) [Zhang et al., 2014;](#page-15-8) [Jaderberg et al., 2014;](#page-13-13) [Zhao et al., 2016;](#page-15-9) [Kho](#page-13-14)[dak et al., 2021;](#page-13-14) [Denil et al., 2014\)](#page-12-8); however, to the best of our knowledge, none of these works considers low-rank update to a frozen model for *adaptation to downstream tasks*. In theory literature, it is known that neural networks outperform other classical learning methods, including the corresponding (finite-width) neural tangent kernels [\(Allen-Zhu et al., 2019;](#page-12-9) [Li & Liang, 2018\)](#page-13-15) when the underlying concept class has certain low-rank structure [\(Ghorbani et al., 2020;](#page-13-16) [Allen-Zhu & Li,](#page-12-10) [2019;](#page-12-10) [Allen-Zhu & Li, 2020a\)](#page-12-11). Another theoretical result in [Allen-Zhu & Li](#page-12-12) [\(2020b\)](#page-12-12) suggests that low-rank adaptations can be useful for adversarial training. In sum, we believe that our proposed low-rank adaptation update is well-motivated by the literature.

## 7 UNDERSTANDING THE LOW-RANK UPDATES

Given the empirical advantage of LoRA, we hope to further explain the properties of the low-rank adaptation learned from downstream tasks. Note that the low-rank structure not only lowers the hardware barrier to entry which allows us to run multiple experiments in parallel, but also gives better interpretability of how the update weights are correlated with the pre-trained weights. We focus our study on GPT-3 175B, where we achieved the largest reduction of trainable parameters (up to  $10,000\times$ ) without adversely affecting task performances.

We perform a sequence of empirical studies to answer the following questions: 1) Given a parameter budget constraint, *which subset of weight matrices* in a pre-trained Transformer should we adapt to maximize downstream performance? 2) Is the "optimal" adaptation matrix ∆W *really rankdeficient*? If so, what is a good rank to use in practice? 3) What is the connection between  $\Delta W$  and W? Does  $\Delta W$  highly correlate with W? How large is  $\Delta W$  comparing to W?

We believe that our answers to question (2) and (3) shed light on the fundamental principles of using pre-trained language models for downstream tasks, which is a critical topic in NLP.

<span id="page-9-0"></span>7.1 WHICH WEIGHT MATRICES IN TRANSFORMER SHOULD WE APPLY LORA TO?

Given a limited parameter budget, which types of weights should we adapt with LoRA to obtain the best performance on downstream tasks? As mentioned in [Section 4.2,](#page-4-4) we only consider weight matrices in the self-attention module. We set a parameter budget of 18M (roughly 35MB if stored in FP16) on GPT-3 175B, which corresponds to  $r = 8$  if we adapt one type of attention weights or  $r = 4$  if we adapt two types, for all 96 layers. The result is presented in [Table 5.](#page-9-1)

<span id="page-9-1"></span>

| Weight Type<br>Rank $r$   | # of Trainable Parameters = 18M |            |            |            |                    |                    |                                    |
|---------------------------|---------------------------------|------------|------------|------------|--------------------|--------------------|------------------------------------|
|                           | $W_q$<br>8                      | $W_k$<br>8 | $W_v$<br>8 | $W_o$<br>8 | $W_q$ , $W_k$<br>4 | $W_q$ , $W_v$<br>4 | $W_q$ , $W_k$ , $W_v$ , $W_o$<br>2 |
| WikiSQL $(\% \pm 0.5\%)$  | 70.4                            | 70.0       | 73.0       | 73.2       | 71.4               | 73.7               | 73.7                               |
| MultiNLI $(\% \pm 0.1\%)$ | 91.0                            | 90.8       | 91.0       | 91.3       | 91.3               | 91.3               | 91.7                               |

Table 5: Validation accuracy on WikiSQL and MultiNLI after applying LoRA to different types of attention weights in GPT-3, given the same number of trainable parameters. Adapting both  $W_q$  and  $W<sub>v</sub>$  gives the best performance overall. We find the standard deviation across random seeds to be consistent for a given dataset, which we report in the first column.

Note that putting all the parameters in  $\Delta W_q$  or  $\Delta W_k$  results in significantly lower performance, while adapting both  $W_q$  and  $W_v$  yields the best result. This suggests that even a rank of four captures enough information in  $\Delta W$  such that it is preferable to adapt more weight matrices than adapting a single type of weights with a larger rank.

<span id="page-9-4"></span>

### 7.2 WHAT IS THE OPTIMAL RANK r FOR LORA?

We turn our attention to the effect of rank r on model performance. We adapt  $\{W_q, W_q\}$ ,  $\{W_a, W_k, W_v, W_c\}$ , and just  $W_q$  for a comparison.

<span id="page-9-2"></span>

|                            | Weight Type          | $r = 1$ | $r = 2$ | $r = 4$ | $r = 8$ | $r = 64$ |
|----------------------------|----------------------|---------|---------|---------|---------|----------|
| WikiSQL(\$\pm 0.5\%\$)     | $W_q$                | 68.8    | 69.6    | 70.5    | 70.4    | 70.0     |
|                            | $W_q, W_v$           | 73.4    | 73.3    | 73.7    | 73.8    | 73.5     |
|                            | $W_q, W_k, W_v, W_o$ | 74.1    | 73.7    | 74.0    | 74.0    | 73.9     |
| MultiNLI (\$\,\pm 0.1\%\$) | $W_q$                | 90.7    | 90.9    | 91.1    | 90.7    | 90.7     |
|                            | $W_q, W_v$           | 91.3    | 91.4    | 91.3    | 91.6    | 91.4     |
|                            | $W_q, W_k, W_v, W_o$ | 91.2    | 91.7    | 91.7    | 91.5    | 91.4     |

Table 6: Validation accuracy on WikiSQL and MultiNLI with different rank r. To our surprise, a rank as small as one suffices for adapting both  $W_q$  and  $W_v$  on these datasets while training  $W_q$  alone needs a larger r. We conduct a similar experiment on GPT-2 in [Section H.2.](#page-23-0)

[Table 6](#page-9-2) shows that, surprisingly, LoRA already performs competitively with a very small  $r$  (more so for  $\{W_q, W_v\}$  than just  $W_q$ ). This suggests the update matrix  $\Delta W$  could have a very small "intrinsic rank".<sup>[6](#page-9-3)</sup> To further support this finding, we check the overlap of the subspaces learned by different choices of r and by different random seeds. We argue that increasing r does not cover a more meaningful subspace, which suggests that a low-rank adaptation matrix is sufficient.

<span id="page-9-3"></span> $6$ However, we do not expect a small  $r$  to work for every task or dataset. Consider the following thought experiment: if the downstream task were in a different language than the one used for pre-training, retraining the entire model (similar to LoRA with  $r = d_{model}$ ) could certainly outperform LoRA with a small r.

**Subspace similarity between different r.** Given  $A_{r=8}$  and  $A_{r=64}$  which are the learned adaptation matrices with rank  $r = 8$  and 64 using the *same pre-trained model*, we perform singular value decomposition and obtain the right-singular unitary matrices  $U_{A_{r=8}}$  and  $U_{A_{r=64}}$ .<sup>[7](#page-10-0)</sup> We hope to answer: how much of the subspace spanned by the top i singular vectors in  $U_{A_{r=8}}^{1-\infty}$  (for  $1 \le i \le 8$ ) is contained in the subspace spanned by top j singular vectors of  $U_{A_{r=64}}$  (for  $1 \le j \le 64$ )? We measure this quantity with a normalized subspace similarity based on the Grassmann distance (See [Ap](#page-21-2)[pendix G](#page-21-2) for a more formal discussion)

$$
\phi(A_{r=8}, A_{r=64}, i, j) = \frac{||U_{A_{r=8}}^{i\top} U_{A_{r=64}}^{j}||_F^2}{\min(i, j)} \in [0, 1]
$$
\n(4)

where  $U_{A_{r=8}}^i$  represents the columns of  $U_{A_{r=8}}$  corresponding to the top-*i* singular vectors.

 $\phi(\cdot)$  has a range of [0, 1], where 1 represents a complete overlap of subspaces and 0 a complete separation. See [Figure 3](#page-10-1) for how  $\phi$  changes as we vary i and j. We only look at the 48th layer (out of 96) due to space constraint, but the conclusion holds for other layers as well, as shown in [Section H.1.](#page-23-1)

<span id="page-10-1"></span>Image /page/10/Figure/4 description: The image displays four heatmaps side-by-side, with a color bar on the right indicating values from 0.0 to 1.0. The title of the figure is "φ(Ar = 64, Ar = 8, i, j)". The first heatmap is labeled "ΔWq" on the top and has the y-axis labeled "i" with values from 1 to 8 on the left, and the x-axis labeled "j" with values 1, 6, 12, 18, 23, 29, 35, 40, 46, 52, 58 at the bottom. This heatmap shows a gradient from dark purple at the bottom to pink at the top, with the color intensity decreasing from left to right. The second heatmap is labeled "ΔWv" on the top and shares the same axis labels as the first heatmap. It also shows a gradient from dark purple to pink, but the colored area forms a triangle, with the color intensity decreasing from the top-left corner downwards and to the right. The third heatmap is labeled "ΔWq" on the top and has the y-axis labeled "i" with values from 1 to 8 on the left, and the x-axis labeled "j" with values 1 to 8 at the bottom. This heatmap displays a gradient from dark purple to pink, forming a triangular pattern where the color intensity decreases from the top-left corner downwards and to the right. The fourth heatmap is labeled "ΔWv" on the top and shares the same axis labels as the third heatmap. It also shows a gradient from dark purple to pink in a triangular pattern, similar to the third heatmap.

Figure 3: Subspace similarity between column vectors of  $A_{r=8}$  and  $A_{r=64}$  for both  $\Delta W_q$  and  $\Delta W_v$ . The third and the fourth figures zoom in on the lower-left triangle in the first two figures. The top directions in  $r = 8$  are included in  $r = 64$ , and vice versa.

We make an *important observation* from [Figure 3.](#page-10-1)

Directions corresponding to the top singular vector overlap significantly between  $A_{r=8}$  and  $A_{r=64}$ , while others do not. Specifically,  $\Delta W_v$  (resp.  $\Delta W_q$ ) of  $A_{r=8}$ and  $\Delta W_v$  (resp.  $\Delta W_q$ ) of  $A_{r=64}$  share a subspace of dimension 1 with normalized similarity  $> 0.5$ , providing an explanation of why  $r = 1$  performs quite well in our downstream tasks for GPT-3.

Since both  $A_{r=8}$  and  $A_{r=64}$  are learned using the same pre-trained model, [Figure 3](#page-10-1) indicates that the top singular-vector directions of  $A_{r=8}$  and  $A_{r=64}$  are the most useful, while other directions potentially contain mostly random noises accumulated during training. Hence, the adaptation matrix can indeed have a very low rank.

Subspace similarity between different random seeds. We further confirm this by plotting the normalized subspace similarity between two randomly seeded runs with  $r = 64$ , shown in [Figure 4.](#page-11-0)  $\Delta W_q$  appears to have a higher "intrinsic rank" than  $\Delta W_v$ , since more common singular value directions are learned by both runs for  $\Delta W_q$ , which is in line with our empirical observation in [Table 6.](#page-9-2) As a comparison, we also plot two random Gaussian matrices, which do not share any common singular value directions with each other.

<span id="page-10-2"></span>7.3 HOW DOES THE ADAPTATION MATRIX  $\Delta W$  COMPARE TO W?

We further investigate the relationship between  $\Delta W$  and W. In particular, does  $\Delta W$  highly correlate with W? (Or mathematically, is  $\Delta W$  mostly contained in the top singular directions of W?) Also,

<span id="page-10-0"></span><sup>&</sup>lt;sup>7</sup>Note that a similar analysis can be carried out with B and the left-singular unitary matrices – we stick with A for our experiments.

<span id="page-11-0"></span>Image /page/11/Figure/0 description: The image displays three heatmaps side-by-side, each with a color bar on the right indicating values from 0.0 to 0.5. The leftmost heatmap is titled "ΔWq" and shows a gradient from light pink/orange in the top-left corner to dark purple at the bottom-right. The y-axis is labeled "i" with values 1, 8, 16, 24, 32, 40, 48, 56, and the x-axis is labeled "j" with values 1, 5, 10, 15, 20, 25, 30, 34, 39, 44, 49, 54, 59. The middle heatmap is titled "φ(Ar = 64, A'r = 64, i, j) ΔWv" and exhibits a similar gradient pattern, though the intensity of the colors appears slightly different. The y-axis and x-axis labels are identical to the first heatmap. The rightmost heatmap is titled "Random Gaussian" and is entirely dark purple, indicating minimal or zero values across the entire grid. The color bar suggests that the dark purple color corresponds to values close to 0.0.

Figure 4: Left and Middle: Normalized subspace similarity between the column vectors of  $A_{r=64}$ from two random seeds, for both  $\Delta W_q$  and  $\Delta W_v$  in the 48-th layer. **Right:** the same heat-map between the column vectors of two random Gaussian matrices. See [Section H.1](#page-23-1) for other layers.

how "large" is  $\Delta W$  comparing to its corresponding directions in W? This can shed light on the underlying mechanism for adapting pre-trained language models.

To answer these questions, we project W onto the r-dimensional subspace of  $\Delta W$  by computing  $U^{\top}WV^{\top}$ , with  $U/V$  being the left/right singular-vector matrix of  $\Delta W$ . Then, we compare the Frobenius norm between  $||U^\top W V^\top||_F$  and  $||W||_F$ . As a comparison, we also compute  $\Vert U^\top W V^\top \Vert_F$  by replacing  $U, V$  with the top r singular vectors of W or a random matrix.

<span id="page-11-1"></span>

|                               | r = 4                     |         |        | r = 64                    |         |        |
|-------------------------------|---------------------------|---------|--------|---------------------------|---------|--------|
|                               | $\Delta W_q$              | $W_q$   | Random | $\Delta W_q$              | $W_q$   | Random |
| $  U^{\top}W_qV^{\top}  _F =$ | $0.32$                    | $21.67$ | $0.02$ | $1.90$                    | $37.71$ | $0.33$ |
| $  W_q  _F = 61.95$           | $  \Delta W_q  _F = 6.91$ |         |        | $  \Delta W_q  _F = 3.57$ |         |        |

Table 7: The Frobenius norm of  $U^{\top}W_qV^{\top}$  where U and V are the left/right top r singular vector directions of either (1)  $\Delta W_q$ , (2)  $W_q$ , or (3) a random matrix. The weight matrices are taken from the 48th layer of GPT-3.

We draw *several conclusions* from [Table 7.](#page-11-1) First, ∆W has a stronger correlation with W compared to a random matrix, indicating that  $\Delta W$  amplifies some features that are already in W. Second, instead of repeating the top singular directions of W, ∆W only *amplifies directions that are not emphasized in* W. Third, the amplification factor is rather huge:  $21.5 \approx 6.91/0.32$  for  $r = 4$ . See [Section H.4](#page-24-0) for why  $r = 64$  has a smaller amplification factor. We also provide a visualization in [Section H.3](#page-23-2) for how the correlation changes as we include more top singular directions from  $W_q$ . This suggests that the low-rank adaptation matrix potentially *amplifies the important features for specific downstream tasks that were learned but not emphasized in the general pre-training model*.

## 8 CONCLUSION AND FUTURE WORK

Fine-tuning enormous language models is prohibitively expensive in terms of the hardware required and the storage/switching cost for hosting independent instances for different tasks. We propose LoRA, an efficient adaptation strategy that neither introduces inference latency nor reduces input sequence length while retaining high model quality. Importantly, it allows for quick task-switching when deployed as a service by sharing the vast majority of the model parameters. While we focused on Transformer language models, the proposed principles are generally applicable to any neural networks with dense layers.

There are many directions for future works. 1) LoRA can be combined with other efficient adaptation methods, potentially providing orthogonal improvement. 2) The mechanism behind fine-tuning or LoRA is far from clear – how are features learned during pre-training transformed to do well on downstream tasks? We believe that LoRA makes it more tractable to answer this than full finetuning. 3) We mostly depend on heuristics to select the weight matrices to apply LoRA to. Are there more principled ways to do it? 4) Finally, the rank-deficiency of  $\Delta W$  suggests that W could be rank-deficient as well, which can also be a source of inspiration for future works.

### **REFERENCES**

- <span id="page-12-1"></span>Armen Aghajanyan, Luke Zettlemoyer, and Sonal Gupta. Intrinsic Dimensionality Explains the Effectiveness of Language Model Fine-Tuning. *arXiv:2012.13255 [cs]*, December 2020. URL <http://arxiv.org/abs/2012.13255>.
- <span id="page-12-10"></span>Zeyuan Allen-Zhu and Yuanzhi Li. What Can ResNet Learn Efficiently, Going Beyond Kernels? In *NeurIPS*, 2019. Full version available at <http://arxiv.org/abs/1905.10337>.
- <span id="page-12-11"></span>Zeyuan Allen-Zhu and Yuanzhi Li. Backward feature correction: How deep learning performs deep learning. *arXiv preprint arXiv:2001.04413*, 2020a.
- <span id="page-12-12"></span>Zeyuan Allen-Zhu and Yuanzhi Li. Feature purification: How adversarial training performs robust deep learning. *arXiv preprint arXiv:2005.10190*, 2020b.
- <span id="page-12-9"></span>Zeyuan Allen-Zhu, Yuanzhi Li, and Zhao Song. A convergence theory for deep learning via overparameterization. In *ICML*, 2019. Full version available at [http://arxiv.org/abs/1811.](http://arxiv.org/abs/1811.03962) [03962](http://arxiv.org/abs/1811.03962).
- <span id="page-12-2"></span>Jimmy Lei Ba, Jamie Ryan Kiros, and Geoffrey E. Hinton. Layer normalization, 2016.
- <span id="page-12-0"></span>Tom B. Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, Sandhini Agarwal, Ariel Herbert-Voss, Gretchen Krueger, Tom Henighan, Rewon Child, Aditya Ramesh, Daniel M. Ziegler, Jeffrey Wu, Clemens Winter, Christopher Hesse, Mark Chen, Eric Sigler, Mateusz Litwin, Scott Gray, Benjamin Chess, Jack Clark, Christopher Berner, Sam McCandlish, Alec Radford, Ilya Sutskever, and Dario Amodei. Language Models are Few-Shot Learners. *arXiv:2005.14165 [cs]*, July 2020. URL <http://arxiv.org/abs/2005.14165>.
- <span id="page-12-7"></span>Jian-Feng Cai, Emmanuel J Candes, and Zuowei Shen. A singular value thresholding algorithm for ` matrix completion. *SIAM Journal on optimization*, 20(4):1956–1982, 2010.
- <span id="page-12-14"></span>Daniel Cer, Mona Diab, Eneko Agirre, Inigo Lopez-Gazpio, and Lucia Specia. Semeval-2017 task 1: Semantic textual similarity multilingual and crosslingual focused evaluation. *Proceedings of the 11th International Workshop on Semantic Evaluation (SemEval-2017)*, 2017. doi: 10.18653/ v1/s17-2001. URL <http://dx.doi.org/10.18653/v1/S17-2001>.
- <span id="page-12-6"></span>Ronan Collobert and Jason Weston. A unified architecture for natural language processing: deep neural networks with multitask learning. In *Proceedings of the 25th international conference on Machine learning*, ICML '08, pp. 160–167, New York, NY, USA, July 2008. Association for Computing Machinery. ISBN 978-1-60558-205-4. doi: 10.1145/1390156.1390177. URL <https://doi.org/10.1145/1390156.1390177>.
- <span id="page-12-8"></span>Misha Denil, Babak Shakibi, Laurent Dinh, Marc'Aurelio Ranzato, and Nando de Freitas. Predicting parameters in deep learning, 2014.
- <span id="page-12-3"></span>Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding, 2019a.
- <span id="page-12-5"></span>Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding. *arXiv:1810.04805 [cs]*, May 2019b. URL <http://arxiv.org/abs/1810.04805>. arXiv: 1810.04805.
- <span id="page-12-13"></span>William B. Dolan and Chris Brockett. Automatically constructing a corpus of sentential paraphrases. In *Proceedings of the Third International Workshop on Paraphrasing (IWP2005)*, 2005. URL <https://aclanthology.org/I05-5002>.
- <span id="page-12-4"></span>Claire Gardent, Anastasia Shimorina, Shashi Narayan, and Laura Perez-Beltrachini. The webnlg challenge: Generating text from rdf data. In *Proceedings of the 10th International Conference on Natural Language Generation*, pp. 124–133, 2017.

- <span id="page-13-16"></span>Behrooz Ghorbani, Song Mei, Theodor Misiakiewicz, and Andrea Montanari. When do neural networks outperform kernel methods? *arXiv preprint arXiv:2006.13409*, 2020.
- <span id="page-13-9"></span>Bogdan Gliwa, Iwona Mochol, Maciej Biesek, and Aleksander Wawer. Samsum corpus: A humanannotated dialogue dataset for abstractive summarization. *CoRR*, abs/1911.12237, 2019. URL <http://arxiv.org/abs/1911.12237>.
- <span id="page-13-12"></span>Lars Grasedyck, Daniel Kressner, and Christine Tobler. A literature survey of low-rank tensor approximation techniques. *GAMM-Mitteilungen*, 36(1):53–78, 2013.
- <span id="page-13-17"></span>Jihun Ham and Daniel D. Lee. Grassmann discriminant analysis: a unifying view on subspace-based learning. In *ICML*, pp. 376–383, 2008. URL [https://doi.org/10.1145/1390156.](https://doi.org/10.1145/1390156.1390204) [1390204](https://doi.org/10.1145/1390156.1390204).
- <span id="page-13-3"></span>Karen Hambardzumyan, Hrant Khachatrian, and Jonathan May. WARP: Word-level Adversarial ReProgramming. *arXiv:2101.00121 [cs]*, December 2020. URL [http://arxiv.org/abs/](http://arxiv.org/abs/2101.00121) [2101.00121](http://arxiv.org/abs/2101.00121). arXiv: 2101.00121.
- <span id="page-13-8"></span>Pengcheng He, Xiaodong Liu, Jianfeng Gao, and Weizhu Chen. Deberta: Decoding-enhanced bert with disentangled attention, 2021.
- <span id="page-13-0"></span>Neil Houlsby, Andrei Giurgiu, Stanislaw Jastrzebski, Bruna Morrone, Quentin de Laroussilhe, Andrea Gesmundo, Mona Attariyan, and Sylvain Gelly. Parameter-Efficient Transfer Learning for NLP. *arXiv:1902.00751 [cs, stat]*, June 2019. URL [http://arxiv.org/abs/1902.](http://arxiv.org/abs/1902.00751) [00751](http://arxiv.org/abs/1902.00751).
- <span id="page-13-13"></span>Max Jaderberg, Andrea Vedaldi, and Andrew Zisserman. Speeding up convolutional neural networks with low rank expansions. *arXiv preprint arXiv:1405.3866*, 2014.
- <span id="page-13-14"></span>Mikhail Khodak, Neil Tenenholtz, Lester Mackey, and Nicolo Fusi. Initialization and regularization ` of factorized neural layers, 2021.
- <span id="page-13-5"></span>Diederik P. Kingma and Jimmy Ba. Adam: A method for stochastic optimization, 2017.
- <span id="page-13-7"></span>Dmitry Lepikhin, HyoukJoong Lee, Yuanzhong Xu, Dehao Chen, Orhan Firat, Yanping Huang, Maxim Krikun, Noam Shazeer, and Zhifeng Chen. Gshard: Scaling giant models with conditional computation and automatic sharding, 2020.
- <span id="page-13-2"></span>Brian Lester, Rami Al-Rfou, and Noah Constant. The Power of Scale for Parameter-Efficient Prompt Tuning. *arXiv:2104.08691 [cs]*, April 2021. URL <http://arxiv.org/abs/2104.08691>. arXiv: 2104.08691.
- <span id="page-13-4"></span>Chunyuan Li, Heerad Farkhoor, Rosanne Liu, and Jason Yosinski. Measuring the Intrinsic Dimension of Objective Landscapes. *arXiv:1804.08838 [cs, stat]*, April 2018a. URL [http:](http://arxiv.org/abs/1804.08838) [//arxiv.org/abs/1804.08838](http://arxiv.org/abs/1804.08838). arXiv: 1804.08838.
- <span id="page-13-1"></span>Xiang Lisa Li and Percy Liang. Prefix-Tuning: Optimizing Continuous Prompts for Generation. *arXiv:2101.00190 [cs]*, January 2021. URL <http://arxiv.org/abs/2101.00190>.
- <span id="page-13-15"></span>Yuanzhi Li and Yingyu Liang. Learning overparameterized neural networks via stochastic gradient descent on structured data. In *Advances in Neural Information Processing Systems*, 2018.
- <span id="page-13-10"></span>Yuanzhi Li, Yingyu Liang, and Andrej Risteski. Recovery guarantee of weighted low-rank approximation via alternating minimization. In *International Conference on Machine Learning*, pp. 2358–2367. PMLR, 2016.
- <span id="page-13-11"></span>Yuanzhi Li, Tengyu Ma, and Hongyang Zhang. Algorithmic regularization in over-parameterized matrix sensing and neural networks with quadratic activations. In *Conference On Learning Theory*, pp. 2–47. PMLR, 2018b.
- <span id="page-13-6"></span>Zhaojiang Lin, Andrea Madotto, and Pascale Fung. Exploring versatile generative language model via parameter-efficient transfer learning. In *Findings of the Association for Computational Linguistics: EMNLP 2020*, pp. 441–459, Online, November 2020. Association for Computational Linguistics. doi: 10.18653/v1/2020.findings-emnlp.41. URL [https://aclanthology.](https://aclanthology.org/2020.findings-emnlp.41) [org/2020.findings-emnlp.41](https://aclanthology.org/2020.findings-emnlp.41).

- <span id="page-14-3"></span>Xiao Liu, Yanan Zheng, Zhengxiao Du, Ming Ding, Yujie Qian, Zhilin Yang, and Jie Tang. GPT Understands, Too. *arXiv:2103.10385 [cs]*, March 2021. URL [http://arxiv.org/abs/](http://arxiv.org/abs/2103.10385) [2103.10385](http://arxiv.org/abs/2103.10385). arXiv: 2103.10385.
- <span id="page-14-1"></span>Yinhan Liu, Myle Ott, Naman Goyal, Jingfei Du, Mandar Joshi, Danqi Chen, Omer Levy, Mike Lewis, Luke Zettlemoyer, and Veselin Stoyanov. Roberta: A robustly optimized bert pretraining approach, 2019.
- <span id="page-14-17"></span>Ilya Loshchilov and Frank Hutter. Decoupled weight decay regularization. *arXiv preprint arXiv:1711.05101*, 2017.
- <span id="page-14-4"></span>Ilya Loshchilov and Frank Hutter. Decoupled weight decay regularization, 2019.
- <span id="page-14-10"></span>Rabeeh Karimi Mahabadi, James Henderson, and Sebastian Ruder. Compacter: Efficient low-rank hypercomplex adapter layers, 2021.
- <span id="page-14-8"></span>Linyong Nan, Dragomir Radev, Rui Zhang, Amrit Rau, Abhinand Sivaprasad, Chiachun Hsieh, Xiangru Tang, Aadit Vyas, Neha Verma, Pranav Krishna, et al. Dart: Open-domain structured data record to text generation. *arXiv preprint arXiv:2007.02871*, 2020.
- <span id="page-14-16"></span>Jekaterina Novikova, Ondřej Dušek, and Verena Rieser. The e2e dataset: New challenges for endto-end generation. *arXiv preprint arXiv:1706.09254*, 2017.
- <span id="page-14-11"></span>Samet Oymak, Zalan Fabian, Mingchen Li, and Mahdi Soltanolkotabi. Generalization guarantees for neural networks via harnessing the low-rank structure of the jacobian. *arXiv preprint arXiv:1906.05392*, 2019.
- <span id="page-14-5"></span>Jonas Pfeiffer, Aishwarya Kamath, Andreas Rücklé, Kyunghyun Cho, and Iryna Gurevych. Adapterfusion: Non-destructive task composition for transfer learning, 2021.
- <span id="page-14-13"></span>Daniel Povey, Gaofeng Cheng, Yiming Wang, Ke Li, Hainan Xu, Mahsa Yarmohammadi, and Sanjeev Khudanpur. Semi-orthogonal low-rank matrix factorization for deep neural networks. In *Interspeech*, pp. 3743–3747, 2018.
- <span id="page-14-9"></span>Alec Radford, Karthik Narasimhan, Tim Salimans, and Ilya Sutskever. Improving Language Understanding by Generative Pre-Training. pp. 12, a.
- <span id="page-14-0"></span>Alec Radford, Jeffrey Wu, Rewon Child, David Luan, Dario Amodei, and Ilya Sutskever. Language Models are Unsupervised Multitask Learners. pp. 24, b.
- <span id="page-14-15"></span>Pranav Rajpurkar, Robin Jia, and Percy Liang. Know what you don't know: Unanswerable questions for squad. *CoRR*, abs/1806.03822, 2018. URL <http://arxiv.org/abs/1806.03822>.
- <span id="page-14-2"></span>Sylvestre-Alvise Rebuffi, Hakan Bilen, and Andrea Vedaldi. Learning multiple visual domains with residual adapters. *arXiv:1705.08045 [cs, stat]*, November 2017. URL [http://arxiv.org/](http://arxiv.org/abs/1705.08045) [abs/1705.08045](http://arxiv.org/abs/1705.08045). arXiv: 1705.08045.
- <span id="page-14-6"></span>Andreas Rücklé, Gregor Geigle, Max Glockner, Tilman Beck, Jonas Pfeiffer, Nils Reimers, and Iryna Gurevych. Adapterdrop: On the efficiency of adapters in transformers, 2020.
- <span id="page-14-12"></span>Tara N Sainath, Brian Kingsbury, Vikas Sindhwani, Ebru Arisoy, and Bhuvana Ramabhadran. Lowrank matrix factorization for deep neural network training with high-dimensional output targets. In *2013 IEEE international conference on acoustics, speech and signal processing*, pp. 6655– 6659. IEEE, 2013.
- <span id="page-14-7"></span>Mohammad Shoeybi, Mostofa Patwary, Raul Puri, Patrick LeGresley, Jared Casper, and Bryan Catanzaro. Megatron-lm: Training multi-billion parameter language models using model parallelism, 2020.
- <span id="page-14-14"></span>Richard Socher, Alex Perelygin, Jean Wu, Jason Chuang, Christopher D. Manning, Andrew Ng, and Christopher Potts. Recursive deep models for semantic compositionality over a sentiment treebank. In *Proceedings of the 2013 Conference on Empirical Methods in Natural Language Processing*, pp. 1631–1642, Seattle, Washington, USA, October 2013. Association for Computational Linguistics. URL <https://aclanthology.org/D13-1170>.

- <span id="page-15-1"></span>Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez, Łukasz Kaiser, and Illia Polosukhin. Attention is all you need. In *Proceedings of the 31st International Conference on Neural Information Processing Systems*, pp. 6000–6010, 2017.
- <span id="page-15-3"></span>Alex Wang, Amanpreet Singh, Julian Michael, Felix Hill, Omer Levy, and Samuel R. Bowman. Glue: A multi-task benchmark and analysis platform for natural language understanding, 2019.
- <span id="page-15-7"></span>Alex Wang, Yada Pruksachatkun, Nikita Nangia, Amanpreet Singh, Julian Michael, Felix Hill, Omer Levy, and Samuel R. Bowman. Superglue: A stickier benchmark for general-purpose language understanding systems, 2020.
- <span id="page-15-11"></span>Alex Warstadt, Amanpreet Singh, and Samuel R Bowman. Neural network acceptability judgments. *arXiv preprint arXiv:1805.12471*, 2018.
- <span id="page-15-10"></span>Adina Williams, Nikita Nangia, and Samuel Bowman. A broad-coverage challenge corpus for sentence understanding through inference. In *Proceedings of the 2018 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long Papers)*, pp. 1112–1122, New Orleans, Louisiana, June 2018. Association for Computational Linguistics. doi: 10.18653/v1/N18-1101. URL [https://www.aclweb.](https://www.aclweb.org/anthology/N18-1101) [org/anthology/N18-1101](https://www.aclweb.org/anthology/N18-1101).
- <span id="page-15-6"></span>Thomas Wolf, Lysandre Debut, Victor Sanh, Julien Chaumond, Clement Delangue, Anthony Moi, Pierric Cistac, Tim Rault, Remi Louf, Morgan Funtowicz, Joe Davison, Sam Shleifer, Patrick ´ von Platen, Clara Ma, Yacine Jernite, Julien Plu, Canwen Xu, Teven Le Scao, Sylvain Gugger, Mariama Drame, Quentin Lhoest, and Alexander M. Rush. Transformers: State-of-the-art natural language processing. In *Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing: System Demonstrations*, pp. 38–45, Online, October 2020. Association for Computational Linguistics. URL [https://www.aclweb.org/anthology/](https://www.aclweb.org/anthology/2020.emnlp-demos.6) [2020.emnlp-demos.6](https://www.aclweb.org/anthology/2020.emnlp-demos.6).
- <span id="page-15-2"></span>Greg Yang and Edward J. Hu. Feature Learning in Infinite-Width Neural Networks. *arXiv:2011.14522 [cond-mat]*, May 2021. URL <http://arxiv.org/abs/2011.14522>. arXiv: 2011.14522.
- <span id="page-15-5"></span>Elad Ben Zaken, Shauli Ravfogel, and Yoav Goldberg. Bitfit: Simple parameter-efficient fine-tuning for transformer-based masked language-models, 2021.
- <span id="page-15-8"></span>Yu Zhang, Ekapol Chuangsuwanich, and James Glass. Extracting deep neural network bottleneck features using low-rank matrix factorization. In *2014 IEEE international conference on acoustics, speech and signal processing (ICASSP)*, pp. 185–189. IEEE, 2014.
- <span id="page-15-9"></span>Yong Zhao, Jinyu Li, and Yifan Gong. Low-rank plus diagonal adaptation for deep neural networks. In *2016 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)*, pp. 5005–5009. IEEE, 2016.
- <span id="page-15-4"></span>Victor Zhong, Caiming Xiong, and Richard Socher. Seq2sql: Generating structured queries from natural language using reinforcement learning. *CoRR*, abs/1709.00103, 2017. URL [http://](http://arxiv.org/abs/1709.00103) [arxiv.org/abs/1709.00103](http://arxiv.org/abs/1709.00103).

<span id="page-15-0"></span>

## A LARGE LANGUAGE MODELS STILL NEED PARAMETER UPDATES

Few-shot learning, or prompt engineering, is very advantageous when we only have a handful of training samples. However, in practice, we can often afford to curate a few thousand or more training examples for performance-sensitive applications. As shown in [Table 8,](#page-16-2) fine-tuning improves the model performance drastically compared to few-shot learning on datasets large and small. We take the GPT-3 few-shot result on RTE from the GPT-3 paper [\(Brown et al., 2020\)](#page-12-0). For MNLI-matched, we use two demonstrations per class and six in-context examples in total.

<span id="page-16-2"></span>

| Method           | MNLI-m (Val. Acc./%) | RTE (Val. Acc./%) |
|------------------|----------------------|-------------------|
| GPT-3 Few-Shot   | 40.6                 | 69.0              |
| GPT-3 Fine-Tuned | 89.5                 | 85.4              |

Table 8: Fine-tuning significantly outperforms few-shot learning on GPT-3 [\(Brown et al., 2020\)](#page-12-0).

<span id="page-16-0"></span>

## B INFERENCE LATENCY INTRODUCED BY ADAPTER LAYERS

Adapter layers are external modules added to a pre-trained model in a *sequential* manner, whereas our proposal, LoRA, can be seen as external modules added in a parallel manner. Consequently, adapter layers must be computed in addition to the base model, inevitably introducing additional latency. While as pointed out in Rücklé et al. [\(2020\)](#page-14-6), the latency introduced by adapter layers can be mitigated when the model batch size and/or sequence length is large enough to full utilize the hardware parallelism. We confirm their observation with a similar latency study on GPT-2 medium and point out that there are scenarios, notably online inference where the batch size is small, where the added latency can be significant.

We measure the latency of a single forward pass on an NVIDIA Quadro RTX8000 by averaging over 100 trials. We vary the input batch size, sequence length, and the adapter bottleneck dimension r. We test two adapter designs: the original one by [Houlsby et al.](#page-13-0) [\(2019\)](#page-13-0), which we call Adapter<sup>H</sup>, and a recent, more efficient variant by [Lin et al.](#page-13-6) [\(2020\)](#page-13-6), which we call Adapter<sup>L</sup>. See [Section 5.1](#page-4-1) for more details on the designs. We plot the slow-down in percentage compared to the no-adapter baseline in [Figure 5.](#page-16-3)

<span id="page-16-3"></span>Image /page/16/Figure/5 description: This image displays a heatmap visualization of experimental results, likely related to machine learning model performance. The figure is divided into two main rows, labeled "AdapterH r" and "AdapterL r", and three columns, labeled "Seq Len = 128", "Seq Len = 256", and "Seq Len = 512". Within each of the six subplots, the x-axis represents "Batch Size" with values ranging from 1 to 32, and the y-axis represents adapter sizes with values 0, 10, 100, and 250. A color bar on the right indicates a scale from 0 to 35, with warmer colors (orange/red) representing higher values and cooler colors (dark purple/black) representing lower values. The top row, "AdapterH r", shows a gradient of increasing values from left to right and top to bottom for Seq Len = 128, with the highest values concentrated in the bottom left corner. For Seq Len = 256 and 512, the values are generally lower and more uniform across the batch sizes and adapter sizes. The bottom row, "AdapterL r", shows a similar pattern for Seq Len = 128, with high values in the bottom left. For Seq Len = 256 and 512, the values are predominantly low, with a few cells showing slightly higher values at smaller batch sizes and adapter sizes.

Figure 5: Percentage slow-down of inference latency compared to the no-adapter  $(r = 0)$  baseline. The top row shows the result for Adapter<sup>H</sup> and the bottom row Adapter<sup>L</sup>. Larger batch size and sequence length help to mitigate the latency, but the slow-down can be as high as over 30% in an online, short-sequence-length scenario. We tweak the colormap for better visibility.

<span id="page-16-1"></span>

## C DATASET DETAILS

GLUE Benchmark is a wide-ranging collection of natural language understanding tasks. It includes MNLI (inference, [Williams et al.](#page-15-10) [\(2018\)](#page-15-10)), SST-2 (sentiment analysis, [Socher et al.](#page-14-14) [\(2013\)](#page-14-14)), MRPC (paraphrase detection, [Dolan & Brockett](#page-12-13) [\(2005\)](#page-12-13)), CoLA (linguistic acceptability, [Warstadt et al.](#page-15-11) [\(2018\)](#page-14-15)), QNLI (inference, [Rajpurkar et al.](#page-14-15) (201[8](#page-16-4))), QQP<sup>8</sup> (question-answering), RTE (inference),

<span id="page-16-4"></span><sup>8</sup> https://quoradata.quora.com/First-Quora-Dataset-Release-Question-Pairs

and STS-B (textual similarity, [Cer et al.](#page-12-14) [\(2017\)](#page-12-14)). The broad coverage makes GLUE benchmark a standard metric to evaluate NLU models such as RoBERTa and DeBERTa. The individual datasets are released under different permissive licenses.

WikiSQL is introduced in [Zhong et al.](#page-15-4) [\(2017\)](#page-15-4) and contains 56, 355/8, 421 training/validation examples. The task is to generate SQL queries from natural language questions and table schemata. We encode context as  $x = \{\text{table schema}, \text{query}\}$  and target as  $y = \{\text{SQL}\}\$ . The dataset is release under the BSD 3-Clause License.

SAMSum is introduced in [Gliwa et al.](#page-13-9) [\(2019\)](#page-13-9) and contains 14, 732/819 training/test examples. It consists of staged chat conversations between two people and corresponding abstractive summaries written by linguists. We encode context as "\n" concatenated utterances followed by a "\n\n", and target as  $y = \{$ summary $\}$ . The dataset is released under the non-commercial licence: Creative Commons BY-NC-ND 4.0.

E2E NLG Challenge was first introduced in [Novikova et al.](#page-14-16) [\(2017\)](#page-14-16) as a dataset for training end-toend, data-driven natural language generation systems and is commonly used for data-to-text evaluation. The E2E dataset consists of roughly 42, 000 training, 4, 600 validation, and 4, 600 test examples from the restaurant domain. Each source table used as input can have multiple references. Each sample input  $(x, y)$  consists of a sequence of slot-value pairs, along with a corresponding natural language reference text. The dataset is released under Creative Commons BY-NC-SA 4.0.

DART is an open-domain data-to-text dataset described in [Nan et al.](#page-14-8) [\(2020\)](#page-14-8). DART inputs are structured as sequences of ENTITY — RELATION — ENTITY triples. With  $82K$  examples in total, DART is a significantly larger and more complex data-to-text task compared to E2E. The dataset is released under the MIT license.

WebNLG is another commonly used dataset for data-to-text evaluation [\(Gardent et al., 2017\)](#page-12-4). With  $22K$  examples in total WebNLG comprises 14 distinct categories, nine of which are seen during training. Since five of the 14 total categories are not seen during training, but are represented in the test set, evaluation is typically broken out by "seen" categories (S), "unseen" categories (U) and "all" (A). Each input example is represented by a sequence of SUBJECT — PROPERTY — OBJECT triples. The dataset is released under Creative Commons BY-NC-SA 4.0.

## D HYPERPARAMETERS USED IN EXPERIMENTS

<span id="page-17-0"></span>

### D.1 ROBERTA

We train using AdamW with a linear learning rate decay schedule. We sweep learning rate, number of training epochs, and batch size for LoRA. Following [Liu et al.](#page-14-1) [\(2019\)](#page-14-1), we initialize the LoRA modules to our best MNLI checkpoint when adapting to MRPC, RTE, and STS-B, instead of the usual initialization; the pre-trained model stays frozen for all tasks. We report the median over 5 random seeds; the result for each run is taken from the best epoch. For a fair comparison with the setup in [Houlsby et al.](#page-13-0) [\(2019\)](#page-13-0) and [Pfeiffer et al.](#page-14-5) [\(2021\)](#page-14-5), we restrict the model sequence length to 128 and used a fixed batch size for all tasks. Importantly, we start with the pre-trained RoBERTa large model when adapting to MRPC, RTE, and STS-B, instead of a model already adapted to MNLI. The runs with this restricted setup are marked with  $\dagger$ . See the hyperparameters used in our runs in [Table 9.](#page-18-2)

<span id="page-17-1"></span>

## D.2 DEBERTA

We again train using AdamW with a linear learning rate decay schedule. Following [He et al.](#page-13-8) [\(2021\)](#page-13-8), we tune learning rate, dropout probability, warm-up steps, and batch size. We use the same model sequence length used by [\(He et al., 2021\)](#page-13-8) to keep our comparison fair. Following [He et al.](#page-13-8) [\(2021\)](#page-13-8), we initialize the LoRA modules to our best MNLI checkpoint when adapting to MRPC, RTE, and STS-B, instead of the usual initialization; the pre-trained model stays frozen for all tasks. We report the median over 5 random seeds; the result for each run is taken from the best epoch. See the hyperparameters used in our runs in [Table 10.](#page-19-1)

<span id="page-18-2"></span>

| Method                         | Dataset                                                                                          | <b>MNLI</b>             | <b>SST-2</b>            | <b>MRPC</b>             | <b>CoLA</b>                                      | <b>QNLI</b>              | <b>QQP</b>              | <b>RTE</b>              | <b>STS-B</b>            |
|--------------------------------|--------------------------------------------------------------------------------------------------|-------------------------|-------------------------|-------------------------|--------------------------------------------------|--------------------------|-------------------------|-------------------------|-------------------------|
|                                | Optimizer<br>Warmup Ratio<br><b>LR Schedule</b>                                                  |                         |                         |                         | AdamW<br>0.06<br>Linear                          |                          |                         |                         |                         |
| RoBERTa base<br>LoRA           | <b>Batch Size</b><br># Epochs<br>Learning Rate<br>LoRA Config.<br>LoRA $\alpha$<br>Max Seq. Len. | 16<br>30<br>5E-04       | 16<br>60<br>5E-04       | 16<br>30<br>4E-04       | 32<br>80<br>4E-04<br>$r_q = r_v = 8$<br>512      | 32<br>25<br>4E-04<br>8   | 16<br>25<br>5E-04       | 32<br>80<br>5E-04       | 16<br>40<br>4E-04       |
| RoBERTa large<br>LoRA          | <b>Batch Size</b><br># Epochs<br>Learning Rate<br>LoRA Config.<br>LoRA $\alpha$<br>Max Seq. Len. | 4<br>10<br>3E-04<br>128 | 4<br>10<br>4E-04<br>128 | 4<br>20<br>3E-04<br>512 | 4<br>20<br>2E-04<br>$r_q = r_v = 8$<br>16<br>128 | 4<br>10<br>2E-04<br>512  | 4<br>20<br>3E-04<br>512 | 8<br>20<br>4E-04<br>512 | 8<br>30<br>2E-04<br>512 |
| RoBERTa large<br>LoRA+         | <b>Batch Size</b><br># Epochs<br>Learning Rate<br>LoRA Config.<br>LoRA $\alpha$<br>Max Seq. Len. | 10<br>3E-04             | 10<br>4E-04             | 20<br>3E-04             | 4<br>20<br>2E-04<br>$r_q = r_v = 8$              | 10<br>2E-04<br>16<br>128 | 20<br>3E-04             | 20<br>4E-04             | 10<br>2E-04             |
| RoBERTa large<br>AdptP (3M)+   | <b>Batch Size</b><br># Epochs<br>Learning Rate<br>Bottleneck r<br>Max Seq. Len.                  | 10<br>3E-05             | 20<br>3E-05             | 20<br>3E-04             | 32<br>20<br>3E-04<br>64<br>128                   | 10<br>3E-04              | 20<br>3E-04             | 20<br>3E-04             | 20<br>3E-04             |
| RoBERTa large<br>AdptP (0.8M)+ | <b>Batch Size</b><br># Epochs<br>Learning Rate<br>Bottleneck r<br>Max Seq. Len.                  | 5<br>3E-04              | 20<br>3E-04             | 20<br>3E-04             | 32<br>20<br>3E-04<br>16<br>128                   | 10<br>3E-04              | 20<br>3E-04             | 20<br>3E-04             | 20<br>3E-04             |
| RoBERTa large<br>AdptH (6M)+   | <b>Batch Size</b><br># Epochs<br>Learning Rate<br>Bottleneck r<br>Max Seq. Len.                  | 10<br>3E-05             | 5<br>3E-04              | 10<br>3E-04             | 32<br>10<br>3E-04<br>64<br>128                   | 5<br>3E-04               | 20<br>3E-04             | 20<br>3E-04             | 10<br>3E-04             |
| RoBERTa large<br>AdptH (0.8M)+ | <b>Batch Size</b><br># Epochs<br>Learning Rate<br>Bottleneck r<br>Max Seq. Len.                  | 10<br>3E-04             | 5<br>3E-04              | 10<br>3E-04             | 10<br>3E-04<br>8<br>128                          | 32<br>5<br>3E-04         | 20<br>3E-04             | 20<br>3E-04             | 10<br>3E-04             |

Table 9: The hyperparameters we used for RoBERTa on the GLUE benchmark.

<span id="page-18-0"></span>

### D.3 GPT-2

We train all of our GPT-2 models using AdamW [\(Loshchilov & Hutter, 2017\)](#page-14-17) with a linear learning rate schedule for 5 epochs. We use the batch size, learning rate, and beam search beam size described in [Li & Liang](#page-13-1) [\(2021\)](#page-13-1). Accordingly, we also tune the above hyperparameters for LoRA. We report the mean over 3 random seeds; the result for each run is taken from the best epoch. The hyperparameters used for LoRA in GPT-2 are listed in [Table 11.](#page-19-2) For those used for other baselines, see [Li & Liang](#page-13-1) [\(2021\)](#page-13-1).

<span id="page-18-1"></span>

### D.4 GPT-3

For all GPT-3 experiments, we train using AdamW [\(Loshchilov & Hutter, 2017\)](#page-14-17) for 2 epochs with a batch size of 128 samples and a weight decay factor of 0.1. We use a sequence length of 384 for

<span id="page-19-1"></span>

| Method              | <b>Dataset</b>                                                                                                                           | <b>MNLI</b>                                | $SST-2$                                             | MRPC                                           | CoLA                                                                | ONLI                                    | QQP                                      | <b>RTE</b>                               | STS-B                                 |
|---------------------|------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------|-----------------------------------------------------|------------------------------------------------|---------------------------------------------------------------------|-----------------------------------------|------------------------------------------|------------------------------------------|---------------------------------------|
|                     | Optimizer<br>AdamW<br>Warmup Ratio<br>0.1<br>Linear<br>LR Schedule                                                                       |                                            |                                                     |                                                |                                                                     |                                         |                                          |                                          |                                       |
| DeBERTa XXL<br>LoRA | <b>Batch Size</b><br>$#$ Epochs<br>Learning Rate<br>Weight Decay<br><b>CLS</b> Dropout<br>LoRA Config.<br>LoRA $\alpha$<br>Max Seq. Len. | 8<br>5<br>1E-04<br>$\Omega$<br>0.15<br>256 | 8<br>16<br>6E-05<br>0.01<br>$\boldsymbol{0}$<br>128 | 32<br>30<br>$2E-04$<br>0.01<br>$\Omega$<br>128 | 4<br>10<br>$1E-04$<br>$\Omega$<br>0.1<br>$r_q = r_v = 8$<br>8<br>64 | 6<br>8<br>$1E-04$<br>0.01<br>0.1<br>512 | 8<br>11<br>$1E-04$<br>0.01<br>0.2<br>320 | 4<br>11<br>$2E-04$<br>0.01<br>0.2<br>320 | 4<br>10<br>2E-04<br>0.1<br>0.2<br>128 |

<span id="page-19-2"></span>

|  |  |  | Table 10: The hyperparameters for DeBERTa XXL on tasks included in the GLUE benchmark. |  |  |  |  |  |  |  |  |  |  |  |  |
|--|--|--|----------------------------------------------------------------------------------------|--|--|--|--|--|--|--|--|--|--|--|--|
|--|--|--|----------------------------------------------------------------------------------------|--|--|--|--|--|--|--|--|--|--|--|--|

| Dataset                | E2E  | WebNLG          | DART |
|------------------------|------|-----------------|------|
|                        |      | Training        |      |
| Optimizer              |      | AdamW           |      |
| Weight Decay           | 0.01 | 0.01            | 0.0  |
| Dropout Prob           | 0.1  | 0.1             | 0.0  |
| Batch Size             |      | 8               |      |
| # Epoch                |      | 5               |      |
| Warmup Steps           |      | 500             |      |
| Learning Rate Schedule |      | Linear          |      |
| Label Smooth           | 0.1  | 0.1             | 0.0  |
| Learning Rate          |      | 0.0002          |      |
| Adaptation             |      | $r_q = r_v = 4$ |      |
| LoRA $\alpha$          |      | 32              |      |
|                        |      | Inference       |      |
| Beam Size              |      | 10              |      |
| Length Penalty         | 0.9  | 0.8             | 0.8  |
| no repeat ngram size   |      | 4               |      |

Table 11: The hyperparameters for GPT-2 LoRA on E2E, WebNLG and DART.

WikiSQL [\(Zhong et al., 2017\)](#page-15-4), 768 for MNLI [\(Williams et al., 2018\)](#page-15-10), and 2048 for SAMSum [\(Gliwa](#page-13-9) [et al., 2019\)](#page-13-9). We tune learning rate for all method-dataset combinations. See [Section D.4](#page-18-1) for more details on the hyperparameters used. For prefix-embedding tuning, we find the optimal  $l_p$  and  $l_i$ to be 256 and 8, respectively, totalling 3.2M trainable parameters. We use  $l_p = 8$  and  $l_i = 8$  for prefix-layer tuning with 20.2M trainable parameters to obtain the overall best performance. We present two parameter budgets for LoRA: 4.7M ( $r_q = r_v = 1$  or  $r_v = 2$ ) and 37.7M ( $r_q = r_v = 8$ ) or  $r_q = r_k = r_v = r_o = 2$ ). We report the best validation performance from each run. The training hyperparameters used in our GPT-3 experiments are listed in [Table 12.](#page-20-1)

<span id="page-19-0"></span>

## E COMBINING LORA WITH PREFIX TUNING

LoRA can be naturally combined with existing prefix-based approaches. In this section, we evaluate two combinations of LoRA and variants of prefix-tuning on WikiSQL and MNLI.

LoRA+PrefixEmbed (LoRA+PE) combines LoRA with prefix-embedding tuning, where we insert  $l_p + l_i$  special tokens whose embeddings are treated as trainable parameters. For more on prefixembedding tuning, see [Section 5.1.](#page-4-1)

**LoRA+PrefixLayer (LoRA+PL)** combines LoRA with prefix-layer tuning. We also insert  $l_p + l_i$ special tokens; however, instead of letting the hidden representations of these tokens evolve natu-

<span id="page-20-1"></span>

| Hyperparameters | Fine-Tune | PreEmbed | PreLayer | BitFit    | AdapterH | LoRA     |
|-----------------|-----------|----------|----------|-----------|----------|----------|
| Optimizer       |           |          | AdamW    |           |          |          |
| Batch Size      |           |          | 128      |           |          |          |
| $#$ Epoch       |           |          | 2        |           |          |          |
| Warmup Tokens   |           |          | 250,000  |           |          |          |
| LR Schedule     |           |          | Linear   |           |          |          |
| Learning Rate   | 5.00E-06  | 5.00E-04 | 1.00E-04 | $1.6E-03$ | 1.00E-04 | 2.00E-04 |

Table 12: The training hyperparameters used for different GPT-3 adaption methods. We use the same hyperparameters for all datasets after tuning learning rate.

rally, we replace them after every Transformer block with an input agnostic vector. Thus, both the embeddings and subsequent Transformer block activations are treated as trainable parameters. For more on prefix-layer tuning, see [Section 5.1.](#page-4-1)

In [Table 15,](#page-22-0) we show the evaluation results of LoRA+PE and LoRA+PL on WikiSQL and MultiNLI. First of all, LoRA+PE significantly outperforms both LoRA and prefix-embedding tuning on WikiSQL, which indicates that LoRA is somewhat orthogonal to prefix-embedding tuning. On MultiNLI, the combination of LoRA+PE doesn't perform better than LoRA, possibly because LoRA on its own already achieves performance comparable to the human baseline. Secondly, we notice that LoRA+PL performs slightly worse than LoRA even with more trainable parameters. We attribute this to the fact that prefix-layer tuning is very sensitive to the choice of learning rate and thus makes the optimization of LoRA weights more difficult in LoRA+PL.

## F ADDITIONAL EMPIRICAL EXPERIMENTS

<span id="page-20-0"></span>

### F.1 ADDITIONAL EXPERIMENTS ON GPT-2

<span id="page-20-2"></span>We also repeat our experiment on DART [\(Nan et al., 2020\)](#page-14-8) and WebNLG [\(Gardent et al., 2017\)](#page-12-4) following the setup of [Li & Liang](#page-13-1) [\(2021\)](#page-13-1). The result is shown in [Table 13.](#page-20-2) Similar to our result on E2E NLG Challenge, reported in [Section 5,](#page-4-0) LoRA performs better than or at least on-par with prefix-based approaches given the same number of trainable parameters.

| Method       | # Trainable<br>Parameters |        |              |             |
|--------------|---------------------------|--------|--------------|-------------|
|              |                           | BLEU↑  | DART<br>MET↑ | TER↓        |
| GPT-2 Medium |                           |        |              |             |
| Fine-Tune    | 354M                      | 46.2   | <b>0.39</b>  | 0.46        |
| AdapterL     | 0.37M                     | 42.4   | 0.36         | 0.48        |
| AdapterL     | 11M                       | 45.2   | 0.38         | 0.46        |
| FTTop2       | 24M                       | 41.0   | 0.34         | 0.56        |
| PrefLayer    | 0.35M                     | 46.4   | 0.38         | 0.46        |
| LoRA         | 0.35M                     | 47.1±2 | <b>0.39</b>  | <b>0.46</b> |
| GPT-2 Large  |                           |        |              |             |
| Fine-Tune    | 774M                      | 47.0   | <b>0.39</b>  | 0.46        |
| AdapterL     | 0.88M                     | 45.7±1 | 0.38         | 0.46        |
| AdapterL     | 23M                       | 47.1±1 | <b>0.39</b>  | 0.45        |
| PrefLayer    | 0.77M                     | 46.7   | 0.38         | 0.45        |
| LoRA         | 0.77M                     | 47.5±1 | <b>0.39</b>  | 0.45        |

Table 13: GPT-2 with different adaptation methods on DART. The variances of MET and TER are less than 0.01 for all adaption approaches.

| Method           | BLEU↑   |         | WebNLG  |     |     |     |     |     |     |
|------------------|---------|---------|---------|-----|-----|-----|-----|-----|-----|
|                  | U       | S       | A       | U   | S   | A   | U   | S   | A   |
| GPT-2 Medium     |         |         |         |     |     |     |     |     |     |
| Fine-Tune (354M) | 27.7    | 64.2    | 46.5    | .30 | .45 | .38 | .76 | .33 | .53 |
| AdapterL (0.37M) | 45.1    | 54.5    | 50.2    | .36 | .39 | .38 | .46 | .40 | .43 |
| AdapterL (11M)   | 48.3    | 60.4    | 54.9    | .38 | .43 | .41 | .45 | .35 | .39 |
| FTTop2 (24M)     | 18.9    | 53.6    | 36.0    | .23 | .38 | .31 | .99 | .49 | .72 |
| Prefix (0.35M)   | 45.6    | 62.9    | 55.1    | .38 | .44 | .41 | .49 | .35 | .40 |
| LoRA (0.35M)     | 46.7±.4 | 62.1±.2 | 55.3±.2 | .38 | .44 | .41 | .46 | .33 | .39 |
| GPT-2 Large      |         |         |         |     |     |     |     |     |     |
| Fine-Tune (774M) | 43.1    | 65.3    | 55.5    | .38 | .46 | .42 | .53 | .33 | .42 |
| AdapterL (0.88M) | 49.8±.0 | 61.1±.0 | 56.0±.0 | .38 | .43 | .41 | .44 | .35 | .39 |
| AdapterL (23M)   | 49.2±.1 | 64.7±.2 | 57.7±.1 | .39 | .46 | .43 | .46 | .33 | .39 |
| Prefix (0.77M)   | 47.7    | 63.4    | 56.3    | .39 | .45 | .42 | .48 | .34 | .40 |
| LoRA (0.77M)     | 48.4±.3 | 64.0±.3 | 57.0±.1 | .39 | .45 | .42 | .45 | .32 | .38 |

Table 14: GPT-2 with different adaptation methods on WebNLG. The variances of MET and TER are less than 0.01 for all the experiments we ran. "U" indicates unseen categories, "S" indicates seen categories, and "A" indicates all categories in the test set of WebNLG.

<span id="page-21-1"></span>

### F.2 ADDITIONAL EXPERIMENTS ON GPT-3

We present additional runs on GPT-3 with different adaptation methods in [Table 15.](#page-22-0) The focus is on identifying the trade-off between performance and the number of trainable parameters.

<span id="page-21-0"></span>

### F.3 LOW-DATA REGIME

To evaluate the performance of different adaptation approaches in the low-data regime. we randomly sample 100, 1k and 10k training examples from the full training set of MNLI to form the low-data MNLI- $n$  tasks. In [Table 16,](#page-22-1) we show the performance of different adaptation approaches on MNLI $n$ . To our surprise, PrefixEmbed and PrefixLayer performs very poorly on MNLI-100 dataset, with PrefixEmbed performing only slightly better than random chance (37.6% vs. 33.3%). PrefixLayer performs better than PrefixEmbed but is still significantly worse than Fine-Tune or LoRA on MNLI-100. The gap between prefix-based approaches and LoRA/Fine-tuning becomes smaller as we increase the number of training examples, which might suggest that prefix-based approaches are not suitable for low-data tasks in GPT-3. LoRA achieves better performance than fine-tuning on both MNLI-100 and MNLI-Full, and comparable results on MNLI-1k and MNLI-10K considering the  $(\pm 0.3)$  variance due to random seeds.

The training hyperparameters of different adaptation approaches on MNLI-n are reported in [Ta](#page-23-3)[ble 17.](#page-23-3) We use a smaller learning rate for PrefixLayer on the MNLI-100 set, as the training loss does not decrease with a larger learning rate.

<span id="page-21-2"></span>

## G MEASURING SIMILARITY BETWEEN SUBSPACES

In this paper we use the measure  $\phi(A, B, i, j) = \psi(U_A^i, U_B^j) = \frac{\|U_A^{i \top} U_B\|_F^2}{\min\{i, j\}}$  to measure the subspace similarity between two column orthonormal matrices  $U_A^i \in \mathbb{R}^{d \times i}$  and  $U_B^j \in \mathbb{R}^{d \times j}$ , obtained by taking columns of the left singular matrices of  $A$  and  $B$ . We point out that this similarity is simply a reverse of the standard Projection Metric that measures distance between subspaces [Ham & Lee](#page-13-17) [\(2008\)](#page-13-17).

<span id="page-22-0"></span>

| Method      | Hyperparameters                    | # Trainable Parameters | WikiSQL | MNLI-m |
|-------------|------------------------------------|------------------------|---------|--------|
| Fine-Tune   |                                    | 175B                   | 73.8    | 89.5   |
|             | $l_p = 32, l_i = 8$                | 0.4 M                  | 55.9    | 84.9   |
|             | $l_p = 64, l_i = 8$                | 0.9 M                  | 58.7    | 88.1   |
| PrefixEmbed | $l_p = 128, l_i = 8$               | 1.7 M                  | 60.6    | 88.0   |
|             | $l_p = 256, l_i = 8$               | 3.2 M                  | 63.1    | 88.6   |
|             | $l_p = 512, l_i = 8$               | 6.4 M                  | 55.9    | 85.8   |
|             | $l_p = 2, l_i = 2$                 | 5.1 M                  | 68.5    | 89.2   |
|             | $l_p = 8, l_i = 0$                 | 10.1 M                 | 69.8    | 88.2   |
| PrefixLayer | $l_p = 8, l_i = 8$                 | 20.2 M                 | 70.1    | 89.5   |
|             | $l_p = 32, l_i = 4$                | 44.1 M                 | 66.4    | 89.6   |
|             | $l_p = 64, l_i = 0$                | 76.1 M                 | 64.9    | 87.9   |
|             | $r=1$                              | 7.1 M                  | 71.9    | 89.8   |
|             | $r=4$                              | 21.2 M                 | 73.2    | 91.0   |
| AdapterH    | $r = 8$                            | 40.1 M                 | 73.2    | 91.5   |
|             | $r=16$                             | 77.9 M                 | 73.2    | 91.5   |
|             | $r=64$                             | 304.4 M                | 72.6    | 91.5   |
|             | $r_v=2$                            | 4.7 M                  | 73.4    | 91.7   |
|             | $r_q = r_v = 1$                    | 4.7 M                  | 73.4    | 91.3   |
| LoRA        | $r_q = r_v = 2$                    | 9.4 M                  | 73.3    | 91.4   |
|             | $r_q = r_k = r_v = r_o = 1$        | 9.4 M                  | 74.1    | 91.2   |
|             | $r_q = r_v = 4$                    | 18.8 M                 | 73.7    | 91.3   |
|             | $r_q = r_k = r_v = r_o = 2$        | 18.8 M                 | 73.7    | 91.7   |
|             | $r_a = r_v = 8$                    | 37.7 M                 | 73.8    | 91.6   |
|             | $r_q = r_k = r_v = r_o = 4$        | 37.7 M                 | 74.0    | 91.7   |
|             | $r_q = r_v = 64$                   | 301.9 M                | 73.6    | 91.4   |
|             | $r_q = r_k = r_v = r_o = 64$       | 603.8 M                | 73.9    | 91.4   |
|             | $r_a = r_v = 8, l_p = 8, l_i = 4$  | 37.8 M                 | 75.0    | 91.4   |
| LoRA+PE     | $r_q = r_v = 32, l_p = 8, l_i = 4$ | 151.1 M                | 75.9    | 91.1   |
|             | $r_q = r_v = 64, l_p = 8, l_i = 4$ | 302.1 M                | 76.2    | 91.3   |
| LoRA+PL     | $r_a = r_v = 8, l_v = 8, l_i = 4$  | 52.8 M                 | 72.9    | 90.2   |

Table 15: Hyperparameter analysis of different adaptation approaches on WikiSQL and MNLI. Both prefix-embedding tuning (PrefixEmbed) and prefix-layer tuning (PrefixLayer) perform worse as we increase the number of trainable parameters, while LoRA's performance stabilizes. Performance is measured in validation accuracy.

<span id="page-22-1"></span>

| Method              | MNLI(m)-100 | MNLI(m)-1k | MNLI(m)-10k | MNLI(m)-392K |
|---------------------|-------------|------------|-------------|--------------|
| GPT-3 (Fine-Tune)   | 60.2        | 85.8       | 88.9        | 89.5         |
| GPT-3 (PrefixEmbed) | 37.6        | 75.2       | 79.5        | 88.6         |
| GPT-3 (PrefixLayer) | 48.3        | 82.5       | 85.9        | 89.6         |
| GPT-3 (LoRA)        | 63.8        | 85.6       | 89.2        | 91.7         |

Table 16: Validation accuracy of different methods on subsets of MNLI using GPT-3 175B. MNLI $n$  describes a subset with  $n$  training examples. We evaluate with the full validation set. LoRA performs exhibits favorable sample-efficiency compared to other methods, including fine-tuning.

To be concrete, let the singular values of  $U_A^i U_B^j$  to be  $\sigma_1, \sigma_2, \cdots, \sigma_p$  where  $p = \min\{i, j\}$ . We know that the Projection Metric [Ham & Lee](#page-13-17) [\(2008\)](#page-13-17) is defined as:

$$
d(U_A^i, U_B^j) = \sqrt{p - \sum_{i=1}^p \sigma_i^2} \in [0, \sqrt{p}]
$$

<span id="page-23-3"></span>

| Hyperparameters     | Adaptation        | MNLI-100                           | MNLI-1k  | MNLI-10K | MNLI-392K |
|---------------------|-------------------|------------------------------------|----------|----------|-----------|
| Optimizer           | -                 | AdamW                              |          |          |           |
| Warmup Tokens       | -                 | 250,000                            |          |          |           |
| LR Schedule         | -                 | Linear                             |          |          |           |
| Batch Size          | -                 | 20                                 | 20       | 100      | 128       |
| # Epoch             | -                 | 40                                 | 40       | 4        | 2         |
| Learning Rate       | FineTune          | 5.00E-6                            |          |          |           |
|                     | PrefixEmbed       | 2.00E-04                           | 2.00E-04 | 4.00E-04 | 5.00E-04  |
|                     | PrefixLayer       | 5.00E-05                           | 5.00E-05 | 5.00E-05 | 1.00E-04  |
|                     | LoRA              | 2.00E-4                            |          |          |           |
| Adaptation-Specific | PrefixEmbed $l_p$ | 16                                 | 32       | 64       | 256       |
|                     | PrefixEmbed $l_i$ | 8                                  |          |          |           |
|                     | PrefixTune        |                                    |          |          |           |
|                     | LoRA              | $l_p = l_i = 8$<br>$r_q = r_v = 8$ |          |          |           |

Table 17: The hyperparameters used for different GPT-3 adaptation methods on  $MNLI(m)-n$ .

where our similarity is defined as:

$$
\phi(A, B, i, j) = \psi(U_A^i, U_B^j) = \frac{\sum_{i=1}^p \sigma_i^2}{p} = \frac{1}{p} \left( 1 - d(U_A^i, U_B^j)^2 \right)
$$

This similarity satisfies that if  $U_A^i$  and  $U_B^j$  share the same column span, then  $\phi(A, B, i, j) = 1$ . If they are completely orthogonal, then  $\phi(A, B, i, j) = 0$ . Otherwise,  $\phi(A, B, i, j) \in (0, 1)$ .

# H ADDITIONAL EXPERIMENTS ON LOW-RANK MATRICES

We present additional results from our investigation into the low-rank update matrices.

<span id="page-23-1"></span>

### H.1 CORRELATION BETWEEN LORA MODULES

See [Figure 6](#page-24-1) and [Figure 7](#page-25-0) for how the results presented in [Figure 3](#page-10-1) and [Figure 4](#page-11-0) generalize to other layers.

<span id="page-23-0"></span>

### H.2 EFFECT OF r ON GPT-2

We repeat our experiment on the effect of  $r$  [\(Section 7.2\)](#page-9-4) in GPT-2. Using the E2E NLG Challenge dataset as an example, we report the validation loss and test metrics achieved by different choices of r after training for 26,000 steps. We present our result in [Table 18.](#page-25-1) The optimal rank for GPT-2 Medium is between 4 and 16 depending on the metric used, which is similar to that for GPT-3 175B. Note that the relationship between model size and the optimal rank for adaptation is still an open question.

<span id="page-23-2"></span>

#### H.3 CORRELATION BETWEEN W AND $\Delta W$

See [Figure 8](#page-25-2) for the normalized subspace similarity between W and  $\Delta W$  with varying r.

Note again that  $\Delta W$  does not contain the top singular directions of W, since the similarity between the top 4 directions in  $\Delta W$  and the top-10% of those in W barely exceeds 0.2. This gives evidence that ∆W contains those "task-specific" directions that are otherwise *not* emphasized in W.

An interesting next question to answer, is how "strong" do we need to amplify those task-specific directions, in order for the model adaptation to work well?

<span id="page-24-1"></span>Image /page/24/Figure/0 description: The image displays a grid of heatmaps, organized into four rows labeled 'Layer 1', 'Layer 32', 'Layer 64', and 'Layer 96', and four columns labeled 'ΔWq', 'ΔWv', 'ΔWq', and 'ΔWv'. Each heatmap visualizes normalized subspace similarity between column vectors of A\_r and A\_r for different layers and parameters. The color bar on the right indicates similarity values ranging from 0.0 to 1.0, with darker colors representing lower similarity and brighter colors representing higher similarity. The x-axis of the heatmaps is labeled 'j' and ranges from 1 to 8 for the last two columns, and from 1 to 58 for the first two columns. The y-axis is labeled 'i' and ranges from 1 to 8 for all heatmaps. The heatmaps show a gradient of colors, with higher similarity values generally concentrated in the upper left portion of the matrices.

Figure 6: Normalized subspace similarity between the column vectors of  $A_{r=8}$  and  $A_{r=64}$  for both  $\Delta W_q$  and  $\Delta W_v$  from the 1st, 32nd, 64th, and 96th layers in a 96-layer Transformer.

<span id="page-24-0"></span>

### H.4 AMPLIFICATION FACTOR

One can naturally consider a *feature amplification factor* as the *ratio*  $\frac{\|\Delta W\|_F}{\|U^{\top}WV^{\top}\|_F}$ , where U and V are the left- and right-singular matrices of the SVD decomposition of  $\Delta W$ . (Recall  $UU^{\top} W V^{\top} V$ gives the "projection" of W onto the subspace spanned by  $\Delta W$ .)

Intuitively, when ∆W mostly contains task-specific directions, this quantity measures how much of them are amplified by  $\Delta W$ . As shown in [Section 7.3,](#page-10-2) for  $r = 4$ , this amplification factor is as large as 20. In other words, there are (generally speaking) four feature directions in each layer (out of the entire feature space from the pre-trained model  $W$ ), that need to be amplified by a very large factor 20, in order to achieve our reported accuracy for the downstream specific task. And, one should expect a very different set of feature directions to be amplified for each different downstream task.

One may notice, however, for  $r = 64$ , this amplification factor is only around 2, meaning that *most* directions learned in  $\Delta W$  with  $r = 64$  are *not* being amplified by much. This should not be surprising, and in fact gives evidence (once again) that the intrinsic rank *needed* to represent the "task-specific directions" (thus for model adaptation) is low. In contrast, those directions in the rank-4 version of  $\Delta W$  (corresponding to  $r = 4$ ) are amplified by a much larger factor 20.

 $(A_{r=64}, A'_{r=64}, i, j)$ 

<span id="page-25-0"></span>Image /page/25/Figure/1 description: The image displays a 2x2 grid of heatmaps, each representing a layer of a neural network. The top row shows Layer 1 and Layer 32, while the bottom row shows Layer 64 and Layer 96. Each heatmap is further divided into two sub-heatmaps, labeled \"ΔWq\" and \"ΔWv\". The y-axis of each heatmap is labeled with numbers from 1 to 61, representing the index 'i', and the x-axis is labeled with numbers from 1 to 61, representing the index 'j'. A color bar on the right indicates the scale of values, ranging from 0.0 to 0.8, with darker colors representing lower values and brighter colors (red/pink) representing higher values. The heatmaps show a gradient pattern, with the top-left corner being the brightest (highest value) and the bottom-right corner being the darkest (lowest value). This pattern is consistent across all sub-heatmaps and layers.

<span id="page-25-1"></span>Figure 7: Normalized subspace similarity between the column vectors of  $A_{r=64}$  from two randomly seeded runs, for both  $\Delta W_q$  and  $\Delta W_v$  from the 1st, 32nd, 64th, and 96th layers in a 96-layer Transformer.

| Rank $r$ | val.loss    | <b>BLEU</b>  | <b>NIST</b>   | <b>METEOR</b> | <b>ROUGE.L</b> | <b>CIDEr</b>  |
|----------|-------------|--------------|---------------|---------------|----------------|---------------|
| 1        | 1.23        | 68.72        | 8.7215        | 0.4565        | 0.7052         | 2.4329        |
| 2        | 1.21        | 69.17        | 8.7413        | 0.4590        | 0.7052         | 2.4639        |
| 4        | 1.18        | <b>70.38</b> | <b>8.8439</b> | <b>0.4689</b> | 0.7186         | <b>2.5349</b> |
| 8        | 1.17        | 69.57        | 8.7457        | 0.4636        | <b>0.7196</b>  | 2.5196        |
| 16       | <b>1.16</b> | 69.61        | 8.7483        | 0.4629        | 0.7177         | 2.4985        |
| 32       | <b>1.16</b> | 69.33        | 8.7736        | 0.4642        | 0.7105         | 2.5255        |
| 64       | <b>1.16</b> | 69.24        | 8.7174        | 0.4651        | 0.7180         | 2.5070        |
| 128      | <b>1.16</b> | 68.73        | 8.6718        | 0.4628        | 0.7127         | 2.5030        |
| 256      | <b>1.16</b> | 68.92        | 8.6982        | 0.4629        | 0.7128         | 2.5012        |
| 512      | <b>1.16</b> | 68.78        | 8.6857        | 0.4637        | 0.7128         | 2.5025        |
| 1024     | 1.17        | 69.37        | 8.7495        | 0.4659        | 0.7149         | 2.5090        |

Table 18: Validation loss and test set metrics on E2E NLG Challenge achieved by LoRA with different rank r using GPT-2 Medium. Unlike on GPT-3 where  $r = 1$  suffices for many tasks, here the performance peaks at  $r = 16$  for validation loss and  $r = 4$  for BLEU, suggesting the GPT-2 Medium has a similar intrinsic rank for adaptation compared to GPT-3 175B. Note that some of our hyperparameters are tuned on  $r = 4$ , which matches the parameter count of another baseline, and thus might not be optimal for other choices of  $r$ .

<span id="page-25-2"></span>Image /page/25/Figure/5 description: This image displays four heatmaps side-by-side, each representing a different configuration. The y-axis is labeled 'i' and ranges from 451 to 1176. The x-axis is labeled 'j' for the first three heatmaps and also 'j' for the fourth heatmap. The first heatmap is titled "φ(Wq, Ar = 4, i, j)", the second is titled "ΔWq φ(Wq, Ar = 8, i, j)", the third is titled "φ(Wq, Ar = 64, i, j)", and the fourth is titled "Random φ(Wq, Arand, i, j)". A color bar on the right indicates values ranging from 0.100 to 0.200, with colors transitioning from dark purple to pink and then to light orange. The first three heatmaps show a gradient from dark purple at the top to light orange at the bottom, with the intensity of the orange increasing as 'i' increases. The fourth heatmap, labeled "Random", is entirely dark purple, indicating minimal values across the board. The first heatmap has a distinct vertical line of darker purple in the middle, while the second and third heatmaps show a more uniform gradient.

Figure 8: Normalized subspace similarity between the singular directions of  $W_q$  and those of  $\Delta W_q$ with varying r and a random baseline.  $\Delta W_q$  amplifies directions that are important but not emphasized in W.  $\Delta W$  with a larger r tends to pick up more directions that are already emphasized in W.
# UNDERSTANDING RECONST<PERSON><PERSON><PERSON><PERSON> ATTACKS WITH THE NEURAL TANGENT KERNEL AND DATASET DIS-TILLATION

<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> CSAIL Cambridge, <PERSON><PERSON><PERSON><PERSON>s, USA {loo, r<PERSON><PERSON>, m<PERSON><PERSON><PERSON>, amini, rus}@mit.edu

## ABSTRACT

Modern deep learning requires large volumes of data, which could contain sensitive or private information that cannot be leaked. Recent work has shown for homogeneous neural networks a large portion of this training data could be reconstructed with only access to the trained network parameters. While the attack was shown to work empirically, there exists little formal understanding of its effective regime which datapoints are susceptible to reconstruction. In this work, we first build a stronger version of the dataset reconstruction attack and show how it can provably recover the *entire training set* in the infinite width regime. We then empirically study the characteristics of this attack on two-layer networks and reveal that its success heavily depends on deviations from the frozen infinite-width Neural Tangent Kernel limit. Next, we study the nature of easily-reconstructed images. We show that both theoretically and empirically, reconstructed images tend to "outliers" in the dataset, and that these reconstruction attacks can be used for *dataset distillation*, that is, we can retrain on reconstructed images and obtain high predictive accuracy.

## 1 INTRODUCTION

Neural networks have been shown to perform well and even generalize on a range of tasks, despite achieving zero loss on training data [\(<PERSON> et al., 2017;](#page-13-0) [2021\)](#page-13-1). But this performance is useless if neural networks cannot be used in practice due to security issues. A fundamental question in the security of neural networks is how much information is *leaked* via this training procedure, that is, can adversaries with access to trained models, or predictions from a model, infer what data was used to train the model? Ideally, we want to ensure that our models are resistant to such attacks. However, in practice, we see that this ideal is commonly violated. One heinous violation of this principle is the phenomenon of memorization [\(Arpit et al., 2017;](#page-9-0) [Feldman & Zhang, 2020b;](#page-10-0) [Feldman, 2020;](#page-10-1) [Carlini et al., 2019\)](#page-9-1), where trained networks can be shown to replicate their training data at test time in generative models. A more extreme example of memorization is presented in [Haim et al.](#page-11-0) [\(2022\)](#page-11-0), where the authors show that it is possible to recover a large subset of the training data given only the trained network parameters.

The existence of this attack begs many follow-up questions: "*Under what circumstances is this attack successful?*"; and "*What are properties of these recovered images?*" In this paper, we consider a stronger variant of the attack presented in [Haim et al.](#page-11-0) [\(2022\)](#page-11-0), provide novel theoretical and empirical insights about dataset reconstruction attacks, and provide answers to the above questions. In particular, we make the following new contributions:

We design a stronger version of [Haim et al.](#page-11-0) [\(2022\)](#page-11-0)'s dataset reconstruction attack that can provably reconstruct the *entire* training set for networks in the neural tangent kernel (NTK) [\(Jacot](#page-11-1) [et al., 2018\)](#page-11-1) regime when trained under mean squared error (MSE) loss. This attack transfers to finite networks with its success dependent on deviations from the NTK regime.

We show that outlier datapoints are prone to reconstruction under our attack, corroborating prior work observing this property. Additionally, we show that removing easily reconstructed images can improve predictive accuracy.

We formally prove and empirically show that a dataset reconstruction attack is a variant of *dataset distillation*. The reconstruction loss is equal to the loss of the kernel-inducing points (KIP) [\(Nguyen et al., 2021a](#page-12-0)[;b\)](#page-12-1) dataset distillation algorithm, under a different norm, plus a variancecontrolled term. Furthermore, we can retrain models using recovered images and achieve high performance.

## 2 BACKGROUND AND RELATED WORKS

Machine Learning Privacy. A large body of work studies how to extract sensitive information from trained models. This is problematic as legislation such as HIPAA and GDPR enforce what data can and cannot be published or used [\(Centers for Medicare & Medicaid Services, 1996;](#page-10-2) [Eu](#page-10-3)[ropean Commission, 2016\)](#page-10-3). Quantifying the influence of training examples leads to the topic of influence functions [\(Koh & Liang, 2017\)](#page-11-2), and Membership-inference attacks [\(Shokri et al., 2016;](#page-12-2) [Carlini et al., 2021\)](#page-10-4), which try to infer whether particular examples were used in training. Defending against these attacks is the study of *differential privacy*, which quantifies and limits the sensitivity of models to small changes in training data [\(Dwork et al., 2006;](#page-10-5) [Abadi et al., 2016\)](#page-9-2). Likewise, the field of *machine unlearning* tries to remove the influence of training examples post-training [\(Bourtoule](#page-9-3) [et al., 2019\)](#page-9-3). Without defense techniques, trained networks have been shown to leak information [\(Rigaki & Garcia, 2020\)](#page-12-3). For generative language models, [\(Carlini et al., 2019\)](#page-9-1) show large language models often reproduce examples in the training corpus verbatim. *Model inversion* techniques aim to recreate training examples by looking at model activations [\(Fredrikson et al., 2015;](#page-10-6) [Yang et al.,](#page-12-4) [2019;](#page-12-4) [He et al., 2019\)](#page-11-3). This memorization phenomenon can be shown to be necessary to achieve high performance under certain circumstances [\(Feldman & Zhang, 2020b;](#page-10-0) [Brown et al., 2021\)](#page-9-4).

Dataset Reconstruction. Recent work [\(Haim et al., 2022\)](#page-11-0) has shown that one can reconstruct a large subset of the training data from trained networks by exploiting the implicit biases of neural nets. They note that homogeneous neural networks trained under a logistic loss converge in direction to the solution of the following max-margin problem [\(Lyu & Li, 2020;](#page-11-4) [Ji & Telgarsky, 2020\)](#page-11-5):

<span id="page-1-0"></span>
$$
\underset{\theta'}{\arg\min} \frac{1}{2} \|\theta'\|_2^2 \quad \text{s.t.} \quad \forall i \in [n], y_i f_{\theta'}(x_i) \ge 1,\tag{1}
$$

Where  $\{x_i, y_i\}$  is the training set with images  $x_i$  and labels  $y_i \in \{+1, -1\}$ , and  $f_{\theta}(x)$  the neural network output with parameters θ ′ . [\(Haim et al., 2022\)](#page-11-0) shows that by taking a trained neural network and optimizing images (and dual parameters) to match the Karush–Kuhn–Tucker (KKT) conditions of the max-margin problem, it is possible to reconstruct training data. This is an attack that causes leakage of training data. Here, we consider a stronger variant of the attack that requires training under mean-squared error (MSE) loss.

Neural Tangent Kernel. To investigate the generalization in neural networks we can use the neural tangent kernel (NTK) theory [\(Jacot et al., 2018;](#page-11-1) [Arora et al., 2019\)](#page-9-5). NTK theory states that networks behave like first-order Taylor expansions of network parameters about their initialization as network width approaches infinity [\(Lee et al., 2019\)](#page-11-6). Furthermore, the resulting feature map and kernel converge to the NTK, and this kernel is frozen throughout training [\(Jacot et al., 2018;](#page-11-1) [Arora et al.,](#page-9-5) [2019\)](#page-9-5). As a result, wide neural networks are analogous to kernel machines, and when trained with MSE loss using a support set  $X<sub>S</sub>$  with labels  $y<sub>S</sub>$  result in test predictions given by:

$$
\hat{y}_T = K_{TS} K_{SS}^{-1} y_S,
$$

with  $K$  being the NTK. For fully-connected networks, this kernel can be computed exactly very quickly (as they reduce to arc-cosine kernels), but for larger convolutional networks, exact computation slows down dramatically [\(Arora et al., 2019;](#page-9-5) [Zandieh et al., 2021\)](#page-13-2). In practice, it has been shown that networks often deviate far from the frozen-kernel theoretical regime, with the resulting empirical NTKs varying greatly within the first few epochs of training before freezing for the rest [\(Hanin & Nica, 2020;](#page-11-7) [Aitken & Gur-Ari, 2020;](#page-9-6) [Fort et al., 2020;](#page-10-7) [Loo et al., 2022b;](#page-11-8) [Tsilivis](#page-12-5) [& Kempe, 2022\)](#page-12-5). In this paper, we use the NTK theory to gain a better understanding of these reconstruction attacks.

Dataset Distillation. Dataset distillation aims to construct smaller synthetic datasets which accurately represent larger datasets. Specifically, training on substantially smaller *distilled dataset* achieves performance comparable to the full dataset, and far above random sampling of the dataset [\(Wang et al., 2018;](#page-12-6) [Zhao et al., 2021;](#page-13-3) [Zhao & Bilen, 2021;](#page-13-4) [Nguyen et al., 2021a](#page-12-0)[;b;](#page-12-1) [Zhou et al., 2022;](#page-13-5) [Loo et al., 2022a\)](#page-11-9). There are many algorithms for this, ranging from methods that directly unroll computation [\(Wang et al., 2018\)](#page-12-6), try to efficiently approximate the inner unrolled computation associated with training on distilled data [\(Zhou et al., 2022;](#page-13-5) [Loo et al., 2022a;](#page-11-9) [Nguyen et al., 2021b\)](#page-12-1), and other heuristics [\(Zhao et al., 2021;](#page-13-3) [Zhao & Bilen, 2021\)](#page-13-4). One algorithm is kernel-induced points (KIP) [\(Nguyen et al., 2021a](#page-12-0)[;b\)](#page-12-1), which leverages NTK theory to derive the following loss:

$$
\mathcal{L}_{KIP} = \frac{1}{2} ||y_t - K_{TS} K_{SS}^{-1} y_S||_2^2.
$$

The loss indicates the prediction error of infinite width networks on distilled images  $X<sub>S</sub>$  and labels  $y_S$ , which are then optimized. We bring up dataset distillation as we show in this paper that our dataset reconstruction attack is a generalization of KIP, and that dataset distillation can be used to defend against the attack.

## <span id="page-2-1"></span>3 A NEURAL TANGENT KERNEL RECONSTRUCTION ATTACK

[Haim et al.](#page-11-0) [\(2022\)](#page-11-0) considers the scenario where the attacker only has access to the final trained network parameters. This attack requires that the networks are homogeneous and are trained for many epochs until convergence so that the network converges in direction to the final KKT point of Eq. [1.](#page-1-0) While it is a good proof-of-concept for such attacks, there are several theoretical and practical limitations of this attack. We find that the attack presented in [Haim et al.](#page-11-0) [\(2022\)](#page-11-0) is brittle. Namely, we were unable to reliably reproduce their results without careful hyperparameter tuning, and careful network initialization strategies. Their attack also requires training until directional convergence, which requires network parameters to tend to infinity, and requires homogenous networks. Furthermore, outside of the top few reconstructions, the overwhelming majority of reconstructions ( $> 70\%$ ) are of poor quality (more in-depth discussions in appendix [A\)](#page-14-0). Here we present an attack which is compatible with early stopping, does not require special initialization strategies, and can provable reconstruct the *entire training set* under certain assumptions, the first guarantee of reconstruction in any regime. However, our attack requires access to the model initialization or a previous training checkpoint. Access to the initialization or earlier training checkpoints arises naturally in many settings, such as fine tuning from public models, or in federated learning where clients receive period updates of the model parameters.

Note that we cannot compare our attack to other ones such as gradient leakage attacks [\(Zhu et al.,](#page-13-6) [2019\)](#page-13-6), membership inference attacks [\(Shokri et al., 2016\)](#page-12-2) and generative model attacks [\(Carlini](#page-10-8) [et al., 2020\)](#page-10-8), as these attacks either require gradient access (which also requires parameter access) in the setting on gradient leakage, specific query data points in the case of membership inference, or a generative model and query points for generative model attacks. Our attack requires no a priori knowledge of the dataset. With this context in mind, the attack desribed in this paper contributes to the literature on parameter-only based attacks.

Consider a neural network trained under MSE loss,  $\mathcal{L} = \frac{1}{2} \sum_{i=0}^{N-1} (y_i - f_\theta(x_i))^2$ , for  $x_i, y_i \in$  $X_T, y_T$ , being the training set datapoints and labels. Now further assume that the network is trained under gradient flow and that the network is approximately in the lazy/NTK regime, that is, it behaves like a first-order Taylor expansion of the network outputs [\(Chizat et al., 2019\)](#page-10-9):

<span id="page-2-0"></span>
$$
f_{\theta}(x) \approx f_{lin,\theta}(x) = f_{\theta_0}(x) + (\theta - \theta_0)^{\dagger} \nabla_{\theta} f_{\theta_0}(x)
$$
 (2)

[Lee et al.](#page-11-6) [\(2019\)](#page-11-6) shows that the time evolution of the network parameters in this regime is given by:

$$
\theta(t) = \theta_0 - \nabla_{\theta} f_{\theta_0} (X_T)^{\mathsf{T}} K_0^{-1} \left( I - e^{-\eta K_0 t} \right) \left( f_{\theta_0} (X_T) - y_T \right)
$$

With  $\eta$  the learning rate and  $K_0$  the finite-width/empirical NTK evaluated at  $\theta_0$ . Namely, the final change in parameters is given by:

<span id="page-2-2"></span>
$$
\Delta\theta = \theta_f - \theta_0 = \nabla_{\theta} f_{\theta_0}(X_T)^{\mathsf{T}} K_0^{-1} (y_T - f_{\theta_0}(X_T))
$$
\n(3)

Image /page/3/Figure/0 description: The image displays a comparison of image reconstructions and original training images for two different datasets: MNIST Odd vs. Even and CIFAR-10 Animal vs. Vehicle. The top row shows reconstructions, and the bottom row shows the original training images. The left side of the image focuses on MNIST, presenting pairs of reconstructed and original digits, categorized as odd or even. The right side of the image focuses on CIFAR-10, showing pairs of reconstructed and original images, categorized as either animals or vehicles. Both datasets show a clear visual distinction between the reconstructed and original images, with the reconstructions generally appearing less detailed or slightly distorted compared to the originals.

<span id="page-3-5"></span>Figure 1: Reconstructed images (top) vs closest training images (bottom) for MNIST Odd vs. Even, and CIFAR-10 Animal vs. Vehicle Classification. Reconstructions were made from 4096-width two hidden layer fully-connected networks trained with standard dynamics and low learning rates. Apart from small amounts of noise, the original images and their reconstructions are visually indistinguishable.

Image /page/3/Figure/2 description: This figure displays two sets of plots, one for MNIST Odd vs. Even and another for CIFAR-10 Animal vs. Vehicle. Each set contains two rows of plots: 'Standard L2 Distance' and 'Linearized L2 Distance'. Within each row, there are four plots corresponding to different training set sizes: 20, 100, 200, and 500. The x-axis for all plots is labeled 'Image index', and the y-axis is labeled 'L2 Distance'. The plots show multiple lines, each representing a different 'Model width' ranging from 256 to 4096, indicated by a color gradient from red to blue. The MNIST plots show that as the training set size increases, the L2 distance generally increases, with wider models showing higher distances. The CIFAR-10 plots exhibit a similar trend, with wider models and larger training set sizes leading to higher L2 distances.

<span id="page-3-4"></span>Figure 2: Reconstruction quality curves for the MNIST Odd vs. Even and for CIFAR-10 Animal vs. Vehicle classification. We either used standard dynamics (top) or linearized dynamics (bottom) and varied both the size of the training set and model width. Smaller datasets are easier to reconstruct while wider models can reconstruct more images, with linearization helping in both scenarios.

Notably, this is the solution to the following optimization problem:

$$
\underset{\Delta\theta}{\arg\min} \frac{1}{2} \|\Delta\theta\|_2^2 \quad \text{s.t.} \quad \Delta\theta^\mathsf{T} \nabla_\theta f_{\theta_0}(X_T) = y_T - f_{\theta_0}(X_T) \tag{4}
$$

The corresponding KKT conditions are:

<span id="page-3-6"></span><span id="page-3-2"></span><span id="page-3-1"></span><span id="page-3-0"></span>
$$
\Delta \theta = \alpha^{\mathsf{T}} \nabla_{\theta} f_{\theta_0}(X_T) \tag{5}
$$

$$
\Delta \theta^{\mathsf{T}} \nabla_{\theta} f_{\theta_0}(X_T) = y_T - f_{\theta_0}(X_T) \tag{6}
$$

With  $\alpha$  being the set of dual parameters. In our formulation, eq. [\(5\)](#page-3-0) ensures we are at a stationary point, while eq. [\(6\)](#page-3-1) ensures that the labels are correct. Like with [\(Haim et al., 2022\)](#page-11-0), we can directly optimize the reconstruction images and dual parameters (X and  $\alpha$ , respectively) to match these KKT conditions, given a network's final parameters and initialization to get  $\Delta\theta$ . In practice, we only need to optimize eq. [\(5\)](#page-3-0), for reasons we will describe next, leading to our reconstruction loss:

$$
\mathcal{L}_{\text{Reconstruction}} = \|\Delta \theta - \alpha^{\mathsf{T}} \nabla_{\theta} f_{\theta_0}(X_T)\|_2^2 \tag{7}
$$

Reconstruction in Infinite Width. Next, we show that this formulation of the attack recovers the *entire* training set for infinite-width models. We further assume that the training data lies on the unit hypersphere.

<span id="page-3-3"></span>**Theorem 1.** If  $\mathcal{L}_{reconstruction} = 0$  (from Eq. [7\)](#page-3-2), then we reconstruct the entire training set in the *infinite-width limit, assuming that training data lies on the unit hypersphere.*

*Proof.* Define  $k_{\theta}(x, x') = \nabla_{\theta} f_{\theta}(x)^\intercal \nabla_{\theta} f_{\theta}(x')$ , that is, the finite-width/empirical NTK function. We know as network width  $w \to \infty$ ,  $\Delta \theta = \sum_{\alpha_i, x_i \in \alpha^T, X_T} \alpha_i \nabla_{\theta_0} f_{\theta_0}(x_i)$ , with  $\alpha^T = K_{\theta_0, TT}^{-1} y_T$ , with  $X_T$  being the training set,  $y_T$  the training labels, and  $K_{\theta_0,TT}$  the finite-width NTK evaluated on the training set. Our attack then becomes:

$$
\left\| \Delta \theta - \sum_{\alpha_j x_j \in \alpha^R, X_R} \alpha_j \nabla_{\theta_f} f_{\theta_f}(x_j) \right\|_2^2 = \left\| \sum_{\alpha_i, x_i \in \alpha^T, X_T} \alpha_i \nabla_{\theta_0} f(x_i) - \sum_{\alpha_j x_j \in \alpha^R, X_R} \alpha_j \nabla_{\theta_f} f_{\theta_f}(x_j) \right\|_2^2
$$
(8)

$$
= \left\| \sum_{\alpha_i, x_i \in \alpha^T, X_T} \alpha_i k_{\theta_0}(x_i, \cdot) - \sum_{\alpha_j, x_j \in \alpha^R, X_R} \alpha_j k_{\theta_j}(x_j, \cdot) \right\|_2^2 \tag{9}
$$

<span id="page-4-2"></span>Table 1: Performance of KIP, Recon-KIP (RKIP), RKIP from a trained network (RKIP-finite), on distilling 500 images down to 20 images. KIP and RKIP provide the best infinite-width performance, while KIP fails for finite models.  $(n=7)$ 

| Distillation Algorithm    | MNIST Odd/Even   |                  |                  | CIFAR-10 Animal/Vehicle |                  |                  |  |
|---------------------------|------------------|------------------|------------------|-------------------------|------------------|------------------|--|
|                           | Standard         | Linearized       | Infinite Width   | Standard                | Linearized       | Infinite Width   |  |
| Full dataset (500 images) | $92.85 \pm 0.42$ | $92.91 \pm 0.33$ | $93.18 \pm 0.37$ | $75.06 \pm 0.21$        | $74.60 \pm 0.21$ | $75.42 \pm 0.28$ |  |
| <b>KIP</b>                | $57.42 \pm 8.41$ | $55.62 \pm 7.48$ | $91.53 + 0.57$   | $35.26 \pm 5.67$        | $32.37 \pm 3.60$ | $70.98 \pm 0.43$ |  |
| <b>RKIP</b>               | $89.61 \pm 1.18$ | $89.99 + 1.11$   | $91.44 + 0.48$   | $72.23 + 3.61$          | $72.76 + 3.74$   | $74.66 + 0.93$   |  |
| RKIP-finite               | $88.45 \pm 0.89$ | $86.15 \pm 3.39$ | $87.31 \pm 3.24$ | $71.96 \pm 1.14$        | $63.99 \pm 4.02$ | $62.05 \pm 4.17$ |  |
| Random images             | $73.52 \pm 3.60$ | $73.54 \pm 3.61$ | $74.12 + 3.73$   | $70.36 \pm 2.53$        | $70.18 \pm 2.54$ | $70.77 + 2.04$   |  |

With T and R referring to the training and reconstruction set, respectively. As  $w \to \infty$  we know that  $k_{\theta_0}, k_{\theta_f} \rightarrow k_{NTK}$ . Furthermore, define

$$
P_T = \sum_{\alpha_i, x_i \in \alpha^T, X_T} \alpha_i \delta(x_i), \qquad P_R = \sum_{\alpha_j, x_j \in \alpha^R, X_R} \alpha_j \delta(x_j)
$$

as measures associated with our trained network and reconstruction, respectively, and  $\mu_*$  =  $\int_{\Omega} k_{NTK}(x, \cdot) dP_*(x)$ , with  $\Omega = S^d$ , with d being the data dimension (Assuming data lies on the unit hypersphere).  $\mu_T$  and  $\mu_R$  are now kernel embeddings of our trained network and reconstruction, respectively. Our reconstruction loss becomes:  $\|\mu_T - \mu_R\|_{\mathcal{H}_{NTK}}^2$ . This is the maximum-mean discrepancy (MMD) [\(Gretton et al., 2012\)](#page-11-10). We note that  $P_T$ ,  $P_R$  are signed Borel measures (since  $\alpha$ are finite and our reconstruction/training sets are on the unit sphere). The NTK is universal over the unit sphere [\(Jacot et al., 2018\)](#page-11-1), implying that the map  $\mu$ : {Family of signed Borel measures}  $\rightarrow$  H is injective [\(Sriperumbudur et al., 2011\)](#page-12-7), meaning that we are able to **recover the entire training** set, provided that  $\alpha_i \neq 0$ , which happens almost surely (see appendix [C\)](#page-16-0). П

Note that in practice we do not enforce the unit sphere requirement on the data, and we still see high reconstruction quality, which we show in section [4.](#page-4-0) This mapping from the network tangent space to image space also sheds light on the success of gradient leakage attacks [\(Zhu et al., 2019\)](#page-13-6), in which gradients are used to find training batch examples.

## <span id="page-4-0"></span>4 DATASET RECONSTRUCTION FOR FINITE NETWORKS

While the attack outlined in Theorem [1](#page-3-3) carries fundamental theoretical insights in the infinite-width limit, it has limited practicality as it requires access to the training images themselves to compute the kernel inner products. How does the attack work for finite-width neural networks, and under what circumstances is this attack successful?

To answer these questions, we follow the experimental protocol of [Haim](#page-11-0) [et al.](#page-11-0) [\(2022\)](#page-11-0), where we try to recover images from the MNIST and CIFAR-10 datasets on the task of odd/even digit or animal/vehicle classification for MNIST and CIFAR-10, respectively. We vary the size of the training set from 10 images per class to 250 images per class (500 total training set size). We consider two hidden layer neural networks with biases using standard initialization (as opposed to NTK parameterization or the initialization scheme proposed in

Image /page/4/Figure/9 description: The image displays two scatter plots side-by-side. The left plot is titled "MNIST Odd vs. Even" and shows "Mean Reconstruction Error" on the y-axis, ranging from 2 to 14, and "Kernel Distance between Init and Final NTKs" on the x-axis, ranging from 0.000 to 0.035. The points are colored from blue to red and vary in size, with a legend indicating that size represents "Dataset size" from 20 to 500. The right plot is titled "CIFAR-10 Animal vs. Vehicle" and shows "Mean Reconstruction Error" on the y-axis, ranging from 4 to 20, and "Kernel Distance between Init and Final NTKs" on the x-axis, ranging from 0.00 to 0.08. Similar to the left plot, the points are colored from blue to red and vary in size, also indicating "Dataset size" from 20 to 500.

<span id="page-4-1"></span>Figure 3: Mean reconstruction error vs. the kernel distance from the initialization to the final kernel. The mean reconstruction error, measured as the average value of the reconstruction curve, is strongly correlated with how much the finite-width NTK evolves over training. Dataset size is given by dot size, while the color indicates model width (see fig. [2\)](#page-3-4).

[Haim et al.](#page-11-0) [\(2022\)](#page-11-0)). We vary the width of the neural networks between 256 and 4096 to see how deviations from the infinite-width regime affect the reconstruction quality. Furthermore, it is known that for finite-width networks the finite-width NTK varies over the course of training, deviating from the infinite-width regime. We can force the kernel to be frozen by considering *linearized* training, where we train a first-order Taylor expansion of the network parameters around its initialization (see

Image /page/5/Figure/0 description: The image displays two sets of plots, one for MNIST 10 Classes and another for CIFAR-10 10 Classes, both under Standard Dynamics. The left side shows four plots for MNIST, with training set sizes of 20, 100, 200, and 500. Each plot has 'Image Index' on the x-axis and 'L2 Distance' on the y-axis, ranging from 0 to 15. Within each plot, multiple lines, colored from red to blue, represent different model widths (256 to 4096), showing the L2 distance for each image index. The right side shows three plots for CIFAR-10, with 'Image Index' on the x-axis and 'L2 Distance' on the y-axis, ranging from 0 to 30. These plots also show multiple lines representing different model widths, with the x-axis scales varying across the plots (0-10, 0-100, 0-200, 0-400). A legend for 'Model width' is provided for the CIFAR-10 plots, listing widths from 256 to 4096.

<span id="page-5-0"></span>Figure 5: Reconstruction curves for networks trained on multiclass MNIST/CIFAR-10.

eq. [\(2\)](#page-2-0)). We consider both networks under standard (unmodified) dynamics and linearized dynamics. In appendix [J.1](#page-24-0) and appendix [J.2](#page-25-0) we consider convolutional architectures and high-resolution datasets, respectively, but we restrict our attention to MLPs on lower-resolution images in the main text.

We train these networks for  $10^6$  iteration using full-batch gradient descent with a low learning rate, and during the reconstruction, we make  $M = 2N$  reconstructions with N being the training set size. A full description of our experimental parameters is available in appendix [H](#page-22-0) and algorithmic runtime details in appendix [B.](#page-15-0) To measure reconstruction quality we consider the following metric. We first measure the squared  $L_2$  distance in pixel space from each training image to each reconstruction. We select the pair of training images and reconstruction which has the lowest distance and remove it from the pool, considering it pair of image/reconstruction. We repeat this process until we have a full set of N training images and reconstructions (See Fig. [1\)](#page-3-5). We then order the  $L_2$  distances into an ascending list of distances and plot this function. We call this the *reconstruction curve* associated with a particular reconstruction set. We plot these reconstruction curves for varying dataset sizes and model widths in fig. [2.](#page-3-4) From fig. [2](#page-3-4) we have the following three observations:

**Smaller training sets are easier to reconstruct.** We see that the reconstruction curve for smaller datasets has low values for all model widths. Wider models can resolve larger datasets. We observe that for a given model width, there is a threshold image index at which the quality of reconstructions severely decreases. For example, for MNIST Odd/Even, 200 images and a width of 1024, this is 80 images. As we increase the model width this threshold increases almost monotonically.

Linearization improves reconstruction quality. We see that linearized networks can resolve more images and have better images compared to their same-width counterparts. The success of linearization suggests that deviations from the frozen NTK regime affect reconstruction quality. We can measure the deviation from the frozen kernel regime by measuring the *kernel distance* of the network's initialization

Image /page/5/Figure/6 description: The image displays a grid of images organized into three columns, each representing a different dataset: Caltech Birds 1/cls, CIFAR-100 2/cls, and CIFAR-10 5/cls. Each column has two rows: 'Reconstructions' and 'Train Images'. The 'Reconstructions' row shows generated or reconstructed images, while the 'Train Images' row shows original training images. The Caltech Birds column shows various bird images. The CIFAR-100 column displays images of a clock, sunflowers, a patterned wall, a sink, a cabinet, and an apple. The CIFAR-10 column features images of a dog, a car, a bird, a reindeer, a car, and a truck.

Figure 4: Reconstructions of training data for few-shot fine tuning on a ResNet-18 pretrained on ImageNet on Caltech Birds (1/cls), CIFAR-100 (2/cls) and CIFAR-10 (5/cls).

<span id="page-5-1"></span>.

NTK and its final NTK, given by the following:  $d(K_0, K_f) = 1 - \frac{\text{Tr}(K_0^T K_f)}{\|K_0\|_F \|K_f\|_F}$ 

Intuitively, this distance tells us how well the initialization and final kernel align. Large values indicate that the kernel has changed substantially, meaning the network is deviating far from the NTK regime. We plot these kernel distances against the mean value of the reconstruction curves in figure fig. [3.](#page-4-1) We see immediately that reconstruction quality is strongly correlated with kernel distance, and that smaller datasets and wider models have a lower kernel distance. In appendix [G,](#page-21-0) we discuss how our attack is **compatible with early stopping and cross-entropy loss**, unlike [\(Haim](#page-11-0) [et al., 2022\)](#page-11-0), which requires training until convergence. A more detailed discussion of the effect of early stopping is available in appendix [G.](#page-21-0)

Multiclass Classification. In previous sections, we showed the validity of the attack on binary classification. Here we verify that the attack works with multiple classes. We repeat the same procedure as in section [4,](#page-4-0) but will all 10 classes. Details are given in appendix [B,](#page-15-0) as well as additional results on 200-way classification on Tiny-ImageNet in appendix [J.2.](#page-25-0) Results are shown by reconstruction curves in fig. [5.](#page-5-0) We observe that this attack has **improved** reconstruction quality with more classes. In appendix [J.3,](#page-25-1) we observe that multi-class classification leads to lower kernel distances, suggest-

ing it behaves more in the kernel regimes, explaining the better reconstruction quality. Future work could investigate this further.

## <span id="page-6-2"></span>5 DATASET RECONSTRUCTION IN FINE TUNING

A key requirement of the attack is the model initialization. When training from scratch, attackers will not have access to this, making the attack useless. However, practitioners often do not train from scratch, but rather fine-tune large publicly available pre-trained models. Furthermore, users often do not have access to large amounts of data, effectively making the task few-shot. With evidence suggesting that training neural networks later during fine-tuning is well approximated by the frozen finite-NTK theory [\(Zancato et al., 2020;](#page-12-8) [Shon et al., 2022;](#page-12-9) [Zhou et al., 2021;](#page-13-7) [Malladi et al., 2023\)](#page-11-11), this makes the few-shot fine-tuning setting an easy target for this attack. To evaluate our attack in this setting, we fine-tuned publically available ResNet-18s pretrained on ImageNet on few-shot image classification on Caltech Birds (200 classes) [\(Welinder et al., 2010\)](#page-12-10), CIFAR-100, CIFAR-10 with 1, 2, and 5 samples per class, respectively. For  $\theta_0$ , we use the initial fine-tuned model parameters. We see the best reconstructions in fig. [4.](#page-5-1) We see that our attack is able to recover some training images but with limited quality. Future work could look at improving these attacks.

<span id="page-6-1"></span>

## 6 WHAT DATAPOINTS ARE SUSCEPTIBLE TO RECONSTRUCTION?

It has been observed in previous work that no datapoints are equally susceptible to privacy attacks [\(Carlini et al., 2022;](#page-10-10) [Feldman & Zhang, 2020a;](#page-10-11) [Carlini et al., 2021;](#page-10-4) [Bagdasaryan & Shmatikov,](#page-9-7) [2019\)](#page-9-7). In particular, *outlier* images tend to be leaked more easily than others. In this section, we show that this occurs for our attack, and provide theoretical justification for this.

### 6.1 Hard to fit Implies Easy to Reconstruct

By considering our reconstruction loss  $\|\Delta\theta \frac{1}{2}$  $\sum_j \alpha_j \phi(x_j)$ 2 with  $\phi(x_j) = \nabla_{\theta_f} f_{\theta_f}(x_j)$  we aim to learn a basis to "explain"  $\Delta\theta$ , we see that this could be cast as a sparse coding problem. Assuming that all  $\phi(x_i)$  are of roughly the same magnitude, we expect the the parameters which larger  $\alpha$  parameters to contribute more to  $\Delta\theta$ , and thus be more easily reconstructed. This is closely related to how data points with high influence are likely to be memorized [\(Feldman & Zhang, 2020a\)](#page-10-11). We verify this heuristic holds empirically by plotting a scatter plot of reconstruction error vs. the corresponding  $|\alpha|$  values calculated for infinite width in fig. [7.](#page-7-0) We see that images with small  $\alpha$  val-

Image /page/6/Figure/7 description: The image displays two plots side-by-side. The left plot, titled "CIFAR-10 Accuracy", shows the test accuracy (%) on the y-axis against the number of datapoints removed on the x-axis. Two lines are plotted: "Prune Easiest Reconstructions" in maroon and "Prune Random" in blue, both with shaded confidence intervals. The accuracy decreases as more datapoints are removed, with "Prune Easiest Reconstructions" generally showing higher accuracy. The right plot, titled "Reconstruction Curve", shows the L2 Distance on the y-axis against the Image Index on the x-axis. Several curves are plotted, colored from dark blue to red, representing different values of 'n' and 'w' as indicated in the legend: n=900, w=1650; n=600, w=1347; n=300, w=952; and n=20, w=245. These curves show an increasing trend in L2 distance as the image index increases, with a sharp rise at the end.

<span id="page-6-0"></span>Figure 6: Test accuracy of iteratively pruned CIFAR-10 using either random pruning or pruning based on easily reconstructed images (Left), and reconstruction curves for pruned CIFAR-10 (Right). We see that easily reconstructed datapoints can be removed without harming accuracy. We observe a privacy "onion" effect where removing easily reconstructed images reveals other images which are easy to reconstruct. Bolded lines indicate the 20 images removed after pruning.

ues are "protected" from reconstruction since their contribution to  $\Delta\theta$  is small and could be written off as noise. From appendix [C,](#page-16-0) we know that  $\alpha = 0$  corresponds to an image/label which does not alter the prediction at all, so this suggests that well-predicted datapoints are safe from reconstruction. Aside from the definition of  $\alpha = K^{-1}y$ , we can alternatively show that  $\alpha$  is closely related to how *quickly* the model fits that datapoint. We can write (see appendix [C.1](#page-16-1) for a derivation) that  $\alpha_j = \int_0^\infty (y_j - f_{\theta_t}(x_j)) dt$  implying that datapoints which are slow to fit will have large  $\alpha$  values, further strengthening the claim that outliers are easier to reconstruct.

### 6.2 A Reconstruction Privacy Onion

[Carlini et al.](#page-10-10) [\(2022\)](#page-10-10) showed that removing "vulnerable" training points reveals another set of training points which are susceptible to inference attacks. Here, we verify that our reconstruction attacks sees a similar phenomenon. Specifically, we consider reconstructing CIFAR-10 training images after training on *n* datapoints, with  $n = 900$  initially. We train and attack networks with  $w \propto \sqrt{n}$ , and then iterative remove the 20 most easily reconstructed datapoints based on the reconstrution curve. We scale the network capacity with  $n$  so that our attack is unable to reconstruct the entire training

Image /page/7/Figure/0 description: The image displays two sets of scatter plots, each with four subplots. The left set is titled "MNIST Odd vs. Even" and the right set is titled "CIFAR-10 Animal vs. Vehicle". Each subplot shows the relationship between "L2 Distance" on the y-axis and "|a|" on the x-axis. The subplots are grouped by a number at the top: 20, 100, 200, and 500. Each subplot also includes information about 'w' and 'p' values. For "MNIST Odd vs. Even", the 'w' values are 256, 512, 1448, and 4096, with corresponding 'p' values of -0.55, -0.75, -0.69, and -0.70. For "CIFAR-10 Animal vs. Vehicle", the 'w' values are 256, 512, 1448, and 4096, with corresponding 'p' values of -0.55, -0.47, -0.44, and -0.54. The plots generally show a downward trend, indicating that as "|a|" increases, "L2 Distance" tends to decrease.

<span id="page-7-0"></span>Figure 7: Scatter plots of reconstruction quality measured in 12 distance and corresponding  $|\alpha|$ values for images. We vary the width with  $n$  so that we observe a range of reconstruction qualities.  $|\alpha|$  is negatively correlated with reconstruction error.

set. We see that in fig. [6,](#page-6-0) that despite our network and attack capacity decreasing as we remove more datapoints, we are still able to reconstruct data points with increasing attack quality, replicating the "privacy onion" effect. Future work could look at how the interaction of  $\alpha$  parameters affects which items are susceptible to reconstruction post-datapoint removal. Likewise, we evaluate the test accuracy on these pruned subsets in fig. [6.](#page-6-0) We see that as, these easily reconstructed datapoints tend to be outliers, removing them has a reduced effect on the test accuracy, however as [Sorscher et al.](#page-12-11) [\(2022\)](#page-12-11) discusses, the decision to remove easy vs. hard datapoints during pruning is dependent on other factors such as the size of the dataset and the complexity of the task.

## 7 UNIFYING RECONSTRUCTION AND DISTILLATION

In the previous sections, we considered the task of reconstructing the *entire* training set. To do this, we set the reconstruction image count  $M > N$ . What happens if we set  $M < N$ ? Do we reconstruct a subset of a few training images, or do we form images that are *averages* of the training set?

We perform this experiment on the CIFAR-10 Animal/Vehicle task with 500 training images for a 4096-width model with linearized dynamics, aiming to reconstruct only 20 images. We recover the images shown in appendix [E.](#page-19-0) With a few exceptions, these images are not items of the training set, but rather these images look like *averages* of classes.

Now, what if we retrain a network on these reconstructed images? Noting that the optimal set of dual parameters for a full reconstruction is given by  $\alpha^T = K_{TT}^{-1} y_T$ , a natural choice for the training labels for these images is  $y_R = K_{RR} \alpha^R$ , where we compute the empirical NTK for  $K_{RR}$  and use the learned  $\alpha^R$ 

Image /page/7/Figure/7 description: This image contains two line graphs side-by-side, both plotting accuracy against the full training set size. The left graph is titled "MNIST Odd vs. Even" and shows accuracy on the y-axis ranging from 65 to 95. The x-axis, representing the full training set size, ranges from 20 to 500. Four lines are plotted: "Full dataset (500 images)" (black), "KIP" (brown), "RKIP" (green), and "RKIP-finite" (purple). The right graph is titled "CIFAR-10 Animal vs. Vehicle" and shows accuracy on the y-axis ranging from 60 to 78. The x-axis, also representing the full training set size, ranges from 20 to 500. The same four lines are plotted, with similar trends observed in both graphs, though the CIFAR-10 graph shows generally lower accuracy values.

<span id="page-7-2"></span>Figure 8: Performance of KIP, RKIP, and RKIPfinite on distill N images down to 20 images, trained on a 4096 width network with standard dynamics. KIP fails to transfer to finite networks while RKIP variations succeed.

parameters found during reconstruction. Retraining a different network from scratch on these 20 recovered images yields high accuracy, as shown in table [1](#page-4-2) as RKIP-finite. This suggests that by doing this reconstruction we performed *dataset distillation*, that is we constructed a smaller set of images that accurately approximates the full dataset. This is not a coincidence, and the two algorithms are in fact the same. More formally:

<span id="page-7-1"></span>**Theorem 2.** *The reconstruction scheme of Eq.* [4](#page-3-6) *with KKT points of Eq.* [5](#page-3-0) *and Eq.* [6,](#page-3-1) *with*  $M \leq N$ *where* M *is the reconstruction image counts and* N *is the dataset size, can be written as a kernel inducing point distillation loss under a different norm plus a variance-controlled error as follows:*

$$
\mathcal{L}_{Reconstruction} = \overbrace{\left\|y_T - K_{TR}K_{RR}^{-1}y_R\right\|_{K_{TT}^{-1}}^2 + \lambda_{\text{var of }R|T}}^{RKP\,loss}
$$

The full proof is given in appendix [D.](#page-17-0)  $\lambda_{var \text{ of } R|T}$  is proportional to the variance of the reconstruction data points conditioned on the training data, based on the NTK (see appendix [D\)](#page-17-0). Intuitively, it ensures that training images provide "information" about the reconstructions. Compared to the loss

Image /page/8/Picture/0 description: The image displays a comparison of distilled datasets for two different tasks: MNIST Odd vs. Even and CIFAR-10 Animal vs. Vehicle. The MNIST section is divided into two rows labeled KIP and RKIP, each showing a grid of generated digits. The CIFAR-10 section is also divided into two rows, showcasing generated images of animals and vehicles. Both sections present multiple examples of synthesized data, with the MNIST section showing digits and the CIFAR-10 section showing images that appear to be animals and vehicles, though somewhat blurry.

Figure 9: Visualizations of distilled datasets, on MNIST Odd/Even and CIFAR-10 Animal/Vehicle classification made with KIP, and RKIP. We distill datasets of 500 original images to 20 images (shown). KIP does not copy the original training images, while RKIP occasionally reproduces training images.

of a well-known dataset-distillation algorithm, KIP (with  $S$  referring to the distilled dataset):

<span id="page-8-0"></span>
$$
\mathcal{L}_{\text{KIP}} = ||y_T - K_{TS} K_{SS}^{-1} y_S||_2^2
$$

The connection is apparent: the reconstruction loss is equal to the KIP dataset distillation loss under a different norm, where, rather than weighting each datapoint equally, we weight training images by their inverse similarity measured by the NTK, plus  $\lambda_{var\text{of }r|T}$ . This leads to a variant of KIP which we call Recon-KIP (RKIP) which uses the reconstruction loss in theorem [2.](#page-7-1) Note that for large datasets, RKIP is not practically feasible since it requires computing  $K_{TT}^{-1}$ , which is typical  $N \times N$ . We deal with small datasets in this work so it is still tractable.

We summarize the performance on KIP and RKIP in table [1](#page-4-2) on the MNIST Odd/Even and CIFAR-10 Animal/Vehicle task, distilling 500 images down to 20. We evaluate 4096-width networks with standard or linearized dynamics, and infinite width using the NTK. Additionally, we consider using the images/labels made from reconstructing dataset points using a finite network trained on the full dataset and call this RKIP-finite. Note in this case the labels are not necessarily  $\{+1, -1\}$ , as  $K_{0,RR}\alpha^R$  are not guaranteed to be one-hot labels. Similar results for distilling fewer images (20 -500 training images) to 20 distilled images are shown in figure fig. [9.](#page-8-0)

We observe in table [1](#page-4-2) that both KIP and RKIP have high infinite-width accuracies, but KIP sees a significant performance drop when transferring to finite networks. For example, while KIP achieves 91.53% infinite-width test accuracy on the MNIST odd/even task, its finite-width performance is 55.62%, not significantly better than a random guess. Interestingly, this performance gap increases as we distill more images, as seen in fig. [8.](#page-7-2) For small distilled datasets there is little to no performance drop but for larger ones the difference is significant. In contrast, RKIP surprisingly sees almost no performance drop in the finite-width settings. We hypothesize that this finite-width transfer performance difference for KIP and not RKIP could be due to the contribution of  $\lambda_{var \text{ of } r|T}$ , which we discuss in appendix [D.](#page-17-0) We leave it to future work to explore this further. Additionally, RKIP-finite performs nearly as well as RKIP, despite distilling using the information from a single finite-width neural network.

## 8 Discussion, Limitations, and Conclusion

In this work we showed that a stronger variant of the attack given in [Haim et al.](#page-11-0) [\(2022\)](#page-11-0) which requires wide neural networks trained under MSE loss can provably reconstruct the entire training set, owing to the injectivity of the NTK kernel measure embedding. We showed that this attack works in practice for finite-width networks, with deviations from the infinite-width regime weakening the attack. We looked at how outlier datapoints are more likely to be reconstructed under our attack, and that these easily reconstructed images can be detrimental to learning. Finally, we made a novel connection between this reconstruction attack and dataset distillation. While this sheds light on dataset reconstruction attacks, and their theoretical underpinnings, there are still many avenues to explore.

In this work, we primarily explored 2-layer fully connected networks, where neural networks are known to behave similarly to their infinite-width counterparts. Meanwhile, deeper convolutional networks are known to deviate significantly, and it is unclear how well the attacks in this paper would transfer to those settings, and what adjustments would need to be made. Secondly, while we observed increasing model width increases the network's "resolving capacity" (i.e. how many images it could reconstruct), future work could look at how this quantity arises from deviations in the finite-width NTK from the infinite width one. Finally, we still need to resolve how the dataset

reconstruction notion of privacy connects with more established notions such as differential privacy, which is the subject of future work.

We believe this work provides an important step toward understanding the strengths and weaknesses of dataset reconstruction attacks and provide novel connections to existing literature such as the Neural Tangent Kernel and dataset distillation.

### 8 REPRODUCBILITY STATEMENT

This work uses open source datasets and models. Experimental details such as hyperparameters are described in appendix [H.](#page-22-0) We additionally provide code for running the experiments in the supplementary material.

### REFERENCES

- <span id="page-9-2"></span>Martin Abadi, Andy Chu, Ian Goodfellow, H. Brendan McMahan, Ilya Mironov, Kunal Talwar, and Li Zhang. Deep learning with differential privacy. In *Proceedings of the 2016 ACM SIGSAC Conference on Computer and Communications Security*, CCS '16, pp. 308–318, New York, NY, USA, 2016. Association for Computing Machinery. ISBN 9781450341394. doi: 10.1145/2976749.2978318. URL <https://doi.org/10.1145/2976749.2978318>.
- <span id="page-9-6"></span>Kyle Aitken and Guy Gur-Ari. On the asymptotics of wide networks with polynomial activations. *ArXiv*, abs/2006.06687, 2020.
- <span id="page-9-5"></span>Sanjeev Arora, Simon S Du, Wei Hu, Zhiyuan Li, Russ R Salakhutdinov, and Ruosong Wang. On exact computation with an infinitely wide neural net. In *Advances in Neural Information Processing Systems*, pp. 8141–8150. Curran Associates, Inc., 2019.
- <span id="page-9-0"></span>Devansh Arpit, Stanisław Jastrzebski, Nicolas Ballas, David Krueger, Emmanuel Bengio, Maxinder S Kanwal, Tegan Maharaj, Asja Fischer, Aaron Courville, Yoshua Bengio, et al. A closer look at memorization in deep networks. In *International conference on machine learning*, pp. 233–242. PMLR, 2017.
- <span id="page-9-9"></span>Igor Babuschkin, Kate Baumli, Alison Bell, Surya Bhupatiraju, Jake Bruce, Peter Buchlovsky, David Budden, Trevor Cai, Aidan Clark, Ivo Danihelka, Claudio Fantacci, Jonathan Godwin, Chris Jones, Ross Hemsley, Tom Hennigan, Matteo Hessel, Shaobo Hou, Steven Kapturowski, Thomas Keck, Iurii Kemaev, Michael King, Markus Kunesch, Lena Martens, Hamza Merzic, Vladimir Mikulik, Tamara Norman, John Quan, George Papamakarios, Roman Ring, Francisco Ruiz, Alvaro Sanchez, Rosalia Schneider, Eren Sezener, Stephen Spencer, Srivatsan Srinivasan, Luyu Wang, Wojciech Stokowiec, and Fabio Viola. The DeepMind JAX Ecosystem, 2020. URL <http://github.com/deepmind>.
- <span id="page-9-7"></span>Eugene Bagdasaryan and Vitaly Shmatikov. Differential privacy has disparate impact on model accuracy. *CoRR*, abs/1905.12101, 2019. URL <http://arxiv.org/abs/1905.12101>.
- <span id="page-9-3"></span>Lucas Bourtoule, Varun Chandrasekaran, Christopher A. Choquette-Choo, Hengrui Jia, Adelin Travers, Baiwu Zhang, David Lie, and Nicolas Papernot. Machine unlearning. CoRR, Travers, Baiwu Zhang, David Lie, and Nicolas Papernot. Machine unlearning. *CoRR*, abs/1912.03817, 2019. URL <http://arxiv.org/abs/1912.03817>.
- <span id="page-9-8"></span>James Bradbury, Roy Frostig, Peter Hawkins, Matthew James Johnson, Chris Leary, Dougal Maclaurin, George Necula, Adam Paszke, Jake VanderPlas, Skye Wanderman-Milne, and Qiao Zhang. JAX: composable transformations of Python+NumPy programs, 2018. URL [http:](http://github.com/google/jax) [//github.com/google/jax](http://github.com/google/jax).
- <span id="page-9-4"></span>Gavin Brown, Mark Bun, Vitaly Feldman, Adam Smith, and Kunal Talwar. When is memorization of irrelevant training data necessary for high-accuracy learning? In *Proceedings of the 53rd Annual ACM SIGACT Symposium on Theory of Computing*, pp. 123–132, 2021.
- <span id="page-9-1"></span>Nicholas Carlini, Chang Liu, Ulfar Erlingsson, Jernej Kos, and Dawn Song. The secret sharer: ´ Evaluating and testing unintended memorization in neural networks. In *Proceedings of the 28th USENIX Conference on Security Symposium*, SEC'19, pp. 267–284, USA, 2019. USENIX Association. ISBN 9781939133069.

- <span id="page-10-8"></span>Nicholas Carlini, Florian Tramer, Eric Wallace, Matthew Jagielski, Ariel Herbert-Voss, Katherine ` Lee, Adam Roberts, Tom B. Brown, Dawn Song, Ulfar Erlingsson, Alina Oprea, and Colin Raf- ´ fel. Extracting training data from large language models. *CoRR*, abs/2012.07805, 2020. URL <https://arxiv.org/abs/2012.07805>.
- <span id="page-10-4"></span>Nicholas Carlini, Steve Chien, Milad Nasr, Shuang Song, Andreas Terzis, and Florian Tramer. Mem- ` bership inference attacks from first principles. *CoRR*, abs/2112.03570, 2021. URL [https:](https://arxiv.org/abs/2112.03570) [//arxiv.org/abs/2112.03570](https://arxiv.org/abs/2112.03570).
- <span id="page-10-10"></span>Nicholas Carlini, Matthew Jagielski, Chiyuan Zhang, Nicolas Papernot, Andreas Terzis, and Florian Tramer. The privacy onion effect: Memorization is relative. In S. Koyejo, S. Mohamed, A. Agarwal, D. Belgrave, K. Cho, and A. Oh (eds.), *Advances in Neural Information Processing Systems*, volume 35, pp. 13263–13276. Curran Associates, Inc., 2022. URL [https://proceedings.neurips.cc/paper\\_files/paper/2022/](https://proceedings.neurips.cc/paper_files/paper/2022/file/564b5f8289ba846ebc498417e834c253-Paper-Conference.pdf) [file/564b5f8289ba846ebc498417e834c253-Paper-Conference.pdf](https://proceedings.neurips.cc/paper_files/paper/2022/file/564b5f8289ba846ebc498417e834c253-Paper-Conference.pdf).
- <span id="page-10-2"></span>Centers for Medicare & Medicaid Services. The Health Insurance Portability and Accountability Act of 1996 (HIPAA). Online at http://www.cms.hhs.gov/hipaa/, 1996.
- <span id="page-10-9"></span>Lénaïc Chizat, Edouard Oyallon, and Francis R. Bach. On lazy training in differentiable programming. In *NeurIPS*, 2019.
- <span id="page-10-12"></span>Rodrigo de Azevedo (https://math.stackexchange.com/users/339790/rodrigo-de azevedo). Does gradient descent converge to a minimum-norm solution in least-squares problems? Mathematics Stack Exchange. URL <https://math.stackexchange.com/q/3499305>. URL:https://math.stackexchange.com/q/3499305 (version: 2022-02-18).
- <span id="page-10-5"></span>Cynthia Dwork, Frank McSherry, Kobbi Nissim, and Adam Smith. Calibrating noise to sensitivity in private data analysis. In *Proceedings of the Third Conference on Theory of Cryptography*, TCC'06, pp. 265–284, Berlin, Heidelberg, 2006. Springer-Verlag. ISBN **********. doi: 10. 1007/******** 14. URL [https://doi.org/10.1007/********\\_14](https://doi.org/10.1007/********_14).
- <span id="page-10-3"></span>European Commission. Regulation (EU) 2016/679 of the European Parliament and of the Council of 27 April 2016 on the protection of natural persons with regard to the processing of personal data and on the free movement of such data, and repealing Directive 95/46/EC (General Data Protection Regulation) (Text with EEA relevance), 2016. URL [https://eur-lex.europa.](https://eur-lex.europa.eu/eli/reg/2016/679/oj) [eu/eli/reg/2016/679/oj](https://eur-lex.europa.eu/eli/reg/2016/679/oj).
- <span id="page-10-1"></span>Vitaly Feldman. Does learning require memorization? a short tale about a long tail. In *Proceedings of the 52nd Annual ACM SIGACT Symposium on Theory of Computing*, STOC 2020, pp. 954–959, New York, NY, USA, 2020. Association for Computing Machinery. ISBN 9781450369794. doi: 10.1145/3357713.3384290. URL <https://doi.org/10.1145/3357713.3384290>.
- <span id="page-10-11"></span>Vitaly Feldman and Chiyuan Zhang. What neural networks memorize and why: Discovering the long tail via influence estimation. *CoRR*, abs/2008.03703, 2020a. URL [https://arxiv.](https://arxiv.org/abs/2008.03703) [org/abs/2008.03703](https://arxiv.org/abs/2008.03703).
- <span id="page-10-0"></span>Vitaly Feldman and Chiyuan Zhang. What neural networks memorize and why: Discovering the long tail via influence estimation. In *Proceedings of the 34th International Conference on Neural Information Processing Systems*, NIPS'20, Red Hook, NY, USA, 2020b. Curran Associates Inc. ISBN 9781713829546.
- <span id="page-10-7"></span>Stanislav Fort, Gintare Karolina Dziugaite, Mansheej Paul, Sepideh Kharaghani, Daniel M. Roy, and Surya Ganguli. Deep learning versus kernel learning: an empirical study of loss landscape geometry and the time evolution of the neural tangent kernel. In *NeurIPS*, 2020. URL [https://proceedings.neurips.cc/paper/2020/hash/](https://proceedings.neurips.cc/paper/2020/hash/405075699f065e43581f27d67bb68478-Abstract.html) [405075699f065e43581f27d67bb68478-Abstract.html](https://proceedings.neurips.cc/paper/2020/hash/405075699f065e43581f27d67bb68478-Abstract.html).
- <span id="page-10-6"></span>Matt Fredrikson, Somesh Jha, and Thomas Ristenpart. Model inversion attacks that exploit confidence information and basic countermeasures. In *Proceedings of the 22nd ACM SIGSAC conference on computer and communications security*, pp. 1322–1333, 2015.

- <span id="page-11-10"></span>Arthur Gretton, Karsten M. Borgwardt, Malte J. Rasch, Bernhard Scholkopf, and Alexander Smola. ¨ A kernel two-sample test. *J. Mach. Learn. Res.*, 13(null):723–773, mar 2012. ISSN 1532-4435.
- <span id="page-11-0"></span>Niv Haim, Gal Vardi, Gilad Yehudai, michal Irani, and Ohad Shamir. Reconstructing training data from trained neural networks. In Alice H. Oh, Alekh Agarwal, Danielle Belgrave, and Kyunghyun Cho (eds.), *Advances in Neural Information Processing Systems*, 2022. URL <https://openreview.net/forum?id=Sxk8Bse3RKO>.
- <span id="page-11-7"></span>Boris Hanin and Mihai Nica. Finite depth and width corrections to the neural tangent kernel. In *International Conference on Learning Representations*, 2020. URL [https://openreview.](https://openreview.net/forum?id=SJgndT4KwB) [net/forum?id=SJgndT4KwB](https://openreview.net/forum?id=SJgndT4KwB).
- <span id="page-11-3"></span>Zecheng He, Tianwei Zhang, and Ruby B. Lee. Model inversion attacks against collaborative inference. In *Proceedings of the 35th Annual Computer Security Applications Conference*, ACSAC '19, pp. 148–162, New York, NY, USA, 2019. Association for Computing Machinery. ISBN 9781450376280. doi: 10.1145/3359789.3359824. URL [https://doi.org/10.1145/](https://doi.org/10.1145/3359789.3359824) [3359789.3359824](https://doi.org/10.1145/3359789.3359824).
- <span id="page-11-12"></span>Jonathan Heek, Anselm Levskaya, Avital Oliver, Marvin Ritter, Bertrand Rondepierre, Andreas Steiner, and Marc van Zee. Flax: A neural network library and ecosystem for JAX, 2020. URL <http://github.com/google/flax>.
- <span id="page-11-1"></span>Arthur Jacot, Franck Gabriel, and Clement Hongler. Neural tangent kernel: Convergence and generalization in neural networks. In S. Bengio, H. Wallach, H. Larochelle, K. Grauman, N. Cesa-Bianchi, and R. Garnett (eds.), *Advances in Neural Information Processing Systems*, volume 31. Curran Associates, Inc., 2018. URL [https://proceedings.neurips.cc/](https://proceedings.neurips.cc/paper/2018/file/5a4be1fa34e62bb8a6ec6b91d2462f5a-Paper.pdf) [paper/2018/file/5a4be1fa34e62bb8a6ec6b91d2462f5a-Paper.pdf](https://proceedings.neurips.cc/paper/2018/file/5a4be1fa34e62bb8a6ec6b91d2462f5a-Paper.pdf).
- <span id="page-11-5"></span>Ziwei Ji and Matus Telgarsky. Directional convergence and alignment in deep learning. In H. Larochelle, M. Ranzato, R. Hadsell, M.F. Balcan, and H. Lin (eds.), *Advances in Neural Information Processing Systems*, volume 33, pp. 17176–17186. Curran Associates, Inc., 2020. URL [https://proceedings.neurips.cc/paper/2020/file/](https://proceedings.neurips.cc/paper/2020/file/c76e4b2fa54f8506719a5c0dc14c2eb9-Paper.pdf) [c76e4b2fa54f8506719a5c0dc14c2eb9-Paper.pdf](https://proceedings.neurips.cc/paper/2020/file/c76e4b2fa54f8506719a5c0dc14c2eb9-Paper.pdf).
- <span id="page-11-13"></span>Diederik P. Kingma and Jimmy Ba. Adam: A method for stochastic optimization. In Yoshua Bengio and Yann LeCun (eds.), *3rd International Conference on Learning Representations, ICLR 2015, San Diego, CA, USA, May 7-9, 2015, Conference Track Proceedings*, 2015. URL [http:](http://arxiv.org/abs/1412.6980) [//arxiv.org/abs/1412.6980](http://arxiv.org/abs/1412.6980).
- <span id="page-11-2"></span>Pang Wei Koh and Percy Liang. Understanding black-box predictions via influence functions. In *Proceedings of the 34th International Conference on Machine Learning - Volume 70*, ICML'17, pp. 1885–1894. JMLR.org, 2017.
- <span id="page-11-14"></span>Ya Le and Xuan S. Yang. Tiny imagenet visual recognition challenge. 2015. URL [https:](https://api.semanticscholar.org/CorpusID:16664790) [//api.semanticscholar.org/CorpusID:16664790](https://api.semanticscholar.org/CorpusID:16664790).
- <span id="page-11-6"></span>Jaehoon Lee, Lechao Xiao, Samuel Schoenholz, Yasaman Bahri, Roman Novak, Jascha Sohl-Dickstein, and Jeffrey Pennington. Wide neural networks of any depth evolve as linear models under gradient descent. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-11-9"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *Advances in Neural Information Processing Systems*, 2022a.
- <span id="page-11-8"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Evolution of neural tangent kernels under benign and adversarial training. In *Advances in Neural Information Processing Systems*, 2022b.
- <span id="page-11-4"></span>Kaifeng Lyu and Jian Li. Gradient descent maximizes the margin of homogeneous neural networks. In *International Conference on Learning Representations*, 2020. URL [https://](https://openreview.net/forum?id=SJeLIgBKPS) [openreview.net/forum?id=SJeLIgBKPS](https://openreview.net/forum?id=SJeLIgBKPS).
- <span id="page-11-11"></span>Sadhika Malladi, Alexander Wettig, Dingli Yu, Danqi Chen, and Sanjeev Arora. A kernel-based view of language model fine-tuning, 2023. URL [https://openreview.net/forum?id=](https://openreview.net/forum?id=erHaiO9gz3m) [erHaiO9gz3m](https://openreview.net/forum?id=erHaiO9gz3m).

- <span id="page-12-0"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. In *International Conference on Learning Representations*, 2021a. URL [https:](https://openreview.net/forum?id=l-PrrQrK0QR) [//openreview.net/forum?id=l-PrrQrK0QR](https://openreview.net/forum?id=l-PrrQrK0QR).
- <span id="page-12-1"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *Thirty-Fifth Conference on Neural Information Processing Systems*, 2021b. URL <https://openreview.net/forum?id=hXWPpJedrVP>.
- <span id="page-12-12"></span>Roman Novak, Lechao Xiao, Jiri Hron, Jaehoon Lee, Alexander A. Alemi, Jascha Sohl-Dickstein, and Samuel S. Schoenholz. Neural tangents: Fast and easy infinite neural networks in python. In *International Conference on Learning Representations*, 2020. URL [https://openreview.](https://openreview.net/forum?id=SklD9yrFPS) [net/forum?id=SklD9yrFPS](https://openreview.net/forum?id=SklD9yrFPS).
- <span id="page-12-13"></span>Roman Novak, Jascha Sohl-Dickstein, and Samuel S. Schoenholz. Fast finite width neural tangent kernel. In Kamalika Chaudhuri, Stefanie Jegelka, Le Song, Csaba Szepesvari, Gang ´ Niu, and Sivan Sabato (eds.), *International Conference on Machine Learning, ICML 2022, 17- 23 July 2022, Baltimore, Maryland, USA*, volume 162 of *Proceedings of Machine Learning Research*, pp. 17018–17044. PMLR, 2022. URL [https://proceedings.mlr.press/](https://proceedings.mlr.press/v162/novak22a.html) [v162/novak22a.html](https://proceedings.mlr.press/v162/novak22a.html).
- <span id="page-12-3"></span>Maria Rigaki and Sebastian Garcia. A survey of privacy attacks in machine learning. *CoRR*, abs/2007.07646, 2020. URL <https://arxiv.org/abs/2007.07646>.
- <span id="page-12-2"></span>Reza Shokri, Marco Stronati, and Vitaly Shmatikov. Membership inference attacks against machine learning models. *CoRR*, abs/1610.05820, 2016. URL [http://arxiv.org/abs/1610.](http://arxiv.org/abs/1610.05820) [05820](http://arxiv.org/abs/1610.05820).
- <span id="page-12-9"></span>Hyounguk Shon, Janghyeon Lee, Seung Hwan Kim, and Junmo Kim. DLCFT: deep linear continual fine-tuning for general incremental learning. *CoRR*, abs/2208.08112, 2022. doi: 10.48550/arXiv. 2208.08112. URL <https://doi.org/10.48550/arXiv.2208.08112>.
- <span id="page-12-11"></span>Ben Sorscher, Robert Geirhos, Shashank Shekhar, Surya Ganguli, and Ari S. Morcos. Beyond neural scaling laws: beating power law scaling via data pruning. *ArXiv*, abs/2206.14486, 2022. URL <https://api.semanticscholar.org/CorpusID:250113273>.
- <span id="page-12-7"></span>Bharath K. Sriperumbudur, Kenji Fukumizu, and Gert R.G. Lanckriet. Universality, characteristic kernels and rkhs embedding of measures. *Journal of Machine Learning Research*, 12(70):2389– 2410, 2011. URL <http://jmlr.org/papers/v12/sriperumbudur11a.html>.
- <span id="page-12-5"></span>Nikolaos Tsilivis and Julia Kempe. What can the neural tangent kernel tell us about adversarial robustness? In Alice H. Oh, Alekh Agarwal, Danielle Belgrave, and Kyunghyun Cho (eds.), *Advances in Neural Information Processing Systems*, 2022. URL [https://openreview.](https://openreview.net/forum?id=KBUgVv8z7OA) [net/forum?id=KBUgVv8z7OA](https://openreview.net/forum?id=KBUgVv8z7OA).
- <span id="page-12-6"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-10"></span>P. Welinder, S. Branson, T. Mita, C. Wah, F. Schroff, S. Belongie, and P. Perona. Caltech-UCSD Birds 200. Technical Report CNS-TR-2010-001, California Institute of Technology, 2010.
- <span id="page-12-14"></span>Matthias Wright. Flax models. <https://github.com/matthias-wright/flaxmodels>, 2022.
- <span id="page-12-4"></span>Ziqi Yang, Jiyi Zhang, Ee-Chien Chang, and Zhenkai Liang. Neural network inversion in adversarial setting via background knowledge alignment. In *Proceedings of the 2019 ACM SIGSAC Conference on Computer and Communications Security*, CCS '19, pp. 225–240, New York, NY, USA, 2019. Association for Computing Machinery. ISBN 9781450367479. doi: 10.1145/3319535.3354261. URL <https://doi.org/10.1145/3319535.3354261>.
- <span id="page-12-8"></span>Luca Zancato, Alessandro Achille, Avinash Ravichandran, Rahul Bhotika, and Stefano Soatto. Predicting training time without training. In H. Larochelle, M. Ranzato, R. Hadsell, M.F. Balcan, and H. Lin (eds.), *Advances in Neural Information Processing Systems*, volume 33, pp. 6136–6146. Curran Associates, Inc., 2020. URL [https://proceedings.neurips.cc/paper\\_](https://proceedings.neurips.cc/paper_files/paper/2020/file/440e7c3eb9bbcd4c33c3535354a51605-Paper.pdf) [files/paper/2020/file/440e7c3eb9bbcd4c33c3535354a51605-Paper.pdf](https://proceedings.neurips.cc/paper_files/paper/2020/file/440e7c3eb9bbcd4c33c3535354a51605-Paper.pdf).

- <span id="page-13-2"></span>Amir Zandieh, Insu Han, Haim Avron, Neta Shoham, Chaewon Kim, and Jinwoo Shin. Scaling neural tangent kernels via sketching and random features. In A. Beygelzimer, Y. Dauphin, P. Liang, and J. Wortman Vaughan (eds.), *Advances in Neural Information Processing Systems*, 2021. URL <https://openreview.net/forum?id=vIRFiA658rh>.
- <span id="page-13-0"></span>Chiyuan Zhang, Samy Bengio, Moritz Hardt, Benjamin Recht, and Oriol Vinyals. Understanding deep learning requires rethinking generalization. In *International Conference on Learning Representations*, 2017. URL <https://openreview.net/forum?id=Sy8gdB9xx>.
- <span id="page-13-1"></span>Chiyuan Zhang, Samy Bengio, Moritz Hardt, Benjamin Recht, and Oriol Vinyals. Understanding deep learning (still) requires rethinking generalization. *Commun. ACM*, 64(3):107–115, feb 2021. ISSN 0001-0782. doi: 10.1145/3446776. URL <https://doi.org/10.1145/3446776>.
- <span id="page-13-4"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. *arXiv preprint arXiv:2102.08259*, 2021.
- <span id="page-13-3"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021. URL [https://openreview.](https://openreview.net/forum?id=mSAKhLYLSsl) [net/forum?id=mSAKhLYLSsl](https://openreview.net/forum?id=mSAKhLYLSsl).
- <span id="page-13-5"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022.
- <span id="page-13-7"></span>Yufan Zhou, Zhenyi Wang, Jiayi Xian, Changyou Chen, and Jinhui Xu. Meta-learning with neural tangent kernels. *CoRR*, abs/2102.03909, 2021. URL [https://arxiv.org/abs/2102.](https://arxiv.org/abs/2102.03909) [03909](https://arxiv.org/abs/2102.03909).
- <span id="page-13-6"></span>Ligeng Zhu, Zhijian Liu, , and Song Han. Deep leakage from gradients. In *Annual Conference on Neural Information Processing Systems (NeurIPS)*, 2019.

## **APPENDIX**

<span id="page-14-0"></span>

### A COMPARISON TO H[AIM ET AL](#page-11-0). [\(2022\)](#page-11-0)

In section [3,](#page-2-1) we mentioned that we had problems reproducing [Haim et al.](#page-11-0) [\(2022\)](#page-11-0)'s attack. Here, we compare the two attacks and discuss the issues we found with theirs.

Firstly, we compare the quality of the two attacks. [Haim et al.](#page-11-0) [\(2022\)](#page-11-0) open sourced their code, as well as gave the best two sets of reconstructions for both CIFAR-10 and MNIST-10. We plot the reconstruction curves of these reconstructions here:

Image /page/14/Figure/4 description: The image contains two plots side-by-side, both with the x-axis labeled "Image Index" ranging from 0 to 500, and the y-axis labeled "L2 Distance" ranging from 0.0 to 15.0 on the left and 5.0 to 25.0 on the right. The left plot is titled "MNIST Odd vs. Even" and shows several colored lines representing different reconstruction attacks, with a training set size of 500. The right plot is titled "CIFAR-10 Animal vs. Vehicle" and also shows several colored lines representing different reconstruction attacks, with a training set size of 500. A legend on the right indicates that the green line represents "Haim et al. 1", the orange line represents "Haim et al. 2", and the purple line represents "Ours, w = 1024". The other lighter colored lines likely represent additional or baseline methods.

Figure 10: Reconstruction curves for the attacks given in [Haim et al.](#page-11-0) [\(2022\)](#page-11-0), in comparison to our reconstruction attacks, with a comparable width of 1024.

In their paper, they consider networks of a width 1000, so for a fair comparison, we highlight our reconstruction with a comparable width of 1024, under standard dynamics. We see that for MNIST, our attack has significantly better quality until image index 50 in which case both attacks perform poorly. For CIFAR-10, our attack performs worse but can achieve better performance with wider width or linearization.

Note that the two reconstruction curves presented from [Haim et al.](#page-11-0) [\(2022\)](#page-11-0) correspond to the two *best* reconstructions with carefully chosen hyperparameters. These hyperparameters are chosen to maximize reconstruction quality, which requires access to the training data to measure the quality. A priori, an attacker would not be able to assess reconstruction quality, and thus would not be able to do such hyperparameter tuning. In contrast, attack parameters such as learning rate/initialization were not fine-tuned for ours, and we use the same hyperparameters for every attack. Further gains could likely be seen with more careful tuning. We would like to emphasize that the goal of this work is not necessarily to create the strongest attack, but more so to explore the properties of the attack, and conditions for failure/success.

A further limitation of their attack is that it requires homogenous neural networks (i.e. no biases), in comparison to ours which uses biases, which is closer to practice. The largest limitation of their attack is that they require a careful initialization scheme, in which the weights of the first layer are initialized with significantly smaller variance. In [Haim et al.](#page-11-0) [\(2022\)](#page-11-0) they discuss that this is **essential** to the success of their attack. In contrast, we use the default initialization given in the Flax neural network library [\(Heek et al., 2020\)](#page-11-12).

We also observed that the KKT condition given in eq. [\(1\)](#page-1-0), which is required for their attack to work often is not reached in practice. To reiterate the definition of directional convergence, it requires that  $\lim_{t\to\infty} \frac{\theta}{\|\theta\|_2} \to \frac{\theta'}{\|\theta'\|_2}$  $\frac{\theta'}{\|\theta'\|_2}$  for trained network parameters  $\theta$ , and  $\theta'$  the solution to eq. [\(1\)](#page-1-0). It is clear that if  $\frac{\|\theta-\theta_0\|_2^2}{\|\theta_0\|_2^2}$  < 1, i.e. the parameters have not drifted far from their initialization, then, we cannot hope the KKT point is reached (of course, with unlikely exceptions such as  $\theta_0$  already being close to the KKT point). In practice, we found that  $\|\theta_0\|_2^2 \approx 333$  and  $\|\theta\|_2^2 \approx 377$ , when running their attack, which suggests that the initialization still contributes a significant amount to the final parameters, suggesting that the KKT was not reached. Of course, our attack is not without limitations as well,

the most notable being that we require network initialization. We leave it to future work to alleviate this requirement.

<span id="page-15-0"></span>

### B RECONSTRUCTION ATTACK ALGORITHM DETAILS

#### Algorithm 1 Standard Reconstruction Attack

<span id="page-15-1"></span>**Require:** Initial Parameters  $\theta_0$ , final parameters  $\theta_f$ , network function  $f_\theta$ , randomly initialized reconstruction images and dual parameters  $\{X_R, \alpha_R\}$ , optimizer  $\Diamond p \text{t}$  im (params, gradients, number of steps T  $\Delta\theta = \theta_f - \theta_0$  $t \leftarrow 1$ while  $t < T$  do  $G \leftarrow \sum_i \alpha_i \nabla_\theta f_{\theta_f}(x_i)$  for  $\alpha_i$ ⊳ Compute reconstruction gradient  $\mathcal{L}_{\text{recon}} = ||\Delta \theta - \dot{G}||_2^2$ <sup>2</sup> ▷ Compute reconstruction loss  $\alpha_R, X_R \leftarrow \text{Optim}\left(\{\alpha_R, X_R\}, \frac{\partial L_{recon}}{\partial \{\alpha_R, X_R\}}\right)$ ▷ Update Reconstruction Images  $t \leftarrow t + 1$ end while

#### Algorithm 2 Batched Reconstruction Attack

<span id="page-15-2"></span>**Require:** Initial Parameters  $\theta_0$ , final parameters  $\theta_f$ , network function  $f_\theta$ , randomly initialized reconstruction images and dual parameters  $\{X_R, \alpha_R\}$ , optimizer Optim(params, gradients, number of steps T, batch size  $|B|$  $\Delta\theta = \theta_f - \theta_0$  $G_R = \sum_i \alpha_i \nabla_{\theta} f_{\theta_f}(x_i)$  for  $\alpha_i, x_i \in \alpha_R, X_R \to \text{Compute total reconstruction gradient (this step)}$ can also be batched) while  $t < T$  do Sample batch  $\alpha_B, X_B \subset \alpha_R, X_R$  of batch size |B| uniformly  $G_B \leftarrow \sum_i \alpha_i \nabla_{\theta} f_{\theta_f}(x_i)$  for  $\alpha_i, x_i \in \alpha_B, X_B \quad \triangleright$  Compute reconstruction gradient for batch  $G_{B,old} \leftarrow \text{detach}(G_B)$   $\triangleright$  Store old batch gradient  $\mathcal{L}_{\text{recon}} \leftarrow ||\Delta \theta - (G_R - G_{B,old} + G_B)||_2^2$ <sup>2</sup> ▷ Compute reconstruction loss  $\alpha_B, X_B \leftarrow \text{Optim}(\{\alpha_B, X_B\}, \partial L_{recon}/\partial \{\alpha_B, X_B\}) \qquad \Rightarrow \text{Optimize batch images}$  $G_{B,new} \leftarrow \sum_i \alpha_i \nabla_\theta f_{\theta_f}(x_i)$  for  $\alpha_i$  $\triangleright$  Compute new batch gradient  $G_R \leftarrow G_R - G_{B,old} + \text{detach}(G_{B,new})$   $\rightarrow$  Update total reconstruction gradient  $t \leftarrow t + 1$ end while

Here we discuss the runtime of our attack, given a model with  $P$  parameters,  $M$  reconstruction images, and  $T$  iterations. We present two versions of the attack: the standard version of the attack, given in algorithm [1,](#page-15-1) and a minibatched version of the attack algorithm [2.](#page-15-2) Both versions of the attack are mathetmatically equivalent, but the batched version allows for larger reconstruction sets that may not fit into memory all at once.

For the standard version of the attack presented in algorithm [1,](#page-15-1)  $O(MPT)$  time is required and  $O(MP)$  memory is required, as naively one needs to store (and backpropagate through) gradients for each of the reconstructed images. Evidently for large datasets, one cannot pass the whole dataset through the model at once and backpropagate, and as we need  $M > N$  to reconstruct the full dataset, this seems problematic. For datasets in our paper, this was not a concern, but for larger datasets it would be.

To mitigate this issue, we present a minibatched version of the attack in algorithm [2,](#page-15-2) which requires  $O(BPT)$  time and  $O(BP)$  memory. The premise of this version that you store the a buffered value of the total sum of gradients  $G_R = \sum_{i=1}^{M} g_i$  over reconstruction examples, and by carefully using autodiff, you can update only a subset of the gradients (using the buffered value of  $G_R$  and subtracting the batch gradients). In algorithm [2,](#page-15-2) the detach function refers to the autodiff graph detachment function common in most autodiff libraries. This method has been implemented and performs exactly the same as the original attack, however the results in this paper do not require it as we dealt with small N.

<span id="page-16-0"></span>

### C LABEL CONDITIONS FOR FULL RECOVERY

As discussed in section [3,](#page-2-1) we require that  $\alpha_i \neq 0$  in order to recover the training image. Here we discuss the conditions for this to occur. We know that  $\alpha = K^{-1}y$ , and without loss of generality, consider reconstructing the final image at index  $N$ . Our equation for  $\alpha$  becomes, focusing on the value of  $\alpha_N$ :

$$
\begin{split} &\begin{bmatrix} \alpha_{:N-1} \\ \alpha_{N} \end{bmatrix} = \begin{bmatrix} K_{:N-1, i,N-1} & K_{:N-1, N} \\ K_{:N-1, N}^{\mathsf{T}} & K_{N, N} \end{bmatrix}^{-1} \begin{bmatrix} y_{:N-1} \\ y_{N} \end{bmatrix} \\ &\begin{bmatrix} \alpha_{:N-1} \\ \alpha_{N} \end{bmatrix} = \begin{bmatrix} K_{:N-1, i,N-1}^{-1} + K_{:N-1, i,N-1}^{-1} K_{:N-1, i,N} Q K_{:N-1, i,N-1}^{\mathsf{T}} & -K_{:N-1, i,N-1}^{-1} K_{:N-1, N} Q \\ -Q K_{:N-1, i,N}^{\mathsf{T}} K_{:N-1, i,N-1}^{-1} & Q \end{bmatrix}^{-1} \begin{bmatrix} y_{:N-1} \\ y_{N} \end{bmatrix} \end{split}
$$

With  $Q = (K_{N,N} - K_{N-1,N} K_{N-1,N-1}^{-1} K_{N-1,N}^{\mathsf{T}})^{-1}$ . Setting  $\alpha_N = 0$ :

$$
0 = -QK_{;N-1,N}^T K_{;N-1;N-1}^{-1} y_{;N-1} + Qy_N
$$
  
$$
y_N = K_{;N-1,N}^T K_{;N-1;N-1}^{-1} y_{;N-1}
$$

Noting  $K_{:N-1,N}^T K_{:N-1,:N-1}^{-1} y_{:N-1}$  corresponds to the kernel regression prediction of  $y_N$  given  $y_{N-1}$ , we see that  $\alpha_N = 0$  iff  $x_N$  is already perfectly predicted by the remaining data points. Of course, the label corresponding exactly to the prediction occurs with probability 0.

#### <span id="page-16-1"></span>C.1 ALTERNATIVE INTERPRETATION OF $\alpha$

In section [6,](#page-6-1) we discussed how  $\alpha$  parameters can be treated as items which are "hard" vs "easy" to fit. Here we derive how  $\alpha_j = \int_0^\infty (y_j - f_{\theta_t}(x_j)) dt$  for MSE loss (up to some scaling parameter).

$$
\mathcal{L}(\theta_t) = \frac{1}{2} \sum_i (y_i - f_{\theta_t}(x_i))^2
$$
  
\n
$$
\frac{\partial \theta_t}{\partial t} = -\eta \frac{\partial \mathcal{L}(\theta_t)}{\partial \theta_t}
$$
  
\n
$$
\frac{\partial \theta_t}{\partial t} = -\eta \sum_i (y_i - f_{\theta_t}(x_i)) \frac{\partial f_{\theta_t}(x_i)}{\partial \theta_t}
$$
  
\n
$$
\Delta \theta = \int_0^\infty \frac{\partial \theta_t}{\partial t} dt
$$
  
\n
$$
= \int_0^\infty -\eta \sum_i (y_i - f_{\theta_t}(x_i)) \frac{\partial f_{\theta_t}(x_i)}{\partial \theta_t} dt
$$
  
\n
$$
= -\eta \sum_i \left[ \int_0^\infty (y_i - f_{\theta_t}(x_i)) \frac{\partial f_{\theta_t}(x_i)}{\partial \theta_t} dt \right]
$$
  
\n
$$
\approx -\eta \sum_i \left[ \int_0^\infty (y_i - f_{\theta_t}(x_i)) dt \frac{\partial f_{\theta_0}(x_i)}{\partial \theta_0} \right]
$$
 (Frozen Kernel approximation)

Noting that  $\Delta \theta = \sum_i \alpha_i \frac{\partial f_{\theta_0}(x_i)}{\partial \theta_0}$  $\frac{\theta_0(x_i)}{\partial \theta_0}$  (eq. [\(5\)](#page-3-0)) and matching terms, also noting that  $P > T$  (more parameters than training points), we have the system is uniquely solved when  $\alpha_i = -\eta \int_{0}^{\infty} (y_j$  $f_{\theta_t}(x_j)$ )dt. We can verify this experimentally, by plotting the calculated values of  $\alpha_i$  vs.  $\int_0^\infty (y_j - \alpha_i) e^{i\theta}$  $f_{\theta_t}(x_j)$ )dt in fig. [11.](#page-17-1)

Image /page/17/Figure/0 description: The image displays two rows of scatter plots, each row representing a different dataset and task. The top row, titled "MNIST Odd vs. Even (w = 4096)", contains four scatter plots. Each plot shows the relationship between alpha (α) on the x-axis and the integral of (yi - f(xi))dt on the y-axis. The plots correspond to different numbers of samples: 20, 100, 200, and 500. The y-axis scales vary across these plots, ranging from approximately -0.01 to 0.01 for 20 samples, -0.10 to 0.10 for 100 samples, -0.2 to 0.2 for 200 samples, and -0.4 to 0.4 for 500 samples. The bottom row, titled "CIFAR-10 Animal vs. Vehicle (w = 4096)", also contains four scatter plots with similar axes and sample sizes. The y-axis scales in this row range from approximately -0.03 to 0.03 for 20 samples, -0.10 to 0.10 for 100 samples, -0.2 to 0.2 for 200 samples, and -0.6 to 0.4 for 500 samples. The x-axis for the MNIST plots ranges from -20 to 20 or 0 to 40, while the CIFAR-10 plots have x-axis ranges from -50 to 50.

<span id="page-17-1"></span>Figure 11: Infinite width values of  $\alpha$  vs. error integral formulation of error calculated from 4096width finite networks. There is a strong correlation between the two values.

<span id="page-17-0"></span>

### D PROOF OF THEOREM 2: RKIP DERIVATION FROM RECONSTRUCTION LOSS

*Proof.* Here we derive how one gets from the reconstruction loss given in eq. [\(7\)](#page-3-2) to theorem [2,](#page-7-1) the RKIP loss. We repeat the losses here:

$$
\mathcal{L}_{\text{Reconstruction}} = \|\Delta \theta - \alpha^{\mathsf{T}} \nabla_{\theta} f_{\theta_0}(X_T)\|_2^2
$$
$$
\mathcal{L}_{\text{RKP}} = \|y_T - K_{TR} K_{RR}^{-1} y_R\|_{K_{TT}}^2
$$

First we note that  $\alpha^T = K_{TT}^{-1} y_T$ , and if we trained on reconstructions with labels  $y_R$ , then  $\alpha^R =$  $K_{RR}^{-1}y_R$ . For brevity we denote  $s_i^* = {\alpha_i^*, x_i^*}$  and  $S^* = {\alpha^*, X_*}$ . Let  $S^T$  and  $S^R$  denote the training and reconstruction set, respectively

$$
\|\Delta \theta - \sum_{s_j^R \in S^R} \alpha_j^R \nabla_{\theta_f} f_{\theta_f}(x_j^R) \|^2_2
$$
  
\n
$$
= \left\| \sum_{s_i^T \in S^T} \alpha_i^T \nabla_{\theta_0} f(x_i^T) - \sum_{s_j^R \in S_R} \alpha_j^R \nabla_{\theta_f} f_{\theta_f}(x_j^R) \right\|_2^2
$$
  
\n
$$
= \left\| \sum_{s_i^T \in S^T} \alpha_i^T k_{\theta_0}(x_i^T, \cdot) - \sum_{s_j^R \in S_R} \alpha_j^R k_{\theta_f}(x_j^R, \cdot) \right\|_2^2
$$

Again take the infinite width limit so  $k_{\theta_0}, k_{\theta_f} \to k_{NTK}$ , we which just write k for simplicity.

$$
\begin{split} \Big\| \sum_{s_{i}^{T} \in S} & \alpha_{i}^{T} k_{\theta_{0}}(x_{i}^{T},\cdot) - \sum_{s_{j}^{R}} \alpha_{j}^{R} k_{\theta_{f}}(x_{j}^{R},\cdot) \Big\|_{2}^{2} \to \Big\| \sum_{s_{i}^{T} \in S} & \alpha_{i}^{T} k(x_{i}^{T},\cdot) - \sum_{s_{j}^{R}} \alpha_{j}^{R} k(x_{j}^{R},\cdot) \Big\|_{2}^{2} \\ & = \sum_{s_{i}^{T} \in S} & \sum_{s_{i}^{T} \in S} \alpha_{i}^{T} \alpha_{j}^{T} k(x_{i}^{T},x_{j}^{T}) - 2 \sum_{s_{i}^{T} \in S} \sum_{s_{j}^{R} \in S} \alpha_{i}^{T} \alpha_{j}^{R} k(x_{i}^{T},x_{j}^{R}) \\ & + \sum_{s_{i}^{R} \in S} \sum_{s_{j}^{R} \in S} \alpha_{i}^{R} \alpha_{j}^{R} k(x_{i}^{R},x_{j}^{R}) \\ & = \alpha^{T} \mathsf{T} K_{TT} \alpha^{T} - 2 \alpha^{T} \mathsf{T} K_{TR} \alpha^{R} + \alpha^{R} \mathsf{T} K_{RR} \alpha^{R} \\ & = y_{T}^{T} K_{TT}^{-1} K_{TT} K_{TT}^{-1} y_{T} - 2 y_{T}^{T} K_{TT}^{-1} K_{TR} K_{RR}^{-1} y_{R} \ & = y_{T}^{T} K_{TT}^{-1} y_{T} - 2 y_{T}^{T} K_{TT}^{-1} K_{TR} K_{RR}^{-1} y_{R} + y_{R}^{T} K_{RR}^{-1} y_{R} \ & = y_{T}^{T} K_{TT}^{-1} y_{T} - 2 y_{T}^{T} K_{TT}^{-1} K_{TR} K_{RR}^{-1} y_{R} + y_{R}^{T} K_{RR}^{-1} y_{R} \ & = y_{T}^{T} K_{TR}^{-1} y_{T} - 2 y_{T}^{T} K_{TR} K_{RR}^{-1} y_{R} + y_{R}^{T} K_{RR}^{-1} y_{R} \ & = \| y_{T} - K_{TR} K_{RR}^{-1} y_{R} \|_{K_{TT}^{-1}}^{2} + y_{R}^{T} (K_{RR}^{-1} - K_{RR}^{-1} K_{RT} K_{TT}^{-1} K_{TR} K_{RR}^{-1}) y_{R} \ \text{label matching} & \lambda_{\text{var of } R|T} \end{split}
$$

 $\Box$ 

 $\lambda_{\text{var of }R|T}$  is proportional to  $K_{RR} - K_{RT} K_{TT}^{-1} K_{TR}$ , which is  $K_{[T,R],[T,R]}/K_{TT}$ , the Schur complement of  $K_{[T,R],[T,R]}$  with  $K_{TT}$ . Note that this is the Gaussian conditional variance formula, assuming we are making predictions of  $R$  based on  $T$ . This regularizer ensures that not only do the reconstructed images result in the correct predictions (ensured by the label matching term) but also that our distilled dataset images do not deviate significantly from the training distribution, as measured by the NTK. We hypothesize this term is what contributes to the success of RKIP over KIP in the finite-width setting. As there is nothing that directly ensures that KIP distilled datapoints remain "similar" to training images (only that they are predictive), these distilled images may be more susceptible to domain shift, such as moving from the infinite-width setting to finite width. This interesting behavior could be the subject of future work.

<span id="page-19-0"></span>

### E FINITE RKIP IMAGES

Image /page/19/Picture/1 description: The image displays a grid of images divided into two rows labeled "Reconstructions" and "Train Images." The top row, "Reconstructions," shows 12 blurry, abstract images that appear to be generated or reconstructed versions of the images in the bottom row. The bottom row, "Train Images," displays 12 clear images of various subjects, including birds, a red car, a horse, an airplane, a deer, a frog, a boat, a fire truck, and a fighter jet. The images are arranged in a 2x6 grid for each row.

CIFAR-10 Animal vs. Vehicle reconstructions with  $M = 20 < N = 500$ 

Figure 12: Reconstructing 20 images from a network trained on 500 CIFAR-10 images. Reconstructions often do not match actual training images and contain heavy corruption. Retraining on these images yields high accuracy.

fig. [13](#page-19-1) and fig. [14](#page-20-0) show the resulting reconstructions when reconstructing 20 images from a dataset that may contain up to 500 images. Reconstructions are made from 4096 networks with linearized dynamics.

Image /page/19/Figure/5 description: This figure displays a grid of MNIST digit reconstructions, comparing odd and even digits. The grid is organized into rows representing different dataset sizes (N) ranging from 20 to 500, with increments of 20, 50, 100, 150, 200, 300, 400, and 500. Each row contains two columns: one for 'Train Images' and one for 'Reconstructions'. Within each cell, there are two rows of digits, with the top row showing the original training images and the bottom row showing the reconstructed images. The title for each pair of columns indicates the dataset size N and a fixed parameter M=20. For example, the top-left section shows reconstructions for N=20, the top-right for N=50, the next row for N=100 and N=150, and so on, down to N=400 and N=500. The digits themselves are displayed in a purple-blue color scheme against a black background.

<span id="page-19-1"></span>Figure 13: Reconstructing 20 images from a dataset that may be larger than 20 images (MNIST Odd vs. Even)

Image /page/20/Figure/0 description: The image displays a grid of CIFAR-10 animal and vehicle reconstructions. Each row represents a different value of N, ranging from 20 to 500, with a fixed M of 20. Within each row, there are two columns: 'Reconstructions' and 'Train Images'. The 'Reconstructions' column shows generated images, while the 'Train Images' column displays the original training images. The reconstructions generally become more pixelated and less recognizable as N increases, indicating a potential loss of detail or fidelity in the reconstruction process with larger datasets.

<span id="page-20-0"></span>Figure 14: Reconstructing 20 images from a dataset that may be larger than 20 images (CIFAR-10 Animal vs. Vehicle)

### F MULTICLASS RECONSTRUCTION LOSS

Here we derive the multi-class reconstruction loss which we use in section [4.](#page-4-0) Our least-norm predictor satisfies the following conditioned (assuming that the network behaves in the linear regime):

$$
\arg\min_{\Delta\theta} \frac{1}{2} \|\Delta\theta\|_2^2 \quad s.t. \forall c \in [C], \quad \Delta\theta^{\mathsf{T}} \nabla_{\theta} f_{\theta_0}^c(X_T) = y_T^c - f_{\theta_0}^c(X_T). \tag{10}
$$

With  $f_{\theta_0}^c$  referring to the network output on the cth class and  $y_T^c$  referring to the training labels for the cth class. The network will converge to the least norm solution (norm of the difference from initialization), due to the network behaving in the lazy regime with gradient flow [\(de Azevedo ,](#page-10-12) [https://math.stackexchange.com/users/339790/rodrigo-de azevedo\)](#page-10-12). Writing the equation with dual variables our full Lagrangian is

$$
\mathcal{L}(\Delta\theta,\alpha) = \frac{1}{2} \|\Delta\theta\|_2^2 + \sum_{c=1}^C \alpha^{c\mathsf{T}} \left( \Delta\theta^{\mathsf{T}} \nabla_{\theta} f_{\theta_0}^c(X_T) - (y_T^c - f_{\theta_0}^c(X_T)) \right). \tag{11}
$$

With  $\alpha$  our set of dual parameters  $\in R^{C \times M}$ , that is, we have a set of M dual parameters for each class. Taking derives w.r.t  $\Delta\theta$ :

$$
0 = \nabla_{\Delta\theta} \mathcal{L}(\Delta\theta, \alpha) = \Delta\theta + \sum_{c=1}^{C} \alpha^{c\mathsf{T}} \left( \nabla_{\theta} f_{\theta_0}^c(X_T) \right).
$$
 (12)

So our multiclass reconstruction loss is

$$
\mathcal{L}_{\text{reconstruction}} = \left\| \Delta \theta - \sum_{c=1}^{C} \alpha^{c \mathsf{T}} \nabla_{\theta} f_{\theta_0}^c(X_T) \right\|_2^2.
$$
 (13)

We can use the same argument as in section [3](#page-3-4) to show this attack is exact in the infinite width limit.

<span id="page-21-0"></span>

### G EARLY STOPPING AND CROSS-ENTROPY

#### G.1 EARLY STOPPING

Image /page/21/Figure/5 description: The image displays two sets of line graphs, one for MNIST Odd vs. Even and another for CIFAR-10 Animal vs. Vehicle. Each set contains four subplots, corresponding to different values of 'w': 20, 100, 200, and 500. The x-axis for all subplots is labeled 'Log Training Loss', ranging from -10 to 0. The y-axis for the MNIST graphs is labeled 'Mean Reconstruction Error', ranging from approximately 1 to 22. The y-axis for the CIFAR-10 graphs ranges from approximately 4 to 19. Within each subplot, there are six lines, each representing a different value of 'w' (1, 2, 4, 8, 16, 32), indicated by a color gradient from red (w=1) to blue (w=32). The lines show the trend of mean reconstruction error as a function of log training loss for each 'w' value. Generally, as the log training loss decreases (moves towards 0), the mean reconstruction error tends to decrease for most 'w' values, especially for the CIFAR-10 dataset. The MNIST dataset shows a similar trend, but with some variations. The legend in the CIFAR-10 section clarifies the color coding for different 'w' values.

<span id="page-21-1"></span>Figure 15: Mean reconstruction errors for networks trained to various early stopping losses from  $10^{-1}$  to  $10^{-6}$ 

Image /page/21/Figure/7 description: The image displays two plots. The left plot, titled "MNIST Odd vs. Even Early Stopping (Standard Dynamics)", shows the L2 Distance on the y-axis against the training set size (20, 100, 200, 500) on the x-axis. Multiple lines, colored from red to blue, represent different model widths. The right plot, titled "CIFAR-10 Animal vs. Vehicle Early Stopping (Standard Dynamics)", also shows the L2 Distance on the y-axis against the image index on the x-axis. This plot is divided into three subplots, each with different x-axis scales (0-10, 0-100, 0-200, 0-400). A legend indicates that the lines represent model widths ranging from 256 (red) to 4096 (dark blue).

<span id="page-21-2"></span>Figure 16: Reconstruction curves for networks trained to a loss of 1e-2, i.e. significant underfitting (under standard dynamics). Compared to fig. [2,](#page-3-4) we see that reconstruction quality is unaffected by early stopping, consistent with the theory.

In the main text, we considered models trained with low learning rates for  $10^6$  epochs, so that we achieve the KKT conditions described in section [3.](#page-2-1) In practice, this is computationally expensive, and often ill-advised due to overfitting, with early stopping being a common regularization technique. One major limitation of the attack proposed in [Haim et al.](#page-11-0) [\(2022\)](#page-11-0) is that it requires the network to reach the KKT point to perform the attack. Our method **does not require the model** to reach convergence for the attack to work. Again we note that the time evolution of network parameters is given by:

$$
\Delta\theta(t) = \nabla_{\theta} f_{\theta_0}(X_T)^{\mathsf{T}} \underbrace{K_0^{-1} \left(I - e^{-\eta K_0 t}\right) \left(y_T - f_{\theta_0}(X_T)\right)}_{\text{time-dependent weights, }\alpha(t)}
$$

Notably, even at finite time, the change in network parameters is still a linear combination of the finite-width NTK feature maps of the training set,  $\nabla_{\theta} f_{\theta_0}(X_T)$ , with the indicated *time dependent* weights,  $\alpha(t)$ . Note that the success of the attack in infinite width relies on the injectivity of the kernel measure embedding, not that  $\alpha(t)$  be at its converged value, implying the attack works with early stopping. The caveat with the early stopping attack is that we cannot necessarily hope to recover the original training labels.

We verify that this attack works in practice by repeating the attack procedure in section [4,](#page-4-0) with early stopping. We apply the attack on networks that achieve a mean training loss of from  $10^{-1}$  to  $10^{-8}$  (note that this means that on for  $\{+1, -1\}$  labels, the outputs were around 0.45 off, i.e. quite underfit, in the case of  $\mathcal{L} = 10^{-1}$ ), with results shown in fig. [15,](#page-21-1) with the specific reconstruction curve for  $\mathcal{L} = 10^{-2}$  in fig. [16.](#page-21-2) We observe that early stopping in general *improves* reconstruction quality. We posit that there are two possible reasons for this: firstly, that early in training there is less time for the kernel to evolve, so the network exhibits network dynamics closer to the lazy regime. Secondly, we hypothesize that this could be that early in training all datapoints have a roughly equal

contribution to the parameter changes, whereas later in training, certain datapoints have a stronger influence (see section [6](#page-6-1) and appendix [C.1\)](#page-16-1). When some datapoints have a much stronger influence than others, this could cause the the less influential datapoints to be "drowned out" by the signal of the more influential ones. Future work could study the complex relationship between early stopping, outlier influence, and network vulnerability more closely. We also note that in the limit of a single gradient step, our attack corresponds to *gradient leakage* attacks [\(Zhu et al., 2019\)](#page-13-6).

#### G.2 CROSS ENTROPY LOSS

Following [\(Lee et al., 2019\)](#page-11-6), we have that wide networks trained under cross-entropy loss also exhibit lazy dynamics provided they are of sufficient width. The corresponding ODE is:

$$
\mathcal{L}(\theta_t) = -\sum_i y_i \log \sigma(f_{\theta_t}(x_i))
$$
$$
\frac{\partial \theta_t}{\partial t} = -\eta \frac{\partial \mathcal{L}(\theta_t)}{\partial \theta_t}
$$
$$
\frac{\partial \theta_t}{\partial t} = -\eta \sum_i (\sigma(f_{\theta_t}(x_i) - y_i) \frac{\partial f_{\theta_t}(x_i)}{\partial \theta_t})
$$

Unlike eq. [\(3\)](#page-2-2), there is not a closed form solution to this, however the key point is that  $\Delta\theta$  still is a linear combination of  $\nabla_{\theta} f_{\theta}(x_i)$ .

<span id="page-22-0"></span>

### H EXPERIMENTAL DETAILS

#### H.1 LIBRARIES AND HARDWARE

We use the JAX, Optax, Flax, and neural-tangents libraries [\(Bradbury et al., 2018;](#page-9-8) [Babuschkin et al.,](#page-9-9) [2020;](#page-9-9) [Heek et al., 2020;](#page-11-12) [Novak et al., 2020;](#page-12-12) [2022\)](#page-12-13). All experiments were run on Nvidia Titan RTX graphics cards with 24Gb VRAM.

#### H.2 NETWORK TRAINING

Unless otherwise stated, networks trained on real data are trained for  $10^6$  iterations of full batch gradient descent, with SGD with momentum 0.9. For the learning rate, we set  $\eta = N \times 2e-7$ , where N is the number of training images. For distilled data, we use a learning rate of  $\eta = N \times 6e-6$ , where  $N$  is now the distilled dataset size. We did not find that results were heavily dependent on the learning rates used during training. Additionally, if the training loss was less than 1e−10, we terminated training early. Every reconstruction curve in the main text is the average of 3 unique networks trained on 3 unique splits of training data.

For binary classification, we use labels in  $\{+1, -2\}$ , and for 10-way multiclass classification, we use labels of 0.9 corresponding to the selected class and -0.1 for other classes.

#### H.3 RECONSTRUCTIONS

To create reconstructions, we initialize reconstruction images with a standard deviation of 0.2, and dual parameters to be uniform random within  $[-0.5, 0.5]$ . We use Adam optimizer [\(Kingma & Ba,](#page-11-13) [2015\)](#page-11-13), with a learning rate of 0.02 for all reconstructions. As stated in appendix [A,](#page-14-0) these could be fine-tuned to improve performance. We optimize the images for 80k iterations. Like with [Haim et al.](#page-11-0) [\(2022\)](#page-11-0), we found that it was useful to use a softplus rather than a Relu during reconstruction, owing to the smoothing gradient loss. We annealed  $\sim$  ftplus temperature from 10 to 200 over the course of training, so that we are effectively using ReLUs by the end of training. Unless otherwise stated, we aim to reconstruct  $M = 2N$  reconstruction images, with N the training set size.

#### H.4 DISTILLATION

We initialize distilled images with a standard deviation of 0.2 and distill for 50k iterations with Adam optimizer with a learning rate of 0.001.

#### H.5 FINE-TUNING EXPERIMENTS

For the fine-tuning experiments in section [5,](#page-6-2) we use the pretrained ResNet-18 from flaxmodels [\(Wright, 2022\)](#page-12-14). For these experiments, we used 64-bit training, as we found it improved reconstruction quality. We train for 30000 iterations with a learning rate of  $1e - 5$ . We use SGD with no momentum, and also freeze the batchnorm layers to the parameters used by the initial model. We use the hybrid loss described in appendix [I.](#page-23-0)

#### H.6 PRUNING EXPERIMENTS

For the pruning experiments we train for  $10<sup>5</sup>$  epochs for each training run with a learning rate of  $n\times3e-6$  with SGD with momentum 0.9. For reconstruction we use the same reconstruction attack in the main text for 30000 iterations. For each training iteration we set with width of the network in the main text for 50000 iterations. For each training iteration we set with width of the network to be  $w = 55\sqrt{n}$ , as we found that generally kept the strength of the attack the same for different values of  $n$ . If we make the attack too strong, then too many training points will be reconstructed and we will no longer have any notion of "easy to reconstruct" examples, as all examples will be reconstructed equally well.

#### H.7 RECONSTRUCTION POST-PROCESSING

We do not post-process our reconstructions and the reconstruction curves and visualization are based on unmodified reconstructions. Note that [Haim et al.](#page-11-0) [\(2022\)](#page-11-0) has a more complex reconstruction scheme involving rescaling the reconstructions and averaging, which is detailed in [Haim](#page-11-0) [et al.](#page-11-0) [\(2022\)](#page-11-0)'s appendix.

<span id="page-23-0"></span>

### I CHOICE OF KERNEL

During reconstruction, there is a small choice we can make, specifically whether we using the initialization gradient tangent space,  $\nabla_{\theta_0} f_{\theta_0}(x_i)$  or the final gradient tangent vector space,  $\nabla_{\theta_f} f_{\theta_f}(x_i)$ . Specifically, we can choose to optimize  $\mathcal{L}_f$  or  $\mathcal{L}_0$ :

$$
\mathcal{L}_f = \left\| \Delta \theta - \sum_{s_j^R \in S^R} \alpha_j^R \nabla_{\theta_f} f_{\theta_f}(x_j^R) \right\|_2^2
$$
$$
\mathcal{L}_0 = \left\| \Delta \theta - \sum_{s_j^R \in S^R} \alpha_j^R \nabla_{\theta_0} f_{\theta_0}(x_j^R) \right\|_2^2
$$

Under linearized dynamics, these two are equal as  $\nabla_{\theta} f_{\theta}(x)$  does not change. However for standard dyanmcis there is a small difference, with the difference increasing the more the kernel changes. For the results in the main text, we use  $\mathcal{L}_f$ . We found that this has 6marginally better performance than  $\mathcal{L}_0$ , but this difference is rather minor. This also leads to a third choice of reconstruction loss, which we call the "hybrid" loss  $\mathcal{L}_h$ , which considers the change in parameters a mixture of both the final and initial kernel:

$$
\mathcal{L}_h = \Big\| \Delta \theta - \sum_{s_j^R \in S^R} \alpha_{j,0}^R \nabla_{\theta_f} f_{\theta_f}(x_j^R) - \sum_{s_j^R \in S^R} \alpha_{j,f}^R \nabla_{\theta_0} f_{\theta_0}(x_j^R) \Big\|_2^2
$$

Image /page/24/Figure/0 description: The image displays two sets of line graphs, one for MNIST Odd vs. Even and another for CIFAR-10 Animal vs. Vehicle. Each set contains four graphs, corresponding to different sample sizes: 20, 100, 200, and 500. The y-axis for both sets is labeled 'L2 Distance', and the x-axis is labeled 'Image Index'. Within each graph, multiple lines, colored in shades of red and blue, represent different values of 'w' (w=1, w=2, w=4, w=8, w=16, w=32). The lines generally show an increasing trend of L2 distance as the image index increases. The CIFAR-10 graphs show higher L2 distances overall compared to the MNIST graphs. A legend in the bottom right corner clarifies the color coding for the 'w' values.

<span id="page-24-1"></span>Figure 17: Reconstruction curves for convolutional architectures trained on MNIST Odd vs. Even and CIFAR-10 Animal vs. Vehicle classification with varying width multipliers from  $1 - 32$ 

Image /page/24/Figure/2 description: The image displays a set of line graphs titled "Tiny-ImageNet Reconstruction Curves." The graphs are arranged in a 2x4 grid. The top row shows "Standard L2 Distance" on the y-axis and "Image Index" on the x-axis, with columns labeled 200, 400, 600, and 800. The bottom row shows "Linearized L2 Distance" on the y-axis and "Image Index" on the x-axis, with the same column labels. Each graph contains multiple colored lines, with a legend on the right indicating that the lines represent different values of 'w': 256 (red), 362, 512, 724, 1024, 1448, 2048, 2896, and 4096 (blue). The y-axis ranges from 0 to 60 in all graphs. The x-axis ranges vary by column: 0 to 200 for the first column, 0 to 400 for the second, 0 to 600 for the third, and 0 to 800 for the fourth.

<span id="page-24-2"></span>Figure 18: Reconstruction curves for networks trained on Tiny-ImageNet 200-way classification

In which we have two sets of dual parameters  $\alpha_0$  and  $\alpha_f$ . This further increases performance, but in general is twice as slow as optimizing  $\mathcal{L}_0$  or  $\mathcal{L}_f$ , and we chose to use this loss for the ResNet-18 finetuning experiments, since that setting is more challenging. Note that this attack could be generalized to include multiple checkpoints  $\theta_t$  along the trajectory and more dual parameters, however of course this would require access to more training checkpoints.

### J ADDITIONAL RESULTS

#### <span id="page-24-0"></span>J.1 CONVOLUTIONAL ARCHITECTURES

Here we provide results for our attack applied to convolutional architectures. We trained networks on binary MNIST and CIFAR-10 classifications tasks, as we did in the main text, but trained on a convolutional architecture from scratch. We use the common LeNet-5 architecture with width multipliers ranging from 1-32. fig. [17](#page-24-1) shows the results. The findings observed on two-layer networks still apply in this settings, however our attack struggles more in deeper architectures.

Tiny-ImageNet Reconstructed Images,  $n = 400$ ,  $w = 4096$ 

Image /page/24/Figure/9 description: This image displays a grid of images, divided into two main sections: "Reconstructions" and "Train Images." Each section contains multiple rows and columns of smaller images. The "Reconstructions" section shows images that appear to be generated or processed, often with a grainy or noisy texture, and some are distorted. The "Train Images" section displays original photographs. The images in both sections cover a variety of subjects, including people, animals, vehicles, objects, and natural scenes. For example, the "Train Images" section includes a black and white image of a person, a close-up of a red chain, a man adjusting his bow tie, a tree trunk, a pink SUV, a blue bucket, a display of shells, a woman in white clothing, a birdhouse with a deer, a close-up of fried food, a bridge, a chimpanzee, a wooden chair, a printer, colorful textiles, a person with a blue umbrella, and a close-up of red flowers.

<span id="page-24-3"></span>Figure 19: Reconstructed images and their nearest train iamge counterparts for Tiny-ImageNet reconstruction for a  $w = 4096$  network with  $n = 400$ 

#### <span id="page-25-0"></span>J.2 TINY-IMAGENET

The main text works mainly with classification on small, low-resulution datasets such as MNIST and CIFAR-10. Here we consider more complex datasets with higher resolution by applying our attack to Tiny-ImageNet classification. Tiny-ImageNet consists of 200 classes with images of resolution  $64\times64$  [\(Le & Yang, 2015\)](#page-11-14). As the quality of the NTK approximation is negatively affected by image resolution [\(Jacot et al., 2018\)](#page-11-1), this experiment serves as an important testing ground of the viability of the attack for higher resolution images. We show the results on few-shot classification, consider 1 − 4 images per classes in fig. [18](#page-24-2) and with the reconstructed images in fig. [19.](#page-24-3) Reconstructing higher resolution images is more challenging and improving this attack on high resolution images is an interesting direction for future work.

<span id="page-25-1"></span>

#### J.3 KERNEL DISTANCE VS. RECONSTRUCTION QUALITY SCATTER PLOTS FOR MULTICLASS CLASSIFICATIONS

fig. [20](#page-25-2) shows the corresponding fig. [3](#page-4-1) for multiclass classification. As we observed, multiclass classification has improved reconstruction quality. From fig. [20,](#page-25-2) we see that multiclass classification sees significantly lower kernel distances (up to 1e-2 for MNIST and 2e-2 for CIFAR-10) compared to binary classification (up to 3e-2 for MNIST and 9e-2 for CIFAR-10, see fig. [3\)](#page-4-1), which may explain why the reconstructions have better quality.

Image /page/25/Figure/4 description: The image displays two scatter plots side-by-side, both titled with dataset names: "MNIST 10 Classes" on the left and "CIFAR-10 10 Classes" on the right. Both plots share the same y-axis label, "Mean Reconstruction Error", and x-axis label, "Kernel Distance between Init and Final NTKs". The y-axis for MNIST ranges from approximately 2 to 14, while the y-axis for CIFAR-10 ranges from approximately 2.5 to 20. The x-axis for MNIST ranges from 0.000 to 0.010, and for CIFAR-10, it ranges from 0.000 to 0.020. Each plot uses colored circles to represent data points, with the color gradient indicating dataset size, as shown in a legend on the MNIST plot. The legend shows that smaller dataset sizes (e.g., 20) are represented by purple/blue colors, and larger dataset sizes (e.g., 500) are represented by red colors. In both plots, there appears to be a general trend where higher kernel distances correlate with higher mean reconstruction errors, and larger dataset sizes tend to be associated with lower reconstruction errors for a given kernel distance.

<span id="page-25-2"></span>Figure 20: Mean reconstruction error vs. the kernel distance from the initialization kernel to the final kernel for multiclass classification. The mean reconstruction error, measured as the average value of the reconstruction curve, is strongly correlated with how much the finite-width NTK evolves over training. Dataset size is given by dot size, while the color indicates model width. Multiclass classification sees significantly lower kernel distances (up to 1e-2 for MNIST and 2e-2 for CIFAR-10) compared to binary classification (up to 3e-2 for MNIST and 9e-2 for CIFAR-10, see fig. [3\)](#page-4-1), which may be the cause of better reconstruction quality.

### J.4 EXTRA PRUNING EXPERIMENTS

In section [6,](#page-6-1) we considered removed class balanced subsets of the training data at each training iteration, either by random or by ease of reconstruction. If instead we allow class imbalance, we see the same behaviour as in section [6](#page-6-1) initially, but as more datapoints are removed, we see in fig. [21](#page-26-0) that removing easy reconstructions results in strongly imbalanced classes, resulting in poor test accuracy. Understanding why some classes are more susceptible to reconstruction is likely related to the discussion in section [6.](#page-6-1) Additionally, we found that if we underfit the data, then we do not observe any difference in test accuracy for pruning random vs. reconstructions. This suggests that the effect of large  $\alpha$  values only shows later in training, when some datapoints are well fit and others still underfit.

Image /page/26/Figure/0 description: The image is a line graph titled "CIFAR-10 Accuracy". The y-axis is labeled "Test Accuracy (%)" and ranges from 17.5 to 32.5. The x-axis is labeled "Datapoints Removed" and ranges from 0 to 800. There are four lines plotted: "Easiest Reconstructions" (solid red), "Easiest Reconstructions (Unbalanced)" (dashed red), "Random" (solid blue), and "Random (Unbalanced)" (dashed blue). All lines start at approximately 32% accuracy at 0 datapoints removed and generally decrease as more datapoints are removed. The "Easiest Reconstructions" lines tend to maintain higher accuracy than the "Random" lines, especially as more datapoints are removed.

<span id="page-26-0"></span>Figure 21: Test accuracy of iteratively pruned CIFAR-10 using either random pruning or pruning based on easily reconstructed images with either class balanced subsets or non-balanced subsets.

#### J.5 ADDITIONAL RECONSTRUCTION CURVES

We show additional reconstruction curves for all dataset sizes in [20, 50, 100, 150, 200, 300, 400, 500] for MNIST Odd vs. Even and CIFAR-10 Animal vs.  $[20, 50, 100, 150, 200, 300, 400, 500]$  for MNIST Odd vs. Vehicle in fig. [22.](#page-26-1) We show the same reconstruction curves with the distillation reconstructions in ??. fig. [23](#page-27-0) shows the same reconstruction curves for early stopping. Finally, fig. [24](#page-28-0) shows the same curves for multi-class classification.

Image /page/26/Figure/4 description: This figure displays four rows of plots, each comparing L2 distance against image index for different training set sizes (20, 50, 100, 150, 200, 300, 400, and 500) and model widths. The first row, titled 'MNIST Odd vs. Even (Standard Dynamics)', shows reconstruction curves. The second row, 'MNIST Odd vs. Even (Linearized Dynamics)', presents similar curves but with linearized dynamics. The third row, 'CIFAR-10 Animal vs. Vehicle (Standard Dynamics)', plots reconstruction curves for the CIFAR-10 dataset with standard dynamics. The fourth row, 'CIFAR-10 Animal vs. Vehicle (Linearized Dynamics)', shows the same CIFAR-10 comparison but with linearized dynamics. Each plot within these rows uses a color gradient from blue to red to represent increasing model widths, ranging from 256 to 4096. The y-axis is labeled 'L2 Distance' and the x-axis is labeled 'Image Index'.

Figure 22: Reconstruction curves for binary classification tasks

#### <span id="page-26-1"></span>J.6 RECONSTRUCTION IMAGES

Here we show all the reconstruction images and their nearest training images in terms of  $L_2$  distance. Images are sorted based on their rank in the reconstruction curve.

Image /page/27/Figure/0 description: This image displays four rows of plots, each containing eight subplots. The first row is titled 'MNIST Odd vs. Even Early Stopping (Standard Dynamics)', and the second row is titled 'MNIST Odd vs. Even Early Stopping (Linearized Dynamics)'. The third row is titled 'CIFAR-10 Animal vs. Vehicle Early Stopping (Standard Dynamics)', and the fourth row is titled 'CIFAR-10 Animal vs. Vehicle Early Stopping (Linearized Dynamics)'. Each subplot has 'Image Index' on the x-axis and 'L2 Distance' on the y-axis. The subplots are grouped by 'Training set size', with sizes labeled as 20, 50, 100, 150, 200, 300, 400, and 500. Within each subplot, multiple lines represent different 'Model width' values, ranging from 256 to 4096, indicated by a color gradient from blue to red. The plots show the L2 distance as a function of image index for various training set sizes and model widths.

Figure 23: Reconstruction curves for binary classification tasks with early stopping

<span id="page-27-0"></span>

### J.6.1 BINARY CLASSIFICATION

We show the reconstruction curves for MNIST Odd vs. Even and CIFAR-10 Animal vs. Vehicle tasks for width 4096 and 1024 networks with linearized or standard dynamics in figures [25](#page-29-0) to [32.](#page-36-0)

### J.6.2 MULTICLASS CLASSIFICATION

We show the reconstruction curves for MNIST and CIFAR-10 10-way classification for width 4096 and 1024 networks with linearized or standard dyanmics in figures [33](#page-37-0) to [40.](#page-44-0)

Image /page/28/Figure/0 description: This figure displays four rows of plots, each containing six subplots. The first row is titled "MNIST 10 Classes (Standard Dynamics)", the second "MNIST 10 Classes (Linearized Dynamics)", the third "CIFAR-10 10 Classes (Standard Dynamics)", and the fourth "CIFAR-10 10 Classes (Linearized Dynamics)". Each subplot has an x-axis labeled "Image Index" and a y-axis labeled "L2 Distance". Above each subplot, a "Training set size" is indicated, with values of 20, 50, 100, 150, 200, 300, 400, and 500. Within each subplot, multiple curves are plotted, representing different "Model width" values ranging from 256 to 4096, indicated by a color gradient from blue to red. The curves generally show an increasing trend of L2 Distance as the Image Index increases, with variations based on the training set size and model width.

<span id="page-28-0"></span>Figure 24: Reconstruction curves for multiclass classification

Image /page/29/Figure/0 description: The image displays a grid of handwritten digits, likely from the MNIST dataset. The title indicates it's a comparison of "MNIST Odd vs. Even Reconstructions sorted by L2 Distance" with parameters N = 500 and w = 4096, using Linearized Dynamics. The odd-numbered rows represent reconstructions, and the even-numbered rows represent the original training images. Each row contains multiple digits, arranged horizontally. The digits are presented in various colors against a dark background, with yellow and blue being prominent colors for the digits.

MNIST Odd vs. Even Reconstructions sorted by L2 Distance

<span id="page-29-0"></span>Figure 25: Reconstructions for MNIST Odd vs. Even, Linearized Dynamics, 4096 width.

|                                                 | N = 500, w = 4096, Standard Dynamics, Odd rows = Reconstructions, Even rows = train images |                            |                                             |                               |                                       |
|-------------------------------------------------|--------------------------------------------------------------------------------------------|----------------------------|---------------------------------------------|-------------------------------|---------------------------------------|
| $508 - \lambda$                                 | $\mu$<br>8<br>ς<br>- 5                                                                     |                            | 296246                                      | 94<br>9                       | 3                                     |
|                                                 | ۷<br>$\overline{8}$<br>$\boldsymbol{\mathcal{U}}$<br>ς                                     | 4                          | 6<br>2<br>4<br>6                            | 4<br>q                        | $\overline{G}$<br>4<br>4              |
| $\overline{\mathbf{c}}$<br>O <sup>4</sup><br>85 | -9<br>9<br>$\delta$<br>8                                                                   | 4<br>-9<br>7               | 3<br>8<br>83<br>ł                           | 3<br>Б                        | ۶<br>9                                |
|                                                 | a                                                                                          |                            | 3<br>8<br>ç<br>3<br>ł                       | я<br>Б                        |                                       |
|                                                 | 5                                                                                          | 8                          | 3<br>2<br>69<br>5                           | 26                            | 3<br>9                                |
|                                                 | 5                                                                                          |                            | 6<br>9                                      | $\overline{\mathcal{X}}$<br>G | Ŕ                                     |
| 3<br>8                                          | 5<br>6<br>q                                                                                | 6                          | 9<br>5<br>6<br>3                            | 4                             | 4<br>9<br>6<br>4                      |
| 8<br>2<br>ሬ<br>3                                | b<br>q<br>5                                                                                | 6                          | 3<br>6<br>$\overline{5}$<br>g               | 4                             | $\overline{\mu}$<br>4<br>9<br>2       |
| 2s<br>႙<br>2                                    | 4<br>8<br>$\boldsymbol{\tau}$                                                              | 356                        | 63<br>14280                                 |                               | 569958<br>4                           |
| စွ                                              | 4<br>8                                                                                     |                            | 3<br>6<br>Ő                                 | 5<br>6                        | Š,<br>ų.<br>0<br>$\sigma$<br>ى        |
| 8<br>6                                          | y<br>4                                                                                     | 64                         | ్త                                          | 4<br>9                        | 5. 3<br>$\mathcal{L}$<br>8            |
| 6<br>ð                                          | 4<br>y<br>8<br>З                                                                           | 6<br>-4                    |                                             | $\overline{\mathcal{L}}$<br>4 | 8<br>5.<br>0                          |
| 4253                                            | $\overline{3}$<br>$\sqrt{ }$                                                               | $\overline{a}$<br>8<br>3   | 0500<br>٩                                   | $0\,2$                        | 3<br>9<br>9:3<br>4                    |
| 53<br><b>u</b>                                  | b<br>- 1<br>3                                                                              | 8<br>3<br>ി                | 0500<br>ာရ                                  | $\overline{O}$<br>2           | 9<br>3<br>9. 3<br>4                   |
| -2                                              | 039703<br>7                                                                                |                            | ୍ନ<br>$\sqrt{a}$<br>$\boldsymbol{\epsilon}$ | $\circ$<br>ч                  | ደ<br>ಾ                                |
| ۷<br>L                                          | З<br>۹<br>Ω                                                                                | 9<br>O                     | 8                                           | 6<br>ч                        | የ                                     |
| ۹<br>944                                        | 3<br>$\mathbf u$<br>-0<br>5                                                                | $\mathbf{2}$<br>24         | lo.<br>02                                   | 90<br>9                       | Э<br>-94<br>O                         |
|                                                 | ⋖                                                                                          | ىم                         | 6                                           | 9<br>Ô                        | a                                     |
|                                                 | ь<br>Ы                                                                                     |                            | б<br>Ж                                      | Ο                             |                                       |
|                                                 |                                                                                            |                            |                                             |                               |                                       |
|                                                 |                                                                                            | q                          |                                             |                               | 9<br>4<br>$\boldsymbol{\mathcal{I}}$  |
|                                                 |                                                                                            | 9                          | 3<br>0                                      | 9                             | ٩<br>G                                |
| -9<br>$\overline{\mathcal{L}}$                  | 5                                                                                          | ୍ୟ<br>Î<br>5               | B<br>8<br>$7 -$                             | Ч<br>$\frac{4}{3}$            | 4739                                  |
| q<br>4                                          |                                                                                            | a                          | 8<br>4                                      | ū<br>п<br>ч                   | 9<br>్య                               |
|                                                 | 4<br>SΞ                                                                                    | D                          | 9<br>9                                      | 1.5                           | 7.9<br>-CS<br>ኖ                       |
| 4                                               |                                                                                            | 0                          | 5<br>4<br>9<br>1                            | 6                             | 9<br>9<br>Ο                           |
| 84                                              | 马<br>-3                                                                                    | $\boldsymbol{u}$<br>$\ell$ | $f \notin 4$<br>$\sim$ 5                    | $1 - 7$                       | 4<br>92<br>8<br>翆                     |
| a                                               | ١<br>5<br>٦                                                                                | Ī<br>ê                     | 9<br>4<br>্র<br>1                           |                               | ŧ<br>ъ<br>g<br>3<br>9                 |
|                                                 | 4.5<br>ъ                                                                                   | 245                        | -3<br>g<br>Ą<br>S                           | ान                            | $Q_2$ $Q$<br>9                        |
|                                                 | グ<br>b                                                                                     | 7                          | Ŋ<br>5                                      | Y<br>9                        | J<br>י                                |
| $\bullet$ 0<br>5                                | 嶀<br>ų,<br>29 A                                                                            | 李燮                         | 3.4<br>of of                                | 8                             | $G \rightarrow$<br>680                |
| 6<br>∽                                          |                                                                                            |                            | ₹                                           |                               | 6<br>Ø                                |
| 47.41                                           |                                                                                            |                            | ъ<br>O<br>- el                              | 8                             | $\boldsymbol{\delta}$<br>8<br>-B<br>z |
| 6<br>2                                          | 2<br>ν                                                                                     | 49                         | 6<br>3<br>2<br>O                            |                               | 2<br>2                                |
| $\mathcal{L}$                                   | 3<br>Ω                                                                                     | 4673                       | r o 3<br>Э                                  | 44.9                          | 一<br>$\mathcal{L}$<br>20%             |
| 2<br>8 <<br>5<br>6                              | 3<br>Ь<br>٥                                                                                | $\mathbf{u}$<br>03         | 6<br>з<br>ይ<br>O<br>o                       | 5<br>2                        | 2.<br>$\mathcal{Z}$<br>D<br>6         |
| 55539146150822095845272054                      |                                                                                            |                            |                                             |                               |                                       |
|                                                 | 6090082                                                                                    |                            | 2077                                        | 02527                         | z.<br>03<br>ב                         |
| V.                                              | $152358097696894002063048$                                                                 |                            |                                             |                               |                                       |
| 25233009762687600225208<br>G                    |                                                                                            |                            |                                             |                               | ٠                                     |

MNIST Odd vs. Even Reconstructions sorted by L2 Distance

Figure 26: Reconstructions for MNIST Odd vs. Even, Standard Dynamics, 4096 width.

Image /page/31/Figure/0 description: The image displays a grid of handwritten digits, likely from the MNIST dataset. The title indicates it's a comparison of odd vs. even reconstructions sorted by L2 distance, with N=500 and w=1024. The odd-numbered rows show reconstructions, and the even-numbered rows show the original training images. Each row contains multiple digits, and the overall arrangement suggests a visual analysis of how well the model reconstructs these digits.

MNIST Odd vs. Even Reconstructions sorted by L2 Distance

Figure 27: Reconstructions for MNIST Odd vs. Even, Linearized Dynamics, 1024 width.

Image /page/32/Figure/0 description: The image displays a grid of MNIST digits, with the title "MNIST Odd vs. Even Reconstructions sorted by L2 Distance". The parameters used are N = 500, w = 1024, and Standard Dynamics. The odd-numbered rows show reconstructions of digits, while the even-numbered rows show the original training images. Each row contains multiple digits arranged horizontally. The digits are presented in a color scheme where yellow represents the digit and purple/blue represents the background, with some noise or variation in the reconstruction rows.

MNIST Odd vs. Even Reconstructions sorted by L2 Distance

Figure 28: Reconstructions for MNIST Odd vs. Even, Standard Dynamics, 1024 width.

| CIFAR-10 Animal vs. Vehicle Reconstructions sorted by L2 Distance |  |
|-------------------------------------------------------------------|--|

| $N = 500$ , w = 4096, Linearized Dynamics, Odd rows = Reconstructions, Even rows = train images                                                                                                                                                               |  |  |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|
| <u>TADA - 210 (114) ACPABRE CAMP</u><br>TADA - 210 (114) ACPABRE CAMP                                                                                                                                                                                         |  |  |
|                                                                                                                                                                                                                                                               |  |  |
| 大 <mark>人子的过去分词变得不是不是人生的人物。 人名英格兰人姓氏</mark> 的变体                                                                                                                                                                                                               |  |  |
| NASA (SAMA CARACTER)                                                                                                                                                                                                                                          |  |  |
| <b>内容についたいインスの人間のムシーズはあったりしゃ</b>                                                                                                                                                                                                                              |  |  |
| むっかかぶ ふっこう こうこうこうかい いっさんこうりょ                                                                                                                                                                                                                                  |  |  |
| 日本のトントーランプ こうちこうかでんだ さっける                                                                                                                                                                                                                                     |  |  |
| <u>Thromating to the Control of Man</u>                                                                                                                                                                                                                       |  |  |
| THE STRIKE STRIKE STRIKE STRIKE                                                                                                                                                                                                                               |  |  |
| 第一 11 12 13 14 15 17 17 17 17 17 17 17 17 17 17 17 17 17                                                                                                                                                                                                      |  |  |
| The South of the Matter of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of                                 |  |  |
| THE ANNUAL PROPERTY OF A STATE                                                                                                                                                                                                                                |  |  |
| TO A DISC TO THE MAN THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF                                                                                                                                               |  |  |
| トラートリップの2007年のエット 上海一人 へんごく                                                                                                                                                                                                                                   |  |  |
| LOC-RA DANTA WARD BALL                                                                                                                                                                                                                                        |  |  |
| LOC-GRIDANDA CAMPRE YOU                                                                                                                                                                                                                                       |  |  |
| BUT A THE STATE OF BUILDING<br>The state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of t |  |  |
|                                                                                                                                                                                                                                                               |  |  |
| ASEK KAN YERRE 2014 IN 1952                                                                                                                                                                                                                                   |  |  |
| EXCEPTION PROTECTION                                                                                                                                                                                                                                          |  |  |
| 573 - 57 LA - 52 - 130 251 034 TD                                                                                                                                                                                                                             |  |  |
| ACALLE AT TANK & THEM I                                                                                                                                                                                                                                       |  |  |
| ACALLY TO TANK A TTOO ALL                                                                                                                                                                                                                                     |  |  |
| TAN TAN TANG TANG TANG                                                                                                                                                                                                                                        |  |  |
|                                                                                                                                                                                                                                                               |  |  |
| NASAN MARKA DA RESERVE SAKE BA                                                                                                                                                                                                                                |  |  |
| Les de la de la de la de la de                                                                                                                                                                                                                                |  |  |
| STAR SE - SA WORK LAIN - MARS                                                                                                                                                                                                                                 |  |  |
| S AN ANG STATE MERINANG A                                                                                                                                                                                                                                     |  |  |
| ALANG ALANG STREET                                                                                                                                                                                                                                            |  |  |
| BOYAYA MARY ME DELLE COMPANY                                                                                                                                                                                                                                  |  |  |
| <b>ALL CONTRACTOR</b>                                                                                                                                                                                                                                         |  |  |
| CONTRACTOR CONTRACTO<br>At the lax holdes w- and and                                                                                                                                                                                                          |  |  |
| A SHELLY A COUNTY OF STRAINING STATES AND                                                                                                                                                                                                                     |  |  |
| <b>NATION CONTRACTORY OF BUILDING</b>                                                                                                                                                                                                                         |  |  |

Figure 29: Reconstructions for CIFAR-10 Animal vs. Vehicle, Linearized Dynamics, 4096 width.

| CIFAR-10 Animal vs. Vehicle Reconstructions sorted by L2 Distance |  |
|-------------------------------------------------------------------|--|

| N = 500, w = 4096, Standard Dynamics, Odd rows = Reconstructions, Even rows = train images                                                                                                                                     |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <u>koji kadi aligente kristina koleda</u><br>Tojika di aligente kristina koleda                                                                                                                                                |
| The Bird of 11 and 10 and 12                                                                                                                                                                                                   |
| <b>THE REAL AST AND A REAL AST</b>                                                                                                                                                                                             |
| - OU-DEAL AND CENTRE AVAILABLE<br>- マンチアルト 小身長 インス かんじょう トンドライン                                                                                                                                                                |
| <b>文化基地合并 化基地效果 医心脏性麻醉剂 网络沙漠</b>                                                                                                                                                                                               |
| ANTI ANTI A TELEVISION AND TRANSPORTED TO A REAL                                                                                                                                                                               |
| <b>BE AREA AND A REAL ATTEMPTION</b>                                                                                                                                                                                           |
| CONTRACTOR CONTRACTOR AND THE RESIDENCE                                                                                                                                                                                        |
| ◆本地 ※ 本地の大 一、 これはこと、 本社 ( )                                                                                                                                                                                                    |
| ANTELLA CARDIA EL POLITICA<br>KIND - J-SP DI - JP - DING                                                                                                                                                                       |
| THE TANK THE STATE OF BUILDING                                                                                                                                                                                                 |
| <b>NASATASTIKA KALAMAN SI</b>                                                                                                                                                                                                  |
| <b>STANDARD TO ALT. MARINE AREA</b><br>KOLO ZRIVER TA AKTI MATERIAL                                                                                                                                                            |
| THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN THE RESIDENCE IN T |
| <b>本人的文化文化文化文化文化文化文化文化文化文化文化文化文化文化文化文化文化文化文化</b>                                                                                                                                                                               |
| Water of the<br>ALL A REAL PROPERTY AND PROPERTY OF A                                                                                                                                                                          |
| METHOD STATES TO BE AN ALLE                                                                                                                                                                                                    |
| MARINE VEHICLE ARE LEADER OF THE COMPANY                                                                                                                                                                                       |
| <b>AND A REAL PROPERTY AND ACCEPT</b><br>SALE A BALLAS TANTAR                                                                                                                                                                  |
| <b>DESCRIPTION OF PROPERTY AND REAL</b>                                                                                                                                                                                        |
| <b>CONTRACTORY AND ACTIONS OF CHARLES COMPANY</b>                                                                                                                                                                              |
| <b>AND A REAL PROPERTY OF A REAL PROPERTY</b>                                                                                                                                                                                  |
| THE REPORT OF THE REAL PROPERTY.<br>March 1989 and 1989 and 1989 and 1989                                                                                                                                                      |
| PORTUGAL CALIFORNIA A SAL                                                                                                                                                                                                      |
| <b>RESERVE AND RESERVE AND RESERVE</b><br>THE VEHICLE AND LAND CONTROL AND LAND                                                                                                                                                |
| <b>SAME TO AN ADVERSE A LA CALLER</b>                                                                                                                                                                                          |
| A THE THE RESIDENCE OF A THE TANK OF                                                                                                                                                                                           |
| <b>A ALL AND A DEAL AND A REPORT OF A REPORT</b>                                                                                                                                                                               |
| SELECTIONS PRESERVED                                                                                                                                                                                                           |
| Library Products of the March 2000                                                                                                                                                                                             |
| THE STATISTICS OF BUILDING                                                                                                                                                                                                     |

Figure 30: Reconstructions for CIFAR-10 Animal vs. Vehicle, Standard Dynamics, 4096 width.

| CIFAR-10 Animal vs. Vehicle Reconstructions sorted by L2 Distance |  |
|-------------------------------------------------------------------|--|

Image /page/35/Picture/1 description: The image displays a grid of images, organized into rows and columns. The title indicates that it is a comparison of CIFAR-10 Animal vs. Vehicle Reconstructions sorted by L2 Distance, with N=500 and w=1024. The text specifies that odd rows represent reconstructions and even rows represent the original train images. The grid contains numerous small images, alternating between reconstructed and original images in each row.

Figure 31: Reconstructions for CIFAR-10 Animal vs. Vehicle, Linearized Dynamics, 1024 width.

| CIFAR-10 Animal vs. Vehicle Reconstructions sorted by L2 Distance |  |
|-------------------------------------------------------------------|--|
|                                                                   |  |

| N = 500, w = 1024, Standard Dynamics, Odd rows = Reconstructions, Even rows = train images                                                                                                                                           |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>AND AND STATES ASSESSED AND ADDRESS</b>                                                                                                                                                                                           |
| 小さい クリークス アーバイントプロセカンズム                                                                                                                                                                                                              |
| A SHEARAN AGUS AS CHUINNEACHADH AN                                                                                                                                                                                                   |
| The state of the Company<br>8 - A<br><b>TANK 19</b>                                                                                                                                                                                  |
| MARK AND ASSESSED ON A MARKED OF                                                                                                                                                                                                     |
| THE MANUSCRIPTION<br>$k = 51$                                                                                                                                                                                                        |
| <b>BANK AND AND AND AND AND</b>                                                                                                                                                                                                      |
| 「マイナー」<br>WY H                                                                                                                                                                                                                       |
| <b>A PART AND REPORT OF A PART AND A PARTIES</b>                                                                                                                                                                                     |
| <b>REDENTIFY AND REL</b><br>ランド 小野                                                                                                                                                                                                   |
| <b>RACE AND MARKET AND ARRIVED BY</b>                                                                                                                                                                                                |
| CONTRACTOR COMPANY<br>$\mathcal{R} = -\frac{1}{2} \mathcal{R} \mathcal{R}$                                                                                                                                                           |
| <b>BANKARAZ</b><br><b>ALL AND A CONTACT PERSONAL PROPERTY</b>                                                                                                                                                                        |
| ▼レンニントミノ三個例 → 大家<br>「さんぶ」<br><b>START START</b><br><b>City</b>                                                                                                                                                                       |
| <b>BE A PARTICULAR STATE A REPORT OF A PARTICULAR AND A REPORT OF A PARTICULAR AND A PARTICULAR AND A PARTICULAR A</b>                                                                                                               |
| THE REAL PROPERTY AND REAL PROPERTY.                                                                                                                                                                                                 |
| Bernard Media State Construction                                                                                                                                                                                                     |
| 家人<br>The March of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of the South of t |
| The contract of the contract of the contract of the contract of the contract of the contract of the contract of                                                                                                                      |
| <b>DESCRIPTION AND RELEASE</b><br><b>Contract Contract Contract Contract Contract Contract Contract Contract Contract Contract Contract Contract Co</b><br>S.                                                                        |
| <b>CONTRACT IN A PRODUCT OF A PRODUCT</b>                                                                                                                                                                                            |
| HALL PHRICARD METROPHY                                                                                                                                                                                                               |
| <b>Andrew Comments</b>                                                                                                                                                                                                               |
| A LEWIS CO., MICH. 49-14039-1-120-2                                                                                                                                                                                                  |
| <b>Kalaugh Sacalat</b><br><b>AND A REPORT OF STANDARD AND A</b>                                                                                                                                                                      |
| <b>RACKA ATES</b>                                                                                                                                                                                                                    |
| <b>THE REAL PROPERTY OF A REAL PROPERTY</b>                                                                                                                                                                                          |
| LEXING SV LEW XXXIII PRESENT                                                                                                                                                                                                         |
|                                                                                                                                                                                                                                      |
| <b>ACLINA AT STATE RECORD ACE ACTIVE</b>                                                                                                                                                                                             |
| <b>START OF BUILDING AND ARRANGEMENT</b>                                                                                                                                                                                             |
| <b>The Alling A Little State of the Alling</b>                                                                                                                                                                                       |
| LA CARA A LA CARA LA CARACTERA DE LA                                                                                                                                                                                                 |
| de f<br>金油 りつう エマン インタル タマス とりふくこう インダ                                                                                                                                                                                               |
| and participated and provided and provided                                                                                                                                                                                           |
| <b>RADIO COMPANY REPORTS</b><br><b>THE BIG</b><br><b>KANE</b><br><b>水解下</b>                                                                                                                                                          |
| <b>A TANK A TANK A TANK A TANK A TANK A TANK A TANK A TANK A TANK A TANK A TANK A TANK A TANK A TANK A TANK A TAN</b>                                                                                                                |
| TAG-1942 X 12 A B Co-ah                                                                                                                                                                                                              |
| <b>The second contract of the second contract of the second contract of the second contract of the second contract of the second contract of the second contract of the second contract of the second contract of the second con</b> |
| LOGC CALL A ZOLAN-Z M-53 CATL                                                                                                                                                                                                        |

<span id="page-36-0"></span>Figure 32: Reconstructions for CIFAR-10 Animal vs. Vehicle, Standard Dynamics, 1024 width.

Image /page/37/Figure/0 description: The image displays a grid of handwritten digits, likely from the MNIST dataset. The title indicates that it shows reconstructions sorted by L2 distance, with N=500 and w=4096, and that odd rows are reconstructions while even rows are train images. Each row contains 10 digits, and there are 20 rows in total, making it a 20x10 grid of digits. The digits are yellow against a dark purple background. The digits themselves appear to be a mix of 0s, 1s, 2s, 3s, 4s, 5s, 6s, 7s, 8s, and 9s, with some rows showing more similar digits than others, consistent with a sorting by distance.

MNIST 10 Classes Reconstructions sorted by L2 Distance

<span id="page-37-0"></span>Figure 33: Reconstructions for MNIST 10 Classes, Linearized Dynamics, 4096 width.

Image /page/38/Figure/0 description: The image displays a grid of handwritten digits, likely from the MNIST dataset. The title indicates it shows reconstructions sorted by L2 distance, with N=500 and w=4096, using Standard Dynamics. Odd rows represent reconstructions, and even rows represent the original training images. Each row contains multiple digits, and the digits are presented in yellow against a dark purple background. The digits are clearly legible, and the arrangement suggests a comparison between original and reconstructed versions of handwritten numbers.

MNIST 10 Classes Reconstructions sorted by L2 Distance

Figure 34: Reconstructions for MNIST 10 Classes, Standard Dynamics, 4096 width.

Image /page/39/Figure/0 description: This image displays a grid of handwritten digits, likely from the MNIST dataset. The title indicates it's about "10 Classes Reconstructions sorted by L2 Distance" with parameters N = 500 and w = 1024, suggesting a dimensionality reduction or reconstruction process. The description clarifies that odd rows represent reconstructions and even rows represent the original training images. The grid is organized into multiple rows, with each row containing several digits. The digits are colored in yellow and blue against a dark purple background. The overall impression is a visual comparison of original digits and their reconstructed versions.

MNIST 10 Classes Reconstructions sorted by L2 Distance

Figure 35: Reconstructions for MNIST 10 Classes, Linearized Dynamics, 1024 width.

Image /page/40/Figure/0 description: The image displays a grid of handwritten digits, likely from the MNIST dataset. The title indicates "MNIST 10 Classes Reconstructions sorted by L2 Distance" with parameters N = 500 and w = 1024. It specifies that odd rows represent reconstructions and even rows represent the original training images. The grid is organized into multiple rows and columns, with each cell containing a digit. The digits are colored in yellow against a dark purple background. The overall arrangement suggests a comparison between original digits and their reconstructed versions, sorted by their L2 distance.

MNIST 10 Classes Reconstructions sorted by L2 Distance

Figure 36: Reconstructions for MNIST 10 Classes, Standard Dynamics, 1024 width.

| N = 500, w = 4096, Linearized Dynamics, Odd rows = Reconstructions, Even rows = train images<br><b>EACAPTING BOOK TO IN PACTICACT</b>                                                                                                                                                                                                           |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| STACK ALL ALL ALL THE CARD IN CA<br><b>THAT YEAR &amp; SAMPLE CX</b><br><b>SECTION</b>                                                                                                                                                                                                                                                          |
| 4 . RESERVE TO A 1999 1999<br>4 5 3 4 5 3 4 5 5 6 5 7 5 6 7 8 5 6<br>÷                                                                                                                                                                                                                                                                          |
| A BOOK TO THE MELTING THE THE STATE OF BUILDING<br><b>ENGLAND HOLLAND COLOR</b>                                                                                                                                                                                                                                                                 |
| CHE PLANE<br>ALC UNK<br>$\frac{1}{2}$<br><b>Sold Barbara</b>                                                                                                                                                                                                                                                                                    |
| THE STATE OF STREET AND THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE STATE OF THE<br>$-M$<br>THE REAL PROPERTY AND REAL PROPERTY.<br>全体文化化学入り<br>$-1$                                              |
| <b>A MARK WAS ARRESTED FOR A MARKET AND A MARKET AND A MARKET AND A MARKET AND A MARKET AND A MARKET AND A MARKET</b><br><b>MELANEX AVERY PROVIDE</b><br><b>AMER</b><br><b>TAGE AND</b>                                                                                                                                                         |
| <b>WISS AMO, AS TEMP THIS PROPERTY.</b><br>SEAMO, AS LED STOR                                                                                                                                                                                                                                                                                   |
| COMMAND TO A LOOKE THE GENERAL<br>我们一个小小的 地名美国伊斯特 不可以说的                                                                                                                                                                                                                                                                                         |
| Mathematic Mathematic Company<br>Mathematic Mathematic A                                                                                                                                                                                                                                                                                        |
| AMORA CRIMINAL<br><b>HELEN</b><br>THE THE SP<br><b>UNITED TO</b>                                                                                                                                                                                                                                                                                |
| <b>A SHOP AND A STRAIN</b><br><b>Address Made R</b>                                                                                                                                                                                                                                                                                             |
| <b>AND THE REAL PROPERTY</b><br>STATISTICS IN THE REAL PROPERTY.<br><b>AND THE STATE</b><br>$\begin{array}{c c c c c c c c c c c c c c c c c c c $<br><b>EW. CAN</b><br>置                                                                                                                                                                       |
| <b>BUT AND CAR THE AND AND THE</b><br>$\sum$<br><b>色色身 精神的人名人的人名</b>                                                                                                                                                                                                                                                                            |
| TWO AND THE AWAY<br><b>ALL MENTS AND REPLACEMENT</b>                                                                                                                                                                                                                                                                                            |
| <b>TALAY</b><br>and the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state<br>$\Lambda$<br>$-11 - 11 = 1 - 1 - 1 - 1 - 1 - 1 - 1 - 1 - 1 - 1 - 1 - 1 - 1 - $<br>$\mathbf{A}$ |
| BUT WARRIS MOZEL & BELLANDS CO<br><b>MARKS LIVE IN A BULLET</b>                                                                                                                                                                                                                                                                                 |
| <b>PHOTO DELIVERED</b><br>$-180$<br>$\frac{1}{2}$                                                                                                                                                                                                                                                                                               |
| <b>EXISTENT AND READY</b><br>不是一处 一级 一头一下<br>· 以外, 一场从一场, 一道<br><b>THE REAL PROPERTY OF SHELF</b>                                                                                                                                                                                                                                               |
| SALXWING IN BLANK<br>SEE YS HAR THE STREET OF STREET                                                                                                                                                                                                                                                                                            |

CIFAR-10 10 Classes Reconstructions sorted by L2 Distance

Figure 37: Reconstructions for CIFAR-10 10 Classes, Linearized Dynamics, 4096 width.

| CIFAR-10 10 Classes Reconstructions sorted by L2 Distance |
|-----------------------------------------------------------|
|                                                           |

| N = 500, w = 4096, Standard Dynamics, Odd rows = Reconstructions, Even rows = train images<br>TATE HARD SEPTEMBER 1984                                                                                                               |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>DEAL OF A MARINE AND A PARTIES</b><br><b>ETAN STRAND TRANSPORT</b>                                                                                                                                                                |
| M. D. CARE AVAIT NEW MARK RACK<br>RID CARL THAT THE CAR FRACT                                                                                                                                                                        |
| ARSCHOOL SENSOR AND CONTRACTOR                                                                                                                                                                                                       |
| <b>A N-19 M-6 - W LA A WALL CAN CAN DATE</b>                                                                                                                                                                                         |
| LEATHER HOSPITAL<br>I MAN PORT TO ME TO BE THE TABLE                                                                                                                                                                                 |
| STAGE A SEPERING A STAGE AND MALE                                                                                                                                                                                                    |
| BASE AREA TANK AND MADE                                                                                                                                                                                                              |
| ALAKE KRAME KRAMETIR                                                                                                                                                                                                                 |
|                                                                                                                                                                                                                                      |
| ENA THE ALL AND THE RESIDENCE                                                                                                                                                                                                        |
| SALE THE SELECTION OF THE SALE OF THE SALE                                                                                                                                                                                           |
| RATH MARINE COMMUSILE<br>RATH - MARINE - TOWNO - EQUIPMENT                                                                                                                                                                           |
| WALK THE ALL AND THE THE TABLE                                                                                                                                                                                                       |
| <b>The Mill of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contract of Contr</b> |
| BOOK AND AT A BOOK IN THE REAL PROPERTY.<br>BOOK A LAND TO A LAND TO A LAND TO A COMPANY                                                                                                                                             |
| The state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of t       |
| TAN VIII FOR A TENSION CON                                                                                                                                                                                                           |
| <b>OF BOILDING A ALCOHOL CAPTAIN</b>                                                                                                                                                                                                 |
| <b>OF THE THE THE THE THE THE THE THE THE THE</b>                                                                                                                                                                                    |
| MEL LAND TO BE A TURN LAND TO BE<br><b>KOMPUNISH STANKTON REFORE</b>                                                                                                                                                                 |
| THE SAN THE REPORT OF THE STATE OF THE REAL PROPERTY.                                                                                                                                                                                |
| THE SAN - SHOP AND A REPORT OF A REPORT OF<br>BERRY TANK THE TEACHER                                                                                                                                                                 |
| <b>AFRICAN MARKETING IN A BOOK OF CAREER</b>                                                                                                                                                                                         |
| OF TANK YOU ARE THE REAL PART<br>54月1日 本日子 又相当 、必由正直与人出生。                                                                                                                                                                            |
| LOCAL PRODUCT VOID CONTRACTOR                                                                                                                                                                                                        |
| LOCAL CARRIER X 3 X X 3 CHARACTER                                                                                                                                                                                                    |
| <b>ALCOHOL: 1 IN ANTECEDE</b>                                                                                                                                                                                                        |
| 第三十五章 第一一条一组 不是 不是 人名英塞尔 计                                                                                                                                                                                                           |
| NAXI ( - INHA) MOVIEW<br>NAKT ( - ALIAH A ME - ALA                                                                                                                                                                                   |

Figure 38: Reconstructions for CIFAR-10 10 Classes, Standard Dynamics, 4096 width.

| $N = 500$ , $w = 1024$ , Linearized Dynamics, Odd rows = Reconstructions, Even rows = train images<br>$\frac{1}{2}$                                                                                                                |
|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>THE STRAND AND A REAL PROPERTY NOW</b>                                                                                                                                                                                          |
| <b>A STATE OF BUILDING AND STATE OF BUILDING</b>                                                                                                                                                                                   |
| <b>NOOM HT MULLET</b>                                                                                                                                                                                                              |
| <u> Alexandria de Santa de Alexandria de Santa de Alexandria de Alexandria de Alexandria de Alexandria de Alexandria de </u><br><b>ALLEY AND CARTES TO ALLEY OF</b>                                                                |
| <b>AND A REPORT OF A PART AND RESIDENT</b>                                                                                                                                                                                         |
| <b>ENGAL PRIL</b><br>ロクシャミス                                                                                                                                                                                                        |
| The State of the State of the State of the State of the State of the State of the State of the State of<br><b>SOLUTION - LASTER</b>                                                                                                |
| <b>REAL AND RELEASED AT A REPORT OF PROPERTY</b>                                                                                                                                                                                   |
| <b>Rolls of Ministers</b><br>$\mathbf{L}^{\mathcal{S}}$ .                                                                                                                                                                          |
| <b>REAL PROPERTY AND REAL PROPERTY</b>                                                                                                                                                                                             |
| <b>THE REAL PROPERTY OF A REAL PROPERTY OF A REAL PROPERTY OF A REAL PROPERTY OF A REAL PROPERTY OF A REAL PROPERTY</b>                                                                                                            |
| <b>AND AND A REPORT OF A PARTICULAR PROPERTY</b><br><b>SCORED MAR</b><br><b>BARA ARAB</b><br>NATION AND I                                                                                                                          |
| and the state of the state of the state of the state of the state of the state of the state of the state of the                                                                                                                    |
| STAR A SEX STAR SCOTT                                                                                                                                                                                                              |
| The contract of the contract of the contract of the contract of the contract of the contract of the contract of                                                                                                                    |
| AST HOW<br><b>LAND MODE</b>                                                                                                                                                                                                        |
| and the state of the state of the state of the state of the state of the state of the state of the<br><b>MONGELL</b>                                                                                                               |
| And the state of the state of the state of the state of the state of the state of the state of the state of the                                                                                                                    |
| I A RICHARD TO BE THE REAL PROPERTY.<br>A-ES-1                                                                                                                                                                                     |
| Maria Maria Maria Maria Maria Maria<br>US SHELL AND STRUCTURE<br><b>DOMESTIC: REALLY</b>                                                                                                                                           |
| Book and the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the s     |
| STATISTICS AND ALL STATISTICS.                                                                                                                                                                                                     |
| <b>New York Communication of the Communication</b>                                                                                                                                                                                 |
| THE REAL PROPERTY OF STREET                                                                                                                                                                                                        |
| A PARTIES OF PROPERTY AND RESIDENCE                                                                                                                                                                                                |
| W--AMPLY SERIES STATE TO THE TABLE                                                                                                                                                                                                 |
| The complete the state of the complete the complete of<br><b>CERTIFICATION</b>                                                                                                                                                     |
| FOR ASSESSMENT ROOM AND RESIDENT                                                                                                                                                                                                   |
| The state of the first the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the<br>繥 |
| <b>CALL AND RESIDENCE OF A REPORT OF A STATE OF A REPORT OF A REPORT OF A REPORT OF A REPORT OF A REPORT OF A RE</b>                                                                                                               |
| <b>CLEAR FOR THE ANGEL AND THE</b>                                                                                                                                                                                                 |
|                                                                                                                                                                                                                                    |
| $\bullet$ - $\bullet$ $\bullet$ $\circ$<br>LOCK -COLOR KARAL                                                                                                                                                                       |

CIFAR-10 10 Classes Reconstructions sorted by L2 Distance

Figure 39: Reconstructions for CIFAR-10 A10 Classes, Linearized Dynamics, 1024 width.

| CIFAR-10 10 Classes Reconstructions sorted by L2 Distance |  |  |  |  |  |
|-----------------------------------------------------------|--|--|--|--|--|
|                                                           |  |  |  |  |  |

| N = 500, w = 1024, Standard Dynamics, Odd rows = Reconstructions, Even rows = train images |       |       |       |       |       |       |       |       |       |
|--------------------------------------------------------------------------------------------|-------|-------|-------|-------|-------|-------|-------|-------|-------|
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |
| Image                                                                                      | Image | Image | Image | Image | Image | Image | Image | Image | Image |

<span id="page-44-0"></span>Figure 40: Reconstructions for CIFAR-10 10 Classes, Standard Dynamics, 1024 width.
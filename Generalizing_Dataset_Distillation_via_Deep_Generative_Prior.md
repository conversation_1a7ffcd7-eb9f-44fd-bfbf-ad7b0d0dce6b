# Generalizing Dataset Distillation via Deep Generative Prior

<span id="page-0-0"></span><PERSON><sup>1</sup> <PERSON><PERSON><sup>1</sup> <PERSON><sup>1</sup> <PERSON><sup>2</sup> <PERSON><PERSON><PERSON><sup>3</sup> <sup>1</sup>Massachusetts Institute of Technology  $2UC$  Berkeley  $3C$ arnegie Mellon University

[georgecazenavette.github.io/glad](https://georgecazenavette.github.io/glad/)

Image /page/0/Picture/3 description: The image displays a grid of animal and fruit images, organized into three categories: ImageNet-Birds, ImageNet-Fruits, and ImageNet-Cats. The ImageNet-Birds section features images of a peacock, flamingos, a macaw, pelicans, penguins, an eagle, a toucan, an ostrich, a black swan, and a cockatoo. The ImageNet-Fruits section shows a pineapple, bananas, strawberries, an orange, a lemon, bell peppers, a pomegranate, a fig, cucumbers, and Granny Smith apples. The ImageNet-Cats section includes images of an Egyptian cat, a Siamese cat, a tabby cat, a Bengal cat, a Persian cat, a tiger, a jaguar, a lion, a snow leopard, and a lynx.

Figure 1. Visualizations of distilled images from three ImageNet subsets. Rather than distilling into pixel space, our *Deep Generative Prior* constrains the images to be more coherent, leading to better cross-architectural generalization.

## Abstract

*Dataset Distillation aims to distill an entire dataset's knowledge into a few synthetic images. The idea is to synthesize a small number of synthetic data points that, when given to a learning algorithm as training data, result in a model approximating one trained on the original data. Despite recent progress in the field, existing dataset distillation methods fail to generalize to new architectures and scale to high-resolution datasets. To overcome the above issues, we propose to use the learned prior from pre-trained deep generative models to synthesize the distilled data. To achieve this, we present a new optimization algorithm that distills a large number of images into a few intermediate feature vectors in the generative model's latent space. Our method augments existing techniques, significantly improving crossarchitecture generalization in all settings.*

## 1. Introduction

Many recent advancements in machine learning come from combining large networks and big data. Such trained models have shown strong capabilities to perform a wide range of diverse tasks [\[11,](#page-8-0) [21,](#page-8-1) [50\]](#page-9-0). Despite the great potential of such approaches, we, as a scientific community, are also curious about the underlying principles and limitations. Do networks have to be large to express the functions of interest? Do datasets have to be big? Can training on "small data" be equally successful?

The seminal work on Knowledge Distillation [\[31\]](#page-8-2) and recent discoveries such as Lottery Ticket Hypothesis [\[25\]](#page-8-3) have revealed small models are often sufficient to approximate the same functions as large trained models. Dataset Distillation [\[64\]](#page-9-1) investigates the analogous yet orthogonal question on datasets: is there a small succinct dataset sufficient for training models? In other words, Dataset Distillation aims to distill a large dataset into a small (synthetic) one, such that training on the small dataset yields comparable performance (Figure [2\)](#page-1-0). Since its introduction, Dataset Distillation has gained much attention in the research community, leading to many applications [\[13,](#page-8-4) [22,](#page-8-5) [43,](#page-9-2) [55\]](#page-9-3), and a growing series of methods that address the distillation problem: generating a discrete set of images effective for model training [\[12,](#page-8-6) [62,](#page-9-4) [64,](#page-9-1) [67,](#page-9-5) [69,](#page-9-6) [70,](#page-9-7) [72\]](#page-9-8). When optimizing for a small, synthetic vision dataset, such methods typically optimize the *raw* pixel values of the images.

Unfortunately, these methods face two major challenges, limiting both their scientific value and empirical applications.

<span id="page-1-1"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: This diagram illustrates a process involving generative latent distillation. On the left, 'Real Training Images' (1300 per class) are shown, feeding into a process labeled 'Generative Latent Distillation'. This process produces 'Distilled Latent Codes' (1 per class), represented as a grid of colored squares. These codes are then fed into a 'Generative Prior' labeled 'G', which generates 'Synthetic Images' (1 per class). These synthetic images are collectively referred to as the 'Final Deliverable'. The diagram also shows two parallel training paths. One path involves training a neural network with the 'Synthetic Images', leading to 'Similar Test Performance'. The other path involves training a neural network with the 'Real Training Images', also leading to 'Similar Test Performance'.

Figure 2. Rather than directly distilling a dataset into synthetic pixels (like all previous methods), our new method GLaD instead distills into the latent space of a *deep generative prior*. This enforces a tuneable amount of coherence in the output synthetic images, leading to far better generalization to new architectures.

First, the distilled synthetic dataset is often optimized w.r.t. a specific network architecture, but struggles to generalize to other architectures. Second, while producing insightful distilled images on toy datasets, these methods generally fail to work well on higher-resolution datasets  $(e.g., \geq 128 \times 128$  resolution) and tend to distill visually noisy images with subpar performance.

In this work, we argue that both issues are partially caused by parameterizing the synthetic dataset in pixel space. Directly optimizing pixels can be susceptible to learning high-frequency patterns that overfit the specific architecture used in training. To address this, we consider regularizing the distillation process to some prior that may help cross-architecture generalization. However, how and where to perform this regularization poses a delicate balance. For example, restricting our synthetic set to the real data manifold can significantly reduce the cross-architecture performance gap but is too strong a regularization to learn good distilled datasets. In the limit, it reduces to coreset selection [\[7,](#page-8-7) [9,](#page-8-8) [28,](#page-8-9) [59\]](#page-9-9), which is known to not work as well [\[12,](#page-8-6) [64,](#page-9-1) [67,](#page-9-5) [70\]](#page-9-7).

We propose Generative Latent Distillation (GLaD), which employs a *deep generative prior* by parameterizing the synthetic dataset in the *intermediate* feature space of generative models, such as Generative Adversarial Networks (GANs) [\[27\]](#page-8-10). Our prior encourages the learned datasets to be more generalizable to new architectures but is also lax enough to *not* prohibitively restrict the expressiveness of the distilled dataset. GLaD acts as an add-on module and can easily be applied to existing and future dataset distillation methods.

There is flexibility in choosing which generative model to use as our prior. Using a generator trained on the same target dataset as the distillation algorithm, our prior uses no additional data or information but consistently improves various distillation algorithms. However, to obtain the best distilled synthetic dataset, we may use more powerful generators trained on larger datasets. On the other extreme, we explore using randomly initialized generators and generators trained on out-of-distribution datasets. We show that they generate aesthetically pleasing synthetic images with distinct visual characteristics and achieve comparable distillation performance. In short, while different generator choices affect distillation results in intriguing ways, GLaD consistently improves performance across many datasets and multiple distillation algorithms.

Within a deep generative model, there is a spectrum of different latent space choices corresponding to different layers in the model  $[1, 47, 75]$  $[1, 47, 75]$  $[1, 47, 75]$  $[1, 47, 75]$  $[1, 47, 75]$ . Our analysis reveals a trade-off between realism (earlier layer latents) and flexibility (later layer latents), and highlights that using an intermediate latent space achieves a nice balance and consistent performance gain compared to the wildly used raw-pixel parametrization.

In Section [4,](#page-5-0) we perform extensive experiments on CIFAR-10 and ImageNet subsets at resolutions up to  $512 \times$ 512. We integrate GLaD with three distinct distillation algorithms (Gradient Matching [\[70\]](#page-9-7), Distribution Matching [\[69\]](#page-9-6), and Trajectory Matching [\[12\]](#page-8-6)), and consistently observe significant improvements in cross-architecture generalization. Our analysis of results from different configurations provides a better understanding of the effect of different generative models and latent spaces. Additionally, our method drastically reduces the high-frequency noise present in highresolution datasets distilled into pixel space, leading to visually pleasing images that may have implications in artistic <span id="page-2-1"></span>and design applications [\[13\]](#page-8-4).

GLaD is a plug-and-play addition to existing and future distillation methods, allowing them to scale up to more realistic datasets and generalize better to different architectures. Given the goal of distilling large-scale datasets, we propose that leveraging generative models and differentiable parameterizations is a natural path forward. Our code and further visualizations be found on our project page [https:](https://georgecazenavette.github.io/glad/) [//georgecazenavette.github.io/glad/](https://georgecazenavette.github.io/glad/).

### 2. Related Work

Dataset Distillation was introduced by Wang et al. [\[64\]](#page-9-1) as an investigation as to how training on very little data could update a model to certain desired behaviors. Several improvements have been made since then, including soft learned labels [\[56\]](#page-9-12), data augmentation [\[67\]](#page-9-5), and trajectory/gradient matching [\[12,](#page-8-6) [70\]](#page-9-7). Many applications have also been explored, such as neural architecture search [\[32\]](#page-8-12) and continual learning  $[67, 70]$  $[67, 70]$  $[67, 70]$ . Some works  $[32, 33]$  $[32, 33]$  $[32, 33]$ also employ generative models but train them from scratch to produce good synthetic training samples, in a manner different from our objective and formulation.

Several concurrent works tackle the dataset distillation via a re-parameterization of the distilled data as a set of bases [\[20,](#page-8-14) [42\]](#page-9-13) or a greater number of lower-resolution images [\[39\]](#page-9-14), motivated by a memory compression standpoint. Our method differs in that we use generative model parameterizations to achieve better distillation performance. Concurrent work [\[68\]](#page-9-15) also learns synthetic datasets in the latent space of a generative model by first inverting the *entire* training set into this latent space before fine-tuning the latents on the distillation objective.

GAN Inversion and Latent Spaces. Our work applies generative priors by parameterizing distilled images into different latent spaces of GANs. This is related to the line of research of GAN inversion  $[10, 74]$  $[10, 74]$  $[10, 74]$ , whose goal is to project a real input image to GAN latent codes for image editing and data augmentation purposes [\[14,](#page-8-16) [15,](#page-8-17) [34,](#page-8-18) [57,](#page-9-17) [63,](#page-9-18) [65,](#page-9-19) [73\]](#page-9-20). These works have proposed various latent spaces (roughly similar to different activation spaces) [\[1,](#page-8-11) [2,](#page-8-19) [29,](#page-8-20) [66,](#page-9-21) [75\]](#page-9-11), wherein image editing or model fine-tuning can be efficiently performed [\[3,](#page-8-21) [5,](#page-8-22) [6,](#page-8-23) [46,](#page-9-22) [47,](#page-9-10) [51,](#page-9-23) [52\]](#page-9-24).

The choice of specific latent space represents a trade-off between expressiveness and reconstruction quality, as shown by recent studies [\[58,](#page-9-25) [76\]](#page-10-0). In our experiments, we leverage a spectrum of such latent spaces designed for the StyleGANfamily of models [\[35,](#page-8-24) [37,](#page-8-25) [38,](#page-8-26) [53\]](#page-9-26) and discover that such a trade-off also exists in our case. The ideal distilled images are not realistic but can benefit from some level of generative prior. Our experiments perform an in-depth analysis on this aspect and show that an *intermediate* generative prior (i.e., starting from an intermediate layer) yields the best performance.

| <b>Algorithm 1</b> Generative Latent Distillation                   |                                                                   |
|---------------------------------------------------------------------|-------------------------------------------------------------------|
| <b>Input:</b> Alg: Distillation algorithm (MTT, DC, or DM).         |                                                                   |
| <b>Input:</b> $\mathcal{T}$ : Real training set.                    |                                                                   |
| <b>Input:</b> $\mathcal{A}$ : Differentiable augmentation function. |                                                                   |
| <b>Input:</b> G: Pre-trained generator.                             |                                                                   |
| <b>Input:</b> $P_z$ : Distribution of latent initializations.       |                                                                   |
| 1: $\mathcal{Z} \sim P_z$                                           | $\triangleright$ Initialize distilled latents                     |
| 2: for each distillation step $do$                                  |                                                                   |
| 3: $\mathcal{S} = G(\mathcal{Z})$                                   | $\triangleright$ Get images from latents                          |
| 4: $\mathcal{L} = \text{Alg}(\mathcal{S}, \mathcal{T})$             | $\triangleright$ Compute distillation loss                        |
| 5: $\mathcal{Z} \leftarrow \text{SGD}(\mathcal{Z}; \mathcal{L})$    | $\triangleright$ Update $\mathcal Z$ with respect to $\mathcal L$ |
| 6: end for                                                          |                                                                   |
| <b>Output:</b> Distilled images $S = G(Z)$                          |                                                                   |

<span id="page-2-0"></span>

### 3. Generative Latent Distillation (**GLaD**)

To date, all existing methods of dataset distillation [\[8,](#page-8-27) [12,](#page-8-6) [44,](#page-9-27) [45,](#page-9-28) [62,](#page-9-4) [64,](#page-9-1) [67,](#page-9-5) [70\]](#page-9-7) have relied on a "backbone" architecture to formulate the distillation objective. Since optimizing distilled images in pixel space allows too much freedom to overfit to the backbone architecture, we propose introducing a *deep generative prior* to the distillation process as a form of regularization by optimizing latent codes of a pre-trained generative model rather than raw pixels. We call our method *Generative Latent Distillation* (GLaD).

#### 3.1. Preliminaries on Dataset Distillation Methods

For completeness, we briefly review the three dataset distillation methods on which we conduct experiments: Gradient Matching (DC) [\[70\]](#page-9-7), Distribution Matching (DM) [\[69\]](#page-9-6), and Matching Training Trajectories (MTT) [\[12\]](#page-8-6). These three methods all seek to distill a small synthetic dataset  $S$  such that a model trained from scratch on  $S$  will have similar performance as a model trained on the full, real dataset  $\mathcal{T}$ .

Dataset Condensation (DC) enforces that the gradients of the classification loss  $\ell$  w.r.t. the synthetic images match those of the real images. For a network with parameters  $\theta$ trained on the synthetic data for some number of iterations, the gradient matching loss is

$$
\mathcal{L}_{\text{DC}} = 1 - \frac{\nabla_{\theta} \ell^{S}(\theta) \cdot \nabla_{\theta} \ell^{T}(\theta)}{\|\nabla_{\theta} \ell^{S}(\theta)\| \|\nabla_{\theta} \ell^{T}(\theta)\|}.
$$
 (1)

Distribution Matching (DM) takes a different approach and instead requires that a feature extractor yields similar output for real and synthetic images of a corresponding class. For a randomly initialized feature extractor  $\psi$ , the distribution matching loss is

$$
\mathcal{L}_{\text{DM}} = \sum_{c} \left\| \frac{1}{|\mathcal{T}_c|} \sum_{\mathbf{x} \in \mathcal{T}_c} \psi(\mathbf{x}) - \frac{1}{|\mathcal{S}_c|} \sum_{\mathbf{s} \in \mathcal{S}_c} \psi(\mathbf{s}) \right\|^2, \quad (2)
$$

where  $\mathcal{T}_c$ ,  $\mathcal{S}_c$  are the real and synthetic images for class c.

<span id="page-3-1"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: A bar chart displays validation accuracy for different models, labeled W+, F0, F4, F8, F12, F16, F20, F24, and Pixel. The y-axis represents validation accuracy, ranging from 28% to 36%. The bars show varying accuracy levels, with F12 having the highest accuracy at approximately 34.5%, followed closely by F8 and F16. The chart also includes images below each corresponding model label, depicting groups of flamingos. An arrow at the bottom indicates a spectrum from 'Stronger Prior: More Realistic' to 'Weaker Prior: More Expressive', with the models progressing along this spectrum from left to right.

Figure 3. Cross-Architecture performance of **MTT** [\[12\]](#page-8-6) per distillation space averaged over ImageNet-[A,B,C,D,E]. Using spaces of earlier layers (left side) imposes stronger priors and yields more realistic images but is not as expressive as using spaces of later layers (right side). A proper amount of generative prior is helpful for cross-architecture generalization, while too strong a prior limits expressivity and thus hurts distillation performance. An intermediate space (e.g., F12) balances this trade-off.

Both the DC and DM methods are augmented with Differential Siamese Augmentation (DSA) [\[67\]](#page-9-5) such that the same differential augmentations [\[35,](#page-8-24) [71\]](#page-9-29) are applied to real and synthetic images at each iteration.

Matching Training Trajectories (MTT) focuses on consistent training over a longer time horizon by encouraging the synthetic data to induce similar update trajectories in parameter space. Prior to distillation, many *expert trajectories*  $\{\theta_t^*\}_0^T$  are obtained by training networks from scratch on the full real dataset and storing parameter snapshots at given intervals. At each distillation step, a random expert trajectory and starting timestamp  $\theta_t^*$  are sampled. A student network is then initialized at the given expert timestamp  $\hat{\theta}_t := \theta_t^*$  and trained for N iterations on the *synthetic data*. The trajectory-matching loss is then calculated as the normalized mean-squared error between the final parameters of the student network  $\hat{\theta}_{t+N}$  and those of a future timestep (M steps ahead) of the expert trajectory  $\theta_{t+M}^*$ :

$$
\mathcal{L}_{\text{MTT}} = \frac{\|\hat{\theta}_{t+N} - \theta_{t+M}^*\|^2}{\|\theta_t^* - \theta_{t+M}^*\|^2}.
$$
 (3)

We make use of a recent method, dubbed TESLA [\[18\]](#page-8-28), which solves the memory issue of MTT by re-formulating the loss function to an equivalent form that only requires the storing of one set of network gradients at a time, thereby reducing the memory usage from linear to constant w.r.t. the number of inner loops  $N$ . However, we do not use the method's soft label assignment.

### 3.2. **GLaD**: Adding a Deep Generative Prior

Rather than naively optimizing the synthetic pixels directly (as in all previous methods  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  $[8, 12, 44, 45, 62, 64, 67,$  [70\]](#page-9-7)), we propose applying a *deep generative prior* to our distillation process by means of distilling into the latent space of a pre-trained generative model. We find that such a prior greatly increases the generalization of the synthetic dataset to architectures other than the "backbone" model used in the distillation process (the lack of such generalization being one of the largest limitations of previous methods). Furthermore, our new parametrization facilitates the distillation of even larger-resolution synthetic data devoid of the high-frequency noise induced by previous distillation methods. Lastly, the deep generative prior acts as a plug-and-play addition to any existing and future methods of dataset distillation.

Concretely, we consider a deep generative model  $G$  that outputs samples  $G(z)$  given latent vector  $z$  (e.g., a GAN). At distillation time, we parameterize the small synthetic dataset  $S$  as

$$
\mathcal{S} \triangleq \{ G(z) \colon z \in \mathcal{Z} \},\tag{4}
$$

where  $\mathcal Z$  is a set of latent vectors. Since G is fully differentiable, we can optimize  $Z$  w.r.t. any distillation objective, such as  $\mathcal{L}_{DC}$ ,  $\mathcal{L}_{DM}$  or  $\mathcal{L}_{MTT}$ . Please see Algorithm [1](#page-2-0) for a complete write-up of our method.

### 3.3. Choosing a Generative Model and Latent Space

For a flexible and effective parameterization, we fill the role of our deep generative prior with the recently proposed StyleGAN-XL [\[53\]](#page-9-26), a modified version of StyleGAN3 [\[36\]](#page-8-29). StyleGAN generators can not only output high-fidelity images [\[35\]](#page-8-24), but also (1) provide multiple flexible latent spaces for parameterizing images  $[1, 47]$  $[1, 47]$  $[1, 47]$  and  $(2)$  inherently impose diverse and interesting priors via architecture (even at random initialization) [\[4\]](#page-8-30). In our experiments, this allows us to

<span id="page-4-4"></span><span id="page-4-1"></span>

| Distil. Alg. | Distil. Space        | ImNet-A                         | ImNet-B                         | ImNet-C                         | ImNet-D                         | ImNet-E                         | ImNette                         | ImWoof                          | ImNet-Birds                     | ImNet-Fruits                    | ImNet-Cats                      |
|--------------|----------------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|
| MTT [12]     | Pixel<br>GLaD (Ours) | 33.4 ± 1.5<br><b>39.9 ± 1.2</b> | 34.0 ± 3.4<br><b>39.4 ± 1.3</b> | 31.4 ± 3.4<br><b>34.9 ± 1.1</b> | 27.7 ± 2.7<br><b>30.4 ± 1.5</b> | 24.9 ± 1.8<br><b>29.0 ± 1.1</b> | 24.1 ± 1.8<br><b>30.4 ± 1.5</b> | 16.0 ± 1.2<br><b>17.1 ± 1.1</b> | 25.5 ± 3.0<br><b>28.2 ± 1.1</b> | 18.3 ± 2.3<br><b>21.1 ± 1.2</b> | 18.7 ± 1.5<br><b>19.6 ± 1.2</b> |
| DC [70]      | Pixel<br>GLaD (Ours) | 38.7 ± 4.2<br><b>41.8 ± 1.7</b> | 38.7 ± 1.0<br><b>42.1 ± 1.2</b> | 33.3 ± 1.9<br><b>35.8 ± 1.4</b> | 26.4 ± 1.1<br><b>28.0 ± 0.8</b> | 27.4 ± 0.9<br><b>29.3 ± 1.3</b> | 28.2 ± 1.4<br><b>31.0 ± 1.6</b> | 17.4 ± 1.2<br><b>17.8 ± 1.1</b> | 28.5 ± 1.4<br><b>29.1 ± 1.0</b> | 20.4 ± 1.5<br><b>22.3 ± 1.6</b> | 19.8 ± 0.9<br><b>21.2 ± 1.4</b> |
| DM [69]      | Pixel<br>GLaD (Ours) | 27.2 ± 1.2<br><b>31.6 ± 1.4</b> | 24.4 ± 1.1<br><b>31.3 ± 3.9</b> | 23.0 ± 1.4<br><b>26.9 ± 1.2</b> | 18.4 ± 0.7<br><b>21.5 ± 1.0</b> | 17.7 ± 0.9<br><b>20.4 ± 0.8</b> | 20.6 ± 0.7<br><b>21.9 ± 1.1</b> | 14.5 ± 0.9<br><b>15.2 ± 0.9</b> | 17.8 ± 0.8<br><b>18.2 ± 1.0</b> | 14.5 ± 1.1<br><b>20.4 ± 1.6</b> | 14.0 ± 1.1<br><b>16.1 ± 0.7</b> |

Table 1. ImageNet (128×128) Performance on Unseen Architectures. These results come from training AlexNet, VGG11, ResNet18, and a Vision Transformer on our synthetic datasets (that were distilled using a ConvNet) and averaging their performances on the real validation sets. Applying the deep generative prior by distilling into F-space rather than pixel space significantly improves the cross-architecture generalization of all methods across all sampled datasets.

| Distil. Alg. | Distil. Space | ImNet-A         | ImNet-B         | ImNet-C         | ImNet-D         | ImNet-E         |
|--------------|---------------|-----------------|-----------------|-----------------|-----------------|-----------------|
| DC           | Pixel         | 52.3±0.7        | 45.1±8.3        | 40.1±7.6        | 36.1±0.4        | 38.1±0.4        |
|              | GLaD (Ours)   | <b>53.1±1.4</b> | <b>50.1±0.6</b> | <b>48.9±1.1</b> | <b>38.9±1.0</b> | <b>38.4±0.7</b> |
| DM           | Pixel         | 52.6±0.4        | 50.6±0.5        | 47.5±0.7        | 35.4±0.4        | 36.0±0.5        |
|              | GLaD (Ours)   | <b>52.8±1.0</b> | <b>51.3±0.6</b> | <b>49.7±0.4</b> | <b>36.4±0.4</b> | <b>38.6±0.7</b> |

Table 2. Performance with 10 images/class.

<span id="page-4-2"></span>

| Distillation Method | Distillation Space | Unseen Evaluation Architecture |                |                |                |                |
|---------------------|--------------------|--------------------------------|----------------|----------------|----------------|----------------|
|                     |                    | AlexNet                        | ResNet18       | VGG11          | ViT            | Average        |
| MTT [12]            | Pixel              | 26.8 $\pm$ 0.6                 | 23.4 $\pm$ 1.3 | 24.9 $\pm$ 0.8 | 21.2 $\pm$ 0.4 | 24.1 $\pm$ 0.8 |
|                     | GLaD (Rand G)      | 27.4 $\pm$ 0.3                 | 30.1 $\pm$ 1.2 | 29.0 $\pm$ 0.8 | 21.9 $\pm$ 0.3 | 27.1 $\pm$ 0.7 |
|                     | GLaD (Trained G)   | 27.9 $\pm$ 0.6                 | 30.2 $\pm$ 0.6 | 31.3 $\pm$ 0.7 | 22.7 $\pm$ 0.4 | 28.0 $\pm$ 0.6 |
| DC [70]             | Pixel              | 25.9 $\pm$ 0.2                 | 27.3 $\pm$ 0.5 | 28.0 $\pm$ 0.5 | 22.9 $\pm$ 0.3 | 26.0 $\pm$ 0.4 |
|                     | GLaD (Rand G)      | <b>30.1</b> $\pm$ 0.5          | 27.3 $\pm$ 0.2 | 28.0 $\pm$ 0.9 | 21.2 $\pm$ 0.6 | 26.6 $\pm$ 0.5 |
|                     | GLaD (Trained G)   | 26.0 $\pm$ 0.7                 | 27.6 $\pm$ 0.6 | 28.2 $\pm$ 0.6 | 23.4 $\pm$ 0.2 | 26.3 $\pm$ 0.5 |
| DM [69]             | Pixel              | 22.9 $\pm$ 0.2                 | 22.2 $\pm$ 0.7 | 23.8 $\pm$ 0.5 | 21.3 $\pm$ 0.5 | 22.6 $\pm$ 0.5 |
|                     | GLaD (Rand G)      | 23.7 $\pm$ 0.3                 | 21.7 $\pm$ 1.0 | 24.3 $\pm$ 0.8 | 21.4 $\pm$ 0.2 | 22.8 $\pm$ 0.6 |
|                     | GLaD (Trained G)   | 25.1 $\pm$ 0.5                 | 22.5 $\pm$ 0.7 | 24.8 $\pm$ 0.8 | 23.0 $\pm$ 0.1 | 23.8 $\pm$ 0.5 |

Table 3. CIFAR-10 Performance on Unseen Architectures. Unlike the high-resolution data, we only see a large improvement to MTT and a moderate improvement to DM. Interestingly, we also see that distilling into the latent space of an *un-trained* generator still yields results on-par or better than pixel-space distillation.

probe the effects of choosing latent spaces from different layers and using generators trained on out-of-distribution datasets or even at random initialization.

Distilling into the latent space of StyleGAN-XL can be thought of as a pseudo-inversion task. However, we found that distilling into even extended  $W^+$  latent space<sup>[1](#page-4-0)</sup>[\[1\]](#page-8-11), the most flexible of the traditional StyleGAN inversion spaces, was too restrictive for our objective (see Figure [3\)](#page-3-0). It limits the synthetic images to be realistic, but, unlike real image inversion, these images are optimized for the distillation task and do not require realism. Prior inversion works propose the "Fn" spaces in StyleGAN as an alternative that allows images to be more diverse and flexible [\[47,](#page-9-10) [75\]](#page-9-11). As such, we choose to distill into these "Fn" spaces, which means optimizing the  $n$ -th hidden layer of StyleGAN-XL's "synthesis" network's latent representation along with all subsequent W<sup>+</sup> modulation codes. Note that in this work,

<span id="page-4-3"></span>

| Distillation Space | ImNet-A    | ImNet-B    | ImNet-C    | ImNet-D    | ImNet-E    |
|--------------------|------------|------------|------------|------------|------------|
| Pixel              | 38.3 ± 4.7 | 32.8 ± 4.1 | 27.6 ± 3.3 | 25.5 ± 1.2 | 23.5 ± 2.4 |
| GLaD (ImageNet G)  | 37.4 ± 5.5 | 41.5 ± 1.2 | 35.7 ± 4.0 | 27.9 ± 1.0 | 29.3 ± 1.2 |
| GLaD (Random G)    | 39.3 ± 2.0 | 40.3 ± 1.7 | 35.0 ± 1.7 | 27.9 ± 1.4 | 29.8 ± 1.4 |
| GLaD (Pokémon G)   | 39.1 ± 2.0 | 39.4 ± 1.5 | 35.3 ± 1.3 | 28.0 ± 1.2 | 29.5 ± 1.3 |
| GLaD (FFHQ G)      | 38.3 ± 5.2 | 40.2 ± 1.1 | 34.9 ± 1.1 | 27.2 ± 0.9 | 29.4 ± 2.1 |

Table 4. Higher-resolution ( $256 \times 256$ ) datasets distilled (using DC) into the latent space of a randomly-initialized generator as well as generators on Pokemon or FFQH still realize significant improve- ´ ments in cross-architecture generalization over their pixel-space counterparts.

 $z$  and  $\mathcal Z$  refer to the concatenation of the Fn feature map with the  $W^+$  codes, not the traditional StyleGAN  $z$ -space. Please see the appendix for details of the StyleGAN-XL architecture and the "Fn" spaces.

Since these "Fn" spaces are from intermediate layers and do not have associated prior distributions, we initialize our latent z vectors using the empirical distribution of latent vectors of the corresponding classes. With access to the earlier layers of G, this can be easily computed. Please see the appendix for details of our initialization scheme.

### 3.4. Memory Saving via Checkpointing

As the forward pass through modern deep generative models usually requires copious amounts of GPU VRAM, our method (if implemented naively) becomes difficult to run on a limited number of GPUs. To circumvent this issue, we employ a technique inspired by gradient checkpointing [\[16\]](#page-8-31). At each distillation iteration, we first obtain our synthetic images  $S = G(\mathcal{Z})$  *without* tracking any gradients. We then calculate our distillation loss  $\mathcal{L}$ , compute the gradient of this loss with respect to our synthetic images ( $\partial \mathcal{L}/\partial \mathcal{S}$ ), and delete the computation graph used to compute  $\mathcal L$  and its gradient. To compute ∂L/∂Z, we *re*-compute the forward pass through  $G, \mathcal{S} = G(\mathcal{Z})$ , this time tracking gradients such that we know  $\partial \mathcal{S}/\partial \mathcal{Z}$ . From here, application of the chain rule gives us  $\partial \mathcal{L}/\partial \mathcal{Z} = (\partial \mathcal{L}/\partial \mathcal{S})(\partial \mathcal{S}/\partial \mathcal{Z})$  which we use to update the latent codes for our synthetic data. For example, with  $128 \times 128$ -resolution StyleGAN-XL, this memory-saving trick allows us to save nearly 2GB memory

<span id="page-4-0"></span><sup>&</sup>lt;sup>1</sup>Here W space refers to the output space of StyleGAN-XL's MLP "mapping" network, and W<sup>+</sup> allows for different W space vectors at different layers.

<span id="page-5-2"></span>Image /page/5/Figure/0 description: This image displays a comparison of image generation results across three different datasets: ImageNet-A with MTT [12], ImageNet-B with DC [70], and ImageNet-C with DM [69]. Each dataset is presented with two rows of generated images. The top row for each dataset is labeled "Pixel-Space: Cross-Arch" with corresponding percentages: 33.4% for ImageNet-A, 38.7% for ImageNet-B, and 23.0% for ImageNet-C. The bottom row for each dataset is labeled "GLaD: Cross-Arch" with corresponding percentages: 39.9% for ImageNet-A, 42.1% for ImageNet-B, and 26.9% for ImageNet-C. The generated images vary in clarity and content, with some appearing more abstract or noisy than others.

Figure 4. Across all sampled ImageNet subsets and all distillation methods, the addition of the generative prior (bottom) offers significantly better cross-architecture generalization than pixel-space distillation (top).

Image /page/5/Figure/2 description: The image displays three rows of images, each accompanied by a descriptive label. The first row, labeled "(a) Pixel-Space Distillation; 24.1% Cross Arch. Acc.", contains a series of abstract, colorful images that appear to be generated or processed. The second row, labeled "(b) F2-Space Distillation, Random Generator; 27.1% Cross Arch. Acc.", also shows abstract images, but they seem slightly more defined than those in the first row. The third row, which is partially visible at the bottom, contains more recognizable images, including a bird, a car, a dog, a horse, a boat, and what appears to be a building or structure. This row is labeled with text that is mostly cut off but seems to indicate a different type of distillation or generation process with a higher accuracy percentage.

(c) F2-Space Distillation, Pre-Trained Generator; 28.0% Cross Arch. Acc.

Figure 5. CIFAR-10 distillations using MTT. The images distilled with both the random and pre-trained generators lead to better crossarchitecture generalization than those distilled into pixel space.

per synthetic image with F0 space.

<span id="page-5-0"></span>

### 4. Experiments

We evaluate our method (GLaD) for distilling CIFAR-10 [\[40\]](#page-9-30) and 10-class subsets of ImageNet 1k [\[19\]](#page-8-32), utilizing StyleGAN-XL [\[53\]](#page-9-26) generators trained on these datasets (obtained from the official released model checkpoints).

The code for our experiments is based on the open-source repositories for DC, DM, and MTT and will be released upon publication. For each method, we integrate the deep generative prior directly into the existing code base. For results with and without the generative prior, we use the same set of hyper-parameters  $(N, M, T^+$ , #iterations, etc.) to ensure a fair comparison. For each method, we choose an  $Fn$ space and use it for all datasets. Please see the appendix for experiment details.

Datasets. For low-resolution data, we apply our method to CIFAR-10 [\[40\]](#page-9-30). For higher-resolution data, we use *subsets* of ImageNet-1k [\[19\]](#page-8-32). Previous dataset distillation work [\[12\]](#page-8-6) introduced several subsets based largely on categories and aesthetics, including birds, fruits, and cats. Two other

<span id="page-5-1"></span>Image /page/5/Figure/10 description: The image displays two rows of images, labeled "Init." and "Final." The "Init." row shows a progression from a blurry image to abstract patterns of geometric shapes and then to a textured pattern. The "Final" row shows a progression of images of penguins, starting with a blurry image of a penguin's head and progressing to a group of penguins in water, with increasing clarity and detail from F0 to F24.

Figure 6. Replacing the feed-forward initialization of the F latents with random noise has drastically different effects in the initial image (top) depending on which layer is used. In the optimized images (bottom), we see artifacts with varying levels of granularity depending on the layer.

conventional subsets are ImageNette and ImageWoof [\[24\]](#page-8-33). We also introduce several new 10-class subsets based on the evaluation performance of a ResNet-50 model that has been pre-trained on ImageNet. In this work, "ImageNet-A" consists of the top-10 classes with "ImageNet-B" consisting of the next 10 and so on for "ImageNet-C," "ImageNet-D," and "ImageNet-E." The classes composing all the subsets can be found in the appendix.

Evaluation Protocol. After distilling our synthetic datasets with their respective algorithm, we then evaluate them on a set of unseen architectures. To evaluate a synthetic dataset on a given architecture, we train a network from scratch on the distilled dataset and then evaluate it on the validation set.

The training regiment is the same for all networks and datasets: SGD with momentum,  $\ell_2$  weight decay, and 500 epochs of linear warm-up followed by another 500 of cosine decay. An appropriate (fixed) starting learning rate is used for each architecture, and the final validation set evaluation is done using the exponential moving average of the model's weights. This process is repeated 5 times, and the mean validation accuracy  $\pm$  1 standard deviation is reported. Further details can be found in the appendix.

<span id="page-6-1"></span><span id="page-6-0"></span>Image /page/6/Figure/0 description: The image displays four different generative adversarial network (GAN) outputs, each labeled with its respective GAN type. The top left image, labeled "ImageNet GAN", shows a clear image of colorful birds perched on branches, with a mosaic-like effect. The top right image, labeled "Random GAN", is a blurry, pixelated representation of colorful objects, possibly birds, against a green, speckled background. The bottom left image, labeled "Bebop GAN", presents a distorted, wavy, and abstract interpretation of colorful objects, with fluid, metallic-like textures. The bottom right image, labeled "FFHQ GAN", is also abstract and blurry, showing a cluster of colorful elements against a textured, greenish-gray background, resembling a more naturalistic, albeit distorted, scene.

Pokémon GAN FFHQ GAN

Figure 7. Distilling into the latent space of generators that have not been trained on the relevant data still produces good images. Here we show a "lorikeet" distilled using a randomly initialized generator as well as generators trained on ImageNet, Pokémon, and FFHQ.

Network Architectures. As with prior dataset distillation works [\[18,](#page-8-28) [44,](#page-9-27) [45,](#page-9-28) [62,](#page-9-4) [64,](#page-9-1) [67,](#page-9-5) [69,](#page-9-6) [70,](#page-9-7) [72\]](#page-9-8), we use the ConvNet  $[26]$  architecture as our backbone network. A Depth- $n$ ConvNet consists of n blocks followed by a fully-connected layer where each block consists of a  $3 \times 3$  convolutional layer with 128 filters, instance normalization [\[60\]](#page-9-31), ReLU non-linearity, and  $2 \times 2$  average pooling with stride 2.

We used two sets of models for our cross-architecture generalization experiments. For our CIFAR experiments, we use the AlexNet [\[41\]](#page-9-32), VGG-11 [\[54\]](#page-9-33), and ResNet-18 [\[30\]](#page-8-35) included with the codebase of DC/DSA/DM/MTT along with a Vision Transformer [\[23\]](#page-8-36) from the DC-BENCH [\[17\]](#page-8-37) repository. For our experiments on higher-resolution data, we use slightly modified versions of the networks to accommodate the larger images.

Our experiments are enumerated below to highlight the contributions of our proposed method.

### 4.1. Finding a Suitable Latent Space

Given the depth of StyleGAN-XL [\[53\]](#page-9-26), there are *many* possible latent spaces that GLaD can use to parameterize our synthetic dataset. To find the best latent space for cross-architecture generalization, we experiment with several ImageNet subsets. In Figure [3,](#page-3-0) we see the final distilled images for the "flamingo" class. Earlier latent spaces enforce a stronger prior on the distilled image,

Image /page/6/Figure/9 description: The image displays three columns of generated images, each labeled with a different GAN model: "Random GAN", "FFHQ GAN", and "Pokémon GAN". Each column contains four smaller images arranged in a 2x2 grid. The "Random GAN" column shows abstract, blurry images with colors ranging from yellow and green to red and pink. The "FFHQ GAN" column features images with a more textured, abstract pattern in shades of gray, brown, green, and red. The "Pokémon GAN" column presents images with a similar textured pattern but with a distinct outline, resembling a stylized drawing, in shades of gray, light brown, and red.

Figure 8. When initializing the latents with random noise, each generator has a unique type of structure present in the output. The final optimized images (Figure [7\)](#page-6-0) contain artifacts of this structure.

while later latent spaces offer more flexibility for the optimization to fit to the distillation objective (MTT, DC, or  $DM$ ). Examining Figure [3,](#page-3-0) we see that the images distilled into F12 space result in the best cross-architecture generalization for MTT. Through analogous experiments, we found F16 to be optimal for DC and F20 for DM.

### 4.2. Improving Cross-Architecture Generalization

Arguably the most lacking point of all previous dataset distillation methods, cross-architecture generalization gives a good understanding of how well the distillation method "understands" the classification task rather than simply overfitting to a given architecture. In Table [1,](#page-4-1) we show crossarchitecture results for MTT, DC, and DM with and without the deep generative prior. For each method and dataset, a 1 image-per-class synthetic set is distilled using a Depth-5 ConvNet as the "backbone" architecture. To evaluate crossarchitecture generalization, we use the distilled set to train AlexNet [\[41\]](#page-9-32), ResNet-18 [\[30\]](#page-8-35), VGG-11 [\[54\]](#page-9-33), and ViT-b/16 [\[23\]](#page-8-36) from scratch and record the validation accuracy. We record the average validation accuracy across these 4 architectures in Table [1.](#page-4-1)

For every tested dataset, GLaD's addition of the generative prior slightly or significantly improved the crossarchitecture generalization of all 3 methods.

In Table [3,](#page-4-2) we also include cross-architecture results for CIFAR-10. Even on this lower-resolution data, GLaD significantly improves the performance of both original MTT and original DM while only showing marginal gains on DC.

### 4.3. Latent Initialization and Generator Choices

In previous experiments, we initialized latents  $z$  (which contain outputs of intermediate layers) via a partial feedforward pass through the generator. Here, we experiment with an alternative initialization wherein the feed-forward initialization is replaced with Gaussian noise (with matching mean and variance). As the StyleGAN generator expects a somewhat coherent representation in the latent space, the images at initialization show interesting artifacts that vary in granularity depending on the latent space used (Figure [6\)](#page-5-1).

<span id="page-7-1"></span><span id="page-7-0"></span>Image /page/7/Picture/0 description: The image is a colorful, abstract pattern with a central focus of vibrant reds, yellows, and blues, surrounded by a muted green and gray textured background. The colors in the center appear to be blended and swirled, creating a sense of depth and movement. The overall impression is one of a complex, possibly digital, artwork.

Pixel-Space

Image /page/7/Picture/2 description: The image is an abstract painting with a mosaic-like appearance. It features a chaotic arrangement of geometric shapes in various colors, including red, blue, white, black, and green. Some shapes are sharp and angular, while others are more rounded and organic. The overall impression is one of vibrant energy and movement, with no discernible subject matter.

Figure 9. Generative Latent Distillation (GLaD) allows us to distill datasets into high-resolution artistic images (bottom), while highresolution images distilled into pixel space degenerate into high-

frequency patterns (top). This example is a  $512\times512$  "macaw" distilled using MTT in pixel space versus GLaD.

Using such Gaussian initialization, we found that GLaD can successfully use generators that were trained on *completely* different datasets (such as FFQH [\[37\]](#page-8-25) and Pokémon [\[49\]](#page-9-34)) or even generators that have not been trained at all (a-la

Deep Image Prior [\[61\]](#page-9-35)). In Table [4,](#page-4-3) we compare the pixelspace and standard GAN results to generative latent distillations using generators trained on the FFHQ and Pokemon ´ along with generators that have not been trained at all. These results use DC, and results using MTT and DM can be found in the appendix.

The images distilled using the FFHQ, Pokémon, and random generators still offer cross-architectural generalization improvements over those distilled directly into pixel space, ofter matching or surpassing the results using the standard ImageNet generator.

We also notice that initializing the latents with random Gaussian noise results in aesthetically pleasing images with different "artistic" properties based on the generator used (Figure [7\)](#page-6-0). This trend also extends to even larger images, allowing our method to create class-based digital arts in different styles, such as the "mosaic" in Figure [9.](#page-7-0)

### 5. Discussion and Limitations

In this work, we have proposed leveraging a generative prior to dataset distillation (GLaD). By applying our deep generative prior, we introduce a new method that significantly improves the generalization of the distilled images. This trend extends all the way to (and likely beyond)  $512\times512$  images, allowing us to generate high-quality distilled images at higher resolutions than ever before. Since GLaD acts as a plug-and-play addition to any dataset distillation method, future works can use it to increase the generality of their data and generate higher-resolution images.

Limitations. Introducing StyleGAN-XL to the distillation pipeline creates a massive new memory sink. Our checkpointing trick allows us to mitigate this issue to some extent. However, this also comes at the expense of a second forward pass through the generator. Given that a single pass through the generator is time-consuming, a second pass doubles the overhead. Additionally, a large enough synthetic set requires passes through the generator to be done in multiple batches, further increasing the extra time needed.

Fortunately, GLaD is compatible with any differentiable generative model, so the development of more efficient generative models in the future will naturally reduce the cost of our method as well.

Acknowledgments. This work was partly done by George Cazenavette during his study at CMU. We thank David Charatan, Ana Dodik, and Vincent Sitzmann of MIT's Scene Representation Group for feedback on the early drafts of this work. This work is supported, in part, by the NSF Graduate Research Fellowship and J.P. Morgan Chase Faculty Research Award.

### References

- <span id="page-8-11"></span>[1] Rameen Abdal, Yipeng Qin, and Peter Wonka. Image2stylegan: How to embed images into the stylegan latent space? In *ICCV*, 2019. [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [5](#page-4-4)
- <span id="page-8-19"></span>[2] Rameen Abdal, Yipeng Qin, and Peter Wonka. Image2stylegan++: How to edit the embedded images? In *CVPR*, 2020. [3](#page-2-1)
- <span id="page-8-21"></span>[3] Yuval Alaluf, Or Patashnik, and Daniel Cohen-Or. Restyle: A residual-based stylegan encoder via iterative refinement. In *ICCV*, 2021. [3](#page-2-1)
- <span id="page-8-30"></span>[4] Manel Baradad Jurjo, Jonas Wulff, Tongzhou Wang, Phillip Isola, and Antonio Torralba. Learning to see by looking at noise. *NeurIPS*, 2021. [4](#page-3-1)
- <span id="page-8-22"></span>[5] David Bau, Steven Liu, Tongzhou Wang, Jun-Yan Zhu, and Antonio Torralba. Rewriting a deep generative model. In *ECCV*. Springer, 2020. [3](#page-2-1)
- <span id="page-8-23"></span>[6] David Bau, Hendrik Strobelt, William Peebles, Jonas Wulff, Bolei Zhou, Jun-Yan Zhu, and Antonio Torralba. Semantic photo manipulation with a generative image prior. *ACM TOG*, 2019. [3](#page-2-1)
- <span id="page-8-7"></span>[7] Cenk Baykal, Murad Tukan, Dan Feldman, and Daniela Rus. Small coresets to represent large training data for support vector machines. *OpenReview*, 2018. [2](#page-1-1)
- <span id="page-8-27"></span>[8] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *NeurIPS Workshop*, 2020. [3,](#page-2-1) [4](#page-3-1)
- <span id="page-8-8"></span>[9] Zalán Borsos, Mojmir Mutny, and Andreas Krause. Coresets via bilevel optimization for continual learning and streaming. *NeurIPS*, 2020. [2](#page-1-1)
- <span id="page-8-15"></span>[10] Andrew Brock, Theodore Lim, James M Ritchie, and Nick Weston. Neural photo editing with introspective adversarial networks. In *ICLR*, 2017. [3](#page-2-1)
- <span id="page-8-0"></span>[11] Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. *NeurIPS*, 2020. [1](#page-0-0)
- <span id="page-8-6"></span>[12] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [5,](#page-4-4) [6,](#page-5-2) [12,](#page-11-0) [13](#page-12-0)
- <span id="page-8-4"></span>[13] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Wearable imagenet: Synthesizing tileable textures via dataset distillation. In *CVPR Workshops*, 2022. [1,](#page-0-0) [3](#page-2-1)
- <span id="page-8-16"></span>[14] Lucy Chai, Jonas Wulff, and Phillip Isola. Using latent space regression to analyze and leverage compositionality in gans. In *ICLR*, 2021. [3](#page-2-1)
- <span id="page-8-17"></span>[15] Lucy Chai, Jun-Yan Zhu, Eli Shechtman, Phillip Isola, and Richard Zhang. Ensembling with deep generative views. In *CVPR*, 2021. [3](#page-2-1)
- <span id="page-8-31"></span>[16] Tianqi Chen, Bing Xu, Chiyuan Zhang, and Carlos Guestrin. Training deep nets with sublinear memory cost. *arXiv preprint arXiv:1604.06174*, 2016. [5](#page-4-4)
- <span id="page-8-37"></span>[17] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dcbench: Dataset condensation benchmark. In *NeurIPS Datasets and Benchmarks Track*, 2022. [7](#page-6-1)
- <span id="page-8-28"></span>[18] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. *arXiv preprint arXiv:2211.10586*, 2022. [4,](#page-3-1) [7,](#page-6-1) [12](#page-11-0)

- <span id="page-8-32"></span>[19] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009. [6,](#page-5-2) [12](#page-11-0)
- <span id="page-8-14"></span>[20] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *NeurIPS*, 2022. [3](#page-2-1)
- <span id="page-8-1"></span>[21] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding. *NAACL*, 2019. [1](#page-0-0)
- <span id="page-8-5"></span>[22] Tian Dong, Bo Zhao, and Lingjuan Liu. Privacy for free: How does dataset condensation help privacy? In *ICML*, 2022. [1](#page-0-0)
- <span id="page-8-36"></span>[23] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. In *ICLR*, 2020. [7](#page-6-1)
- <span id="page-8-33"></span>[24] Fastai. Fastai/imagenette: A smaller subset of 10 easily classified classes from imagenet, and a little more french. [6](#page-5-2)
- <span id="page-8-3"></span>[25] Jonathan Frankle and Michael Carbin. The lottery ticket hypothesis: Finding sparse, trainable neural networks. *ICLR*, 2019. [1](#page-0-0)
- <span id="page-8-34"></span>[26] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *CVPR*, 2018. [7](#page-6-1)
- <span id="page-8-10"></span>[27] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. *NeurIPS*, 2014. [2](#page-1-1)
- <span id="page-8-9"></span>[28] Sariel Har-Peled and Akash Kushal. Smaller coresets for k-median and k-means clustering. *Discrete & Computational Geometry*, 2007. [2](#page-1-1)
- <span id="page-8-20"></span>[29] Erik Härkönen, Aaron Hertzmann, Jaakko Lehtinen, and Sylvain Paris. Ganspace: Discovering interpretable gan controls. In *NeurIPS*, 2020. [3](#page-2-1)
- <span id="page-8-35"></span>[30] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, 2016. [7](#page-6-1)
- <span id="page-8-2"></span>[31] Geoffrey Hinton, Oriol Vinyals, and Jeffrey Dean. Distilling the knowledge in a neural network. In *NeurIPS Workshop*, 2015. [1](#page-0-0)
- <span id="page-8-12"></span>[32] Jonathan Ho and Stefano Ermon. Generative adversarial imitation learning. In *NeurIPS*, 2016. [3](#page-2-1)
- <span id="page-8-13"></span>[33] Chengeng Huang and Sihai Zhang. Generative dataset distillation. In *2021 7th International Conference on Big Data Computing and Communications (BigCom)*. IEEE, 2021. [3](#page-2-1)
- <span id="page-8-18"></span>[34] Minyoung Huh, Richard Zhang, Jun-Yan Zhu, Sylvain Paris, and Aaron Hertzmann. Transforming and projecting images into class-conditional generative networks. In *ECCV*, 2020. [3](#page-2-1)
- <span id="page-8-24"></span>[35] Tero Karras, Miika Aittala, Janne Hellsten, Samuli Laine, Jaakko Lehtinen, and Timo Aila. Training generative adversarial networks with limited data. In *NeurIPS*, 2020. [3,](#page-2-1) [4](#page-3-1)
- <span id="page-8-29"></span>[36] Tero Karras, Miika Aittala, Samuli Laine, Erik Härkönen, Janne Hellsten, Jaakko Lehtinen, and Timo Aila. Alias-free generative adversarial networks. In *NeurIPS*, 2021. [4,](#page-3-1) [12](#page-11-0)
- <span id="page-8-25"></span>[37] Tero Karras, Samuli Laine, and Timo Aila. A style-based generator architecture for generative adversarial networks. In *CVPR*, 2019. [3,](#page-2-1) [8](#page-7-1)
- <span id="page-8-26"></span>[38] Tero Karras, Samuli Laine, Miika Aittala, Janne Hellsten, Jaakko Lehtinen, and Timo Aila. Analyzing and improving the image quality of stylegan. In *CVPR*, 2020. [3](#page-2-1)

- <span id="page-9-14"></span>[39] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. *ICML*, 2022. [3](#page-2-1)
- <span id="page-9-30"></span>[40] Alex Krizhevsky and Geoffrey Hinton. Learning multiple layers of features from tiny images. Technical report, Citeseer, 2009. [6](#page-5-2)
- <span id="page-9-32"></span>[41] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. In *NeurIPS*, 2012. [7](#page-6-1)
- <span id="page-9-13"></span>[42] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *NeurIPS*, 2022. [3](#page-2-1)
- <span id="page-9-2"></span>[43] Wojciech Masarczyk and Ivona Tautkute. Reducing catastrophic forgetting with learning on synthetic data. In *CVPR Workshops*, 2020. [1](#page-0-0)
- <span id="page-9-27"></span>[44] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *ICLR*, 2020. [3,](#page-2-1) [4,](#page-3-1) [7](#page-6-1)
- <span id="page-9-28"></span>[45] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021. [3,](#page-2-1) [4,](#page-3-1) [7](#page-6-1)
- <span id="page-9-22"></span>[46] Xingang Pan, Xiaohang Zhan, Bo Dai, Dahua Lin, Chen Change Loy, and Ping Luo. Exploiting deep generative prior for versatile image restoration and manipulation. *PAMI*, 2021. [3](#page-2-1)
- <span id="page-9-10"></span>[47] Gaurav Parmar, Yijun Li, Jingwan Lu, Richard Zhang, Jun-Yan Zhu, and Krishna Kumar Singh. Spatially-adaptive multilayer selection for gan inversion and editing. In *CVPR*, 2022. [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [5](#page-4-4)
- <span id="page-9-36"></span>[48] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, Alban Desmaison, Andreas Kopf, Edward Yang, Zachary DeVito, Martin Raison, Alykhan Tejani, Sasank Chilamkurthy, Benoit Steiner, Lu Fang, Junjie Bai, and Soumith Chintala. Pytorch: An imperative style, high-performance deep learning library. In *NeurIPS*. 2019. [12](#page-11-0)
- <span id="page-9-34"></span><span id="page-9-0"></span>[49] Pokémon. <pokemon.com>. [8](#page-7-1)
- [50] Aditya Ramesh, Prafulla Dhariwal, Alex Nichol, Casey Chu, and Mark Chen. Hierarchical text-conditional image generation with clip latents. *arXiv preprint arXiv:2204.06125*, 2022. [1](#page-0-0)
- <span id="page-9-23"></span>[51] Elad Richardson, Yuval Alaluf, Or Patashnik, Yotam Nitzan, Yaniv Azar, Stav Shapiro, and Daniel Cohen-Or. Encoding in style: a stylegan encoder for image-to-image translation. In *CVPR*, 2021. [3](#page-2-1)
- <span id="page-9-24"></span>[52] Daniel Roich, Ron Mokady, Amit H Bermano, and Daniel Cohen-Or. Pivotal tuning for latent-based editing of real images. *ACM TOG*, 2022. [3](#page-2-1)
- <span id="page-9-26"></span>[53] Axel Sauer, Katja Schwarz, and Andreas Geiger. Stylegan-xl: Scaling stylegan to large diverse datasets. In *SIGGRAPH*, 2022. [3,](#page-2-1) [4,](#page-3-1) [6,](#page-5-2) [7](#page-6-1)
- <span id="page-9-33"></span>[54] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. In *ICLR*, 2015. [7](#page-6-1)
- <span id="page-9-3"></span>[55] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate

synthetic training data. In *ICML*, 2020. [1](#page-0-0)

- <span id="page-9-12"></span>[56] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *IJCNN*, 2021. [3](#page-2-1)
- <span id="page-9-17"></span>[57] Ayush Tewari, Mohamed Elgharib, Florian Bernard, Hans-Peter Seidel, Patrick Pérez, Michael Zollhöfer, and Christian Theobalt. Pie: Portrait image embedding for semantic control. *ACM TOG*, 2020. [3](#page-2-1)
- <span id="page-9-25"></span>[58] Omer Tov, Yuval Alaluf, Yotam Nitzan, Or Patashnik, and Daniel Cohen-Or. Designing an encoder for stylegan image manipulation. In *SIGGRAPH*, 2021. [3](#page-2-1)
- <span id="page-9-9"></span>[59] Ivor W Tsang, James T Kwok, and Pak-Ming Cheung. Core vector machines: Fast svm training on very large data sets. *JMLR*, 2005. [2](#page-1-1)
- <span id="page-9-31"></span>[60] Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*, 2016. [7](#page-6-1)
- <span id="page-9-35"></span>[61] Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Deep image prior. In *CVPR*, 2018. [8](#page-7-1)
- <span id="page-9-4"></span>[62] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. *CVPR*, 2022. [1,](#page-0-0) [3,](#page-2-1) [4,](#page-3-1) [7](#page-6-1)
- <span id="page-9-18"></span>[63] Tengfei Wang, Yong Zhang, Yanbo Fan, Jue Wang, and Qifeng Chen. High-fidelity gan inversion for image attribute editing. In *CVPR*, 2022. [3](#page-2-1)
- <span id="page-9-1"></span>[64] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [7](#page-6-1)
- <span id="page-9-19"></span>[65] Tianyi Wei, Dongdong Chen, Wenbo Zhou, Jing Liao, Weiming Zhang, Lu Yuan, Gang Hua, and Nenghai Yu. A simple baseline for stylegan inversion. *arXiv preprint arXiv:2104.07661*, 2021. [3](#page-2-1)
- <span id="page-9-21"></span>[66] Zongze Wu, Dani Lischinski, and Eli Shechtman. Stylespace analysis: Disentangled controls for stylegan image generation. In *CVPR*, 2021. [3](#page-2-1)
- <span id="page-9-5"></span>[67] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [7,](#page-6-1) [14](#page-13-0)
- <span id="page-9-15"></span>[68] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *NeurIPS Workshop*, 2022. [3](#page-2-1)
- <span id="page-9-6"></span>[69] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *WACV*, 2023. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-1) [5,](#page-4-4) [6,](#page-5-2) [7,](#page-6-1) [12,](#page-11-0) [13](#page-12-0)
- <span id="page-9-7"></span>[70] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2020. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [5,](#page-4-4) [6,](#page-5-2) [7,](#page-6-1) [12,](#page-11-0) [13](#page-12-0)
- <span id="page-9-29"></span>[71] Shengyu Zhao, Zhijian Liu, Ji Lin, Jun-Yan Zhu, and Song Han. Differentiable augmentation for data-efficient gan training. In *NeurIPS*, 2020. [4](#page-3-1)
- <span id="page-9-8"></span>[72] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *NeurIPS*, 2022. [1,](#page-0-0) [7](#page-6-1)
- <span id="page-9-20"></span>[73] Jiapeng Zhu, Yujun Shen, Deli Zhao, and Bolei Zhou. Indomain gan inversion for real image editing. In *ECCV*. Springer, 2020. [3](#page-2-1)
- <span id="page-9-16"></span>[74] Jun-Yan Zhu, Philipp Krähenbühl, Eli Shechtman, and Alexei A Efros. Generative visual manipulation on the natural image manifold. In *ECCV*, 2016. [3](#page-2-1)
- <span id="page-9-11"></span>[75] Peihao Zhu, Rameen Abdal, John Femiani, and Peter Wonka. Barbershop: Gan-based image compositing using segmentation masks. *ACM TOG*, 2021. [2,](#page-1-1) [3,](#page-2-1) [5](#page-4-4)

<span id="page-10-0"></span>[76] Peihao Zhu, Rameen Abdal, Yipeng Qin, John Femiani, and Peter Wonka. Improved stylegan embedding: Where are the good latents? *arXiv preprint arXiv:2012.09036*, 2020. [3](#page-2-1)

<span id="page-11-1"></span><span id="page-11-0"></span>

| Dataset         | 0                     | 1                   | 2                  | 3                    | 4               | 5                        | 6                      | 7                     | 8                 | 9                     |
|-----------------|-----------------------|---------------------|--------------------|----------------------|-----------------|--------------------------|------------------------|-----------------------|-------------------|-----------------------|
| ImageNet-A      | Leonberg              | Probiscis<br>Monkey | Rapeseed           | Three-Toed<br>Sloth  | Cliff Dwelling  | Yellow<br>Lady's Slipper | Hamster                | Gondola               | Orca              | Limpkin               |
| ImageNet-B      | Spoonbill             | Website             | Lorikeet           | Hyena                | Earthstar       | Trollybus                | Echidna                | Pomeranian            | Odometer          | Ruddy<br>Turnstone    |
| ImageNet-C      | Freight Car           | Hummingbird         | Fireboat           | Disk Brake           | Bee Eater       | Rock Beauty              | Lion                   | European<br>Gallinule | Cabbage Butterfly | Goldfinch             |
| ImageNet-D      | Ostrich               | Samoyed             | Snowbird           | Brabancon<br>Griffon | Chickadee       | Sorrel                   | Admiral                | Great<br>Gray Owl     | Hornbill          | Ringlet               |
| ImageNet-E      | Spindle               | Toucan              | Black Swan         | King<br>Penguin      | Potter's Wheel  | Photocopier              | Screw                  | Tarantula             | Sscilloscope      | Lycaenid              |
| ImageNette      | Tench                 | English<br>Springer | Cassette<br>Player | Chainsaw             | Church          | French Horn              | Garbage<br>Truck       | Gas Pump              | Golf Ball         | Parachute             |
| ImageWoof       | Australian<br>Terrier | Border Terrier      | Samoyed            | Beagle               | Shih-Tzu        | English<br>Foxhound      | Rhodesian<br>Ridgeback | Dingo                 | Golden Retriever  | English<br>Sheepdog   |
| ImageNet-Birds  | Peacock               | Flamingo            | Macaw              | Pelican              | King<br>Penguin | Bald Eagle               | Toucan                 | Ostrich               | Black Swan        | Cockatoo              |
| ImageNet-Fruits | Pineapple             | Banana              | Strawberry         | Orange               | Lemon           | Pomegranate              | Fig                    | Bell Pepper           | Cucumber          | Granny Smith<br>Apple |
| ImageNet-Cats   | Tabby<br>Cat          | Bengal<br>Cat       | Persian<br>Cat     | Siamese Cat          | Egyptian<br>Cat | Lion                     | Tiger                  | Jaguar                | Snow<br>Leopard   | Lynx                  |

Table 5. Class listings for our ImageNet subsets. Visualizations show classes in the same order given here.

### A. More Visualizations

Please see our web page for more visualizations: [georgecazenavette.github.io/glad.](https://georgecazenavette.github.io/glad/)

### B. StyleGAN Latent Spaces

Here we further elaborate on our embedding spaces for those unfamiliar with the StyleGAN architecture. Some details will be left out for easier digestion. Visualizations of these embedding spaces can be seen in Figure [10.](#page-12-1)

For standard image generation with StyleGAN 3 [\[36\]](#page-8-29) (and other StyleGAN models), a random vector is first sampled from the multi-variate standard normal distribution: z ∼  $\mathcal{N}(0, I)$ . This random Gaussian vector is then fed through a "mapping" network (typically a simple MLP) to obtain a "style code" W. In a class-conditional StyleGAN, this "mapping" network is the *only* place where the class information is used. This W is then passed to every "style block" of the "synthesis" network as used to modulate the convolutional layers of each block. The "synthesis" network takes in a learned constant as input and uses the modulated convolutions of the style blocks to generate the realistic image.

For each distilled sample in W<sup>+</sup> space, we optimize a *different* W code for each style block and use the synthesis network to generate our synthetic data. The "mapping" network is not used aside from initializing W. In  $Fn$  space, we *directly* optimize the feed-forward input to the  $n<sup>th</sup>$  style block as well as the W codes for all subsequent style blocks. Any preceding style blocks and W codes are simply ignored.

### C. Dataset Specifications

Our high-resolution data is taken directly from the ImageNet 1k dataset [\[19\]](#page-8-32) using PyTorch's built-in ImageNet loader [\[48\]](#page-9-36). To train our expert trajectories, we use data from the ImageNet training set. To compile *our* training set for the expert trajectories, we select the classes from the given subset, resize the short side of the image to the given resolution, and take a center crop according to the given resolution (as done by MTT  $[12]$ ). The validation set is obtained in the same way as the ImageNet validation set.

For an enumeration of which ImageNet classes are in each of our datasets, please see Table [5.](#page-11-1)

### D. More Experimental Results

In Table  $6$ , we show the performance of the distilled images on the backbone architecture (the architecture used for distillation). We note that we do not expect GLaD to perform better than the baseline pixel-based distillation since GLaD is designed to *reduce* overfitting to the distilling architecture. Despite this, DC and DM with GLaD perform as well or better than the pixel-based versions (with MTT performing somewhat worse).

In Table [7,](#page-12-3) we show results using 10 distilled images per class. GLaD still tends to perform better than pixel space distillation.

In Table  $8$ , we show the baseline results of training each architecture on the whole dataset. We did not spend time tuning the hyper-parameters here, perhaps explaining why ConvNet tends to have the best performance.

## E. Hyper-Parameters and Experimental Details

For the experiments on MTT and our new method, we base our experiments on the open-source code for DC+DM [\(link\)](https://github.com/VICO-UoE/DatasetCondensation) [\[69,](#page-9-6) [70\]](#page-9-7), MTT [\(link\)](https://github.com/GeorgeCazenavette/mtt-distillation) [\[12\]](#page-8-6), and TESLA [\(link\)](https://openreview.net/forum?id=dN70O8pmW8) [\[18\]](#page-8-28).

To optimize the distilled images/latents and learnable synthetic step size  $(\alpha)$ , we use the same optimizer and hyperparameters as the original methods. For the W<sup>+</sup> latents, divide the learning rate by 10.

<span id="page-12-1"></span><span id="page-12-0"></span>Image /page/12/Figure/0 description: This figure illustrates three different latent spaces of StyleGAN, labeled W+, F1, and F3. Each space shows a series of 'Style Block' components connected by latent variables (W0 to WN) and feature vectors (F1 to FN). The W+ space, enclosed by a dashed red rectangle, shows all latent variables W0 through WN directly connected to their respective style blocks. The F1 space, also outlined in a dashed red rectangle, highlights the direct optimization of latent variables W1 through WN, with W0 and F1 being part of a separate, gray-highlighted block. The F3 space, enclosed by a dashed red rectangle, shows direct optimization of latent variables W3 through WN, with W0, W1, W2, F1, and F2 being part of a larger gray-highlighted block. The text below the diagrams reads 'optimization spaces of StyleGAN. Latent variables boxed in red are directly optimized while'.

Figure 10. Different optimization spaces of StyleGAN. Latent variables boxed in red are directly optimized while those that are grayed out are not used at all. Note: the "mapping" network is omitted here since we do not use it in any of our optimization spaces.

<span id="page-12-2"></span>

| Distil. Alg. | Distil. Space | ImNet-A        | ImNet-B        | ImNet-C        | ImNet-D        | ImNet-E        | ImNette        | ImWoof         | ImNet-Birds    | ImNet-Fruits   | ImNet-Cats     |
|--------------|---------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
| MTT [12]     | Pixel         | $51.7 	pm 0.2$ | $53.3 	pm 1.0$ | $48.0 	pm 0.7$ | $43.0 	pm 0.6$ | $39.5 	pm 0.9$ | $41.8 	pm 0.6$ | $22.6 	pm 0.6$ | $37.3 	pm 0.8$ | $22.4 	pm 1.1$ | $26.6 	pm 0.4$ |
|              | GLaD (Ours)   | $50.7 	pm 0.4$ | $51.9 	pm 1.3$ | $44.9 	pm 0.4$ | $39.9 	pm 1.7$ | $37.6 	pm 0.7$ | $38.7 	pm 1.6$ | $23.4 	pm 1.1$ | $35.8 	pm 1.4$ | $23.1 	pm 0.4$ | $26.0 	pm 1.1$ |
| DC [70]      | Pixel         | $43.2 	pm 0.6$ | $47.2 	pm 0.7$ | $41.3 	pm 0.7$ | $34.3 	pm 1.5$ | $34.9 	pm 1.5$ | $34.2 	pm 1.7$ | $22.5 	pm 1.0$ | $32.0 	pm 1.5$ | $21.0 	pm 0.9$ | $22.0 	pm 0.6$ |
|              | GLaD (Ours)   | $44.1 	pm 2.4$ | $49.2 	pm 1.1$ | $42.0 	pm 0.6$ | $35.6 	pm 0.9$ | $35.8 	pm 0.9$ | $35.4 	pm 1.2$ | $22.3 	pm 1.1$ | $33.8 	pm 0.9$ | $20.7 	pm 1.1$ | $22.6 	pm 0.8$ |
| DM [69]      | Pixel         | $39.4 	pm 1.8$ | $40.9 	pm 1.7$ | $39.0 	pm 1.3$ | $30.8 	pm 0.9$ | $27.0 	pm 0.8$ | $30.4 	pm 2.7$ | $20.7 	pm 1.0$ | $26.6 	pm 2.6$ | $20.4 	pm 1.9$ | $20.1 	pm 1.2$ |
|              | GLaD (Ours)   | $41.0 	pm 1.5$ | $42.9 	pm 1.9$ | $39.4 	pm 0.7$ | $33.2 	pm 1.4$ | $30.3 	pm 1.3$ | $32.2 	pm 1.7$ | $21.2 	pm 1.5$ | $27.6 	pm 1.9$ | $21.8 	pm 1.8$ | $22.3 	pm 1.6$ |

Table 6. Performance on ConvNet (architecture used to distill).

<span id="page-12-3"></span>

|    | Distil. Alg. Distil. Space ImNet-A ImNet-B ImNet-C ImNet-D ImNet-E                              |                                                                            |  |              |
|----|-------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------|--|--------------|
| DC | Pixel<br>GLaD (Ours) $53.1 \pm 1.4$ $50.1 \pm 0.6$ $48.9 \pm 1.1$ $38.9 \pm 1.0$ $38.4 \pm 0.7$ | $52.3 \pm 0.7$ $45.1 \pm 8.3$ $40.1 \pm 7.6$ $36.1 \pm 0.4$ $38.1 \pm 0.4$ |  |              |
| DM | Pixel<br>GLaD (Ours) $52.8 \pm 1.0$ $51.3 \pm 0.6$ $49.7 \pm 0.4$ $36.4 \pm 0.4$ $38.6 \pm 0.7$ | $52.6 \pm 0.4$ $50.6 \pm 0.5$ $47.5 \pm 0.7$ $35.4 \pm 0.4$                |  | $36.0 + 0.5$ |

Table 7. Performance with 10 images/class.

<span id="page-12-4"></span>

| Arch.    | ImNet-A      | ImNet-B      | ImNet-C      | ImNet-D      | ImNet-E      |
|----------|--------------|--------------|--------------|--------------|--------------|
| ConvNet  | 90.6 $±$ 0.6 | 92.3 $±$ 0.2 | 84.2 $±$ 0.3 | 74.5 $±$ 1.0 | 76.2 $±$ 0.6 |
| ResNet18 | 78.8 $±$ 1.6 | 80.2 $±$ 1.1 | 69.2 $±$ 1.6 | 51.0 $±$ 0.7 | 53.2 $±$ 2.8 |
| VGG11    | 78.4 $±$ 1.1 | 81.4 $±$ 1.5 | 74.6 $±$ 1.2 | 67.3 $±$ 1.6 | 67.8 $±$ 1.3 |
| AlexNet  | 81.0 $±$ 0.3 | 76.5 $±$ 1.4 | 72.2 $±$ 1.1 | 65.4 $±$ 1.1 | 63.5 $±$ 1.1 |
| ViT      | 77.5 $±$ 0.4 | 76.4 $±$ 0.4 | 75.5 $±$ 1.4 | 58.6 $±$ 0.9 | 59.5 $±$ 1.2 |

Table 8. Training networks from scratch on the *whole* dataset.

For our MTT experiments, we set the number of synthetic steps per iteration  $(N)$  as 10, the number of real epochs to match  $(M)$  as 2, and the maximum starting epoch  $(T^+)$  set

to 2. All experiments on MTT and our new method are run for 5k iterations and then evaluated via the protocol described <span id="page-13-0"></span>in the body of the paper.

All 32  $\times$  32, 128  $\times$  128, 256  $\times$  256, and 512  $\times$  512 experiments are distilled using ConvNetD3, ConvNetD5, ConvNetD6, and ConvNetD7 respectively as the backbone.

The same suite of differentiable augmentations (originally from the DSA codebase [\[67\]](#page-9-5)) is used for all experiments: color, crop, cutout, flip, scale, and rotate with the default parameters.

To obtain the expert trajectories used by MTT, we train a model from scratch on the real dataset for 15 epochs of SGD with a learning rate of  $10^{-2}$ , a batch size of 256, and NO momentum or regularization.

Our experiments were run on a combination of RTX2080ti, RTX3090, RTX6000, RTXA5000, and RTXA6000 GPUs depending on availability.
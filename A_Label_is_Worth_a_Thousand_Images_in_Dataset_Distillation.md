# A Label is Worth a Thousand Images in Dataset Distillation

Tian Qin Harvard University Cambridge, MA <EMAIL>

Zhiwei Deng Google DeepMind Mountain View, CA <EMAIL>

David <PERSON>-<PERSON> Harvard University & MSR Cambridge, MA <EMAIL>

### Abstract

Data *quality* is a crucial factor in the performance of machine learning models, a principle that dataset distillation methods exploit by compressing training datasets into much smaller counterparts that maintain similar downstream performance. Understanding how and why data distillation methods work is vital not only for improving these methods but also for revealing fundamental characteristics of "good" training data. However, a major challenge in achieving this goal is the observation that distillation approaches, which rely on sophisticated but mostly disparate methods to generate synthetic data, have little in common with each other. In this work, we highlight a largely overlooked aspect common to most of these methods: the use of soft (probabilistic) labels. Through a series of ablation experiments, we study the role of soft labels in depth. Our results reveal that the main factor explaining the performance of state-of-the-art distillation methods is not the specific techniques used to generate synthetic data but rather the use of soft labels. Furthermore, we demonstrate that not all soft labels are created equal; they must contain *structured information* to be beneficial. We also provide empirical scaling laws that characterize the effectiveness of soft labels as a function of images-per-class in the distilled dataset and establish an empirical Pareto frontier for data-efficient learning. Combined, our findings challenge conventional wisdom in dataset distillation, underscore the importance of soft labels in learning, and suggest new directions for improving distillation methods. Code for all experiments is available at <https://github.com/sunnytqin/no-distillation>.

### 1 Introduction

Data is central to the success of modern machine learning models, and there is increasing evidence that data *quality* trumps quantity in many settings. For example, in the context of large language models (LLM), Abdin et al. [\[1\]](#page-10-0), Gunasekar et al. [\[11\]](#page-10-1), and Hughes [\[14\]](#page-10-2) show that training LLMs in a "data-optimal" regime allows for significant reductions in model size without sacrificing performance and capabilities. In other words, when trained on high-quality data, small models can rival the performance of their much larger counterparts. Despite its obvious importance, there is no clear answer to what characteristics define "good data." Data distillation offers one approach to answering this question. First introduced by Wang et al. [\[29\]](#page-11-0), the goal of dataset distillation is to condense a dataset into a smaller (synthetic) counterpart, such that training on this distilled dataset achieves performance comparable to training on the original dataset. By studying the distillation process, we can thus investigate what information is preserved in the data (features, symmetries, and information), which in turn may shed light on fundamental characteristics that make for "good" training data.

The success of dataset distillation raises two key questions. First, what aspects of the training data best facilitate "data-efficient" learning? Second, what distillation procedure better captures the aspects of the dataset relevant for prediction? So far, dataset distillation work has focused almost exclusively

38th Conference on Neural Information Processing Systems (NeurIPS 2024).

Image /page/1/Figure/0 description: The image displays a figure with two parts: a grid of synthetic images on the left and a line graph on the right. The grid shows synthetic images generated by different dataset distillation methods (Ra-BPTT, SRe2L, MTT) for various classes (German shepherd, Egyptian cat, Barn, Dam, Teddy bear, Banana, Cliff). The line graph, titled "Dataset Distillation for ImageNet-1K", plots "Student Accuracy (%)" on the y-axis against "Image/Class (IPC)" on the x-axis. The graph includes several lines representing different methods: SRe2L (soft label w/ epoch tuning), Random \$\in D\_{train}\$ (hard label), FRePo (hard label), Ra-BPTT (soft label), and MTT (TESLA) (soft label). The x-axis ranges from 1 to 200, and the y-axis ranges from 0 to 60. The caption below the figure reads: "Figure 1: Soft labels are crucial for dataset distillation Left: Synthetic images by different distillation methods. Right: Student test accuracy comparison between different distillation methods."

<span id="page-1-0"></span>Figure 1: Soft labels are crucial for dataset distillation *Left:* Synthetic images by different distillation methods. *Right:* Student test Accuracy comparison between different distillation methods and random baseline from training data (both with hard labels or with soft labels). For soft/hard label generation details, see Appendix [A.2.](#page-12-0)

on the second question, with a strong emphasis on improving ways to generate synthetic data for each class [\[23\]](#page-11-1). Figure [1](#page-1-0) (left) shows examples of the synthetic images generated by various state-of-the-art (SOTA) data distillation methods. Our first key observation is that, despite very different generation strategies resulting in perceptually distinct synthetic images, virtually all leading methods, especially those able to scale to large datasets such as ImageNet-1K (or its downsized version) ([\[6,](#page-10-3) [9,](#page-10-4) [33,](#page-11-2) [39\]](#page-11-3)), use soft (i.e., probabilistic) labels for the datasets they distill. Furthermore, Cui et al. [\[6\]](#page-10-3) and Yin et al. [\[33\]](#page-11-2) directly use soft labels generated by pretrained experts.

Based on this observation, we take a step back and revisit the first key motivating question above. Specifically, we ask: what is the relative importance of features (e.g., images) and labels for distillation—and therefore, data-efficient training? To answer this question, we design a series of ablation experiments aimed at studying the role of labels in dataset distillation, including a simple but effective baseline that pairs randomly sampled training images (i.e., not synthetic, learned ones) and with soft labels. Surprisingly, we observe that (i) the main factor explaining the success of these methods is the use of soft labels, and (ii) the simple soft label baseline achieves performance comparable to SOTA dataset distillation methods (Figure [1,](#page-1-0) right).<sup>[1](#page-1-1)</sup> This result alone has important implications for practitioners and researchers alike. For practitioners, it calls into question spending compute or research efforts on generating synthetic images, given their relatively limited contribution to overall distillation performance. For researchers, it suggests revisiting common assumptions about existing data distillation approaches and considering alternatives.

After demonstrating the importance of soft labels, and given the limited analysis of their role in data distillation, we design further experiments to study in detail what makes them so effective for learning. We focus on the setting where soft labels are generated by an "expert" model, e.g., by labeling an image with the class probabilities predicted by the model, as most SOTA distillation methods do. We find that when learning in a data-limited setting, a student model trained on soft-labeled data achieves its best performance by learning from an early-stopped expert, and its success relies on leveraging semantic structure contained in these soft labels. We also present an empirical knowledge-data scaling law along with a Pareto frontier that showcase how (optimal) knowledge can be used to reduce dataset size. Next, we argue that the soft label baseline is a special case of Knowledge Distillation (KD) [\[13\]](#page-10-5), where the role of soft labels has been better appreciated. Our final set of experiments seek to turn these findings into better methods for generating soft labels. To this end, we first show that expert ensembles can improve learning value of soft labels. We also show that directly optimizing labels with data distillation methods (without training any experts) recovers the *same* information in labels, indicating that expert knowledge might be the *necessary* way for dataset distillation.

Taken together, the results of our in-depth analysis highlight the importance of labels in dataset distillation, challenging conventional wisdom. Our contributions can be summarized as follows:

<span id="page-1-1"></span> ${}^{1}$ Ra-BPTT [\[9\]](#page-10-4) and FRePo[\[39\]](#page-11-3) only scale to IPC=1, 2 on downsized ImageNet-1K. TESLA[\[6\]](#page-10-3) is an MTT[\[4\]](#page-10-6) variant with memory-efficient implementation and uses soft labels.

- We conduct a series of ablations, including a simple-but-powerful baseline, to show that the success of existing data distillation methods is driven not by synthetic data generation strategies but by the use of informative labels.
- We show that *structured semantic information* (e.g., distributional similarity across related classes) is key to good soft labels and that different distillation budgets imply different optimal soft label structure profiles. Furthermore, we show how to effectively modulate these profiles by using expert labelers trained with early stopping, a strategy that turns out to recover soft labels obtained with existing data distillation methods as a particular case.
- We establish an empirical data-knowledge scaling law to quantify how knowledge can be used to reduce dataset size, and we also establish a Pareto frontier for data-efficient learning.

<span id="page-2-0"></span>

## 2 Related Work

Dataset distillation. Data distillation methods have been primarily developed for image classification tasks. Existing methods can be organized into three main categories: (1) meta-model matching, (2) distribution matching, and (3) trajectory matching [\[23\]](#page-11-1). Meta-model matching methods approach the problem by solving the original bi-level optimization formulation of Wang et al. [\[29\]](#page-11-0). To tackle the computation challenge, various methods have been proposed to either approximate the objective [\[20\]](#page-10-7) or improve the optimization process [\[9,](#page-10-4) [15,](#page-10-8) [39\]](#page-11-3). Distribution Matching (DM) seeks to minimize the statistical distance in the output (logit) space between original and distilled dataset samples [\[36\]](#page-11-4). Further refinements to the method include [\[16,](#page-10-9) [28,](#page-11-5) [35,](#page-11-6) [37,](#page-11-7) [39\]](#page-11-3). Yin et al. [\[33\]](#page-11-2) propose to match network statistics (batchnorm) along with logits and then assigning soft labels to synthetic images. Follow-up works [\[12,](#page-10-10) [32\]](#page-11-8) brings further improvements to the soft label assignment strategy. Matching training trajectories (MTT) was proposed by Cazenavette et al. [\[5\]](#page-10-11), suggesting that matching long-range training dynamics can serve as a proxy objective to the expensive bi-level formulation. Further improvements and variations include [\[6,](#page-10-3) [8\]](#page-10-12). In this work, we aim to uncover common factors responsible for the success of these methods (i.e., the use of soft labels). We also draw connection between data distillation and knowledge distillation. Concurrent work [\[30\]](#page-11-9) also discovers that models learned from distilled data behaves similar to an early stopped expert.

Distillation with soft labels. Almost all existing distillation methods able to scale to ImageNet-1K (downsized or original) leverage soft labels. Cui et al. [\[6\]](#page-10-3) and Yin et al. [\[33\]](#page-11-2) decouple image learning from label assignment and directly use pre-trained experts to assign soft labels. Feng et al. [\[9\]](#page-10-4) and Zhou et al. [\[39\]](#page-11-3) learn the distilled images and soft labels simultaneously. In the very early years of data distillation research, the importance of soft labels was highlighted by Bohdal et al. [\[3\]](#page-10-13) and Sucholutsky and Schonlau [\[26\]](#page-11-10), although this was done in a very limited experimental setting (MNIST), limiting its influence in further work that tackled larger and more complex classification tasks more representative of modern machine learning. Despite the increasing prevalence of soft labels in state-of-the-art dataset distillation methods, little work has been done in a controlled setting to understand how data and labels each contribute to the quality of the distilled data. This work extensively addresses this question.

Knowledge Distillation. The goal of knowledge distillation (KD) is to transfer knowledge learned by a large teacher model to a small student model [\[2,](#page-10-14) [10,](#page-10-15) [13\]](#page-10-5). While the KD objective may seem at odds with the dataset distillation objective, many dataset distillation methods [\[32,](#page-11-8) [33\]](#page-11-2) are directly inspired by data-free KD methods [\[25,](#page-11-11) [31\]](#page-11-12). In fact, the use of soft labels has been extensively studied in the context of KD [\[13,](#page-10-5) [19,](#page-10-16) [27\]](#page-11-13). Generally, soft labels function both as supervisory signals and regularizers. Yuan et al. [\[34\]](#page-11-14) and Zhou et al. [\[38\]](#page-11-15) further argue that under the KD setting the regularization effect from soft labels is equally —if not more— important than sharing knowledge. In this work we uncover deeper connections between the two fields. We show that one way to achieve dataset distillation is to incorporate expert knowledge into soft labels (i.e., KD). We further show that, unlike the conclusions drawn in KD, the knowledge-sharing aspect of soft labels is the dominant effect under the data distillation setting.

<span id="page-2-1"></span>

## 3 Soft Labels are Crucial for Distillation

#### 3.1 Background on dataset distillation and a simple soft label baseline

The goal of dataset distillation is to create a condensed version of a larger dataset that retains the essential information needed for training a model. Formally, we denote the original dataset as

| <b>Labeling Strategy</b> | <b>Hard label</b>                  | Soft label baseline              |                      |                                    |                      | <b>CutMiX</b> augmented soft labels |                    |  |
|--------------------------|------------------------------------|----------------------------------|----------------------|------------------------------------|----------------------|-------------------------------------|--------------------|--|
| Labeling Expert          | None                               | ResNet18                         |                      | ResNet <sub>50</sub>               |                      | ResNet18                            |                    |  |
| <b>Image Generation</b>  | Random $\in$ $\mathcal{D}_{train}$ | Random $\in \mathcal{D}_{train}$ |                      | Random $\in$ $\mathcal{D}_{train}$ |                      | Random $\in \mathcal{D}_{train}$    | SRe <sup>2</sup> L |  |
| Eval Model               | ResNet18                           | ResNet18                         | ResNet <sub>50</sub> | ResNet18                           | ResNet <sub>50</sub> | ResNet18                            | ResNet18           |  |
| $IPC=1$                  | 0.6(0.1)                           | 6.6(0.4)                         | 6.8(0.4)             | 6.7(0.4)                           | 7.3(0.2)             | 1.6(0.1)                            | 2.9(0.5)           |  |
| 10                       | 3.9(0.6)                           | 23.9(0.4)                        | 24.0(0.4)            | 22.9(0.6)                          | 23.9(0.5)            | 25.8(0.7)                           | 21.3(0.6)          |  |
| 50                       | 20.4(0.4)                          | 47.9(0.6)                        | 53.1(0.5)            | 43.0(0.6)                          | 53.2(0.3)            | 54.3(0.6)                           | 46.8 $(0.2)$       |  |
| 100                      | 28.2(0.3)                          | 53.4(0.5)                        | 57.6 $(0.4)$         | 53.5(0.5)                          | 58.3(0.5)            | 54.7(0.2)                           | 52.8(0.3)          |  |
| 200                      | 37.8(0.5)                          | 56.8 $(0.4)$                     | 62.3(0.6)            | 56.5(0.8)                          | 62.7(0.4)            | 57.7(0.6)                           | 57.0 $(0.4)$       |  |
| Full                     | $ResNet18 = 69.8\%$                |                                  |                      |                                    |                      | $ResNet50 = 76.1\%$                 |                    |  |

<span id="page-3-0"></span>Table 1: Benchmark SOTA methods against CutMix baseline and soft label baseline on **ImageNet-1K.** SRe<sup>2</sup>L is the only method that can scale to ImageNet-1K. Both soft label-based baselines with random training images can already achieve comparable performances.

 $\mathcal{D}_{target} = \{(x_i, y_i)\}\$ , where  $x_i$ 's are input images and  $y_i$ 's are labels. Similarly, we can denote the distilled dataset as  $\mathcal{D}_{syn} = \{(\tilde{x}_i, \tilde{y}_i)\}\$ , and to achieve dataset size reduction  $|\mathcal{D}_{syn}| \ll |\mathcal{D}_{target}|$ . Dataset distillation problem can be formulated as a bi-level optimization problem [\[29\]](#page-11-0):

<span id="page-3-1"></span>
$$
\underset{\mathcal{D}_{syn}}{\text{argmin}} \mathcal{L}(\theta^{\mathcal{D}_{syn}}; \mathcal{D}_{target}) \quad \text{s.t.} \quad \theta^{\mathcal{D}_{syn}} = \underset{\theta}{\text{argmin}} \ \mathcal{L}(\theta; \mathcal{D}_{syn}) \tag{1}
$$

By convention, the size of the distilled data set is often quantified in terms of images per class (IPC). The goal of this work is to study the role of  $\tilde{y}$  in a controlled setting while fixing  $\tilde{x}$ .

Our first set of experiments comparing the performance of popular distillation methods with hard vs. soft labels (Figure [1\)](#page-1-0) show that soft labels are crucial for the performance of those distillation methods. On the other hand, the use of pretrained experts during distillation is also common. These two observations suggest an ablation study to evaluate the importance of soft labels in the context of dataset distillation. To this end, we introduce a simple baseline that "distills" datasets by selecting *real* (i.e., not synthetic) samples and soft-labeling them. Specifically, we randomly sample images from the training data and use pretrained experts to generate labels for them. The only hyper-parameter is thus which expert to use. To generate diverse experts, we save checkpoints at each epoch and vary which checkpoint is used based on the data budget.

### <span id="page-3-2"></span>3.2 Benchmarking distillation methods against the soft label baseline

From the many dataset distillation methods that have been proposed, we select the top-performing one from each of the three categories summarized in Section [2](#page-2-0) as a representative: Ra-BPTT [\[9\]](#page-10-4) (bi-level optimization objective), MTT [\[4\]](#page-10-6) (trajectory matching objective), and SR2<sup>2</sup>L [\[33\]](#page-11-2) (distribution matching objective). Among these,  $SRe^{2}L[33]$  $SRe^{2}L[33]$  is the only one that generalizes to ImageNet-1K without the need to downsize it (see Appendix [A.3](#page-13-0) for downsized comparison).  $SRe<sup>2</sup>L$  first generates images with a distribution matching objective using a pretrained expert. The same expert is then used to generate soft labels for these synthetic images. Specifically,  $SRe<sup>2</sup>L$  proposes generating 300 soft labels per prototype, where each soft label corresponds to a unique augmentation to the image. We establish an additional baseline, which we denote as "CutMix augmented soft labels" since  $SRe<sup>2</sup>L$ directly derives the labelling method from from Shen and Xing [\[25\]](#page-11-11). In this additional baseline, we remove the synthetic image generation process and replace it with random images sampled from the training data. We then employ the same CutMix augmented label generation. Table [1](#page-3-0) shows that randomly sampled training images paired with soft labels yield performance comparable to distilled synthetic ones. Moreover, the storage cost of the 300 soft labels needed for  $SRe^{2}L$  is at least 300 $\times$ larger than that of the soft label baseline [\[22\]](#page-10-17). We also observe that when the labels are generated by a teacher model with a different architecture, the student still performs well. In other words, the soft label baseline meets the cross-architecture generalizability requirement for dataset distillation.

In addition to ImageNet, we benchmark the performance of these methods on smaller datasets, where they generally tend to perform better. We compare the soft label baseline against other methods on TinyImageNet, CIFAR-100, and CIFAR-10, as shown in Table [2.](#page-4-0) For these datasets, existing methods sometimes significantly outperform the baseline at very low data budgets (i.e., high compression rates). However, at higher data budgets, the soft label baseline again yields better or comparable performances. These results show that current methods still face issues scaling to larger data budgets.

<span id="page-4-0"></span>Table 2: Benchmark SOTA methods against soft label baseline ("SI baseline") on TinyImageNet, CIFAR-100 and CIFAR-10. For smaller datasets at small data-budget, SOTA distillation methods can outperform the baseline. Scaling those methods to large data-budget remains a challenge.

| Dataset | Distill Method | TinyImageNet         |                |                |                | CIFAR-100            |                |                |                | CIFAR-10             |                |                |                |
|---------|----------------|----------------------|----------------|----------------|----------------|----------------------|----------------|----------------|----------------|----------------------|----------------|----------------|----------------|
|         |                | Ra-BPTT              | MTT            | DM             | SI Baseline    | Ra-BPTT              | MTT            | DM             | SI Baseline    | Ra-BPTT              | MTT            | DM             | SI Baseline    |
| IPC=1   |                | $20.1$ $(0.3)$       | $8.8$ $(0.3)$  | $3.9$ $(0.2)$  | $7.6$ $(0.3)$  | $35.3$ $(0.4)$       | $24.3$ $(0.3)$ | $11.4$ $(0.3)$ | $16.0$ $(0.3)$ | $53.2$ $(0.7)$       | $46.3$ $(0.8)$ | $26.0$ $(0.8)$ | $21.1$ $(0.5)$ |
| 10      |                | $24.4$ $(0.2)$       | $23.2$ $(0.2)$ | $12.9$ $(0.4)$ | $27.2$ $(0.4)$ | $47.5$ $(0.2)$       | $40.1$ $(0.4)$ | $29.7$ $(0.3)$ | $34.3$ $(0.2)$ | $69.4$ $(0.4)$       | $65.3$ $(0.7)$ | $48.9$ $(0.6)$ | $46.3$ $(0.8)$ |
| 50      |                | -                    | $28.0$ $(0.3)$ | $24.1$ $(0.3)$ | $35.6$ $(0.4)$ | $50.6$ $(0.2)$       | $47.7$ $(0.3)$ | $43.6$ $(0.4)$ | $47.1$ $(0.4)$ | $75.3$ $(0.3)$       | $71.6$ $(0.2)$ | $63.0$ $(0.4)$ | $62.1$ $(0.5)$ |
| 100     |                | -                    | -              | $28.0$ $(0.3)$ | $38.2$ $(0.2)$ | -                    | -              | -              | $51.3$ $(0.5)$ | -                    | -              | -              | -              |
| Full    |                | ConvNet(F) = $38.5%$ |                |                |                | ConvNet(F) = $56.4%$ |                |                |                | ConvNet(F) = $81.5%$ |                |                |                |

<span id="page-4-2"></span>Image /page/4/Figure/2 description: The image contains two plots. The left plot is titled "ImageNet-1K Soft Label Baseline ResNet18". The x-axis is labeled "Expert test Acc (%)" and ranges from 15 to 50. The left y-axis is labeled "Student test Acc (%)" and ranges from 5 to 25. The right y-axis is labeled "Avg soft label entropy" and ranges from 3.0 to 5.0. There are four lines representing different IPC values: IPC=1 (blue dotted line), IPC=2 (green dashed line), IPC=5 (red dashed line), and IPC=10 (cyan dashed line). A gray solid line represents "Label Entropy". The right plot is titled "TinyImageNet Soft Label Baseline ConvNetD4". The x-axis is labeled "Avg softlabel entropy" and ranges from 0.5 to 2.5. The left y-axis is labeled "Student test acc (%)" and ranges from 33 to 39. The right y-axis is also labeled "Expert test acc (%)" and ranges from 33 to 39. There is a legend indicating "IPC=100", "Expert" (gray dotted line), and "Student" (blue dotted line with shaded error bars).

Figure 2: Expert test accuracy v.s. student test accuracy v.s. soft label entropy. The quality of soft labels (measured by student accuracy) depends on expert accuracy (*left*) and label entropy (*right*).

Importantly, the soft label baseline is robust to random image selection and expert training seeds (Appendix [A.4\)](#page-13-1). Using a simple image selection strategy (cross-entropy) could improve the performance of the soft label baseline (Appendix [B\)](#page-14-0). However, the goal of establishing this baseline is not to achieve the best performance but to showcase the importance of soft labels for dataset distillation. In the next section, we aim to understand why soft labels are so important for learning from limited data.

## 4 Good Soft Labels Require Structured Information

Despite its embarrassing simplicity, the soft label baseline yields strong performances, suggesting that labels are an important —perhaps the most important— aspect of data-efficient learning. Therefore, we next seek to understand why soft labels are so effective for learning. In Section [4.1,](#page-4-1) we start with a generation observation that expert epoch determines the optimal soft label. In Section [4.2,](#page-5-0) we develop an understanding that the structured information encoded in soft labels is what allows models to learn from so little data. Finally, in Section [4.3,](#page-6-0) we establish an empirical knowledge-data scaling law and an extreme version of distillation — learning with no (feature) data.

### <span id="page-4-1"></span>4.1 General observations from the soft label baseline

In establishing the baseline, we observe that the optimal expert to generate soft labels depends on the (distilled) data budget. Specifically, for smaller data budgets, it is often better to use labels generated by an early-stopped expert, which we will refer to "expert at epoch  $x$ ." As an example, Figure [2](#page-4-2) visualizes how using expert at different epochs impact the student test accuracy for ImageNet-1K.

As we train the expert for more epochs, two things in soft labels can change: the information being conveyed in softmax values (i.e., the softmax distribution itself), and the amount of information being conveyed (i.e., the entropy). In Figure [5,](#page-5-1) we visualize the expert soft labels for a randomly selected image from TinyImageNet (a German Shepherd, with the corresponding label shown in red) at 4 different epochs. At epoch 0, the soft labels are randomly initialized. The expert at epoch11 (optimal for IPC=1) considers "Bison" as top guess but only assigns  $2\%$  probability. At epoch 20, the top two guesses become "Brown bear" and "Bison." Finally, at epoch 50 (roughly optimal for  $IPC=10$ , the expert correctly assigns "German Shepherd" with the maximum likelihood. This anecdotal example shows that the information being conveyed can change over the course of expert

<span id="page-5-2"></span>Image /page/5/Figure/0 description: This is a line graph titled "TinyImageNet, i-th Label Swap Test". The y-axis is labeled "Relative perfomance (%)" and ranges from 20 to 100. The x-axis is labeled "Swap i-th label (log2-scale)" and ranges from 2 to 128 on a logarithmic scale. There are three dashed lines representing different IPC values: IPC=50 (green), IPC=10 (blue), and IPC=1 (red). All three lines show an increasing trend in relative performance as the swap i-th label value increases. The green line (IPC=50) generally shows the highest performance, reaching close to 100% at the highest swap values. The blue line (IPC=10) is below the green line but above the red line (IPC=1) for most of the graph. The red line (IPC=1) shows the lowest performance, especially at lower swap values, but also approaches 100% at the highest swap values.

Figure 3: **Importance of i-th label by per**forming label swapping test. Swap the  $i$ -th label (sorted by softmax value) with the last label. Top labels contain structured information and the non-top labels contain noise.

Image /page/5/Figure/2 description: A heatmap titled "TinyImageNet IPC=10 (Expert Epoch, Temp) Grid" displays student accuracy percentages. The y-axis represents "Expert Epoch" with values ranging from 38 to 84, and the x-axis represents "Temperature" with values from 1.0 to 5.0. The color bar on the right indicates "Student Acc (%)" from 24 to 30. The grid cells contain numerical values representing the accuracy for each combination of expert epoch and temperature. For example, at an expert epoch of 84 and temperature of 1.0, the accuracy is 24.0%. At an expert epoch of 62 and temperature of 2.3, the accuracy is 30.0%. At an expert epoch of 54 and temperature of 2.8, the accuracy is 30.0%.

Figure 4: (Expert Epoch, Temp) grid search on TinyImageNet IPC=10. Temperature smoothing does not fully resolve the issue that later epoch experts yield sub-optimal labels for a given data budget.

training. Besides changes in the per-class information, the entropy of labels also drop over the course of training. Eventually, the labels sharpen and converge to hard labels, meaning less information is being conveyed. As a first attempt to isolate these two factors (information, entropy), we look at experts that have been trained until convergence. Figure [2](#page-4-2) (right) shows that the expert starts to converge and even slightly overfits. The test accuracy stabilizes around 38%, but the entropy of the labels continues to drop drastically. Despite similar performance by the expert, the later epoch experts generate labels that are significantly worse for the student model than the earlier epoch experts. In the next section, we further disentangle the contributions of these two sources of variation (information and entropy) to the quality of soft labels.

### <span id="page-5-0"></span>4.2 Structured information in soft labels and its importance for distillation

We first distinguish two components in soft labels - *structured information* and *unstructured noise*. We define structured information as softmax values that the student models learn from they contain information such as semantic similarities between classes (see Appendix [C.1](#page-14-1) for a visualization of information in soft labels). Unstructured noise, on the other hand, contains no information but may provide regularization during student training. The contribution of these two effects has only been studied in KD settings [\[34,](#page-11-14) [38\]](#page-11-15). In data distillation, we aim to quantify the percentage of softmax values that provide learnable information and the percentage that contains only noise. We also want to determine whether the optimal structured information is unique to each data budget.

Label swapping test. To quantify the percentage of softmax values that contain structured information, we design a " $i$ -th label swapping test." This experiment relies on the assumption

<span id="page-5-1"></span>Image /page/5/Figure/8 description: The image displays four bar charts stacked vertically, each representing softmax probabilities for different classes at various training epochs for a German shepherd sample. The title of the figure is "Soft labels for a German shepherd sample." The y-axis for all charts is labeled "Softmax Probability," and the x-axis for all charts is labeled "Class." The first chart, labeled "Expert at Epoch 0," shows a relatively flat distribution of probabilities across many classes, with a few small peaks and one prominent red bar indicating a specific class with a probability of approximately 0.005. The second chart, "Expert at Epoch 11," shows a more varied distribution, with several classes having higher probabilities, including one peak around 0.025 and another around 0.035. The red bar is still present but lower than in the first chart. The third chart, "Expert at Epoch 20," exhibits a sharper distribution with a few distinct peaks, the highest being around 0.080, and the red bar is significantly reduced. The fourth chart, "Expert at Epoch 50," shows a highly concentrated distribution with a very prominent red bar reaching approximately 0.400, indicating a strong prediction for a specific class, while other classes have very low probabilities. The x-axis labels list a wide variety of classes, including animals, objects, and food items.

Figure 5: Soft labels generated by expert at different epochs. The structured information in soft labels changes over the course of training.

that if we sort labels by their softmax values in descending order, the last label likely contains no useful information. In the experiment, we swap  $i$ -th label with the last label, and measure the relative performance compared to unswapped labels. Refer to Appendix [C.2](#page-15-0) for a detailed experiment procedure. Figure [3](#page-5-2) shows *i*-th label swapping test results on TinyImageNet. For all IPC budgets, swapping top labels significantly hurts the performance, indicating when learning with few data,

<span id="page-6-1"></span>Image /page/6/Figure/0 description: The image displays two scatter plots side-by-side, both titled "TinyImageNet Data-Knowledge Scaling Law." The left plot, labeled "(Expert at epoch 11)," shows student accuracy (%) on the y-axis against dataset size (IPC) on the x-axis, with data points plotted for various values of K (1, 2, 4, 8, 16, 32, 64, 128, 200) and a "Hardlabel" category. The right plot, labeled "Pareto Front," also plots student accuracy (%) against dataset size (IPC) on a logarithmic scale, showing data for "Epoch 11," "Epoch 20," "Epoch 40," "Epoch 60," and "Epoch 90," along with "Hardlabel," "Full Data," and "Fitted" lines. The y-axis on the left plot ranges from 2 to 14, while the y-axis on the right plot ranges from 5 to 40, with a dashed line at 40. The x-axis for both plots is labeled "Dataset Size (IPC), Log10-scaled."

Figure 6: Data-Knowledge Scaling Law for TinyImageNet. *Left*: Trading knowledge (amount of information in soft labels) with data using one expert. *Right*: Establishing the Pareto-optimal front for data-efficient learning.

students rely more on structured information (knowledge) during learning. For 1 IPC, the top label does not contain as much information compared to higher IPC. It is likely because the top-1 label generated by an early-epoch expert, which itself has poor performance, does not contain much useful information.

**Effect of temperature and epoch in soft labels.** So far we have only observed that for a given data budget, there is an optimal expert epoch for label generation (Figure [6\)](#page-6-1). This observation does not imply that the structured information generated by this expert is indeed optimal. Specifically, we could not eliminate the possibility that labels generated by the expert at a later epoch may contain better information but too sharp for the student to learn from (i.e., suboptimal entropy). To further disentangle the two factors (information and entropy) and understand the impact of label entropy on label quality, we use a solution from KD: varying the temperature of the softmax value [\[13\]](#page-10-5) to control label entropy. Specifically, we perform an extensive grid search on (Expert epoch, Temperature) under TinyImageNet IPC=10 setting, results shown in Figure [4.](#page-5-2) Increasing the temperature *does* further improve the soft label baseline performance, but the optimal epoch in the temperature=1 setting remains optimal even when temperature smoothing is used. From these two experiments, we conclude that students leverage structured information in soft labels during learning, and the optimal information (expert) is unique to each data budget.

### <span id="page-6-0"></span>4.3 Trading data with knowledge

We have established that the smaller the data budget, the more heavily the student model relies on structured information present in the soft labels. In this section, we further explore the tradeoff between data and knowledge in two settings. First, we explore a scaling law to quantify how knowledge can reduce dataset size. Second, we further the idea of "learning from limited data" to "learning from no data" for a given class.

Scaling law. The data-knowledge scaling law experiment quantitatively measures how knowledge can reduce dataset size *and* the optimal knowledge for each dataset size. For the first experiment, we use an expert trained on TinyImageNet for 7 epochs (with a test accuracy of 11%) as the teacher model. As in the baseline setting, the data (images) are randomly selected from the original training set. To establish the empirical scaling law, we vary dataset sizes (measured by IPC) and the amount of knowledge in the soft labels.To control the amount of knowledge in the soft labels, we retain different top-k values (sorted by softmax probabilities in descending order) and zero out the rest *without* re-normalization.We compare the data-knowledge scaling law against the standard training scaling law (i.e., using hard labels from data).

The results of the knowledge-data trade-off experiment are shown in Figure [6](#page-6-1) left. To fully recover the teacher model, the student needs around 10 IPC if full knowledge is present  $(k=200)$ . But the IPC value quadruples to 40 if we reduce  $k$  to 32. Moreover, learning from this expert becomes suboptimal as the dataset size increases, and students trained on hard labels start to outperform. This finding supports the observation from Section [4.2:](#page-5-0) with an increased data budget, the student benefits more from learning from a model at a later epoch. In Appendix [C.3,](#page-15-1) we repeat the knowledge-data trade-off

<span id="page-7-0"></span>Image /page/7/Figure/0 description: The image displays three bar charts side-by-side, each titled "IPC1", "IPC10", and "IPC50" respectively. The overall title of the figure is "TinyImageNet, Remove class images v.s. class label". The y-axis for all charts is labeled "Image Class" and ranges from 0 to 20. The x-axis for "IPC1" ranges from 0 to 50, while the x-axis for "IPC10" and "IPC50" ranges from 0 to 70 and 0 to 80 respectively. Each chart shows horizontal bars representing different image classes. There are three categories of bars for each class: "Control" (light gray), "Remove Class image" (pink), and "Remove Class label" (purple). Error bars are present for each bar. The bars generally decrease in length as the image class number increases. The "Remove Class image" and "Remove Class label" bars are stacked on top of the "Control" bars.

Student Accuracy (%) for Removed class

Figure 7: Zero-shot learning in the absence of knowledge v.s. data. The student model can achieve good performances when data is absent but much worse performances when knowledge is absent.

experiment using an expert trained for later epochs. By combining experts at different epochs with different data budgets, we can establish a Pareto-optimal front for data-efficient learning, as shown in Figure [6](#page-6-1) right, and curve fit below. The use of expert knowledge provides a constant  $6\times$  data size reduction since  $|\mathcal{D}| =$  num class  $\times$  IPC:

Hard Label: Student Test Acc = 
$$
\left(\frac{\text{IPC}}{29.9}\right)^{0.077} - 0.8
$$

Soft Label: Student Test Acc = 
$$
\left(\frac{6.04 \times \text{IPC}}{29.9}\right)^{0.077} - 0.8
$$

Learning from (almost) no data. We have used the empirical knowledge-data scaling law experiment to establish that one can directly trade knowledge with data. This observation inspires us to push this idea to the extreme. For a selected class, in which of the following two scenarios would the student perform better? 1) The student learns in the complete absence of data from this class. 2) The student learns with data from this class but in the absence of knowledge.

For a selected class i, we design a zero-shot experiment with a control and two treatment groups:

- Control: The student model is trained with data from all classes and full soft label knowledge.
- Treatment 1 Remove image: We remove *all* training images from class i while keeping the softmax values corresponding to class  $i$  in the rest of the training data.
- Treatment 2 Remove label: We keep images from class i and the *full* soft label for those images. For all other images in the training data that don't belong to class  $i$ , we zero-out softmax values corresponding to class i *without* re-normalization.

Figure [7](#page-7-0) shows the experimental results on TinyImageNet under three IPC settings (classes sorted by control group performance). Compared to the control group, the performance of the remove-data treatment suffers only a little.This good performance implies that the student can perform zero-shot classification on the removed class *without* having seen any images from the class. In contrast, the student's performance suffers drastically when labels are removed. For IPC=1, the student model fails to learn anything at the remove-label setting (0% test accuracy for removed classes), and the performance of the remove-label treatment only starts to catch up at IPC=50.This substantial performance difference between the two treatment groups suggests that the information conveyed in labels is more important than the data.

## 5 Soft Labels are Not Created Equal

So far we have established that one way to achieve dataset distillation is by trading knowledge for data, specifically using knowledge from pretrained experts. In this section, we continue to work under the setting where images are randomly sampled from the training data, but we expand on different ways to obtain "knowledge." In Section [5.1,](#page-8-0) we show that an expert ensemble can consistently improve soft label quality. In Section [5.2,](#page-8-1) we discuss the possibility of obtaining soft labels through data distillation methods.

<span id="page-8-2"></span>Table 3: Comparison of label generation strategies on TinyImageNet. Ensemble provides consistent performance improvement. Distillation method (BPTT) can be adopted to learn labels.

| <b>Label Source</b> | Data      |           | Expert    |                          | Distilled |
|---------------------|-----------|-----------|-----------|--------------------------|-----------|
| Labeling Method     | Hard      | Single    | Ensemble  | <b>BPTT</b>              | MTT       |
| $IPC=1$             | 1.6(0.1)  | 7.6(0.3)  | 8.7(0.1)  | 13.5(0.3)                | 2.2(0.0)  |
| 10                  | 7.3(0.2)  | 27.2(0.4) | 30.1(0.2) | 25.0(0.4)                | 10.3(0.2) |
| 50                  | 20.7(0.3) | 35.6(0.4) | 39.3(0.4) | $\overline{\phantom{a}}$ | 22.2(0.5) |

### <span id="page-8-0"></span>5.1 Expert ensemble

A common strategy to boost predictive performance in machine learning is to use ensemble predictors. By combining predictions from various models, ensemble methods effectively capture a more comprehensive representation of the underlying data distribution. Therefore, we hypothesize that averaging soft label predictions from multiple experts might improve the quality of the labels. We train 10 ConvNet experts with different random seeds on TinyImageNet. To generate ensemble soft labels, we average the logits from all the experts for each of the randomly selected training images before passing them through the softmax calculation. For a fair comparison, we use the expert epoch previously determined in the soft label baseline. Table [3](#page-8-2) shows that soft labels derived from expert ensembles consistently improve student model accuracy compared to single-expert labels.

### <span id="page-8-1"></span>5.2 Learning soft labels through data distillation methods

Instead of using pretrained experts to generate soft labels, a natural alternative is to learn labels through dataset distillation methods. The motivation is twofold: First, we aim to explore different approaches in hopes of finding better ways to obtain labels. More importantly, we have demonstrated that using the knowledge-distillation framework (i.e., using pretrained experts) is a *sufficient* mechanism to obtain informative labels. We now want to understand whether the information generated by experts is the only (or *necessary*) solution.

Distribution matching-based distillation methods, one of the three predominant categories of successful distillation methods identified above, cannot be easily adapted to learn labels because they aim to generate synthetic images that minimize cross entropy on a teacher model. This leaves two other possible families of methods: training trajectory matching and bi-level optimization. For both methods, we can adopt the same training objective but freeze the images and learn the labels only.For the training trajectory matching objective, we adopt MTT [\[4\]](#page-10-6), and for the bi-level optimization objective, we adopt truncated-BPTT [\[9\]](#page-10-4) (also known as BPTT). We experiment with these two adaptations on TinyImageNet, with results shown in Table [3.](#page-8-2) The MTT adaptation fails to meaningfully improve on the hard label baseline—see Appendix [D.1](#page-16-0) for a detailed description of the MTT objective and a discussion on why it fails. On the other hand, the BPTT adaptation yields meaningful results, even slightly improving on ensemble labels in the IPC=1 setting.

To understand whether expert knowledge is *necessary* to obtain useful soft labels, we compare BPTT-learned labels with ensemble-generated labels. The motivation behind comparing BPTT labels is not due to their performance, but because BPTT addresses dataset distillation directly from its problem formulation (Eqn. [1\)](#page-3-1). By directly solving the bi-level optimization problem, no experts are trained during the distillation process. Moreover, it eliminates concerns about whether a proxy distillation objective, such as training trajectory matching, may introduce bias into the distillation outcome. See Appendix [D.2](#page-16-1) for a detailed description of truncated-BPTT algorithm and our adaptation to learn labels.

We fix the randomly sampled training images and generate soft labels using an expert ensem-

<span id="page-8-3"></span>Image /page/8/Figure/9 description: The image displays two heatmaps side-by-side, both titled "Normalized JSD for BPTT v.s. Ensemble labels". The left heatmap is labeled "IPC=1" and the right heatmap is labeled "IPC=10". Both heatmaps have "Expert Epoch" on the x-axis, ranging from 0 to 50. The y-axis on the left heatmap is labeled "Per Image" and ranges from 0 to 200. The y-axis on the right heatmap is not explicitly labeled with numbers but appears to correspond to a similar scale, with a label "1K" and "2K" visible. A color bar on the right side of the image indicates "Normalized JSD" and ranges from 0 to 1.0, with colors transitioning from purple (low values) to yellow (high values). The heatmaps show a pattern where values are generally higher (more yellow) towards the beginning of the "Expert Epoch" and decrease (more purple) towards the end, with some horizontal banding visible.

Figure 8: Normalized JSD between BPTTlearned labels and ensemble-expert labels on TinyImageNet. Despite not training any experts, distillation method (BPTT) recovers the same labels as those generated by early stopped experts.

ble and using BPTT. We use ensemble labels instead of a single expert labels to reduce bias. We use the Jensen-Shannon Distance (JSD) to quantify the distance between the softmax distributions. For each image, we also perform a min-max normalization across all expert epochs to identify the epoch at which the BPTT-generated label for that image is most similar to the ensemble-generated labels. See Appendix [D.2](#page-16-1) for a detailed description of the comparison methodology. Figure [8](#page-8-3) shows the normalized JSD for all images under the TinyImageNet IPC=1 and IPC=10 settings. For both data budgets, the minimizing epoch for the normalized JSD across all images is roughly the same (Epoch 13 for IPC=1, 25 for IPC=10). Conveniently, epoch 13 is fairly close to the optimal early-stop epoch for IPC=1 (optimal stop epoch = 11). In Appendix [D.2,](#page-16-1) we also compare raw JSD values to further ensure that the distributions are sufficiently close to each other on an absolute scale.

The two observations above provide strong evidence that BPTT recovers the same labels generated by the optimal early-stopped expert, *without* explicitly training any. Note that by restricting to only learning labels, we limit the scope of the dataset distillation problem. However, in this restricted setting, the dataset distillation solution converges to the knowledge distillation solution (i.e., labels from an optimal early-stopped expert), indicating that expert knowledge is *necessary* to obtain informative labels. A future direction is to explore how this conclusion might change when images are learned simultaneously with labels.

## 6 Discussion and Conclusions

This paper highlights the crucial role of soft labels in dataset distillation. Through a series of ablation experiments and a simple soft label baseline, we have shown that the use of soft labels is the main factor behind the success of state-of-the-art dataset distillation methods. We also demonstrated that structured information in soft labels is crucial for data-efficient learning. We established an empirical knowledge-data scaling law to characterize the effectiveness of using knowledge to reduce dataset size and an empirical Pareto frontier for data-efficient learning. Additionally, we showed that when only learning labels, data distillation converges to the knowledge distillation solution.

Implications for dataset distillation Our findings have several implications. First, they suggest rethinking existing data distillation methods that emphasize on synthetic image generation techniques, while under emphasize the importance of informative labels. Shifting the focus of distillation research to account for both of these factors is likely to lead to novel, potentially better, approaches. For example, in this work we only considered the setting where images are randomly selected from the training data, but future work may explore simultaneous image/label synthesis methods to improve distillation performance. Future research may also investigate whether data distillation can be achieved without using expert knowledge, either implicitly or explicitly, in a model-agnostic or even task-agnostic way. For example, future research may investigate how to synthesize images or labels by summarizing characteristics of the dataset, such as symmetries and other invariance priors.

Limitations and future work We have emphasized the importance of soft labels by implementing a simple soft label baseline. While this approach performs well on ImageNet-1K, advanced methods such as  $[12, 24]$  $[12, 24]$  $[12, 24]$  continue to push the state of the art. This suggests that, while labels are crucial, successful dataset distillation requires leveraging both images and labels to achieve optimal compression. Future work should explore how best to optimize both components during distillation and examine how each uniquely influences student model learning. Additionally, investigating what other types of information, beyond expert knowledge, can be distilled for effective compression remains an open research question.

On the label generation front, we have explored strategies based on existing methodologies, including pretrained experts and Ra-BPTT. Future research could further investigate more effective ways to generate informative labels that enhance model performance.

Lastly, like much of the dataset distillation research, our focus has primarily been on image classification tasks. Although dataset distillation has demonstrated strong results in this area, extending these methods to other tasks and data modalities remains under-explored. While we believe our conclusions are generalizable, a limitation of this work is the narrow range of tasks evaluated. Future studies should explore how distillation techniques perform across a broader set of domains and modalities.

# Acknowledgments

Tian Qin and David Alvarez-Melis were partially supported by the Kempner Institute, the Aramont Fellowship Fund, and the FAS Dean's Competitive Fund for Promising Scholarship. We would also like to thank Clara Mohri and Eran Malach for their insightful discussions and helpful feedback throughout the development of this work. Their input has been invaluable in shaping the direction of our research.

### References

- <span id="page-10-0"></span>[1] M. Abdin et al. "Phi-3 Technical Report: A Highly Capable Language Model Locally on Your Phone". *arXiv [cs.CL]* (2024). arXiv: [2404.14219 \[cs.CL\]](https://arxiv.org/abs/2404.14219).
- <span id="page-10-14"></span>[2] J. Ba and R. Caruana. "Do deep nets really need to be deep?" *Adv. Neural Inf. Process. Syst.* (2013), pp. 2654–2662. eprint: <1312.6184>.
- <span id="page-10-13"></span>[3] O. Bohdal, Y. Yang, and T. Hospedales. "Flexible Dataset Distillation: Learn Labels Instead of Images". *arXiv [cs.LG]* (2020). arXiv: [2006.08572 \[cs.LG\]](https://arxiv.org/abs/2006.08572).
- <span id="page-10-6"></span>[4] G. Cazenavette et al. "Dataset Distillation by Matching Training Trajectories". *arXiv [cs.CV]* (2022). arXiv: [2203.11932 \[cs.CV\]](https://arxiv.org/abs/2203.11932).
- <span id="page-10-11"></span>[5] G. Cazenavette et al. "Generalizing dataset distillation via deep generative prior". *arXiv [cs.CV]* (2023), pp. 3739–3748. arXiv: [2305.01649 \[cs.CV\]](https://arxiv.org/abs/2305.01649).
- <span id="page-10-3"></span>[6] J. Cui, R. Wang, S. Si, and C.-J. Hsieh. "Scaling Up Dataset Distillation to ImageNet-1K with Constant Memory". *arXiv [cs.CV]* (2022). arXiv: [2211.10586 \[cs.CV\]](https://arxiv.org/abs/2211.10586).
- <span id="page-10-18"></span>[7] Z. Deng and O. Russakovsky. "Remember the past: Distilling datasets into addressable memories for neural networks". *arXiv [cs.LG]* (2022). Ed. by S. Koyejo et al., pp. 34391– 34404. arXiv: [2206.02916 \[cs.LG\]](https://arxiv.org/abs/2206.02916).
- <span id="page-10-12"></span>[8] J. Du et al. "Minimizing the Accumulated Trajectory Error to Improve Dataset Distillation". *arXiv [cs.LG]* (2022). arXiv: [2211.11004 \[cs.LG\]](https://arxiv.org/abs/2211.11004).
- <span id="page-10-4"></span>[9] Y. Feng, R. Vedantam, and J. Kempe. "Embarassingly Simple Dataset Distillation". *arXiv [cs.LG]* (2023). arXiv: [2311.07025 \[cs.LG\]](https://arxiv.org/abs/2311.07025).
- <span id="page-10-15"></span>[10] J. Gou, B. Yu, S. J. Maybank, and D. Tao. "Knowledge Distillation: A Survey". *Int. J. Comput. Vis.* 129.6 (2021), pp. 1789–1819.
- <span id="page-10-1"></span>[11] S. Gunasekar et al. "Textbooks Are All You Need". *arXiv [cs.CL]* (2023). arXiv: [2306.11644](https://arxiv.org/abs/2306.11644) [\[cs.CL\]](https://arxiv.org/abs/2306.11644).
- <span id="page-10-10"></span>[12] Z. Guo et al. "Towards lossless Dataset Distillation via difficulty-aligned trajectory matching". *arXiv [cs.CV]* (2023). arXiv: [2310.05773 \[cs.CV\]](https://arxiv.org/abs/2310.05773).
- <span id="page-10-5"></span>[13] G. Hinton, O. Vinyals, and J. Dean. "Distilling the Knowledge in a Neural Network". *arXiv [stat.ML]* (2015). arXiv: [1503.02531 \[stat.ML\]](https://arxiv.org/abs/1503.02531).
- <span id="page-10-2"></span>[14] A. Hughes. *Phi-2: The surprising power of small language models*. [https : / / www .](https://www.microsoft.com/en-us/research/blog/phi-2-the-surprising-power-of-small-language-models/) [microsoft . com / en - us / research / blog / phi - 2 - the - surprising - power - of](https://www.microsoft.com/en-us/research/blog/phi-2-the-surprising-power-of-small-language-models/)  [small-language-models/](https://www.microsoft.com/en-us/research/blog/phi-2-the-surprising-power-of-small-language-models/). Accessed: 2024-5-21.
- <span id="page-10-8"></span>[15] J.-H. Kim et al. "Dataset Condensation via Efficient Synthetic-Data Parameterization". *arXiv [cs.LG]* (2022). arXiv: [2205.14959 \[cs.LG\]](https://arxiv.org/abs/2205.14959).
- <span id="page-10-9"></span>[16] H. B. Lee, D. B. Lee, and S. J. Hwang. "Dataset Condensation with Latent Space Knowledge Factorization and Sharing". *arXiv [cs.LG]* (2022). arXiv: [2208.10494 \[cs.LG\]](https://arxiv.org/abs/2208.10494).
- <span id="page-10-20"></span>[17] T. Maintainers and Contributors. *TorchVision: PyTorch's Computer Vision library*. 2016.
- <span id="page-10-21"></span>[18] G. A. Miller. "WordNet: a lexical database for English". *Commun. ACM* 38.11 (1995), pp. 39– 41.
- <span id="page-10-16"></span>[19] R. Müller, S. Kornblith, and G. E. Hinton. "When does label smoothing help?" *Adv. Neural Inf. Process. Syst.* abs/1906.02629 (2019). eprint: <1906.02629>.
- <span id="page-10-7"></span>[20] T. Nguyen, Z. Chen, and J. Lee. "Dataset Meta-Learning from Kernel Ridge-Regression". *arXiv [cs.LG]* (2020). arXiv: [2011.00050 \[cs.LG\]](https://arxiv.org/abs/2011.00050).
- <span id="page-10-19"></span>[21] A. Paszke et al. "PyTorch: An Imperative Style, High-Performance Deep Learning Library". *arXiv [cs.LG]* (2019). arXiv: [1912.01703 \[cs.LG\]](https://arxiv.org/abs/1912.01703).
- <span id="page-10-17"></span>[22] T. Qin, Z. Deng, and D. Alvarez-Melis. "Distributional Dataset Distillation with Subtask Decomposition". *arXiv [cs.LG]* (2024). arXiv: [2403.00999 \[cs.LG\]](https://arxiv.org/abs/2403.00999).

- <span id="page-11-1"></span>[23] N. Sachdeva and J. McAuley. "Data Distillation: A Survey". *arXiv [cs.LG]* (2023). arXiv: [2301.04272 \[cs.LG\]](https://arxiv.org/abs/2301.04272).
- <span id="page-11-16"></span>[24] S. Shao et al. "Generalized large-scale data condensation via various backbone and Statistical Matching". *arXiv [cs.CV]* (2023). arXiv: [2311.17950 \[cs.CV\]](https://arxiv.org/abs/2311.17950).
- <span id="page-11-11"></span>[25] Z. Shen and E. Xing. "A Fast Knowledge Distillation Framework for Visual Recognition". *arXiv [cs.CV]* (2021). arXiv: [2112.01528 \[cs.CV\]](https://arxiv.org/abs/2112.01528).
- <span id="page-11-10"></span>[26] I. Sucholutsky and M. Schonlau. "Soft-Label Dataset Distillation and Text Dataset Distillation". *2021 International Joint Conference on Neural Networks (IJCNN)*. IEEE, 2021, pp. 1–8.
- <span id="page-11-13"></span>[27] Y. Tian, D. Krishnan, and P. Isola. "Contrastive Representation Distillation". *arXiv [cs.LG]* (2019). arXiv: [1910.10699 \[cs.LG\]](https://arxiv.org/abs/1910.10699).
- <span id="page-11-5"></span>[28] K. Wang et al. "CAFE: Learning to Condense dataset by Aligning FEatures". *arXiv [cs.CV]* (2022), pp. 12196–12205. arXiv: [2203.01531 \[cs.CV\]](https://arxiv.org/abs/2203.01531).
- <span id="page-11-0"></span>[29] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros. "Dataset Distillation". *arXiv [cs.LG]* (2018). arXiv: [1811.10959 \[cs.LG\]](https://arxiv.org/abs/1811.10959).
- <span id="page-11-9"></span>[30] W. Yang, Y. Zhu, Z. Deng, and O. Russakovsky. "What is Dataset Distillation Learning?" *arXiv [cs.LG]* (2024). arXiv: [2406.04284 \[cs.LG\]](https://arxiv.org/abs/2406.04284).
- <span id="page-11-12"></span>[31] H. Yin et al. "Dreaming to Distill: Data-free Knowledge Transfer via DeepInversion". *arXiv [cs.LG]* (2019). arXiv: [1912.08795 \[cs.LG\]](https://arxiv.org/abs/1912.08795).
- <span id="page-11-8"></span>[32] Z. Yin and Z. Shen. "Dataset Distillation in Large Data Era". *arXiv [cs.CV]* (2023). arXiv: [2311.18838 \[cs.CV\]](https://arxiv.org/abs/2311.18838).
- <span id="page-11-2"></span>[33] Z. Yin, E. Xing, and Z. Shen. "Squeeze, Recover and Relabel: Dataset Condensation at ImageNet Scale From A New Perspective". *arXiv [cs.CV]* (2023). arXiv: [2306.13092 \[cs.CV\]](https://arxiv.org/abs/2306.13092).
- <span id="page-11-14"></span>[34] L. Yuan et al. "Revisiting Knowledge Distillation via label smoothing regularization". *Proc. IEEE Comput. Soc. Conf. Comput. Vis. Pattern Recognit.* (2020), pp. 3902–3910.
- <span id="page-11-6"></span>[35] B. Zhao and H. Bilen. "Dataset Condensation with Differentiable Siamese Augmentation". *Proceedings of the 38th International Conference on Machine Learning*. Ed. by M. Meila and T. Zhang. Vol. 139. Proceedings of Machine Learning Research. PMLR, 2021, pp. 12674–12685.
- <span id="page-11-4"></span>[36] B. Zhao and H. Bilen. "Dataset Condensation with Distribution Matching". *2023 IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*. IEEE, 2023, pp. 6514–6523.
- <span id="page-11-7"></span>[37] B. Zhao, K. R. Mopuri, and H. Bilen. "Dataset Condensation with Gradient Matching". *arXiv [cs.CV]* (2020). arXiv: [2006.05929 \[cs.CV\]](https://arxiv.org/abs/2006.05929).
- <span id="page-11-15"></span>[38] H. Zhou et al. "Rethinking Soft Labels for Knowledge Distillation: A Bias-Variance Tradeoff Perspective". *arXiv [cs.LG]* (2021). arXiv: [2102.00650 \[cs.LG\]](https://arxiv.org/abs/2102.00650).
- <span id="page-11-3"></span>[39] Y. Zhou, E. Nezhadarya, and J. Ba. "Dataset Distillation using Neural Feature Regression". *arXiv [cs.LG]* (2022). arXiv: [2206.00719 \[cs.LG\]](https://arxiv.org/abs/2206.00719).

## A Soft label baseline

# A.1 Experimental Details

Models For datasets including downsized ImageNet-1K, TinyImageNet, CIFAR-100, and CIFAR-10 we employ the standard ConvNet architecture that is used by data distillation methods we benchmark against [\[4,](#page-10-6) [7,](#page-10-18) [9,](#page-10-4) [36\]](#page-11-4). We use 3 convolutional blocks for low resolution datasets (res: $32 \times 32$ , CIFAR-10, CIFAR-100) and 4 convolutional blocks for medium resolution datasets (res:  $64 \times 64$ , TinyImageNet, downsized ImageNet-1K). For the original size ImageNet-1K, we use the PyTorch [\[21\]](#page-10-19) official implementation of ResNet18 and ResNet50 to standardize comparison against the SOTA methods [\[33\]](#page-11-2) we benchmark against.

Expert training We follow a standard training recipe to train experts on downsized ImageNet-1K, TinyImageNet, CIFAR-10, and CIFAR-100. This standard training recipe involves an SGD optimizer and a simple step learning rate schedule, and is used by distillation methods that require experts training in the distillation pipeline [\[4,](#page-10-6) [6\]](#page-10-3). Note that due to the simplicity of the training recipe and model architecture, the expert performance on the full dataset does not reach SOTA image classification performances. The expert training procedure is chosen to facilitate fair comparisons in the context of dataset distillation. For ImageNet-1K, SOTA methods [\[33\]](#page-11-2) we benchmark against leverage PyTorch pretrained ResNet18 as their teacher model, therefore we adopt the PyTorch official training recipe [\[17\]](#page-10-20) for expert training.

Evaluation To train student models on distilled dataset, we only allow learning rate tuning and we train student models until convergence. We train five student models with different random seeds and report mean and variance. For other methods we reported in Table [1,](#page-3-0) [4,](#page-13-2) and [2,](#page-4-0) we report values from the original paper. For methods that we report performance without using soft labels (Figure [1](#page-1-0) right), we use results reported in the original paper for MTT (TESLA)[\[6\]](#page-10-3), and FRePo [\[39\]](#page-11-3). For Ra-BPTT [\[9\]](#page-10-4) and  $SRe<sup>2</sup>L$  [\[33\]](#page-11-2), since they do not report results without soft labels, we acquire the distilled datasets and replace soft labels with hard labels. We perform the same evaluating procedure as above.

Compute All experiments are conducted on NVIDIA A100 SXM4 40GB or NVIDIA H100 80GB HBM3. To train small datasets, experiments typically require less than 5 GPU hours and for large datasets, experiments require at most 100 GPU hours.

# <span id="page-12-0"></span>A.2 Label generation and epoch tuning for existing methods

We provide further details regarding how labels are generated in Figure [1\(](#page-1-0)right). We compare our soft label baselines to four existing methods, each with their own soft label generation strategies proposed by the original authors. We apply the original soft label generation strategy used by each method. Overall, some of these methods already include epoch tuning (MTT/TESLA), while others do not  $(SRe<sup>2</sup>L)$ , and for some, the concept of epoch tuning is not well-defined (Ra-BPTT and FRePo). In addition, we also compare the performance of the four proposed methods with or without soft labels. We clarify how hard labels and soft labels are obtained for each of the methods:

SRe2L The original method uses labels generated from a fully trained expert without epoch tuning. In our reported results for  $SRe^{2}L$ , we include a version of the soft label generation method that directly replicates the original method (detailed in Section [3.2\)](#page-3-2). We also include a version of the soft label generation method that is identical to the soft label baseline, which includes epoch tuning (detailed in Section [3.1\)](#page-2-1). To obtain the hard label counterpart, since  $SRe<sup>2</sup>L$  initializes its distilled images directly from training images, we use the original label of those images as the hard label.

MTT (TESLA) In the original method, the expert used to generate labels is already epoch-tuned, and the epoch also impacts the image generation pipeline. Therefore, we replicate the soft label generation strategy in the original method. To obtain the hard label counterpart, since MTT/TESLA initialize their distilled images directly from training images, we use the original label of those images as the hard label.

Ra-BPTT The original method generates labels along with images using a bi-level optimization objective, so no experts are trained during the process. Because labels are not generated by experts, epoch tuning is not applicable. We use the soft labels generated by the original method. In addition, we have experimented with using pre-trained experts to generate labels for Ra-BPTT generated images but observed that experts trained on real images perform poorly on images synthesized by Ra-BPTT. This is likely because the generated images are too out-of-distribution for experts trained on real training data. Thus, the original labels generated by Ra-BPTT should be considered optimal

for this method. Since Ra-BPTT images are initialized with random Gaussian noise, we use the argmax as hard labels.

FRePo Similar to Ra-BPTT, FRePo is based on Back-Propagation in Time (BPTT), and no experts are trained during the distillation process. Like Ra-BPTT, labels are learned during the distillation process along with images. We use the hard label results reported in the original paper.

### <span id="page-13-0"></span>A.3 Downsized ImageNet-1K results

There are three other methods that can scale to ImageNet-1K but a downsized version (reducing image resolution from 256  $\times$  256 to 64  $\times$  64) and only at small IPC values. We compare their performances against the soft label baseline in Table [4.](#page-13-2) Among those methods, only Ra-BPTT can slightly outperform the soft label baseline, albeit by a small margin. Scaling these methods to larger data-budget remains a challenge, and as a result, we truncate Table [4](#page-13-2) to IPC=2.

<span id="page-13-2"></span>Table 4: Benchmark SOTA methods against the soft label baseline ("Sl Baseline") on (downsized) **ImageNet-1K.** Other distillation methods can only scale to downsized  $(64 \times 64)$  ImageNet-1K and they do not significantly outperform the baseline.

| <b>Distillation Method</b> | <b>ImageNet-1K (Downsized)</b> |              |             |                    |
|----------------------------|--------------------------------|--------------|-------------|--------------------|
|                            | <b>Ra-BPTT</b>                 | <b>FRePo</b> | <b>DM</b>   | <b>S1 Baseline</b> |
| IPC=1                      | $10.77 (0.4)$                  | $7.5 (0.5)$  | $1.5 (0.1)$ | $8.0 (0.4)$        |
| 2                          | $12.05 (0.5)$                  | $9.7 (0.2)$  | $1.7 (0.1)$ | $9.6 (0.3)$        |

### <span id="page-13-1"></span>A.4 Variance from expert training seed and random image seed

The soft label baseline involves randomly selecting images from training data and use pretrained single expert to generate soft labels for the selected images. We use 6 different random seeds for image selection to confirm that the soft label baseline is not sensitive to random image selection, shown in Table [5.](#page-13-3) Additionally, we confirm that the soft label baseline is no sensitive to experts training seeds. Specifically, we train six different experts using the same hyperparameters and only varying random seeds. In expert seed experiment, we fix the randomly selected images for the distilled dataset. Results shown in Table [6.](#page-13-4) As above, student test accuracy is reported as the average of five runs with different random seeds.

<span id="page-13-3"></span>Table 5: Image selection with different random seeds on TinyImageNet. Our soft label baseline is not sensitive to random image selections.

| IPC | Seed 1    | Seed 2    | Seed 3    | Seed 4    | Seed 5    | Seed 6    |
|-----|-----------|-----------|-----------|-----------|-----------|-----------|
| 1   | 7.7(0.2)  | 7.5(0.2)  | 7.3(0.3)  | 7.0(0.2)  | 7.2(0.2)  | 7.5(0.1)  |
| 10  | 26.8(0.1) | 26.8(0.2) | 27.3(0.1) | 26.6(0.1) | 26.9(0.2) | 27.0(0.1) |
| 50  | 36.1(0.3) | 35.6(0.5) | 35.6(0.4) | 36.0(0.1) | 35.8(0.3) | 35.6(0.1) |

<span id="page-13-4"></span>Table 6: Image selection with different expert training seeds on TinyImageNet. Our soft label baseline is not sensitive to experts with different training seeds

| IPC | Seed 1     | Seed 2     | Seed 3     | Seed 4     | Seed 5     | Seed 6     |
|-----|------------|------------|------------|------------|------------|------------|
| 1   | 7.6 (0.1)  | 7.5 (0.1)  | 8.0 (0.2)  | 7.5 (0.2)  | 7.9 (0.1)  | 7.6 (0.1)  |
| 10  | 26.4 (0.4) | 26.8 (0.3) | 26.4 (0.3) | 26.6 (0.2) | 26.4 (0.1) | 26.8 (0.2) |
| 50  | 35.7 (0.1) | 35.9 (0.2) | 36.0 (0.1) | 35.7 (0.4) | 35.7 (0.3) | 36.2 (0.4) |

### A.5 Hyperparameters for expert training and soft label generation

We include the optimal expert epochs to reproduce results reported in Table [1](#page-3-0) and Table [2.](#page-4-0) For smaller IPC values, we train experts with a reduced learning rate so that when we save expert checkpoints, we get expert accuracy at a more granular scale. Note that using a smaller learning rate for expert training is for implementation simplicity. Alternatively, one could use partial epoch checkpoints to achieve the same outcome.

| Dataset      | Expert Architecture | IPC | Expert LR          | Student LR         | Expert Epoch | Expert Test Accuracy (%) |
|--------------|---------------------|-----|--------------------|--------------------|--------------|--------------------------|
| ImageNet     | ResNet18            | 1   | $5 \times 10^{-4}$ | $1 \times 10^{-3}$ | 11           | 17.3                     |
|              |                     | 10  | $5 \times 10^{-4}$ | $5 \times 10^{-3}$ | 43           | 35.8                     |
|              |                     | 50  | $1 \times 10^{-1}$ | $5 \times 10^{-3}$ | 30           | 60.9                     |
|              |                     | 100 | $1 \times 10^{-1}$ | $5 \times 10^{-3}$ | 34           | 62.8                     |
|              |                     | 200 | $1 \times 10^{-1}$ | $2 \times 10^{-3}$ | 34           | 62.8                     |
| ImageNet     | ResNet50            | 1   | $5 \times 10^{-4}$ | $1 \times 10^{-3}$ | 9            | 15.4                     |
|              |                     | 10  | $5 \times 10^{-4}$ | $5 \times 10^{-3}$ | 24           | 31.0                     |
|              |                     | 50  | $1 \times 10^{-1}$ | $5 \times 10^{-3}$ | 30           | 66.6                     |
|              |                     | 100 | $1 \times 10^{-1}$ | $5 \times 10^{-3}$ | 32           | 68.6                     |
|              |                     | 200 | $1 \times 10^{-1}$ | $2 \times 10^{-3}$ | 61           | 73.3                     |
| TinyImageNet | ConvNetD4           | 1   | $1 \times 10^{-2}$ | $1 \times 10^{-2}$ | 11           | 19.2                     |
|              |                     | 10  | $1 \times 10^{-2}$ | $1 \times 10^{-1}$ | 50           | 35.0                     |
|              |                     | 50  | $1 \times 10^{-2}$ | $1 \times 10^{-1}$ | 90           | 38.1                     |
|              |                     | 100 | $1 \times 10^{-2}$ | $1 \times 10^{-1}$ | 102          | 38.8                     |
| CIFAR-100    | ConvNetD3           | 1   | $1 \times 10^{-2}$ | $1 \times 10^{-2}$ | 13           | 33.1                     |
|              |                     | 10  | $1 \times 10^{-2}$ | $1 \times 10^{-2}$ | 36           | 47.6                     |
|              |                     | 50  | $1 \times 10^{-2}$ | $1 \times 10^{-2}$ | 125          | 52.7                     |
|              |                     | 100 | $1 \times 10^{-2}$ | $1 \times 10^{-2}$ | 125          | 52.7                     |
| CIFAR-10     | ConvNetD3           | 1   | $5 \times 10^{-4}$ | $1 \times 10^{-2}$ | 20           | 56.0                     |
|              |                     | 10  | $5 \times 10^{-4}$ | $1 \times 10^{-2}$ | 44           | 65.1                     |
|              |                     | 50  | $5 \times 10^{-4}$ | $1 \times 10^{-2}$ | 128          | 74.9                     |
|              |                     | 100 | $5 \times 10^{-4}$ | $1 \times 10^{-2}$ | 164          | 76.5                     |

Table 7: Hyperparameter list to reproduce soft label baseline results in Table [1](#page-3-0) and Table [2.](#page-4-0)

<span id="page-14-0"></span>

## B Image selection using cross entropy

The soft label baseline involves the most naive way of selecting images from training data: random selection. We additionally show that using cross-entropy (CE) as a selection criteria further improves the data quality. For each class, we first use the optimal expert to compute cross-entropy values for each images in the original training set. We then divide the training images into 10 quantiles according to the CE values, where 1 corresponds to "easiest" sample with lowest CE. We perform the CE-selection on TinyImageNet with IPC=1, 10, 50 settings, and report results in Figure [9.](#page-14-2) Using the "easiest" samples provide a small but consistent improvement ( $\approx 1\%$ ) to random selection, while using the "hardest" samples hurts the performance much more.

<span id="page-14-2"></span>Image /page/14/Figure/4 description: This is a line graph titled "TinyImageNet Image selection w/ Cross-Entropy". The x-axis is labeled "Cross Entropy Quantile" and ranges from 1 to 10. The y-axis is labeled "Student Accuracy \Delta (%) compared to random baseline" and ranges from -4 to 3. There are four lines plotted: a black solid line labeled "Random Selection Baseline", a blue dashed line labeled "IPC=1", a green dashed line labeled "IPC=10", and a red dashed line labeled "IPC=50". All lines show a general downward trend as the Cross Entropy Quantile increases. The red dashed line (IPC=50) starts at the highest accuracy and decreases the most rapidly, while the blue dashed line (IPC=1) starts at the lowest accuracy and shows the slowest decrease.

Figure 9: Selecting images with cross-entropy criteria further improves the soft label baseline performances. We select training images based on cross-entropy scores using epoch-tuned experts, and report the relative student accuracy change when compared to random selection.

<span id="page-14-1"></span>

## C Additional Analysis Results

### C.1 Soft label visualization

We visualize the soft labels for TinyImageNet training set generated by a ConvNet expert at epoch 50 in Figure [10.](#page-15-2) For every image in the original training set, we use the expert to generate soft labels

and aggregate soft labels by the predicted class (i.e., argmax) with simple averaging. The diagonal (probability for the predicted class) is zeroed out so we can see the structures in non-top softmax values. Qualitatively, the clusters present correspond to WordNet ontology [\[18\]](#page-10-21), some of which we hand annotate with their common ancestors in WordNet.

<span id="page-15-2"></span>Image /page/15/Figure/1 description: This is a heatmap visualization of TinyImageNet Expert at Epoch 50, showing training data soft labels. The heatmap displays the average softmax probability (%) for each class. The classes are categorized into broad groups: Animal, Artifact, and Food, with subcategories like Chordate, Insect, and Placental within the Animal group. A smaller section labeled 'Geological formation' is also visible. The color bar on the right indicates probabilities ranging from 2% (light yellow) to 16% (dark purple). The x-axis and y-axis represent the 'Non-top guess class' and 'Top guess class (argmax)' respectively, with a list of specific class names for each.

Figure 10: Visualizing softmax probabilities for each class in Tiny ImageNet Soft labels are generated by pre-trained experts, and they exhibit structures related to semantic similarity.

### <span id="page-15-0"></span>C.2 *i*-th label swapping test

The experiment procedure is the following:

- i Use the optimal early-stopped expert to assign soft labels for a given set of randomly samples training images.
- ii Sort softmax probabilities in descending order
- iii At each iteration  $i \in [1, |C|]$ , we swap the *i*-th label with the last label.
- iv Train student model on the swapped label, and compute

Relative Performance = 
$$
\frac{\text{Student Model Accuracy on swapped } i\text{-th label}}{\text{Student Model Accuracy on unswapped label}}
$$

### <span id="page-15-1"></span>C.3 Data-knowledge Scaling Law

In Figure [11,](#page-16-2) we repeat the same data-knowledge scaling law in Section [4.3.](#page-6-0) The comparison Figure [6](#page-6-1) and Figure [11](#page-16-2) shows that to fully learn the expert model at a later epoch, the student models need more data. For example, to fully learn the expert model at epoch 11 (e.g., Figure [6\)](#page-6-1), with full knowledge, the student model needs less than 20 IPC. In contrast, to fully learn the expert model

<span id="page-16-2"></span>at epoch 46 (e.g. Figure [11\)](#page-16-2), the student model needs almost 100 IPC. By combining scaling laws for different experts and for different data budgets, we establish the Pareto frontier for data-efficient learning shown in Figure [6](#page-6-1) right.

Image /page/16/Figure/1 description: The figure is a line graph titled "TinyImageNet Data-Knowledge Scaling Law (Expert at epoch 46)". The x-axis represents "Dataset Size (IPC), Log10-scaled" and ranges from 1 to 100. The y-axis represents "Student Accuracy (%)" and ranges from 5 to 35. The graph displays multiple dashed lines, each representing a different value of K (from K=1 to K=200) or "Hardlabel". The lines show an increasing trend in student accuracy as the dataset size increases. The legend indicates that different colors and line styles correspond to different K values, with K=1 being the lowest accuracy and K=200 showing the highest accuracy among the soft label options. The "Hardlabel" line is also plotted, showing a lower accuracy compared to most of the soft label lines.

Figure 11: **Empirical Data-Knowledge Scaling Law with expert at epoch 46.** We repeat the scaling law experiment in Section [11](#page-16-2) using a later-epoch expert. The comparison between two experiments shows that more data is needed for the student to fully recover a later-epoch expert.

<span id="page-16-0"></span>

## D Details on label learning with distillation methods

### D.1 Learn label with MTT

MTT [\[4\]](#page-10-6) distills data by a trajectory matching objective. Given a network, the method first trains many experts on the full dataset, and saves the entire training trajectory (namely, the expert model parameters at each epoch). To learn the distilled data, a student model is initialized from a random checkpoint of a random expert, and then the student model is trained for several iterations on the distilled dataset. The goal is to match the model parameters (after being trained on the distilled dataset) with the model parameters trained the full dataset at a future epoch. We adopt Algorithm 1 in the original paper [\[4\]](#page-10-6) but simply freeze images after initializing them with random training data. Labels are initialized as hard labels according to the image class. During distillation, only labels are learned. We perform an extensive hyperparameter search on sensitive parameters such as: N: number of steps the student model trains on the distilled data,  $M$ : the future expert epoch we compare student model parameters to, T: the maximum expert epoch we initialize student model parameters with. Best results are reported in Table [3.](#page-8-2)

Table [3](#page-8-2) shows that MTT-adaptation only provides marginal improvements to the hard label baseline. When only images are distilled, the trajectory matching objective is a suitable proxy to the bi-level dataset distillation objective. Nevertheless, the training dynamics of student learning with soft labels could be quite different from the training dynamics of experts learning with hard labels. In other words, when soft labels are used to train student models, it is not guaranteed that matching experts' trajectory remains a suitable proxy for dataset distillation's objective.

### <span id="page-16-1"></span>D.2 Learn label with BPTT

We use truncated-BPTT [\[7,](#page-10-18) [9\]](#page-10-4) to solve the bi-level optimization problem detailed in Eqn. [1.](#page-3-1) See Algorithm [1](#page-17-0) for details. Instead of learning both images and labels, we initialize images from randomly selected training data and freeze them during distillation and learn label only. The BPTT algorithm has relative high sensitivity to hyperparameters such as  $T$ : total number of unrolling steps,  $M$ : truncated window size. Additionally, one common pitfall to BPTT is exploding gradients. To combat the optimization challenge, we adopt the algorithm and the training recipe from Feng et al. [\[9\]](#page-10-4). We perform an extensive hyperparameter search and report the best results in Table [3.](#page-8-2)

The comparison methodology is the following. To compare labels learned by BPTT and those generated from experts, we use the same data  $(\bar{x}_i)_{i=1}^{\text{IPC}}$  to generate labels with either methods. We denote expert-ensemble labels as  $\{y_i^l\}$ , where l indicates the early-stopped epoch. We denote the BPTT-learned labels as  $\{y_i^{\text{BPTT}}\}$ . We use Jensen-Shannon Distance (JSD) to quantify the distance between the soft labels generated by two methods.

### <span id="page-17-0"></span>Algorithm 1 Learn soft label with BPTT

**Require:** Target dataset  $\mathcal{D}_{target}$ . T: total number of unrolling steps. M: truncated window size.  $\alpha_1$ : student network learning rate.  $\alpha_2$ : distilled data(label) learning rate.  $\mathcal{L}$ : loss function(cross-entropy for classification) Randomly sample images from  ${x_i}_{i=1}^{\text{IPC}} \sim \mathcal{D}$ Initialize soft label with hard label. Distilled dataset:  $\mathcal{D}_{syn} = \{x_i, y_i\}_{i=1}^{\text{IPC}}$ while Not converged do Sample a batch of data  $d_{target} \in \mathcal{D}_{target}$ Randomly initialize student network parameter  $\theta_0$ for  $n: 0 \to T-1$  do if If  $n == T - M$  then Start accumulating gradients end if Sample a mini-batch of distilled data  $d_{syn} \sim \mathcal{D}_{syn}$ Perform gradient update on student network parameters  $\theta_{n+1} = \theta_n - \alpha_1 \nabla \mathcal{L}(d_{sun}; \theta_n)$ end for Compute loss on target data  $\mathcal{L}(d_{target}, \theta_N)$ Perform gradient update on soft label  $\{y_i\} \leftarrow \{y_i\} - \alpha_2 \nabla \mathcal{L}(d_{target}, \theta_N)$ end while

For each image  $x_i$ , we compute  $\text{JSD}(y_i^{\text{BPTT}}, y_i^l)$  for all l. We perform min-max normalization across epochs:

$$
\text{Normalized JSD}(y_i^{\text{BPTT}}, y_i^l) = \frac{\text{JSD}(y_i^{\text{BPTT}}, y_i^l) - \min_k \text{JSD}(y_i^{\text{BPTT}}, y_i^k)}{\max_k \text{JSD}(y_i^{\text{BPTT}}, y_i^k) - \min_k \text{JSD}(y_i^{\text{BPTT}}, y_i^k)}
$$

In addition to the normalized JSD shown in Figure [8,](#page-8-3) Figure [12](#page-17-1) shows the raw JSD comparisons between BPTT learned labels and ensemble-expert tagged labels. To build a baseline reference, we compute JSD values between soft labels generate by four single experts (only differ by training seeds) on the same set of images. For IPC=1, the JSD between BPTT learned labels and ensemble labels have slightly higher JSDs than the expert pairwise distance. For IPC=10, the JSD values fall into the same distribution. The raw JSD comparison further suggests that BPTT generated labels recovers a softmax distribution that is very similar to ensemble labels.

<span id="page-17-1"></span>Similar to standard distillation, BPTT might suffer from the same scaling problem. In other words, it yields suboptimal performance when distilling on larger IPCs. We suspect that optimization might be the cause behind BPTT learned label has worse performance than the best ensemble-expert labels at IPC=10.

Image /page/17/Figure/6 description: The image contains two histograms side-by-side. The left histogram is titled "JSD Expert epoch 13" and the right histogram is titled "JSD Expert epoch 25". Both histograms have "JSD" on the x-axis, ranging from 0.0 to 0.6, and "Count" on the y-axis. The left histogram's y-axis ranges from 0 to 60, while the right histogram's y-axis ranges from 0 to 800. The legend for both histograms indicates "Expert at Epoch 13" or "Expert at Epoch 25" (depending on the plot), "Expert i / Expert j" (represented by yellow and red outlines), and "BPTT / Ensemble" (represented by blue bars). In the left plot, the blue bars (BPTT/Ensemble) peak around 0.3, with counts in the 40s. The yellow and red outlines are spread out, with peaks around 0.2 and 0.3. In the right plot, the blue bars (BPTT/Ensemble) peak around 0.25, with counts around 650. The yellow and red outlines are more spread out, with peaks around 0.25 and 0.35.

Figure 12: JSD between BPTT-learned labels and expert-ensemble labels JSDs between BPTT and expert-ensemble labels fall into the same distribution as JSDs between labels generated by two random experts.
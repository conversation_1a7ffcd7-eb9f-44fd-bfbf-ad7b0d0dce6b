<span id="page-0-1"></span>

# Dataset Distillation: A Comprehensive Review

Ruonan Yu<sup>∗</sup> , <PERSON><PERSON><sup>∗</sup> , <PERSON><PERSON><PERSON>†

**Abstract**—Recent success of deep learning is largely attributed to the sheer amount of data used for training deep neural networks. Despite the unprecedented success, the massive data, unfortunately, significantly increases the burden on storage and transmission and further gives rise to a cumbersome model training process. Besides, relying on the raw data for training *per se* yields concerns about privacy and copyright. To alleviate these shortcomings, dataset distillation (DD), also known as dataset condensation (DC), was introduced and has recently attracted much research attention in the community. Given an original dataset, DD aims to derive a much smaller dataset containing synthetic samples, based on which the trained models yield performance comparable with those trained on the original dataset. In this paper, we give a comprehensive review and summary of recent advances in DD and its application. We first introduce the task formally and propose an overall algorithmic framework followed by all existing DD methods. Next, we provide a systematic taxonomy of current methodologies in this area, and discuss their theoretical interconnections. We also present current challenges in DD through extensive experiments and envision possible directions for future works.

✦

**Index Terms**—Dataset Distillation, Dataset Condensation, Data Compression, Efficient Learning

## **1 INTRODUCTION**

**D** EEP learning has gained tremendous success during the past few years in various fields, such as computer vision [\[1\]](#page-15-0), natural language processing [\[2\]](#page-15-1), and speech EEP learning has gained tremendous success during the past few years in various fields, such as comrecognition [\[3\]](#page-15-2). The groundbreaking deep models, such as AlexNet [\[1\]](#page-15-0) in 2012, ResNet [\[4\]](#page-15-3) in 2016, Bert [\[2\]](#page-15-1) in 2018, alongside the more recent ViT  $[5]$ , CLIP  $[6]$ , and DALLE [\[7\]](#page-16-1), all rely on large-scale datasets for training. However, it takes a great effort to deal with such a sheer amount of data for collecting, storing, transmitting, pre-processing, among others. Moreover, training over massive datasets typically requires enormous computation costs, and sometimes thousands of GPU hours to achieve satisfactory performance, which, in turn, incommodes many applications that rely on training over datasets multiple times, such as hyperparameter optimization  $[8]$ ,  $[9]$ ,  $[10]$  and neural architecture search  $[11]$ ,  $[12]$ ,  $[13]$ . Even worse, information and data are growing explosively in the real world: on the one hand, only training on new-coming data is prone to the catastrophic forgetting problem  $[14]$ ,  $[15]$ , which hurts the performance significantly; on the other hand, storing all historical data is highly cumbersome, if not infeasible at all. In summary, there are contradictions between the demands for highaccuracy models and the limited computation and storage resources. In order to solve the aforementioned problem, one natural idea is to compress original datasets into smaller ones and only store useful information for target tasks, which alleviates the burden on storage while preserving the model performance.

A relatively straightforward method to obtain such smaller datasets is to select the most representative or valuable samples from original datasets so that models trained on these subsets can achieve as good performance as the original ones. This line of method is known as core-set or instance selection. Although effective, such heuristic selection-

- ∗*: Equal Contribution.*
- †*: Corresponding Author (<EMAIL>).*

Image /page/0/Figure/11 description: This diagram illustrates the process of dataset distillation. A large dataset, denoted by T, is distilled into a smaller dataset S, containing M images, where N is much greater than M. The distilled dataset S is then used to train a model, which achieves similar test performance to a model trained on the original large dataset T. The process is visualized with cylinders representing datasets, a grid of images for the original dataset, a smaller grid for the distilled dataset, and traffic light icons indicating model training and performance.

<span id="page-0-0"></span>Fig. 1. An overview for dataset distillation. Dataset distillation aims to generate a small informative dataset such that the models trained on these samples have similar test performance to those trained on the original dataset.

based methods discard a large fraction of training samples directly, dismissing their contribution to training results and often resulting in sub-optimal performance. Moreover, it inevitably raises concerns about privacy and copyright, if datasets containing raw samples are published and accessed directly without copyright or privacy protection [\[16\]](#page-16-10), [\[17\]](#page-16-11).

To overcome the privacy and copyright issues, another line of work that focuses on generating new training data for compression has been proposed, known as dataset distillation (DD) or dataset condensation (DC). We show the overall pipeline of DD in Fig. [1.](#page-0-0) Unlike the core-set fashion of directly selecting informative training samples, methods along this line, which this paper will review, aim at synthesizing original datasets into a limited number of samples such that they are learned or optimized to represent the knowledge of original datasets. It was first introduced and studied by Wang *et al.* [\[18\]](#page-16-12), where an instructive method is proposed to update synthetic samples iteratively, so that models trained on these samples can perform well on the real ones. This seminal work has attracted many follow-

<sup>•</sup> *Authors are with the National University of Singapore.*

<span id="page-1-1"></span>up studies in recent years. On the one hand, considerable progress has been made in improving the performance of DD by a series of methods [\[19\]](#page-16-13), [\[20\]](#page-16-14), [\[21\]](#page-16-15), [\[22\]](#page-16-16), [\[23\]](#page-16-17), [\[24\]](#page-16-18), and the real-world performances of models trained on synthetic datasets are indeed approaching those trained on original ones as much as possible. On the other hand, a variety of works have extended the application of DD into a variety of research fields, such as continual learning [\[25\]](#page-16-19), [\[26\]](#page-16-20), [\[27\]](#page-16-21), [\[28\]](#page-16-22), [\[29\]](#page-16-23) and federated learning [\[30\]](#page-16-24), [\[31\]](#page-16-25), [\[32\]](#page-16-26), [\[33\]](#page-16-27), [\[34\]](#page-16-28), [\[35\]](#page-16-29), [\[36\]](#page-16-30).

This paper aims to provide an overview of current research in dataset distillation. Specifically, the contributions are listed as follows:

- We explore and summarize existing works in dataset distillation and its application comprehensively.
- We provide a systematic classification of current methods in DD. Categorized by the optimization objective, there are three mainstream solutions: performance matching, parameter matching, and distribution matching. Their relationship will also be discussed.
- By abstracting all the key components in DD, we construct an overall algorithmic framework followed by all existing DD methods.
- We discuss current challenges in DD and envision possible future directions for improvements.

The rest of the article is organized as follows. We will begin with discussions of closely-related works for DD in Section [2,](#page-1-0) including core-set methods, hyper-parameter optimization, and generative models. Then in Section [3,](#page-2-0) we first define the dataset distillation problem formally, along with a general framework of the dataset distillation method. Based on this, various methods and their relationship will be systematically introduced. Section [4](#page-9-0) will introduce applications of DD in various research fields. Section [5](#page-12-0) will evaluate the performance of representation DD methods. Section [6](#page-13-0) will discuss the current challenges in the field and propose some possible directions for future works, and Section [7](#page-15-5) will conclude the paper.

<span id="page-1-0"></span>

## **2 RELATED WORKS**

Dataset distillation (DD) aims distilling knowledge of the dataset into some synthetic data while preserving the performance of the models trained on it. Its motivations, objectives, and solutions are closely related to some existing fields, like knowledge distillation, core-set selection, generative modeling, and hyperparameter optimization. Their relationship with dataset distillation will be discussed in this section.

### **2.1 Knowledge Distillation**

Knowledge distillation (KD)  $[37]$ ,  $[38]$ ,  $[39]$ ,  $[40]$  aims to transfer knowledge from a large teacher network to a smaller student network, such that the student network can preserve the performance of the teacher with reduced computational overhead. The seminal work by Hinton *et al.* [\[37\]](#page-16-31) leads the student to mimic the outputs of the teacher, which can represent knowledge acquired by the teacher network. Afterward, improvements of KD have focused on four aspects: representations of knowledge, teacherstudent architectures, distillation algorithms, and distillation schemes. First, knowledge can be represented by model response/output  $[37]$ ,  $[41]$ , features  $[38]$ ,  $[42]$ ,  $[43]$ , and re-lation [\[44\]](#page-16-38), [\[45\]](#page-16-39), [\[46\]](#page-16-40). Second, teacher-student architectures refer to the network architectures of teacher and student models, which determines the quality of knowledge acquisition and distillation from teacher to student  $[40]$ . Third, distillation algorithms determine the ways of knowledge transfer. A simple and typical way is to match the knowledge captured by the teacher and student models directly [\[37\]](#page-16-31), [\[38\]](#page-16-32). Beyond that, many different algorithms are proposed to handle more complex settings, such as adversarial distillation  $[47]$ , attention-based distillation  $[39]$ , and data-free distillation [\[48\]](#page-16-42), [\[49\]](#page-16-43). Finally, distillation schemes control training configurations of teacher and student, and there are offline-  $[37]$ ,  $[38]$ , online-  $[50]$ , and self-distillation  $[51]$ . As for application, KD is widely used in ensemble learning [\[52\]](#page-17-2) and model compression  $[38]$ ,  $[53]$ ,  $[54]$ .

The concept of DD is inspired by KD [\[18\]](#page-16-12). Specifically, DD aims at a lightweight dataset, while KD aims at a lightweight model. In this view, DD and KD are only conceptually related but technically orthogonal. It is worth noting that, similar to DD, recent data-free KD methods [\[48\]](#page-16-42), [\[49\]](#page-16-43), [\[55\]](#page-17-5) are also concerned with the generation of synthetic training samples since original training datasets are unavailable. Their differences are two-fold. On the one hand, datafree KD takes a teacher model as input, while DD takes an original dataset as input. On the other hand, data-free KD aims at a student model with performance similar to the teacher. In order for a satisfactory student, it generally relies on a large number of synthetic samples. For DD, it aims at a smaller dataset, whose size is pre-defined and typically small due to limited storage budgets.

### **2.2 Core-set or Instance Selection**

core-set or instance selection is a classic selection-based method to reduce the size of the training set. It only preserves a subset of the original training dataset containing only valuable or representative samples, such that the models trained on it can achieve similar performance to those trained on the original. Since simply searching for the subset with the best performance is NP-Hard, current core-set methods select samples mainly based on some heuristic strategies. Some early methods generally expect a consistent data distribution between core-sets and the original ones [\[56\]](#page-17-6), [\[57\]](#page-17-7), [\[58\]](#page-17-8), [\[59\]](#page-17-9). For example, Herding [\[56\]](#page-17-6), [\[57\]](#page-17-7), one of the classic core-set methods, aims to minimize the distance in feature space between centers of the selected subset and the original dataset by incrementally and greedily adding one sample each time. In recent years, more strategies beyond matching data distribution have been proposed. For instance, bi-level optimization-based methods are proposed and applied in fields of continual learning [\[60\]](#page-17-10), semi-supervised learning  $[61]$ , and active learning  $[62]$ . For another example, Craig *et al.* [\[63\]](#page-17-13) propose to find the core-set with the minimal difference between gradients produced by the core-set and the original core-set with respective to the same neural network.

Aiming to synthesize condensed samples, many objectives in DD shares similar spirits as core-set techniques. As <span id="page-2-2"></span>a matter of fact, on the functional level, any valid objective in core-set selection is also applicable in DD, such as those matching distribution, bi-level optimization, and matching gradient-based methods. However, core-set and DD have distinct differences. On the one hand, core-set is based on selected samples from original datasets, while DD is based on synthesizing new data. In other words, the former contains raw samples in real datasets, while the latter does not require synthetic samples to look real, potentially providing better privacy protection. On the other hand, due to the NPhard nature, core-set methods typically rely on heuristics criteria or greedy strategies to achieve a trade-off between efficiency and performance. Thus, they are more prone to sub-optimal results compared with DD, where all samples in condensed datasets are learnable.

### **2.3 Generative Models**

Generative models are capable of generating new data by learning the latent distribution of a given dataset and sampling from it. The primary target is to generate realistic data, like images, text, and video, which can fool human beings. DD is related to this field in two aspects. For one thing, one classic optimization objective in DD is to match the distribution of synthetic datasets and original ones, which is conceptually similar to generative modeling, *e.g.*, generative adversarial network (GAN) [\[64\]](#page-17-14), [\[65\]](#page-17-15) and vari-ational autoencoders (VAE) [\[66\]](#page-17-16). Nevertheless, generative models try to synthesize realistic images, while DD cares about generating informative images that can boost training efficiency and save storage costs.

For another, generative models can also be effective in DD. In DD, synthetic samples are not learned directly in some works. Instead, they are produced by some generative models. For example, IT-GAN [\[67\]](#page-17-17) adopts conditional GANs [\[65\]](#page-17-15) and GANs inversion [\[68\]](#page-17-18), [\[69\]](#page-17-19) for synthetic data generation so that it only needs to update latent codes rather than directly optimizing image pixels; KFS [\[70\]](#page-17-20) uses latent codes together with several decoders to output synthetic data. This usage of generative models is known as a kind of synthetic dataset parameterization, whose motivation is that latent codes provide a more compact representation of knowledge than raw samples and thus improve storage efficiency.

#### **2.4 Hyperparameter Optimization**

The performance of machine learning models is typically sensitive to the choice of hyperparameters  $[9]$ ,  $[10]$ . Researches in hyperparameter optimization aim to automatically tune hyperparameters to yield optimal performance for machine learning models, eliminating the tedious work of manual adjustment and thus improving efficiency and performance. Hyperparameter optimization has a rich history. Early works, like [\[71\]](#page-17-21) and [\[72\]](#page-17-22), are gradient-free modelbased methods, choosing hyperparameters to optimize the validation loss after fully training the model. However, the efficiency of these methods is limited when dealing with tens of hyperparameters [\[9\]](#page-16-3). Thus, gradient-based optimization methods  $[9]$ ,  $[10]$ ,  $[73]$ ,  $[74]$  are proposed and tremendously succeed in scaling up, which open up a new way of optimizing high-dimensional hyperparameters. Introducing gradients into hyperparameter optimization problems opens up a new way of optimizing high-dimensional hyperparameters [\[10\]](#page-16-4).

DD can also be converted into a hyperparameter optimization problem if each sample in synthetic datasets is regarded as a high-dimensional hyperparameter. In this spirit, the seminal algorithm proposed by Wang *et al.* [\[18\]](#page-16-12) optimizes the loss on real data for models trained by synthetic datasets with gradient decent, shares essentially the same idea with gradient-based hyperparameter optimization. However, the focus and final goals of DD and hyperparameter optimization are different. The former focus more on getting synthetic datasets to improve the efficiency of the training process, while the latter tries tuning hyperparameter to optimize model performance. Given such orthogonal objectives, other streams of DD methods, like parameter matching and distribution matching, can hardly be related to hyperparameter optimization.

<span id="page-2-0"></span>

## **3 DATASET DISTILLATION METHODS**

Dataset distillation (DD), also known as dataset condensation (DC), is first formally proposed by Wang *et al.* [\[18\]](#page-16-12). The target is to extract knowledge from a large-scale dataset and build a much smaller synthetic dataset, such that models trained on it have comparable performance to those trained on the original dataset. This section first provides a comprehensive definition of dataset distillation and summarizes a general workflow for DD. Then, we categorize current DD methods by different optimization objectives: performance matching, parameter matching, and distribution matching. Algorithms associated with these objectives will be illustrated in detail, and their potential relationships will also be discussed. Some works also focus on synthetic data parameterization and label distillation, which will be introduced in the following parts.

### **3.1 Definition of Dataset Distillation**

The canonical dataset distillation problem involves learning a small set of synthetic data from an original large-scale dataset so that models trained on the synthetic dataset can perform comparably to those trained on the original. Given a real dataset consisting of  $|T|$  pairs of training images and corresponding labels, denoted as  $\mathcal{T} = (X_t, Y_t)$ , where  $X_t \in$  $\mathbb{R}^{N \times d}$ , N is the number of real samples, d is the number of features,  $Y_t \in \mathbb{R}^{N \times C}$ , and C is the number of output entries, the synthetic dataset is denoted as  $S = (X_s, Y_s)$ , where  $X_s \in \mathbb{R}^{M \times D}$ , M is the number of synthetic samples,  $Y_s \in \mathbb{R}^{M \times C}$ ,  $M \ll N$ , and D is the number of features for each sample. For the typical image classification tasks, D is height  $\times$  width  $\times$  channels, and y is a one-hot vector whose dimension  $C$  is the number of classes. Formally, we formulate the problem as the following:

<span id="page-2-1"></span>
$$
S = \underset{S}{\arg\min} \mathcal{L}(S, \mathcal{T}), \tag{1}
$$

where  $\mathcal L$  is some objective for dataset distillation, which will be elaborated in the following contents.

<span id="page-3-3"></span><span id="page-3-0"></span>

| <b>Algorithm 1:</b> Dataset Distillation Framework |                                                         |
|----------------------------------------------------|---------------------------------------------------------|
| <b>Input:</b> Original dataset $\mathcal T$        |                                                         |
| <b>Output:</b> Synthetic dataset $S$               |                                                         |
| Initialize $S$                                     | $\triangleright$ Random, real, or core-set              |
| while not converge do                              |                                                         |
| Get a network $\theta$                             | $\triangleright$ Random or from some cache              |
| Update $\theta$ and cache it if necessary          |                                                         |
|                                                    | $\triangleright$ Via S or $\mathcal T$ , for some steps |
| Update S via $\mathcal L(S, \mathcal T)$           |                                                         |
|                                                    | $\triangleright$ PerM, ParM, DisM, or their variants    |
| end                                                |                                                         |
| return $S$                                         |                                                         |

### **3.2 General Workflow of Dataset Distillation**

In this section, we illustrate the general workflow for current DD methods. Given that the primary goal of DD is to derive a smaller synthetic dataset given a real dataset such that neural networks trained on the synthetic data can have comparable performance with those trained on the real one, the objective function  $\mathcal L$  in Eq. [1](#page-2-1) would rely on some neural networks. Therefore, two key steps in current DD methods are to train neural networks and compute  $\mathcal L$  via these networks. They perform alternately in an iterative loop to optimize synthetic datasets  $S$ , which formulates a general workflow for DD as shown in Alg. [1.](#page-3-0)

Firstly,  $S$  is initialized before the optimization loop, which may have crucial impacts on the convergence and final performance of condensation methods [\[75\]](#page-17-25). Typically, the synthetic dataset  $S$  is usually initialized in two ways: randomly initialization *e.g.*, from Gaussian noise, and randomly selected real samples from the original dataset  $\mathcal{T}$ . The latter is adopted in most DD methods currently. Moreover, some coreset methods, *e.g.*, K-Center [\[76\]](#page-17-26), can also be applied to initialize the synthetic dataset [\[75\]](#page-17-25).

In the iterative loop for synthetic dataset optimization, neural networks and synthetic data are updated alternately. Usually, to avoid overfitting problems and make the output synthetic dataset more generalizable, the network  $\theta$ will be periodically fetched at the beginning of the loop. The networks can be randomly initialized or loaded from some cached checkpoints. For the former, common methods for initializing neural networks are applicable, *e.g.*, Kaiming [\[77\]](#page-17-27), Xavier [\[78\]](#page-17-28), and normal distribution. For the latter, cached checkpoints can be training snapshots, *i.e.*, updated networks in previous iterations [\[19\]](#page-16-13), [\[22\]](#page-16-16), [\[23\]](#page-16-17), [\[79\]](#page-17-29), [\[80\]](#page-17-30), or pretrained checkpoints for different epochs on the original dataset [\[81\]](#page-17-31). The fetched network is updated via  $S$  [\[22\]](#page-16-16), [\[23\]](#page-16-17) or  $\mathcal{T}$  [\[82\]](#page-17-32) for some steps if needed. Then, the synthetic dataset is updated through some dataset distillation objective  $\mathcal L$  based on the network, which will be introduced in detail in Section [3.3.](#page-3-1) Here, the order of network update and synthetic dataset update may be various in some works [\[19\]](#page-16-13), [\[22\]](#page-16-16), [\[23\]](#page-16-17), [\[79\]](#page-17-29), [\[80\]](#page-17-30), [\[82\]](#page-17-32). For details on updating networks and synthetic datasets, please refer to Fig. [5.](#page-7-0)

<span id="page-3-1"></span>

### **3.3 Optimization Objectives in DD**

To obtain synthetic datasets that are valid to replace original ones in downstream training, different works in DD propose different optimization objectives, denoted as  $\mathcal L$  in Eq. [1.](#page-2-1) This part will introduce three mainstream solutions: performance matching, parameter matching, and distribution matching. We will also reveal the relationships between them.

#### *3.3.1 Performance Matching*

**Meta Learning Based Methods.** Performance matching was first proposed in the seminal work by Wang *et al.* [\[18\]](#page-16-12). This approach aims to optimize a synthetic dataset such that neural networks trained on it could have the lowest loss on the original dataset, and thus the performance of models trained by synthetic and real datasets is matched:

$$
\mathcal{L}(\mathcal{S}, \mathcal{T}) = \mathbb{E}_{\theta^{(0)} \sim \Theta} [l(\mathcal{T}; \theta^{(T)})],
$$
  
$$
\theta^{(t)} = \theta^{(t-1)} - \eta \nabla l(\mathcal{S}; \theta^{(t-1)}),
$$
 (2)

<span id="page-3-2"></span>where  $\Theta$  is the distribution for initialization of network parameters, l is the loss function to train networks, *e.g.*, cross-entropy loss for classification, T denotes the number of inner iterations, and  $\eta$  is the learning rate for inner loop. The objective of performance matching indicates a bilevel optimization algorithm: in inner loops, weights of a differentiable model with parameter  $\theta$  are updated with  $\mathcal S$ via gradient decent, and the recursive computation graph is cached; in outer loops, models trained after inner loops are validated on  $\mathcal T$  and the validation loss is backpropagated through the unrolled computation graph to  $S$ . DD methods based on such an objective are similar to bi-level meta-learning [\[83\]](#page-17-33) and gradient-based hyperparameter optimization techniques [\[10\]](#page-16-4). The intuition is shown in Fig. [2.](#page-4-0)

The following work by Deng *et al.* [\[84\]](#page-17-34) finds that equipping the update of parameter  $\theta$  for the inner loop with momentum  $m$  can improve performance significantly. This alternative update function can be written as:

$$
m^{(0)} = 0,
$$
  
\n
$$
m^{(t)} = \beta m^{(t-1)} + \nabla l(\mathcal{S}; \theta^{(t-1)}),
$$
  
\n
$$
\theta^t = \theta_{t-1} - \eta m^{(t)},
$$
\n(3)

where  $\beta$  is a hyperparameter indicating the momentum rate.

**Kernel Ridge Regression Based Methods.** The above meta-learning-based method involves bi-level optimization to compute the gradient of validation loss *w.r.t* synthetic datasets by backpropagating through the entire training graph. In this way, outer optimization steps are computationally expensive, and the GPU memory required is proportional to the number of inner loops. Thus, the number of inner loops is limited, which results in insufficient inner optimization and bottlenecks the performance [\[22\]](#page-16-16). It is also inconvenient for this routine to be scaled up to large models. There is a class of methods tackling this problem [\[19\]](#page-16-13), [\[20\]](#page-16-14),  $[21]$ ,  $[85]$  based on kernel ridge regression (KRR), which performs convex optimization and results in a closed-form solution for the linear model which avoids extensive innerloop training. Projecting samples into a high-dimensional feature space with a non-linear neural network  $f$  parameterized by  $\theta$ , where  $\theta$  is sampled from some distribution  $\Theta$ ,

<span id="page-4-4"></span>Image /page/4/Figure/1 description: This is a diagram illustrating a meta-learning process. It shows a sequence of three gray traffic light-like structures, labeled \(\\theta\_s^{(0)}\), \(\\theta\_s^{(1)}\), and \(\\theta\_s^{(2)}\), representing model parameters at different stages. Solid black arrows indicate a progression from one stage to the next. Above the first two stages, yellow cylinders labeled \(S\) are depicted, with dashed red arrows pointing from the \(S\) cylinders to the corresponding \(\\theta\_s\) stages, indicating a 'Meta Train' process. A blue cylinder labeled \(T\) is positioned above the third stage, with a dashed red arrow pointing from it to the \(\\theta\_s^{(2)}\), labeled 'Meta Test'. A final solid black arrow extends from the third stage, labeled \(\\mathcal{L}\_{cls}\), representing a classification loss.

<span id="page-4-0"></span>Fig. 2. Meta learning based performance matching. The bi-level learning framework aims to optimize the meta test loss on the real dataset  $\mathcal T$ , for the model meta-trained on the synthetic dataset  $S$ .

the performance matching metric can be represented as:

<span id="page-4-1"></span>
$$
\mathcal{L}(S, T) = \mathbb{E}_{\theta \sim \Theta} [||Y_t - f_{\theta}(X_t) W_{S, \theta}^*||^2],
$$
  

$$
W_{S, \theta}^* = \underset{W_{S, \theta}}{\arg hinspace \min} \{||Y_s - f_{\theta}(X_s) W_{S, \theta}||^2 + \lambda ||W_{S, \theta}||^2\} \qquad (4)
$$
  

$$
= f_{\theta}(X_s)^T (f_{\theta}(X_s) f_{\theta}(X_s)^T + \lambda I)^{-1} Y_s.
$$

Here  $\lambda$  is a small number for regularization. Rewriting Eq. [4](#page-4-1) in kernel version, we have:

$$
\mathcal{L}(\mathcal{S}, \mathcal{T}) = \mathbb{E}_{\theta \sim \Theta} [||Y_t - K_{X_t X_s}^{\theta} (K_{X_s X_s}^{\theta} + \lambda I)^{-1} Y_s||^2], \quad (5)
$$

where  $K_{X_1X_2}^{\theta} = f_{\theta}(X_1) f_{\theta}(X_2)^T$ . Nguyen *et al.* [\[20\]](#page-16-14), [\[21\]](#page-16-15) propose to perform KRR with the neural tangent kernel (NTK) instead of training neural networks for multiple steps to learn synthetic datasets, based on the property that KRR for NTK approximates the training of the corresponding wide neural network [\[86\]](#page-17-36), [\[87\]](#page-17-37), [\[88\]](#page-17-38), [\[89\]](#page-17-39).

To alleviate the high complexity of computing NTK, Loo *et al.* [\[85\]](#page-17-35) propose RFAD, based on Empirical Neural Network Gaussian Process (NNGP) kernel [\[90\]](#page-17-40), [\[91\]](#page-17-41) alternatively. They also adopt platt scaling [\[92\]](#page-17-42) by applying cross-entropy loss to labels of real data instead of mean square error, which further improves the performance and is demonstrated to be more suitable for classification tasks.

Concurrently, FRePo [\[19\]](#page-16-13) decomposes a neural network into a feature extractor and a linear classifier, and considers θ and  $W_{\mathcal{S}, \theta}$  in Eq. [4](#page-4-1) as parameters of the feature extractor and the linear classifier respectively. Instead of pursuing a fully-optimized network in an entire inner training loop, it only obtains optimal parameters for the linear classifier via Eq. [4,](#page-4-1) and the feature extractor is trained on the current synthetic dataset, which results in a decomposed two-phase algorithm. Formally, its optimization objective can be written as:

$$
\mathcal{L}(\mathcal{S}, \mathcal{T}) = \mathbb{E}_{\theta^{(0)} \sim \Theta} [\sum_{t=0}^{T} ||Y_t - K_{X_t X_s}^{\theta^{(t)}} (K_{X_s X_s}^{\theta^{(t)}} + \lambda I)^{-1} Y_s||^2],
$$
  
$$
\theta_{\mathcal{S}}^{(t)} = \theta_{\mathcal{S}}^{(t-1)} - \eta \nabla l(\mathcal{S}; \theta_{\mathcal{S}}^{(t-1)}).
$$
 (6)

For FRePo, updates of synthetic data and networks are decomposed, unlike the meta-learning-based method in Eq. [2,](#page-3-2) which requires backpropagation through multiple updates.

#### *3.3.2 Parameter Matching*

The approach of matching parameters of neural networks in DD is first proposed by Zhao *et al.* [\[22\]](#page-16-16); and is extended by a series of following works  $[79]$ ,  $[80]$ ,  $[81]$ ,  $[85]$ . Unlike performance matching, which optimizes the performance

of networks trained on synthetic datasets, the key idea of parameter matching is to train the same network using synthetic datasets and original datasets for some steps, respectively, and encourage the consistency of their trained neural parameters. According to the number of training steps using  $S$  and  $T$ , parameter matching methods can be further divided into two streams: single-step parameter matching and multi-step parameter matching.

**Single-Step Parameter Matching.** In single-step parameter matching, as shown in Fig.  $3(a)$  $3(a)$ , a network is updated using  $S$  and  $T$  for only 1 step, respectively, and their resultant gradients with respective to  $\theta$  are encouraged to be consistent, which is also known as gradient matching. It is first proposed by Zhao *et al.* [\[22\]](#page-16-16) and is extended by a series of following works [\[79\]](#page-17-29), [\[80\]](#page-17-30), [\[82\]](#page-17-32), [\[93\]](#page-18-0). After each step of updating synthetic data, the network used for computing gradients is trained on  $S$  for  $T$  steps. In this case, the objective function can be formalized as follows:

$$
\mathcal{L}(\mathcal{S}, \mathcal{T}) = \mathbb{E}_{\theta^{(0)} \sim \Theta} \left[ \sum_{t=0}^{T} \mathcal{D}(\mathcal{S}, \mathcal{T}; \theta^{(t)}) \right],
$$
  
\n
$$
\theta^{(t)} = \theta^{(t-1)} - \eta \nabla l(\mathcal{S}; \theta^{(t-1)}),
$$
\n(7)

where metric  $D$  measures the distance between gradients  $\nabla l(S; \theta^{(t)})$  and  $\nabla l(\mathcal{T}; \theta^{(t)})$ . Since only a single-step gradient is necessary, and updates of synthetic data and networks are decomposed, this approach is memory-efficient compared with meta-learning-based performance matching. Particularly, in image classification tasks, when updating the synthetic dataset S, Zhao *et al.* [\[22\]](#page-16-16) sample each synthetic and real batch pair  $S_c$  and  $T_c$  from S and T respectively containing samples from the  $c$ -th class, and each class of synthetic data is updated separately in each iteration:

$$
D(S, T; \theta) = \sum_{c=0}^{C-1} d(\nabla l(S_c; \theta), \nabla l(T_c; \theta))
$$
 (8)
$$
d(A, B) = \sum_{i=1}^{L} \sum_{j=1}^{J_i} (1 - \frac{A_j^{(i)} \cdot B_j^{(i)}}{\|A_j^{(i)}\| \|B_j^{(i)}\|})
$$

<span id="page-4-2"></span>where  $C$  is the total number of classes,  $L$  is the number of layers in neural networks,  $i$  is the layer index,  $J_i$  is the number of output channels for the  $i$ -th layer, and  $j$  is the channel index. As shown in Eq. [8,](#page-4-2) the original idea proposed by Zhao *et al.* [\[22\]](#page-16-16) adopts the negative cosine similarity to evaluate the distance between two gradients. It also preserves the layer-wise and channel-wise structure of the network to get an effective distance measurement.

Nevertheless, this method has some limitations, *e.g.*, the distance metric between two gradients considers each class independently and ignores relationships underlain for different classes. Thus, class-discriminative features are largely neglected. To remedy this issue, Lee *et al.* [\[79\]](#page-17-29) propose a new distance metric for gradient matching considering classdiscriminative features:

$$
\mathcal{D}(\mathcal{S}, \mathcal{T}; \theta) = d(\frac{1}{C} \sum_{c=0}^{C-1} \nabla l(\mathcal{S}_c; \theta), \frac{1}{C} \sum_{c=0}^{C-1} \nabla l(\mathcal{T}_c; \theta)).
$$
 (9)

<span id="page-4-3"></span>Compared with Eq. [8,](#page-4-2) the position of summation over all classes is different.

Similar insight is also discussed in Jiang *et al.* [\[80\]](#page-17-30), which combines the two formulation in Eq. [8](#page-4-2) and Eq. [9](#page-4-3) with a

<span id="page-5-2"></span>Image /page/5/Figure/1 description: The image displays a two-part diagram illustrating a machine learning training process. Part (a) shows two datasets, labeled S and T. Dataset S is trained with parameters denoted as \(\\theta\_s^{(0)}\\) and \(\\theta\_s^{(1)}\\) which are represented by traffic light icons. Dataset T is trained with parameters \(\\theta\_t^{(0)}\\) and \(\\theta\_t^{(1)}\\) also represented by traffic light icons. A consistency loss, \(D\_{grad}\\) is calculated between \(\\theta\_s^{(1)}\\) and \(\\theta\_t^{(1)}\\) and influences the training of \(\\theta\_s^{(1)}\\) and \(\\theta\_t^{(1)}\\) from \(\\theta\_s^{(0)}\\) and \(\\theta\_t^{(0)}\\) respectively. Part (b) shows a sequential training process for dataset S with parameters \(\\theta\_s^{(1)}\\) through \(\\theta\_s^{(T\_s)}\\) and for dataset T with parameters \(\\theta\_T^{(1)}\\) through \(\\theta\_T^{(T\_t)}\\) which are represented by circles and traffic light icons respectively. A consistency loss, \(D\_{param}\\) is calculated between \(\\theta\_s^{(T\_s)}\\) and \(\\theta\_T^{(T\_t)}\\) and influences the overall parameter update.

Fig. 3. (a) Single-step parameter matching. (b) Multi-step parameter matching. They optimize the consistency of trained model parameters using real data and synthetic data. For single-step parameter matching, it is equivalent to matching gradients. For multi-step parameter matching, it is also known as matching training trajectories.

hyperparameter  $\lambda$  for their balance:

$$
D(S, T; \theta) = \sum_{c=0}^{C-1} d(\nabla l(S_c; \theta), \nabla l(T_c; \theta)) + \lambda d(\frac{1}{C} \sum_{c=0}^{C-1} \nabla l(S_c; \theta), \frac{1}{C} \sum_{c=0}^{C-1} \nabla l(T_c; \theta))
$$
(10)

Moreover, the work also argues that the cosine distancebased function  $d$  in Eq.  $8$  only considers the angle between the gradients. Since synthetic datasets  $S$  are small in general, it is prone to over-fitting and bad generalization problems. Also, it is found that gradient norms of  $S$  degrade quickly and will stick in a local minimum, *i.e.*,  $\nabla l(\mathcal{S}; \theta) = 0$  after only a few gradient descent steps. In this case, it is not very sensible to match the angle. Thus, Jiang *et al.* [\[80\]](#page-17-30) further consider the magnitude of the gradient vectors by adding the Euclidean distance between them:

$$
d(\mathbf{A}, \mathbf{B}) = \sum_{j=1}^{J} (1 - \frac{\mathbf{A_j} \cdot \mathbf{B_j}}{\|\mathbf{A_j}\| \|\mathbf{B_j}\|} + \|\mathbf{A_j} - \mathbf{B_j}\|). \tag{11}
$$

Beyond formulations of the distance between two groups of gradients, there are also following works focusing on more effective strategies to update networks. The original work by Zhao *et al.* [\[22\]](#page-16-16) adopts synthetic dataset  $S$  to update network parameters  $\theta$ , while S and  $\theta$  are strongly bonded in the optimization process, leading to a chicken-egg problem. Also, the size of the synthetic dataset is tiny, the overfitting problem easily occurs in the early stage of the training, and network gradients vanish quickly. To solve these problems, Kim *et al.* [\[82\]](#page-17-32) proposes to train networks on the real dataset  $\mathcal T$ . In this way, the overfitting problem can be alleviated due to the large size of  $\mathcal T$ , and network parameters are independent of S.

Besides, existing gradient matching methods [\[22\]](#page-16-16), [\[23\]](#page-16-17), [\[79\]](#page-17-29), [\[80\]](#page-17-30), [\[82\]](#page-17-32) are computationally expensive to gain synthetic datasets with satisfactory generalizability, as thousands of differently initialized networks are required to optimize S. To accelerate dataset distillation, Zhang *et al.* [\[93\]](#page-18-0) propose model augmentations, which adopt earlystage models to form a candidate model pool and apply weight perturbation on selected early-stage models from the pool during dataset distillation to increase model diversity. This way, synthetic datasets with comparable performance <span id="page-5-0"></span>can be obtained with significantly fewer random networks and optimization iterations.

**Multi-Step Parameter Matching.** For single-step parameter matching, since only single-step gradient is matched, errors may be accumulated in evaluation where models are updated by synthetic data for multiple steps. To solve this problem, Cazenavette *et al.* [\[81\]](#page-17-31) propose a multi-step parameter matching approach known as matching training trajectory (MTT). In this method,  $\theta$  will be initialized and sampled from checkpoints of training trajectories on original datasets. As shown in Fig. [3\(](#page-5-0)b), starting from  $\theta^{(0)}$ , the algorithm trains the model on synthetic datasets for  $T_s$  steps and the original dataset for  $T_t$  steps, respectively, where  $T_s$  and  $T_t$  are hyperparameters, and tries to minimize the distance of the endings of these two trajectories, *i.e.*,  $\theta_S^{(T_s)}$ and  $\theta_{\mathcal{T}}^{(T_t)}$ :

$$
\mathcal{L}(\mathcal{S}, \mathcal{T}) = \mathbb{E}_{\theta^{(0)} \sim \Theta} [\mathcal{D}(\theta_{\mathcal{S}}^{(T_s)}, \theta_{\mathcal{T}}^{(T_t)})],
$$
  
\n
$$
\theta_{\mathcal{S}}^{(t)} = \theta_{\mathcal{S}}^{(t-1)} - \eta \nabla l(\mathcal{S}; \theta_{\mathcal{S}}^{(t-1)}),
$$
  
\n
$$
\theta_{\mathcal{T}}^{(t)} = \theta_{\mathcal{T}}^{(t-1)} - \eta \nabla l(\mathcal{T}; \theta_{\mathcal{T}}^{(t-1)}),
$$
\n(12)

<span id="page-5-1"></span>where  $\Theta$  is the distribution for cached teacher trajectories, l is the loss function for training networks, and  $D$  is defined as follows:

$$
\mathcal{D}(\theta_{\mathcal{S}}^{(T_s)}, \theta_{\mathcal{T}}^{(T_t)}) = \frac{\|\theta_{\mathcal{S}}^{(T_s)} - \theta_{\mathcal{T}}^{(T_t)}\rangle\|^2}{\|\theta_{\mathcal{T}}^{(T_t)} - \theta^{(0)}\|^2}.
$$
 (13)

Note that the total loss is normalized by the distance between the starting point  $\theta^{(0)}$  and the expert endpoint  $\theta_{\mathcal{T}}^{(T_t)}$ . This normalization helps get a strong signal where the expert does not move as much at later training epochs and self-calibrates the magnitude difference across neurons and layers. It is demonstrated that this multi-step parameter matching strategy yields better performance than the singlestep counterpart.

As a following work along this routine, Li *et al.* [\[94\]](#page-18-1) find that a few parameters are difficult to match in dataset distillation for multi-step parameter matching, which negatively affects the condensation performance. To remedy this problem and generate a more robust synthetic dataset, they adopt parameter pruning, removing the difficult-to-match parameters when the similarity of the model parameters trained on synthetic datasets  $\theta_{\mathcal{S}}^{(T_s)}$  and on real datasets

<span id="page-6-1"></span>Image /page/6/Figure/1 description: This diagram illustrates a machine learning framework. A source dataset 'S' and a target dataset 'T' are shown. Data from 'S' is processed to generate synthetic embeddings, and data from 'T' is processed to generate real embeddings. Both synthetic and real embeddings are then used to derive synthetic and real statistics, respectively. A consistency module links the synthetic and real statistics, and a discriminator 'D\_dis' is involved in the process, likely for adversarial training or evaluation. Red dashed arrows indicate feedback or adversarial signals, while yellow and blue solid arrows represent data flow or processing steps.

<span id="page-6-0"></span>Fig. 4. Distribution matching. It matches statistics of features in some networks for synthetic and real datasets.

 $\theta_{\mathcal{T}}^{(T_t)}$  is less than a threshold after updating networks in each dataset distillation iteration. The synthetic dataset is updated by optimizing the objective function  $\mathcal L$  calculated with pruned parameters.

Optimizing the multi-step parameter matching objective defined in Eq. [12](#page-5-1) involves backpropagation through the unrolled computational graph for  $T_s$  network updates, which triggers the problem of memory efficiency, similar to metalearning-based methods. To solve this problem in MTT, Cui *et al.* [\[95\]](#page-18-2) propose TESLA with constant memory complexity *w.r.t.* update steps. The key step in TESLA lies in that, in the *i*-th step,  $0 \le i < T_s$ , it calculates the gradient for the loss function  $l(\theta_{\mathcal{S}}^{(i)}; \mathcal{S}^{(i)})$  against both current network parameters  $\theta_{\mathcal{S}}^{(i)}$  and current synthetic samples  $\mathcal{S}^{(i)}$ . The gradient for each synthetic sample is accumulated. Thus, during backpropagation, there is no need to record the computational graph for  $T_s$  training steps. Also, it is worth noting that adopting soft labels for synthetic data is crucial when condensing datasets with many classes.

Moreover, Du *et al.* [\[96\]](#page-18-3) find that MTT results in accumulated trajectory error for downstream training and propose FTD. To be specific, in the objective of MTT shown in Eq. [12,](#page-5-1)  $\theta^{(0)}$  may be sampled from some training checkpoints of latter epochs on the original dataset, and the trajectory matching error on these checkpoints is minimized. However, it is hard to guarantee that desired parameters seen in dataset distillation optimization can be faithfully achieved when trained using the synthetic dataset, which causes the accumulated error, especially for latter epochs. To alleviate this problem, FTD adds flat regularization when training with original datasets, which results in flat training trajectories and makes targeting networks more robust to weight perturbation. Thus, it yields lower accumulated error compared with the baseline.

#### *3.3.3 Distribution Matching*

The distribution matching approach aims to obtain synthetic data whose distribution can approximate that of real data. Instead of matching training effects, *e.g.*, the performance of models trained on  $S$ , distribution matching directly optimizes the distance between the two distributions using some metrics, *e.g.*, Maximum Mean Discrepancy (MMD) leveraged in Zhao *et al.* [\[24\]](#page-16-18). The intuition is shown in Fig. [4.](#page-6-0) Since directly estimating the real data distribution can be expensive and inaccurate as images are high-dimensional data, distribution matching adopts a set of embedding functions, *i.e.*, neural networks, each providing a partial interpretation of the input and their combination providing a comprehensive interpretation, to approximate MMD. Here, we denote the parametric function as  $f_{\theta}$ , and distribution matching is defined as:

$$
\mathcal{L}(\mathcal{S}, \mathcal{T}) = \mathbb{E}_{\theta \in \Theta}[\mathcal{D}(\mathcal{S}, \mathcal{T}; \theta)],\tag{14}
$$

where  $\Theta$  is a specific distribution for random neural network initialization,  $X_{s,c}$  and  $X_{t,c}$  denote samples from the  $c$ -th class in synthetic and real datasets respectively, and  $D$ is some metric measuring the distance between two distributions. DM by Zhao *et al.* [\[24\]](#page-16-18) adopts classifier networks without the last linear layer as embedding functions. The center, *i.e.*, mean vector, of the output embeddings for each class of synthetic and real datasets are encouraged to be close. Formally,  $D$  is defined as:

$$
\mathcal{D}(\mathcal{S}, \mathcal{T}; \theta) = \sum_{c=0}^{C-1} \|\mu_{\theta, s, c} - \mu_{\theta, t, c}\|^2,
$$

$$
\mu_{\theta, s, c} = \frac{1}{M_c} \sum_{j=1}^{M_c} f_{\theta}^{(i)}(X_{s, c}^{(j)}), \quad \mu_{\theta, t, c} = \frac{1}{N_c} \sum_{j=1}^{N_c} f_{\theta}^{(i)}(X_{t, c}^{(j)}) \quad (15)
$$

where  $M_c$  and  $N_c$  are the number of samples for the c-th class in synthetic and real datasets respectively, and  $j$  is the sample index.

Instead of matching features before the last linear layer, Wang *et al.* [\[97\]](#page-18-4) propose CAFE, which forces statistics of features for synthetic and real samples extracted by each network layer except the final one to be consistent. The distance function is as follows:

$$
\mathcal{D}(\mathcal{S}, \mathcal{T}; \theta) = \sum_{c=0}^{C-1} \sum_{i=1}^{L-1} \|\mu_{\theta,s,c}^{(i)} - \mu_{\theta,t,c}^{(i)}\|^2, \tag{16}
$$

where  $L$  is the number of layers in a neural network, and  $i$  is the layer index. Also, to explicitly learn discriminative synthetic images, CAFE adopts the discrimination loss  $\mathcal{D}r$ . Here, the center of synthetic samples for each class is used as a classifier to classify real samples. The discriminative loss for the c-th class tries to maximize the probability of this class for the classifier:

Dr(S, T; \theta) = -\sum\_{c=0}^{C-1} \sum\_{j=1}^{N\_c} \log p(c|X\_{t,c}^{(j)}, S, \theta),

$$
p(c|X_{t,c}^{(j)}, S, \theta) = \frac{\exp\{\mu_{\theta,s,c}^{(L-1)^T} f_{\theta}^{(L-1)}(X_{t,c}^{(j)})\}}{\sum_{c'=0}^{C-1} \exp\{\mu_{\theta,s,c'}^{(L-1)^T} f_{\theta}^{(L-1)}(X_{t,c}^{(j)})\}}.
$$
(17)

Meanwhile, networks are updated alternately with synthetic data by a dedicated schedule. Overall, the objective function can be written as:

$$
\mathcal{L}(\mathcal{S}, \mathcal{T}) = \mathbb{E}_{\theta^{(0)} \in \Theta}[\sum_{t=0}^{T} \{ \mathcal{D}(\mathcal{S}, \mathcal{T}; \theta^{(t)}) + \lambda \mathcal{D}r(\mathcal{S}, \mathcal{T}; \theta^{(t)}) \}],
$$
\n
$$
\theta^{(t)} = \theta^{(t-1)} - \eta \nabla l(\mathcal{S}; \theta^{(t-1)}),
$$
\n(18)

where  $\lambda$  is a hyperparameter for balance.

#### *3.3.4 Connections between Objectives in DD*

In this part, we show connections between the above three optimization objectives and give proof that they are essentially related. For simplicity, we assume that only the last linear layer of neural networks is considered for updating

<span id="page-7-2"></span>Image /page/7/Figure/1 description: This is a flowchart illustrating the field of Dataset Distillation. The top level, 'Dataset Distillation', branches into three 'Major Classes': 'Performance Matching', 'Distribution Matching', and 'Parameter Matching'. Each of these major classes further divides into 'Minor Classes'. 'Performance Matching' has 'Meta Learning' and 'KRR'. 'Distribution Matching' has 'Single Layer' and 'Multi Layer'. 'Parameter Matching' has 'Single-Step' and 'Multi-Step'. Below the 'Minor Classes' are the 'Methods', which are specific techniques within each minor class. For example, under 'Meta Learning' are 'DD' and 'AddMem'. Under 'KRR' are 'KIP', 'RFAD', and 'FRePo'. Under 'Single Layer' are 'DM' and 'IT-GAN'. Under 'Multi Layer' are 'KFS' and 'CAFE'. Under 'Single-Step' are 'DC', 'DSA', 'DCC', 'EGM', 'IDC', and 'EDD'. Under 'Multi-Step' are 'MTT', 'HaBa', 'DDPP', 'FTD', and 'TESLA'. The lowest level, 'Network Update Fashions', shows how these methods are applied, with labels like 'Use S (Unroll)', 'Do Not Update', 'Use T', 'Use T and Model Aug.', and 'Use T (Cached), S (Unroll), and Pruning'. A legend on the left indicates different types of data representation used in these methods, including 'TEXT: Learnable Label', 'TEXT: Soft Label', 'TEXT: Fixed Label', and various colored boxes representing 'Latent Code-Linear Decoder', 'Latent Code-GAN', 'Linear Transformations', 'Latent Code-Non Linear Decoder', 'DSA Data Augmentation', and 'Raw Parameterization'.

Fig. 5. Taxonomy of existing DD methods. A DD method can be analyzed from 4 aspects: optimization objective, fashion of updating networks, synthetic data parameterization, and fashion of learning labels. A tabular version can be found in the supplement.

synthetic data, while previous network layers, *i.e.*, the feature extractor  $f_{\theta}$ , are fixed. The parameter of the linear layer is  $W \in \mathbb{R}^{F \times C}$ , where F is the dimension for of feature from  $f_{\theta}$ , and C is the number of classes. Least square function is adopted for model optimization, as the case in FRePo [\[19\]](#page-16-13).

**Performance Matching v.s. Optimal Parameter Matching.** For performance matching, the goal is that optimize the performance on  $T$  for models trained on  $S$ , as shown in Eq. [2.](#page-3-2) Here, for simplicity, we ignore the regularization term and assume  $\lambda = 0$ , given that  $\lambda$  is generally a small constant for numerical stability. Then, the performance matching objective in Eq. [2](#page-3-2) for some given  $\theta$  can be written as:

$$
\mathcal{L}_{perfM} = ||Y_t - f_{\theta}(X_t) f_{\theta}(X_s)^T (f_{\theta}(X_s) f_{\theta}(X_s)^T)^{-1} Y_s||^2.
$$
 (19)

Under this circumstance, we have the following proposition:

**Proposition 1.** *Performance matching objective for kernel ridge regression models is equivalent to optimal parameter matching objective, or infinity-step parameter matching.*

*Proof.* Given that KRR is a convex optimization problem and the optimal analytical solution can be achieved by a sufficient number of training steps, the optimal parameter matching, in this case, is essentially infinity-step parameter matching, which can be written as:

$$
\mathcal{L}_{paraM} = \|W_{\mathcal{S},\theta}^{*} - W_{\mathcal{T},\theta}^{*}\|^{2},
$$

$$
W_{\mathcal{S},\theta}^{*} = \underset{W_{\mathcal{S},\theta}}{\arg\min} \{ \|Y_{s} - f_{\theta}(X_{s})W_{\mathcal{S},\theta} \|^{2} \}
$$

$$
= f_{\theta}(X_{s})^{T} (f_{\theta}(X_{s})f_{\theta}(X_{s})^{T})^{-1} Y_{s},
$$

$$
W_{\mathcal{T},\theta}^{*} = \underset{W_{\mathcal{T},\theta}}{\arg\min} \{ \|Y_{t} - f_{\theta}(X_{t})W_{\mathcal{T},\theta} \|^{2} \}
$$

$$
= (f_{\theta}(X_{t})^{T} f_{\theta}(X_{t}))^{-1} f_{\theta}(X_{t})^{T} Y_{t}.
$$
(20)

Here, we denote  $(f_{\theta}(X_t)^T f_{\theta}(X_t))^{-1} f_{\theta}(X_t)^T$  as  $\mathcal M.$  Then we get:

<span id="page-7-1"></span>
$$
\|\mathcal{M}\|^2 \mathcal{L}_{perfM} = \mathcal{L}_{param}.
$$
 (21)

As  $M$  is a constant matrix and not related to the optimization problem *w.r.t* S, concluded from Eq. [21,](#page-7-1) performance matching is equivalent to optimal parameter matching under this circumstance. П

**Single-Step Parameter Matching v.s. First-Moment Distribution Matching.** In this part, we reveal connections be-

<span id="page-7-0"></span>tween single-step parameter matching, *i.e.*, gradient matching, and distribution matching based on the first moment, *e.g.*, [\[24\]](#page-16-18). Some insights are adapted from Zhao *et al.* [\[24\]](#page-16-18). We have the following proposition:

**Proposition 2.** *First-order distribution matching objective is approximately equal to gradient matching of each class for kernel ridge regression models following a random feature extractor.*

*Proof.* Denote the parameter of the final linear layer as  $W =$  $[w_0, w_1, \ldots, w_{C-1}]$ , where  $w_c \in \mathbb{R}^F$  is the weight vector connected to the c-th class. And for each training sample  $(x_i)$  with class label c, we get the loss function as follows:

$$
l = ||y_i - f_{\theta}(x_j)W||^2 = \sum_{c'=0}^{C-1} ((\mathbb{1}_{c'=c} - f_{\theta}(x_j)w_{c'})^2, \qquad (22)
$$

where  $y_i$  is a one-hot vector with the c-th entry 1 and others  $0$ , and  $\mathbbm{1}$  is the indicator function. Then, the partial derivative of the classification loss on j-th sample *w.r.t* the  $c'$ -th neuron is:

$$
g_{c',j} = (p_{c',j} - \mathbb{1}_{c'=c}) \cdot f_{\theta}(x_j)^T, \tag{23}
$$

where  $p_{c',j} = f_{\theta}(x_j)w_{c'}$ . For seminal works on gradient matching [\[22\]](#page-16-16) and distribution matching [\[24\]](#page-16-18), objective functions are calculated for each class separately. For the  $c$ -th class, the gradient of the  $c'$ -th weight vector  $w_{c'}$  is:

$$
g_{c'} = \frac{1}{N_c} \sum_{j=1}^{N_c} g_{c',j} = \frac{1}{N_c} \sum_{j=1}^{N_c} (p_{c',j} - \mathbb{1}_{c'=c}) \cdot f_{\theta}(x_j)^T
$$
  
$$
\approx (q - \mathbb{1}_{c'=c}) \frac{1}{N_c} \sum_{j=1}^{N_c} f_{\theta}(x_j)^T
$$
(24)

where the approximation is valid since the network parameterized by  $\theta$  is randomly initialized, and the prediction for each class is near uniform, denoted as  $q$ . The term  $\frac{1}{N_c} \sum_{j=1}^{N_c} f_{\theta}(x_j)^T$  is actually the first moment of the output distribution for samples of the c-th class, *i.e.*,  $\mu_{\theta,s,c}$  for S and  $\mu_{\theta,t,c}$  for T. Thus, first-moment distribution matching is approximately equivalent to gradient matching for each class in this case.  $\Box$ 

**Single-Step Parameter Matching v.s. Second-Order Distribution Matching.** The relationship between gradient matching and first-order distribution matching discussed

<span id="page-8-3"></span>Image /page/8/Figure/1 description: The image depicts a diagram illustrating a process involving storage, a variable z, a function g\_phi(z), a storage unit S, an input, and a component labeled "Some Grad ...". A solid arrow goes from z to S, labeled with g\_phi(z). Another solid arrow goes from S to the input. A dashed red arrow goes from the input back to S, and another dashed red arrow goes from the input towards the "Some Grad ..." component. The storage is represented by a gray oval containing the variable z, which is a yellow circle. The storage unit S is depicted as a yellow cylinder. The input is shown as a black arrow pointing to a gray rectangular component with three green circles, representing a traffic light.

<span id="page-8-1"></span>Fig. 6. Synthetic data parameterization. It is a sub-area in DD and orthogonal to the objectives for dataset distillation, since the process for generating  $S$  is differentiable and gradients for updating  $S$  can be further backpropagated to synthetic parameters.

above relies on an important assumption that the outputs of networks are uniformly distributed, which requires fully random neural networks without any learning bias. Here we show in the following proposition that without such a strong assumption, we can still bridge gradient matching and distribution matching.

**Proposition 3.** *Second-order distribution matching objective optimizes an upper bound of gradient matching objective for kernel ridge regression models.*

*Proof.* The gradient matching objective in this case satisfies:

<span id="page-8-0"></span>The following equations are derived:

$$
\mathcal{L} = \left\|\frac{1}{|\mathcal{S}|} \nabla l(f_{\theta}(X_s), W) - \frac{1}{|\mathcal{T}|} \nabla l(f_{\theta}(X_t), W)\right\|^2
$$

$$
= \left\|\frac{1}{|\mathcal{S}|} f_{\theta}(X_s)^T (f_{\theta}(X_s)W - Y_s) - \frac{1}{|\mathcal{T}|} f_{\theta}(X_t)^T (f_{\theta}(X_t)W - Y_t) \right\|^2
$$

$$
= \left\|\left(\frac{1}{|\mathcal{S}|} f_{\theta}(X_s)^T f_{\theta}(X_s) - \frac{1}{|\mathcal{T}|} f_{\theta}(X_t)^T f_{\theta}(X_t)\right)W - \left(\frac{1}{|\mathcal{S}|} f_{\theta}(X_s)^T Y_s - \frac{1}{|\mathcal{T}|} f_{\theta}(X_t)^T Y_t\right) \right\|^2 \quad (25)
$$

$$
\le \left\|\frac{1}{|\mathcal{S}|} f_{\theta}(X_s)^T f_{\theta}(X_s) - \frac{1}{|\mathcal{T}|} f_{\theta}(X_t)^T f_{\theta}(X_t)\right\|^2 \|W\|^2 + \left\|\frac{1}{|\mathcal{S}|} f_{\theta}(X_s)^T Y_s - \frac{1}{|\mathcal{T}|} f_{\theta}(X_t)^T Y_t \right\|^2.
$$

The  $r.h.s$  of the inequality of Eq.  $25$  measures the difference between first and second-order statistics of synthetic and real data, *i.e.*, mean, and correlation. In this way, distribution matching essentially optimizes an upper bound of gradient matching.

### **3.4 Synthetic Data Parameterization**

One of the essential goals of dataset distillation is to synthesize informative datasets to improve training efficiency, given a limited storage budget. In other words, as for the same limited storage, more information on the original dataset is expected to be preserved so that the model trained on condensed datasets can achieve comparable and satisfactory performance. In the image classification task, the typical method of dataset distillation is to distill the information of the dataset into a few synthetic images with the same resolution and number of channels as real images. However, due to the limited storage, information carried by a small number of images is limited. Moreover, with the same format as real data, it is unclear whether synthetic data contain useless or redundant information. Focusing on these concerns and orthogonal to optimization objectives in DD, a series of works propose different ways of synthetic data parameterization. In a general form, for a synthetic dataset, some codes  $z \in \mathcal{Z} \subset \mathbb{R}^{D'}$ ,  $\mathcal{Z} = \{(z_j, y_j)\}|_{j=1}^{|\mathcal{Z}|}$  in a format other than the raw shape are used for storage. And there

is some function  $g_\phi: \mathbb{R}^{D'} \to \mathbb{R}^D$  parameterized by  $\phi$  that maps a code with  $d'$  dimensions to the format of raw images for downstream training. In this case, the synthetic dataset is denoted as:

<span id="page-8-2"></span>
$$
S = (X_s, Y_s), X_s = \{g_{\phi}(z_j)\}_{j=1}^{|\mathcal{S}|}.
$$
 (26)

As shown in Fig. [6,](#page-8-1) when training,  $\phi$  and  $\mathcal Z$  can be updated in an end-to-end fashion, by further backpropagating the gradient for  $S$  to them, since the process for data generation is differentiable.

#### *3.4.1 Differentiable Siamese Augmentation*

Differentiable siamese augmentation (DSA) is a set of augmentation policies designed to improve the data efficiency, including crop  $[1]$ , cutout  $[98]$ , flip, scale, rotate, and color jitters [\[1\]](#page-15-0) operations. It is first applied to the dataset distillation task by Zhao *et al.* [\[23\]](#page-16-17) to increase the data efficiency and thus generalizability. Here,  $g_{\phi}(\cdot)$  is a family of image transformations parameterized with  $\phi \sim \Phi$ , where  $\phi$  is the parameter for data augmentation, and a code  $z$  still holds the same format as a raw image. It is differentiable so that the synthetic data can be optimized via gradient descent. Moreover, the parameter for data augmentation is the same for synthetic and real samples within each iteration to avoid the averaging effect. This technique has been applied in many following DD methods, *e.g.*, DM [\[24\]](#page-16-18), MTT [\[81\]](#page-17-31), TESLA [\[95\]](#page-18-2), *etc*.

#### *3.4.2 Upsampling*

According to experimental observations, Kim *et al.* [\[82\]](#page-17-32) find that the size of the synthetic dataset rather than resolution dominates its performance. Motivated by this, they propose a synthetic data parameterization strategy termed IDC, which uses multi-formation to increase the number of synthetic data under the same storage budget by reducing the data resolution. Intuitively, the format of  $z$  is a downsampled format of a raw image, *e.g.*, 1/2 down-sampling such that the number of synthetic images can become quadrupled, and  $g_{\phi}$  here is a parameter-free up-sampling function, whose computational overhead is negligible.

##### *3.4.3 Generators and Latent Vectors*

A generator is capable of producing data from some latent vectors, which provide a more compact representation of raw data by learning hidden patterns of data formation. Parameterizations based on generators and latent vectors can be regarded as a decomposition of shared knowledge and distinct knowledge across various samples, where generators extract common patterns while different latent vectors store different magnitudes for these patterns. The dimension of latent vectors is typically lower than that of original formats. Thus, given the same storage budget, more synthetic data can be obtained by storing generators and latent vectors instead of raw samples, and more information on original datasets can be preserved, which indicates a more data-efficient parameterization. In this case,  $z$  in Eq. [26](#page-8-2) denotes latent codes and  $g_{\phi}$  denotes the generator parameterized by  $\phi$ .

**GAN.** Zhao *et al.* [\[67\]](#page-17-17) propose to generate informative synthetic samples via GAN. In this work,  $g_{\phi}$  is a pretrained <span id="page-9-1"></span>and fixed GAN generator. Motivated by the observation that latent vectors obtained by GAN inversion are more informative than those sampled randomly for downstream training, the method learns a set of latent vectors initialized with GAN inversion of raw data. In specific, it first initializes the whole latent set  $\mathcal Z$  by GAN inversion [\[68\]](#page-17-18), such that the generated result  $g_{\phi}(z_i)$  with each latent vector  $z_i$  is as close as possible to the corresponding real image in the original training dataset  $\mathcal{T}$ :

$$
\underset{z}{\arg\min} \left\|h(g_{\phi}(z), \omega) - h(x, \omega)\right\|^2 + \lambda \|g_{\phi}(z) - x\|^2, \tag{27}
$$

where  $h(\cdot,\omega)$  is a pre-trained and fixed feature extractor with parameter  $\omega$ , and  $\lambda$  is the weight to balance the two terms. Then in the training phase, the objective function is based on the combination of DM and a regularization term:

$$
\mathcal{L} = (1 - \lambda')\mathcal{L}_{DM} + \lambda' R,
$$

$$
\mathcal{L}_{DM} = \mathbb{E}_{\theta \in \Theta}[\|\frac{1}{N}\sum_{j=1}^{N} f_{\theta}(g_{\phi}(z_j)) - \frac{1}{N}\sum_{j=1}^{N} f_{\theta}(x_j)\|^2],
$$

$$
R = \mathbb{E}_{\theta \in \Theta}[\sum_{j=1}^{N} \|f_{\theta}(g_{\phi}(z_j)) - f_{\theta}(x_j)\|^2], (28)
$$

where the  $x_i$  and  $z_i$  are paired, and  $\lambda'$  is another hyperparameter for balance. The regularization item can better preserve the training information compared to the feature alignment in GAN inversion.

**Addressing Matrices.** Deng *et al.* [\[84\]](#page-17-34) propose compressing the critical information of original datasets into compact addressable memories. Instead of learning each synthetic sample separately, it learns a common memory representation for all classes and samples, which can be accessed through learnable addressing matrices to construct the synthetic dataset. Thus, the size of the synthetic dataset does not necessarily correlate with the number of classes linearly, and the high compression rate achieves better data efficiency. In this case, the shared common memory, or basis, serves as the parameter  $\phi$  for the mapping function g, and the address, or coefficient, serves as the latent code  $z$  for each sample. Specifically, assume that there are  $C$  classes, and we want to retrieve  $R$  samples for each class. Then we have R addressing matrices  $A_j \in \mathbb{R}^{C \times K}$  for  $1 \leq j \leq R$ . K is the number of bases vectors, and the memory  $\phi \in \mathbb{R}^{K \times D}$ , where  $D$  is the dimension for each sample. The  $j$ -th synthetic sample for the  $c$ -th class  $x_{c,j}$  is constructed by the following linear transformation:

$$
z_{c,j} = y_c A_j, \quad x_{c,j} = z_{c,j} \phi,
$$
\n
$$
(29)
$$

where  $y_c$  is the corresponding one-hot vector. For optimization, the method proposes an improved meta learning based performance matching framework with momentum used for inner loops.

**Decoders.** Beyond the linear transformation-based method by Deng *et al.* [\[84\]](#page-17-34), HaBa [\[99\]](#page-18-6) and KFS [\[70\]](#page-17-20) adopt non-linear transformation for the mapping function  $g$ . HaBa uses the multi-step parameter matching method MTT [\[81\]](#page-17-31) for optimization while KFS is based on distribution matching [\[24\]](#page-16-18). Nevertheless, they use similar techniques for synthetic data parameterization. Specifically, there are  $|\mathcal{Z}|$  latent codes (bases) and  $|\Phi|$  neural decoders (hallucinators). Latent codes may include distinct knowledge for different samples,

while decoders contain shared knowledge across all samples. A synthetic sample can be generated by sending the *j*-th latent code  $z_i$  to the k-th decoder parameterized by  $\phi_k$ :

$$
x_{j,k} = g_{\phi_k}(z_j),\tag{30}
$$

where  $z_j \in \mathcal{Z}$ ,  $1 \leq j \leq |\mathcal{Z}|$  and  $\phi_k \in \Phi$ ,  $1 \leq k \leq |\Phi|$ . In other words, different latent codes and decoders are combined interchangeably and arbitrarily, such that the size of the synthetic dataset can reach up to  $|\mathcal{Z}| \times |\Phi|$ , which increases the data efficiency exponentially.

### **3.5 Label Distillation Methods.**

In dataset distillation for image classification tasks, most methods fix the class label  $Y_s$  as a one-hot format and only learn synthetic images  $X_s$ . Some works [\[19\]](#page-16-13), [\[20\]](#page-16-14), [\[84\]](#page-17-34), [\[100\]](#page-18-7) find that making labels learnable can improve performance. Bohdal *et al.* [\[101\]](#page-18-8) reveal that even only learning labels without learning images can achieve satisfactory performance. Moreover, Cui *et al.* [\[95\]](#page-18-2) takes soft labels predicted by a teacher model trained on real datasets, which is demonstrated to be significantly effective when condensing datasets with a large number of classes. Generally, as plugand-play schemes, how to deal with labels in DD is orthogonal to optimization objectives and is compatible with all existing ones.

<span id="page-9-0"></span>

## **4 APPLICATIONS**

Benefiting from the nature of the high compression rate, the research of dataset distillation has led to many successful innovative applications in various fields like continual learning and federated learning, which will be introduced in this section.

### **4.1 Continual Learning**

Continual learning [\[102\]](#page-18-9) aims to remedy the catastrophic forgetting problem caused by the inability of neural networks to learn a stream of tasks in sequence. A widely used strategy is rehearsal, maintaining representative samples that can be re-used to retain knowledge about previous tasks  $[15]$ ,  $[103]$ ,  $[104]$ ,  $[105]$ . In this case, how to preserve as much knowledge of previous data in a buffer space with limited memory becomes an essential point. Benefiting from the high compression rate, the distilled data can capture the essence of the whole dataset, making it an inspiring application in continual learning. Several works [\[19\]](#page-16-13), [\[22\]](#page-16-16), [\[24\]](#page-16-18), [\[25\]](#page-16-19), [\[26\]](#page-16-20) have directly applied dataset distillation techniques to continual learning scenarios. Instead of selecting the representative samples of historical data, they train synthetic datasets for historical data to keep the knowledge of the past task.

Besides, some synthetic dataset parameterization methods are also successfully applied to improve memory efficiency. Kim *et al.* [\[82\]](#page-17-32) use data partition and upsampling to increase the number of training samples under the same storage budget. Deng *et al.* [\[84\]](#page-17-34) propose using addressable memories and addressing matrices to construct synthetic data of historical tasks, where different tasks can share the same memory base so that the memory cost will not increase linearly or positively correlate with the number of past

<span id="page-10-1"></span>tasks. Sangermano *et al.* [\[27\]](#page-16-21) propose obtaining the synthetic dataset by linear weighted combining historical images, and the optimization target is linear coefficients rather than pixels of synthetic data. Wiewel *et al.* [\[28\]](#page-16-22) propose a method that learns a weighted combination of shared components for samples in a specific class rather than directly learning the synthetic dataset. Masarczyk *et al.* [\[29\]](#page-16-23) use the generative model to create the synthetic dataset and form a sequence of tasks to train the model, and the parameters of the generative model are fine-tuned by the evaluation loss of the learner on real data.

### **4.2 Federated Learning**

Federated learning (FL) [\[106\]](#page-18-13), [\[107\]](#page-18-14), [\[108\]](#page-18-15) develops a privacy-preserving distributed model training scheme such that multiple clients collaboratively learn a model without sharing their private data. A standard way of federated learning to keep data private is to transmit model updates instead of private user data. However, this may cause an increased communication cost, for the size of model updates may be very large and make it burdensome for clients when uploading frequently.

To remedy this problem and improve communication efficiency, some research [\[30\]](#page-16-24), [\[31\]](#page-16-25), [\[32\]](#page-16-26), [\[33\]](#page-16-27), [\[34\]](#page-16-28), [\[35\]](#page-16-29) on federated learning transmits the locally generated synthetic datasets instead of model updates from clients to the server. The motivation is quite straightforward: the model parameters are usually much larger than a small number of data points which can reduce the costs significantly in upload transmission from clients back to the server, and the synthetic dataset can preserve the essence of the whole original dataset. It is worth noting that label distillation [\[101\]](#page-18-8) is usually considered to prevent synthetic data from representing an actual label for privacy issues [\[30\]](#page-16-24). To further strengthen privacy protection, Zhou et al. [\[31\]](#page-16-25) combine dataset distillation and distributed one-shot learning, such that for every local update step, each synthetic data successively updates the network for one gradient descent step. Thus, synthetic data is closely bonded with one specific network weight, and the eavesdropper cannot reproduce the result with only leaked synthetic data. Xiong *et al.* [\[32\]](#page-16-26) adopt distribution matching [\[24\]](#page-16-18) to generate synthetic data and update synthetic data with the Gaussian mechanism to protect the privacy of local data. Song *et al.* [\[33\]](#page-16-27) apply dataset distillation in one-shot federated learning and propose a novel evaluation metric  $\gamma$ -accuracy gain to tune the importance of accuracy and analyze communication efficiency. Liu *et al.* [\[34\]](#page-16-28) develop two mechanisms for local updates: dynamic weight assignment, when training the synthetic dataset, assigning dynamic weights to each sample based on its training loss; meta-knowledge sharing, sharing local synthetic dataset among clients to mitigate heterogeneous data distributions among clients.

In addition to reducing the communication cost of each round, dataset distillation can also be applied to reduce the communication epochs to reduce the total communication consumption. Pi *et al.* [\[36\]](#page-16-30) propose to use fewer rounds of standard federated learning to generate the synthetic dataset on the server by applying multi-step parameter matching on global model trajectories and then using the generated synthetic dataset to complete subsequent training.

#### **4.3 Neural Architecture Search**

Neural architecture search (NAS) [\[13\]](#page-16-7), [\[109\]](#page-18-16) aims to discover the optimal structure of a neural network from some search space. It typically requires expensive training of numerous candidate neural network architectures on full datasets.

In this case, datasets by DD, benefiting from its small size, can be served as proxy sets to accelerate model evaluation in neural architecture search, which is proved feasible in some works [\[22\]](#page-16-16), [\[23\]](#page-16-17), [\[24\]](#page-16-18). Besides, Such *et al.* [\[11\]](#page-16-5) propose a method named generative teaching networks combining a generative model and a learner to create the synthetic dataset, which is learner-agnostic, *i.e.*, generalizes to different latent learner architectures and initializations.

<span id="page-10-0"></span>

#### **4.4 Privacy, Security and Robustness**

Machine learning suffers from a wide range of privacy attacks [\[110\]](#page-18-17), *e.g.*, model inversion attack [\[111\]](#page-18-18), membership inference attack  $[112]$ , property inference attack  $[113]$ . Dataset distillation provides a perspective to start from the data alone, protect the privacy and improve model robustness.

There are some straightforward applications of dataset distillation to protect the privacy of the dataset, for synthetic data may look unreal to recognize the actual label, and it is hard to reproduce the result with synthetic data without knowing the architecture and initialization of the target model. For example, remote training [\[114\]](#page-18-21) transmits synthetic datasets with distilled labels instead of the original dataset to protect data privacy.

More for data privacy protection, Dong *et al.* [\[16\]](#page-16-10) point out that dataset distillation can offer privacy protection to prevent unintentional data leakage. They emerge dataset distillation techniques into the privacy community and give the theoretical analysis of the connection between dataset distillation and differential privacy. Also, they empirically validate that the synthetic dataset is irreversible to original data in terms of similarity metrics of  $L_2$  and LPIPS [\[115\]](#page-18-22). Chen *et al.* [\[116\]](#page-18-23) apply dataset distillation to generate highdimensional data with differential privacy guarantees for private data sharing with lower memory and computation costs.

As for the robustness of model improvement, Tsilivis *et al.* [\[117\]](#page-18-24) proposes that dataset distillation can provide a new perspective for solving robust optimization. They combine the adversarial training and KIP [\[20\]](#page-16-14), [\[21\]](#page-16-15) to optimize the training data instead of model parameters with high efficiency. It is beneficial that the optimized data can be deployed with other models and give favorable transferability. Also, it enjoys satisfactory robustness against PGD attacks [\[118\]](#page-18-25). Huang *et al.* [\[119\]](#page-18-26) study the robust dataset learning problem such that the network trained on the dataset is adversarially robust. They formulate robust dataset learning as a min-max, tri-level optimization problem where the robust error of adversarial data on the robust data parameterized model is minimized.

Furthermore, traditional backdoor attacks, which inject triggers into the original data and use the malicious data to train the model, cannot work on the distilled synthetic dataset as it is too small for injection, and inspection can quickly mitigate such attacks. However, Liu *et al.* [\[120\]](#page-18-27)

<span id="page-11-0"></span>propose a new backdoor attack method, DOORPING, which attacks during the dataset distillation process rather than afterward model training. Specifically, DOORPING continuously optimizes the trigger in every epoch before updating the synthetic dataset to ensure that the trigger is preserved in the synthetic dataset. Experiment results show that nine defense mechanisms for the backdoor attacks at three levels, *i.e.*, model-level [\[121\]](#page-18-28), [\[122\]](#page-18-29), [\[123\]](#page-18-30), input-level [\[124\]](#page-18-31), [\[125\]](#page-18-32), [\[126\]](#page-18-33) and dataset-level [\[127\]](#page-18-34), [\[128\]](#page-18-35), [\[129\]](#page-18-36), are unable to mitigate the attacks effectively.

### **4.5 Graph Neural Network**

Graph neural networks (GNNs) [\[130\]](#page-18-37), [\[131\]](#page-18-38), [\[132\]](#page-18-39), [\[133\]](#page-18-40) are developed to analyze graph-structured data, representing many real-world data such as social networks [\[134\]](#page-18-41) and chemical molecules [\[135\]](#page-18-42). Despite their effectiveness, similar to traditional deep neural networks, GNNs suffer from datahungry problems: large-scale datasets are required to learn powerful representations. Motivated by dataset distillation, graph-structured data can also be distilled into a synthetic and simplified one to improve the training efficiency while preserving the performance.

Jin *et al.* [\[136\]](#page-18-43) apply gradient matching [\[22\]](#page-16-16) in graph distillation, distilling both graph structure and node attributes. It is worth noting that the graph structure and node features may have connections, such that node features can represent the graph structure matrix  $[137]$ . In this case, the optimization target is only node features, which avoids the quadratic increase of computation complexity with the number of synthetic graph nodes. Based on that, Jin *et al.* [\[138\]](#page-18-45) propose a more efficient graph condensation method via one-step gradient matching without training networks. To handle the discrete data, they formulate the graph structure as a probabilistic model that can be learned in a differentiable manner. Liu *et al.* [\[139\]](#page-19-0) adopt distribution matching [\[24\]](#page-16-18) in graph condensation, synthesizing a small graph that shares a similar distribution of receptive fields with the original graph.

#### **4.6 Recommender System**

Recommender systems [\[140\]](#page-19-1), [\[141\]](#page-19-2) aim to give users personalized recommendations, content, or services through a large volume of dynamically generated information, such as item features, user past behaviors, and similar decisions made by other users. The recommender system research also faces a series of challenges on heavy computational overheads caused by training models on massive datasets, which can involve billions of user-item interaction logs. At the same time, the security of user data should also be considered [\[142\]](#page-19-3). In this case, Sachdeva *et al.* [\[143\]](#page-19-4) develop a data distillation framework to distill the collaborative filtering data into small, high-fidelity data summaries. They take inspiration from KIP  $[20]$ ,  $[21]$  to build an efficient framework. To deal with the discrete nature of the recommendation problem, instead of directly optimizing the interaction matrix, this method learns a continuous prior for each user-item pair, and the interaction matrix is sampled from the learned distribution.

### **4.7 Text Classification**

Text classification [\[144\]](#page-19-5) is one of the classical problems in natural language processing, which aims to assign labels or tags to textual units. The latest developed language models [\[145\]](#page-19-6), which require massive datasets, are burdensome to train, fine-tune and use. Motivated by dataset distillation, it is possible to generate a much smaller dataset to cover the knowledge in the original dataset and provide a more efficient dataset to train models. Nevertheless, the discrete nature of text data makes dataset distillation a challenging problem. In this case, Li *et al.* [\[146\]](#page-19-7) propose to generate words embeddings instead of actual text words to form synthetic datasets and adopt performance matching [\[18\]](#page-16-12) as the optimization objective. Moreover, label distillation can also be applied to the text classification task. Sucholutsky *et al.* [\[100\]](#page-18-7) propose to embed text into a continuous space, and then train the embedded text data with the soft-label image distillation method.

### **4.8 Knowledge Distillation**

Most of the existing KD studies [\[37\]](#page-16-31), [\[38\]](#page-16-32), [\[39\]](#page-16-33), [\[147\]](#page-19-8), [\[148\]](#page-19-9) transfer the knowledge hints of the whole sample space during the training process while neglecting the variation of student's capacity over the training progress. Such redundant knowledge causes two issues: 1. more computational costs, *e.g.*, memory footprint and GPU time; 2. poor performance, distracting the attention of the student model from the proper knowledge and weakening the learning efficiency. In this case, Li *et al.* [\[149\]](#page-19-10) propose a novel KD paradigm of knowledge condensation, which performs knowledge condensation and model distillation alternately. A sample is condensed according to its value determined by the feedback from the student model iteratively.

#### **4.9 Medical**

Sharing of medical datasets is crucial to establish the crosshospital flow of medical information and improve the quality of medical services [\[150\]](#page-19-11), *e.g.*, constructing high-accuracy computer-aided diagnosis systems [\[151\]](#page-19-12). However, potential issues on privacy protection [\[152\]](#page-19-13), transmission, and storage costs are unneglectable. To solve this problem, Li *et al.* [\[153\]](#page-19-14), [\[154\]](#page-19-15), [\[155\]](#page-19-16) leverage dataset distillation to extract the essence of a dataset and construct a much smaller anonymous synthetic dataset for data sharing. They handle largesize and high-resolution medical datasets by dividing them into several patches and labeling them into different categories manually. Also, they successfully apply performance matching  $[18]$ , and multi-step parameter matching  $[81]$  ob-jectives, along with the label distillation [\[101\]](#page-18-8) to generate synthetic medical datasets.

### **4.10 Fashion, Art and Design**

The images produced by dataset distillation have a certain degree of visual appreciation. It retains the characteristics and textures of the given category and produces artistic fragments. Cazenavette *et al.* [\[156\]](#page-19-17) propose a method that generates tileable distilled texture, which is aesthetically pleasing to be applied to practical tasks, *e.g.*, clothing. They update the canvas by applying random crops on a

<span id="page-12-1"></span>padded distilled canvas and performing dataset distillation on those crops. For fashion compatibility learning, Chen *et al.* [\[157\]](#page-19-18) propose using designer-generated data to guide outfit compatibility modeling. They extract disentangled features from a set of fashion outfits, generate the fashion graph, and leverage the designer-generated data through a dataset distillation scheme, which benefits the fashion compatibility prediction.

<span id="page-12-0"></span>

## **5 EXPERIMENTS**

In this section, we conduct two sets of quantitative experiments, *i.e.*, performance and training cost evaluation, on representative dataset distillation methods that cover three classes of primary condensation metrics, including DD [\[18\]](#page-16-12), DC [\[22\]](#page-16-16), DSA [\[23\]](#page-16-17), DM [\[24\]](#page-16-18), MTT [\[81\]](#page-17-31), and FRePo [\[19\]](#page-16-13).

### **5.1 Experimental Setup**

The performance of dataset distillation is mainly evaluated on the classification task. We follow typical settings in the area of dataset distillation, which are illustrated below:

**Datasets.** We adopt five datasets, including MNIST [\[158\]](#page-19-19), Fashion-MNIST [\[159\]](#page-19-20), CIFAR-10 [\[160\]](#page-19-21), CIFAR-100 [\[160\]](#page-19-21), and Tiny-ImageNet [\[161\]](#page-19-22). These datasets are widely used as benchmarks in existing dataset distillation works.

**Networks.** We use the default ConvNet [\[1\]](#page-15-0) architecture provided by the authors, which mainly consists of multiple Conv-ReLU-AvgPooling blocks. Also, we evaluate the performance of synthetic datasets across different architectures. The network architectures of evaluation models are as follows: ConvNet [\[1\]](#page-15-0) with no normalization layer, AlexNet [\[1\]](#page-15-0) with no normalization layer and instance normalization layer, ResNet [\[4\]](#page-15-3) with instance normalization layer and batch normalization layer, and VGG [\[162\]](#page-19-23) with instance normalization layer and batch normalization layer.

**Evaluation Protocol.** For performance evaluation, we first generate synthetic datasets through candidate methods and train target networks using these datasets. Then, the performance of trained models is evaluated by the corresponding test set of the original dataset. As for training cost evaluation, all methods are evaluated under full batch training for fair comparisons. Moreover, all methods adopt the default data augmentation strategies the authors provided for distillation performance evaluation. While for generalization evaluation, we adopt the DSA [\[23\]](#page-16-17) data augmentation in the evaluation model training process for fair comparisons. Additionally, we measure the distillation performance of condensed datasets with 1, 10, and 50 images per class (IPC) for different datasets. The crossarchitecture experiments are conducted on CIFAR-10 with 10 IPC. Efficiency evaluation experiments are conducted on the CIFAR-10 dataset with a wide range of IPC.

**Implementation Details.** Here, we implement DC, DSA, DM, MTT, and FRePo by the official code provided by the authors. For DD, we modify the inner loop optimization by adding the momentum term, which is demonstrated to improve the performance substantially [\[84\]](#page-17-34). FRePo is implemented in JAX and PyTorch, respectively, and the rest methods are implemented in PyTorch. For fair comparisons, all efficiency evaluation experiments are conducted on the single A100-SXM4-40GB.

### **5.2 Performance Evaluation**

**Distillation Performance.** We evaluate the distillation performance of different dataset distillation methods on the models with the same architecture as the default training models. The experiment settings are default as the authors provided. The testing results are shown in Table [1.](#page-13-1) Please refer to the supplementary for the performance of all existing DD methods. Due to the limitation of GPU memory, some experimental results for Tiny-ImageNet are missing. As shown in Table [1,](#page-13-1) in most cases, FRePo achieves state-ofthe-art performance, especially for more complex datasets, *e.g.*, CIFAR-10, CIFAR-100, and Tiny-ImageNet. At the same time, MTT often achieves second-best performance. Comparing the performance results of DC and DSA, although dataset augmentation cannot guarantee to benefit the distillation performance, it influences the results significantly in most times. As for DD, it obtains the SOTA performance in the case of MNIST 1 IPC, Fashion-MNIST 1 IPC, and 10 IPC. The performance of DM is often not good as other methods.

**Cross Architecture Generalization.** The transferability of different methods is evaluated on a wide range of network architectures, unseen during training in the case of CIFAR-10 with 10 IPC. From results shown in Table [2,](#page-13-2) the instance normalization layer seems to be a vital ingredient in several methods (DD, DC, DSA, DM, and MTT), which may be harmful to the transferability. The performance degrades significantly for most methods except FRePo when no normalization is adopted (Conv-NN, AlexNet-NN). If the normalization layer is inconsistent with the training architecture, the test results will drop significantly. It suggests that the synthetic dataset generated by those methods encodes the inductive bias of a particular training architecture. As for DD, the distilled data highly rely on the training model, and thus the performance degrades significantly on heterogeneous networks with different normalization layers, *e.g.*, batch normalization. Moreover, Comparing the transferability of DC and DSA, we find that DSA data augmentation can help train models with different architectures, especially those with batch normalization layers.

### **5.3 Training Cost Evaluation**

We measure run-time and peak GPU memory required by selected methods for training cost evaluation. For fair comparisons, in this section, the number of inner loops, if there have any, is based on the experiment settings provided by the author, and evaluation is under full batch training.

**Run-Time Evaluation.** We evaluate the required time for one outer loop and the time to calculate the loss function for a batch of synthetic data, respectively. As for DD and MTT, the process of network updating is unrolled, and thus the required time for the entire outer loop and the loss function is the same. Also, as DM does not update the network, the required time for these two processes is the same. The evaluation results are shown in Fig.  $7(a)(b)$  $7(a)(b)$ . The results show that DD requires a significantly longer run time to update the synthetic dataset for a single iteration, as the gradient computation for the performance matching loss over the synthetic dataset involves bi-level optimization. DC, DSA, and FRePo implemented by PyTorch have similar required run times. Comparing DSA and DC, using DSA

TABLE 1

<span id="page-13-3"></span><span id="page-13-1"></span>Comparison for different dataset distillation methods on the same architecture with training.  $\dagger$  adopt momentum  $[84]$ . Please refer to the supplementary for the performance of all the existing DD methods.

| Dataset       | Img/Cls | Method                   |                |                |                |                |                |
|---------------|---------|--------------------------|----------------|----------------|----------------|----------------|----------------|
|               |         | DD\[\dagger\] [18], [84] | DC [22]        | DSA [23]       | DM [24]        | MTT [81]       | FRePo [19]     |
| MNIST         | 1       | 95.2 $\pm$ 0.3           | 91.7 $\pm$ 0.5 | 88.7 $\pm$ 0.6 | 89.9 $\pm$ 0.8 | 91.4 $\pm$ 0.9 | 93.8 $\pm$ 0.6 |
|               | 10      | 98.0 $\pm$ 0.1           | 97.4 $\pm$ 0.2 | 97.9 $\pm$ 0.1 | 97.6 $\pm$ 0.1 | 97.3 $\pm$ 0.1 | 98.4 $\pm$ 0.1 |
|               | 50      | 98.8 $\pm$ 0.1           | 98.8 $\pm$ 0.2 | 99.2 $\pm$ 0.1 | 98.6 $\pm$ 0.1 | 98.5 $\pm$ 0.1 | 99.2 $\pm$ 0.1 |
| Fashion-MNIST | 1       | 83.4 $\pm$ 0.3           | 70.5 $\pm$ 0.6 | 70.6 $\pm$ 0.6 | 71.5 $\pm$ 0.5 | 75.1 $\pm$ 0.9 | 75.6 $\pm$ 0.5 |
|               | 10      | 87.6 $\pm$ 0.4           | 82.3 $\pm$ 0.4 | 84.8 $\pm$ 0.3 | 83.6 $\pm$ 0.2 | 87.2 $\pm$ 0.3 | 86.2 $\pm$ 0.3 |
|               | 50      | 87.7 $\pm$ 0.3           | 83.6 $\pm$ 0.4 | 88.8 $\pm$ 0.2 | 88.2 $\pm$ 0.1 | 88.3 $\pm$ 0.1 | 89.6 $\pm$ 0.1 |
| CIFAR-10      | 1       | 46.6 $\pm$ 0.6           | 28.3 $\pm$ 0.5 | 28.8 $\pm$ 0.7 | 26.5 $\pm$ 0.4 | 46.3 $\pm$ 0.8 | 46.8 $\pm$ 0.7 |
|               | 10      | 60.2 $\pm$ 0.4           | 44.9 $\pm$ 0.5 | 53.2 $\pm$ 0.8 | 48.9 $\pm$ 0.6 | 65.3 $\pm$ 0.7 | 65.5 $\pm$ 0.6 |
|               | 50      | 65.3 $\pm$ 0.4           | 53.9 $\pm$ 0.5 | 60.6 $\pm$ 0.5 | 63.0 $\pm$ 0.4 | 71.6 $\pm$ 0.2 | 71.7 $\pm$ 0.2 |
| CIFAR-100     | 1       | 19.6 $\pm$ 0.4           | 12.6 $\pm$ 0.4 | 13.9 $\pm$ 0.3 | 11.4 $\pm$ 0.3 | 24.3 $\pm$ 0.3 | 27.2 $\pm$ 0.4 |
|               | 10      | 32.7 $\pm$ 0.4           | 25.4 $\pm$ 0.3 | 32.3 $\pm$ 0.3 | 29.7 $\pm$ 0.3 | 40.1 $\pm$ 0.4 | 41.3 $\pm$ 0.2 |
|               | 50      | 35.0 $\pm$ 0.3           | 29.7 $\pm$ 0.3 | 42.8 $\pm$ 0.4 | 43.6 $\pm$ 0.4 | 47.7 $\pm$ 0.2 | 44.3 $\pm$ 0.2 |
| Tiny-ImageNet | 1       | -                        | 5.3 $\pm$ 0.2  | 6.6 $\pm$ 0.2  | 3.9 $\pm$ 0.2  | 8.8 $\pm$ 0.3  | 15.4 $\pm$ 0.3 |
|               | 10      | -                        | 11.1 $\pm$ 0.3 | 16.3 $\pm$ 0.2 | 13.5 $\pm$ 0.3 | 23.2 $\pm$ 0.2 | 24.9 $\pm$ 0.2 |
|               | 50      | -                        | 11.2 $\pm$ 0.3 | 25.3 $\pm$ 0.2 | 24.1 $\pm$ 0.3 | 28.2 $\pm$ 0.5 | -              |

TARI F 2

<span id="page-13-2"></span>Cross-architecture transfer performance on CIFAR-10 with 10 Img/Cls. ConvNet is the default evaluation model used for each method. NN, IN and BN stand for no normalization, Instance Normalization, and Batch Normalization respectively. † adopt momentum [\[84\]](#page-17-34).

|                | Train Arch | Evaluation Model |                |                |                |                |                |                |                |
|----------------|------------|------------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|                | Train Arch | Conv             | Conv-NN        | AlexNet-NN     | AlexNet-IN     | ResNet18-IN    | ResNet18-BN    | VGG11-IN       | VGG11-BN       |
| DDT [18], [84] | Conv-IN    | $60.2 \pm 0.4$   | $17.8 \pm 2.7$ | $11.4 \pm 0.1$ | $38.5 \pm 1.3$ | $33.9 \pm 1.1$ | $16.0 \pm 1.2$ | $40.0 \pm 1.7$ | $21.2 \pm 2.4$ |
| DC [22]        | Conv-IN    | $44.9 \pm 0.5$   | $31.9 \pm 0.6$ | $27.3 \pm 1.6$ | $45.5 \pm 0.3$ | $43.3 \pm 0.6$ | $32.7 \pm 0.9$ | $43.7 \pm 0.6$ | $37.3 \pm 1.1$ |
| DSA [23]       | Conv-IN    | $53.2 \pm 0.8$   | $36.4 \pm 1.5$ | $34.0 \pm 2.3$ | $45.5 \pm 0.6$ | $42.3 \pm 0.9$ | $34.9 \pm 0.5$ | $43.1 \pm 0.9$ | $40.6 \pm 0.5$ |
| DM [24]        | Conv-IN    | $49.2 \pm 0.8$   | $35.2 \pm 0.5$ | $34.9 \pm 1.1$ | $44.2 \pm 0.9$ | $40.2 \pm 0.8$ | $40.1 \pm 0.8$ | $41.7 \pm 0.7$ | $43.9 \pm 0.4$ |
| MTT [81]       | Conv-IN    | $64.4 \pm 0.9$   | $41.6 \pm 1.3$ | $34.2 \pm 2.6$ | $51.9 \pm 1.3$ | $45.8 \pm 1.2$ | $42.9 \pm 1.5$ | $48.5 \pm 0.8$ | $45.4 \pm 0.9$ |
| FRePo [19]     | Conv-BN    | $65.6 \pm 0.6$   | $65.6 \pm 0.6$ | $58.2 \pm 0.5$ | $39.0 \pm 0.3$ | $47.4 \pm 0.7$ | $53.0 \pm 1.0$ | $35.0 \pm 0.7$ | $56.8 \pm 0.6$ |

augmentation makes the running time slightly longer, but it can significantly improve the performance. When the IPC is small, FRePo (both the JAX and PyTorch versions) is significantly faster than other methods, but as the IPC increases, the PyTorch version is similar to the secondefficient echelon methods, while the JAX version is close to DM. MTT runs the second slowest, but there is no more data to analyze due to out-of-memory.

**Peak GPU Memory Usage.** We evaluate the peak GPU memory usage during the whole dataset distillation process. Here, we record the peak GPU memory in practice and show the results in Fig. [7\(](#page-14-0)c). The results show that DM requires the lowest GPU memory, while MTT requires the most. When IPC is only 50, the operation of MTT encounters out-ofmemory. In addition, with the gradual growth of IPC, JAX version FRePo reveals the advantages of memory efficiency. The DD and PyTorch versions of FRePo require relatively large GPU memory. The former needs to go through the entire training graph during backpropagation, while the latter adopts a wider network. Moreover, there is no significant difference in the GPU memory required by DC and DSA, indicating that DSA augmentation is memory-friendly.

### **5.4 Empirical Studies**

The following conclusions are drawn from the above experiments:

- The performance of DD has significantly improved thanks to the momentum item. However, DD has relatively poor generalizability, and the required runtime and GPU memory are considerable.
- Compared with DC and DSA, adopting DSA can significantly improve the distillation performance and generalizability while not increasing the running time and memory requirement too much.
- DM does not perform as well as other methods. However, DM has relatively good generalizability and significant advantages regarding run time and GPU memory requirements.
- MTT has the overall second-best performance but requires much running time and space due to unrolled gradient computation through backpropagation.
- FRePo achieves SOTA performance when IPC are small, regardless of performance or training cost. However, as IPC increases, FRePo does not have comparable performance.

<span id="page-13-0"></span>

## **6 CHALLENGES AND POSSIBLE IMPROVEMENTS**

The research on dataset distillation has promising prospects, and many algorithms have been applied in various fields. Although existing methods have achieved reasonable performance, there are still some challenges and issues. In this section, we summarise the key challenges and discuss possible directions for improvements.

<span id="page-14-3"></span>Image /page/14/Figure/1 description: This image contains three plots labeled (a), (b), and (c). Plot (a) and (b) show run-time per loop and run-time per step, respectively, on a logarithmic y-axis against "Image per Class" on the x-axis. Plot (c) shows "Peak GPU Memory (GB)" on the y-axis against "Image per Class" on the x-axis. All plots display data for six different methods: DD, DC, DSA, DM, MTT, FRePo-JAX, and FRePo-PyTorch. In plot (a), DD has the highest run-time per loop, increasing from approximately 5 to 100 as Image per Class increases from 0 to 1000. FRePo-PyTorch shows the lowest run-time per loop, increasing from approximately 0.02 to 1.5. In plot (b), DD again has the highest run-time per step, increasing from approximately 0.2 to 100. FRePo-PyTorch has the lowest run-time per step, increasing from approximately 0.005 to 0.2. Plot (c) shows peak GPU memory usage. DD and FRePo-PyTorch show the highest memory usage, both reaching approximately 40 GB at 1000 Image per Class. MTT shows the lowest memory usage, staying around 12 GB. FRePo-JAX shows a memory usage that plateaus at approximately 20 GB after 600 Image per Class.

Fig. 7. (a) Run-time per loop (including time required for updating synthetic data and networks in one loop). (b) Run-time per step (including only time required for updating synthetic data). (c) Peak GPU Memory Usage. All evaluated under full batch training on CIFAR-10.

<span id="page-14-2"></span>

### **6.1 Computational Cost**

The motivation of DD is to train a small informative dataset so that the model training on it can achieve the same performance as training on the original data set, thereby reducing GPU time. However, the problem is that generating the synthetic dataset is typically expensive and that the required time will rapidly increase when distilling large-scale datasets. This high computational cost is mainly caused by backpropagating through unrolled computational graphs for updating synthetic datasets. For example, MTT [\[81\]](#page-17-31) does not scale well in terms of both memory and run time issues, where unrolling 30 steps on CIFAR10 with IPC 50 requires 47GB GPU memory, and is prohibitive in largescale problems like ImageNet-1K [\[163\]](#page-19-24), as shown in Table [3.](#page-15-6)

To tackle this problem, many works have been proposed for different DD frameworks to reduce computational costs. One intuitive way is to avoid the inner-loop network training. As for performance matching, KRR-based methods [\[19\]](#page-16-13), [\[20\]](#page-16-14), [\[21\]](#page-16-15), [\[85\]](#page-17-35), [\[93\]](#page-18-0) are proposed to improve the computational efficiency of DD [\[18\]](#page-16-12), which performs convex optimization and results in a closed-form solution for linear models and avoids extensive inner-loop training. As for distribution matching, DM [\[24\]](#page-16-18) improves efficiency using random networks as the feature extractors without training, which avoids expensive bi-level optimization. However, the high efficiency sacrificed its performance.

As for MTT, Cui *et al.* [\[95\]](#page-18-2) re-organize the computing flow for the gradients, which reduces the memory cost to constant complexity. Nevertheless, it still requires pretraining hundreds of teacher models on original datasets, which affects the time and memory efficiency negatively. Model augmentation methods like Zhang *et al.* [\[93\]](#page-18-0) may potentially be helpful to alleviate the problem.

#### **6.2 Scaling Up**

The existing dataset distillation methods mainly evaluate the performance of the synthetic dataset up to about 50 IPC. In practical applications, it may be necessary to increase the compression ratio to obtain more information on the whole dataset, and the dataset and the network adopted may be larger and more complex. Under such circumstances, most

<span id="page-14-0"></span>Image /page/14/Figure/9 description: This is a line graph showing the testing accuracy (%) on the y-axis against the number of images per class on the x-axis. The graph displays multiple lines representing different methods: Whole, Random, K-Center, DD, DC, DSA, DM, MTT, and FRePo-JAX. The x-axis ranges from 0 to 1000 images per class, and the y-axis ranges from approximately 25% to 85% testing accuracy. All methods show an increasing trend in testing accuracy as the number of images per class increases. The 'Whole' line is a horizontal line at approximately 85% accuracy, indicating a baseline. Other lines show varying performance, with FRePo-JAX and DC generally achieving higher accuracies at lower image counts compared to methods like Random and K-Center.

<span id="page-14-1"></span>Fig. 8. Performance comparison with different compression ratios on CIFAR10 using Random selection, K-Center, DD, DC, DSA, DM, MTT and FRePo-JAX. Results are derived from [\[75\]](#page-17-25) and our experiments.

existing dataset distillation methods cannot meet the demand. In short, the scaling-up problem of dataset distillation is reflected in three aspects: hard to obtain an informative synthetic dataset with a larger IPC; challenging to perform the dataset distillation algorithm on a large-scale dataset such as ImageNet; not easy to adapt to a large, complex model.

**Larger Compression Ratios.** As shown in Fig. [8,](#page-14-1) on the whole, with the increase of IPC, the accuracy of the dataset distillation methods improves more slowly. Moreover, only when it is less than 200 IPC, the methods of dataset distillation are obviously better than selection-based methods. With the further increase of IPC, the margin between the dataset distillation methods and selection-based methods narrows considerably, and finally, the selection-based methods are slightly better than the dataset distillation methods. The above observation shows that as the synthetic dataset increases, the ability of the dataset to compress knowledge of the real dataset does not increase significantly, even though the memory efficiency of some methods can support larger IPC. In other words, many existing dataset distillation methods are not suitable for high compression ratios.

**Larger Original Datasets.** Another scaling-up problem is that many existing dataset distillation methods are challenging to apply to large-scale datasets due to high GPU memory requirements and extensive GPU hours as discussed in Section [6.1.](#page-14-2) As shown in Table [3,](#page-15-6) only a few methods

<span id="page-15-8"></span><span id="page-15-6"></span>TABLE 3 The performance results of different dataset distillation methods on ImageNet-1K. Results are derived from [\[95\]](#page-18-2) and our experiments. † adopt momentum [\[84\]](#page-17-34).

| ImageNet-1K    | IPC         |              |              |              |
|----------------|-------------|--------------|--------------|--------------|
|                | 1           | 2            | 10           | 50           |
| DD  [18], [84] | -           | -            | -            | -            |
| DC [22]        | -           | -            | -            | -            |
| DSA [23]       | -           | -            | -            | -            |
| DM [24]        | $1.5 	 0.1$ | $1.7 	 0.1$  | -            | -            |
| MTT [81]       | -           | -            | -            | -            |
| FRePo [19]     | $7.5 	 0.3$ | $9.7 	 0.2$  | -            | -            |
| TESLA [95]     | $7.7 	 0.2$ | $10.5 	 0.3$ | $17.8 	 1.3$ | $27.9 	 1.2$ |

<span id="page-15-7"></span>TABLE 4 The evaluation performance results on CIFAR-10 with 10 Img/Cls for more extensive networks. † adopt momentum [\[84\]](#page-17-34).

|                           | <b>Evaluation Model</b> |                |                |                |                |
|---------------------------|-------------------------|----------------|----------------|----------------|----------------|
|                           | ResNet18                | ResNet34       | ResNet50       | ResNet101      | ResNet152      |
| $DD^{\dagger}$ [18], [84] | $33.9 \pm 1.1$          | $32.2 \pm 2.7$ | $19.0 \pm 2.0$ | $18.2 \pm 1.4$ | $12.3 \pm 0.7$ |
| DC [22]                   | $43.3 \pm 0.6$          | $35.4 \pm 1.3$ | $23.7 \pm 0.4$ | $17.6 \pm 1.0$ | $16.3 \pm 1.0$ |
| <b>DSA</b> [23]           | $42.3 \pm 0.9$          | $33.2 \pm 1.0$ | $22.9 \pm 0.7$ | $18.7 \pm 1.3$ | $15.7 \pm 1.0$ |
| DM [24]                   | $39.5 \pm 0.6$          | $31.2 \pm 1.1$ | $23.9 \pm 0.6$ | $17.7 \pm 1.3$ | $16.8 \pm 1.4$ |
| <b>MTT</b> [81]           | $45.8 \pm 1.2$          | $34.6 \pm 3.3$ | $22.5 \pm 0.4$ | $18.1 \pm 2.0$ | $18.0 \pm 1.1$ |
| FRePo [19]                | $47.4 \pm 0.7$          | $41.3 \pm 2.0$ | $41.1 \pm 1.8$ | $28.7 \pm 1.2$ | $22.7 \pm 1.0$ |

can handle the DD task on ImageNet1K. In addition, under the same compression ratio, for the more complex original dataset, the synthetic dataset obtained by the dataset distillation method may have a weaker ability to restore the performance of the original dataset, *i.e.*, the ratio of test accuracy of models trained on  $S$  and  $T$ .

**Larger Networks.** Many dataset distillation methods include the process of bi-level meta-optimization. Larger and more complex network structures require more GPU memory and runtime to compute and store the gradients during the dataset distillation process. At the same time, as shown in Table [4,](#page-15-7) when the generated dataset is evaluated on complex networks, its performance is significantly lower than that of simple networks. As the size of the synthetic dataset is small, complex networks are prone to overfitting problems trained on the small training dataset.

#### **6.3 Generalization across Different Architectures**

The generalizability of the synthetic dataset across different network architectures is an essential standard to evaluate the performance of a DD method, as it indicates availability in practice. However, the evaluation results of the synthetic data generated by the existing DD methods on heterogenous models have not reached homogeneous performance. Through the results shown in Table [2,](#page-13-2) if the synthetic dataset and the training architecture are strongly bonded in the optimization process, it will hurt the transferability of the generated synthetic dataset. Moreover, the choice of the normalization layer significantly impacts transferability.

To remedy this problem, one intuitive idea is to unbind the network and the synthetic dataset, such as the practice of DM  $[24]$  and IDC  $[82]$ . DM treats the network as the feature extractor without updating, and IDC updates the training network on the real dataset instead of the synthetic dataset. From the results, these strategies are somehow effective. However, DM has relatively poor performance, and IDC needs extra run time to train networks on real datasets, especially when distilling large-scale datasets like

ImageNet. Another way is to increase the diversity of training architectures, where dedicated schemes are necessary to coordinate their training and their impact on synthetic data.

#### **6.4 Design for Other Tasks and Applications**

The existing DD methods mainly focus on the classification task. Here, we expect future works to apply DD and conduct sophisticated designing in more tasks in computer vision, *e.g.*, semantic segmentation [\[164\]](#page-19-25), [\[165\]](#page-19-26) and objective detection [\[166\]](#page-19-27), natural language processing, *e.g.*, machine translation  $[167]$ , and multi-modal scenarios  $[6]$ .

#### **6.5 Security and Privacy**

Existing works focus on improving the methods to generate more informative synthetic datasets while neglecting the potential security and privacy of DD. A recent work [\[120\]](#page-18-27) provides a new backdoor attack method, DOORPING, which attacks during the dataset distillation process rather than afterward model training as introduced in Section [4.4.](#page-10-0) Possible defense mechanisms and more security and privacy issues are left for future work.

<span id="page-15-5"></span>

## **7 CONCLUSION**

This paper aims at a comprehensive review of dataset distillation (DD), a popular research topic recently, to synthesize a small dataset given an original large one for the sake of similar performance. We present a systematic taxonomy of existing DD methods and categorize them into three major streams by the optimization objective: performance matching, parameter matching, and distribution matching. Theoretical analysis reveals their underlying connections. For general understanding, we abstract a common algorithmic framework for all current approaches. Application of DD on various topics, including continual learning, neural architecture search, privacy, *etc* is also covered. Different approaches are experimentally compared in terms of accuracy, time efficiency, and scalability, indicating some critical challenges in this area left for future research.

#### **ACKNOWLEDGMENT**

We sincerely thank Guang Li *et al.* for the useful information in [Awesome-Dataset-Distillation.](https://github.com/Guang000/Awesome-Dataset-Distillation)

## **REFERENCES**

- <span id="page-15-0"></span>[1] A. Krizhevsky, I. Sutskever, and G. E. Hinton, "Imagenet classification with deep convolutional neural networks," *Communications of the ACM*, vol. 60, no. 6, pp. 84–90, 2017. [1,](#page-0-1) [9,](#page-8-3) [13](#page-12-1)
- <span id="page-15-1"></span>[2] J. Devlin, M.-W. Chang, K. Lee, and K. Toutanova, "Bert: Pretraining of deep bidirectional transformers for language understanding," *arXiv preprint arXiv:1810.04805*, 2018. [1](#page-0-1)
- <span id="page-15-2"></span>[3] D. Amodei, S. Ananthanarayanan, R. Anubhai, J. Bai, E. Battenberg, C. Case, J. Casper, B. Catanzaro, Q. Cheng, G. Chen *et al.*, "Deep speech 2: End-to-end speech recognition in english and mandarin," in *International conference on machine learning*. PMLR, 2016, pp. 173–182. [1](#page-0-1)
- <span id="page-15-3"></span>[4] K. He, X. Zhang, S. Ren, and J. Sun, "Deep residual learning for image recognition," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2016, pp. 770–778. [1,](#page-0-1) [13](#page-12-1)
- <span id="page-15-4"></span>[5] A. Dosovitskiy, L. Beyer, A. Kolesnikov, D. Weissenborn, X. Zhai, T. Unterthiner, M. Dehghani, M. Minderer, G. Heigold, S. Gelly *et al.*, "An image is worth 16x16 words: Transformers for image recognition at scale," *arXiv preprint arXiv:2010.11929*, 2020. [1](#page-0-1)

- <span id="page-16-0"></span>[6] A. Radford, J. W. Kim, C. Hallacy, A. Ramesh, G. Goh, S. Agarwal, G. Sastry, A. Askell, P. Mishkin, J. Clark *et al.*, "Learning transferable visual models from natural language supervision, in *International Conference on Machine Learning*. PMLR, 2021, pp. 8748–8763. [1,](#page-0-1) [16](#page-15-8)
- <span id="page-16-1"></span>[7] A. Ramesh, P. Dhariwal, A. Nichol, C. Chu, and M. Chen, "Hierarchical text-conditional image generation with clip latents," *arXiv preprint arXiv:2204.06125*, 2022. [1](#page-0-1)
- <span id="page-16-2"></span>[8] C. Chen, Y. Zhang, J. Fu, X. Liu, and M. Coates, "Bidirectional learning for offline infinite-width model-based optimization," in *Thirty-Sixth Conference on Neural Information Processing Systems*, 2022. [Online]. Available: [https://openreview.net/forum?id=](https://openreview.net/forum?id=_j8yVIyp27Q) [j8yVIyp27Q](https://openreview.net/forum?id=_j8yVIyp27Q) [1](#page-0-1)
- <span id="page-16-3"></span>[9] D. Maclaurin, D. Duvenaud, and R. Adams, "Gradient-based hyperparameter optimization through reversible learning," in *International conference on machine learning*. PMLR, 2015, pp. 2113–2122. [1,](#page-0-1) [3](#page-2-2)
- <span id="page-16-4"></span>[10] J. Lorraine, P. Vicol, and D. Duvenaud, "Optimizing millions of hyperparameters by implicit differentiation," in *International Conference on Artificial Intelligence and Statistics*. PMLR, 2020, pp. 1540–1552. [1,](#page-0-1) [3,](#page-2-2) [4](#page-3-3)
- <span id="page-16-5"></span>[11] F. P. Such, A. Rawal, J. Lehman, K. Stanley, and J. Clune, "Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data," in *International Conference on Machine Learning*. PMLR, 2020, pp. 9206–9216. [1,](#page-0-1) [11](#page-10-1)
- <span id="page-16-6"></span>[12] L. Li and A. Talwalkar, "Random search and reproducibility for neural architecture search," in *Uncertainty in artificial intelligence*. PMLR, 2020, pp. 367–377. [1](#page-0-1)
- <span id="page-16-7"></span>[13] T. Elsken, J. H. Metzen, and F. Hutter, "Neural architecture search: A survey," *The Journal of Machine Learning Research*, vol. 20, no. 1, pp. 1997–2017, 2019. [1,](#page-0-1) [11](#page-10-1)
- <span id="page-16-8"></span>[14] I. J. Goodfellow, M. Mirza, D. Xiao, A. Courville, and Y. Bengio, "An empirical investigation of catastrophic forgetting in gradient-based neural networks," *arXiv preprint arXiv:1312.6211*, 2013. [1](#page-0-1)
- <span id="page-16-9"></span>[15] S.-A. Rebuffi, A. Kolesnikov, G. Sperl, and C. H. Lampert, "icarl: Incremental classifier and representation learning," in *Proceedings of the IEEE conference on Computer Vision and Pattern Recognition*, 2017, pp. 2001–2010. [1,](#page-0-1) [10](#page-9-1)
- <span id="page-16-10"></span>[16] T. Dong, B. Zhao, and L. Lyu, "Privacy for free: How does dataset condensation help privacy?" *arXiv preprint arXiv:2206.00240*, 2022. [1,](#page-0-1) [11](#page-10-1)
- <span id="page-16-11"></span>[17] R. Shokri and V. Shmatikov, "Privacy-preserving deep learning," in *Proceedings of the 22nd ACM SIGSAC conference on computer and communications security*, 2015, pp. 1310–1321. [1](#page-0-1)
- <span id="page-16-12"></span>[18] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-2) [4,](#page-3-3) [12,](#page-11-0) [13,](#page-12-1) [14,](#page-13-3) [15,](#page-14-3) [16,](#page-15-8) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-16-13"></span>[19] Y. Zhou, E. Nezhadarya, and J. Ba, "Dataset distillation using neural feature regression," *arXiv preprint arXiv:2206.00719*, 2022. [2,](#page-1-1) [4,](#page-3-3) [5,](#page-4-4) [8,](#page-7-2) [10,](#page-9-1) [13,](#page-12-1) [14,](#page-13-3) [15,](#page-14-3) [16,](#page-15-8) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-16-14"></span>[20] T. Nguyen, Z. Chen, and J. Lee, "Dataset meta-learning from kernel ridge-regression," *arXiv preprint arXiv:2011.00050*, 2020. [2,](#page-1-1) [4,](#page-3-3) [5,](#page-4-4) [10,](#page-9-1) [11,](#page-10-1) [12,](#page-11-0) [15,](#page-14-3) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-16-15"></span>[21] T. Nguyen, R. Novak, L. Xiao, and J. Lee, "Dataset distillation with infinitely wide convolutional networks," *Advances in Neural Information Processing Systems*, vol. 34, pp. 5186–5198, 2021. [2,](#page-1-1) [4,](#page-3-3) [5,](#page-4-4) [11,](#page-10-1) [12,](#page-11-0) [15,](#page-14-3) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-16-16"></span>[22] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching." *ICLR*, vol. 1, no. 2, p. 3, 2021. [2,](#page-1-1) [4,](#page-3-3) [5,](#page-4-4) [6,](#page-5-2) [8,](#page-7-2) [10,](#page-9-1) [11,](#page-10-1) [12,](#page-11-0) [13,](#page-12-1) [14,](#page-13-3) [16,](#page-15-8) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-16-17"></span>[23] B. Zhao and H. Bilen, "Dataset condensation with differentiable siamese augmentation," in *International Conference on Machine Learning*. PMLR, 2021, pp. 12 674–12 685. [2,](#page-1-1) [4,](#page-3-3) [6,](#page-5-2) [9,](#page-8-3) [11,](#page-10-1) [13,](#page-12-1) [14,](#page-13-3) [16,](#page-15-8) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-16-18"></span>[24] Bo Zhao and Hakan Bilen, "Dataset condensation with distribution matching," *CoRR*, vol. abs/2110.04181, 2021. [2,](#page-1-1) [7,](#page-6-1) [8,](#page-7-2) [9,](#page-8-3) [10,](#page-9-1) [11,](#page-10-1) [12,](#page-11-0) [13,](#page-12-1) [14,](#page-13-3) [15,](#page-14-3) [16,](#page-15-8) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-16-19"></span>[25] Y. Liu, Y. Su, A.-A. Liu, B. Schiele, and Q. Sun, "Mnemonics training: Multi-class incremental learning without forgetting," in *Proceedings of the IEEE/CVF conference on Computer Vision and Pattern Recognition*, 2020, pp. 12 245–12 254. [2,](#page-1-1) [10](#page-9-1)
- <span id="page-16-20"></span>[26] A. Rosasco, A. Carta, A. Cossu, V. Lomonaco, and D. Bacciu, "Distilled replay: Overcoming forgetting through synthetic samples," in *International Workshop on Continual Semi-Supervised Learning*. Springer, 2022, pp. 104–117. [2,](#page-1-1) [10](#page-9-1)

- <span id="page-16-21"></span>[27] M. Sangermano, A. Carta, A. Cossu, and D. Bacciu, "Sample condensation in online continual learning," in *2022 International Joint Conference on Neural Networks (IJCNN)*. IEEE, 2022, pp. 01– 08. [2,](#page-1-1) [11](#page-10-1)
- <span id="page-16-22"></span>[28] F. Wiewel and B. Yang, "Condensed composite memory continual learning," in *2021 International Joint Conference on Neural Networks (IJCNN)*. IEEE, 2021, pp. 1–8. [2,](#page-1-1) [11](#page-10-1)
- <span id="page-16-23"></span>[29] W. Masarczyk and I. Tautkute, "Reducing catastrophic forgetting with learning on synthetic data," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops*, 2020, pp. 252–253. [2,](#page-1-1) [11](#page-10-1)
- <span id="page-16-24"></span>[30] J. Goetz and A. Tewari, "Federated learning via synthetic data," *arXiv preprint arXiv:2008.04489*, 2020. [2,](#page-1-1) [11](#page-10-1)
- <span id="page-16-25"></span>[31] Y. Zhou, G. Pu, X. Ma, X. Li, and D. Wu, "Distilled one-shot federated learning," *arXiv preprint arXiv:2009.07999*, 2020. [2,](#page-1-1) [11](#page-10-1)
- <span id="page-16-26"></span>[32] Y. Xiong, R. Wang, M. Cheng, F. Yu, and C.-J. Hsieh, "Feddm: Iterative distribution matching for communication-efficient federated learning," *arXiv preprint arXiv:2207.09653*, 2022. [2,](#page-1-1) [11](#page-10-1)
- <span id="page-16-27"></span>[33] R. Song, D. Liu, D. Z. Chen, A. Festag, C. Trinitis, M. Schulz, and A. Knoll, "Federated learning via decentralized dataset distillation in resource-constrained edge environments," *arXiv preprint arXiv:2208.11311*, 2022. [2,](#page-1-1) [11](#page-10-1)
- <span id="page-16-28"></span>[34] P. Liu, X. Yu, and J. T. Zhou, "Meta knowledge condensation for federated learning," *arXiv preprint arXiv:2209.14851*, 2022. [2,](#page-1-1) [11](#page-10-1)
- <span id="page-16-29"></span>[35] S. Hu, J. Goetz, K. Malik, H. Zhan, Z. Liu, and Y. Liu, "Fedsynth: Gradient compression via synthetic data in federated learning," *arXiv preprint arXiv:2204.01273*, 2022. [2,](#page-1-1) [11](#page-10-1)
- <span id="page-16-30"></span>[36] R. Pi, W. Zhang, Y. Xie, J. Gao, X. Wang, S. Kim, and Q. Chen, "Dynafed: Tackling client data heterogeneity with global dynamics," *arXiv preprint arXiv:2211.10878*, 2022. [2,](#page-1-1) [11](#page-10-1)
- <span id="page-16-31"></span>[37] G. Hinton, O. Vinyals, J. Dean *et al.*, "Distilling the knowledge in a neural network," *arXiv preprint arXiv:1503.02531*, vol. 2, no. 7, 2015. [2,](#page-1-1) [12](#page-11-0)
- <span id="page-16-32"></span>[38] A. Romero, N. Ballas, S. E. Kahou, A. Chassang, C. Gatta, and Y. Bengio, "Fitnets: Hints for thin deep nets," *arXiv preprint arXiv:1412.6550*, 2014. [2,](#page-1-1) [12](#page-11-0)
- <span id="page-16-33"></span>[39] S. Zagoruyko and N. Komodakis, "Paying more attention to attention: Improving the performance of convolutional neural networks via attention transfer," *arXiv preprint arXiv:1612.03928*, 2016. [2,](#page-1-1) [12](#page-11-0)
- <span id="page-16-34"></span>[40] J. Gou, B. Yu, S. J. Maybank, and D. Tao, "Knowledge distillation: A survey," *International Journal of Computer Vision*, vol. 129, no. 6, pp. 1789–1819, 2021. [2](#page-1-1)
- <span id="page-16-35"></span>[41] S. I. Mirzadeh, M. Farajtabar, A. Li, N. Levine, A. Matsukawa, and H. Ghasemzadeh, "Improved knowledge distillation via teacher assistant," in *Proceedings of the AAAI conference on artificial intelligence*, vol. 34, no. 04, 2020, pp. 5191–5198. [2](#page-1-1)
- <span id="page-16-36"></span>[42] K. Xu, L. Rui, Y. Li, and L. Gu, "Feature normalized knowledge distillation for image classification," in *European Conference on Computer Vision*. Springer, 2020, pp. 664–680. [2](#page-1-1)
- <span id="page-16-37"></span>[43] X. Wang, T. Fu, S. Liao, S. Wang, Z. Lei, and T. Mei, "Exclusivityconsistency regularized knowledge distillation for face recognition," in *European Conference on Computer Vision*. Springer, 2020, pp. 325–342. [2](#page-1-1)
- <span id="page-16-38"></span>[44] S. You, C. Xu, C. Xu, and D. Tao, "Learning from multiple teacher networks," in *Proceedings of the 23rd ACM SIGKDD International Conference on Knowledge Discovery and Data Mining*, 2017, pp. 1285–1294. [2](#page-1-1)
- <span id="page-16-39"></span>[45] W. Park, D. Kim, Y. Lu, and M. Cho, "Relational knowledge distillation," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2019, pp. 3967–3976. [2](#page-1-1)
- <span id="page-16-40"></span>[46] J. Liu, D. Wen, H. Gao, W. Tao, T.-W. Chen, K. Osa, and M. Kato, "Knowledge representing: efficient, sparse representation of prior knowledge for knowledge distillation," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops*, 2019, pp. 0–0. [2](#page-1-1)
- <span id="page-16-41"></span>[47] X. Wang, R. Zhang, Y. Sun, and J. Qi, "Kdgan: Knowledge distillation with generative adversarial networks," *Advances in neural information processing systems*, vol. 31, 2018. [2](#page-1-1)
- <span id="page-16-42"></span>[48] R. G. Lopes, S. Fenu, and T. Starner, "Data-free knowledge distillation for deep neural networks," *arXiv preprint arXiv:1710.07535*, 2017. [2](#page-1-1)
- <span id="page-16-43"></span>[49] G. Fang, K. Mo, X. Wang, J. Song, S. Bei, H. Zhang, and M. Song, "Up to 100x faster data-free knowledge distillation," in *Proceedings of the AAAI Conference on Artificial Intelligence*, vol. 36, no. 6, 2022, pp. 6597–6604. [2](#page-1-1)

- <span id="page-17-0"></span>[50] X. Zhang, X. Zhou, M. Lin, and J. Sun, "Shufflenet: An extremely efficient convolutional neural network for mobile devices," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2018, pp. 6848–6856. [2](#page-1-1)
- <span id="page-17-1"></span>[51] L. Zhang, J. Song, A. Gao, J. Chen, C. Bao, and K. Ma, "Be your own teacher: Improve the performance of convolutional neural networks via self distillation," in *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2019, pp. 3713–3722. [2](#page-1-1)
- <span id="page-17-2"></span>[52] I. Radosavovic, P. Dollár, R. Girshick, G. Gkioxari, and K. He, "Data distillation: Towards omni-supervised learning," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2018, pp. 4119–4128. [2](#page-1-1)
- <span id="page-17-3"></span>[53] J. Ba and R. Caruana, "Do deep nets really need to be deep?" *Advances in neural information processing systems*, vol. 27, 2014. [2](#page-1-1)
- <span id="page-17-4"></span>[54] A. G. Howard, M. Zhu, B. Chen, D. Kalenichenko, W. Wang, T. Weyand, M. Andreetto, and H. Adam, "Mobilenets: Efficient convolutional neural networks for mobile vision applications," *arXiv preprint arXiv:1704.04861*, 2017. [2](#page-1-1)
- <span id="page-17-5"></span>[55] G. K. Nayak, K. R. Mopuri, V. Shaj, V. B. Radhakrishnan, and A. Chakraborty, "Zero-shot knowledge distillation in deep networks," in *International Conference on Machine Learning*. PMLR, 2019, pp. 4743–4751. [2](#page-1-1)
- <span id="page-17-6"></span>[56] M. Welling, "Herding dynamical weights to learn," in *Proceedings of the 26th Annual International Conference on Machine Learning*, 2009, pp. 1121–1128. [2](#page-1-1)
- <span id="page-17-7"></span>Y. Chen, M. Welling, and A. Smola, "Super-samples from kernel herding," *arXiv preprint arXiv:1203.3472*, 2012. [2](#page-1-1)
- <span id="page-17-8"></span>[58] D. Feldman, M. Faulkner, and A. Krause, "Scalable training of mixture models via coresets," *Advances in neural information processing systems*, vol. 24, 2011. [2](#page-1-1)
- <span id="page-17-9"></span>[59] O. Bachem, M. Lucic, and A. Krause, "Coresets for nonparametric estimation-the case of dp-means," in *International Conference on Machine Learning*. PMLR, 2015, pp. 209–217. [2](#page-1-1)
- <span id="page-17-10"></span>[60] Z. Borsos, M. Mutny, and A. Krause, "Coresets via bilevel optimization for continual learning and streaming," *Advances in Neural Information Processing Systems*, vol. 33, pp. 14 879–14 890, 2020. [2](#page-1-1)
- <span id="page-17-11"></span>[61] K. Killamsetty, X. Zhao, F. Chen, and R. Iyer, "Retrieve: Coreset selection for efficient and robust semi-supervised learning," *Advances in Neural Information Processing Systems*, vol. 34, pp. 14 488– 14 501, 2021. [2](#page-1-1)
- <span id="page-17-12"></span>[62] K. Killamsetty, D. Sivasubramanian, G. Ramakrishnan, and R. Iyer, "Glister: Generalization based data subset selection for efficient and robust learning," in *Proceedings of the AAAI Conference on Artificial Intelligence*, vol. 35, no. 9, 2021, pp. 8110–8118. [2](#page-1-1)
- <span id="page-17-13"></span>[63] B. Mirzasoleiman, J. Bilmes, and J. Leskovec, "Coresets for dataefficient training of machine learning models," in *International Conference on Machine Learning*. PMLR, 2020, pp. 6950–6960. [2](#page-1-1)
- <span id="page-17-14"></span>[64] I. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair, A. Courville, and Y. Bengio, "Generative adversarial networks," *Communications of the ACM*, vol. 63, no. 11, pp. 139–144, 2020. [3](#page-2-2)
- <span id="page-17-15"></span>[65] M. Mirza and S. Osindero, "Conditional generative adversarial nets," *arXiv preprint arXiv:1411.1784*, 2014. [3](#page-2-2)
- <span id="page-17-16"></span>[66] D. P. Kingma and M. Welling, "Auto-encoding variational bayes," *arXiv preprint arXiv:1312.6114*, 2013. [3](#page-2-2)
- <span id="page-17-17"></span>[67] B. Zhao and H. Bilen, "Synthesizing informative training samples with gan," *arXiv preprint arXiv:2204.07513*, 2022. [3,](#page-2-2) [9,](#page-8-3) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-17-18"></span>[68] R. Abdal, Y. Qin, and P. Wonka, "Image2stylegan: How to embed images into the stylegan latent space?" in *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2019, pp. 4432–4441. [3,](#page-2-2) [10](#page-9-1)
- <span id="page-17-19"></span>[69] J. Zhu, Y. Shen, D. Zhao, and B. Zhou, "In-domain gan inversion for real image editing," in *European conference on computer vision*. Springer, 2020, pp. 592–608. [3](#page-2-2)
- <span id="page-17-20"></span>[70] H. B. Lee, D. B. Lee, and S. J. Hwang, "Dataset condensation with latent space knowledge factorization and sharing," *arXiv preprint arXiv:2208.10494*, 2022. [3,](#page-2-2) [10,](#page-9-1) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-17-21"></span>[71] J. Snoek, H. Larochelle, and R. P. Adams, "Practical bayesian optimization of machine learning algorithms," *Advances in neural information processing systems*, vol. 25, 2012. [3](#page-2-2)
- <span id="page-17-22"></span>[72] J. Bergstra, D. Yamins, and D. Cox, "Making a science of model search: Hyperparameter optimization in hundreds of dimensions for vision architectures," in *International conference on machine learning*. PMLR, 2013, pp. 115–123. [3](#page-2-2)

- <span id="page-17-23"></span>[73] L. Franceschi, M. Donini, P. Frasconi, and M. Pontil, "Forward and reverse gradient-based hyperparameter optimization," in *International Conference on Machine Learning*. PMLR, 2017, pp. 1165–1173. [3](#page-2-2)
- <span id="page-17-24"></span>[74] L. Franceschi, P. Frasconi, S. Salzo, R. Grazzi, and M. Pontil, "Bilevel programming for hyperparameter optimization and meta-learning," in *International Conference on Machine Learning*. PMLR, 2018, pp. 1568–1577. [3](#page-2-2)
- <span id="page-17-25"></span>[75] J. Cui, R. Wang, S. Si, and C.-J. Hsieh, "DC-BENCH: Dataset condensation benchmark," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022. [4,](#page-3-3) [15,](#page-14-3) [20](#page-19-29)
- <span id="page-17-26"></span>[76] O. Sener and S. Savarese, "Active learning for convolutional neural networks: A core-set approach," *arXiv preprint arXiv:1708.00489*, 2017. [4](#page-3-3)
- <span id="page-17-27"></span>[77] K. He, X. Zhang, S. Ren, and J. Sun, "Delving deep into rectifiers: Surpassing human-level performance on imagenet classification," in *Proceedings of the IEEE international conference on computer vision*, 2015, pp. 1026–1034. [4](#page-3-3)
- <span id="page-17-28"></span>[78] X. Glorot and Y. Bengio, "Understanding the difficulty of training deep feedforward neural networks," in *Proceedings of the thirteenth international conference on artificial intelligence and statistics*. JMLR Workshop and Conference Proceedings, 2010, pp. 249–256. [4](#page-3-3)
- <span id="page-17-29"></span>[79] S. Lee, S. Chun, S. Jung, S. Yun, and S. Yoon, "Dataset condensation with contrastive signals," in *Proceedings of the International Conference on Machine Learning (ICML)*, 2022, pp. 12 352–12 364. [4,](#page-3-3) [5,](#page-4-4) [6,](#page-5-2) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-17-30"></span>[80] Z. Jiang, J. Gu, M. Liu, and D. Z. Pan, "Delving into effective gradient matching for dataset condensation," *arXiv preprint arXiv:2208.00311*, 2022. [4,](#page-3-3) [5,](#page-4-4) [6,](#page-5-2) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-17-31"></span>[81] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 4750–4759. [4,](#page-3-3) [5,](#page-4-4) [6,](#page-5-2) [9,](#page-8-3) [10,](#page-9-1) [12,](#page-11-0) [13,](#page-12-1) [14,](#page-13-3) [15,](#page-14-3) [16,](#page-15-8) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-17-32"></span>[82] J.-H. Kim, J. Kim, S. J. Oh, S. Yun, H. Song, J. Jeong, J.-W. Ha, and H. O. Song, "Dataset condensation via efficient syntheticdata parameterization," *arXiv preprint arXiv:2205.14959*, 2022. [4,](#page-3-3) [5,](#page-4-4) [6,](#page-5-2) [9,](#page-8-3) [10,](#page-9-1) [16,](#page-15-8) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-17-33"></span>[83] C. Finn, P. Abbeel, and S. Levine, "Model-agnostic meta-learning for fast adaptation of deep networks," in *Proceedings of the 34th International Conference on Machine Learning, ICML 2017, Sydney, NSW, Australia, 6-11 August 2017*, ser. Proceedings of Machine Learning Research, D. Precup and Y. W. Teh, Eds., vol. 70. PMLR, 2017, pp. 1126–1135. [Online]. Available: <http://proceedings.mlr.press/v70/finn17a.html> [4](#page-3-3)
- <span id="page-17-34"></span>[84] Z. Deng and O. Russakovsky, "Remember the past: Distilling datasets into addressable memories for neural networks," *arXiv preprint arXiv:2206.02916*, 2022. [4,](#page-3-3) [10,](#page-9-1) [13,](#page-12-1) [14,](#page-13-3) [16,](#page-15-8) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-17-35"></span>[85] N. Loo, R. Hasani, A. Amini, and D. Rus, "Efficient dataset distillation using random feature approximation," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022. [4,](#page-3-3) [5,](#page-4-4) [15,](#page-14-3) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-17-36"></span>[86] A. Jacot, F. Gabriel, and C. Hongler, "Neural tangent kernel: Convergence and generalization in neural networks," *Advances in neural information processing systems*, vol. 31, 2018. [5](#page-4-4)
- <span id="page-17-37"></span>[87] J. Lee, L. Xiao, S. Schoenholz, Y. Bahri, R. Novak, J. Sohl-Dickstein, and J. Pennington, "Wide neural networks of any depth evolve as linear models under gradient descent," *Advances in neural information processing systems*, vol. 32, 2019. [5](#page-4-4)
- <span id="page-17-38"></span>[88] S. Arora, S. S. Du, W. Hu, Z. Li, R. R. Salakhutdinov, and R. Wang, "On exact computation with an infinitely wide neural net," *Advances in Neural Information Processing Systems*, vol. 32, 2019. [5](#page-4-4)
- <span id="page-17-39"></span>[89] J. Lee, S. Schoenholz, J. Pennington, B. Adlam, L. Xiao, R. Novak, and J. Sohl-Dickstein, "Finite versus infinite neural networks: an empirical study," *Advances in Neural Information Processing Systems*, vol. 33, pp. 15 156–15 172, 2020. [5](#page-4-4)
- <span id="page-17-40"></span>[90] R. M. Neal, *Bayesian learning for neural networks*. Springer Science & Business Media, 2012, vol. 118. [5](#page-4-4)
- <span id="page-17-41"></span>[91] J. Lee, Y. Bahri, R. Novak, S. S. Schoenholz, J. Pennington, and J. Sohl-Dickstein, "Deep neural networks as gaussian processes," *arXiv preprint arXiv:1711.00165*, 2017. [5](#page-4-4)
- <span id="page-17-42"></span>[92] J. Platt *et al.*, "Probabilistic outputs for support vector machines and comparisons to regularized likelihood methods," *Advances in large margin classifiers*, vol. 10, no. 3, pp. 61–74, 1999. [5](#page-4-4)

- <span id="page-18-0"></span>[93] L. Zhang, J. Zhang, B. Lei, S. Mukherjee, X. Pan, B. Zhao, C. Ding, Y. Li, and X. Dongkuan, "Accelerating dataset distillation via model augmentation," *arXiv preprint arXiv:2212.06152*, 2022. [5,](#page-4-4) [6,](#page-5-2) [15,](#page-14-3) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-18-1"></span>[94] Li, Guang and Togo, Ren and Ogawa, Takahiro and Haseyama, Miki, "Dataset distillation using parameter pruning," *arXiv preprint arXiv:2209.14609*, 2022. [6,](#page-5-2) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-18-2"></span>[95] J. Cui, R. Wang, S. Si, and C.-J. Hsieh, "Scaling up dataset distillation to imagenet-1k with constant memory," *arXiv preprint arXiv:2211.10586*, 2022. [7,](#page-6-1) [9,](#page-8-3) [10,](#page-9-1) [15,](#page-14-3) [16,](#page-15-8) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-18-3"></span>[96] J. Du, Y. Jiang, V. T. F. Tan, J. T. Zhou, and H. Li, "Minimizing the accumulated trajectory error to improve dataset distillation, *arXiv preprint arXiv:2211.11004*, 2022. [7,](#page-6-1) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-18-4"></span>[97] K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You, "Cafe: Learning to condense dataset by aligning features," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 12 196–12 205. [7,](#page-6-1) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-18-5"></span>[98] T. DeVries and G. W. Taylor, "Improved regularization of convolutional neural networks with cutout," *arXiv preprint arXiv:1708.04552*, 2017. [9](#page-8-3)
- <span id="page-18-6"></span>[99] S. Liu, K. Wang, X. Yang, J. Ye, and X. Wang, "Dataset distillation via factorization," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022. [10,](#page-9-1) [21,](#page-20-0) [22,](#page-21-0) [23](#page-22-0)
- <span id="page-18-7"></span>[100] I. Sucholutsky and M. Schonlau, "Soft-label dataset distillation and text dataset distillation," in *2021 International Joint Conference on Neural Networks (IJCNN)*. IEEE, 2021, pp. 1–8. [10,](#page-9-1) [12](#page-11-0)
- <span id="page-18-8"></span>[101] O. Bohdal, Y. Yang, and T. Hospedales, "Flexible dataset distillation: Learn labels instead of images," *arXiv preprint arXiv:2006.08572*, 2020. [10,](#page-9-1) [11,](#page-10-1) [12](#page-11-0)
- <span id="page-18-9"></span>[102] J. Kirkpatrick, R. Pascanu, N. Rabinowitz, J. Veness, G. Desjardins, A. A. Rusu, K. Milan, J. Quan, T. Ramalho, A. Grabska-Barwinska *et al.*, "Overcoming catastrophic forgetting in neural networks," *Proceedings of the national academy of sciences*, vol. 114, no. 13, pp. 3521–3526, 2017. [10](#page-9-1)
- <span id="page-18-10"></span>[103] P. Buzzega, M. Boschini, A. Porrello, and S. Calderara, "Rethinking experience replay: a bag of tricks for continual learning," in *2020 25th International Conference on Pattern Recognition (ICPR)*. IEEE, 2021, pp. 2180–2187. [10](#page-9-1)
- <span id="page-18-11"></span>[104] Y. Liu, B. Schiele, and Q. Sun, "Adaptive aggregation networks for class-incremental learning," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2021, pp. 2544–2553. [10](#page-9-1)
- <span id="page-18-12"></span>[105] A. Prabhu, P. H. Torr, and P. K. Dokania, "Gdumb: A simple approach that questions our progress in continual learning," in *European conference on computer vision*. Springer, 2020, pp. 524– 540. [10](#page-9-1)
- <span id="page-18-13"></span>[106] J. Konečnỳ, H. B. McMahan, D. Ramage, and P. Richtárik, "Federated optimization: Distributed machine learning for on-device intelligence," *arXiv preprint arXiv:1610.02527*, 2016. [11](#page-10-1)
- <span id="page-18-14"></span>[107] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y Arcas, "Communication-efficient learning of deep networks from decentralized data," in *Artificial intelligence and statistics*. PMLR, 2017, pp. 1273–1282. [11](#page-10-1)
- <span id="page-18-15"></span>[108] Q. Yang, Y. Liu, T. Chen, and Y. Tong, "Federated machine learning: Concept and applications," *ACM Transactions on Intelligent Systems and Technology (TIST)*, vol. 10, no. 2, pp. 1–19, 2019. [11](#page-10-1)
- <span id="page-18-16"></span>[109] M. Wistuba, A. Rawat, and T. Pedapati, "A survey on neural architecture search," *arXiv preprint arXiv:1905.01392*, 2019. [11](#page-10-1)
- <span id="page-18-17"></span>[110] L. Lyu, H. Yu, and Q. Yang, "Threats to federated learning: A survey," *arXiv preprint arXiv:2003.02133*, 2020. [11](#page-10-1)
- <span id="page-18-18"></span>[111] M. Fredrikson, S. Jha, and T. Ristenpart, "Model inversion attacks that exploit confidence information and basic countermeasures," in *Proceedings of the 22nd ACM SIGSAC conference on computer and communications security*, 2015, pp. 1322–1333. [11](#page-10-1)
- <span id="page-18-19"></span>[112] R. Shokri, M. Stronati, C. Song, and V. Shmatikov, "Membership inference attacks against machine learning models," in *2017 IEEE symposium on security and privacy (SP)*. IEEE, 2017, pp. 3–18. [11](#page-10-1)
- <span id="page-18-20"></span>[113] L. Melis, C. Song, E. De Cristofaro, and V. Shmatikov, "Exploiting unintended feature leakage in collaborative learning," in *2019 IEEE symposium on security and privacy (SP)*. IEEE, 2019, pp. 691–706. [11](#page-10-1)
- <span id="page-18-21"></span>[114] I. Sucholutsky and M. Schonlau, "Secdd: Efficient and secure method for remotely training neural networks," *arXiv preprint arXiv:2009.09155*, 2020. [11](#page-10-1)

- <span id="page-18-22"></span>[115] M. Kettunen, E. Härkönen, and J. Lehtinen, "E-lpips: robust perceptual image similarity via random transformation ensembles," *arXiv preprint arXiv:1906.03973*, 2019. [11](#page-10-1)
- <span id="page-18-23"></span>[116] D. Chen, R. Kerkouche, and M. Fritz, "Private set generation with discriminative information," *arXiv preprint arXiv:2211.04446*, 2022. [11](#page-10-1)
- <span id="page-18-24"></span>[117] N. Tsilivis, J. Su, and J. Kempe, "Can we achieve robustness from data alone?" *arXiv preprint arXiv:2207.11727*, 2022. [11](#page-10-1)
- <span id="page-18-25"></span>[118] A. Madry, A. Makelov, L. Schmidt, D. Tsipras, and A. Vladu, "Towards deep learning models resistant to adversarial attacks," *arXiv preprint arXiv:1706.06083*, 2017. [11](#page-10-1)
- <span id="page-18-26"></span>[119] Y. Wu, X. Li, F. Kerschbaum, H. Huang, and H. Zhang, "Towards robust dataset learning," 2022. [Online]. Available: <https://arxiv.org/abs/2211.10752> [11](#page-10-1)
- <span id="page-18-27"></span>[120] Y. Liu, Z. Li, M. Backes, Y. Shen, and Y. Zhang, "Backdoor attacks against dataset distillation," *arXiv preprint arXiv:2301.01197*, 2023. [11,](#page-10-1) [16](#page-15-8)
- <span id="page-18-28"></span>[121] Y. Li, X. Lyu, N. Koren, L. Lyu, B. Li, and X. Ma, "Neural attention distillation: Erasing backdoor triggers from deep neural networks," *arXiv preprint arXiv:2101.05930*, 2021. [12](#page-11-0)
- <span id="page-18-29"></span>[122] Y. Liu, W.-C. Lee, G. Tao, S. Ma, Y. Aafer, and X. Zhang, "Abs: Scanning neural networks for back-doors by artificial brain stimulation," in *Proceedings of the 2019 ACM SIGSAC Conference on Computer and Communications Security*, 2019, pp. 1265–1282. [12](#page-11-0)
- <span id="page-18-30"></span>[123] B. Wang, Y. Yao, S. Shan, H. Li, B. Viswanath, H. Zheng, and B. Y. Zhao, "Neural cleanse: Identifying and mitigating backdoor attacks in neural networks," in *2019 IEEE Symposium on Security and Privacy (SP)*. IEEE, 2019, pp. 707–723. [12](#page-11-0)
- <span id="page-18-31"></span>[124] S. Cho, T. J. Jun, B. Oh, and D. Kim, "Dapas: Denoising autoencoder to prevent adversarial attack in semantic segmentation," in *2020 International Joint Conference on Neural Networks (IJCNN)*. IEEE, 2020, pp. 1–8. [12](#page-11-0)
- <span id="page-18-32"></span>[125] Y. Gao, C. Xu, D. Wang, S. Chen, D. C. Ranasinghe, and S. Nepal, "Strip: A defence against trojan attacks on deep neural networks," in *Proceedings of the 35th Annual Computer Security Applications Conference*, 2019, pp. 113–125. [12](#page-11-0)
- <span id="page-18-33"></span>[126] H. Kwon, "Defending deep neural networks against backdoor attack by using de-trigger autoencoder," *IEEE Access*, 2021. [12](#page-11-0)
- <span id="page-18-34"></span>[127] J. Hayase, W. Kong, R. Somani, and S. Oh, "Spectre: defending against backdoor attacks using robust statistics," *arXiv preprint arXiv:2104.11315*, 2021. [12](#page-11-0)
- <span id="page-18-35"></span>[128] D. Tang, X. Wang, H. Tang, and K. Zhang, "Demon in the variant: Statistical analysis of dnns for robust backdoor contamination detection," *arXiv preprint arXiv:1908.00686*, 2019. [12](#page-11-0)
- <span id="page-18-36"></span>[129] B. Tran, J. Li, and A. Madry, "Spectral signatures in backdoor attacks," *Advances in neural information processing systems*, vol. 31, 2018. [12](#page-11-0)
- <span id="page-18-37"></span>[130] P. W. Battaglia, J. B. Hamrick, V. Bapst, A. Sanchez-Gonzalez, V. Zambaldi, M. Malinowski, A. Tacchetti, D. Raposo, A. Santoro, R. Faulkner *et al.*, "Relational inductive biases, deep learning, and graph networks," *arXiv preprint arXiv:1806.01261*, 2018. [12](#page-11-0)
- <span id="page-18-38"></span>[131] T. N. Kipf and M. Welling, "Semi-supervised classification with graph convolutional networks," *arXiv preprint arXiv:1609.02907*, 2016. [12](#page-11-0)
- <span id="page-18-39"></span>[132] P. Veličković, G. Cucurull, A. Casanova, A. Romero, P. Lio, and Y. Bengio, "Graph attention networks," *arXiv preprint arXiv:1710.10903*, 2017. [12](#page-11-0)
- <span id="page-18-40"></span>[133] Z. Wu, S. Pan, F. Chen, G. Long, C. Zhang, and S. Y. Philip, "A comprehensive survey on graph neural networks," *IEEE transactions on neural networks and learning systems*, vol. 32, no. 1, pp. 4–24, 2020. [12](#page-11-0)
- <span id="page-18-41"></span>[134] W. Fan, Y. Ma, Q. Li, Y. He, E. Zhao, J. Tang, and D. Yin, "Graph neural networks for social recommendation," in *The world wide web conference*, 2019, pp. 417–426. [12](#page-11-0)
- <span id="page-18-42"></span>[135] Z. Ying, J. You, C. Morris, X. Ren, W. Hamilton, and J. Leskovec, "Hierarchical graph representation learning with differentiable pooling," *Advances in neural information processing systems*, vol. 31, 2018. [12](#page-11-0)
- <span id="page-18-43"></span>[136] W. Jin, L. Zhao, S. Zhang, Y. Liu, J. Tang, and N. Shah, "Graph condensation for graph neural networks," *arXiv preprint arXiv:2110.07580*, 2021. [12](#page-11-0)
- <span id="page-18-44"></span>[137] J. J. Pfeiffer III, S. Moreno, T. La Fond, J. Neville, and B. Gallagher, "Attributed graph models: Modeling network structure with correlated attributes," in *Proceedings of the 23rd international conference on World wide web*, 2014, pp. 831–842. [12](#page-11-0)
- <span id="page-18-45"></span>[138] W. Jin, X. Tang, H. Jiang, Z. Li, D. Zhang, J. Tang, and B. Yin, "Condensing graphs via one-step gradient matching," in *Proceed-*

<span id="page-19-29"></span>*ings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, 2022, pp. 720–730. [12](#page-11-0)

- <span id="page-19-0"></span>[139] M. Liu, S. Li, X. Chen, and L. Song, "Graph condensation via receptive field distribution matching," *arXiv preprint arXiv:2206.13697*, 2022. [12](#page-11-0)
- <span id="page-19-1"></span>[140] J. A. Konstan and J. Riedl, "Recommender systems: from algorithms to user experience," *User modeling and user-adapted interaction*, vol. 22, no. 1, pp. 101–123, 2012. [12](#page-11-0)
- <span id="page-19-2"></span>[141] F. O. Isinkaye, Y. O. Folajimi, and B. A. Ojokoh, "Recommendation systems: Principles, methods and evaluation," *Egyptian informatics journal*, vol. 16, no. 3, pp. 261–273, 2015. [12](#page-11-0)
- <span id="page-19-3"></span>[142] A. Narayanan and V. Shmatikov, "Robust de-anonymization of large sparse datasets," in *2008 IEEE Symposium on Security and Privacy (sp 2008)*. IEEE, 2008, pp. 111–125. [12](#page-11-0)
- <span id="page-19-4"></span>[143] N. Sachdeva, M. P. Dhaliwal, C.-J. Wu, and J. McAuley, "Infinite recommendation networks: A data-centric approach," *arXiv preprint arXiv:2206.02626*, 2022. [12](#page-11-0)
- <span id="page-19-5"></span>[144] S. Minaee, N. Kalchbrenner, E. Cambria, N. Nikzad, M. Chenaghlu, and J. Gao, "Deep learning–based text classification: a comprehensive review," *ACM Computing Surveys (CSUR)*, vol. 54, no. 3, pp. 1–40, 2021. [12](#page-11-0)
- <span id="page-19-6"></span>[145] T. Brown, B. Mann, N. Ryder, M. Subbiah, J. D. Kaplan, P. Dhariwal, A. Neelakantan, P. Shyam, G. Sastry, A. Askell *et al.*, "Language models are few-shot learners," *Advances in neural information processing systems*, vol. 33, pp. 1877–1901, 2020. [12](#page-11-0)
- <span id="page-19-7"></span>[146] Y. Li and W. Li, "Data distillation for text classification," *arXiv preprint arXiv:2104.08448*, 2021. [12](#page-11-0)
- <span id="page-19-8"></span>[147] Y. Tian, D. Krishnan, and P. Isola, "Contrastive representation distillation," *arXiv preprint arXiv:1910.10699*, 2019. [12](#page-11-0)
- <span id="page-19-9"></span>[148] P. Chen, S. Liu, H. Zhao, and J. Jia, "Distilling knowledge via knowledge review," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2021, pp. 5008–5017. [12](#page-11-0)
- <span id="page-19-10"></span>[149] C. Li, M. Lin, Z. Ding, N. Lin, Y. Zhuang, Y. Huang, X. Ding, and L. Cao, "Knowledge condensation distillation," in *European Conference on Computer Vision*. Springer, 2022, pp. 19–35. [12](#page-11-0)
- <span id="page-19-11"></span>[150] R. Kumar, W. Wang, J. Kumar, T. Yang, A. Khan, W. Ali, and I. Ali, "An integration of blockchain and ai for secure data sharing and detection of ct images for the hospitals," *Computerized Medical Imaging and Graphics*, vol. 87, p. 101812, 2021. [12](#page-11-0)
- <span id="page-19-12"></span>[151] E. R. Weitzman, L. Kaci, and K. D. Mandl, "Sharing medical data for health research: the early personal health record experience," *Journal of medical Internet research*, vol. 12, no. 2, p. e1356, 2010. [12](#page-11-0)
- <span id="page-19-13"></span>[152] G. A. Kaissis, M. R. Makowski, D. Rückert, and R. F. Braren, "Secure, privacy-preserving and federated machine learning in medical imaging," *Nature Machine Intelligence*, vol. 2, no. 6, pp. 305–311, 2020. [12](#page-11-0)
- <span id="page-19-14"></span>[153] G. Li, R. Togo, T. Ogawa, and M. Haseyama, "Soft-label anonymous gastric x-ray image distillation," in *2020 IEEE International Conference on Image Processing (ICIP)*. IEEE, 2020, pp. 305–309. [12](#page-11-0)
- <span id="page-19-15"></span>[154] Li, Guang and Togo, Ren and Ogawa, Takahiro and Haseyama, Miki, "Dataset distillation for medical dataset sharing," *arXiv preprint arXiv:2209.14603*, 2022. [12](#page-11-0)
- <span id="page-19-16"></span>[155] Li, Guang and Togo, Ren and Ogawa, Takahiro and Haseyama, Miki, "Compressed gastric image generation based on soft-label dataset distillation for medical data sharing," *Computer Methods and Programs in Biomedicine*, vol. 227, p. 107189, 2022. [12](#page-11-0)
- <span id="page-19-17"></span>[156] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Wearable imagenet: Synthesizing tileable textures via dataset distillation," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 2278–2282. [12](#page-11-0)
- <span id="page-19-18"></span>[157] Y. Chen, Z. Wu, Z. Shen, and J. Jia, "Learning from designers: Fashion compatibility analysis via dataset distillation," in *2022 IEEE International Conference on Image Processing (ICIP)*. IEEE, 2022, pp. 856–860. [13](#page-12-1)
- <span id="page-19-19"></span>[158] Y. LeCun, L. Bottou, Y. Bengio, and P. Haffner, "Gradient-based learning applied to document recognition," *Proceedings of the IEEE*, vol. 86, no. 11, pp. 2278–2324, 1998. [13,](#page-12-1) [20](#page-19-29)
- <span id="page-19-20"></span>[159] H. Xiao, K. Rasul, and R. Vollgraf, "Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms," *arXiv preprint arXiv:1708.07747*, 2017. [13,](#page-12-1) [20](#page-19-29)
- <span id="page-19-21"></span>[160] A. Krizhevsky, G. Hinton *et al.*, "Learning multiple layers of features from tiny images," 2009. [13,](#page-12-1) [20](#page-19-29)
- <span id="page-19-22"></span>[161] Y. Le and X. Yang, "Tiny imagenet visual recognition challenge," *CS 231N*, vol. 7, no. 7, p. 3, 2015. [13,](#page-12-1) [20](#page-19-29)

- <span id="page-19-23"></span>[162] K. Simonyan and A. Zisserman, "Very deep convolutional networks for large-scale image recognition," *arXiv preprint arXiv:1409.1556*, 2014. [13](#page-12-1)
- <span id="page-19-24"></span>[163] O. Russakovsky, J. Deng, H. Su, J. Krause, S. Satheesh, S. Ma, Z. Huang, A. Karpathy, A. Khosla, M. Bernstein *et al.*, "Imagenet large scale visual recognition challenge," *International journal of computer vision*, vol. 115, no. 3, pp. 211–252, 2015. [15,](#page-14-3) [20](#page-19-29)
- <span id="page-19-25"></span>[164] J. Long, E. Shelhamer, and T. Darrell, "Fully convolutional networks for semantic segmentation," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2015, pp. 3431–3440. [16](#page-15-8)
- <span id="page-19-26"></span>[165] L.-C. Chen, G. Papandreou, I. Kokkinos, K. Murphy, and A. L. Yuille, "Deeplab: Semantic image segmentation with deep convolutional nets, atrous convolution, and fully connected crfs," *IEEE transactions on pattern analysis and machine intelligence*, vol. 40, no. 4, pp. 834–848, 2017. [16](#page-15-8)
- <span id="page-19-27"></span>[166] R. Girshick, J. Donahue, T. Darrell, and J. Malik, "Rich feature hierarchies for accurate object detection and semantic segmentation," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2014, pp. 580–587. [16](#page-15-8)
- <span id="page-19-28"></span>[167] D. Bahdanau, K. Cho, and Y. Bengio, "Neural machine translation by jointly learning to align and translate," *arXiv preprint arXiv:1409.0473*, 2014. [16](#page-15-8)
- <span id="page-19-30"></span>[168] Y. Netzer, T. Wang, A. Coates, A. Bissacco, B. Wu, and A. Y. Ng, "Reading digits in natural images with unsupervised feature learning," 2011. [20](#page-19-29)

# **APPENDIX A TAXONOMY OF EXISTING DD METHODS**

In Table [5,](#page-20-1) we provide a tablular version of the taxonomy shown in Fig. 5 of the main manuscript, to provide an alternative view with more details. A DD method can be analyzed from 4 aspects: optimization objective, fashion of updating networks, synthetic data parameterization, and fashion of learning labels.

# **APPENDIX B PERFORMANCE OF EXISTING DD METHODS**

Here, we list the distillation performance of all existing DD methods on benchmark datasets with different IPC in Table [6](#page-20-2) - Table [12.](#page-22-1) The benchmark datasets include MNIST [\[158\]](#page-19-19), Fashion-MNIST [\[159\]](#page-19-20), SVHN [\[168\]](#page-19-30), CIFAR-10 [\[160\]](#page-19-21), CIFAR-100 [\[160\]](#page-19-21), Tiny-ImageNet [\[161\]](#page-19-22) and ImageNet-1K [\[163\]](#page-19-24). Results are derived from official papers, DC-Benchmark [\[75\]](#page-17-25) and our experiments.

TABLE 5

Taxonomy of existing dataset distillation methods. 'v' denotes some variant of an optimization objective here.

<span id="page-20-1"></span><span id="page-20-0"></span>

| Method         | Objective                          | Network Update                                                | Parameterization         | Label     |
|----------------|------------------------------------|---------------------------------------------------------------|--------------------------|-----------|
| DD [18]        | Performance Matching-Meta          | Use $S$ (Unroll)                                              | Raw                      | Fixed     |
| AddMem[84]     | Performance Matching-Meta          | Use $S$ (Unroll)                                              | Memory-Addressing Matrix | Learnable |
| KIP [20], [21] | Performance Matching-KRR           | Do not Update Network                                         | Raw                      | Learnable |
| FRePo [19]     | Performance Matching-KRR           | Use $S$ with a Network Pool                                   | Raw                      | Learnable |
| RFAD [85]      | Performance Matching-KRR           | Do not Update Network                                         | Raw                      | Learnable |
| DC [22]        | Single-Step Parameter Matching     | Use $S$                                                       | Raw                      | Fixed     |
| DSA [23]       | Single-Step Parameter Matching     | Use $S$                                                       | DSA                      | Fixed     |
| IDC [82]       | Single-Step Parameter Matching     | Use $T$                                                       | Up-sampling & DSA        | Fixed     |
| EDD [93]       | Single-Step Parameter Matching     | Use $T$ and Model Augmentation                                | Up-sampling & DSA        | Fixed     |
| DCC [79]       | v - Single-Step Parameter Matching | Use $S$                                                       | DSA / Raw                | Fixed     |
| EGM [80]       | v - Single-Step Parameter Matching | Use $S$                                                       | Raw                      | Fixed     |
| MTT [81]       | Multi-Step Parameter Matching      | Use $T$ (Cached) and $S$ (Unroll)                             | DSA                      | Fixed     |
| DDPP [94]      | Multi-Step Parameter Matching      | Use $T$ (Cached), $S$ (Unroll) and Pa-<br>rameter Pruning     | DSA                      | Fixed     |
| HaBa [99]      | Multi-Step Parameter Matching      | Use $T$ (Cached) and $S$ (Unroll)                             | Hallucinator-Basis & DSA | Fixed     |
| FTD [96]       | Multi-Step Parameter Matching      | Use $T$ with Flat Regularization<br>(Cached) and $S$ (Unroll) | DSA                      | Fixed     |
| TESLA[95]      | v - Multi-Step Parameter Matching  | Use $T$ (Cached) and $S$                                      | DSA                      | Soft      |
| DM [24]        | Single-Layer Distribution Matching | Do not Update Network                                         | DSA                      | Fixed     |
| IT-GAN [67]    | Single-Layer Distribution Matching | Do not Update Network                                         | Latent Code-GAN          | Fixed     |
| KFS [70]       | Single-Layer Distribution Matching | Do not Update Network                                         | Latent Code-Decoder      | Fixed     |
| CAFE [97]      | Multi-Layer Distribution Matching  | Use $S$                                                       | Raw / DSA                | Fixed     |

 $\overline{\phantom{0}}$ 

<span id="page-20-2"></span>TABLE 6 The performance results of all existing methods on MNIST. † adopt momentum [\[84\]](#page-17-34).

TABLE 7 The performance results of all existing methods on Fashion-MNIST. † adopt momentum [\[84\]](#page-17-34).

|               |                              |                | <b>IPC</b>     |                |
|---------------|------------------------------|----------------|----------------|----------------|
|               |                              | $\mathbf{1}$   | 10             | 50             |
|               | <b>DD</b> [18]               |                | $79.5 + 8.1$   |                |
|               | DD <sup>†</sup> [18], [84]   | $95.2 \pm 0.3$ | $98.0 \pm 0.1$ | $98.8 \pm 0.1$ |
|               | AddMem <sup>[84]</sup>       | $98.7 \pm 0.7$ | $99.3 \pm 0.5$ | $99.4 \pm 0.4$ |
|               | KIP (ConvNet) [21]           | $90.1 \pm 0.1$ | $97.5 + 0.0$   | $98.3 \pm 0.1$ |
|               | KIP ( $\infty$ -FC) [20]     | $85.5 \pm 0.1$ | $97.2 \pm 0.2$ | $98.4 \pm 0.1$ |
|               | KIP ( $\infty$ -Conv) [21]   | $97.3 \pm 0.1$ | $99.1 \pm 0.1$ | $99.5 \pm 0.1$ |
|               | FRePo (ConvNet) [19]         | $93.8 \pm 0.6$ | $98.4 \pm 0.1$ | $99.2 \pm 0.1$ |
|               | FRePo ( $\infty$ -Conv) [19] | $93.6 \pm 0.5$ | $98.5 + 0.1$   | $99.1 \pm 0.1$ |
|               | RFAD (ConvNet) [85]          | $94.4 \pm 1.5$ | $98.5 \pm 0.1$ | $98.8 \pm 0.1$ |
|               | RFAD ( $\infty$ -Conv) [85]  | $97.2 \pm 0.2$ | $99.1 \pm 0.0$ | $99.1 \pm 0.0$ |
|               | DC [22]                      | $91.7 \pm 0.5$ | $97.4 \pm 0.2$ | $98.8 \pm 0.2$ |
| <b>NNIST</b>  | <b>DSA</b> [23]              | $88.7 \pm 0.6$ | $97.9 \pm 0.1$ | $99.2 \pm 0.1$ |
|               | <b>IDC</b> [82]              |                |                |                |
|               | <b>EDD</b> [93]              |                |                |                |
|               | <b>DCC</b> [79]              |                |                |                |
|               | <b>EGM</b> [80]              | $91.9 \pm 0.4$ | $97.9 \pm 0.2$ | $98.6 \pm 0.1$ |
|               | <b>MTT</b> [81]              | $91.4 \pm 0.9$ | $97.3 \pm 0.1$ | $98.5 \pm 0.1$ |
|               | <b>DDPP</b> [94]             |                |                |                |
|               | HaBa [99]                    |                |                |                |
|               | <b>FTD</b> [96]              |                |                |                |
|               | <b>TESLA</b> [95]            |                |                |                |
|               | DM [24]                      | $89.9 \pm 0.8$ | $97.6 \pm 0.1$ | $98.6 \pm 0.1$ |
|               | IT-GAN $[67]$                |                |                |                |
|               | <b>KFS [70]</b>              |                |                |                |
|               | <b>CAFE</b> [97]             | $93.1 \pm 0.3$ | $97.5 \pm 0.1$ | $98.9 \pm 0.2$ |
|               |                              |                | <b>IPC</b>     |                |
|               |                              | 1              | 10             | 50             |
|               | DD[18]                       |                |                |                |
|               | DD <sup>†</sup> [18], [84]   | $83.4 \pm 0.3$ | $87.6 + 0.4$   | $87.7 \pm 0.3$ |
|               | AddMem <sup>[84]</sup>       | $88.5 \pm 0.1$ | $90.0 \pm 0.7$ | $91.2 \pm 0.3$ |
|               | KIP (ConvNet) [21]           | $70.6 \pm 0.6$ | $84.6 \pm 0.3$ | $88.7 \pm 0.2$ |
|               | KIP ( $\infty$ -FC) [20]     |                |                |                |
|               | KIP ( $\infty$ -Conv) [21]   | $82.9 \pm 0.2$ | $91.0 \pm 0.1$ | $92.4 \pm 0.1$ |
|               | FRePo (ConvNet) [19]         | $75.6 \pm 0.5$ | $86.2 \pm 0.3$ | $89.6 \pm 0.1$ |
|               | FRePo ( $\infty$ -Conv) [19] | $76.3 \pm 0.4$ | $86.7 \pm 0.3$ | $90.0 \pm 0.0$ |
|               | RFAD (ConvNet) [85]          | $78.6 \pm 1.3$ | $87.0 \pm 0.5$ | $88.8 \pm 0.4$ |
|               | RFAD ( $\infty$ -Conv) [85]  | $84.6 \pm 0.2$ | $90.3 \pm 0.2$ | $91.4 \pm 0.1$ |
| Fashion-MNIST | DC [22]                      | $70.5 \pm 0.6$ | $82.3 \pm 0.4$ | $83.6 \pm 0.4$ |
|               | <b>DSA</b> [23]              | $70.6 \pm 0.6$ | $84.8 \pm 0.3$ | $88.8 \pm 0.2$ |
|               | <b>IDC</b> [82]              |                |                |                |
|               | <b>EDD</b> [93]              |                |                |                |
|               | DCC [79]                     |                |                |                |
|               | <b>EGM [80]</b>              | $71.4 + 0.4$   | $85.4 \pm 0.3$ | $87.9 \pm 0.2$ |
|               | <b>MTT</b> [81]              | $75.1 \pm 0.9$ | $87.2 \pm 0.3$ | $88.3 \pm 0.1$ |
|               | <b>DDPP</b> [94]             |                |                |                |
|               | HaBa $[99]$                  |                |                |                |
|               | <b>FTD [96]</b>              |                |                |                |
|               | <b>TESLA</b> [95]            |                |                |                |
|               | DM [24]                      | $71.5 \pm 0.5$ | $83.6 \pm 0.2$ | $88.2 \pm 0.1$ |
|               | IT-GAN $[67]$                |                |                |                |
|               | <b>KFS [70]</b>              |                |                |                |
|               | <b>CAFE</b> [97]             | $73.7 \pm 0.7$ | $83.0 \pm 0.4$ | $88.2 \pm 0.3$ |

<span id="page-21-0"></span>TABLE 8 The performance results of all existing methods on SVHN. † adopt momentum [\[84\]](#page-17-34).

TABLE 10 The performance results of all existing methods on CIFAR-100. † adopt momentum [\[84\]](#page-17-34).

|                  |                               |                | <b>IPC</b>     |                |
|------------------|-------------------------------|----------------|----------------|----------------|
|                  |                               | $\mathbf{1}$   | 10             | 50             |
|                  | DD [18]                       |                |                |                |
|                  | $DD^{\dagger}$ [18], [84]     |                |                |                |
|                  | AddMem <sup>[84]</sup>        | $87.3 \pm 0.1$ | $89.1 \pm 0.2$ | $89.5 \pm 0.2$ |
|                  | KIP (ConvNet) [21]            | $57.3 \pm 0.1$ | $75.0 \pm 0.1$ | $80.5 \pm 0.1$ |
|                  | KIP ( $\infty$ -FC) [20]      |                |                |                |
|                  | KIP ( $\infty$ -Conv) [21]    | $64.3 \pm 0.4$ | $81.1 \pm 0.5$ | $84.3 \pm 0.1$ |
|                  | FRePo (ConvNet) [19]          |                |                |                |
|                  | FRePo ( $\infty$ -Conv) [19]  |                |                |                |
|                  | RFAD (ConvNet) [85]           | $52.2 \pm 2.2$ | $74.9 \pm 0.4$ | $80.9 \pm 0.3$ |
|                  | $RFAD$ ( $\infty$ -Conv) [85] | $57.4 \pm 0.8$ | $78.2 \pm 0.5$ | $82.4 \pm 0.1$ |
|                  | DC[22]                        | $31.2 \pm 1.4$ | $76.1 \pm 0.6$ | $82.3 \pm 0.3$ |
|                  | <b>DSA</b> [23]               | $27.5 \pm 1.4$ | $79.2 \pm 0.5$ | $84.4 \pm 0.4$ |
| <b>SVHN</b>      | <b>IDC</b> [82]               |                |                |                |
|                  | EDD [93]                      |                |                |                |
|                  | <b>DCC</b> [79]               | $47.5 \pm 2.6$ | $80.5 \pm 0.6$ | $87.2 \pm 0.3$ |
|                  | <b>EGM [80]</b>               | $34.5 \pm 1.9$ | $76.2 \pm 0.7$ | $83.8\pm0.3$   |
|                  | <b>MTT</b> [81]               |                |                |                |
|                  | <b>DDPP</b> [94]              |                |                |                |
|                  | HaBa [99]                     | $69.8 \pm 1.3$ | $83.2 \pm 0.4$ | $88.3 \pm 0.1$ |
|                  | FTD [96]                      |                |                |                |
|                  | <b>TESLA</b> [95]             |                |                |                |
|                  | DM [24]                       |                |                |                |
|                  | IT-GAN $[67]$                 |                |                |                |
|                  | <b>KFS [70]</b>               |                |                |                |
|                  | <b>CAFE</b> [97]              | $42.9 \pm 3.0$ | $77.9 \pm 0.6$ | $82.3 \pm 0.4$ |
|                  |                               | IPC            |                |                |
|                  |                               | 1              | 10             | 50             |
|                  | DD [18]                       | -              | -              | -              |
|                  | $DD^{\dagger}$ [18], [84]     | $19.6 \pm 0.4$ | $32.7 \pm 0.4$ | $35.0 \pm 0.3$ |
|                  | AddMem [84]                   | $34.0 \pm 0.4$ | $42.9 \pm 0.7$ | -              |
|                  | KIP (ConvNet) [21]            | $15.7 \pm 0.2$ | $28.3 \pm 0.1$ | -              |
|                  | KIP ( $\infty$ -FC) [20]      | -              | -              | -              |
|                  | KIP ( $\infty$ -Conv) [21]    | $34.9 \pm 0.1$ | $49.5 \pm 0.3$ | -              |
|                  | FRePo (ConvNet) [19]          | $27.2 \pm 0.4$ | $41.3 \pm 0.2$ | $44.3 \pm 0.2$ |
|                  | FRePo ( $\infty$ -Conv) [19]  | $30.4 \pm 0.3$ | $42.1 \pm 0.2$ | $43.0 \pm 0.3$ |
|                  | RFAD (ConvNet) [85]           | $26.3 \pm 1.1$ | $33.0 \pm 0.3$ | -              |
|                  | RFAD ( $\infty$ -Conv) [85]   | $44.1 \pm 0.1$ | $46.8 \pm 0.2$ | -              |
| <b>CIFAR-100</b> | DC [22]                       | $12.6 + 0.4$   | $25.4 \pm 0.3$ | $29.7 \pm 0.3$ |
|                  | DSA [23]                      | $13.9 \pm 0.3$ | $32.3 \pm 0.3$ | $42.8 \pm 0.4$ |
|                  | IDC [82]                      | -              | $44.8 \pm 0.2$ | -              |
|                  | EDD [93]                      | $29.8 \pm 0.2$ | $46.2 \pm 0.3$ | $52.6 + 0.4$   |
|                  | DCC [79]                      | $14.6 \pm 0.3$ | $33.5 \pm 0.3$ | $40.0 \pm 0.3$ |
|                  | EGM [80]                      | $12.7 \pm 0.4$ | $31.1 \pm 0.3$ | -              |
|                  | MTT [81]                      | $24.3 \pm 0.3$ | $40.1 \pm 0.4$ | $47.7 \pm 0.2$ |
|                  | DDPP [94]                     | $24.6 \pm 0.1$ | $43.1 \pm 0.3$ | $48.4 \pm 0.3$ |
|                  | HaBa [99]                     | $33.4 \pm 0.4$ | $40.2 \pm 0.2$ | $47.0 \pm 0.2$ |
|                  | FTD [96]                      | $25.2 \pm 0.2$ | $43.4 \pm 0.3$ | $50.7 \pm 0.3$ |
|                  | TESLA [95]                    | $24.8 \pm 0.4$ | $41.7 \pm 0.3$ | $47.9 \pm 0.3$ |
|                  | DM [24]                       | $11.4 \pm 0.3$ | $29.7 \pm 0.3$ | $43.6 \pm 0.4$ |
|                  | IT-GAN [67]                   | -              | -              | -              |
|                  | KFS [70]                      | $40.0 \pm 0.5$ | $50.6 \pm 0.2$ | -              |
|                  | CAFE [97]                     | $14.0 \pm 0.3$ | $31.5 \pm 0.2$ | $42.9 \pm 0.2$ |

TABLE 9 The performance results of all existing methods on CIFAR-10. † adopt momentum [\[84\]](#page-17-34).

TABLE 11 The performance results of all existing methods on Tiny-ImageNet. † adopt momentum [\[84\]](#page-17-34).

|            |                              |                |                |                |                              | <b>IPC</b>                |                |                |                |
|------------|------------------------------|----------------|----------------|----------------|------------------------------|---------------------------|----------------|----------------|----------------|
|            |                              |                | <b>IPC</b>     |                |                              |                           | <b>1</b>       | <b>10</b>      | <b>50</b>      |
|            |                              | 1              | 10             | 50             |                              | DD [18]                   | -              | -              | -              |
|            | <b>DD</b> [18]               |                | $36.8 + 1.2$   |                |                              | $DD^{\dagger}$ [18], [84] | -              | -              | -              |
|            | $DD^{\dagger}$ [18], [84]    | $46.6 \pm 0.6$ | $60.2 + 0.4$   | $65.3 + 0.4$   |                              | AddMem [84]               | -              | -              | -              |
|            | AddMem <sup>[84]</sup>       | $66.4 \pm 0.4$ | $71.2 \pm 0.4$ | $73.6 \pm 0.5$ |                              | KIP (ConvNet) [21]        | -              | -              | -              |
|            | KIP (ConvNet) [21]           | $49.9 \pm 0.2$ | $62.7 \pm 0.3$ | $68.6 \pm 0.2$ |                              | KIP ( $\infty$ -FC) [20]  | -              | -              | -              |
|            | KIP ( $\infty$ -FC) [20]     | $40.5 \pm 0.4$ | $53.1 \pm 0.5$ | $58.6 \pm 0.4$ | KIP ( $\infty$ -Conv) [21]   | -                         | -              | -              |                |
|            | KIP ( $\infty$ -Conv) [21]   | $64.3 \pm 0.4$ | $81.1 \pm 0.5$ | $84.3 \pm 0.1$ | FRePo (ConvNet) [19]         | $15.4 \pm 0.3$            | $24.9 \pm 0.2$ | -              |                |
|            | FRePo (ConvNet) [19]         | $46.8 \pm 0.7$ | $65.5 \pm 0.6$ | $71.7 \pm 0.2$ | FRePo ( $\infty$ -Conv) [19] | $17.6 \pm 0.2$            | $25.3 \pm 0.2$ | -              |                |
|            | FRePo ( $\infty$ -Conv) [19] | $47.9 \pm 0.6$ | $68.0 \pm 0.2$ | $74.4 \pm 0.1$ | RFAD (ConvNet) [85]          | -                         | -              | -              |                |
|            | RFAD (ConvNet) [85]          | $53.6 \pm 1.2$ | $66.3 \pm 0.5$ | $71.1 \pm 0.4$ | RFAD ( $\infty$ -Conv) [85]  | -                         | -              | -              |                |
|            | RFAD ( $\infty$ -Conv) [85]  | $61.4 \pm 0.8$ | $73.7 \pm 0.2$ | $76.6 \pm 0.3$ | Tiny-ImageNet                | DC [22]                   | $5.3 \pm 0.2$  | $11.1 \pm 0.3$ | $11.2 \pm 0.3$ |
|            | DC [22]                      | $28.3 \pm 0.5$ | $44.9 \pm 0.5$ | $53.9 \pm 0.5$ |                              | DSA [23]                  | $6.6 \pm 0.2$  | $16.3 \pm 0.2$ | $25.3 \pm 0.2$ |
|            | <b>DSA</b> [23]              | $28.8 \pm 0.7$ | $53.2 \pm 0.8$ | $60.6 \pm 0.5$ |                              | IDC [82]                  | -              | -              | -              |
| $CIFAR-10$ | <b>IDC</b> [82]              | $50.0 + 0.4$   | $67.5 + 0.5$   | $74.5 \pm 0.1$ |                              | EDD [93]                  | -              | -              | -              |
|            | <b>EDD</b> [93]              | $49.2 \pm 0.4$ | $67.1 \pm 0.2$ | $73.8 \pm 0.1$ |                              | DCC [79]                  | -              | -              | -              |
|            | DCC [79]                     | $34.0 \pm 0.7$ | $54.5 \pm 0.5$ | $64.2 \pm 0.4$ |                              | EGM [80]                  | -              | -              | -              |
|            | <b>EGM</b> [80]              | $30.0 \pm 0.6$ | $50.2 \pm 0.6$ | $60.0 \pm 0.4$ |                              | MTT [81]                  | $8.8 \pm 0.3$  | $23.2 \pm 0.2$ | $28.2 \pm 0.5$ |
|            | <b>MTT</b> [81]              | $46.3 \pm 0.8$ | $65.3 \pm 0.7$ | $71.6 \pm 0.2$ |                              | DDPP [94]                 | -              | -              | -              |
|            | <b>DDPP</b> [94]             | $46.4 + 0.6$   | $65.5 \pm 0.3$ | $71.9 \pm 0.2$ |                              | HaBa [99]                 | -              | -              | -              |
|            | HaBa [99]                    | $48.3 \pm 0.8$ | $69.9 \pm 0.4$ | $74.0 \pm 0.2$ |                              | FTD [96]                  | $10.4 \pm 0.3$ | $24.5 \pm 0.2$ | -              |
|            | <b>FTD</b> [96]              | $46.8 \pm 0.3$ | $66.6 \pm 0.3$ | $73.8 \pm 0.2$ | TESLA [95]                   | -                         | -              | -              |                |
|            | <b>TESLA</b> [95]            | $48.5 \pm 0.8$ | $66.4 + 0.8$   | $72.6 \pm 0.7$ |                              | DM [24]                   | $3.9 \pm 0.2$  | $13.5 \pm 0.3$ | $24.1 \pm 0.3$ |
|            | DM [24]                      | $26.5 \pm 0.4$ | $48.9 \pm 0.6$ | $63.0 \pm 0.4$ |                              | IT-GAN [67]               | -              | -              | -              |
|            | IT-GAN $[67]$                |                |                |                |                              | KFS [70]                  | $22.7 \pm 0.2$ | $27.8 \pm 0.2$ | -              |
|            | <b>KFS [70]</b>              | $59.8 \pm 0.5$ | $72.0 \pm 0.3$ | $75.0 \pm 0.2$ |                              | CAFE [97]                 | -              | -              | -              |
|            | <b>CAFE</b> [97]             | $31.6 \pm 0.8$ | $50.9 \pm 0.5$ | $62.3 \pm 0.4$ |                              |                           |                |                |                |

<span id="page-22-1"></span><span id="page-22-0"></span>TABLE 12 The performance results of all existing methods on ImageNet-1K. † adopt momentum [\[84\]](#page-17-34).

|             |                              | <b>IPC</b>    |                |                |                |
|-------------|------------------------------|---------------|----------------|----------------|----------------|
|             |                              | 1             | 2              | 10             | 50             |
|             | DD [18]                      | -             | -              | -              | -              |
|             | DD† [18], [84]               | -             | -              | -              | -              |
|             | AddMem [84]                  | -             | -              | -              | -              |
|             | KIP (ConvNet) [21]           | -             | -              | -              | -              |
|             | KIP ( $\infty$ -FC) [20]     | -             | -              | -              | -              |
|             | KIP ( $\infty$ -Conv) [21]   | -             | -              | -              | -              |
|             | FRePo (ConvNet) [19]         | $7.5 \pm 0.3$ | $9.7 \pm 0.2$  | -              | -              |
|             | FRePo ( $\infty$ -Conv) [19] | -             | -              | -              | -              |
|             | RFAD (ConvNet) [85]          | -             | -              | -              | -              |
|             | RFAD ( $\infty$ -Conv) [85]  | -             | -              | -              | -              |
|             | DC [22]                      | -             | -              | -              | -              |
| ImageNet-1K | <b>DSA</b> [23]              | -             | -              | -              | -              |
|             | <b>IDC</b> [82]              | -             | -              | -              | -              |
|             | EDD [93]                     | -             | -              | -              | -              |
|             | DCC [79]                     | -             | -              | -              | -              |
|             | <b>EGM</b> [80]              | -             | -              | -              | -              |
|             | <b>MTT</b> [81]              | -             | -              | -              | -              |
|             | DDPP [94]                    | -             | -              | -              | -              |
|             | HaBa [99]                    | -             | -              | -              | -              |
|             | <b>FTD</b> [96]              | -             | -              | -              | -              |
|             | TESLA [95]                   | $7.7 \pm 0.2$ | $10.5 \pm 0.3$ | $17.8 \pm 1.3$ | $27.9 \pm 1.2$ |
|             | DM [24]                      | $1.5 \pm 0.1$ | $1.7 \pm 0.1$  | -              | -              |
|             | IT-GAN [67]                  | -             | -              | -              | -              |
|             | <b>KFS</b> [70]              | -             | -              | -              | -              |
|             | <b>CAFE</b> [97]             | -             | -              | -              | -              |
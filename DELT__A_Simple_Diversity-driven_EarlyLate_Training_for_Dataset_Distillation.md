# <span id="page-0-1"></span>DELT: A Simple Diversity-driven **EarlyLate** Training for Dataset Distillation

Zhiqiang Shen\*

<PERSON><PERSON><PERSON><PERSON>\* <PERSON>mar <PERSON>f\* <PERSON><PERSON><PERSON> Yin Shitong Shao

Shtong Shao

VILA Lab, MBZUAI

{zhiqiang.shen, zeyuan.yin}@mbzuai.ac.ae {ammarsherif90, 1090784053sst}@gmail.com

# Abstract

*Recent advances in dataset distillation have led to solutions in two main directions. The conventional batch-to-batch matching mechanism is ideal for small-scale datasets and includes bi-level optimization methods on models and syntheses, such as FRePo, RCIG, and RaT-BPTT, as well as other methods like distribution matching, gradient matching, and weight trajectory matching. Conversely, batchto-global matching typifies decoupled methods, which are particularly advantageous for large-scale datasets. This approach has garnered substantial interest within the community, as seen in SRe*2*L, G-VBSM, WMDD, and CDA. A primary challenge with the second approach is the lack of diversity among syntheses within each class since samples are optimized independently and the same global supervision signals are reused across different synthetic images. In this study, we propose a new Diversity-driven E*arly*L*ate *Training (DELT) scheme to enhance the diversity of images in batch-to-global matching with less computation. Our approach is conceptually simple yet effective, it partitions predefined IPC samples into smaller subtasks and employs local optimizations to distill each subset into distributions from distinct phases, reducing the uniformity induced by the unified optimization process. These distilled images from the subtasks demonstrate effective generalization when applied to the entire task. We conduct extensive experiments on CIFAR, Tiny-ImageNet, ImageNet-1K, and its sub-datasets. Our approach outperforms the previous state-of-the-art by 2*∼*5% on average across different datasets and IPCs (images per class), increasing diversity per class by more than 5% while reducing synthesis time by up to 39.3% for enhancing the training efficiency.*

# 1. Introduction

In the era of large models and large datasets, dataset distillation has emerged as a crucial strategy to enhance training efficiency and make advanced technologies more ac-

<span id="page-0-0"></span>Image /page/0/Figure/12 description: This figure compares two methods: Prior Dataset Distillation and Our DELT Distillation. Both methods involve a 'Full' set of bird images and generate 'IPC1:N' outputs through a 'batch-to-global matching' process with intermediate representations labeled IPC1, IPC2, ..., IPCN, each transformed by a factor T. The 'Prior' method uses N x T computation, while 'Our' method uses T + (T - RI) ... + RI computation. The bottom part of the figure illustrates 'Our DELT generated images' with 'Early-optimized image' (IPC1) and 'Late-optimized image' (IPCN) shown as a sequence of generated images.

Figure 1. Distilling datasets to IPC<sub>N</sub> requires  $N \times T$  iterations in traditional distillation processes (left) but fewer iterations in our EarlyLate strategy (right). IPC $_{1:N}$  represents a set of images from 1 to N. The red shaded area is our saved computational cost.

cessible and affordable for the general public. Previous approaches [\[3,](#page-8-0) [4,](#page-8-1) [6,](#page-8-2) [14,](#page-8-3) [18,](#page-8-4) [21,](#page-8-5) [36,](#page-9-0) [42,](#page-9-1) [43,](#page-9-2) [47\]](#page-9-3) primarily employ a *batch-to-batch* matching technique, where information like features, gradients, and trajectories from a local original data batch are used to supervise and train a corresponding batch of generated data. The strength of this method lies in its ability to capture fine-grained information from the original data, as each batch's supervision signals vary. However, the downside is the necessity to repeatedly input both original and generated data for each training iteration, which significantly increases memory usage and computational costs. Recently, a new decoupled method [\[19,](#page-8-6) [39,](#page-9-4) [40\]](#page-9-5) has been proposed to separate the model training and data synthesis, also it leverages the *batch-to-global* matching to avoid inputting original data during distilled data generation. This solution has demonstrated great advantage on large-scale datasets like ImageNet-1K [\[28,](#page-9-6) [40\]](#page-9-5) and ImageNet-21K [\[39\]](#page-9-4). However, as shown in Fig. [2,](#page-1-0) a significant limitation of this approach is the lack of diversity caused by the mechanism of synthesizing each data point individually, where supervision is repetitively applied across various synthetic images. For instance,  $SRe^{2}L$  [\[40\]](#page-9-5) utilizes globally-counted layer-wise

<sup>\*</sup>Equal contribution. Work done while Ammar and Shitong visiting MBZUAI. Code is at: <https://github.com/VILA-Lab/DELT>.

<span id="page-1-3"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays a line graph on the left and three rows of sample images on the right. The line graph plots "Cosine Similarity" on the y-axis against "Class Index" on the x-axis, ranging from 0 to 1000. Four lines represent different methods: SRe2L (green), CDA (blue), RDED (orange), and DELT (red), each with shaded areas indicating variability. The top row on the right shows sample images labeled "(1) SRe2L". The middle row shows sample images labeled "(2) CDA". The bottom row shows sample images labeled "(3) Ours [1x1 initialized]". The sample images appear to be of birds, with varying degrees of detail and color saturation.

Figure 2. Left: Intra-class semantic cosine similarity after a pretrained ResNet-18 model on ImageNet-1K dataset, lower values are better. **Right**: Synthetic images from SRe<sup>2</sup>L, CDA and our DELT.

running means and variances from the pre-trained model for supervising different intra-class image synthesis. This methodology results in a severely limited diversity within the same category of generated images.

To address this issue, a few prior studies [\[28,](#page-9-6) [32\]](#page-9-7) have proposed to enlarge diversity within each class. For instance, G-VBSM [\[28\]](#page-9-6) utilizes a diverse set of *local-matchglobal* matching signals derived from multiple backbones and statistical metrics, to achieve more precise and effective matching than the singular model. However, as the diversity of matching models grows, the overall complexity of the framework also increases thus diminishing its conciseness. RDED [\[32\]](#page-9-7) crops each original image into multiple patches and ranks these using realism scores generated by an observer model. Then it stitches every four chosen patches from previous stage into a single new image to produce IPC-numbered distilled images for each class. RDED is efficient to combine multiple images but does not enhance or optimize the visual content on the distilled dataset, thus the diversity and richness of information are largely dependent on the distribution of the original dataset.

Our solution, termed the EarlyLate training scheme, is straightforward and also orthogonal to these prior methods: by initializing each image in the same category at a different starting point for optimization, we ensure that the final optimized results vary significantly across images. We also use teacher-ranked real image patches to initialize the synthetic images. This prevents some images from being short-optimized and ensures they provide sufficient information. As shown in Fig. [1](#page-0-0) of the computation comparison, our approach not only enhances intra-class diversity but also dramatically reduces the computational load of the training process by 39.3% on ImageNet-1K. Specifically, while conventional training requires  $T$  optimization iterations per image or batch, in our EarlyLate scheme, the first image undergoes  $T_1$  iterations (where  $T_1 = T$ ). Subsequent batches are processed with progressively fewer iterations, such as  $T_2$  ( $T_2 = T_1 - RI^1$  $T_2 = T_1 - RI^1$  $T_2 = T_1 - RI^1$ ) for the next set, and so forth. The iterations for the final batch are reduced to RI

<span id="page-1-1"></span> ${}^{1}$ RI is the number of round iterations and will be introduced in Sec. [4.3.](#page-4-0)

which is  $1/j$  of the standard count (where typically  $j = 4$ ) or 8), meaning the total number of optimization iterations required is just about 2/3 of prior *batch-to-global* matching methods, such as  $SRe<sup>2</sup>L$  and CDA. We further visualize the average cosine similarity between each sample of 50 IPCs using the associated cluster centroid within the same class on ImageNet-1K, as shown in Fig. [2](#page-1-0) left subfigure, our DELT illustrates a smaller similarity significantly, and also shows substantially better visual diversity than other counterparts across all classes, as in the right subfigure of Fig. [2.](#page-1-0)

We conduct extensive experiments on various datasets of CIFAR-10, Tiny-ImageNet, ImageNet-1K and its subsets. On ImageNet-1K, our proposed approach achieves 66.1% under IPC 50 with ResNet-101, outperforming previous state-of-the-art RDED by 4.9%. On small-scale datasets of CIFAR-10, our approach also obtains 2.5% and 19.2% improvement over RDED and SRe<sup>2</sup>L using ResNet-101.

Our main contributions in this work are as follows:

- We propose a simple yet effective EarlyLate training scheme for dataset distillation to enhance intra-class diversity of synthetic images for *batch-to-global* matching.
- We demonstrate empirically that the proposed method can generate optimized images at different distances with a fast speed, to enlarge informativeness among generations.
- We conducted extensive experiments and ablations on various datasets across different scales to prove the ef-fectiveness of the proposed approach<sup>[2](#page-1-2)</sup>.

## 2. Related Work

Dataset Distillation. Dataset distillation or condensation [\[36\]](#page-9-0) focuses on creating a compact yet representative subset from a large original dataset. This enables more efficient model training while maintaining the ability to evaluate on the original test data distribution and achieve satisfactory performance. Previous works [\[3,](#page-8-0) [4,](#page-8-1) [6,](#page-8-2) [14,](#page-8-3) [18,](#page-8-4) [21,](#page-8-5) [36,](#page-9-0) [42,](#page-9-1) [43,](#page-9-2) [47\]](#page-9-3) mainly designed how to better match the distribution between original data and generated data in a *batch-to-batch* manner, such as the distribution of fea-

<span id="page-1-2"></span> $2$ Our synthetic images on ImageNet-1K are available at [link.](https://drive.google.com/file/d/1Rr_ik94FNte75yc4GiKtv927qdBwKskr/view?usp=sharing)

<span id="page-2-1"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: The image displays a diagram illustrating two methods for matching data batches. The top section, labeled '(1) Batch-to-Batch Matching', shows two input batches, T\_b and S\_b, both feeding into a parameter set labeled 'θ'. From 'θ', outputs labeled 'Gradient/Trajectory/Feature, etc.' are generated for both batches, which then feed into a loss function L\_D. A dashed orange arrow indicates a feedback loop from L\_D back to the input of the parameter set θ. A note indicates that 'b represents a local batch in T and S'. The bottom section, labeled '(2) Batch-to-Global Matching', starts with an 'Original dataset' feeding into a parameter set 'θ' for 'weight pretraining' which results in a loss L\_ce. A blue arrow labeled 'weight sharing' directs from 'θ' to a parameter set 'θ\_f'. 'θ' also feeds into 'Global mean, var statistics'. The batch S\_b feeds into 'θ\_f', which then produces 'Batch mean, var statistics'. Both 'Global mean, var statistics' and 'Batch mean, var statistics' feed into a loss function L\_D. A dashed orange arrow indicates a feedback loop from L\_D back to the input of S\_b.

(2) *Batch–to-Global* Matching

Figure 3. *Batch–to-batch* vs. *batch-to-global* matching in DD. θ<sup>f</sup> indicates weights are pretrained and frozen in synthesis stage.

tures [\[42\]](#page-9-1), gradients [\[43\]](#page-9-2), or the model weight trajectories [\[3,](#page-8-0) [6\]](#page-8-2). The primary optimization method used is bi-level optimization [\[20,](#page-8-7) [41\]](#page-9-8), which involves optimizing model parameters and updating images simultaneously. For instance, using gradient matching, the process can be formulated as to minimize the gradient distance:

$$
\min_{\mathcal{S}\in\mathbb{R}^{N\times d}} D\left(\nabla_{\theta}\ell(\mathcal{S};\theta), \nabla_{\theta}\ell(\mathcal{T};\theta)\right) = D(\mathcal{S},\mathcal{T};\theta),\qquad(1)
$$

where the function  $D(\cdot, \cdot)$  is defined as a distance metric such as MSE [\[37\]](#page-9-9),  $\theta$  denotes the model parameters, and  $\nabla_{\theta} \ell(\cdot; \theta)$  represents the gradient, utilizing either the original dataset  $T$  or its synthetic version  $S$ . N is the number of d-dimensional synthetic data. During distillation, the synthetic dataset S and model  $\theta$  are updated alternatively,

$$
\mathcal{S} \leftarrow \mathcal{S} - \lambda \nabla_{\mathcal{S}} D(\mathcal{S}, \mathcal{T}; \theta), \quad \theta \leftarrow \theta - \eta \nabla_{\theta} \ell(\theta; \mathcal{S}). \quad (2)
$$

where  $\lambda$  and  $\eta$  are learning rates designated for S and  $\theta$ . Diversity in Dataset Distillation. *Batch-to-global* matching used in [\[8,](#page-8-8) [19,](#page-8-6) [28,](#page-9-6) [38](#page-9-10)[–40\]](#page-9-5) tracks the distribution of BN statistics derived from original dataset for the local batch synthetic data. However, this type of approach can easily encounter diversity issues within the same class due to the optimization objective. Fig. [3](#page-2-0) illustrates the difference of *batch-to-batch* and *batch-to-global* matching mechanisms, where b represents a local batch in data  $\mathcal T$  and  $\mathcal S$ .

Moreover, for the recent advances of multi-stage dataset distillation methods, MDC [\[14\]](#page-8-3) proposes to compress multiple condensation processes into a single one by including an adaptive subset loss on top of the basic condensation loss, so that to obtain datasets with multiple sizes. PDD [\[4\]](#page-8-1) generates multiple small batches of synthetic images, each batch is conditioned on the accumulated data from previous batches. Unlike PDD, our current synthetic batch is independent with different operation iterations and not relevant to any previous batches. D3 [\[25\]](#page-8-9) partitions large datasets into smaller subtasks and employs locally trained experts to distill each subset into distributions. These distilled distributions from the subtasks demonstrate effective generalization when applied to the entire task. The recently proposed LPLD [\[38\]](#page-9-10) batches images by class, leveraging the natural independence between classes, and introduces classwise supervision for alignment.

# 3. Approach

the original dataset  $\mathcal T$ . The learning goal using this distilled Preliminaries. The objective of a regular dataset distillation task is to generate a compact synthetic dataset  $S =$  $\{(\hat{x}_1, \hat{y}_1), \ldots, (\hat{x}_{|\mathcal{S}|}, \hat{y}_{|\mathcal{S}|})\}$  as a *student* dataset that captures a substantial amount of the information from a larger labeled dataset  $\mathcal{T} = \{(\boldsymbol{x}_1, \boldsymbol{y}_1), \dots, \big(\boldsymbol{x}_{|\mathcal{T}|}, \boldsymbol{y}_{|\mathcal{T}|}\big)\},$  which serves as the *teacher* dataset. Here,  $\hat{y}$  represents the soft label for the synthetic sample  $\hat{x}$ , and the size of S is much smaller than  $\mathcal T$ , yet it retains the essential information of data is to train a post-validation model with parameters  $\theta$ :

$$
\theta_{\mathcal{S}} = \underset{\theta}{\arg\min} \mathcal{L}_{\mathcal{S}}(\theta),\tag{3}
$$

$$
\mathcal{L}_{\mathcal{S}}(\boldsymbol{\theta}) = \mathbb{E}_{(\hat{\boldsymbol{x}}, \hat{\boldsymbol{y}}) \in \mathcal{S}} \left[ \ell(\phi_{\boldsymbol{\theta}_{\mathcal{S}}}(\hat{\boldsymbol{x}}), \hat{\boldsymbol{y}}; \boldsymbol{\theta}) \right], \tag{4}
$$

where  $\ell$  is a standard loss function such as soft crossentropy and  $\phi_{\theta_{\mathcal{S}}}$  represents the model.

The primary aim of dataset distillation is to produce synthetic data that ensures minimal performance difference between models trained on the synthetic dataset  $S$  and those trained on the original dataset  $T$  using validation data  $V$ . The optimization procedure for generating  $S$  is given by:

$$
\arg\min_{\mathcal{S},|\mathcal{S}|} (\sup \{|\ell(\phi_{\theta_{\mathcal{T}}}(x_{\text{val}}), y_{\text{val}})\n- \ell(\phi_{\theta_{\mathcal{S}}}(x_{\text{val}}), y_{\text{val}})_{(x_{\text{val}}, y_{\text{val}})\sim V}.
$$
\n(5)

where  $(x_{val}, y_{val})$  are the sample and label pairs in the validation set of the real dataset  $\mathcal{T}$ . The learning task then focuses on the  $\langle \text{data}, \text{label} \rangle$  pairs within S, maintaining a balanced representation of distilled data across each class. Initialization. Previous dataset distillation methods [\[28,](#page-9-6) [39,](#page-9-4) [40\]](#page-9-5) on large-scale datasets like ImageNet-1K and 21K employ Gaussian noise by default for data initialization in the synthesis phase. However, Gaussian noise is random and lacks any semantic information. Intuitively, using real images provides a more meaningful and structured starting point, and this structured start can lead to quicker convergence during optimization because the initial data already contains useful features and patterns that are closer to the target distribution, which further enhances realism, quality, and generalization of the synthesized images. As shown in Fig. [2](#page-1-0) right subfigure, our generated images exhibit both diversity and a high degree of realism in some cases.

Selection Criteria. Here, we introduce how to select real image patches to initialize the synthetic images. In our final syntheses, a significant fraction of our data has been subject to limited optimization iterations, making effective initialization crucial. A proper initialization also dramatically

<span id="page-3-3"></span><span id="page-3-1"></span>Image /page/3/Figure/0 description: The figure illustrates a process involving multiple rounds of updates and concatenations. It begins with an initial block labeled 'IPC\_{0:Mk-1}' feeding into a 'Scheduler'. The scheduler then outputs a block labeled 'IPC\_{0:k-1}'. This is followed by a series of rounds, labeled 'Round 1', 'Round 2', and continuing to 'Round M-1'. Each round involves a concatenation node (represented by a plus sign within a circle) that combines two input blocks. For 'Round 1', 'IPC\_{0:k-1}' is concatenated with 'IPC\_{k:2k-1}'. For 'Round 2', the result of 'Round 1' is concatenated with 'IPC\_{2k:3k-1}'. This pattern continues, with each round taking the output of the previous round and concatenating it with a new block, such as 'IPC\_{3k:4k-1}' (implied by the progression). The dashed arrows indicate CDA Gradient updates for T iterations. The legend at the top explains that the plus sign represents a concatenation node, 'IPC\_{a:b}' represents Images Per Class whose rank is in the range [a+1, b+1], and the dashed arrow signifies CDA Gradient updates for T iterations. The final output after 'Round M-1' is a stacked block labeled 'IPC\_{0:Mk-1}', showing a progression of colored layers.

Figure 4. The proposed DELT learning procedure via a multi-round EarlyLate scheme.

**4** original patches can lead to markedly improved results. **Top Medium Selection Criteria** minimizes the overall computational load required for the updating degree on data. Prior approach [\[32\]](#page-9-7) has demonstrated that choosing representative data patches from the original dataset without training can yield favorable performance without any additional training. Our observation, however, underscores that applying iterative refinement to

> **3** all patches' probabilities and sort them as the initialization pool. Then, we choose a patch of images scoring closer **2** to the per-class median as initialization for optimization. As illustrated in Fig. [5,](#page-3-0) our selection criterion is based on a pretrained teacher model as a ranker, we calculate More details regarding initialization and order can be found in Appendix. The motivation is that such images have a medium difficulty level to the teacher, so they have more room for information enhancement via distillation gradients. We further empirically validate this strategy by comparing different strategies in Table [4b.](#page-6-0)

<span id="page-3-0"></span>Image /page/3/Figure/4 description: The image displays a diagram illustrating a selection process using a teacher ranker. On the left, five images are shown, each with a bounding box around a subject. Arrows point from these images to corresponding numbers: 2, 3, 5, 1, and 4. These numbers are then fed into a 'Teacher Ranker'. The output of the teacher ranker is a ranked list of numbers from 1 to 5, each associated with a confidence score: 1 with 0.91, 2 with 0.87, 3 with 0.63, 4 with 0.45, and 5 with 0.23. The numbers 1, 2, and 3 from this ranked list are then used for 'IPC initialization', with arrows pointing from the corresponding ranked numbers (1, 2, and 3) to new images, each with a bounding box around a subject. The figure is captioned 'Figure 5. Selection criteria with a teach ranker'.

Figure 5. Selection criteria with a teach ranker.

in Fig. [4,](#page-3-1) to further emphasize diversity and avoid poten-Diversity-driven IPC Concatenation Training. As shown tial distribution bias from initialization, we optimize the initialized images starting from different points. The motivation behind this design is that different data samples require varying numbers of iterations to converge as the early stopping [\[24\]](#page-8-10) from other research domain. Importantly, as images become easier to predict with more updates by class labels, training primarily on easy data points can hinder model generalization. Therefore, our method enhances generalization by generating data samples with varying difficulty levels, acting as a regularizer by limiting the opti-

conduct a single gradient loop, continually introducing new mization process to a smaller volume of image pixel space. Previous work [\[1\]](#page-8-11) studies how to perform simple early stopping on different layers' weights with progressive retraining to mitigate noisy labels. Unlike it, we are pioneering to study both *early* and *late* training when optimizing data. Moreover, we improve the efficiency of our approach by performing gradient updates in a single scan. Initially, we data for distillation by concatenating them at different time stamps. Consequently, the  $M$  batch receives the synthetic images of all preceding batches, *IPC*<sub>0:Mk−1</sub>, as final generations. This process can be simplified as follows:

$$
IPC_{0:Mk-1} = [\underbrace{\hat{x}_0, \hat{x}_1, \dots, \hat{x}_{k-1}}_{IPC_{0:k-1}}, \dots, \hat{x}_{Mk-1}] \quad (6)
$$

where  $[\hat{x}_0, \hat{x}_1, \dots, \hat{x}_{Mk-1}]$  refers to the concatenation of generated images.  $M$  is the number of batches,  $k$  is the number of generated images in each batch. We train these different batches at different starting points, each batch goes through a complete learning phase, but the total number of iterations varies. Then, the multiple IPCs of  $\hat{x}$  are concatenated into a simple batch. Because of its early-late training property, we refer to this scheme as EarlyLate training. Data synthesis. Our EarlyLate optimization procedure can be formulated as a multi-stage training scheme:

Round 1: 
$$
\underset{\mathcal{C}_{\text{IPC}_{0:k-1}},|\mathcal{C}|}{\arg \min} \ell\left(\phi_{\theta_{\mathcal{T}}}\left(\widetilde{\boldsymbol{x}}_{\text{IPC}_{0:k-1}}\right), \boldsymbol{y}\right) + \mathcal{R}_{\text{reg}}
$$

<span id="page-3-2"></span>Round M-1: 
$$
\underset{\mathcal{C}_{IPC_{0:Mk-1}},|\mathcal{C}|}{\arg \min} \ell \left( \phi_{\theta_{\mathcal{T}}} \left( \widetilde{\mathbf{x}}_{IPC_{0:Mk-1}} \right), \mathbf{y} \right) + \mathcal{R}_{reg}
$$
\n
$$
\tag{7}
$$

...

where  $C$  is the target distilled dataset. The number of batches  $M > 1$  (If  $M = 1$ , training will degenerate into a way without EarlyLate). This process is referred to in Fig. [4](#page-3-1) bottom row.  $\mathcal{R}_{reg}$  is the regularization term, we also <span id="page-4-2"></span>utilize the BatchNorm distribution regularization term as in  $SRe<sup>2</sup>L$  [\[40\]](#page-9-5) to improve the quality of the generated images. Training Procedure. Regarding concatenation training, we elaborate: Our EarlyLate enhances the diversity of the synthetic data by varying the number of iterations for different IPCs during data synthesis phase. This means the first IPC can be recovered for the largest number of iterations like 4K while the last IPC will only be recovered using 500 iterations. To make this process efficient, we share the recovery time (on the GPU) across the different IPCs via concatenation to minimize time as much as possible. Therefore, the first image IPC will start recovery for a couple of iterations, and when it completes iteration 3,500 the last IPC will join it in the recovery phase to get its 500 iterations.

As illustrated in Fig. [4,](#page-3-1) our learning procedure is extremely simple using an incremental learning process: We split the total IPCs to be learned into multiple batches. The training begins with the first batch. Following a predefined number of iterations, the second batch commences its iterative training, and this process continues sequentially with subsequent batches. We define two types of optimization iterations for training: maximum iteration (MI) for the earliest batch training and round iteration (RI). MI presents the number of optimization iterations that the earliest batch goes through, i.e., the maximum number of iterations for the first batch's gradient updating. RI represents the number of iterations used for each round in Fig. [4.](#page-3-1) It essentially indicates the iteration gap between the optimization of two adjacent batches. *Batch-to-global* matching algorithm [\[39\]](#page-9-4) of Eq. [7](#page-3-2) is utilized between each round. In our DELT, later sub-batches will join the previous sub-batches in the image recovery stage instead of freezing the earlier sub-batches.

#### 4. Experiments

### 4.1. Datasets and Result Details

We first run DELT on five standard benchmark tests including CIFAR-10 (10 classes) [\[16\]](#page-8-12), Tiny-ImageNet (200 classes) [\[17\]](#page-8-13), ImageNet-1K (1,000 classes) [\[7\]](#page-8-14) and it variants of ImageNette (10 classes) [\[9\]](#page-8-15), and ImageNet-100 (100 classes) [\[35\]](#page-9-11) with performances reported in Table [1](#page-5-0) and Table [2.](#page-5-1) The evaluation protocol follows prior works [\[32,](#page-9-7) [40\]](#page-9-5). We compare DELT to six baseline dataset distillation algorithms including Matching Training Trajectories (MTT) [\[3\]](#page-8-0), Improved Distribution Matching (IDM) [\[45\]](#page-9-12), TrajEctory Matching with Constant Memory (TESLA) [\[6\]](#page-8-2), Squeeze-Recover-Relabel  $(SRe<sup>2</sup>L)$  [\[40\]](#page-9-5), Difficulty-Aligned Trajectory-Matching (DATM) [\[12\]](#page-8-16), Realistic-Diverse-Efficient Dataset Distillation (RDED) [\[32\]](#page-9-7). Following previous dataset distillation methods [\[32,](#page-9-7) [40,](#page-9-5) [43\]](#page-9-2), we use ConvNet [\[10\]](#page-8-17), ResNet-18/ResNet-101 [\[13\]](#page-8-18), EfficientNet-B0 [\[33\]](#page-9-13), MobileNet-V2 [\[27\]](#page-8-19), MnasNet1 3 [\[34\]](#page-9-14), and RegNet-Y-8GF [\[26\]](#page-8-20), as our backbone for training or post-validation. All our experi-

<span id="page-4-1"></span>Image /page/4/Picture/5 description: The image displays a visual representation of a process or experiment involving image manipulation, likely related to deep learning or computer vision. It is divided into four main sections, each labeled with a grid size: 1x1, 2x2, 3x3, and 4x4. Each section contains two columns of images. The left column in each section shows a series of input images, while the right column shows corresponding output images. Arrows indicate a transformation or processing step from the left column to the right column within each section. The 1x1 section shows four pairs of images, with the output images appearing to be processed versions of the input images. The 2x2 section contains a larger grid of input images on the left and a corresponding set of output images on the right, also with arrows indicating a transformation. Similarly, the 3x3 and 4x4 sections show progressively larger grids of input images and their transformed outputs. The overall arrangement suggests a study on how image processing or generation changes with increasing grid sizes or complexity.

Figure 6. Mosaic splicing patterns on ImageNet-1K using real image patches as the initialization. In each block, the left column is the starting real image initialized samples and right is the final optimized syntheses. From top to bottom are images generated by early training and late training.

ments are conducted on  $4 \times$  NVIDIA RTX 4090 GPUs.

As shown in Table [1,](#page-5-0) our approach establishes the new state-of-the-art accuracy in 13 out of 15 of the configurations on five datasets from small-scale CIFAR-10 to largescale ImageNet-1K using either relatively large backbone architecture of ResNet-101 or small MobileNet-v2, in many cases with significant margins of improvement. The results using small-scale architecture ConvNet are shown in Table [2,](#page-5-1) our approach also achieves the state-of-the-art accuracy in 7 out of 9 of the configurations on four datasets.

### 4.2. Cross-architecture generalization

An important characteristic of distilled datasets is their effectiveness in generalizing to novel training architectures. In this context, we assess the transferability of DELT's distilled datasets tailored for ImageNet-1K with 10 images per class. Following previous studies [\[32,](#page-9-7) [40\]](#page-9-5), we test our models using five distinct architectures: ResNet-18 [\[13\]](#page-8-18), MobileNet-V2 [\[27\]](#page-8-19), MnasNet1\_3 [\[34\]](#page-9-14), EfficientNet-B0 [\[33\]](#page-9-13), and RegNet-Y-8GF [\[26\]](#page-8-20). As shown in Table [5,](#page-6-0) our proposed approach demonstrates significantly better performance than other competitive methods on all these architectures.

<span id="page-4-0"></span>

### 4.3. Ablation Study

Mosaic splicing pattern. Mosaic stitching method [\[2\]](#page-8-21) in RDED selects four crops from the train set as the optimal hyperparameter, and puts the contents of the four crops into a synthetic image that is directly used for post-validation. In this work, considering that we use different difficulty levels of selection for initialization, we examine different strategies of the Mosaic splicing patterns, including  $1 \times 1$ ,  $2 \times 2$ ,  $3 \times 3$ ,  $4 \times 4$ , and  $5 \times 5$  patches, as illustrated in Fig. [6.](#page-4-1) The ablation results are shown in Table [4a,](#page-6-0) it can be observed that  $1 \times 1$  achieves the best accuracy.

Initialization. We examine how different initialization strategies affect final performance, including: choosing the lowest probability crops, medium probability crops and highest probability crops. Our results are shown in Table [4b.](#page-6-0)

<span id="page-5-2"></span><span id="page-5-0"></span>

|               |              | ResNet-18                |                  |                | ResNet-101             |                  |                | MobileNet-v2     |                |
|---------------|--------------|--------------------------|------------------|----------------|------------------------|------------------|----------------|------------------|----------------|
| Dataset       | <b>IPC</b>   | $SRe^2L[40]$             | <b>RDED</b> [32] | DELT (Ours)    | SRe <sup>2</sup> L[40] | <b>RDED</b> [32] | DELT (Ours)    | <b>RDED</b> [32] | DELT (Ours)    |
|               | $\mathbf{1}$ | $16.6 \pm 0.9$           | $22.9 \pm 0.4$   | $24.0 \pm 0.8$ | $13.7 \pm 0.2$         | $18.7 \pm 0.1$   | $20.4 \pm 1.0$ | $18.1 \pm 0.9$   | $20.2 \pm 0.4$ |
| $CIFAR-10$    | 10           | $29.3 \pm 0.5$           | $37.1 \pm 0.3$   | $43.0 \pm 0.9$ | $24.3 \pm 0.6$         | $33.7 \pm 0.3$   | $37.4 \pm 1.2$ | $29.2 \pm 1.1$   | $29.3 \pm 0.3$ |
|               | 50           | $45.0 \pm 0.7$           | $62.1 \pm 0.1$   | $64.9 \pm 0.9$ | $34.9 \pm 0.1$         | $51.6 \pm 0.4$   | $54.1 \pm 0.8$ | $39.9 \pm 0.5$   | $42.9 \pm 2.2$ |
|               | 1            | $19.1 \pm 1.1$           | $35.8 \pm 1.0$   | $24.1 \pm 1.8$ | $15.8 \pm 0.6$         | $25.1 \pm 2.7$   | $19.4 \pm 1.7$ | $26.4 \pm 3.4$   | $19.1 \pm 1.0$ |
| ImageNette    | 10           | $29.4 \pm 3.0$           | $61.4 \pm 0.4$   | $66.0 \pm 1.4$ | $23.4 \pm 0.8$         | $54.0 \pm 0.4$   | $55.4 \pm 6.2$ | $52.7 \pm 6.6$   | $64.7 \pm 1.4$ |
|               | 50           | $40.9 \pm 0.3$           | $80.4 \pm 0.4$   | $88.2 \pm 1.2$ | $36.5 \pm 0.7$         | $75.0 \pm 1.2$   | $83.3 \pm 1.1$ | $80.0 \pm 0.0$   | $85.7 \pm 0.4$ |
|               | 1            | $2.62 \pm 0.1$           | $9.7 \pm 0.4$    | $9.3 \pm 0.5$  | $1.9 \pm 0.1$          | $3.8 \pm 0.1$    | $5.6 \pm 1.0$  | $3.5 \pm 0.1$    | $3.5 \pm 0.5$  |
| Tiny-ImageNet | 10           | $16.1 \pm 0.2$           | $41.9 \pm 0.2$   | $43.0 \pm 0.1$ | $14.6 \pm 1.1$         | $22.9 \pm 3.3$   | $42.8 \pm 0.9$ | $24.6 \pm 0.1$   | $26.5 \pm 0.5$ |
|               | 50           | $41.1 \pm 0.4$           | $58.2 \pm 0.1$   | $55.7 \pm 0.5$ | $42.5 \pm 0.2$         | $41.2 \pm 0.4$   | $58.5 \pm 0.3$ | $49.3 \pm 0.2$   | $51.3 \pm 0.5$ |
|               | 10           | $9.5 \pm 0.4$            | $36.0 \pm 0.3$   | $28.2 \pm 1.5$ | $6.4 \pm 0.1$          | $33.9 \pm 0.1$   | $22.4 \pm 3.3$ | $23.6 \pm 0.7$   | $15.8 \pm 0.2$ |
| ImageNet-100  | 50           | $27.0 \pm 0.4$           | $61.6 \pm 0.1$   | $67.9 \pm 0.6$ | $25.7 \pm 0.3$         | $66.0 \pm 0.6$   | $70.8 \pm 2.3$ | $51.5 \pm 0.8$   | $55.0 \pm 1.8$ |
|               | 100          | $\overline{\phantom{a}}$ | $74.5 \pm 0.4$   | $75.1 \pm 0.2$ | ٠                      | $73.5 \pm 0.8$   | $77.6 \pm 1.8$ | $70.8 \pm 1.1$   | $76.7 \pm 0.3$ |
|               | 10           | $21.3 \pm 0.6$           | $42.0 \pm 0.1$   | $46.1 \pm 0.4$ | $30.9 \pm 0.1$         | $48.3 \pm 1.0$   | $48.5 \pm 1.6$ | $33.1 \pm 1.2$   | $35.5 \pm 0.7$ |
| ImageNet-1K   | 50           | $46.8 \pm 0.2$           | $56.5 \pm 0.1$   | $59.2 \pm 0.4$ | $60.8 \pm 0.5$         | $61.2 \pm 0.4$   | $66.1 \pm 0.5$ | $52.8 \pm 0.4$   | $56.2 \pm 0.3$ |
|               | 100          | $52.8 \pm 0.3$           | $59.8 \pm 0.1$   | $62.4 \pm 0.2$ | $62.8 \pm 0.2$         |                  | $67.6 \pm 0.3$ | $56.2 \pm 0.1$   | $58.9 \pm 0.3$ |

<span id="page-5-1"></span>Table 1. Comparison with SOTA dataset distillation methods using relatively large-scale backbones on five benchmarks across different scales. MobileNet-v2 is modified to match the low resolutions of CIFAR-10 and Tiny-ImageNet following [\[44\]](#page-9-15). Due to the limited table space, some prior methods that are slightly weaker or comparable with RDED are not listed, such as CDA and G-VBSM. Since IPC = 1 is not applicable to use EarlyLate strategy, thus under IPC = 1 setting, the single image in each class is optimized with a constant iteration.

|                |            |                |                |                | ConvNet          |                  |                |
|----------------|------------|----------------|----------------|----------------|------------------|------------------|----------------|
| <b>Dataset</b> | <b>IPC</b> | MTT[3]         | IDM $[45]$     | TESLA [6]      | <b>DATM</b> [12] | <b>RDED</b> [32] | DELT (Ours)    |
|                |            | $8.8 \pm 0.3$  | $10.1 \pm 0.2$ |                | $17.1 \pm 0.3$   | $12.0 + 0.1$     | $12.4 + 0.8$   |
| Tiny-ImageNet  | 10         | $23.2 + 0.2$   | $21.9 + 0.3$   |                | $31.1 + 0.3$     | $39.6 + 0.1$     | $40.0 + 0.4$   |
|                | 50         | $28.0 \pm 0.3$ | $27.7 \pm 0.3$ |                | $39.7 \pm 0.3$   | $47.6 + 0.2$     | $48.6 \pm 0.2$ |
|                | 10         |                | $17.1 \pm 0.6$ |                |                  | $29.6 + 0.1$     | $24.7 + 1.5$   |
| ImageNet-100   | 50         |                | $26.3 \pm 0.4$ |                |                  | $50.2 + 0.2$     | $51.9 + 1.1$   |
|                | 100        | ۰              |                |                |                  | $58.6 + 0.4$     | $61.5 \pm 0.5$ |
|                |            |                |                | $7.7 + 0.2$    |                  | $6.4 \pm 0.1$    | $8.8 + 0.5$    |
| ImageNet-1K    | 10         |                | ۰              | $17.8 \pm 1.3$ |                  | $20.4 \pm 0.1$   | $31.3 \pm 0.8$ |
|                | 50         |                | ۰              | $27.9 \pm 1.2$ |                  | $38.4 \pm 0.2$   | $41.7 \pm 0.1$ |

Table 2. Comparison with SOTA dataset distillation methods using small-scale backbone architecture on three datasets. Following [\[3,](#page-8-0) [32,](#page-9-7) [45\]](#page-9-12), Conv-4 for Tiny-ImageNet and ImageNet-1K, Conv-6 for ImageNet-100. Entries marked with "-" are missing due to scalability issue.

Overall, the performance gap between different strategies is not significant, and selecting the medium probability crops as the initialization achieves the best accuracy.

Optimization iterations. We examine two types of optimization iterations: maximum iteration (MI) for the earliest batch training and round iteration (RI) as the iteration gap between the two adjacent batches. As shown in Table [4c,](#page-6-0) we test MI values of 1K, 2K, and 4K, using 500 and 1K iterations for each RI. Note that when MI is set to 1K, it is not feasible to use 1K as RI. The results show that 4K (same as [\[39,](#page-9-4) [40\]](#page-9-5)) MI and 500 RI achieves the best accuracy.

Early-only vs. EarlyLate. Early-only is equivalent to using constant MI to optimize each image. This will transform to baseline *batch-to-global* matching of CDA [\[39\]](#page-9-4) + real image initialization. Our results in Table [4d](#page-6-0) clearly show that the EarlyLate training brings a significant improvement on final performance. More importantly, this strategy is the key factor in enhancing generation diversity.

#### Real image stitching vs. Minimax diffusion vs. Ours. We

further compare our approach with real image stitching [\[32\]](#page-9-7) and diffusion generation [\[11\]](#page-8-22). The results are presented in Table [4e.](#page-6-0) While the first two methods produce more realistic images, each image contains limited information. In contrast, our method achieves the best final performance.

Without real image initialization. Our EarlyLate strategy enhances the performance of 1∼2% over the initialization as shown in Table [4d.](#page-6-0) Without initialization, our method consistently improves by 2.4% as shown in Table [3.](#page-6-1)

### 4.4. Computational Analysis

For image optimization-based methods like SRe<sup>2</sup>L and CDA, the total computational cost is calculated as  $N \times T$ , where  $N$  is the MI. In our EarlyLate scheme, the first batch of images undergo  $T_1$  iterations (where  $T_1 = T$ ). Subsequent batches are processed with progressively fewer iterations, such as  $T_2$  ( $T_2 = T_1 - RI$ ) for the next set, and so forth. The iterations for the final batch are reduced to RI which is  $1/j$  of the standard count (where  $j = 4$  or 8 in our

<span id="page-6-2"></span><span id="page-6-1"></span><span id="page-6-0"></span>

| Strategy | SRe2L [40] w/o Init w/o EarlyLate | SRe2L [40] w/o Init w/ EarlyLate | CDA [39] w/o Init w/o EarlyLate | CDA [39] w/o Init w/EarlyLate |
|----------|-----------------------------------|----------------------------------|---------------------------------|-------------------------------|
| Acc.     | 46.8                              | $53.4(+6.6)$                     | 53.5                            | $55.9(+2.4)$                  |

Table 3. Performance comparison without real image initialization on ImageNet-1K with IPC 50.

| # Patches   | Top 1 acc | Selection criteria                                                           |
|-------------|-----------|------------------------------------------------------------------------------|
| $1 	imes 1$ | 57.57     | Lowest probability                                                           |
| $2 	imes 2$ | 56.92     | Medium probability                                                           |
| $3 	imes 3$ | 56.62     | Highest probability                                                          |
| $4 	imes 4$ | 56.71     | (b) Selection criteria. Initializing $1 	imes 1$ teacher model's probability |

(a) Number of patches. Ablation on initializing different numbers of scoring patches. Results are from ResNet-18 on ImageNet-1K for 500 iterations to synthesize 50 IPCs. Our optimization-based method favors  $1 \times 1$  initialized patch, and will involve inconsistency and noise using more objects.

| Iterations<br>(MI) | Round Iterations (RI) |       |
|--------------------|-----------------------|-------|
|                    | 500                   | 1K    |
| 1K                 | 44.87                 | 43.71 |
| 2K                 | 45.61                 | 44.40 |
| 4K                 | <b>46.42</b>          | 44.66 |

(c) Round Iterations. Top-1 acc. of our method for IPC 10 using different round iterations with ResNet-18.

| Selection criteria  | Top 1 acc    |
|---------------------|--------------|
| Lowest probability  | 57.55        |
| Medium probability  | <b>57.67</b> |
| Highest probability | 57.03        |

ages selected according to teacher model's probability

|     | Dataset          | $CDA [39] + Our init.$                                     | Ours |      |
|-----|------------------|------------------------------------------------------------|------|------|
|     | ImageNet-1K      | 43.5                                                       | 46.1 |      |
|     | Tiny-ImageNet    | 42.2                                                       | 43.0 |      |
|     | $CIFAR-10$       | 39.4                                                       | 43.0 |      |
|     |                  | (d) Ablation on init. and $\text{EarlyDate}$ under IPC 10. |      |      |
| IPC | <b>RDED [32]</b> | MinimaxDiffusion $[11]$                                    |      | Ours |
| 10  | 42.O             | 44.3                                                       |      | 46.1 |
| 50  | 56.5             | 58.6                                                       |      | 59.2 |

(e) Comparison with real and diffusion-generated data.

| Table 4. <b>Ablation experiments</b> on various aspects of our framework with ResNet-18 on ImageNet-1K. |  |  |  |  |  |  |  |  |  |  |
|---------------------------------------------------------------------------------------------------------|--|--|--|--|--|--|--|--|--|--|
|---------------------------------------------------------------------------------------------------------|--|--|--|--|--|--|--|--|--|--|

|           | Recover / Validation | ResNet-18       | EfficientNet-B0 | MobileNet-V2    | MnasNet1_3      | RegNet-Y-8GF    |
|-----------|----------------------|-----------------|-----------------|-----------------|-----------------|-----------------|
| ResNet-18 | SRe²L [40]†          | 41.9            | 41.9            | 33.1            | 39.3            | 51.5            |
|           | CDA [39]             | 42.2            | 43.9            | 34.2            | 39.7            | 52.9            |
|           | G-VBSM [28]          | 41.4            | 42.6            | 33.5            | 40.1            | 52.2            |
|           | RDED [32]            | 42.3            | 42.8            | 34.4            | 40.0            | 54.8            |
|           | Ours                 | $46.4_{(****)}$ | $47.1_{(****)}$ | $36.1_{(****)}$ | $40.7_{(+0.7)}$ | $57.5_{(****)}$ |

Table 5. Cross-architecture generalization. Results are evaluated on IPC 10.  $^{\dagger}$  is reproduced following CDA's configuration.

ablation), the total number of our optimization iterations required is  $N \times T - \frac{j(j-1)}{2}$  $\frac{-1}{2}$ RI, which is roughly 2/3 of prior *batch-to-global* matching methods. Our real time consumptions for data generation are shown in Table [6,](#page-7-0) note that the smaller the dataset like CIFAR, the more time is spent on loading and processing the data, rather than training.

### 4.5. Visualization of DELT

Fig. [7](#page-7-1) illustrates a comprehensive visual comparison between randomly selected synthetic images from our distilled dataset and those from the real image patches [\[32\]](#page-9-7), MinimaxDiffusion [\[11\]](#page-8-22), MTT [\[3\]](#page-8-0), IDC [\[15\]](#page-8-23), SRe<sup>2</sup>L [\[40\]](#page-9-5), SCDD [\[46\]](#page-9-16), CDA [\[39\]](#page-9-4) and G-VBSM [\[28\]](#page-9-6) distilled data. It can be observed that the images generated by each method have their own characteristics. MinimaxDiffusion leverages the diffusion model to synthesize images which is close to the real ones. However, as in our above ablation, both real and diffusion-generated data are inferior to ours. MTT results show noticeable artifacts and distortions, the objects in all images are located in the middle of the generations, the diversity is limited. IDC results also show distorted and less recognizable dog images, but diversity is increased.  $SRe<sup>2</sup>L$  exhibits some dog features but with significant distortions and a similar simple background. SCDD shows

more recognizable dog features but still the color is simple and monochromatic, the same situation happens in CDA. G-VBSM shows more colorful patterns, possibly due to recovery from multiple different networks, but all generations are in the same pattern and the diversity is not large. Our approach's synthetic images exhibit a higher degree of diversity, including both compressed distorted images from long-optimized initializations and clear, recognizable dog images from short-optimized initializations, a unique capability not present in other methods.

### 4.6. Application I: Data-free Network Pruning

Our distilled dataset acts as a multifunctional training tool and boosts the adaptability for diverse downstream applications. We validate its utility in the scenario of data-free network pruning [\[31\]](#page-9-17). Table [7](#page-7-2) shows the applicability of our dataset in this task when pruning 50% weights, where it significantly surpasses previous methods such as SRe<sup>2</sup>L and RDED under IPC 10 and 50.

### 4.7. Application II: Continual Learning

We examine the effectiveness of DELT generated images in the continual learning scenario. Following the setup in prior studies [\[40,](#page-9-5) [42\]](#page-9-1), we perform 100-step class-incremental ex-

<span id="page-7-4"></span><span id="page-7-1"></span>Image /page/7/Figure/0 description: This figure displays a grid of images comparing different image optimization methods for generating images of golden retrievers. The leftmost column shows real images of golden retrievers, labeled 'Real'. The subsequent columns show generated images from various methods: 'M-Diffusion', 'MTT', 'IDC', 'SRe2L', 'SCDD', 'CDA', and 'G-VBSM', with the final column labeled 'Ours'. Each row corresponds to a specific real image and its generated counterparts. The images showcase golden retrievers in different poses and settings, with the generated images exhibiting varying degrees of realism and artifacting. The figure is titled 'Figure 7. Distilled dataset visualization compared with other image optimization-based methods.'

<span id="page-7-0"></span>Figure 7. Distilled dataset visualization compared with other image optimization-based methods.

| Method          | Dataset (hours) under same 4K iterations for all methods |                                                        |                                                         |
|-----------------|----------------------------------------------------------|--------------------------------------------------------|---------------------------------------------------------|
|                 | ImageNet-1K                                              | Tiny-ImageNet                                          | CIFAR-10                                                |
| G-VBSM [28]     | 114.1                                                    | 5.5                                                    | 0.195                                                   |
| SRe2L [40]      | 29.0                                                     | 5.0                                                    | 0.084                                                   |
| CDA [39]        | 29.0                                                     | 5.0                                                    | 0.084                                                   |
| Ours (RI = 1K)  | <b>18.8</b> ( <span style="color:blue;">↓35.2%</span> )  | <b>3.6</b> ( <span style="color:blue;">↓28.0%</span> ) | <b>0.084</b> ( <span style="color:blue;">↓0.0%</span> ) |
| Ours (RI = 500) | <b>17.6</b> ( <span style="color:blue;">↓39.3%</span> )  | <b>3.4</b> ( <span style="color:blue;">↓32.0%</span> ) | <b>0.083</b> ( <span style="color:blue;">↓1.1%</span> ) |

<span id="page-7-2"></span>Table 6. Actual computational consumption (hours under IPC 50) in data synthesis with image optimization-based methods on a single NVIDIA 4090 GPU. A total 4K iterations are used for all methods and datasets to ensure fair comparisons. "RI" represents *round iterations*.

|                                               |      | $\vert$ SRe <sup>2</sup> L [40] RDED [32] Ours |                 |
|-----------------------------------------------|------|------------------------------------------------|-----------------|
| $\frac{\text{IPC}}{\text{IPC}} \frac{10}{50}$ | 12.5 | 13.2                                           | $17.9_{(+4.7)}$ |
|                                               | 31.7 | 42.8                                           | $44.8_{(+2.0)}$ |

Table 7. Accuracy of data-free network pruning using slimming [\[22\]](#page-8-24) on VGG11-BN [\[30\]](#page-9-18).

<span id="page-7-3"></span>Image /page/7/Figure/6 description: This figure, titled "Figure 8. Continual learning results.", is a line graph illustrating the top-1 accuracy (%) against the class number. The x-axis represents the class number, ranging from 100 to 900 in increments of 100. The y-axis represents the top-1 accuracy in percentage, ranging from 10% to 40%. Three lines are plotted: a blue line labeled "SRe2L" with diamond markers, an orange line labeled "G-VBSM" with circle markers, and a green line labeled "Ours" with triangle markers. The "Ours" line shows the highest accuracy, starting at approximately 16% for 100 classes and increasing to about 42% for 900 classes. The "G-VBSM" line starts at approximately 10% for 100 classes and increases to about 30% for 900 classes. The "SRe2L" line shows the lowest accuracy, starting at approximately 8% for 100 classes and increasing to about 20% for 900 classes.

periments on ImageNet-1K, comparing our results with the baselines G-VBSM and SRe<sup>2</sup>L. As shown in Fig. [8,](#page-7-3) our DELT distilled dataset significantly outperforms G-VBSM, with an average improvement of about 10% on the 100-step

class-incremental learning task. This highlights the significant benefits of deploying DELT, particularly in mitigating the challenges of continual learning.

# 5. Conclusion

We have introduced a new training strategy, EarlyLate, to improve image diversity in *batch-to-global* matching scenarios for dataset distillation. The proposed approach organizes predefined IPC samples into smaller, manageable subtasks and utilizes local optimizations. This strategy helps in refining each subset into distributions characteristic of different phases, thereby mitigating the homogeneity typically caused by a singular optimization process. The images refined through this method exhibit robust generalization across the entire task. We have extensively evaluated this approach on CIFAR-10, Tiny-ImageNet, ImageNet-1K, and its variants. Our empirical findings indicate that our approach significantly outperforms prior state-of-the-art methods across various IPC configurations.

## Acknowledgments

This research is supported by the MBZUAI-WIS Joint Program for AI Research and the Google Research award grant.

#### References

- <span id="page-8-11"></span>[1] Yingbin Bai, Erkun Yang, Bo Han, Yanhua Yang, Jiatong Li, Yinian Mao, Gang Niu, and Tongliang Liu. Understanding and improving early stopping for learning with noisy labels. *Advances in Neural Information Processing Systems*, 34:24392–24403, 2021. [4](#page-3-3)
- <span id="page-8-21"></span>[2] Alexey Bochkovskiy, Chien-Yao Wang, and Hong-Yuan Mark Liao. Yolov4: Optimal speed and accuracy of object detection. *arXiv preprint arXiv:2004.10934*, 2020. [5](#page-4-2)
- <span id="page-8-0"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-1) [2,](#page-1-3) [3,](#page-2-1) [5,](#page-4-2) [6,](#page-5-2) [7](#page-6-2)
- <span id="page-8-1"></span>[4] Xuxi Chen, Yu Yang, Zhangyang Wang, and Baharan Mirzasoleiman. Data distillation can be like vodka: Distilling more times for better quality. In *The Twelfth International Conference on Learning Representations*, 2024. [1,](#page-0-1) [2,](#page-1-3) [3](#page-2-1)
- <span id="page-8-26"></span>[5] Ekin D Cubuk, Barret Zoph, Jonathon Shlens, and Quoc V Le. Randaugment: Practical automated data augmentation with a reduced search space. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition workshops*, pages 702–703, 2020. [11](#page-10-0)
- <span id="page-8-2"></span>[6] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [1,](#page-0-1) [2,](#page-1-3) [3,](#page-2-1) [5,](#page-4-2) [6](#page-5-2)
- <span id="page-8-14"></span>[7] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [5](#page-4-2)
- <span id="page-8-8"></span>[8] Jiawei Du, Xin Zhang, Juncheng Hu, Wenxin Huang, and Joey Tianyi Zhou. Diversity-driven synthesis: Enhancing dataset distillation through directed weight adjustment. In *Advances in neural information processing systems*, 2024. [3](#page-2-1)
- <span id="page-8-15"></span>[9] Fastai. Fastai/imagenette: A smaller subset of 10 easily classified classes from imagenet, and a little more french. [5](#page-4-2)
- <span id="page-8-17"></span>[10] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4367–4375, 2018. [5](#page-4-2)
- <span id="page-8-22"></span>[11] Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. In *CVPR*, 2024. [6,](#page-5-2) [7](#page-6-2)
- <span id="page-8-16"></span>[12] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *The Twelfth International Conference on Learning Representations*, 2024. [5,](#page-4-2) [6](#page-5-2)

- <span id="page-8-18"></span>[13] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [5](#page-4-2)
- <span id="page-8-3"></span>[14] Yang He, Lingao Xiao, Joey Tianyi Zhou, and Ivor Tsang. Multisize dataset condensation. *ICLR*, 2024. [1,](#page-0-1) [2,](#page-1-3) [3](#page-2-1)
- <span id="page-8-23"></span>[15] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *Proceedings of the 39th International Conference on Machine Learning*, 2022. [7](#page-6-2)
- <span id="page-8-12"></span>[16] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [5](#page-4-2)
- <span id="page-8-13"></span>[17] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [5](#page-4-2)
- <span id="page-8-4"></span>[18] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022. [1,](#page-0-1) [2](#page-1-3)
- <span id="page-8-6"></span>[19] Haoyang Liu, Tiancheng Xing, Luwei Li, Vibhu Dalal, Jingrui He, and Haohan Wang. Dataset distillation via the wasserstein metric. *arXiv preprint arXiv:2311.18531*, 2023. [1,](#page-0-1) [3](#page-2-1)
- <span id="page-8-7"></span>[20] Risheng Liu, Jiaxin Gao, Jin Zhang, Deyu Meng, and Zhouchen Lin. Investigating bi-level optimization for learning and vision from a unified perspective: A survey and beyond. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 44(12):10045–10067, 2021. [3](#page-2-1)
- <span id="page-8-5"></span>[21] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *Advances in Neural Information Processing Systems*, 35:1100–1113, 2022. [1,](#page-0-1) [2](#page-1-3)
- <span id="page-8-24"></span>[22] Zhuang Liu, Jianguo Li, Zhiqiang Shen, Gao Huang, Shoumeng Yan, and Changshui Zhang. Learning efficient convolutional networks through network slimming. In *Proceedings of the IEEE international conference on computer vision*, pages 2736–2744, 2017. [8](#page-7-4)
- <span id="page-8-25"></span>[23] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, et al. Pytorch: An imperative style, high-performance deep learning library. *Advances in neural information processing systems*, 32, 2019. [11](#page-10-0)
- <span id="page-8-10"></span>[24] Lutz Prechelt. Early stopping-but when? In *Neural Networks: Tricks of the trade*, pages 55–69. Springer, 2002. [4](#page-3-3)
- <span id="page-8-9"></span>[25] Tian Qin, Zhiwei Deng, and David Alvarez-Melis. Distributional dataset distillation with subtask decomposition. *arXiv preprint arXiv:2403.00999*, 2024. [3](#page-2-1)
- <span id="page-8-20"></span>[26] Ilija Radosavovic, Raj Prateek Kosaraju, Ross Girshick, Kaiming He, and Piotr Dollár. Designing network design spaces. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 10428–10436, 2020. [5](#page-4-2)
- <span id="page-8-19"></span>[27] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4510–4520, 2018. [5](#page-4-2)

- <span id="page-9-6"></span>[28] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. In *CVPR*, 2024. [1,](#page-0-1) [2,](#page-1-3) [3,](#page-2-1) [7,](#page-6-2) [8](#page-7-4)
- <span id="page-9-19"></span>[29] Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In *European Conference on Computer Vision*, pages 673–690. Springer, 2022. [11](#page-10-0)
- <span id="page-9-18"></span>[30] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [8](#page-7-4)
- <span id="page-9-17"></span>[31] Suraj Srinivas and R Venkatesh Babu. Data-free parameter pruning for deep neural networks. *arXiv preprint arXiv:1507.06149*, 2015. [7](#page-6-2)
- <span id="page-9-7"></span>[32] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *CVPR*, 2024. [2,](#page-1-3) [4,](#page-3-3) [5,](#page-4-2) [6,](#page-5-2) [7,](#page-6-2) [8](#page-7-4)
- <span id="page-9-13"></span>[33] Mingxing Tan and Quoc Le. Efficientnet: Rethinking model scaling for convolutional neural networks. In *International conference on machine learning*, pages 6105–6114. PMLR, 2019. [5](#page-4-2)
- <span id="page-9-14"></span>[34] Mingxing Tan, Bo Chen, Ruoming Pang, Vijay Vasudevan, Mark Sandler, Andrew Howard, and Quoc V Le. Mnasnet: Platform-aware neural architecture search for mobile. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 2820–2828, 2019. [5](#page-4-2)
- <span id="page-9-11"></span>[35] Yonglong Tian, Dilip Krishnan, and Phillip Isola. Contrastive multiview coding. In *Computer Vision–ECCV 2020: 16th European Conference, Glasgow, UK, August 23–28, 2020, Proceedings, Part XI 16*, pages 776–794. Springer, 2020. [5](#page-4-2)
- <span id="page-9-0"></span>[36] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2](#page-1-3)
- <span id="page-9-9"></span>[37] Zhou Wang and Alan C Bovik. Mean squared error: Love it or leave it? a new look at signal fidelity measures. *IEEE signal processing magazine*, 26(1):98–117, 2009. [3](#page-2-1)
- <span id="page-9-10"></span>[38] Lingao Xiao and Yang He. Are large-scale soft labels necessary for large-scale dataset distillation? In *Advances in neural information processing systems*, 2024. [3](#page-2-1)
- <span id="page-9-4"></span>[39] Zeyuan Yin and Zhiqiang Shen. Dataset distillation via curriculum data synthesis in large data era. *Transactions on Machine Learning Research*. [1,](#page-0-1) [3,](#page-2-1) [5,](#page-4-2) [6,](#page-5-2) [7,](#page-6-2) [8,](#page-7-4) [11](#page-10-0)
- <span id="page-9-5"></span>[40] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *NeurIPS*, 2023. [1,](#page-0-1) [3,](#page-2-1) [5,](#page-4-2) [6,](#page-5-2) [7,](#page-6-2) [8,](#page-7-4) [11](#page-10-0)
- <span id="page-9-8"></span>[41] Yihua Zhang, Prashant Khanduri, Ioannis Tsaknakis, Yuguang Yao, Mingyi Hong, and Sijia Liu. An introduction to bi-level optimization: Foundations and applications in signal processing and machine learning. *arXiv preprint arXiv:2308.00788*, 2023. [3](#page-2-1)
- <span id="page-9-1"></span>[42] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *IEEE/CVF Winter Conference on Applications of Computer Vision, WACV 2023, Waikoloa, HI, USA, January 2-7, 2023*, 2023. [1,](#page-0-1) [2,](#page-1-3) [3,](#page-2-1) [7](#page-6-2)
- <span id="page-9-2"></span>[43] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [1,](#page-0-1) [2,](#page-1-3) [3,](#page-2-1) [5](#page-4-2)

- <span id="page-9-15"></span>[44] Borui Zhao, Quan Cui, Renjie Song, Yiyu Qiu, and Jiajun Liang. Decoupled knowledge distillation. In *Proceedings of the IEEE/CVF Conference on computer vision and pattern recognition*, pages 11953–11962, 2022. [6](#page-5-2)
- <span id="page-9-12"></span>[45] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023. [5,](#page-4-2) [6](#page-5-2)
- <span id="page-9-16"></span>[46] Muxin Zhou, Zeyuan Yin, Shitong Shao, and Zhiqiang Shen. Self-supervised dataset distillation: A good compression is all you need. *arXiv preprint arXiv:2404.07976*, 2024. [7](#page-6-2)
- <span id="page-9-3"></span>[47] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022. [1,](#page-0-1)  $\mathcal{L}$

# <span id="page-10-0"></span>Appendix

### A. Limitations

Our method effectively avoids the issue of insufficient data diversity generated by *batch-to-global* methods and reduces the computational cost of the generation process. However, there is still a performance gap when training the model on our generated data compared to training on the original dataset. Also, our short-optimized data exhibits similar appearance and semantic information to the original images, which has demonstrated better privacy protection than prior train-free methods but may still potentially leak the privacy of the original dataset to some extent.

### B. More Training Details

For reproducibility, we provide all our hyperparameter settings used in our experiments in Table [8,](#page-11-0) we outline such details below.

Squeezing and Pre-trained models. Following the previous works  $[39, 40]$  $[39, 40]$  $[39, 40]$ , we use the official PyTorch  $[23]$  pretrained ResNet-18 model for ImageNet-1K, and we use the same official Torchvision [\[23\]](#page-8-25) code to obtain our pre-trained models, ResNet-18 and ConvNet, for the other datasets.

Ranking. For our initialization, we simply use ResNet-18 pre-trained models to rank and select the medium images as initialization for all our datasets, except for ImageNet-100 where we simply extract the medium images based on the rankings of the original ImageNet-1K.

Recovery. For our synthetic stage, we provide the details of general hyperparameters used for different datasets, including ImageNet-1K, ImageNet-100, ImageNette, Tiny-ImageNet, and CIFAR10, in Table [8b.](#page-11-0) Synthesizing a single image per class, i.e.,  $IPC = 1$ , is special as we cannot use *rounds*, so we apply individual numbers of iterations based on both the dataset scale and the validation teacher model as outlined in Table [8c.](#page-11-0) We also utilize the BatchNorm distribution regularization term as in  $SRe<sup>2</sup>L$  [\[40\]](#page-9-5) for Eq. 7 in the main paper to improve the quality of the generated images:

$$
\mathcal{R}_{reg}(\tilde{\boldsymbol{x}}) = \sum_{l} \left| \mu_l(\tilde{\boldsymbol{x}}) - \mathbf{BN}_l^{\mathrm{RM}} \right|_2 + \sum_{l} \left| \sigma_l^2(\tilde{\boldsymbol{x}}) - \mathbf{BN}_l^{\mathrm{RV}} \right|_2 \quad (8)
$$

where *l* is the index of BN layer,  $\mu_l(\tilde{x})$  and  $\sigma_l^2(\tilde{x})$  are mean and variance.  $BN_l^{\text{RM}}$  and  $BN_l^{\text{RV}}$  are running mean and running variance in pre-trained model at l-th layer, which are globally counted.

Validation. This includes the soft-label generation [\[29\]](#page-9-19) as used in SRe<sup>2</sup>L, post-training and evaluation. We outline such details in Table [8a.](#page-11-0) We use timm's version of RandAugment [\[5\]](#page-8-26) with different settings depending on the synthesized dataset being validated, as shown in Table [8c.](#page-11-0)

<span id="page-10-1"></span>Image /page/10/Picture/11 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. The smaller images are visualizations of synthetic images from Tiny ImageNet. The grid displays a variety of subjects, including birds, mammals like cats and red pandas, insects, cars, buildings, landscapes, and bottles. The overall impression is a diverse collection of generated images, likely used to demonstrate the capabilities of a model trained on the Tiny ImageNet dataset.

Figure 9. Synthetic image visualizations on Tiny-ImageNet generated by our DELT.

<span id="page-10-2"></span>Image /page/10/Picture/13 description: The image is a grid of 8x8 synthetic images, likely generated by a machine learning model. The images are diverse, featuring various subjects such as people, animals (dogs, fish), vehicles (trucks, cars), buildings (churches, houses), sports equipment (golf balls, chainsaws), and abstract patterns. Some images appear to be of lower quality or have artifacts, suggesting they are generated rather than real photographs. The overall impression is a collage of diverse visual content.

Figure 10. Synthetic image visualizations on ImageNette generated by our DELT.

### C. More Visualization

We provide more visualizations on synthetic Tiny-ImageNet, ImageNette and CIFAR-10 datasets in Fig. [9,](#page-10-1) [10,](#page-10-2) [11.](#page-12-0) In each figure, each column represents a different class, with images progressing from long optimization at the top

<span id="page-11-0"></span>

| (a) Validation settings         |       |                            |                                           | (b) Recovery settings  |                |       |                               |
|---------------------------------|-------|----------------------------|-------------------------------------------|------------------------|----------------|-------|-------------------------------|
| config                          | value |                            | config                                    |                        | value          |       |                               |
| optimizer                       |       | AdamW                      |                                           | $\alpha_{\rm RN}$      |                | 0.01  |                               |
| base learning rate              |       | $0.001$ (all)              |                                           | optimizer              |                | Adam  |                               |
|                                 |       | $0.0025$ (MobileNet-v2)    |                                           | base learning rate     |                | 0.25  |                               |
| weight decay                    | 0.01  |                            |                                           | momentum               |                |       | $\beta_1, \beta_2 = 0.5, 0.9$ |
|                                 |       | 100 (IPC 50)               |                                           | batch size             |                | 100   |                               |
| batch size                      |       | 50 (IPC 10)                |                                           | learning rate schedule |                |       | cosine decay                  |
| learning rate schedule          |       | 10 (IPC 1)<br>cosine decay |                                           | recovery iteration     |                | 4,000 |                               |
| training epoch                  | 300   |                            |                                           | round iteration        |                |       | 500 [IPC 10, 50, 100]         |
|                                 |       | RandAugment                |                                           | initialization         |                |       | top medium                    |
| augmentation                    |       | RandomResizedCrop          |                                           | augmentation           |                |       | RandomResizedCrop             |
|                                 |       | RandomHorizontalFlip       |                                           |                        |                |       |                               |
|                                 |       |                            | (c) Dataset-specific settings in recovery |                        |                |       |                               |
| config                          |       | CIFAR10                    | Tiny-ImageNet                             | ImageNette             | ImageNet-100   |       | $ImageNet-1K$                 |
| RandAugment (m)                 |       | 5                          | 4                                         | 6                      | 6              |       | 6                             |
| RandAugment (n)                 |       | 4                          | 3                                         | 2                      | $\mathfrak{D}$ |       | $\mathfrak{D}$                |
| RandAugment (mstd)              |       | 1.0                        | 1.0                                       | 1.0                    | 1.0            |       | 1.0                           |
|                                 |       | 2K(R18)                    | 500 (R18)                                 | 1K(R18)                |                |       | 3K (Conv4)                    |
| <b>IPC1</b> Recovery Iterations |       | 3K (R101)                  | 500 (R101)                                | 1K(R101)               |                |       |                               |
|                                 |       | 2K (MobileNet)             | 500 (MobileNet)                           | 2K (MobileNet)         |                |       |                               |
|                                 |       |                            | 1K (Conv4)                                | 4K (Conv5)             |                |       |                               |
|                                 |       |                            | Table 8. Hyper-parameter settings.        |                        |                |       |                               |

| Initialization | SRe2L + w/ Init w/o EarlyLate | CDA + Init w/o EarlyLate | CDA + w/ Init + w/ EarlyLate (Ours) |
|----------------|-------------------------------|--------------------------|-------------------------------------|
| $2	imes2$      | 55.3                          | 56.9                     | $58.2_{(****)}$                     |
| $3	imes3$      | 55.8                          | 56.6                     | $58.1_{(****)}$                     |
| $4	imes4$      | 55.2                          | 56.7                     | $57.4_{(+0.7)}$                     |
| $5	imes5$      | 54.6                          | 56.5                     | $57.3_{(+0.8)}$                     |

Table 9. Performance comparison w/ and w/o EarlyLate on ImageNet-1K under IPC 50.

| Order      | <b>DELT</b> |
|------------|-------------|
| Random     | 67.9        |
| Ascending  | 67.2        |
| Descending | 67.7        |
| Our DELT   | 68.2        |

<span id="page-11-2"></span><span id="page-11-1"></span>Table 10. Impact of using different ordering on ImageNet-100 when having the same initialized images of the median probability.

| <b>Selection Strategy</b> | <b>DELT</b> |
|---------------------------|-------------|
| Random                    | 67.7        |
| Ascending                 | 66.9        |
| Descending                | 67.3        |
| Our DELT                  | 68.2        |

Table 11. Comparison of the performance of different initialization strategies. The initialized images are different.

to short optimization at the bottom.

### D. More Ablation

Performance comparison w/ and w/o **EarlyLate**. We compare using the recent CDA and SRe<sup>2</sup>L as the base frameworks. The performance comparison for IPC 50 on ImageNet-1K is shown in Table [9.](#page-11-0) It can be observed that the proposed *EarlyLate* strategy enhances the performance by around 1% with the initialization.

Comparison of random, ascending and descending orders by patch probability. In our DELT, we select the  $N$  patches with scores around the median from the teacher, where the score represents the probability of the true class. To order them, we start with the median, and we go back and forth expanding the window around the median until we cover the number of IPCs, refer to Fig. [5](#page-3-0) of the main paper for details. The rationale is that these patches present a medium difficulty level for the teacher, allowing more potential for information enhancement through distillation gradients while having a good starting point of information. We empirically validate it by comparing different strategies in Table 4b of the main paper.

<span id="page-12-0"></span>Image /page/12/Picture/0 description: The image displays a grid of 80 small, blurry images, arranged in 8 rows and 10 columns. Each small image appears to be a synthetic sample, likely generated by a machine learning model, as indicated by the caption below the grid. The samples depict a variety of objects and scenes, including animals such as dogs, cats, birds, and horses; vehicles like cars, airplanes, and trucks; and some abstract patterns or textures. The overall impression is a collection of low-resolution, somewhat distorted representations of common objects and living beings.

Figure 11. Synthetic images on CIFAR-10 generated by our DELT.

As shown in Table [10,](#page-11-1) we present the impact of using different orderings on ImageNet-100 when having the same initialized images, those around the median. We also include a comparison of different initialization strategies based on the order in Table [11.](#page-11-2) Unlike Table [10,](#page-11-1) the initialized images here are different across different strategies. Different initial crop ranges in random crop augmentation for our DELT method. Table [12](#page-12-1) compares different initial crop ranges in random crop augmentation for our DELT method. As shown in the results, the 0.08-1.0 range yields the best performance, which is the ablation and support for the default setting in our framework.

<span id="page-12-1"></span>

| Random Crop Range | Top 1-acc   |
|-------------------|-------------|
| 0.08-1.0          | <b>67.8</b> |
| 0.2-1.0           | 67.3        |
| 0.5-1.0           | 66.3        |
| 0.8-1.0           | 66.3        |

Table 12. Different initial crop ranges in random crop augmentation for our DELT method.

<span id="page-12-2"></span>Image /page/12/Figure/5 description: The image contains two line graphs. The top graph plots the total time in hours against the number of synthetic IPCs, ranging from 0 to 100. It shows three lines: a dashed blue line labeled 'DELT (RI=500)', an orange line labeled 'CDA/SRe^2L', and a green line labeled 'G-VBSM'. The green line shows a significantly higher total time, reaching over 200 hours at 100 IPCs, while the blue and orange lines are much lower, with the orange line slightly above the blue line. The bottom graph also plots total time in hours against the number of synthetic IPCs from 0 to 100. This graph displays three lines: a dashed blue line labeled 'DELT (RI=500)', a dotted red line labeled 'DELT (RI=1K)', and an orange line labeled 'CDA/SRe^2L'. In this graph, the orange line shows the highest total time, reaching approximately 48 hours at 100 IPCs. The blue and red lines are closer to each other, with the red line slightly above the blue line, both reaching around 35 hours at 100 IPCs. The bottom of the image has text that reads 'Figure 12. Visualization of computation time consumption on ex'.

Figure 12. Visualization of computation time consumption on our DELT and other methods including CDA,  $SRe^{2}L$ , and G-VBSM.

### E. Computational Efficiency

Fig. [12](#page-12-2) illustrates the computation time required for our DELT method compared to other methods, including CDA, SRe<sup>2</sup>L, and G-VBSM, at various numbers of IPCs. The top subfigure shows a comparison of DELT (with a RI of 500), CDA/SRe<sup>2</sup>L, and G-VBSM, where G-VBSM demonstrates the highest computation time, scaling significantly as the number of IPCs increases. In contrast, both DELT and CDA/SRe<sup>2</sup>L maintain relatively low and consistent computation times, with DELT slightly outperforming CDA/SRe<sup>2</sup>L. The bottom subfigure further compares DELT with RIs of 500 and 1,000 against CDA/SRe<sup>2</sup>L, highlighting that both configurations of DELT offer lower or comparable computation times to  $CDA/SRe^2L$  across IPC values, with minimal increase as the IPC count rises. These results emphasize DELT's efficiency in computation time, particularly in comparison to G-VBSM, making it a computationally efficient choice for scenarios with larger datasets.
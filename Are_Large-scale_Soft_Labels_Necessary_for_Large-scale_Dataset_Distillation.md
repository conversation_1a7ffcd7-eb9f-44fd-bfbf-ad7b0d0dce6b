# Are Large-scale Soft Labels Necessary for Large-scale Dataset Distillation?

<PERSON><PERSON><sup>1,3</sup> and <PERSON> He<sup>1,2,3</sup><sup>∗</sup>

<sup>1</sup>CFAR, Agency for Science, Technology and Research, Singapore <sup>2</sup>IHPC, Agency for Science, Technology and Research, Singapore <sup>3</sup>National University of Singapore xiao\<EMAIL>, he\<EMAIL>

### Abstract

In ImageNet-condensation, the storage for auxiliary soft labels exceeds that of the condensed dataset by over 30 times. However, *are large-scale soft labels necessary for large-scale dataset distillation*? In this paper, we first discover that the high within-class similarity in condensed datasets necessitates the use of large-scale soft labels. This high within-class similarity can be attributed to the fact that previous methods use samples from different classes to construct a single batch for batch normalization (BN) matching. To reduce the within-class similarity, we introduce class-wise supervision during the image synthesizing process by batching the samples within classes, instead of across classes. As a result, we can increase within-class diversity and reduce the size of required soft labels. A key benefit of improved image diversity is that soft label compression can be achieved through simple random pruning, eliminating the need for complex rule-based strategies. Experiments validate our discoveries. For example, when condensing ImageNet-1K to 200 images per class, our approach compresses the required soft labels from 113 GB to 2.8 GB (40 $\times$  compression) with a 2.6% performance gain. Code is available at: [https://github.com/he-y/soft-label-pruning-for-dataset-distillation.](https://github.com/he-y/soft-label-pruning-for-dataset-distillation)

# 1 Introduction

We are pacing into the era of ImageNet-level condensation, and the previous works [\[1,](#page-9-0) [2,](#page-9-1) [3,](#page-9-2) [4,](#page-9-3) [5\]](#page-9-4) fail in scaling up to large-scale datasets due to extensive memory constraint. Until recently, Yin *et al.*[\[6\]](#page-9-5) decouple the traditional distillation scheme into three phases. First, a teacher model is pretrained with full datasets (squeeze phase). Second, images are synthesized by matching the Batch Normalization (BN) statistics from the teacher and student models (recover phase). Third, auxiliary data such as soft labels are pregenerated from different image augmentations to create abundant supervision for posttraining (relabel phase).

However, the auxiliary data are  $30 \times$  larger than the distilled data in ImageNet-1K. To

<span id="page-0-0"></span>Image /page/0/Figure/9 description: This is a scatter plot showing the relationship between total storage (GB, image + label) on the x-axis and accuracy (%) on the y-axis. Several methods are plotted, including SRe2L (NeurIPS'23), G-VBSM (CVPR'24), RDED (CVPR'24), CDA, and Ours. Each data point is represented by a circle, with the size of the circle indicating some additional information. The plot also includes an inset showing storage requirements for different datasets (IPC-50, IPC-200, ImageNet-1K) for both base and ours labels, with corresponding icons. For IPC-50, the base label requires 0.75G storage and the ours label requires 0.71G. For IPC-200, the base label requires 2.98G storage and the ours label requires 2.91G. For ImageNet-1K, the base label requires 138G storage and the ours label requires 0G. The main plot shows that as total storage increases, accuracy generally increases for all methods. The 'Ours' method appears to achieve higher accuracy at lower storage levels compared to other methods.

Figure 1: The relationship between performance and total storage of auxiliary information needed. Our method achieves SOTA performance with fewer soft labels than images.

<sup>∗</sup>Corresponding Author

<sup>38</sup>th Conference on Neural Information Processing Systems (NeurIPS 2024).

<span id="page-1-0"></span>Image /page/1/Picture/0 description: The image displays a grid of 24 smaller images, arranged in two columns and four rows. The top row of the left column features abstract depictions of sharks or dolphins in a dark, watery environment. The second row shows close-ups of what appear to be cat faces with intricate fur patterns. The third row presents abstract, colorful images that resemble fruits or flowers. The bottom row contains abstract images with red and white patterns, possibly depicting animals or objects. The right column mirrors the left column's structure, with the top row showing similar aquatic animal imagery, the second row displaying abstract patterns resembling fur or feathers, the third row featuring colorful, abstract floral or fruit-like compositions, and the bottom row showing abstract red and white patterns.

(a)  $SRe^{2}L$  [\[6\]](#page-9-5). (b) Ours (IPC=10).

Figure 2: Visual comparison between  $SRe^{2}L$  and the proposed method. The classes are hammer shark (top), pineapple (middle), and pomegranate (bottom). Our method is more visually diverse.

attain correct and effective supervision, the exact augmentations and soft labels of every training epoch are stored [\[6,](#page-9-5) [7,](#page-9-6) [8,](#page-9-7) [9,](#page-9-8) [10\]](#page-9-9). The required soft label storage is the colored circles in Fig. [1.](#page-0-0)

In this paper, we consider *whether large-scale soft labels are necessary*, and *what causes the excessive requirement of these labels*? To answer these questions, we provide an analysis of the distilled images using  $SRe^{2}L$  [\[7\]](#page-9-6), and we find that within-class diversity is at stake as shown in Fig. [2.](#page-1-0) To be more precise, we analyze the similarity using Feature Cosine Similarity and Maximum Mean Discrepancy in Sec. [3.2.](#page-3-0) The high similarity of images within the same class requires extensive data augmentation to provide different supervision.

To address this issue, we propose Label Pruning for Large-scale Distillation (LPLD). Specifically, we modified the algorithms by batching images within the same class, leveraging the fact that different classes are naturally independent. Furthermore, we introduce class-wise supervision to align our changes. In addition, we have explored different label pruning metrics and found that simple random pruning was performed on par with carefully selected labels. To further increase diversity, we improve the label pool by introducing randomness in a finer granularity (i.e., batch-level). Our method effectively distills the images while requiring less label storage compared to image storage, as shown in Fig. [1.](#page-0-0)

The key contributions of this work are: (1) To the best of our knowledge, it is the first work to introduce label pruning to large-scale dataset distillation. (2) We discover that high within-class diversity necessitates large-scale soft labels. (3) We re-batch images and introduce class-wise supervision to improve data diversity, allowing random label pruning to be effective with an improved label pool. (4) Our LPLD method achieves SOTA performance using a lot less label storage, and it is validated with extensive experiments on various networks (e.g., ResNet, EfficientNet, MobileNet, and Swin-V2) and datasets (e.g., Tiny-ImageNet, ImageNet-1K, and ImageNet-21K).

# 2 Related Works

Dataset Distillation. DD [\[1\]](#page-9-0) first introduces dataset distillation, which aims to learn a synthetic dataset that is equally effective but much smaller in size. The matching objectives include performance matching  $[1, 11, 12, 13, 14]$  $[1, 11, 12, 13, 14]$  $[1, 11, 12, 13, 14]$  $[1, 11, 12, 13, 14]$  $[1, 11, 12, 13, 14]$  $[1, 11, 12, 13, 14]$  $[1, 11, 12, 13, 14]$  $[1, 11, 12, 13, 14]$  $[1, 11, 12, 13, 14]$ , gradient matching  $[4, 15, 16, 17]$  $[4, 15, 16, 17]$  $[4, 15, 16, 17]$  $[4, 15, 16, 17]$  $[4, 15, 16, 17]$  $[4, 15, 16, 17]$  $[4, 15, 16, 17]$ , distribution or feature matching  $[5, 2, 16, 17]$  $[5, 2, 16, 17]$  $[5, 2, 16, 17]$  $[5, 2, 16, 17]$ [18\]](#page-9-17), trajectory matching [\[3,](#page-9-2) [19,](#page-10-0) [20\]](#page-10-1), representative matching [\[21,](#page-10-2) [22\]](#page-10-3), loss-curvature matching [\[23\]](#page-10-4), and Batch-Norm matching[\[6,](#page-9-5) [7,](#page-9-6) [9,](#page-9-8) [10\]](#page-9-9).

Dataset Distillation of Large-Scale Datasets. Large-scale datasets scale up in terms of image size and the number of total images, incurring affordable memory consumption for most of the welldesigned matching objectives targeted for small datasets. MTT [\[3\]](#page-9-2) is able to condense Tiny-ImageNet (ImageNet-1K subsets with images downsampled to  $64 \times 64$  and 200 classes). IDC [\[24\]](#page-10-5) conducts experiments on ImageNet-10, which contains an image size of  $224 \times 224$  but has only 10 classes. TESLA [\[20\]](#page-10-1) manages to condense the full ImageNet-1K dataset by exactly computing the unrolled

gradient with constant memory or complexity.  $SRe<sup>2</sup>L$  [\[6\]](#page-9-5) decouples the bilevel optimization into three phases: 1) squeezing, 2) recovering, and 3) relabeling. The proposed framework surpasses TESLA [\[20\]](#page-10-1) by a noticeable margin. CDA [\[7\]](#page-9-6) improves the recovering phase by introducing curriculum learning. RDED [\[8\]](#page-9-7) replaces the recovering phase with an optimization-free approach by concatenating selected image patches. SC-DD [\[10\]](#page-9-9) uses self-supervised models as recovery models. Existing methods [\[7,](#page-9-6) [8,](#page-9-7) [10\]](#page-9-9) place high emphasis on improving the recovering phase; however, the problem of the relabeling phase is overlooked: *a large amount of storage is required for the relabeling phase.*

Label Compression. The problem of excessive storage seems to be fixed if the teacher model generates soft labels immediately used by the student model on the fly. However, when considering the actual use case of distilled datasets (i.e., Neural Architecture Search), using pre-generated labels enjoys speeding up training and reduced memory cost. More importantly, the generated labels can be repeatedly used. FKD [\[25\]](#page-10-6) employs label quantization to store only the top-k logits. In contrast, our method retains full logits, offering an orthogonal approach to quantization. A comparison to FKD is provided in Appendix [D.3.](#page-18-0) Unlike FerKD [\[26\]](#page-10-7), which removes some unreliable soft labels, our strategy targets higher pruning ratios.

Comparison with G-VBSM [\[9\]](#page-9-8). In one recent work, G-VBSM also mentioned re-batching the images within classes; however, the motivation is that having a single image in a class is insufficient [\[9\]](#page-9-8). It re-designed the loss by introducing a model pool, matching additional statistics from convolutional layers, and updating the statistics of synthetic images using exponential moving averages (EMA). Additionally, an ensemble of models is involved in both the data synthesis and relabel phase, requiring a total of N forward propagation from N different models, where  $N = 4$  is used for ImageNet-1K experiments. On the other hand, we aim to improve the within-class data diversity for **reducing** soft label storage. Furthermore, to account for the re-batching operation, we introduce class-wise supervision while all G-VBSM statistics remain global.

### 3 Method

#### 3.1 Preliminaries

The conventional Batch Normalization (BN) transformation is defined as follows:

$$
y = \gamma \left(\frac{x - \mu}{\sqrt{\sigma^2 + \epsilon}}\right) + \beta,\tag{1}
$$

where  $\gamma$  and  $\beta$  are parameters learned during training,  $\mu$  and  $\sigma^2$  are the mean and variance of the input features, and  $\epsilon$  is a small constant to prevent division by zero. Additionally, the running mean and running variance are maintained during network training and subsequently utilized as  $\mu$  (mean) and  $\sigma^2$  (variance) during the inference phase, given that the true mean and variance of the test data are not available.

The matching object of SRe2L [\[7\]](#page-9-6) follows DeepInversion [\[27\]](#page-10-8), which optimizes synthetic datasets by matching the models' layer-wise BN statistics:

$$
\mathcal{L}_{\text{BN}}(\widetilde{\boldsymbol{x}}) = \sum_{l} \left\|\mu_{l}(\widetilde{\boldsymbol{x}}) - \mathbb{E}(\mu_{l} \mid \mathcal{T})\right\|_{2} + \sum_{l} \left\|\sigma_{l}^{2}(\widetilde{\boldsymbol{x}}) - \mathbb{E}(\sigma_{l}^{2} \mid \mathcal{T})\right\|_{2}
$$
$$
\approx \sum_{l} \left\|\mu_{l}(\widetilde{\boldsymbol{x}}) - \mathbf{BN}_{l}^{\text{RM}}\right\|_{2} + \sum_{l} \left\|\sigma_{l}^{2}(\widetilde{\boldsymbol{x}}) - \mathbf{BN}_{l}^{\text{RV}}\right\|_{2}, \tag{2}
$$

where the BN's running mean  $BN_l^{\text{RM}}$  and running variance  $BN_l^{\text{RV}}$  are used to approximate the expected mean  $\mathbb{E}(\mu_l | \mathcal{T})$  and expected variance  $\mathbb{E}(\sigma_l^2 | \mathcal{T})$  of the original dataset  $\mathcal{T}$ , repsectively. The BN loss matches BN for layers l, and  $\mu_l(\tilde{x})$  and  $\sigma_l^2(\tilde{x})$  are the mean and variance of the synthetic images  $\tilde{x}$ images  $\widetilde{x}$ .

The BN loss term is used as a regularization term applied to the classification loss  $\mathcal{L}_{\text{CE}}$ . Therefore, the matching objective is:

<span id="page-2-0"></span>
$$
\underset{\widetilde{\boldsymbol{x}}}{\arg\min} \underbrace{\ell\left(\boldsymbol{\theta}_{\mathcal{T}}\left(\widetilde{\boldsymbol{x}}\right),\boldsymbol{y}\right)}_{\mathcal{L}_{\text{CE}}} + \alpha \cdot \mathcal{L}_{\text{BN}}\left(\widetilde{\boldsymbol{x}}\right),\tag{3}
$$

where  $\theta_{\tau}$  is the model pretrained on the original dataset T. The symbol  $\alpha$  is a small factor controlling the regularization strength of BN loss.

<span id="page-3-2"></span>Table 1: The cosine similarity between image features. The similarities are the average of 1K class on the synthetic ImageNet-1K dataset. Features are extracted using pretrained ResNet-18.

| <b>IPC</b> | SRe2L             | <b>CDA</b>        | <b>Ours</b>       | <b>Full Dataset</b> |
|------------|-------------------|-------------------|-------------------|---------------------|
| 50         | $0.841 \pm 0.023$ | $0.816 \pm 0.026$ | $0.796 \pm 0.029$ | $0.695 \pm 0.045$   |
| 100        | $0.840 \pm 0.016$ | $0.814 \pm 0.019$ | $0.794 \pm 0.021$ |                     |
| 200        | $0.839 \pm 0.011$ | $0.813 \pm 0.013$ | $0.793 \pm 0.015$ |                     |

Image /page/3/Figure/2 description: This is a stacked bar chart showing the maximum mean discrepancy for three different methods: SRe2L, CDA, and Ours. The x-axis represents classes from 0 to 1000, and the y-axis represents the maximum mean discrepancy. SRe2L has an average discrepancy of 10.69, CDA has an average of 8.35, and Ours has an average of 4.69. The chart visually demonstrates that the 'Ours' method consistently achieves the lowest maximum mean discrepancy across all classes.

Figure 3: MMD visualization.

<span id="page-3-0"></span>

### 3.2 Diversity Analysis on Synthetic Dataset

#### 3.2.1 Similarity within Synthetic Dataset: Feature Cosine Similarity

A critical aspect of image diversity is how similar or different the images are within the same class. To quantify this, we utilize the feature cosine similarity measure defined above. Lower cosine similarity values between images within the same class indicate greater diversity, as the images are less similar to one another. This relationship is formally stated as follows:

Proposition 1. *The lower feature cosine similarity of images indicates higher diversity because the images are less similar to one another.*

Feature Cosine similarity can be formally put as:

$$
\cos \text{similarity} := \frac{f(\widetilde{\boldsymbol{x}}_{\boldsymbol{c}}) \cdot f(\widetilde{\boldsymbol{x}}_{\boldsymbol{c}}')}{\|f(\widetilde{\boldsymbol{x}}_{\boldsymbol{c}})\| \|f(\widetilde{\boldsymbol{x}}_{\boldsymbol{c}}')\|} = \frac{\sum_{i=1}^{n} f(\widetilde{\boldsymbol{x}}_{\boldsymbol{c},i}) f(\widetilde{\boldsymbol{x}}_{\boldsymbol{c},i})}{\sqrt{\sum_{i=1}^{n} f(\widetilde{\boldsymbol{x}}_{\boldsymbol{c},i})^{2}} \sqrt{\sum_{i=1}^{n} f(\widetilde{\boldsymbol{x}}_{\boldsymbol{c},i})^{2}}},\tag{4}
$$

where  $\tilde{x}_c$  and  $\tilde{x}'_c$  are two images from the same class c,  $f(\cdot)$  are the features extracted from a pretrained model, and n is the feature dimension pretrained model, and  $n$  is the feature dimension.

## 3.2.2 Similarity between Synthetic and Original Dataset: Maximum Mean Discrepancy

The similarity between images is not the only determinant of diversity since images can be dissimilar to each other yet not representative of the original dataset. Therefore, to further validate the diversity of our synthetic dataset, we consider an additional metric: the Maximum Mean Discrepancy (MMD) between synthetic datasets and original datasets. This measure helps evaluate how well the synthetic data represents the original data distribution. The following proposition clarifies the relationship between MMD and dataset diversity:

Proposition 2. *A lower MMD suggests that the synthetic dataset captures a broader range of features similar to the original dataset, indicating greater diversity.*

The empirical approximation of MMD can be formally defined as [\[28,](#page-10-9) [29\]](#page-10-10),

$$
\text{MMD}^{2} \left( \boldsymbol{P}_{\mathcal{T}}, \boldsymbol{P}_{\mathcal{S}} \right) = \hat{\mathcal{K}}_{\mathcal{T}, \mathcal{T}} + \hat{\mathcal{K}}_{\mathcal{S}, \mathcal{S}} - 2\hat{\mathcal{K}}_{\mathcal{T}, \mathcal{S}} \tag{5}
$$

where  $\hat{K}_{X,Y} = \frac{1}{|X|\cdot|Y|} \sum_{i=1}^{|X|} \sum_{j=1}^{|Y|} \mathcal{K}(f(x_i), f(y_j))$  with  $\{x_i\}_{i=1}^{|X|} \sim X, \{y_i\}_{i=1}^{|Y|} \sim Y$ . T and S denote real and synthetic datasets, respectively;  $K$  is the reproducing kernel (e.g., Gaussian kernel); P is the feature (embedding) distribution, and  $f(\cdot)$  is the feature representation extracted by model  $\theta$ , where  $f(\mathcal{T}) \sim P_{\mathcal{T}}, f(\mathcal{S}) \sim P_{\mathcal{S}}$ .

### 3.3 Label Pruning for Large-scale Distillation (LPLD)

<span id="page-3-1"></span>

#### 3.3.1 Diverse Sample Generation via Class-wise Supervision

The previous objective function follows Eq. [3;](#page-2-0) it uses a subset of classes  $\mathcal{B}_c$  to match the BN statistics of the entire dataset, and images in the same class are independently generated, causing an low image diversity within classes. However, inspired by He *et al.*[\[30\]](#page-10-11), images in the same class should work collaboratively, and images that are optimized individually (see Baseline B in work [\[30\]](#page-10-11)) do not lead to the optimal performance when IPC (Images Per Class) gets larger.

<span id="page-4-0"></span>Image /page/4/Figure/0 description: This is a diagram illustrating a method for improving batch normalization in neural networks. The diagram is divided into two main sections: 'SRe2L / CDA / SC-DD' on the left and 'Ours' on the right. The left side shows a network with multiple BN layers, each with associated batch and global statistics, and a BN-matching loss. The right side, labeled 'Ours', introduces an 'IPC' (Inter-Process Communication) module with 're-batching' and then feeds into a similar network structure with BN layers. The 'Ours' section also shows BN-matching loss calculations, but these are connected to the re-batched data. The diagram highlights the update process for trainable layers and the role of batch and global statistics in both approaches.

Figure 4: Illustration of existing methods (left, grey) and the proposed method (right, blue). Existing methods (i.e.,  $SRe<sup>2</sup>L$ , CDA) independently generate along the IPC (Image-Per-Class) dimension, causing a high similarity between images of the same class. The proposed method allows images of the same class to collaborate, leaving different classes naturally independent. In addition, synthetic images are updated under class-wise supervision. The classification loss is omitted for simplicity.

Step 1: Re-batching Images within Class. Subsequently, to obtain a collaborative effect among different images of the same class, we sample images from the same class and provide the images with class-wise supervision [\[4,](#page-9-3) [24\]](#page-10-5). Fig. [4](#page-4-0) illustrates the changes.

Step 2: Introducing Class-wise Supervision. However, the running mean and variance approximate the original dataset's expected mean and variance in a global aspect. The matching objective becomes sub-optimal in class-wise matching situation. To this end, we propose to track BN statistics for each class separately. Since we only track the running mean and variance, the extra storage is marginal even when up to 1K classes in ImageNet-1K (see Appendix [E.2](#page-20-0) and [E.4\)](#page-22-0).

**Step 3: Class-wise Objective Function.** The new class-wise objective function is modified from Eq. [3,](#page-2-0) which has two loss functions. First, we compute the classification loss (i.e., the Cross-Entropy Loss) with BN layers using global statistics to ensure effective supervision. Second, we compute BN loss by matching class-wise BN statistics. The modified parts are highlighted in blue color, and the objective function is formally put as,

Cross-Entropy Loss with Global BN Statistics

<span id="page-4-1"></span>
$$
\arg\min_{\widetilde{\mathbf{x}}_c} \left( -\sum_{i=1}^N y_{c,i} \log \left( \text{softmax} \left( \theta_\mathcal{T} \left( \frac{\widetilde{\mathbf{x}}_{c,i} - \mathbf{BN}_{\text{global}}^{\text{RM}}}{\sqrt{\mathbf{BN}_{\text{global}}^{\text{RV}}} + \epsilon} \right) \right)_{c} \right) + \alpha \cdot \sum_{l} \left( \left\| \mu_l(\widetilde{\mathbf{x}}_c) - \mathbf{BN}_{l,c}^{\text{RM}} \right\|_2 + \left\| \sigma_l^2(\widetilde{\mathbf{x}}_c) - \mathbf{BN}_{l,c}^{\text{RV}} \right\|_2 \right) \right)
$$
(6)

Batch Norm Loss with Class-wise BN Statistics

We want to emphasize that even though we are adjusting the BN loss with class-wise statistics, the global statistics of the dataset are still taken into account. The output logits for calculating CE loss are produced using global statistics. This is because altering  $\mu$  and  $\sigma$  without fine-tuning  $\gamma$  and  $\beta$ could lead to a decline in model performance, resulting in less effective supervision.

Theoretical Number of Updates for Stable Class-wise BN Statistics. Traditional BN layers do not compute class-wise statistics; therefore, we need to either keep track of the class-wise statistics while training a model from scratch or compute these statistics using a pretrained model. We prefer the latter as the former requires extensive computing resources. To understand how many BN statistics updates are needed, we can first look at the update rules of BN running statistics for a class c:

$$
\mathbf{BN}_{l,c}^{\mathrm{RM}} \leftarrow (1 - \epsilon) \cdot \mathbf{BN}_{l,c}^{\mathrm{RM}} + \epsilon \cdot \mu_l(\boldsymbol{x}_c),
$$
  
\n
$$
\mathbf{BN}_{l,c}^{\mathrm{RV}} \leftarrow (1 - \epsilon) \cdot \mathbf{BN}_{l,c}^{\mathrm{RV}} + \epsilon \cdot \sigma_l^2(\boldsymbol{x}_c),
$$
\n(7)

<span id="page-5-1"></span>Image /page/5/Figure/0 description: The image illustrates a process of random pruning for a soft label pool and random sampling for training. The top legend indicates that gray rectangles represent images, dashed rectangles represent batches, dashed orange rectangles represent epochs, colored squares (green, blue, pink) represent augmentations like CutMix, and red crosses represent pruned labels. The left side shows a 'Generated Soft Labels' pool with 12 batches, where some labels are augmented (green, blue, pink) and some are pruned (red cross). The middle section shows 'Epoch-level Pruning' and 'Batch-level Pruning' applied to this pool, with red crosses indicating pruned labels at different positions. The right side shows 'Random Sampling for Training' over two epochs. In Training Epoch 1, specific batches are selected, and in Training Epoch 2, different batches are selected, with some labels pruned. The overall figure demonstrates how labels are pruned and sampled for training.

Figure 5: Illustration of two random processes in label pruning with improved label pool. First, we need a smaller soft label pool due to the storage budget. We can conduct pruning at two levels: (1) epoch-level and (2) batch-level. Batch-level pruning can provide a more diverse label pool since augmentations (e.g., Mixup or CutMix) are different across batches. The illustrated pruning ratio is 25%; the crossed-out labels denote the pruned labels, and the remaining form the label pool. Second, we randomly sample soft labels for model training.

where  $\epsilon$  is the momentum. Since the momentum factor for the current batch statistics is usually set to a small value (i.e.,  $\epsilon = 0.1$ ), we can theoretically compute existing running statistics that can be statistically significant after how many updates, assuming all other factors are fixed.

Since the running statistics are computed per class, we provide the theoretical number of updates required to stabilize all class statistics (see Appendix [A](#page-12-0) for the proof):

<span id="page-5-0"></span>
$$
n \ge \max \left( \frac{-2 \ln \left( \frac{T}{2} \right)}{\underbrace{\delta^2 \min(q_c)}_{\text{Chernoff Bound}}}, \frac{\ln \left( \frac{C}{\tau} \right)}{\underbrace{(1 - \delta)\varepsilon \min(q_c)}_{\text{BN Convergence}}} \right), \tag{8}
$$

where n is the number updates needed,  $q_c$  is the probability that class c appears in a batch, T is a probability threshold,  $\varepsilon$  is the momentum parameter in Batch Normalization,  $\delta$  is the acceptable relative deviation (where  $0 \le \delta \le 1$ ), C is some constant, and  $\tau$  is the desired convergence tolerance for the BN statistics. How Eq. [8](#page-5-0) guides our experiment design is detailed in Appendix [E.3.](#page-20-1)

#### 3.3.2 Random Label Pruning with Improved Label Pool

Excelling in Both Similarity Measures. By adopting the changes provided in Sec. [3.3.1,](#page-3-1) our synthetic dataset is more diverse and representative than the existing methods. First, our dataset exhibits smaller feature cosine similarity within classes compared to datasets produced by existing methods, as shown in Table [1.](#page-3-2) This indicates that our synthetic images are less similar to each other and, thus, more diverse. Second, our dataset exhibits a significantly lower MMD shown in Fig [3](#page-3-2) compared to datasets produced by existing methods. This suggests that our synthetic dataset better captures the feature distribution of the original dataset. After obtaining a diverse dataset, the next move is to address superfluous soft labels.

Random Label Pruning. Different from dataset pruning metrics, which many wield training dynamics [\[31,](#page-10-12) [32\]](#page-10-13), label pruning is inherently different since the labels in different epochs are independently generated or evaluated. Subsequently, these methods do not directly apply, and we modify these metrics to determine which epochs contain the most useful augmentations and soft labels. Through empirical study, we find that using soft labels carefully pruned from different metrics is **no better** than simple random pruning. As a result, we can discard complex rule-based pruning metrics, attaining both simplicity and efficiency. After obtaining the soft label pool, we have to decide which labels will be used. Following the previous random pruning scheme, we randomly sample the labels for model training in order to ensure diversity and avoid any prior knowledge.

Improved Label Pool. Considering that random selection may be the most efficient choice, we rethink the diversity of the label pool, as labels at the epoch-level are not the finest elements. <span id="page-6-0"></span>Table 2: Tiny-ImageNet label pruning results. The standard deviation is attained from three different runs. † denotes the reported results.

| $\left \begin{array}{ccccc} & 1 \times & 10 \times & 20 \times & 30 \times & 40 \times & 40 \times \end{array}\right $<br>ResNet-18 SRe <sup>2</sup> L CDA Ours SRe <sup>2</sup> L CDA Ours SRe <sup>2</sup> L CDA Ours SRe <sup>2</sup> L CDA Ours SRe <sup>2</sup> L CDA Ours SRe <sup>2</sup> L CDA Ours |  |  |  |  |  |  |  |  |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|--|--|--|--|
| $IPC50 \t\t   41.1^{\dagger} \t 48.7^{\dagger} \t 48.8_{\pm 0.4}   40.3 \t 45.0 \t 46.7_{\pm 0.6}   39.0 \t 41.2 \t 44.3_{\pm 0.5}   34.6 \t 35.8 \t 40.2_{\pm 0.3}   29.8 \t 30.9 \t 38.4_{\pm 1.3}$                                                                                                       |  |  |  |  |  |  |  |  |
| $[PC100 \t   49.7^{\dagger} \t 53.2^{\dagger} \t 53.6 \pm 0.3 \t   48.3 \t 50.7 \t 52.2 \pm 0.2 \t   46.5 \t 48.0 \t 50.6 \pm 0.2 \t 43.0 \t 44.2 \t 47.6 \pm 0.2 \t 39.4 \t 40.0 \t 46.1 \pm 0.2 \t 32.2 \pm 0.2 \t 44.0 \t 56.6 \pm 0.2 \t 57.6 \pm 0.2 \t 58.6 \pm 0.2 \t 59.6 \$                        |  |  |  |  |  |  |  |  |

(a) Comparison between SOTA methods.

(b) Experiments on larger networks.

|  |  | $\begin{array}{ l c c c c c c c c c c c c c c c c c c $ |  |  |  |
|--|--|---------------------------------------------------------|--|--|--|
|  |  | $\begin{array}{ l c c c c c c c c c c c c c c c c c c $ |  |  |  |
|  |  |                                                         |  |  |  |

<span id="page-6-1"></span>Table 3: ImageNet-1K label pruning result. Our method consistently shows a better performance under various pruning ratios. The validation model is ResNet-18.  $\dagger$  denotes the reported results.

| ResNet-18 | 1×    |       |                  | 10×   |      |                  | 20×   |      |                  | 30×   |      |                  | 40×   |      |                  |
|-----------|-------|-------|------------------|-------|------|------------------|-------|------|------------------|-------|------|------------------|-------|------|------------------|
|           | SRe2L | CDA   | Ours             | SRe2L | CDA  | Ours             | SRe2L | CDA  | Ours             | SRe2L | CDA  | Ours             | SRe2L | CDA  | Ours             |
| IPC10     | 20.1  | 33.3  | <b>34.6</b> ±0.9 | 18.9  | 28.4 | <b>32.7</b> ±0.6 | 16.0  | 21.9 | <b>28.6</b> ±0.4 | 14.1  | 14.2 | <b>23.1</b> ±0.1 | 11.4  | 13.2 | <b>20.2</b> ±0.3 |
| IPC20     | 33.6  | 44.0  | <b>47.2</b> ±0.5 | 31.1  | 39.7 | <b>44.7</b> ±0.4 | 29.2  | 34.1 | <b>41.0</b> ±0.3 | 24.5  | 27.5 | <b>35.9</b> ±0.3 | 21.7  | 24.0 | <b>33.0</b> ±0.6 |
| IPC50     | 46.8† | 53.5† | <b>55.4</b> ±0.3 | 44.1  | 50.3 | <b>54.4</b> ±0.2 | 41.5  | 46.1 | <b>51.8</b> ±0.2 | 37.2  | 41.8 | <b>48.6</b> ±0.2 | 35.5  | 38.0 | <b>46.7</b> ±0.3 |
| IPC100    | 52.8† | 58.0† | <b>59.4</b> ±0.2 | 51.1  | 55.1 | <b>58.8</b> ±0.0 | 49.5  | 53.3 | <b>57.4</b> ±0.0 | 46.7  | 49.7 | <b>55.2</b> ±0.1 | 44.4  | 47.2 | <b>54.0</b> ±0.8 |
| IPC200    | 57.0† | 63.3† | <b>62.6</b> ±0.8 | 56.5  | 59.4 | <b>62.4</b> ±0.7 | 55.1  | 58.3 | <b>61.7</b> ±0.7 | 52.9  | 56.0 | <b>60.1</b> ±0.5 | 51.9  | 54.4 | <b>59.6</b> ±0.6 |

The augmentations such as CutMix and Mixup are performed at the batch level, where the same augmentations are applied to images within the same batch and are different across batches. Therefore, we improve the label pool by allowing batches in different epochs to form a new epoch. The improved label pool breaks the fixed batch orders and the fixed combination of augmentations within an epoch, allowing a more diverse training process while reusing the labels. Our label pruning method is illustrated in Fig. [5.](#page-5-1)

## 4 Experiments

### 4.1 Experiment Settings

Dataset details can be found in Appendix [B](#page-15-0) and detailed settings are provided in Appendix [C.](#page-15-1) Computing resources used for experiments can be found in Appendix [E.5.](#page-22-1)

Dataset. Our experiment results are evaluated on Tiny-ImageNet [\[33\]](#page-10-14), ImageNet-1K [\[34\]](#page-10-15), and ImageNet-21K-P [\[35\]](#page-10-16). We follow the data pre-processing procedure of  $SRe<sup>2</sup>L$  [\[6\]](#page-9-5) and CDA [\[7\]](#page-9-6).

**Squeeze.** We modify the pretrained model by adding class-wise BN running mean and running variance; since they are not involved in computing the BN statistics, they do not affect performance. As mentioned in Sec. [3.3.1,](#page-3-1) we compute class-wise BN statistics by training for one epoch with model parameters kept frozen.

Recover. We perform data synthesis following Eq. [6.](#page-4-1) The batch size for the recovery phase is the same as the IPC. Besides, we adhere to the original setting in  $SRe<sup>2</sup>L$ .

Relabel. We use pretrained ResNet18 [\[36\]](#page-10-17) for all experiments as the relabel model except otherwise stated. For Tiny-ImageNet and ImageNet-1K, we use Pytorch pretrained model. For ImageNet-21K-P, we use Timm pretrained model.

Validate. For validation, we adhere to the hyperparameter settings of CDA [\[7\]](#page-9-6).

Pruning Setting. For label pruning, we exclude the last batch (usually with an incomplete batch size) of each epoch from the label pool. There are two random processes: (1) Random candidate selection from all batches. (2) Random reuse of candidate labels.

<span id="page-7-1"></span>Table 5: Comparison between different pruning metrics. Results are obtained from ImageNet-1K IPC10 and validated using ResNet-18. (b) Calibration of label pool.

|         | (a) Nandom pruning vs. Frunnig metrics at $40\times$ . |      |                                               |      |      |        |                   | $20\times$   | $30\times$   | $50\times$   | $100\times$ |
|---------|--------------------------------------------------------|------|-----------------------------------------------|------|------|--------|-------------------|--------------|--------------|--------------|-------------|
| IPC10   |                                                        |      | correct diff diff_signed cut_ratio confidence |      |      |        | $-90%$<br>$-50\%$ | 25.5<br>28.2 | 22.8<br>22.5 | 17.4<br>17.8 | 10.3<br>9.7 |
| Hard    | 19.6                                                   | 18.9 | 19.2                                          | 19.5 | 19.0 | $-10%$ | $-30%$            | 28.3         | 22.6         | 17.1         | 8.7         |
| Easy    | 19.3                                                   | 18.7 | 19.3                                          | 19.5 | 17.9 | $-30%$ | $-5%$             | 27.8         | 21.5         | 16.0         | 7.9         |
| Uniform | 20.0                                                   | 18.5 | 20.1                                          | 19.7 | 19.2 | $-90%$ | $\Omega$          | 27.7         | 22.1         | 16.3         | 8.6         |
| Random  |                                                        |      | 20.2                                          |      |      |        | $^{\circ}$        | 28.6         | 23.1         | 7.6          | 9.6         |

(a) Random pruning vs. Pruning metrics at  $40\times$ 

### 4.2 Primary Result

Tiny-ImageNet. Table [2a](#page-6-0) presents a comparison between the label pruning outcomes on Tiny-ImageNet for our approach,  $SRe^2L$  [\[6\]](#page-9-5), and the subsequent work, CDA [\[7\]](#page-9-6). Our method not only consistently surpasses  $SRe<sup>2</sup>L$  across identical pruning ratios but also achieves comparable results to  $SRe^{2}L$  while using  $40\times$  fewer labels. When compared to CDA, our method exhibits closely matched performance, yet it demonstrates superior accuracy preservation. For instance, at a  $40\times$ label reduction, our method secures a notable 7.5% increase in accuracy over CDA, even though the improvement stands at a mere  $0.1\%$  at the  $1\times$  benchmark. Table [2b](#page-6-0) provides the pruning results on ResNet50 and ResNet101. Although there are consistent improvements observed when compared to ResNet18, scaling to large networks does not necessarily bring improvements.

ImageNet-1K. Table [3](#page-6-1) compares the ImageNet-1K pruning results with SOTA methods on ResNet18. Our method outperforms other SOTA methods at various pruning ratios and different IPCs. More importantly, our method consistently exceeds the unpruned version of  $SRe<sup>2</sup>L$  with  $30\times$  less storage. Such a result is not impressive at first glance; however, when considering the actual storage, the storage is reduced from 29G to 0.87G. In addition, we notice the performance at  $10\times$  (or 90%) pruning ratio degrades slightly, especially for large IPCs. For example, merely 0.2% performance degradation on IPC200 using ResNet18. Pruning results of larger IPCs can be found in Appendix [D.2.](#page-18-1)

### 4.3 Analysis

Ablation Study. Table [4](#page-7-0) presents the ablation study of the proposed method. Row 1 is the implementation of  $SRe<sup>2</sup>L$  under CDA's hyperparameter settings. Row 2 is simply re-ordering the loops, and the performance at  $1 \times$  is improved; nevertheless, when considering the extreme pruning ratio (i.e.,  $100 \times$ ), it falls short of the existing method. Row 3 computes class-wise BN running statistics in the "squeeze" phase,

<span id="page-7-0"></span>Table 4: Ablation study of the proposed method. C denotes using class-wise matching. CS denotes suing class-wise supervision. ILP denotes using an improved label pool. (IPC50, ResNet18, ImageNet-1K).

| $+C$   | $+CS$          | $+ILP$ | $1\times$ |      | $10\times 20\times$ | $30\times$ | $50\times$ | $100\times$ |
|--------|----------------|--------|-----------|------|---------------------|------------|------------|-------------|
| $\sim$ | $\sim$         |        | 52.0      | 49.4 | 46.4                | 41.1       | 34.8       | 25.4        |
|        | $\overline{a}$ | ÷.     | 54.7      | 51.9 | 48.5                | 42.9       | 37.7       | 22.6        |
|        |                | ÷.     | 55.3      | 53.2 | 49.9                | 45.7       | 39.7       | 29.1        |
|        |                |        | 55.4      | 54.4 | 51.8                | 48.6       | 43.1       | 33.7        |

and these class-wise statistics are used as supervision in the "recover" phase. A steady improvement is observed. Row 4 allows pre-generated labels to be sampled at batch level from different epochs, further boosting the performance. Refer to Appendix [D.1](#page-18-2) for an expanded version of ablation.

Label Pruning Metrics. From Table [5a,](#page-7-1) we empirically find that using different metrics explained in Appendix [E.1](#page-20-2) is no better than random pruning. In addition, as mentioned in FerKD [\[25\]](#page-10-6), calibrating the searching space by discarding a portion of easy or hard images can be beneficial. We conduct a similar experiment to perform random pruning on a calibrated label pool, and the metric for determining easy or hard images is "confidence". However, as shown in Table [5b,](#page-7-1) no such range can consistently outperform the non-calibrated ones (last row). An interesting observation is that the label pruning law at large pruning ratio seems to coincide partially with data pruning, where removing hard labels becomes beneficial [\[37\]](#page-10-18).

Generalization. Table [6a](#page-8-0) shows the performance under large compression rates. Smaller IPC datasets suffer more from label pruning since it requires more augmentation and soft label pairs to boost data diversity. Furthermore, label pruning results on ResNet50 are provided in Table [6b.](#page-8-0) Not only scaling to large networks of the same family (i.e., ResNet) but Table [6c](#page-8-0) also demonstrates

Table 6: Additional ImageNet-1K label pruning results.

<span id="page-8-0"></span>(a) Large pruning rate. (b) Label pruning results on ResNet-50.

(c) Cross-architecture result. IPC50.

| ResNet-18 $50\times 100\times$ |            | ResNet-50 $1 \times 10 \times 20 \times 30 \times 50 \times 100 \times$ Model |  |  |  |                                                                                 | Size Full Acc $1 \times 10 \times 30 \times$ |  |  |
|--------------------------------|------------|-------------------------------------------------------------------------------|--|--|--|---------------------------------------------------------------------------------|----------------------------------------------|--|--|
| IPC <sub>10</sub>              | $17.6$ 9.6 | IPC <sub>10</sub>                                                             |  |  |  | 41.7 37.7 35.4 27.5 22.6 11.0 ResNet-18 [36]                                    | 11.7M 69.76 55.44 54.45 48.62                |  |  |
| IPC20                          | 30.0 17.9  | IPC20                                                                         |  |  |  | 54.4 52.3 48.9 45.4 39.5 24.0 ResNet-50 [36]                                    | 25.6M 76.13 62.24 61.22 56.24                |  |  |
| IPC50                          | 43.1 33.7  | IPC50                                                                         |  |  |  | 62.2 61.2 58.8 56.2 52.3 44.7 EfficientNet-B0 [38] 5.3M 77.69 55.51 54.69 52.10 |                                              |  |  |
| IPC100                         | 52.0 44.7  | <b>IPC100</b>                                                                 |  |  |  | 65.7 65.1 63.9 62.0 59.8 54.2 MobileNet-V2 [39] 3.5M 71.88 49.12 49.26 45.80    |                                              |  |  |
| <b>IPC200</b>                  | 577 526    | <b>IPC200</b>                                                                 |  |  |  | 67.8 67.1 66.7 65.4 64.1 60.1 Swin-V2-Tiny [40] 28.4M 82.07 40.59 37.35 29.54   |                                              |  |  |

P, using ResNet-18. I denotes image storage. L method. "Ours" uses improved label pool.

<span id="page-8-1"></span>Table 7: Label pruning result on ImageNet-21K-Table 8: Label pruning for optimization-free

| denotes label storage. $\dagger$ denotes reported results. |        |  |                                                                                                                                                        |  |            |  | $10\times$ | $20 \times$ | $30\times$                                                | $40\times$ |  |  |                                                                                                                               |  |
|------------------------------------------------------------|--------|--|--------------------------------------------------------------------------------------------------------------------------------------------------------|--|------------|--|------------|-------------|-----------------------------------------------------------|------------|--|--|-------------------------------------------------------------------------------------------------------------------------------|--|
|                                                            |        |  |                                                                                                                                                        |  | $10\times$ |  | $40\times$ |             | ResNet-18   RDED Ours   RDED Ours   RDED Ours   RDED Ours |            |  |  |                                                                                                                               |  |
| IPC                                                        | $\top$ |  | $\mathbb{L}$ SRe <sup>2</sup> L CDA Ours $\mathbb{L}$ Ours $\mathbb{L}$ Ours                                                                           |  |            |  |            |             | IPC10                                                     |            |  |  | 37.9 39.1 32.5 35.7 25.4 30.8 24.0 29.1                                                                                       |  |
|                                                            |        |  | IPC10 3G 643G 18.5 <sup>†</sup> 22.6 <sup>†</sup> 25.4 65G 24.1 16G 21.3<br>IPC20 5G 1285G 20.5 <sup>†</sup> 26.4 <sup>†</sup> 30.3 129G 31.3 32G 29.4 |  |            |  |            |             | IPC20<br><b>IPC50</b><br><b>IPC100</b>                    |            |  |  | 45.8 48.1 41.2 44.3 36.2 39.5 32.9 38.4<br>53.2 54.3 49.9 52.7 48.8 49.7 44.3 48.7<br>57.3 57.8 55.3 57.1 55.2 55.3 51.4 54.2 |  |

the generalization capability of the proposed method across different network architectures. An analogous trend is evident in the context of label pruning: comparable performance is achieved with  $10\times$  fewer labels. This reinforces the statement that the necessity for extensive augmentations and labels can be significantly reduced if the dataset exhibits sufficient diversity.

Large Dataset. ImageNet-21K-P has 10,450 classes, significantly increasing the disk storage as each soft label stores a probability of 10,450 classes. The IPC20 dataset leads to a 1.2 TB (i.e., 1285 GB) label storage, making the existing framework less practical. However, with the help of our method, it can surpass  $SRe^{2}L$  [\[6\]](#page-9-5) by a large margin despite using  $40\times$  less storage. For example, we attain an 8.9% accuracy improvement on IPC20 with label storage reduced from 1285 GB to 32 GB.

Pruning for Optimization-Free Approach. RDED [\[8\]](#page-9-7) is an optimization-free approach during the "recover" phase. However, extensive labels are still required for post-evaluation. To prune labels, consistent improvements are observed using the improved label pool, as shown in Table [8.](#page-8-1)

Comparison with G-VBSM [\[9\]](#page-9-8). Compared to G-VBSM [\[9\]](#page-9-8), which uses an ensemble of 4 models to recover and relabel, our method outperforms it at various pruning ratios with only a single model (see Table [9\)](#page-8-2). Furthermore, the techniques used for G-VBSM apply to our method. By adopting label generation with ensemble and a loss function of "MSE+0.1  $\times$  GT" [\[9\]](#page-9-8), our method can be further improved by a large margin on IPC10 of ImageNet-1K, using ResNet18. Implementation details can be found in Appendix [C.4.](#page-18-3)

<span id="page-8-2"></span>Table 9: Compare with G-VBSM [\[9\]](#page-9-8). "Ours+" uses ensemble and MSE+GT loss.

| IPC10  | G-VBSM | Ours | Ours+ |
|--------|--------|------|-------|
| 1 $×$  | 31.4   | 35.7 | 39.0  |
| 10 $×$ | 28.4   | 32.7 | 37.6  |
| 20 $×$ | 26.5   | 28.6 | 34.8  |
| 30 $×$ | 22.5   | 23.1 | 30.3  |
| 40 $×$ | 18.8   | 20.2 | 27.9  |

Visualization. Fig. [2b](#page-1-0) visualizes our method on three classes. More visualizations are provided in Appendix [F.](#page-23-0)

## 5 Conclusion

To answer the question *"whether large-scale soft labels are necessary for large-scale dataset distillation?"*, we conduct diversity analysis on synthetic datasets. The high within-class similarity is observed and necessitates large-scale soft labels. Our LPLD method re-batches images within classes and introduces class-wise BN supervision during the image synthesis phase to address this issue. These changes improve data diversity, so that simple random label pruning can perform on par with complex rule-based pruning metrics. Additionally, we randomly conduct pruning on an improved label pool. Finally, LPLD is validated by extensive experiments, serving a strong baseline that takes into account actual storage. Limitations and future works are provided in Appendix [E.6.](#page-22-2) The ethics statement and broader impacts can be found in Appendix [E.7.](#page-22-3)

### Acknowledgement

This work was supported in part by A\*STAR Career Development Fund (CDF) under C233312004, in part by the National Research Foundation, Singapore, and the Maritime and Port Authority of Singapore / Singapore Maritime Institute under the Maritime Transformation Programme (Maritime AI Research Programme – Grant number SMI-2022-MTP-06). The computational work for this article was partially performed on resources of the National Supercomputing Centre (NSCC), Singapore (<https://www.nscc.sg>).

## References

- <span id="page-9-0"></span>[1] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-9-1"></span>[2] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 12196–12205, 2022.
- <span id="page-9-2"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, 2022.
- <span id="page-9-3"></span>[4] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *Proc. Int. Conf. Learn. Represent.*, 2021.
- <span id="page-9-4"></span>[5] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proc. IEEE Winter Conf. Appl. Comput. Vis.*, pages 6514–6523, 2023.
- <span id="page-9-5"></span>[6] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *Proc. Adv. Neural Inform. Process. Syst.*, 2023.
- <span id="page-9-6"></span>[7] Zeyuan Yin and Zhiqiang Shen. Dataset distillation in large data era. *arXiv preprint arXiv:2311.18838*, 2023.
- <span id="page-9-7"></span>[8] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. *arXiv preprint arXiv:2312.03526*, 2023.
- <span id="page-9-8"></span>[9] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, 2024.
- <span id="page-9-9"></span>[10] Muxin Zhou, Zeyuan Yin, Shitong Shao, and Zhiqiang Shen. Self-supervised dataset distillation: A good compression is all you need. *arXiv preprint arXiv:2404.07976*, 2024.
- <span id="page-9-10"></span>[11] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *Proc. Int. Conf. Learn. Represent.*, 2021.
- <span id="page-9-11"></span>[12] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *Proc. Adv. Neural Inform. Process. Syst.*, pages 5186–5198, 2021.
- <span id="page-9-12"></span>[13] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *Proc. Adv. Neural Inform. Process. Syst.*, 2022.
- <span id="page-9-13"></span>[14] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *Proc. Adv. Neural Inform. Process. Syst.*, 2022.
- <span id="page-9-14"></span>[15] Zixuan Jiang, Jiaqi Gu, Mingjie Liu, and David Z Pan. Delving into effective gradient matching for dataset condensation. *arXiv preprint arXiv:2208.00311*, 2022.
- <span id="page-9-15"></span>[16] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *Proc. Int. Conf. Mach. Learn.*, pages 12352–12364, 2022.
- <span id="page-9-16"></span>[17] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. In *Proc. Int. Conf. Mach. Learn.*, 2023.
- <span id="page-9-17"></span>[18] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 7856–7865, 2023.

- <span id="page-10-0"></span>[19] Jiawei Du, Yidi Jiang, Vincent TF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, 2023.
- <span id="page-10-1"></span>[20] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *Proc. Int. Conf. Mach. Learn.*, pages 6565–6590. PMLR, 2023.
- <span id="page-10-2"></span>[21] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. DREAM: Efficient dataset distillation by representative matching. *arXiv preprint arXiv:2302.14416*, 2023.
- <span id="page-10-3"></span>[22] Murad Tukan, Alaa Maalouf, and Margarita Osadchy. Dataset distillation meets provable subset selection. *arXiv preprint arXiv:2307.08086*, 2023.
- <span id="page-10-4"></span>[23] Seungjae Shin, Heesun Bae, Donghyeok Shin, Weonyoung Joo, and Il-Chul Moon. Losscurvature matching for dataset selection and condensation. In *International Conference on Artificial Intelligence and Statistics*, pages 8606–8628, 2023.
- <span id="page-10-5"></span>[24] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *Proc. Int. Conf. Mach. Learn.*, 2022.
- <span id="page-10-6"></span>[25] Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In *Proc. Eur. Conf. Comput. Vis.*, pages 673–690, 2022.
- <span id="page-10-7"></span>[26] Zhiqiang Shen. Ferkd: Surgical label adaptation for efficient distillation. In *Proc. Int. Conf. Comput. Vis.*, pages 1666–1675, 2023.
- <span id="page-10-8"></span>[27] Hongxu Yin, Pavlo Molchanov, Jose M Alvarez, Zhizhong Li, Arun Mallya, Derek Hoiem, Niraj K Jha, and Jan Kautz. Dreaming to distill: Data-free knowledge transfer via deepinversion. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 8715–8724, 2020.
- <span id="page-10-9"></span>[28] Hansong Zhang, Shikun Li, Pengju Wang, Dan Zeng, and Shiming Ge. M3d: Dataset condensation by minimizing maximum mean discrepancy. In *Proc. AAAI Conf. Artif. Intell.*, pages 9314–9322, 2024.
- <span id="page-10-10"></span>[29] Tian Qin, Zhiwei Deng, and David Alvarez-Melis. Distributional dataset distillation with subtask decomposition. *arXiv preprint arXiv:2403.00999*, 2024.
- <span id="page-10-11"></span>[30] Yang He, Lingao Xiao, Joey Tianyi Zhou, and Ivor Tsang. Multisize dataset condensation. In *Proc. Int. Conf. Learn. Represent.*, 2024.
- <span id="page-10-12"></span>[31] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J. Gordon. An empirical study of example forgetting during deep neural network learning. In *Proc. Int. Conf. Learn. Represent.*, 2019.
- <span id="page-10-13"></span>[32] Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. In *Proc. Adv. Neural Inform. Process. Syst.*, pages 20596–20607, 2021.
- <span id="page-10-14"></span>[33] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-10-15"></span>[34] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 248–255. Ieee, 2009.
- <span id="page-10-16"></span>[35] Tal Ridnik, Emanuel Ben-Baruch, Asaf Noy, and Lihi Zelnik-Manor. Imagenet-21k pretraining for the masses. *arXiv preprint arXiv:2104.10972*, 2021.
- <span id="page-10-17"></span>[36] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 770–778, 2016.
- <span id="page-10-18"></span>[37] Ben Sorscher, Robert Geirhos, Shashank Shekhar, Surya Ganguli, and Ari Morcos. Beyond neural scaling laws: beating power law scaling via data pruning. In *Proc. Adv. Neural Inform. Process. Syst.*, pages 19523–19536, 2022.
- <span id="page-10-19"></span>[38] Mingxing Tan and Quoc Le. Efficientnet: Rethinking model scaling for convolutional neural networks. In *International conference on machine learning*, pages 6105–6114. PMLR, 2019.
- <span id="page-10-20"></span>[39] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 4510–4520, 2018.

- <span id="page-11-0"></span>[40] Ze Liu, Han Hu, Yutong Lin, Zhuliang Yao, Zhenda Xie, Yixuan Wei, Jia Ning, Yue Cao, Zheng Zhang, Li Dong, et al. Swin transformer v2: Scaling up capacity and resolution. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 12009–12019, 2022.
- <span id="page-11-1"></span>[41] Kaiming He, Haoqi Fan, Yuxin Wu, Saining Xie, and Ross Girshick. Momentum contrast for unsupervised visual representation learning. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 9729–9738, 2020.
- <span id="page-11-2"></span>[42] Terrance DeVries and Graham W Taylor. Improved regularization of convolutional neural networks with cutout. *arXiv preprint arXiv:1708.04552*, 2017.
- <span id="page-11-3"></span>[43] Mingxing Tan and Quoc Le. Efficientnetv2: Smaller models and faster training. In *Proc. Int. Conf. Mach. Learn.*, pages 10096–10106. PMLR, 2021.
- <span id="page-11-4"></span>[44] Xiangyu Zhang, Xinyu Zhou, Mengxiao Lin, and Jian Sun. Shufflenet: An extremely efficient convolutional neural network for mobile devices. In *Proc. IEEE Conf. Comput. Vis. Pattern Recog.*, pages 6848–6856, 2018.
- <span id="page-11-5"></span>[45] Geoff Pleiss, Tianyi Zhang, Ethan Elenberg, and Kilian Q Weinberger. Identifying mislabeled data using the area under the margin ranking. In *Proc. Adv. Neural Inform. Process. Syst.*, pages 17044–17056, 2020.

<span id="page-12-0"></span>

## A Proof

We aim to determine a lower bound on the number of batches (updates)  $n$  required to ensure that the Batch Normalization (BN) statistics for each class in the ImageNet dataset converge within a specified tolerance  $\tau$ , with high probability. The dataset has a varying number of images per class, affecting the probability of each class appearing in a batch during sampling.

### A.1 Preliminary Analysis

**Defining Class Probabilities:** Let  $p_c$  denote the probability that a randomly selected image from the dataset belongs to class c:

> $p_c = \frac{\text{Number of images in class } c}{\text{Total number of images in the de-}$ Total number of images in the dataset.

Due to the unequal distribution of images across classes,  $p_c$  varies among classes.

**Probability of Class Appearance in a Batch:** When sampling a batch of size  $B$ , the probability that class c does not appear in the batch is  $(1 - p_c)^B$ . Therefore, the probability that class c appears in the batch is:

$$
q_c = 1 - (1 - p_c)^B.
$$

This represents the likelihood that at least one image from class  $c$  is included in a given batch.

**Number of Batches:** Let  $n$  be the total number of batches sampled during training.

We assume that batches are sampled independently with replacement from the dataset. Under this assumption, each batch is an independent trial where class  $c$  appears with probability  $q_c$ . Therefore, the number of batches  $M$  where class  $c$  appears follows a binomial distribution:

$$
M \sim \text{Binomial}(n, q_c).
$$

Remark: In practice, batches are often sampled without replacement within an epoch, introducing dependency between batches. However, for large datasets where the total number of images  $N$  is significantly larger than the batch size  $B$  and the number of batches  $n$ , the dependence becomes negligible. In such cases, the binomial distribution serves as a reasonable approximation.

The expected value of  $M$  is:

$$
E[M] = nq_c.
$$

### A.2 Chernoff Bound

To ensure that M is not significantly less than its expected value  $E[M]$ , we apply the Chernoff bound:

$$
\Pr(M \le (1 - \delta)E[M]) \le \exp\left(-\frac{\delta^2 E[M]}{2}\right),\,
$$

where  $\delta \in (0,1)$  represents the acceptable relative deviation from the expected value. This bound provides a way to quantify the probability that a random variable deviates from its expected value, which is crucial for making high-confidence guarantees.

To ensure the probability that M is less than  $(1 - \delta)E[M]$  is at most  $T_1$ , we set:

$$
\exp\left(-\frac{\delta^2 n q_c}{2}\right) \le T_1.
$$

Solving for  $n$ :

$$
n \ge \frac{-2\ln(T_1)}{\delta^2 q_c}.
$$

To ensure this condition holds for all classes, we use the minimum value of  $q_c$ :

$$
n \ge \frac{-2\ln(T_1)}{\delta^2 \min(q_c)}.
$$

### A.3 BN Convergence

BN Statistics Update: Batch Normalization updates its running statistics using an exponential moving average. The update rule for the BN statistics of class c at iteration  $t + 1$  is:

$$
BN_c^{t+1} = (1 - \varepsilon) BN_c^t + \varepsilon B_N^{t+1},
$$

where  $\varepsilon$  is the momentum parameter, and  $\overrightarrow{BN}_c^{t+1}$  $c<sup>i+1</sup>$  is the BN statistics estimated from the current batch for class c.

Since BN statistics for class  $c$  are updated only when class  $c$  appears in the batch, we consider only the updates corresponding to those batches. After  $M$  such updates:

$$
BN_c^{t+M} = (1 - \varepsilon)^M BN_c^t + \varepsilon \sum_{k=1}^M (1 - \varepsilon)^{M-k} B_N^k,
$$

where  $\hat{\text{BN}}_c^k$  $\int_{c}^{\infty}$  is the BN statistics estimated in the k-th batch containing class c.

Assuming  $\hat{\text{BN}}_c^k$  $\int_{c}^{\infty}$  is an unbiased estimator of the true BN statistics  $\mu_{c}$  when class c appears, the expected value is:

$$
E[BN_c^{t+M}] = (1 - \varepsilon)^M BN_c^t + \mu_c [1 - (1 - \varepsilon)^M].
$$

**Convergence Within Tolerance:** To ensure that the BN statistics converge within a tolerance  $\tau$  of the true statistics  $\mu_c$ :

$$
\left|E[\text{BN}_c^{t+M}] - \mu_c\right| \le \tau.
$$

Since:

$$
\left| E[\mathbf{BN}_c^{t+M}] - \mu_c \right| = (1 - \varepsilon)^M \left| \mathbf{BN}_c^t - \mu_c \right|,
$$

and assuming  $|BN_c^t - \mu_c| \leq C$  for some constant C, we have:

$$
(1 - \varepsilon)^M C \le \tau.
$$

Taking natural logarithms:

$$
M\ln(1-\varepsilon) + \ln(C) \le \ln(\tau).
$$

Using the approximation  $ln(1 - \varepsilon) \approx -\varepsilon$  for small  $\varepsilon$ :

$$
-M\varepsilon + \ln(C) \le \ln(\tau).
$$

Solving for M:

$$
M \ge M_0 = \frac{\ln\left(\frac{C}{\tau}\right)}{\varepsilon}.
$$

**Origin of**  $M_0$ : Here,  $M_0$  is derived from the BN convergence requirement that ensures:

$$
(1 - \varepsilon)^{M_0} |\mathbf{BN}_c^t - \mu_c| \le \tau.
$$

It represents the minimum number of updates required for the BN statistics of class  $c$  to converge within the desired tolerance  $\tau$ .

### A.4 Combining Bounds

#### Event Definitions:

- Let  $E_1$  be the event that class c appears in sufficient batches (as guaranteed by the Chernoff bound).
- Let  $E_2$  be the event that the BN statistics for class c converge within the desired tolerance  $\tau$ .

Target Probability: We aim to ensure that both events occur simultaneously with high probability:

$$
P(E_1 \cap E_2) \ge 1 - T.
$$

Union Bound Application: For any two events, the probability of their intersection satisfies:

$$
P(E_1 \cap E_2) = 1 - P(\overline{E_1 \cap E_2})
$$
  
= 1 - P(\overline{E\_1} \cup \overline{E\_2})  
$$
\geq 1 - P(\overline{E_1}) - P(\overline{E_2}).
$$

**Error Probability Allocation:** For simplicity, we allocate the total acceptable failure probability  $T$ equally between the two events:

 $P(\overline{E_1}) \le \frac{T}{2}$  (allocated to Chernoff bound),

  
 $P(\overline{E_2}) \le \frac{T}{2}$  (allocated to BN convergence).

**Chernoff Bound Analysis:** For event  $E_1$ , we require that the probability of class c appearing in fewer than the expected number of batches is at most  $\frac{T}{2}$ :

$$
P(M \le (1 - \delta)nq_c) \le \frac{T}{2}.
$$

Applying the Chernoff bound:

$$
\exp\left(-\frac{\delta^2 n q_c}{2}\right) \le \frac{T}{2}.
$$

Solving for  $n$ :

$$
-\frac{\delta^2 n q_c}{2} \le \ln\left(\frac{T}{2}\right),
$$
  
$$
n \ge \frac{-2\ln\left(\frac{T}{2}\right)}{\delta^2 q_c}.
$$

**BN Convergence Requirement:** For event  $E_2$ , we require that the number of batches  $M$  where class c appears is sufficient for BN convergence:

$$
M \ge M_0 = \frac{\ln\left(\frac{C}{\tau}\right)}{\varepsilon}.
$$

To ensure that this condition holds when event  $E_1$  occurs, we use the fact that, with probability at least  $1 - \frac{T}{2}$ , we have:

$$
M \ge (1 - \delta)nq_c.
$$

Therefore, to guarantee  $M \geq M_0$ , we require:

$$
(1 - \delta)nq_c \ge M_0 = \frac{\ln\left(\frac{C}{\tau}\right)}{\varepsilon}.
$$

Solving for  $n$ :

$$
n \ge \frac{\ln\left(\frac{C}{\tau}\right)}{(1-\delta)\varepsilon q_c}.
$$

**Final Combined Bound:** To ensure that both conditions hold for all classes, we use  $\min(q_c)$ :

$$
n \geq \max \left( \frac{-2\ln\left(\dfrac{T}{2}\right)}{\delta^2\min(q_c)}, \frac{\ln\left(\dfrac{C}{\tau}\right)}{(1-\delta)\varepsilon\min(q_c)} \right),
$$

where  $\delta$  represents the acceptable relative deviation from the expected number of batches,  $\varepsilon$  is the momentum parameter in BN updates, T denotes the acceptable total failure probability  $(T = T_1 + T_2)$ ,  $\tau$  is the convergence threshold for BN statistics, C represents an upper bound on  $|BN_c^t - \mu_c|$  at initialization, and  $\min(q_c)$  represents the minimum probability that a class appears in a batch.

This bound ensures:

- With probability at least  $1 \frac{T}{2}$ , each class c appears in at least  $(1 \delta)nq_c$  batches (event  $E_1$  occurs).
- With probability at least  $1 \frac{T}{2}$ , the BN statistics for each class c converge within tolerance  $\tau$  (event  $E_2$  occurs).
- By the union bound, both events  $E_1$  and  $E_2$  occur simultaneously with probability at least  $1-T$ .

<span id="page-15-0"></span>

### B Dataset Details

We perform experiments on the following three datasets:

- Tiny-ImageNet [\[33\]](#page-10-14) is the subset of ImageNet-1K containing 500 images per class of a total of 200 classes, and spatial sizes of images are downsampled to  $64 \times 64$ .
- ImageNet-1K [\[34\]](#page-10-15) contains 1,000 classes and 1,281,167 images in total. The image sizes are resized to  $224\times224.$
- ImageNet-21K-P [\[35\]](#page-10-16) is the pruned version of ImageNet-21K, containing 10,450 classes and 11,060,223 images in total. Images are sized to  $224 \times 224$  resolution.

<span id="page-15-1"></span>

## C Hyperparameter Settings

<span id="page-15-2"></span>

### C.1 ImageNet-1K

| Table 10: Squeezing and class-wise BN statistics of ImageNet-1K. |                                                                       |
|------------------------------------------------------------------|-----------------------------------------------------------------------|
| Info                                                             | Detail                                                                |
| <b>Total Images</b>                                              | 1,281,167                                                             |
| <b>Batch Size</b>                                                | 256                                                                   |
| <b>BN Updates</b>                                                | 5005                                                                  |
| Source                                                           | https://github.com/pytorch/vision/tree/main/references/classification |

<span id="page-16-0"></span>

| Config             | Value         | Detail                          |
|--------------------|---------------|---------------------------------|
| Iteration          | 4,000         | -                               |
| Optimizer          | Adam          | $\beta_1, \beta_2 = (0.5, 0.9)$ |
| Image LR           | 0.25          | -                               |
| Batch Size         | IPC-dependent | e.g., 50 for IPC50              |
| Initialization     | Random        | -                               |
| BN Loss $(\alpha)$ | 0.01          | -                               |

Table 11: Data Synthesis of ImageNet-1K.

Table 12: Relabel and Validation of ImageNet-1K.

<span id="page-16-1"></span>

| Config       | Value                | Detail                    |
|--------------|----------------------|---------------------------|
| Epochs       | 300                  | -                         |
| Optimizer    | AdamW                | -                         |
| Model LR     | 0.001                | -                         |
| Batch Size   | 128                  | -                         |
| Scheduler    | CosineAnnealing      | -                         |
| EMA Rate     | Not Used             | -                         |
| Augmentation | RandomResizedCrop    | scale ratio = (0.08, 1.0) |
|              | RandomHorizontalFlip | probability = 0.5         |
|              | CutMix               | -                         |

We use Pytorch pretrained ResNet-18 [\[36\]](#page-10-17), with a Top-1 accuracy of 69.76%, as both the recovery and relabeling model. Class-wise BN statistics are computed using a modified version of the training script of the source provided in Table [10.](#page-15-2) The recovery, or data synthesis, phase is provided in Table [11,](#page-16-0) which follows CDA [\[7\]](#page-9-6) except by changing the batch size to an IPC-dependent size. Relabel and validation processes share the same setting as provided in Table [12.](#page-16-1)

<span id="page-16-2"></span>

## C.2 Tiny-ImageNet

|                                            |                                            | Info                       |  |  |  |
|--------------------------------------------|--------------------------------------------|----------------------------|--|--|--|
| 100,000                                    | Total Images                               |                            |  |  |  |
| 256                                        | Batch Size                                 |                            |  |  |  |
| 391                                        | <b>BN</b> Updates                          |                            |  |  |  |
| https://github.com/zeyuanyin/tiny-imagenet |                                            | Source                     |  |  |  |
|                                            | Table 14: Data Synthesis of Tiny-ImageNet. |                            |  |  |  |
|                                            |                                            |                            |  |  |  |
|                                            | Adam                                       |                            |  |  |  |
| $\beta_1, \beta_2 = (0.5, 0.9)$            |                                            | Optimizer                  |  |  |  |
|                                            | 0.1                                        | Image LR                   |  |  |  |
| e.g., 50 for IPC50                         | IPC-dependent                              | <b>Batch Size</b>          |  |  |  |
|                                            | Random                                     | Initialization             |  |  |  |
|                                            |                                            |                            |  |  |  |
| Detail                                     | Value<br>4,000                             | Config<br><b>Iteration</b> |  |  |  |

<span id="page-16-3"></span>Table 13: Squeezing and class-wise BN statistics of Tiny-Imagenet.

<span id="page-17-0"></span>

| Config            | Value                                     | Detail                                             |
|-------------------|-------------------------------------------|----------------------------------------------------|
| Epochs            | 100                                       | -                                                  |
| Optimizer         | SGD                                       | $\rho = 0.9, \epsilon = 0.0001$                    |
| Model LR          | 0.2                                       | -                                                  |
| Batch Size        | 64                                        | -                                                  |
| Warm-up Scheduler | Linear                                    | epoch = 5, $\epsilon$ = 0.01                       |
| Scheduler         | CosineAnnealing                           | -                                                  |
| EMA Rate          | Not Used                                  | -                                                  |
| Augmentation      | RandomResizedCrop<br>RandomHorizontalFlip | scale ratio = $(0.08, 1.0)$<br>probability = $0.5$ |

Table 15: Relabel and Validation of Tiny-ImageNet.

Following SRe<sup>2</sup>L and CDA [\[7\]](#page-9-6), we use a modified version of ResNet-18 [\[41\]](#page-11-1) for Tiny-ImageNet. We modify the training script from Table [13](#page-16-2) to compute class-wise BN statistics. The pretrained model has a Top-1 accuracy of 59.47%, and the model is used for data synthesis and relabel/validation as shown in Table [14](#page-16-3) and Table [15,](#page-17-0) respectively. Note that for the validation phase, a warm-up of 5 epochs is added with a different learning rate scheduler (i.e., linear).

<span id="page-17-1"></span>

## C.3 ImageNet-21K-P

Table 16: Squeezing and class-wise BN statistics of Imagenet-21K-P.

<span id="page-17-2"></span>

| Info                                                | Detail                                      |                                 |
|-----------------------------------------------------|---------------------------------------------|---------------------------------|
| <b>Total Images</b>                                 | 11,060,223                                  |                                 |
| <b>Batch Size</b>                                   | 1,024                                       |                                 |
| <b>BN Updates</b>                                   | 10,801                                      |                                 |
| Source                                              | https://github.com/Alibaba-MIIL/ImageNet21K |                                 |
| Table 17: Data Synthesis of ImageNet-21K-P.         |                                             |                                 |
| Config                                              | Value                                       | Detail                          |
| Iteration                                           | 2,000                                       | -                               |
| Optimizer                                           | Adam                                        | $\beta_1, \beta_2 = (0.5, 0.9)$ |
| Image LR                                            | 0.05                                        | -                               |
| Batch Size                                          | IPC-dependent                               | e.g., 20 for IPC20              |
| Initialization                                      | Random                                      | -                               |
| BN Loss $(\alpha)$                                  | 0.25                                        | -                               |
| Table 18: Relabel and Validation of ImageNet-21K-P. |                                             |                                 |
| Config                                              | Value                                       | Detail                          |
| Epochs                                              | 300                                         | -                               |
| Optimizer                                           | AdamW                                       | decay = 0.01                    |
| Model LR                                            | 0.002                                       | -                               |
| Batch Size                                          | 32                                          | -                               |
| Scheduler                                           | CosineAnnealing                             | -                               |
| <b>Label Smoothing</b>                              | 0.2                                         | -                               |
| <b>EMA Rate</b>                                     | Not Used                                    | -                               |
| Augmentation                                        | RandomResizedCrop<br>CutOut                 | scale ratio = $(0.08, 1.0)$     |

<span id="page-17-3"></span>Following CDA [\[7\]](#page-9-6), we use ResNet-18 trained for 80 epochs initialized with well-trained ImageNet-1K weight [\[35\]](#page-10-16). Class-wise BN statistics are computed using a modified version of the training script of the source provided in Table [16.](#page-17-1) The pretrained ResNet-18 on ImageNet-21K-P has a Top-1 accuracy of 38.1%, and the model is used for data synthesis and relabel/validation as shown in Table [17](#page-17-2) and Table [18,](#page-17-3) respectively. Note that CutMix used in ImageNet-1K is replaced with CutOut [\[42\]](#page-11-2), and a relatively large label smooth of 0.2 is used during the ImageNet-21K-P pretraining phase. We incorporate the same changes to the relabel/validation phase of the synthetic dataset.

<span id="page-18-3"></span>

### C.4 Implementation of Baselines

**RDED.** RDED [\[8\]](#page-9-7) has several different changes to the  $SRe^{2}L$  settings. (1) The batch size is adjusted according to the IPC size (i.e., 100 for IPC10 and 200 for IPC50). (2) It uses additional augmentation (i.e., ShufflePatch to shuffle the position of patches). Such augmentations are considered additional storage since the exact order of patch shuffling needs to be stored. (3) Weaker augmentation (i.e., a larger lower bound for the random area of the resized crop). (4) A smoothed learning rate scheduler. We adhere to all the changes for experiments regarding RDED.

G-VBSM. In Table [9,](#page-8-2) we adopt the several techniques used for G-VBSM [\[9\]](#page-9-8). (1) Soft labels are generated with an ensemble of models. Specifically, we use ResNet18 [\[36\]](#page-10-17), MobileNetV2 [\[39\]](#page-10-20), EfficientNet-B0 [\[43\]](#page-11-3), ShuffleNetV2-0.5 [\[44\]](#page-11-4). (2) Logit Normalization is used to keep the same label storage. (3) A different MSE+ $\gamma \times$ GT loss replaces KL divergence, where  $\gamma = 0.1$ .

## D Additional Experiments

<span id="page-18-4"></span><span id="page-18-2"></span>

### D.1 Ablation

Table 19: Ablation study of the proposed method. C denotes using class-wise matching. CS denotes suing class-wise supervision. ILP denotes using an improved label pool. (IPC50, ResNet18, ImageNet-1K).

|  | +C +CS +ILP $1 \times 10 \times 20 \times 30 \times 50 \times 100 \times$ |  |  |                                        |  |
|--|---------------------------------------------------------------------------|--|--|----------------------------------------|--|
|  | $-52.0$ 49.4 46.4 41.1 34.8 25.4                                          |  |  |                                        |  |
|  | $\checkmark$ - - 54.7 51.9 48.5 42.9 37.7 22.6                            |  |  |                                        |  |
|  | $\checkmark$ - $\checkmark$ 54.7 52.9 49.9 46.2 40.7 27.0                 |  |  |                                        |  |
|  | $\sqrt{ }$ $\sqrt{ }$ - 55.3 53.2 49.9 45.7 39.7 29.1                     |  |  |                                        |  |
|  |                                                                           |  |  | $\sqrt{55.4}$ 54.4 51.8 48.6 43.1 33.7 |  |

Table [19](#page-18-4) presents an expanded version of Table [4.](#page-7-0) The row highlighted in grey outlines the ablation study on class-wise supervision, demonstrating that the ILP component (Improved Label Pool) enhances performance independently of class-wise supervision.

<span id="page-18-5"></span><span id="page-18-1"></span>

### D.2 Scaling on Large IPCs

Table 20: Experiment on the scalability of large IPCs. T denotes the total storage of images and labels, and storage is measured in GB. The validation model is ResNet18.

|                                             |  |  | $\parallel$ 1 × T $\parallel$ 30 × T $\parallel$ 40 × T                                                                                        |  |
|---------------------------------------------|--|--|------------------------------------------------------------------------------------------------------------------------------------------------|--|
| IPC300<br><b>IPC400</b>                     |  |  | $\begin{array}{ c c c c c c c c } \hline 65.3 & 178 & 62.6 & 10 & 61.9 & 9 \\ \hline 67.4 & 237 & 65.2 & 13 & 64.6 & 12 \\ \hline \end{array}$ |  |
| $\text{ImageNet-1K}$   69.8 138   - -   - - |  |  |                                                                                                                                                |  |

Table [20](#page-18-5) demonstrates that our method exhibits commendable scalability across large IPCs. We observe non-marginal enhancements when deploying even larger IPCs, such as IPC300 and IPC400. Moreover, our approach achieves nearly identical accuracy levels — specifically, 65.3% vs. 65.2% — when comparing the use of IPC300 at  $1 \times$  with IPC400 at  $30 \times$  less labels. Compared to the full ImageNet-1K dataset, our method preserves a large portion of the accuracy with  $10\times$  less storage. This performance is achieved despite the vastly different storage requirements of 178G and 13G, respectively, indicating a higher flexibility of IPC choice with a fixed storage budget.

<span id="page-18-0"></span>

### D.3 Comparison with Fast Knowledge Distillation [\[25\]](#page-10-6)

The label quantization technique mentioned in Fast Knowledge Distillation (FKD) [\[25\]](#page-10-6) is orthogonal to the proposed method for several reasons. Firstly, as demonstrated in Table [21,](#page-19-0) there are six components related to soft labels. FKD only compresses the prediction logits (component 6), while the our method addresses all six components.

Secondly, even for the overlapping storage component (component 6: prediction logits), the compression targets differ between FKD and our method, as shown in Table [22.](#page-19-1) The total stored prediction logits can be approximated by the formula: number of condensed images  $\times$  number of augmentations  $\times$  dimension of logits. FKD's label quantization focuses on compressing the dimension of logits, whereas the proposed label pruning method focuses on compressing the number of augmentations.

<span id="page-19-0"></span>Table 21: Different storage components between FKD and the proposed method. FKD, originally for model distillation, requires storage only for components 1, 2, and 6. Adapting it to dataset distillation requires additional storage for components 3, 4, and 5.

| Components of Storage                 | FKD | Proposed Method |
|---------------------------------------|-----|-----------------|
| 1. coordinates of crops               | ×   | ✓               |
| 2. flip status                        | ×   | ✓               |
| 3. index of cutmix images             | ×   | ✓               |
| 4. strength of cutmix                 | ×   | ✓               |
| 5. coordinates of cutmix bounding box | ×   | ✓               |
| 6. prediction logits                  | ✓   | ✓               |

<span id="page-19-1"></span>Table 22: Breakdown explanation for component 6 (prediction logits) storage between FKD's label quantization and the proposed label pruning. The number of condensed images is computed by  $N =$  $\text{IPC} \times \text{number\_of\_classes}$ . FKD's compression target is dimension\_of\_logits, while the proposed method's target is number of augmentations.

| Method                    | Number of<br>Augmentations per Image | Dimension of<br>Logits per Augmentation | Total Storage for<br>Prediction Logits |
|---------------------------|--------------------------------------|-----------------------------------------|----------------------------------------|
| Baseline (no compression) | 300                                  | 1,000                                   | $N 	imes 300 	imes 1000$               |
| Label Quantization (FKD)  | 300                                  | 10                                      | $N 	imes 300 	imes 10$                 |
| Label Pruning (Proposed)  | 3                                    | 1,000                                   | $N 	imes 3 	imes 1000$                 |

Although FKD's approach is orthogonal to our method, a comparative analysis was conducted to better understand their relative performance. Table [23](#page-19-2) presents a detailed comparison between FKD's two label quantization strategies (Marginal Smoothing and Marginal Re-Norm) and the proposed method. It is important to note that FKD only compresses component 6, with the compression rate related to hyper-parameter K. Components 1-5 remain uncompressed ( $1 \times$  rate) in FKD. Additionally, FKD's quantized logits store both values and indices, so their actual storage is doubled, and their compression rate is halved.

This analysis has yielded two key observations. First, our method demonstrates higher accuracy at comparable compression rates. For IPC10, our method achieves  $32.70\%$  accuracy at  $10\times$  compression, while FKD only reaches 18.10% at  $8.2 \times$  compression. Second, our method exhibits better compression at similar accuracy levels. On IPC10, our method attains 20.20% accuracy at  $40\times$ compression, whereas FKD achieves 19.04% at just 4.5x compression.

<span id="page-19-2"></span>Table 23: Comparison between FKD's two label quantization strategies (Marginal Smoothing and Marginal Re-Norm) and ours.

| Method                      | Compression Rate of |                    | Full                    | Accuracy $(\% )$ |
|-----------------------------|---------------------|--------------------|-------------------------|------------------|
|                             | Component 1-5       | Component 6        | <b>Compression Rate</b> | on IPC10         |
| Baseline (no compression)   | $1\times$           | $1\times$          | $1\times$               | 34.60            |
| $FKD$ (Smoothing, $K=100$ ) | $1\times$           | $(10/2)=5\times$   | $4.5\times$             | 18.70            |
| FKD (Smoothing, K=50)       | $1\times$           | $(20/2)=10\times$  | $8.2\times$             | 15.53            |
| $FKD$ (Smoothing, $K=10$ )  | $1\times$           | $(100/2)=50\times$ | $23.0\times$            | 9.20             |
| $FKD$ (Re-Norm, $K=100$ )   | $1\times$           | $(10/2)=5\times$   | $4.5\times$             | 19.04            |
| $FKD$ (Re-Norm, $K=50$ )    | $1\times$           | $(20/2)=10\times$  | $8.2\times$             | 18.10            |
| $FKD$ (Re-Norm, $K=10$ )    | $1\times$           | $(100/2)=50\times$ | $23.0\times$            | 15.52            |
| Ours $(10\times)$           | $10\times$          | $10\times$         | $10\times$              | 32.70            |
| Ours $(20\times)$           | $20\times$          | $20\times$         | $20\times$              | 28.60            |
| Ours $(40\times)$           | $40\times$          | $40\times$         | $40\times$              | 20.20            |

## E Additional Information

<span id="page-20-2"></span>

### E.1 Label Pruning Metrics

We determine labels according to the statistics of the auxiliary information:

- 1. correct: the number of correctly classified images [\[31\]](#page-10-12)
- 2. diff: the absolute difference between the Top-2 outputs
- 3. signed\_diff: the signed difference between Top-2 output [\[45\]](#page-11-5)
- 4. cut\_ratio: the cut-mix ratio
- 5. confidence: the value of the largest output [\[26\]](#page-10-7).

These metrics serve for the baselines compared to random label pruning in Table [5](#page-7-1) After knowing the metric, knowing which data type to prune (i.e., "easy", "hard", or "uniform") is important. Additionally, FerKD [\[26\]](#page-10-7) argues the reliability of generated soft labels and proposes to use neither too easy nor too hard samples.

<span id="page-20-3"></span><span id="page-20-0"></span>

### E.2 Image and Label Storage

Table 24: Image and label storage.  $\mathbb I$  denotes image storage.  $\mathbb L$  denotes label storage. "Ratio" is label-to-image ratio.

| ImageNet-1K (GB) |      |        |       |
|------------------|------|--------|-------|
| Storage          | I    | L      | Ratio |
| IPC10            | 0.15 | 5.67   | 37.0  |
| IPC20            | 0.30 | 11.33  | 37.6  |
| IPC50            | 0.75 | 28.33  | 37.9  |
| IPC100           | 1.49 | 56.66  | 38.0  |
| IPC200           | 2.98 | 113.33 | 38.0  |
| IPC300           | 4.76 | 172.63 | 36.3  |

Table. [24](#page-20-3) shows that stored labels are more than  $10 \times$ ,  $30 \times$ , and  $200 \times$  sized of the image storage, depending on the number of classes of the dataset.

<span id="page-20-1"></span>

### E.3 Theoretical Analysis on the Number of Updates

Our experiments are grounded in a careful analysis of the number of updates required for stable Batch Normalization (BN) statistics. We begin by examining the derived bound from Eq. [8:](#page-5-0)

$$
n \geq \max \left( \underbrace{\frac{-2\ln\left(\frac{T}{2}\right)}{\delta^2\min(q_c)}}_{\text{Chernoff Bound}}, \quad \underbrace{\frac{\ln\left(\frac{C}{\tau}\right)}{(1-\delta)\varepsilon\min(q_c)}}_{\text{BN Convergence}} \right) .
$$

To evaluate this bound, we substitute the following values:

- $T = 0.05$  (acceptable total failure probability, corresponding to 95% confidence)
- $\delta = 0.2$  (acceptable relative deviation from the expected number of batches)
- $\varepsilon = 0.1$  (momentum parameter in BN)
- min( $p_c$ ) =  $\frac{732}{1,281,167} \approx 0.0005711$  (ratio of the least number of images in a class to total images)
- $B = 256$  (batch size)
- min $(q_c) = 1 (1 \min(p_c))^B$  (minimum probability that any class appears in a batch)

First, we compute  $min(q_c)$ :

$$
\min(q_c) = 1 - (1 - \min(p_c))^B
$$

$$
= 1 - (1 - 0.0005711)^{256}
$$

$$
= 1 - (0.9994289)^{256}
$$

$$
\approx 1 - e^{-256 \times 0.0005711} \quad (\text{since } \min(p_c) \text{ is small})
$$

$$
= 1 - e^{-0.1462}
$$

$$
\approx 1 - 0.8639 = 0.1361.
$$

Thus,  $\min(q_c) \approx 0.1361$ .

Next, we compute the two parts of the bound separately.

From Chernoff Bound Term: Given that we allocate the total failure probability  $T$  equally between the two events, we have  $T/2 = 0.025$ .

$$
n 
\ge 
\frac{-2 
\ln\left(\frac{T}{2}\right)}{\delta^2 
\min(q_c)}
$$
 $= 
\frac{-2 
\ln(0.025)}{(0.2)^2 
\times 0.1361}$  $= 
\frac{-2 
\times (-3.6889)}{0.04 
\times 0.1361}$  (since ln(0.025) = -3.6889) $= 
\frac{7.3778}{0.005444}$  $\approx 1,355.2.$ 

From BN Convergence Term: We need to specify C and  $\tau$ . Let's assume:

- $C = 1$  (an upper bound on  $|BN_c^t \mu_c|$  at initialization, as the running mean is typically initialized to zero)
- $\tau = 0.01$  (desired convergence tolerance)

Compute the numerator:

$$
\ln\left(\frac{C}{\tau}\right) = \ln\left(\frac{1}{0.01}\right) = \ln(100) = 4.6052.
$$

Now, compute the denominator:

$$
(1 - \delta)\varepsilon \min(q_c) = (1 - 0.2) \times 0.1 \times 0.1361 = 0.8 \times 0.1 \times 0.1361 = 0.010888.
$$

Compute the second part:

$$
n 
\ge 
\frac{\ln\left(\frac{C}{\tau}\right)}{(1-\delta)\varepsilon \min(q_c)}
$$
$$
= 
\frac{4.6052}{0.010888}
$$
$$
\approx 423.08.
$$

# Final Bound:

 $n \ge \max(1, 355.2, 423.08) = 1,355.2 \approx 1,356$  (rounding up to the nearest whole number).

This theoretical result indicates that approximately 1, 356 batches are needed for stable BN statistics with the specified parameters.

**Practical Implications:** This observation leads to a key insight: pretrained models have already undergone sufficient updates to achieve stable BN statistics. Specifically, in the context of ImageNet-1K:

Updates per epoch = 
$$
\frac{1,281,167}{256}
$$
 ≈ 5,005 updates > 1,356.

Since one epoch consists of approximately 5, 005 updates, which is substantially more than the theoretical requirement of 1, 356 batches, we can confirm that a single epoch of training is sufficient for the BN statistics of each class to converge within the desired tolerance with high probability.

<span id="page-22-4"></span><span id="page-22-0"></span>

### E.4 Class-wise Statistics Storage

Table 25: Additional storage required for class-wise statistics. The model is ResNet-18, and storage is measured in MB.

|               | Tiny-ImageNet | ImageNet-1K | ImageNet-21K-P |
|---------------|---------------|-------------|----------------|
| Original      | 43.06         | 44.66       | 247.20         |
| + Class Stats | 50.41         | 81.30       | 445.87         |
| Diff.         | 7.35          | 36.64       | 198.67         |

The additional storage allocation for class-specific statistics is detailed in Table [25.](#page-22-4) It is observed that this storage requirement escalates with an increase in the number of classes. However, this is a one-time necessity during the recovery phase and becomes redundant once the synthetic data generation is completed.

<span id="page-22-1"></span>

## E.5 Computing Resources

Experiments are performed on 4 A100 80G GPU cards. We notice that for Tiny-ImageNet, there is a slight performance drop when multiple GPU cards are used with DataParallel in PyTorch. Therefore, we use 4 GPU cards for ImageNet-1K and ImageNet-21K-P experiments and 1 GPU card for all Tiny-ImageNet experiments.

<span id="page-22-2"></span>

### E.6 Limitation and Future Work

We recognize that there are several limitations and potential areas for further investigation. Firstly, while our work significantly reduces the required storage, the process for generating the soft labels is still necessary, as we randomly select from this label space. Secondly, reducing the required labels may not directly lead to a reduced training speed, as the total training epochs remain the same in order to achieve the best performance. Future work is warranted to reduce label storage as well as the required training budget simultaneously.

<span id="page-22-3"></span>

### E.7 Ethics Statement and Broader Impacts

Our research study focuses on dataset distillation, which aims to preserve data privacy and reduce computing costs by generating small synthetic datasets that have no direct connection to real datasets. However, this approach does not usually generate datasets with the same level of accuracy as the full datasets.

In addition, our work in reducing the size of soft labels and enhancing image diversity can have a positive impact on the field by making large-scale dataset distillation more efficient, thereby reducing storage and computational requirements. This efficiency can facilitate broader access to advanced machine learning techniques, potentially fostering innovation across diverse sectors.

<span id="page-23-0"></span>

## F Visualization

In this section, we present visualizations of the datasets used in our experiments. Due to the different matching objectives, datasets of different IPCs should have distinct images. Therefore, we provide the visualization of different IPCs. Figure [6](#page-23-1) shows randomly sampled images from ImageNet-1K at various IPC. Figure [7](#page-24-0) depicts the Tiny-ImageNet dataset with IPC50 and IPC100. Figure [8](#page-24-1) provides visualizations of ImageNet-21K-P at IPC10 and IPC20.

# F.1 ImageNet-1K

<span id="page-23-1"></span>Image /page/23/Picture/3 description: The image is a 5x5 grid of abstract paintings. The paintings vary in color and subject matter, with some appearing to depict animals, landscapes, or still life objects. The overall style is impressionistic and dreamlike, with blurred lines and vibrant colors.

(a) IPC10 (b) IPC50

Image /page/23/Picture/7 description: A grid of 25 images, each depicting a different scene or object with an abstract, painterly style. The images include close-ups of animals like dogs and birds, still lifes of fruit and household items, and outdoor scenes with water and greenery. The overall impression is a collection of artistic interpretations of various subjects.

(c) IPC100 (d) IPC200

) IPC200

Figure 6: Visualization of ImageNet-1K. Images are randomly sampled.

# F.2 Tiny-ImageNet

<span id="page-24-0"></span>Image /page/24/Picture/1 description: The image displays two grids of images, labeled (a) IPC50 and (b) IPC100. Each grid contains 100 smaller images arranged in a 10x10 formation. The smaller images appear to be diverse, showcasing various subjects such as animals (birds, cats), nature scenes (landscapes, trees, water), and everyday objects. The overall presentation suggests a comparison or collection of image samples related to the labels IPC50 and IPC100.

Figure 7: Visualization of Tiny-ImageNet. Images are randomly sampled.

Image /page/24/Figure/3 description: The image contains the text "F.3 ImageNet-21K-P" in a bold, sans-serif font.

<span id="page-24-1"></span>Image /page/24/Picture/4 description: The image displays two grids of abstract, colorful images, labeled (a) IPC10 and (b) IPC20. Each grid contains 20 smaller square images arranged in a 4x5 layout. The images themselves are highly stylized and abstract, with a mix of vibrant colors and blurred shapes, making it difficult to discern specific objects or scenes with certainty. Some images appear to hint at recognizable forms like animals, landscapes, or everyday objects, but they are rendered in a very impressionistic or dreamlike manner.

Figure 8: Visualization of ImageNet-21K-P. Images are randomly sampled.
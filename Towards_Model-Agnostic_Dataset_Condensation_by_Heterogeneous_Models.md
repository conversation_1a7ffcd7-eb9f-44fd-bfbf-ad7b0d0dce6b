# Towards Model-Agnostic Dataset Condensation by Heterogeneous Models

<PERSON><PERSON><PERSON><PERSON><sup>o</sup>[,](https://orcid.org/0000-0003-4533-4875) <PERSON><sup>\*</sup><sup>o</sup>, and Gyeong-Moon Park<sup>\*</sup>

Kyung Hee University, Yongin, Republic of Korea {moonjunyyy, ju.kim, gmpark}@khu.ac.kr

Abstract. The advancement of deep learning has coincided with the proliferation of both models and available data. The surge in dataset sizes and the subsequent surge in computational requirements have led to the development of the Dataset Condensation (DC). While prior studies have delved into generating synthetic images through methods like distribution alignment and training trajectory tracking for more efficient model training, a significant challenge arises when employing these condensed images practically. Notably, these condensed images tend to be specific to particular models, constraining their versatility and practicality. In response to this limitation, we introduce a novel method, Heterogeneous Model Dataset Condensation (HMDC), designed to produce universally applicable condensed images through cross-model interactions. To address the issues of gradient magnitude difference and semantic distance in models when utilizing heterogeneous models, we propose the Gradient Balance Module (GBM) and Mutual Distillation (MD) with the Spatial-Semantic Decomposition method. By balancing the contribution of each model and maintaining their semantic meaning closely, our approach overcomes the limitations associated with model-specific condensed images and enhances the broader utility. The source code is available in <https://github.com/KHU-AGI/HMDC> .

Keywords: Dataset condensation · Model agnostic · Heterogeneous

## 1 Introduction

In recent years, deep learning [\[25,](#page-15-0) [26,](#page-15-1) [32\]](#page-16-0) has demonstrated a remarkable surge in both effectiveness and applicability across diverse domains [\[8,](#page-14-0)[11,](#page-14-1)[17,](#page-15-2)[33\]](#page-16-1). With the increasing depth and complexity of models, the need for substantial datasets has become imperative to sustain their performance and forestall overfitting [\[5,](#page-14-2)[19\]](#page-15-3). Yet, the challenges extend beyond the mere acquisition of huge datasets to management and efficient utilization. In this context, techniques such as dataset distillation (DD) [\[35\]](#page-16-2) or dataset condensation (DC) [\[38\]](#page-16-3) have emerged, aiming to address these challenges by offering more efficient data management strategies. These methods not only enable the selection of core-sets [\[1,](#page-14-3) [9,](#page-14-4) [12\]](#page-14-5) capable of maintaining the original performance using a whole dataset but also facilitate a

<sup>⋆</sup> Corresponding authors

<span id="page-1-0"></span>Fig. 1: Accuracy plots illustrating the performance of different models trained using images generated by recent dataset condensation methods on the CIFAR-10 dataset with an IPC10 setting. Each bar signifies a performance comparison relative to randomly selected images on 10 images per class, with the initial state of each method identical to that of the random image. Notably, the methods exhibit over-condensation on ConvNet, resulting in performance degradation on other models.

Image /page/1/Figure/2 description: A bar chart displays the difference in performance with random selection for various models across different datasets. The y-axis represents the percentage difference, ranging from -40.00% to 20.00%. The x-axis shows four datasets: CAFE (CVPR 2022), IDC (ICML 2022), IDM (CVPR 2023), and DREAM (ICCV 2023), followed by HMDC (Ours). For each dataset, five bars represent different models: ConvNet (blue), ResNet18 (orange), ResNet50 (green), ViT-Tiny (purple), and ViT-Base (dark blue). Most bars show a negative difference, indicating a performance drop compared to random selection, with some exceptions like ConvNet and ResNet18 in the DREAM dataset, and all models in the HMDC dataset showing positive differences. Specifically, for HMDC (Ours), ConvNet shows approximately 12.00%, ResNet18 approximately 10.00%, ResNet50 approximately 5.00%, ViT-Tiny approximately 14.00%, and ViT-Base approximately 12.00% difference.

dramatic reduction in dataset size through the creation of synthetic data  $[2, 3, 3]$  $[2, 3, 3]$  $[2, 3, 3]$ [21,](#page-15-4) [28,](#page-15-5) [30,](#page-15-6) [34,](#page-16-4) [35,](#page-16-2) [38,](#page-16-3) [40,](#page-16-5) [41\]](#page-16-6) that accurately represents the original dataset.

Traditionally, dataset condensation methods have employed a compact 3 layered model named ConvNet [\[38\]](#page-16-3) for the condensation process [\[2,](#page-14-6) [28,](#page-15-5) [34,](#page-16-4) [40\]](#page-16-5). The standard evaluation method for assessing the performance of condensed images has been the ConvNet-to-ConvNet test, where condensation and evaluation are conducted on ConvNet. Some studies have delved into assessing the general performance of condensed images on other shallow models, such as 3 layered MLP or AlexNet [\[24\]](#page-15-7). However, as illustrated in Figure [1,](#page-1-0) the effectiveness of generated images diminishes when applied to widely used models like ResNet [\[13\]](#page-15-8) and Vision Transformer (ViT) [\[8\]](#page-14-0). This indicates that synthetic images are over-condensed on ConvNet, showing a high model dependence. This dependency significantly constrains the versatility of condensed images. Introducing a completely new model necessitates training a new model and generating a new condensed image, implying that training data must be stored in some way. Consequently, there is a need for a model-agnostic benchmark, not limited to ConvNet-to-ConvNet, that operates independently of the specific model.

The primary challenge in achieving model-agnostic dataset condensation lies in identifying the common characteristics within a model and devising an effective method for their extraction. It is difficult to distinguish between features with general recognition information and those excessively tailored to a particular model. To address this challenge, for the first time, we introduce a novel approach that utilizes two models to extract generalized knowledge without bias towards any specific model. To this end, we propose a novel dataset condensation method, Heterogeneous Model Dataset Condensation (HMDC), leveraging heterogeneous models to extract common features that are more universally applicable.

When naively employing two heterogeneous models simultaneously for dataset condensation, two difficult issues arise. Firstly, there is a problem of gradient magnitude difference, characterized by significant differences in the size of the gradient provided to the synthetic image due to structural or depth variations between the two models. This discrepancy can lead to the disregard of one model or the failure of the image to converge. To alleviate this, we present a Gradient Balance Module (GBM), which accumulates the gradient magnitude of each optimization target, to control the magnitude of the loss. Thus, even if two models have different structures, they can have a similar impact on the synthetic image.

Another challenge arises in the semantic distance resulting from different knowledge between models. As the two models undergo learning, they converge towards optimal points specific to other models, leading to a growing of semantic distance and instability in image convergence. To tackle this, we propose a Mutual Distillation (MD) by Spatial-Semantic Decomposition of the two models and feature matching throughout the process. This process enables consistent updates of synthetic images regardless of the model, avoiding over-condensation on any particular model by obtaining information from different models. This characteristic makes our method effective in a model-agnostic setting.

Our main contributions can be summarized as follows:

- For the first time, we present Heterogeneous Model Dataset Condensation (HMDC) for model-agnostic dataset condensation, which resolves the overcondensation issue to a specific model.
- To facilitate the convergence of synthetic images, we propose the Gradient Balance Module to control a gap between the gradient magnitudes of heterogeneous models.
- We propose Mutual Distillation by Spatial-Semantic Decomposition feature matching of heterogeneous models to fill in the semantic distance between models.
- From the extensive experiments, we demonstrate that our condensed images consistently show great performances from shallow models to widely used large models.

## 2 Related Work

### 2.1 Dataset Condensation

Through the exploration of various core-set selection methodologies [\[1,](#page-14-3) [9,](#page-14-4) [12\]](#page-14-5), it has become evident that synthesized images generated through optimization procedures exhibit greater efficacy compared to direct utilization of real images. Optimization techniques for generating synthetic images primarily fall into

two categories: those concerned with tracking training trajectories and those focused on aligning feature distributions. The trajectory tracking approach involves aligning the gradients [\[21,](#page-15-4) [28,](#page-15-5) [38,](#page-16-3) [41\]](#page-16-6) between real and synthetic images during the training process, or adapting the weights of the model trained on synthetic images to resemble the model trained on real images [\[2\]](#page-14-6). These techniques aim to generate synthetic images based on their influence on the model's learning process. Another stream of condensation methods emphasizes feature distribution matching at intermediate layers [\[34\]](#page-16-4), or output distribution alignment with the synthetic images [\[30,](#page-15-6)[35,](#page-16-2)[40,](#page-16-5)[41\]](#page-16-6). These approaches aim to generate synthetic images by emphasizing feature similarity within the synthetic image but they still depend on utilizing an intermediate training state of the model. In this work, we use the gradient matching method, which has shown better performance in previous studies  $[21, 28, 38, 41]$  $[21, 28, 38, 41]$  $[21, 28, 38, 41]$  $[21, 28, 38, 41]$ . In the existing studies  $[2, 28, 34, 40]$  $[2, 28, 34, 40]$  $[2, 28, 34, 40]$  $[2, 28, 34, 40]$ , synthetic images were typically generated using small 3-layer models, resulting in images that exhibited limited compatibility with other models. In contrast to conventional approaches, our method employs two distinct models to generate a balanced condensed image that avoids being overly biased toward either model, addressing limitations observed in earlier approaches.

## 2.2 Knowledge Distillation

Knowledge Distillation (KD), as introduced by Hinton et al. [\[16\]](#page-15-9), is a technique in machine learning where a smaller model known as the student model trains to replicate the behavior of a larger model which is the teacher model. This process is done to transfer the knowledge and generalization capabilities of the teacher model to the smaller and more efficient student model. This transfer of knowledge in KD is achieved by aligning what is often referred to as dark knowledge, which can manifest as either logits [\[10,](#page-14-8) [29,](#page-15-10) [36,](#page-16-7) [39\]](#page-16-8) or features [\[14,](#page-15-11) [15,](#page-15-12) [18,](#page-15-13) [22\]](#page-15-14).

While the primary objective of KD is to create a more efficient smaller model that performs similarly to a larger one, in this study, we leverage KD to specifically reduce the semantic distance between models. This approach enables learning from a single image through the knowledge of two distinct models, thereby ensuring stable learning without the risk of collapse.

### 2.3 Utilization of Heterogeneity

Previous methods for adjusting loss or gradient have primarily focused on using the uncertainty [\[20\]](#page-15-15) or norm of the gradient [\[4\]](#page-14-9) to balance multi-task learning or employing multiple adaptors for domain-robust models [\[27\]](#page-15-16). In this work, we claim that while there is a single task, heterogeneity is necessary to solve it effectively. We decompose the features of the image model into spatial and semantic information, allowing us to simultaneously leverage the knowledge of two models with distinct features. By accumulating the gradient norm, we can identify differences in the average gradient and appropriately scale it to inject more general features into the synthetic images, thereby compensating for the imbalance in learning caused by the structures of models.

<span id="page-4-0"></span>Fig. 2: Diagram of Heterogeneous Model Dataset Condensation (HMDC), where two distinct models are employed for feature extraction. These features undergo dimension adjustment through Spatial-Semantic Decomposition, a critical step facilitating Mutual Distillation, and enhancing knowledge sharing between the two models. Throughout the dataset condensation process, the compensatory Gradient Balance Module comes into play, mitigating gradient variations inherent to different models. This module ensures the extraction of general knowledge by harmonizing gradient magnitudes, thus contributing to a more universally applicable condensation process.

Image /page/4/Figure/2 description: This is a diagram illustrating a method for heterogeneous model dataset condensation and mutual distillation. The process begins with a real image and a synthetic image, which are fed into two different models, Model 1 and Model 2. Both models extract spatial and semantic features. The spatial features are then processed through an interpolation step and concatenated with semantic features, followed by an affine transformation. The output of Model 1's head and the processed features from Model 2 are used to calculate a mean squared error loss (L\_MSE) and its gradient (∇L). Similarly, Model 2's head and processed features from Model 1 are used for another L\_MSE calculation and gradient. These components are then fed into a Gradient Balancing Module, which appears to adjust the gradients based on the L\_MSE values. The diagram also shows a legend indicating a 'Real Image Path' (blue arrow), a 'Synthetic Image Path' (orange arrow), and a 'Concatenate' operation (⊕ symbol).

# 3 Method

### 3.1 Problem Formulation

Dataset Condensation (DC) [\[38\]](#page-16-3) is an approach that aims at creating a synthetic dataset denoted as  $S = (\mathbf{x}_i, y_i)_{i=1}^{|\mathcal{S}|}$  from the complete training data  $\mathcal{T} =$  $(\mathbf{x}_i, y_i)_{i=1}^{|\mathcal{T}|}$ , where  $|\mathcal{S}| \ll |\mathcal{T}|$ . This synthetic dataset,  $\mathcal{S}$ , is designed to train a model to a performance level comparable to what could be achieved with the original data. The dataset  $S$  can encompass a subset of  $T$ . However, recent research has unveiled that the utilization of synthetic data yields superior performance [\[2,](#page-14-6) [3,](#page-14-7) [21,](#page-15-4) [28,](#page-15-5) [30,](#page-15-6) [34,](#page-16-4) [35,](#page-16-2) [38,](#page-16-3) [40,](#page-16-5) [41\]](#page-16-6). In many cases, the central challenge in dataset condensation revolves around the determination of the optimization target, denoted as  $\phi(\mathbf{x}, y)$ , for the condensed image set S. This can be formally expressed as an optimization problem:

$$
S = \arg\min_{S} \sum_{i=1}^{|\mathcal{B}|} \mathcal{D}(\phi(\mathbf{x}_i^t, y_i^t), \phi(\mathbf{x}_i^s, y_i^s)), (\mathbf{x}_i^t, y_i^t)_{i=1}^{|\mathcal{B}|} \sim \mathcal{T}, (\mathbf{x}_i^s, y_i^s)_{i=1}^{|\mathcal{B}|} \sim \mathcal{S}, (1)
$$

where  $\mathcal{D}(\cdot, \cdot)$  represents a matching function, such as Euclidean or cosine distance; commonly, Mean Squared Error (MSE) is used, and  $\beta$  represents a minibatch.  $\phi$  is typically associated with features, gradients, or weights of the model. For model-agnostic DC, we utilize a gradient-based method that involves two heterogeneous models, enabling the condensed image to acquire more generalized knowledge. In the following sections, we introduce Heterogeneous Model Dataset Condensation (HMDC) along with methods to find an appropriate optimization target.

### 3.2 Heterogeneous Model Dataset Condensation

Traditional methods for gradient-based dataset condensation typically utilize the gradient of the model's cross-entropy loss as the optimization target [\[38,](#page-16-3)[40\]](#page-16-5), denoted as  $\phi(\mathbf{x}, y)$ . This is expressed mathematically as:

<span id="page-5-0"></span>
$$
\phi(\mathbf{x}, y) = \nabla \mathcal{L}_{\text{CE}}(f_{\theta}(\mathbf{x}), y). \tag{2}
$$

Here,  $f_{\theta}$  represents a model parameterized by  $\theta$  and  $\mathcal{L}_{CE}$  is the cross-entropy loss. However, these approaches exhibit a model-dependent nature since they focus on training the model's path on the image. In contrast, our HMDC seeks to extract the common features by concurrently considering the training paths of two models,  $f_{\theta_1}$  and  $f_{\theta_2}$ , where two models complement their features each other as illustrated in Figure [2.](#page-4-0) This is expressed mathematically as:

$$
S = \arg \min_{S} \left\{ \sum_{i=1}^{|\mathcal{B}|} \mathcal{D} \left( \nabla \mathcal{L}_{CE} \left( f_{\theta_1} \left( \mathbf{x}_i^t \right), y_i^t \right), \nabla \mathcal{L}_{CE} \left( f_{\theta_1} \left( \mathbf{x}_i^s \right), y_i^s \right) \right) \right\}
$$

$$
+ \sum_{i=1}^{|\mathcal{B}|} \mathcal{D} \left( \nabla \mathcal{L}_{CE} \left( f_{\theta_2} \left( \mathbf{x}_i^t \right), y_i^t \right), \nabla \mathcal{L}_{CE} \left( f_{\theta_2} \left( \mathbf{x}_i^s \right), y_i^s \right) \right) \right\}
$$

$$
= \arg \min_{S} \left\{ \sum_{i=1}^{|\mathcal{B}|} \mathcal{L}^1 + \sum_{i=1}^{|\mathcal{B}|} \mathcal{L}^2 \right\}, (3)
$$

where  $\mathcal{L}^1$  and  $\mathcal{L}^2$  mean the optimization target about  $f_{\theta_1}$  and  $f_{\theta_2}$  respectively, in this case, the mean squared error between gradients generated by synthetic and real images in each model. To enhance performance, additional regularization terms can be introduced as  $\mathcal{L}^3, ..., \mathcal{L}^k$ . This formulation envisions a dataset condensation method that straightforwardly utilizes two models. However, two key challenges arise the gradient magnitude difference and semantic distance between the models. We address these challenges with our novel Gradient Balance Module (GBM) and Mutual Distillation (MD) with Spatial-Semantic Decomposition (SSD), which are described next.

### 3.3 Gradient Balance Module

The optimization of Eq. [3](#page-5-0) faces new challenges due to a significant disparity in the gradient magnitude among  $\nabla \mathcal{L}^1, \nabla \mathcal{L}^2, ..., \nabla \mathcal{L}^k$ . This discrepancy can stem from various factors, such as differences in model structure, depth, and feature dimensionality. Furthermore, if there are additional optimization targets, a hyperparameter search becomes necessary. The varying magnitudes in these gradients could potentially lead to the neglect of one side or hinder convergence.

To address this, we propose the gradient balance module as illustrated in Figure [2,](#page-4-0) which sets up an accumulator to store the size of the gradient from each optimization target. The accumulator  $\mathcal{A} = \{a_1, a_2, ..., a_k\} \in \mathbb{R}^k$  is expressed as:

$$
\mathcal{A} = \left[ \sum_{s=1}^{S} \max \left( \left| \nabla \mathcal{L}^{1}(s) \right| \right), \sum_{s=1}^{S} \max \left( \left| \nabla \mathcal{L}^{2}(s) \right| \right), ..., \sum_{s=1}^{S} \max \left( \left| \nabla \mathcal{L}^{k}(s) \right| \right) \right], \quad (4)
$$

where S represents the total number of optimization steps, an aforementioned k is the number of the optimization targets, max  $(\cdot)$  represents the maximum scalar value in a given tensor, and there are  $k$  optimization targets. During the optimization, the reciprocal of the normalized accumulator is multiplied to ensure a similar gradient amplitude for each element:

$$
S = \arg\min_{S} \left\{ \left[ \mathcal{L}^1, \mathcal{L}^2, ..., \mathcal{L}^k \right] \cdot \min(\mathcal{A}) \mathcal{A}^R \right\},\tag{5}
$$

where min (·) represents the minimum scalar value in a given tensor,  $A^R$  represents element-wise reciprocal of vector  $A$ . In our study, we utilize three distinct losses as optimization targets. To address the imbalance between images in the update and prevent image collapse, we normalize the gradient of each synthetic image. To manage the computational intensity associated with additional gradient computations, we apply an accumulation strategy once in a while, e.g., by sampling once every 10 steps.

## 3.4 Mutual Distillation with Spatial-Semantic Decomposition

Eq. [3](#page-5-0) designed to track the learning path of each model through the gradient matching. However, if the meanings of the two features differ significantly, it impedes the effective learning of synthetic images and hinders their convergence. To mitigate this, we propose a new mutual distillation loss that restricts the semantic divergence between the two models in training processes. On top of that, since two different models vary in depth, dimensionality, and the number of features, effective distillation between them requires careful consideration.

To address this challenge, we introduce a Spatial-Semantic Decomposition (SSD) to preserve the semantics of the features to the maximum extent possible. It decomposes the features of a model into spatial and semantic parts and transforms them accordingly, allowing you to compare the semantics of two models with a linear projection. We designate the features related to classification as semantic features, representing the entire image, and those characterizing each spatial location as spatial features. As spatial features can be represented in an image-like form, features of varying sizes can be aligned using bilinear interpolation, simplifying the alignment process without requiring complex transformations. For easy understanding, here we explain our proposed method using two example models below Vision Transformer (ViT) [\[8\]](#page-14-0) and Convolutional Neural Network (CNN) [\[26\]](#page-15-1). Note that two heterogeneous models can vary, not limited

to them.

ViT Architecture. First, in the ViT architecture, we utilize a class token (CLS) attached to the front of input tokens as semantic features, and the other image tokens as spatial features. Given the feature from every layer of ViT in the dimension of  $\mathcal{F}_{ViT} \in \mathbb{R}^{n \times (w_{ViT}h_{ViT}+1) \times d_{ViT}}$ :

$$
\mathcal{F}_{\text{ViT}}^{\text{semantic}} = \mathcal{F}_{\text{ViT}} \left[ :, 0, : \right] \in \mathbb{R}^{n \times 1 \times d_{\text{ViT}}},
$$
\n
$$
\mathcal{F}_{\text{ViT}}^{\text{partial}} = \mathcal{F}_{\text{ViT}} \left[ :, 1, :, : \right] \in \mathbb{R}^{n \times (w_{\text{ViT}} \times h_{\text{ViT}}) \times d_{\text{ViT}}},
$$
\n(6)

where *n* means the number of layers,  $w_{\text{ViT}}$  and  $h_{\text{ViT}}$  means the number of tokens generated by the ViT when it performs patch embedding to generate image tokens in the horizontal and vertical directions, respectively. The CLS token is attached to the front, and  $d_{\text{ViT}}$  is the dimension of each token. Given the variations in the size of spatial features across layers and models, standardization is achieved through bilinear interpolation. To address differences in feature dimensionality, we apply a learnable affine transformation. The dimension-aligned feature is expressed as:

$$
\mathcal{F}_1 = \left[W_1^1 \left[ \mathcal{F}_{\text{ViT}}^{\text{semantic}}[1]; I_{w \times h} \left( \mathcal{F}_{\text{ViT}}^{\text{spatial}}[1] \right) \right] + \mathbf{b}_1^1; \dots; W_n^1 \left[ \mathcal{F}_{\text{ViT}}^{\text{semantic}}[n]; I_{w \times h} \left( \mathcal{F}_{\text{ViT}}^{\text{spatial}}[n] \right) \right] + \mathbf{b}_n^1 \right], \tag{7}
$$

where *n* is the number of layers in ViT,  $W_1^1, ..., W_n^1 \in \mathbb{R}^{d \times d_{\text{ViT}}}, \mathbf{b}_1^1, ..., \mathbf{b}_n^1 \in \mathbb{R}^d$ , and  $\prod_{w \times h}$  refers to bilinear interpolation into the size of  $w \times h$ . As a result, we get the feature of  $\mathbb{R}^{n \times (wh+1) \times d}$ . w, h, and d mean a target interpolation width, height, and dimension respectively. We use dimension and interpolation size into smaller values between two models, which is found to be empirically better. For computational convenience, it is transposed to  $\mathbb{R}^{(wh+1)\times d\times n}$ .

CNN Architecture. In the case of CNN features, inherently semantic features can be generated through mean pooling of spatial features. This process is mathematically expressed as:

$$
\mathcal{F}_{\text{CNN}} = \{ \mathcal{F}_{\text{CNN}}^1, ..., \mathcal{F}_{\text{CNN}}^m \}, \quad \mathcal{F}_{\text{CNN}}^l \in \mathbb{R}^{d_l \times w_l \times h_l}.
$$
\n
$$
(8)
$$

$$
\mathcal{F}_{\text{CNN}}^{\text{semantic}} = \left\{ \frac{1}{w_1 h_1} \sum_{w=1}^{w_1} \sum_{h=1}^{h_1} \mathcal{F}_{\text{CNN}}^1[:, w, h], ..., \frac{1}{w_m h_m} \sum_{w=1}^{w_m} \sum_{h=1}^{h_m} \mathcal{F}_{\text{CNN}}^m[:, w, h] \right\},
$$

$$
\mathcal{F}_{\text{CNN}}^{\text{spatial}} = \mathcal{F}_{\text{CNN}},
$$
(9)

where m is the number of layers in CNN. As observed in the case of ViT, we standardize dimension and feature size via bilinear interpolation and affine transformation. This process is expressed as:

$$
\mathcal{F}_2 = \left[ W_1^2 \left[ \mathcal{F}_{\text{CNN}}^{\text{semantic}}[1]; \text{I}_{w \times h} \left( \mathcal{F}_{\text{CNN}}^{\text{spatial}}[1] \right) \right] + \mathbf{b}_1^2; \right],
$$

$$
\ldots; W_m^2 \left[ \mathcal{F}_{\text{CNN}}^{\text{semantic}}[m]; \text{I}_{w \times h} \left( \mathcal{F}_{\text{CNN}}^{\text{spatial}}[m] \right) \right] + \mathbf{b}_m^2 \right] \quad (10)
$$

where  $W_1^2 \in \mathbb{R}^{d \times d_1}, ..., W_m^2 \in \mathbb{R}^{d \times d_m}$ , and  $\mathbf{b}_1^2, ..., \mathbf{b}_m^2 \in \mathbb{R}^d$ . Finally we get the feature of  $\mathbb{R}^{m \times (wh+1) \times d}$ . And, it is transposed to  $\mathbb{R}^{(wh+1) \times d \times m}$ .

The aligned dimensionality and the number of features are currently the same, but the number of layers in the two models is still different, *i.e.*,  $\mathcal{F}_1 \in$  $\mathbb{R}^{(wh+1)\times d\times n}$ ,  $\mathcal{F}_2 \in \mathbb{R}^{(wh+1)\times d\times m}$ . We further match the number of layers by using an  $n \times m$  matrix  $M_{\text{layer}} \in \mathbb{R}^{n \times m}$ , where n represents the number of layers in one model and  $m$  denotes the number of layers in the other. Softmax is applied to this matrix for layer selection. We call this matching process as Spatial-Semantic Decomposition (SSD), where the matched features can be expressed as follows:

$$
\begin{cases} \mathcal{F}_1, \mathcal{F}_2 \cdot \text{softmax}(M_{\text{layer}}^{\mathrm{T}}) & m > n \\ \mathcal{F}_1 \cdot \text{softmax}(M_{\text{layer}}), \mathcal{F}_2 & \text{otherwise} \end{cases} \tag{11}
$$

Here, we align the number of layers to the smaller one, which is found to be empirically better. This gives us two  $\mathbb{R}^{(wh+1)\times d\times n}$  of dimension-aligned features when  $m > n$  or  $\mathbb{R}^{(wh+1) \times d \times m}$  dimension of feature otherwise. Both the feature affine matrixes  $(W^1 \text{ and } W^2)$  and the layer matching matrix  $(M_{\text{layer}})$  undergo training to ensure feature alignment between the models at each step. Note again that we give an example using CNN and ViT, but our method can be extended to any model with spatial features.

The Spatial-Semantic Decomposition method enables a comparison of the distinct features of two models. Throughout the training process, we propose Mutual Distillation (MD) to align the meanings of these features, aiming to make them similar knowledge across both models. The training loss for each model  $f_1$  and  $f_2$  is expressed as:

$$
\mathcal{L}_{MD}(\mathbf{x}) = \text{MSE}(\text{SSD} (f_{\theta_1}(\mathbf{x}), f_{\theta_2}(\mathbf{x}))),
$$
  
\n
$$
\mathcal{L}_{f_1} = \mathcal{L}_{CE} (f_{\theta_1}(\mathbf{x}), y) + \mathcal{L}_{MD}(\mathbf{x}),
$$
  
\n
$$
\mathcal{L}_{f_2} = \mathcal{L}_{CE} (f_{\theta_2}(\mathbf{x}), y) + \mathcal{L}_{MD}(\mathbf{x}).
$$
\n(12)

To guide the image in learning intermediate information, the Mutual Distillation loss serves as an additional regularization term for the synthetic image. Through this process, each semantic aspect of the model and the learning path of the synthetic image are guided, achieving a balance between the two models and enhancing generality in the results. Consequently, the number of optimization target k for condensed images is 3 in this case. Total loss function for condensed

images can be defined as a vector inner product as follows:

$$
\mathbf{L} = \left[ \mathcal{L}^1, \mathcal{L}^2, \text{MSE} \left( \nabla \mathcal{L}_{\text{MD}}(\mathbf{x}^t), \nabla \mathcal{L}_{\text{MD}}(\mathbf{x}^s) \right) \right],
$$

$$
\mathcal{L}_{\text{target}} = \mathbf{L} \cdot \min(\mathcal{A}) \mathcal{A}^{\text{R}}
$$

$$
= \frac{\min(\mathcal{A})}{a_1} \mathcal{L}_1 + \frac{\min(\mathcal{A})}{a_2} \mathcal{L}_2 + \frac{\min(\mathcal{A})}{a_3} \text{MSE} \left( \nabla \mathcal{L}_{\text{MD}}(\mathbf{x}^t), \nabla \mathcal{L}_{\text{MD}}(\mathbf{x}^s) \right),
$$
(13)

where  $a_1, a_2$ , and  $a_3$  is current value in the accumulator A part of the Gradient Balance Module. This allows us to learn by considering the semantic distance between two models when training a model and when training an image, and to extract more general knowledge from it.

# 4 Experiments

### 4.1 Implementation Details

In this study, we performed a comprehensive comparative analysis, assessing the effectiveness of our proposed method against cutting-edge gradient matching techniques, IDC [\[21\]](#page-15-4) and DREAM [\[28\]](#page-15-5). To broaden our evaluation scope to distribution matching, we selected CAFE [\[34\]](#page-16-4) and IDM [\[40\]](#page-16-5) as benchmark methodologies.

To ensure a fair and standardized comparison, we employed a consistent augmentation strategy across all methods. This strategy encompassed a sequence of color modifications, cropping, and either Cutout [\[7\]](#page-14-10) or CutMix [\[37\]](#page-16-9), aligning with the recommended practices outlined in IDC [\[40\]](#page-16-5). Also, we use multi-formation which is used in IDC and IDM [\[40\]](#page-16-5) for every method. Nonetheless, it appears that while this robust augmentation technique enhances the performance of the gradient-based method, it adversely affects the distribution matching method, resulting in a decline in performance. In the images generated during the process, a noticeable inclination to produce corrupted images under intense augmentation was observed.

To measure how the condensed image effectively trains the large model, We conducted experiments on CIFAR10 [\[23\]](#page-15-17) on Images Per Class (IPC) 1, 10, 50. Our assessment followed the experimental procedures detailed in each referenced paper. For evaluation, we utilized ConvNet, ViT-tiny [\[8\]](#page-14-0), ResNet18 [\[13\]](#page-15-8), ViTsmall, ResNet50, ResNet101, and ViT-base models, each with specific learning rates (0.01, 0.001, 0.001, 0.001, 0.001, 0.0001, and 0.0001, respectively). The best scores were measured as performance metrics during the training of the models for a consistent duration of 2,000 epochs. Because training a large model on a small dataset makes it difficult to compare performance, every model in Table [1](#page-10-0) except ConvNet was pre-trained on the ImagiNet-1K [\[6\]](#page-14-11) dataset.

In our approach, we adopt ConvNet and ViT-tiny as heterogeneous models. We configure the iteration parameter to 100 and set the loop to iterate 100 times for each iteration. Within each iteration, we perform updates to the image,

<span id="page-10-0"></span>Table 1: Experimental results of dataset condensation methods on CIFAR-10. HMDC is the best or second-best performer in most cases.

|    | IPC Methods     | Models $(\#Parameters)$                 |                   |                                                                                                                        |                    |                                                                     |                                     |                                                                                                                                                          |                   |                                     |                                                    |  |  |  |
|----|-----------------|-----------------------------------------|-------------------|------------------------------------------------------------------------------------------------------------------------|--------------------|---------------------------------------------------------------------|-------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------|-------------------------------------|----------------------------------------------------|--|--|--|
|    |                 | ConvNet<br>(0.3M)                       | ResNet18<br>(11M) | ResNet50<br>(22M)                                                                                                      | ResNet101<br>(43M) | <b>CNN</b><br>Average                                               | ViT-tinv<br>(5.5M)                  | ViT-small<br>(21M)                                                                                                                                       | ViT-base<br>(86M) | <b>ViT</b><br>Average               | Average                                            |  |  |  |
|    | Random          | $22.51 \pm 0.30$                        | $39.39 \pm 3.43$  | $40.43 \pm 8.01$                                                                                                       | $51.17 \pm 1.04$   | $38.38 \pm 3.19$                                                    | $32.49 \pm 3.59$                    | $58.33 \pm 1.42$                                                                                                                                         | $41.05 \pm 5.99$  |                                     | $43.96 \pm 3.66$ + $40.77 \pm 3.39$                |  |  |  |
|    | CAFE            | $27.16 \pm 0.74$                        | $22.65 \pm 4.08$  | $24.83 \pm 1.87$                                                                                                       | $24.09 \pm 1.54$   | $24.68 \pm 2.06$ $16.53 \pm 2.71$                                   |                                     | $19.35 \pm 3.71$                                                                                                                                         | $22.11 \pm 7.67$  |                                     | $19.33 \pm 4.70$ , $22.39 \pm 3.19$                |  |  |  |
|    | <b>IDM</b>      | $37.51 \pm 0.32$                        | $17.50 \pm 2.36$  | $16.07 \pm 1.20$                                                                                                       | $11.47 \pm 0.57$   | $20.64 \pm 1.11 + 12.85 \pm 0.74$                                   |                                     | $11.40 \pm 1.57$                                                                                                                                         | $12.12 \pm 4.27$  |                                     | $12.12 \pm 2.19$ $16.99 \pm 1.57$                  |  |  |  |
|    | $_{\text{IDC}}$ | $32.86 \pm 0.24$                        | $18.71 \pm 1.51$  | $20.53 \pm 2.80$                                                                                                       | $18.69 \pm 2.05$   |                                                                     | $22.70 \pm 1.65$ 18.53 $\pm$ 0.87   | $15.90 \pm 2.79$                                                                                                                                         | $12.46 \pm 4.05$  |                                     | $15.63 \pm 2.57$ + $19.67 \pm 2.04$                |  |  |  |
|    | <b>DREAM</b>    | $38.76 \pm 0.47$                        | $29.75 \pm 2.40$  | $28.99 \pm 2.45$                                                                                                       | $33.24 \pm 8.03$   |                                                                     | $32.68 \pm 3.33$ $19.86 \pm 1.00$   | $21.82 \pm 1.96$                                                                                                                                         | $22.00 \pm 4.50$  | $21.23 \pm 2.49$ , $27.77 \pm 2.97$ |                                                    |  |  |  |
|    | <b>HMDC</b>     |                                         |                   |                                                                                                                        |                    |                                                                     |                                     | $38.74 \pm 0.37$ 52.76 $\pm$ 2.45 57.40 $\pm$ 4.40 51.70 $\pm$ 0.81 50.15 $\pm$ 2.01'39.27 $\pm$ 6.08 56.21 $\pm$ 8.00                                   |                   |                                     | $49.46 \pm 10.9$ $48.31 \pm 8.31$ $49.36 \pm 4.71$ |  |  |  |
|    | Random          | $36.45 \pm 0.12$                        | $56.59 \pm 4.86$  | $69.55 \pm 9.69$                                                                                                       | $82.03 \pm 2.06$   |                                                                     | $61.15 \pm 4.18 + 59.41 \pm 9.20$   | $90.11 \pm 1.25$ $81.26 \pm 5.66$                                                                                                                        |                   | $76.93\pm5.37$ $67.91\pm4.69$       |                                                    |  |  |  |
|    | CAFE            | $34.89 \pm 0.60$                        | $49.85 + 4.85$    | $61.52 \pm 10.8$                                                                                                       | $65.73 \pm 6.24$   | $53.00 \pm 5.63 + 47.94 \pm 5.20$                                   |                                     | $88.70 \pm 2.86$                                                                                                                                         | $75.07 \pm 11.91$ | $70.57 + 6.66 + 60.53 + 6.07$       |                                                    |  |  |  |
| 10 | <b>IDM</b>      | $48.22 \pm 0.34$                        | $31.12 \pm 2.02$  | $30.91 \pm 4.06$                                                                                                       | $25.78 \pm 2.46$   | $34.01 \pm 2.22$ $21.87 \pm 2.01$                                   |                                     | $28.41 \pm 2.88$                                                                                                                                         | $26.74 \pm 4.90$  | $25.67 \pm 3.27$ $30.44 \pm 2.67$   |                                                    |  |  |  |
|    | IDC             | $46.67 \pm 0.15$                        | $56.35 \pm 1.92$  | $67.53 \pm 2.77$                                                                                                       | $60.94 \pm 4.48$   |                                                                     | $57.87 \pm 2.33$ $50.23 \pm 8.38$   | $68.67 \pm 8.50$ $64.32 \pm 12.32$ $61.07 \pm 9.73$ , $59.24 \pm 5.50$                                                                                   |                   |                                     |                                                    |  |  |  |
|    |                 | DREAM $49.23 \pm 0.16$                  | $57.97 \pm 2.77$  | $67.85 \pm 2.54$                                                                                                       | $64.12 \pm 3.95$   |                                                                     | $59.79 \pm 2.36$ $+ 55.40 \pm 6.06$ | $71.77 \pm 8.74$                                                                                                                                         | $68.88 \pm 8.16$  | $65.35 \pm 7.65$ $62.18 \pm 4.63$   |                                                    |  |  |  |
|    | <b>HMDC</b>     | $47.54 \pm 0.73$                        |                   |                                                                                                                        |                    |                                                                     |                                     | $69.75 \pm 0.34$ 77.99 $\pm 1.53$ 82.25 $\pm 0.93$ 69.38 $\pm 0.88$ 73.60 $\pm 4.20$ 89.02 $\pm 1.42$ 85.58 $\pm 1.77$ 82.73 $\pm 2.46$ 75.10 $\pm 1.56$ |                   |                                     |                                                    |  |  |  |
|    | Random          | $45.47 \pm 0.39$                        | $73.55 \pm 1.02$  | $80.96 \pm 6.48$                                                                                                       | $91.33 \pm 0.57$   |                                                                     |                                     | $72.83 \pm 2.11$ $+ 69.84 \pm 6.60$ 96.14 $\pm$ 0.06 93.98 $\pm$ 1.99 86.65 $\pm$ 2.88 $+ 78.75 \pm 2.44$                                                |                   |                                     |                                                    |  |  |  |
| 50 | CAFE            | $44.32 \pm 0.13$                        | $75.06 \pm 1.33$  |                                                                                                                        |                    | $83.45 \pm 3.46$ 91.92 $\pm$ 0.35 73.69 $\pm$ 1.32 67.84 $\pm$ 3.05 |                                     | $95.81 \pm 0.87$                                                                                                                                         | $92.86 \pm 2.92$  | $85.50 \pm 2.28$ + $78.75 \pm 1.73$ |                                                    |  |  |  |
|    | <b>IDM</b>      | $52.82 \pm 0.26$                        | $64.01 \pm 0.47$  | $72.60 \pm 0.49$                                                                                                       | $67.15 \pm 12.6$   |                                                                     | $64.14 \pm 3.45$ $53.55 \pm 4.13$   | $77.52 \pm 15.4$                                                                                                                                         | $74.56 \pm 11.6$  |                                     | $68.54 \pm 10.4$ , $66.03 \pm 6.42$                |  |  |  |
|    | IDC             | $49.75 \pm 0.22$                        | $71.16 \pm 1.26$  | $82.82 \pm 3.23$                                                                                                       | $91.45 \pm 0.50$   |                                                                     | $73.80 \pm 1.30$ $70.37 \pm 1.52$   | $95.44 \pm 0.29$                                                                                                                                         | $92.65 \pm 1.56$  |                                     | $86.16 \pm 1.12$ , $79.09 \pm 1.22$                |  |  |  |
|    |                 | DREAM $52.90 \pm 0.16$ 72.47 $\pm 0.91$ |                   | 79.56±0.73                                                                                                             | $86.74 \pm 2.31$   |                                                                     |                                     | $72.92 \pm 1.02$ 67.27 $\pm 4.36$ 91.41 $\pm 0.78$                                                                                                       | $90.03 \pm 3.71$  |                                     | $82.90\pm2.95$ $77.20\pm1.85$                      |  |  |  |
|    | <b>HMDC</b>     |                                         |                   | $52.40 \pm 0.04$ $75.78 \pm 0.60$ $84.06 \pm 0.84$ $89.08 \pm 0.30$ $75.33 \pm 0.44$ $76.04 \pm 5.24$ $90.91 \pm 0.54$ |                    |                                                                     |                                     |                                                                                                                                                          | $91.45 \pm 0.68$  |                                     | $86.13 \pm 2.15$ $179.96 \pm 1.18$                 |  |  |  |

update the model, and adjust the affine layer along with the layer-matching matrix. The learning rate of each model is 0.001, and the affine layer and layermatching matrix are 0.01. Both use SGD optimizer [\[31\]](#page-16-10) as follows prior works. We set the batch size to 128.

## 4.2 Results

Table [1](#page-10-0) presents a comprehensive performance comparison of the condensed images generated by each technique on CIFAR10. The proposed HMDC demonstrates commendable performance across most models, except ConvNet. Notably, other techniques generally exhibit inferior performance compared to Random across all models, except for ConvNet. The described methods collaboratively yield a condensed image that is impactful without inducing over-condensation, particularly evident in the case of ConvNet. This trend becomes more pronounced as IPC decreases. Simultaneously, our method competes favorably with other methods on ConvNet. Noteworthy is that HMDC performs the best on IPC 1 for all models. This suggests that the proposed HMDC effectively captures general features and incorporates them into a limited synthetic image. Unlike previous methods, HMDC shows promise for training large models from images compressed from relatively small models, aligning with the goal of dataset condensation. Despite utilizing two models, HMDC requires only 100 iterations and consumes less time than other models, typically using 1,200 to 20,000 iterations.

### 4.3 Ablation Studies

We conducted an ablation study to evaluate the impact of the proposed features on performance. Specifically, we examined performance by removing Mutual Distillation by Spatial-Sementic Decomposition and the Gradient Balance

<span id="page-11-0"></span>Table 2: Table illustrating the outcomes of the ablation study. GBM means Gradient Balance Module and MD means Mutual Distillation by Spatial-Semantic Decomposition. The results demonstrate the individual contributions of the presented factors to performance enhancements, revealing a synergistic effect when employing them simultaneously.

|               | $CIFAR-10$ (IPC 10) |          |          |           |              |          |           |          |                                                                                                                                                                                                               |         |  |  |
|---------------|---------------------|----------|----------|-----------|--------------|----------|-----------|----------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|--|--|
| <b>GBM MD</b> | ConvNet             | ResNet18 | ResNet50 | ResNet101 | $\text{CNN}$ | ViT-tiny | ViT-small | ViT-base | ViT                                                                                                                                                                                                           | Average |  |  |
|               | (0.3M)              | (11M)    | (22M)    | (43M)     | Average      | (5.5M)   | (21M)     | (86M)    | Average                                                                                                                                                                                                       |         |  |  |
|               |                     |          |          |           |              |          |           |          | DREAM 49.23±0.16 57.97±2.77 67.85±2.54 64.12±3.95 59.79±2.36 55.40±6.06 71.77±8.74 68.88±8.16 65.35±7.65 62.18±4.63                                                                                           |         |  |  |
|               | $46.42 \pm 0.37$    |          |          |           |              |          |           |          | $72.11 \pm 0.06$ $76.46 \pm 1.09$ $76.85 \pm 0.88$ $67.96 \pm 0.60$ $74.09 \pm 4.36$ $90.01 \pm 1.00$ $82.71 \pm 0.76$ $82.27 \pm 2.04$ $74.09 \pm 1.22$                                                      |         |  |  |
|               |                     |          |          |           |              |          |           |          | $47.43 \pm 0.46 \quad 71.62 \pm 1.25 \quad 76.92 \pm 0.36 \quad 77.70 \pm 1.77 \quad 68.41 \pm 0.96 \quad 73.67 \pm 2.38 \quad 86.62 \pm 3.11 \quad 78.53 \pm 6.77 \quad 79.61 \pm 4.08 \quad 73.21 \pm 2.30$ |         |  |  |
|               |                     |          |          |           |              |          |           |          | $47.30\pm0.41$ $72.01\pm1.46$ $78.05\pm2.23$ $79.07\pm1.69$ $69.11\pm1.45$ $70.96\pm4.02$ $86.25\pm5.25$ $80.16\pm5.36$ $79.12\pm4.88$ $73.40\pm2.92$                                                         |         |  |  |
|               |                     |          |          |           |              |          |           |          | $47.54\pm82.73$ 69.75 $\pm75.10$ 77.99 $\pm0.73$ 82.25 $\pm0.34$ 69.38 $\pm1.53$ 73.60 $\pm0.93$ 89.02 $\pm0.88$ 85.58 $\pm4.20$ 82.73 $\pm1.42$ 75.10 $\pm1.77$                                              |         |  |  |

<span id="page-11-1"></span>Table 3: The performance variations across model combinations, depicting results for pairs of identical models  $(CNN + CNN)$  and pairs involving larger models than those employed in the experiment.

| ConvNet ResNet18 ViT-tiny ViT-small | CIFAR-10 (IPC 10) |                   |                   |                    |                |                    |                    |                   |                |            |
|-------------------------------------|-------------------|-------------------|-------------------|--------------------|----------------|--------------------|--------------------|-------------------|----------------|------------|
|                                     | ConvNet<br>(0.3M) | ResNet18<br>(11M) | ResNet50<br>(22M) | ResNet101<br>(43M) | CNN<br>Average | ViT-tiny<br>(5.5M) | ViT-small<br>(21M) | ViT-base<br>(86M) | ViT<br>Average | Average    |
| Random                              | 36.45±0.12        | 56.59±4.86        | 69.55±9.69        | 82.03±2.06         | 61.15±4.18     | 59.41±9.20         | 90.11±1.25         | 81.26±5.66        | 76.93±5.37     | 67.91±4.69 |
| <br><br><br>                        | 47.54±0.73        | 69.75±0.34        | 77.99±1.53        | 82.25±0.93         | 69.38±0.88     | 73.60±4.20         | 89.02±1.42         | 85.58±1.77        | 82.73±2.46     | 75.10±1.56 |
| <br><br><br>                        | 45.85±0.71        | 44.09±4.33        | 47.32±1.06        | 32.91±2.31         | 42.54±2.10     | 33.79±1.90         | 34.93±5.55         | 34.58±14.63       | 34.43±7.36     | 39.07±4.35 |
| <br><br><br>                        | 38.10±0.40        | 70.67±0.93        | 79.85±1.59        | 83.82±1.26         | 68.11±1.05     | 75.80±2.54         | 92.42±0.46         | 88.60±4.28        | 85.61±2.43     | 75.61±1.64 |

<span id="page-11-2"></span>Table 4: Comparison table between simply using ViT-Tiny and using the presented method.

|        |                          | CIFAR-10 (IPC 10)                 |                                   |                  |                                                                                                       |            |          |           |          |                                                                                                                                                                                         |         |
|--------|--------------------------|-----------------------------------|-----------------------------------|------------------|-------------------------------------------------------------------------------------------------------|------------|----------|-----------|----------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|
|        | Methods ConvNet ViT-tiny | ConvNet                           | ResNet18                          | ResNet50         | ResNet101                                                                                             | <b>CNN</b> | ViT-tiny | ViT-small | ViT-base | ViT                                                                                                                                                                                     | Average |
|        |                          | (0.3M)                            | (11M)                             | (22M)            | (43M)                                                                                                 | Average    | (5.5M)   | (21M)     | (86M)    | Average                                                                                                                                                                                 |         |
| Random |                          |                                   | $36.45 \pm 0.12$ $56.59 \pm 4.86$ |                  | $69.55 \pm 9.69$ 82.66 $\pm$ 1.82 61.31 $\pm$ 4.12 59.36 $\pm$ 9.19 90.11 $\pm$ 1.25 81.26 $\pm$ 5.66 |            |          |           |          | $76.91 \pm 5.37$ , 68.00 $\pm 4.66$                                                                                                                                                     |         |
| Dream  |                          | $49.23 \pm 0.16$ 57.97 $\pm 2.77$ |                                   |                  |                                                                                                       |            |          |           |          | $67.85 \pm 2.54$ $64.12 \pm 3.95$ $59.79 \pm 2.36$ $55.40 \pm 6.06$ $71.77 \pm 8.74$ $68.88 \pm 8.16$ $65.35 \pm 7.65$ $62.18 \pm 4.63$                                                 |         |
| Dream  |                          | $25.77 \pm 0.57$                  | $49.28 \pm 2.70$                  | $56.65 \pm 4.37$ | $62.26 \pm 4.08$                                                                                      |            |          |           |          | $48.49\pm2.93$ $+60.26\pm3.78$ $62.68\pm10.5$ $51.77\pm22.3$ $58.24\pm12.2$ $+52.67\pm6.91$                                                                                             |         |
| HMDC   |                          |                                   |                                   |                  |                                                                                                       |            |          |           |          | $47.54\pm0.73$ 69.75 $\pm$ 0.34 77.99 $\pm$ 1.53 82.25 $\pm$ 0.93 69.38 $\pm$ 0.88 $\cdot$ 73.60 $\pm$ 4.20 89.02 $\pm$ 1.42 85.58 $\pm$ 1.77 82.73 $\pm$ 2.46 $\cdot$ 75.10 $\pm$ 1.56 |         |

Module. Table [2](#page-11-0) presents the experimental result of ablation. The experimental results demonstrate that each element contributes to performance, and when both methods are employed, they exhibit synergy.

In the Ablation Study, the results demonstrate that the isolated application of the Gradient Balance Module (GBM) and Mutual Distillation (MD) can be beneficial in certain scenarios, though they tend to favor either Convolutional Neural Network (CNN) model. This leads to good performance on certain models and poor performance on others. The isolated use of the GBM tends to favor smaller models because the semantic distance between the two models fails to encapsulate the complex patterns necessary for larger models. Conversely, when only MD is employed, it extracts more generalized features, performing well with larger models. However, the results are biased due to the gradient difference in the condensed model. By combining these methods, a balanced gradient is achieved for each model, significantly reducing the semantic distance between them. This synergy not only enhances the overall performance but also ensures a more model-agnostic improvement.

<span id="page-12-0"></span>Image /page/12/Picture/1 description: The image displays two grids of generated images, labeled (a) and (b). Grid (a) is titled "Condensed image generated by DREAM [28]", and grid (b) is titled "Condensed image generated by Heterogeneous". Both grids contain multiple rows and columns of smaller images, each depicting various objects and animals such as airplanes, cars, birds, cats, dogs, deer, horses, boats, and trucks. The images within the grids appear to be generated by a machine learning model, likely a generative adversarial network (GAN), as suggested by the labels.

Fig. 3: Comparision of condensed images between DREAM [\[28\]](#page-15-5) and HMDC(Ours)

Condensed image generated by DREAM [\[28\]](#page-15-5) method, with CIFAR10, 10 images per class.

(b) Condensed image generated by Heterogeneous Model Dataset Condensation (HMDC) method, with CIFAR10, 10 images per class.

### 4.4 Qualitative Results

Figure [3a](#page-12-0) presents a condensed image generated using the DREAM [\[28\]](#page-15-5) method. In contrast, Figure [3b](#page-12-0) depicts a condensed image created using the Heterogeneous Model Dataset Condensation approach. Overall, we observe that objects exhibit the same characteristics, such as increased contrast and sharpened edges. While the visual disparities are minimal, it is noteworthy that the image generated by our method exhibits fewer artifacts and distortions compared to DREAM. These distortions appear to be caused by over-condensation, improving performance on certain models but degrading performance on others.

## 4.5 Analysis

Table [3](#page-11-1) illustrates the performance outcomes of Heterogeneous Model Dataset Condensation across various model combinations. Significantly, there is an evident decrease in overall performance when the CNN is used uniformly, contrary to the intended objective of the method. In the third row, we adjusted the learning rate of the affine layer and layer-matching matrix to 0.001. This suggests that with minimal modification, larger models can be accommodated using HMDC. We can also see that the use of larger models shows an overall benefit.

Table [4](#page-11-2) presents a comparative analysis of our experimental results with the state-of-the-art method, DREAM, changing the model into ViT-Tiny. The results reveal an overall enhancement in performance for ViT-Tiny attributed to an increased understanding of the model. However, overall performance drops, especially in the ConvNet and it is notably worse compared to using random

<span id="page-13-0"></span>Fig. 4: A logarithmic plot depicting the gradient magnitude evolution of a synthetic image throughout the training process. L1 and L2 refer to the optimization targets in Eq. [3.](#page-5-0) L3 is MSE  $(\nabla \mathcal{L}_{MD}(\mathbf{x}^t), \nabla \mathcal{L}_{MD})$ .

Image /page/13/Figure/2 description: The image contains two line graphs side-by-side. Both graphs have a logarithmic y-axis ranging from 0.001 to 100, with major grid lines at 0.001, 0.01, 0.1, 1, 10, and 100. The x-axis for both graphs is not explicitly labeled with numbers but has three lines labeled L1, L2, and L3, indicated by a legend at the bottom. The left graph shows three lines: a blue line (L1) fluctuating between approximately 0.004 and 0.008, an orange line (L2) peaking at around 40 and then dropping to around 8, and a gray line (L3) fluctuating between approximately 0.03 and 0.05. The right graph also shows three lines: a blue line (L1) fluctuating between approximately 8 and 15, an orange line (L2) fluctuating between approximately 8 and 40, and a gray line (L3) fluctuating between approximately 8 and 20. The overall trend in the right graph is that all three lines are generally in the range of 8 to 20, with some peaks and dips.

images. This trend is further emphasized by the large gap between HMDC in combination with ConvNet. Conversely, in ConvNet, overall performance is diminished due to the model's limited capacity, with a pronounced decline in the ViT series. This observation underscores the model dependency of the condensed image and the condensed image is over-condensed.

Figure [4](#page-13-0) depicts the maximum gradient magnitude of the synthetic image after the learning step. On the left, before integrating the Gradient Balance Module, there is a substantial disparity in the gradient magnitudes, leading to the neglect of other losses. Following the inclusion of the Gradient Balance Module, the gradient magnitudes from each loss function become uniform. This ensures balanced dataset condensation irrespective of the model, underscoring the essential role of the Gradient Balance Module in Heterogeneous Model Dataset Condensation.

# 5 Conclusion

We identified a model dependency issue in existing dataset condensation methods and proposed a remedy by balancing different heterogeneous models. However, employing two distinct models for dataset condensation introduces two challenges: bias arising from differences in gradient magnitude between the models and semantic distance resulting from the models converging to their respective optimal points. To address the gradient magnitude disparity, we introduced the Gradient Balance Module. To tackle the semantic distance issue, we proposed Mutual Distillation by Spatial-Semantic Decomposition. The combination of these components effectively alleviated the model dependency problem.

However, there are still some limitations. Firstly, it is impossible to surpass the model's capacity limits, as evidenced by the performance gap for ViT-Base due to a fifteenfold difference in parameter number. Furthermore, due to the continued use of floating points in condensed images, they occupy four times the capacity of the equivalent number of real images. From this standpoint, there is a need for quantization-aware condensation that enables the synthetic image to be treated like a real image.

# Acknowledgement

This work was supported by MSIT (Ministry of Science and ICT), Korea, under the ITRC (Information Technology Research Center) support program (IITP-2024-RS-2023-00258649) supervised by the IITP (Institute for Information & Communications Technology Planning & Evaluation), and in part by the IITP grant funded by the Korea Government (MSIT) (Artificial Intelligence Innovation Hub) under Grant 2021-0-02068, and by the IITP grant funded by the Korea government (MSIT) (No.RS-2022-00155911, Artificial Intelligence Convergence Innovation Human Resources Development (Kyung Hee University)).

## References

- <span id="page-14-3"></span>1. Agarwal, P.K., Har-Peled, S., Varadarajan, K.R.: Approximating extent measures of points. J. ACM 51(4), 606–635 (jul 2004). [https://doi.org/10.1145/1008731.](https://doi.org/10.1145/1008731.1008736) [1008736](https://doi.org/10.1145/1008731.1008736), <https://doi.org/10.1145/1008731.1008736>
- <span id="page-14-6"></span>2. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Dataset distillation by matching training trajectories. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 4750–4759 (2022)
- <span id="page-14-7"></span>3. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Generalizing dataset distillation via deep generative prior. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3739–3748 (2023)
- <span id="page-14-9"></span>4. Chen, Z., Badrinarayanan, V., Lee, C.Y., Rabinovich, A.: Gradnorm: Gradient normalization for adaptive loss balancing in deep multitask networks. In: International conference on machine learning. pp. 794–803. PMLR (2018)
- <span id="page-14-2"></span>5. Cubuk, E.D., Zoph, B., Mane, D., Vasudevan, V., Le, Q.V.: Autoaugment: Learning augmentation policies from data. arXiv e-prints p. arXiv preprint arXiv:1805.09501 (2018)
- <span id="page-14-11"></span>6. Deng, J., Dong, W., Socher, R., Li, L.J., Li, K., Fei-Fei, L.: Imagenet: A largescale hierarchical image database. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 248–255. Ieee (2009)
- <span id="page-14-10"></span>7. DeVries, T., Taylor, G.W.: Improved regularization of convolutional neural networks with cutout. arXiv preprint arXiv:1708.04552 (2017)
- <span id="page-14-0"></span>8. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth 16x16 words: Transformers for image recognition at scale. In: International Conference on Learning Representations (2020)
- <span id="page-14-4"></span>9. Feldman, D., Schmidt, M., Sohler, C.: Turning big data into tiny data: Constantsize coresets for k-means, pca, and projective clustering. SIAM Journal on Computing 49(3), 601–657 (2020)
- <span id="page-14-8"></span>10. Furlanello, T., Lipton, Z., Tschannen, M., Itti, L., Anandkumar, A.: Born again neural networks. In: International Conference on Machine Learning. pp. 1607–1616. PMLR (2018)
- <span id="page-14-1"></span>11. Goodfellow, I., Pouget-Abadie, J., Mirza, M., Xu, B., Warde-Farley, D., Ozair, S., Courville, A., Bengio, Y.: Generative adversarial nets. Advances in neural information processing systems 27 (2014)
- <span id="page-14-5"></span>12. Har-Peled, S., Mazumdar, S.: On coresets for k-means and k-median clustering. In: Proceedings of the Thirty-Sixth Annual ACM Symposium on Theory of

Computing. p. 291–300. STOC '04, Association for Computing Machinery, New York, NY, USA (2004). <https://doi.org/10.1145/1007352.1007400>, [https:](https://doi.org/10.1145/1007352.1007400) [//doi.org/10.1145/1007352.1007400](https://doi.org/10.1145/1007352.1007400)

- <span id="page-15-8"></span>13. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 770–778 (2016)
- <span id="page-15-11"></span>14. Heo, B., Kim, J., Yun, S., Park, H., Kwak, N., Choi, J.Y.: A comprehensive overhaul of feature distillation. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 1921–1930 (2019)
- <span id="page-15-12"></span>15. Heo, B., Lee, M., Yun, S., Choi, J.Y.: Knowledge transfer via distillation of activation boundaries formed by hidden neurons. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 33, pp. 3779–3787 (2019)
- <span id="page-15-9"></span>16. Hinton, G., Vinyals, O., Dean, J.: Distilling the knowledge in a neural network. arXiv preprint arXiv:1503.02531 (2015)
- <span id="page-15-2"></span>17. Ho, J., Jain, A., Abbeel, P.: Denoising diffusion probabilistic models. Advances in neural information processing systems 33, 6840–6851 (2020)
- <span id="page-15-13"></span>18. Huang, Z., Wang, N.: Like what you like: Knowledge distill via neuron selectivity transfer. arXiv preprint arXiv:1707.01219 (2017)
- <span id="page-15-3"></span>19. Karystinos, G.N., Pados, D.A.: On overfitting, generalization, and randomly expanded training sets. IEEE Transactions on Neural Networks 11(5), 1050–1057 (2000)
- <span id="page-15-15"></span>20. Kendall, A., Gal, Y., Cipolla, R.: Multi-task learning using uncertainty to weigh losses for scene geometry and semantics. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 7482–7491 (2018)
- <span id="page-15-4"></span>21. Kim, J.H., Kim, J., Oh, S.J., Yun, S., Song, H., Jeong, J., Ha, J.W., Song, H.O.: Dataset condensation via efficient synthetic-data parameterization. In: International Conference on Machine Learning. pp. 11102–11118. PMLR (2022)
- <span id="page-15-14"></span>22. Kim, J., Park, S., Kwak, N.: Paraphrasing complex network: Network compression via factor transfer. Advances in neural information processing systems 31 (2018)
- <span id="page-15-17"></span>23. Krizhevsky, A., Hinton, G., et al.: Learning multiple layers of features from tiny images (2009)
- <span id="page-15-7"></span>24. Krizhevsky, A., Sutskever, I., Hinton, G.E.: Imagenet classification with deep convolutional neural networks. Advances in neural information processing systems 25 (2012)
- <span id="page-15-0"></span>25. LeCun, Y., Bengio, Y., Hinton, G.: Deep learning. nature 521(7553), 436–444 (2015)
- <span id="page-15-1"></span>26. LeCun, Y., Bottou, L., Bengio, Y., Haffner, P.: Gradient-based learning applied to document recognition. Proceedings of the IEEE 86(11), 2278-2324 (1998)
- <span id="page-15-16"></span>27. Li, W.H., Liu, X., Bilen, H.: Universal representation learning from multiple domains for few-shot classification. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 9526–9535 (2021)
- <span id="page-15-5"></span>28. Liu, Y., Gu, J., Wang, K., Zhu, Z., Jiang, W., You, Y.: Dream: Efficient dataset distillation by representative matching. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 17314–17324 (October 2023)
- <span id="page-15-10"></span>29. Mirzadeh, S.I., Farajtabar, M., Li, A., Levine, N., Matsukawa, A., Ghasemzadeh, H.: Improved knowledge distillation via teacher assistant. In: Proceedings of the AAAI conference on artificial intelligence. vol. 34, pp. 5191–5198 (2020)
- <span id="page-15-6"></span>30. Nguyen, T., Novak, R., Xiao, L., Lee, J.: Dataset distillation with infinitely wide convolutional networks. Advances in Neural Information Processing Systems 34, 5186–5198 (2021)

- <span id="page-16-10"></span>31. Robbins, H., Monro, S.: A stochastic approximation method. The annals of mathematical statistics pp. 400–407 (1951)
- <span id="page-16-0"></span>32. Rumelhart, D.E., Hinton, G.E., Williams, R.J., et al.: Learning internal representations by error propagation (1985)
- <span id="page-16-1"></span>33. Vaswani, A., Shazeer, N., Parmar, N., Uszkoreit, J., Jones, L., Gomez, A.N., Kaiser, Ł., Polosukhin, I.: Attention is all you need. Advances in neural information processing systems 30 (2017)
- <span id="page-16-4"></span>34. Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X., You, Y.: Cafe: Learning to condense dataset by aligning features. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 12196–12205 (2022)
- <span id="page-16-2"></span>35. Wang, T., Zhu, J.Y., Torralba, A., Efros, A.A.: Dataset distillation. arXiv preprint arXiv:1811.10959 (2018)
- <span id="page-16-7"></span>36. Yang, C., Xie, L., Su, C., Yuille, A.L.: Snapshot distillation: Teacher-student optimization in one generation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 2859–2868 (2019)
- <span id="page-16-9"></span>37. Yun, S., Han, D., Oh, S.J., Chun, S., Choe, J., Yoo, Y.: Cutmix: Regularization strategy to train strong classifiers with localizable features. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 6023–6032 (2019)
- <span id="page-16-3"></span>38. Zhao, B., Mopuri, K.R., Bilen, H.: Dataset condensation with gradient matching. In: International Conference on Learning Representations (2020)
- <span id="page-16-8"></span>39. Zhao, B., Cui, Q., Song, R., Qiu, Y., Liang, J.: Decoupled knowledge distillation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 11953–11962 (2022)
- <span id="page-16-5"></span>40. Zhao, G., Li, G., Qin, Y., Yu, Y.: Improved distribution matching for dataset condensation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 7856–7865 (2023)
- <span id="page-16-6"></span>41. Zhou, Y., Nezhadarya, E., Ba, J.: Dataset distillation using neural feature regression. Advances in Neural Information Processing Systems 35, 9813–9827 (2022)
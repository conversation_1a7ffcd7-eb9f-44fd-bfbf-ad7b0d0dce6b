# DATA DISTILLATION CAN BE LIKE VODKA: DISTILL-ING MORE TIMES FOR BETTER QUALITY

Xuxi Chen\*<sup>1</sup>, <PERSON>\*<sup>2</sup>, <PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><PERSON><sup>2</sup>

<sup>1</sup>University of Texas at Austin <sup>2</sup>University of California, Los Angeles

{xxchen,atlaswang}@utexas.edu, {yuyang,baharan}@cs.ucla.edu

## ABSTRACT

Dataset distillation aims to minimize the time and memory needed for training deep networks on large datasets, by creating a small set of synthetic images that has a similar generalization performance to that of the full dataset. However, current dataset distillation techniques fall short, showing a notable performance gap when compared to training on the original data. In this work, we are the first to argue that using just one synthetic subset for distillation will not yield optimal generalization performance. This is because the training dynamics of deep networks drastically change during the training. Hence, multiple synthetic subsets are required to capture the training dynamics at different phases of training. To address this issue, we propose Progressive Dataset Distillation (PDD). PDD synthesizes multiple small sets of synthetic images, each conditioned on the previous sets, and trains the model on the cumulative union of these subsets without requiring additional training time. Our extensive experiments show that PDD can effectively improve the performance of existing dataset distillation methods by up to 4.3%. In addition, our method for the first time enable generating considerably larger synthetic datasets.

## 1 INTRODUCTION

Dataset distillation aims to generate a very small number of synthetic examples from a large dataset, which can provide a similar generalization performance to that of training on the full dataset [\(Wang](#page-11-0) [et al., 2018;](#page-11-0) [Loo et al., 2022;](#page-10-0) [Nguyen et al., 2021a](#page-10-1)[;b;](#page-10-2) [Zhou et al., 2022\)](#page-11-1). If this can be achieved, it can significantly reduce the costs and memory requirement of training deep network on large datasets. Therefore, dataset distillation has gained a lot of recent interest and found various applications, ranging from continual learning, neural architecture search to privacypreserving ML [\(Zhao et al., 2021;](#page-11-2) [Dong et al., 2022\)](#page-10-3).

<span id="page-0-0"></span>Image /page/0/Figure/9 description: This is a line graph showing the relationship between distilled training epochs on full data and test accuracy (%). The x-axis represents the number of distilled training epochs on full data, ranging from 20 to 90. The y-axis represents the test accuracy in percentage, ranging from 66% to 76%. There are two lines plotted: 'Our Multi-stage (PDD-MTT)' represented by blue stars connected by a line, and 'Single-stage (MTT)' represented by a black pentagon. The 'Our Multi-stage (PDD-MTT)' line shows an increasing trend in test accuracy as the number of epochs increases. For example, at 30 epochs, the accuracy is around 68%; at 50 epochs, it's around 72%; at 60 epochs, it's around 73%; at 70 epochs, it's around 74%; and at 90 epochs, it's around 76%. The 'Single-stage (MTT)' data point at 10 epochs shows an accuracy of approximately 65.5%.

Figure 1: Our proposed multi-stage dataset distillation framework PDD improves the state-of-the-art algorithms by distilling longer training dynamics on full data.

Existing dataset distillation methods generate a set of synthetic examples that match the gradient [\(Zhao et al., 2021;](#page-11-2) [Zhao & Bilen, 2021b\)](#page-11-3), Neural Tangent Kernel (NTK) [\(Loo et al., 2022;](#page-10-0) [Nguyen](#page-10-1) [et al., 2021a](#page-10-1)[;b;](#page-10-2) [Zhou et al., 2022\)](#page-11-1), or weights [\(Kim et al., 2022\)](#page-10-4) of a number of randomly initialized models being trained on the original [\(Zhao et al., 2021\)](#page-11-2) or synthetic data [\(Zhao & Bilen, 2021b\)](#page-11-3). However, as matching the entire training dynamics is intractable, existing methods only match the dynamics of *early training iterations*, as short as the first four epochs [\(Zhao et al., 2021\)](#page-11-2). As training dynamics of deep networks drastically change during the training, a synthetic subset generated based on early training dynamics cannot represent the dynamics of later training phases. Hence, existing dataset distillation methods suffer from a substantial performance gap to that of training on the original data [\(Zhao et al., 2021;](#page-11-2) [Kim et al., 2022\)](#page-10-4).

Recent results on optimization and generalization of neural networks revealed that gradient methods have an inductive bias towards learning simple functions, in particular early in training [\(Kalimeris](#page-10-5) [et al., 2019;](#page-10-5) [Hu et al., 2020;](#page-10-6) [Hermann & Lampinen, 2020;](#page-10-7) [Neyshabur et al., 2014;](#page-10-8) [Shah et al., 2020\)](#page-11-4).

<sup>\*</sup>Equal Contribution.

That is, models trained with (stochastic) gradient methods learn nearly linear functions in the initial training iterations [\(Kalimeris et al., 2019;](#page-10-5) [Hu et al., 2020\)](#page-10-6). As iterations progress, SGD learns functions of increasing complexity [\(Kalimeris et al., 2019\)](#page-10-5). This implies that synthetic examples generated based on early training dynamics can only train low-complexity neural networks that perform well on easy examples that are separable by low-complexity models. This limitation is further supported by recent studies [\(Pooladzandi et al., 2022;](#page-10-9) [Yang et al., 2023\)](#page-11-5), which observed that deep models benefit the most from learning examples of increasing difficulty levels at various training stages and one subset of the training data is not enough to support the entire training.

Building on this observation, to bridge the gap to training on the full data, it is crucial to synthesize examples that can capture the dynamics of later training phases. This is, however, very challenging. First, synthesizing examples that match the training dynamics of many randomly initialized networks over longer training intervals has a very high computational cost. Moreover, capturing the complex training dynamics over longer intervals requires synthesizing more images, which makes it prohibitively expensive. Finally, even if a larger subset can be generated to match the dynamics of a longer training interval, it is not enough to bridge the gap to training on the full data.

In this work, we address the above challenges by proposing a Progressive Dataset Distillation (PDD) pipeline. We are the first to employ a multi-stage idea that focuses on different phases of training. The key idea behind our method is to generate multiple synthetic subsets that can capture the training dynamics in different phases. To do so, we synthesize examples that capture training dynamics of the full data in a given training interval. Then, we train the model on the synthesized examples and generate another set of synthetic examples that, together with the previous synthetic sets, capture training dynamics of the full data in the consecutive training interval. Importantly, our progressive distillation approach effectively trains neural networks with superior generalization performance without increasing the training time on the synthetic examples. Figure [1](#page-0-0) confirms that by distilling dynamics of later training stages on CIFAR-10, PDD effectively improves the performance when training on the distilled data.

Our extensive experiments confirm that our multi-stage distillation approach outperforms existing methods by up to 5% on ConvNet and 5% for cross-architecture generalization to ResNet-10 and ResNet-18. Remarkably, PDD is the first method to enable generating larger synthetic datasets. In doing so, it considerably bridges the gap to training on the full data by achieving 90% of the full accuracy with only 5% of the full data size on CIFAR-10 and 8% of full data size on CIFAR-100 [\(Krizhevsky et al., 2009\)](#page-10-10) and provides state-of-the-art accuracy on Tiny-ImageNet [\(Le & Yang, 2015\)](#page-10-11). We also conduct studies showing that: 1) our multi-stage synthesis framework achieves consistent improvement if new intervals are introduced; 2) our framework generates synthetic samples with strong generalization ability across various architectures; 3) the distillation process can be performed on progressively challenging subsets of the full data at each stage, resulting in minimal performance degradation.

## <span id="page-1-0"></span>2 RELATED WORKS

Dataset Distillation (DD) [\(Wang et al., 2018\)](#page-11-0) aims to generate a synthetic subset from a large training data that can achieve a similar generalization performance to that of training on the full dataset, when trained on. To achieve this, DD adopted an optimization process comprising two nested loops. The inner loop involves training a model using the synthesized data until it reaches convergence, while the outer loop aims to optimize the synthetic data such that the trained model generalizes well on the original dataset. More recent studies [\(Loo et al., 2022;](#page-10-0) [Nguyen et al., 2021a](#page-10-1)[;b;](#page-10-2) [Zhou et al., 2022\)](#page-11-1) leverage the same framework but use kernel methods, such as Neural Tangent Kernel (NTK), to approximate the inner optimization in a closed form. While kernel-based algorithms achieved higher accuracy than DD [\(Wang et al., 2018\)](#page-11-0) on networks that satisfy the infinite-width assumption, they do not work well in practice as the constant NTK assumption does not generally hold.

Another set of methods relies on gradient matching. In particular, DC [\(Zhao et al., 2021\)](#page-11-2) minimizes the distance between the gradients of the synthetic and original data on the network being trained on the synthetic data. DSA [\(Zhao & Bilen, 2021b\)](#page-11-3) improves upon  $DC$  by applying differentiable siamese augmentations to both the original and synthetic data while matching their training gradients. Incorporating differentiable data augmentation has been adopted by almost all subsequent studies. Later on, IDC [\(Kim et al., 2022\)](#page-10-4) proposed a multi-formulation framework to generate more augmented examples from the same set of synthetic data, to boost the performance with the same storage budget.

The synthetic data is generated by minimizing the distance between the gradients of the synthetic and original data on the network being trained on the full data.

Besides matching the gradients, other methods involve matching the training trajectories of the network parameters [\(Cazenavette et al., 2022\)](#page-10-12) or the data distribution [\(Wang et al., 2022;](#page-11-6) [Zhao](#page-11-7) [& Bilen, 2023\)](#page-11-7). MTT [\(Cazenavette et al., 2022\)](#page-10-12) pre-computes and stores training trajectories of expert networks trained on the original data, and then minimizes the distance between the parameters of the network trained on the synthetic data and the expert networks. CAFE [\(Wang et al., 2022\)](#page-11-6) matches the features between the synthetic and real data in all intermediate layers. To avoid the expensive bi-level optimization, DM [\(Zhao & Bilen, 2021a\)](#page-11-8) minimizes the distance between feature embeddings of the synthetic and real data based on randomly initialized networks. More recently, HuBa [\(Liu et al., 2022\)](#page-10-13) proposed to distill a dataset into two components, Bases and Hallucination to increase the representation capability of distilled datasets. IT-GAN [\(Zhao & Bilen, 2022\)](#page-11-9) inverted the training samples into latent spaces and further fine-tuned towards a distillation objective, and GLaD [\(Cazenavette et al., 2023\)](#page-10-14) used generative adversarial networks as a prior to help the crossarchitecture generalization.

Existing works generate a set of synthetic examples that match the dynamics of neural networks during early-training stage or at multiple random initializations. In contrast, we show that progressively generating multiple synthetic subsets to match the training dynamics in different stages of training yields superior performance.

#### 3 PROBLEM FORMULATION AND PRELIMINARY

Given a large dataset  $\mathcal{T}=\{(\pmb{x}_i,y_i)\}$  which consists of  $|\mathcal{T}|$  samples from  $C$  classes, dataset distillation aims to learn a synthetic set  $\mathcal{S} = \{(\bm{s}_i, y_i)\}$  with  $|\mathcal{S}|$  synthetic samples so that the deep neural networks can be trained on S and achieve a comparable generalization performance to those trained on  $\mathcal T$ . Formally,

$$
\mathbb{E}_{\boldsymbol{x}\sim P(\mathcal{D})}[\mathcal{L}(\phi_{\boldsymbol{\theta}}\tau(\boldsymbol{x}),y)] \simeq \mathbb{E}_{x\sim P(\mathcal{D})}[\mathcal{L}(\phi_{\boldsymbol{\theta}}s(\boldsymbol{x}),y)],\tag{1}
$$

where  $P(\mathcal{D})$  is the real data distribution,  $\phi_{\theta}(\mathcal{T})$  and  $\phi_{\theta}(\mathcal{S})$  are models trained on  $\mathcal{T}$  and  $\mathcal{S}$  respectively.  $\mathcal{L}(\cdot, \cdot)$  is the loss function, e.g., cross-entropy loss.

State-of-the-art dataset distillation methods condense the real dataset into a small synthetic set by matching the gradient of full data along the synthetic or real trajectory. This can be expressed as follows:

<span id="page-2-0"></span>
$$
\underset{\mathcal{S}}{\arg\min} \mathbb{E}_{\boldsymbol{\theta}_0 \sim P_{\boldsymbol{\theta}_0}}[\sum_{t=0}^{T-1} D(\nabla_{\boldsymbol{\theta}} \mathcal{L}^{\mathcal{S}}(\boldsymbol{\theta}_t), \nabla_{\boldsymbol{\theta}} \mathcal{L}^{\mathcal{T}}(\boldsymbol{\theta}_t))],
$$
\n(2)

where  $\theta_t$  is the model parameters, and D computes distance between the gradients. DC [\(Zhao et al.,](#page-11-2) [2021\)](#page-11-2), and DSA [\(Zhao & Bilen, 2021b\)](#page-11-3) update  $\theta_t$  by minimizing the loss  $\mathcal{L}^S(\theta_t)$  on the synthetic data. On the other hand, IDC [\(Kim et al., 2022\)](#page-10-4) showed that updating  $\theta_t$  by minimizing the loss  $\mathcal{L}^{\mathcal{T}}(\pmb{\theta}_t)$  on the full data yields superior performance. Matching the gradient of the augmented version of the training and synthetic examples further improves the performance [\(Zhao et al., 2021;](#page-11-2) [Kim](#page-10-4) [et al., 2022\)](#page-10-4).

Alternatively, MTT [\(Cazenavette et al., 2022\)](#page-10-12) trains two models on synthetic and real data and matches weight trajectories  $\theta_{t+N}$  of length N when training the model on synthetic data S with weight trajectories  $\hat{\pmb{\theta}}_{t+M}^*$  of length  $M \gg N$  when training the model on real data  $\mathcal{T}$ :

<span id="page-2-1"></span>
$$
\arg\min_{\mathcal{S}} \frac{\|\boldsymbol{\theta}_{t+N} - \boldsymbol{\theta}_{t+M}^*\|_2^2}{\|\boldsymbol{\theta}_t - \boldsymbol{\theta}_{t+M}^*\|_2^2}.
$$
\n(3)

Existing dataset distillation methods synthesize examples based on the gradients or weights of the models during the initial training epochs [\(Cazenavette et al., 2022;](#page-10-12) [Kim et al., 2022\)](#page-10-4), or match outputs of multiple randomly initialized models [\(Zhao & Bilen, 2021a\)](#page-11-8). The most successful methods, synthesize examples that capture the training dynamics of models trained on *full data* [\(Kim et al.,](#page-10-4) [2022;](#page-10-4) [Zhao et al., 2021\)](#page-11-2). However, they only capture the *early* training dynamics. For example, IDC [\(Zhao et al., 2021\)](#page-11-2) and MTT [\(Kim et al., 2022\)](#page-10-4) synthesize examples by matching gradients and weights of the first 4 and 15 epochs of a 200 training pipeline respectively, when distilling CIFAR-10 and CIFAR-100. This is because matching weights or gradients over longer intervals becomes computationally difficult and does not yield high-quality synthetic data. This introduces a performance gap to that of training on the original data.

<span id="page-3-0"></span>Image /page/3/Figure/1 description: The image depicts a multi-stage process for model training, likely in machine learning. It shows three stages labeled 'Stage 1', 'Stage 2', and 'Stage 3', with 'Transition' indicated between stages. Each stage involves a 'distill' operation followed by a 'train' operation. There are two boxes on the left, one labeled 'Optimizable' and the other 'Fixed'. In Stage 1, a fixed block and an optimizable block are shown, with a distillation process leading to a block labeled 'S1', which is then trained to produce a parameter 'θ1'. Stage 2 shows a similar process, starting with 'S1' and an optimizable block, distilling to 'S2', and training to produce 'θ2'. Stage 3 continues this pattern with 'S2' and an optimizable block, distilling to 'S3'. The process culminates in a 'PDD Output' section, which lists stacked blocks labeled 'SP', '...', 'S3', 'S2', and 'S1', suggesting a sequence of outputs or learned representations.

Figure 2: An illustration of the proposed Progressive Dataset Distillation (PDD) framework. It consists of multiple distillation stages and transitions in between. In each distillation stage, we distill a new set of images conditioned on images synthesized in the previous stages. In transitions, we train models on all synthesized images so far, as the starting weights for the next distillation stage to capture longer training dynamics. Our framework can be applied to any dataset distillation algorithm.

#### 4 PROGRESSIVE DATASET DISTILLATION (PDD)

Next, we introduce our Progressive Dataset Distillation (PDD) framework to generate synthetic images that match the training dynamics of different stages of training.

#### 4.1 DISTILLING MULTIPLE TRAINING STAGES

To capture the learning dynamics of different stages of training, our key idea, shown in Figure [2,](#page-3-0) is to generate a *sequence* of small synthetic datasets  $S_1, S_2, \cdots, S_p$ , such that each synthetic dataset  $S_i$  captures dynamics of training on the full data in a different stage of training. Then at test time, when the model is trained on the synthetic images, we can train the model on different subsets to mimic different stages of training on the full data.

However, naively dividing the full training pipeline into  $P$  intervals and generating a subset based on the training dynamics of each interval does not yield a satisfactory performance, due to the following reasons. First, generating different synthetic subsets independently results in capturing redundant information in the subsets and does not improve the performance. Second, since the synthetic sub-

<span id="page-3-1"></span>

| <b>Algorithm 1 Progressive Dataset Distillation (PDD)</b>                                                                                            |
|------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>Input:</b> A dataset distillation algorithm $A$ , full                                                                                            |
| training set $\mathcal T$                                                                                                                            |
| <b>Output:</b> Model trained on a series of synthetic                                                                                                |
| datasets: $S_1, S_2, \ldots, S_N$                                                                                                                    |
| <b>Generating synthetic subsets: PDD</b>                                                                                                             |
| $\mathcal{S}_0 \leftarrow \emptyset$                                                                                                                 |
| Initialize $\theta_0$ randomly                                                                                                                       |
| for $i = 1, 2, , P$ do                                                                                                                               |
| $\mathcal{S}_i = \mathcal{A}(\boldsymbol{\theta}_i, \mathcal{T}   \cup_{i=1}^{i-1} \mathcal{S}_i)$                                                   |
| $\boldsymbol{\theta}_i = \arg \min_{\boldsymbol{\theta}} \mathcal{L}(\boldsymbol{\theta}, \cup_{i=1}^{i-1} S_i, \boldsymbol{\theta}_{i-1})$          |
| end for                                                                                                                                              |
| <b>Evaluation: Training on the PDD subsets</b>                                                                                                       |
| Initialize $\theta_0$ randomly                                                                                                                       |
| for $i = 1, 2, \cdots, P$ do                                                                                                                         |
| $\boldsymbol{\theta}_i = \arg\min_{\boldsymbol{\theta}} \mathcal{L}(\boldsymbol{\theta}, \cup_{j=1}^{i-1} \mathcal{S}_j, \boldsymbol{\theta}_{i-1})$ |
| end for                                                                                                                                              |

sets are small, at test time when the model is trained on the synthetic images, minimizing the loss on subset  $S_{i+1}$  results in forgetting the previously learned information from subsets  $S_1, \dots S_i$ . Finally, even if forgetting can be prevented, transitioning from training on  $S_i$  to training on  $S_{i+1}$  at test time changes the training loss and interrupts the optimization pipeline. This does not allow the model to learn well from multiple synthetic subsets.

To address the above issues, we synthesize each subset  $S_i$  based on the dynamics of training on the full data at stage *i*, *conditioned* on the previous subsets  $S_1, S_2, \cdots, S_i$ . That is, we generate  $S_i$  such that  $S_1 \cup S_2 \cup \cdots \cup S_i$  captures the training dynamics at stage *i*. Note that we only synthesize  $S_i$ at interval  $T_i$  while keeping  $S_1, S_2, \cdots, S_{i-1}$  fixed. This prevents capturing redundant information in subset  $S_i$  that are already captured by previous subsets  $S_1, S_2, \cdots, S_{i-1}$ . Next, to address the discontinuity in training on multiple subsets, we synthesize every subset  $S_i$  based on the training dynamics of full data starting from parameters  $\pmb\theta_i$ , where training on  $\mathcal S_1\cup\mathcal S_2\cup\dots\cup\mathcal S_{i-1}$  is finished. This allows smooth transitioning between different subsets when training on the synthetic data. Finally, at test time when the model is trained on the synthetic subsets, to prevent forgetting the information learned from the previous subsets, we first train the model on  $S_1$ , then  $S_1 \cup S_2$ , and keep training on the union of the previous subsets in addition to the new one  $S_1 \cup S_2 \cup \cdots \cup S_i$ .

We summarize our pipeline in Algorithm [1.](#page-3-1) Formally, for  $i = 1, \dots, P$ , we generate a synthetic subset  $S_i$  as follows:

$$
S_i = \mathcal{A}(\boldsymbol{\theta}_i, \mathcal{T} | \cup_{j=1}^{i-1} S_j) \quad \text{s.t.} \quad \boldsymbol{\theta}_i = \argmin_{\boldsymbol{\theta}} \mathcal{L}(\boldsymbol{\theta}, \cup_{j=1}^{i-1} S_j, \boldsymbol{\theta}_{i-1}), \quad (4)
$$

where  $\mathcal{L}(\theta, \mathcal{S}, \theta_{i-1})$  is the loss of the model trained on data S starting from  $\theta_{i-1}$ . A can be any dataset distillation method, such as DC [\(Zhao et al., 2021\)](#page-11-2), DSA [\(Zhao & Bilen, 2021b\)](#page-11-3), IDC [\(Zhao](#page-11-2) [et al., 2021\)](#page-11-2), and MTT [\(Kim et al., 2022\)](#page-10-4), described in Eq. equation [2](#page-2-0) and equation [3.](#page-2-1)

Distillation and training costs. Note that conditioning the distillation on previous subsets does not increase the cost of synthesizing a new subset, as we generate the same number of synthetic images at every interval. On the other hand, at test time, we train on similar number of images in total with multiple stages. This is because instead of training on  $k = |\mathcal{S}|$  synthetic examples during the entire training, PDD with m intervals first trains the model on  $k/m$  synthetic images. Then, it trains the model on  $2k/m$  synthetic images and keeps increasing the number of training examples until it trains on k examples at the final interval.

#### 4.2 DISCARDING EASIER-TO-LEARN EXAMPLES AT LATER STAGES

As training progress, PDD generates synthetic examples that enable the network to learn higher complexity functions. This implies that at later stages, we can safely discard the examples that are learned early in training with lower-complexity functions from the distillation pipeline. To calculate the learning difficulty of examples, we use the forgetting score [\(Toneva et al., 2019\)](#page-11-10) defined as the number of times the prediction of every example changes from correct to wrong during the training. Examples with higher forgetting scores are learned later during the training with higher complexity functions. On the other hand, examples that have a very low forgetting score are those that can be classified by lower complexity functions, early in training. At every distillation stage, we drop examples with low forgetting scores and focus the distillation on examples with increasing levels of difficulty, measure by forgetting score. This improves the efficiency of PDD without harming the performance, as we will confirm experimentally.

Next, we will show experimentally that PDD effectively trains higher-quality neural networks with superior generalization performance without increasing the training time on the synthetic examples.

### 5 EXPERIMENTS

In this section, we assess the classification performance of neural networks trained on synthetic images generated by our framework. In addition to evaluating on the architecture used for distillation, we also investigate the transferability of the distilled images to larger models with different architectures. We further show with ablation studies that PDD trains models with increasing classification accuracy when we increase the number of intervals, and confirm the importance of conditioning and transitions.

#### 5.1 EXPERIMENTAL SETTINGS

Datasets. We conduct our experiments on three standard datasets: CIFAR-10, CIFAR-100 [\(Krizhevsky et al., 2009\)](#page-10-10) and Tiny-ImageNet [\(Le & Yang, 2015\)](#page-10-11). CIFAR-10 and CIFAR-100 consist of 50, 000 training images, with 10 and 100 classes, respectively. The image size for CIFAR is  $32 \times 32$ . Tiny-ImageNet contains 100,000 training images from 200 categories, with the image size of  $64 \times 64$ .

Baselines. We consider both data selection and distillation algorithms as baselines, including random selection, Herding [\(Welling, 2009\)](#page-11-11), K-center [\(Farahani & Hekmatfar, 2009\)](#page-10-15), and Forgetting [\(Toneva](#page-11-10) [et al., 2019\)](#page-11-10) for selection and DC [\(Zhao et al., 2021\)](#page-11-2), DSA [\(Zhao & Bilen, 2021b\)](#page-11-3), DM [\(Zhao & Bilen,](#page-11-8) [2021a\)](#page-11-8), CAFE [\(Wang et al., 2022\)](#page-11-6), IDC [\(Kim et al., 2022\)](#page-10-4), and MTT [\(Cazenavette et al., 2022\)](#page-10-12) for distillation. Herding [\(Welling, 2009\)](#page-11-11) greedily selects samples to approximate the mean of the entire dataset; Forgetting score [\(Toneva et al., 2019\)](#page-11-10) keeps track of how many times a training sample is learned and forgotten during the training and keeps examples with the highest forgetting score;

|                  | Dataset                                                                                   | CIFAR-10                                                                                                                                                       |                                                                                                                                                                | CIFAR-100                                                                                                                                                      |                                                                                                                                              | Tiny-ImageNet                                                                      |                                                    |
|------------------|-------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------|----------------------------------------------------|
|                  | <b>IPC</b>                                                                                | 10                                                                                                                                                             | 50                                                                                                                                                             | 10                                                                                                                                                             | 50                                                                                                                                           | 10                                                                                 | 50                                                 |
| Selection        | Random<br>Herding<br>K-Center<br>Forgetting                                               | $26.0 \pm 1.2$<br>$31.6 \pm 0.7$<br>$14.7 \pm 0.9$<br>$23.3 \pm 1.0$                                                                                           | $43.4 \pm 1.0$<br>$40.4 \pm 0.6$<br>$27.0 \pm 1.4$<br>$23.3 \pm 1.1$                                                                                           | $14.6 \pm 0.5$<br>$17.3 \pm 0.3$<br>$7.1 \pm 0.2$<br>$15.1 \pm 0.2$                                                                                            | $30.0 \pm 0.4$<br>$33.7 \pm 0.5$<br>$30.5 \pm 0.3$                                                                                           | $5.0 \pm 0.2$<br>$6.3 \pm 0.2$<br>$5.1 \pm 0.2$                                    | $15.0 \pm 0.4$<br>$16.7 \pm 0.3$<br>$15.0 \pm 0.3$ |
| Distillation     | DC.<br><b>DSA</b><br>CAFE<br>CAFE + DSA<br><b>DM</b><br>MTT<br>PDD+MTT<br>TDC.<br>PDD+IDC | $44.9 \pm 0.5$<br>$52.1 \pm 0.5$<br>$46.3 \pm 0.6$<br>$50.9 \pm 0.5$<br>$48.9 \pm 0.6$<br>$65.3 \pm 0.7$<br>$66.9 \pm 0.4$<br>$67.5 \pm 0.5$<br>$67.9 \pm 0.2$ | $53.9 \pm 0.5$<br>$60.6 \pm 0.5$<br>$55.5 \pm 0.6$<br>$62.3 \pm 0.4$<br>$63.0 \pm 0.4$<br>$71.9 \pm 0.2$<br>$74.2 \pm 0.5$<br>$74.5 \pm 0.1$<br>$76.5 \pm 0.4$ | $25.2 \pm 0.3$<br>$32.3 \pm 0.3$<br>$27.8 \pm 0.3$<br>$31.5 \pm 0.2$<br>$29.7 \pm 0.3$<br>$39.6 \pm 0.3$<br>$43.1 \pm 0.7$<br>$45.1 \pm 0.3$<br>$45.8 \pm 0.5$ | $42.8 \pm 0.4$<br>$37.9 \pm 0.3$<br>$42.9 \pm 0.2$<br>$43.6 \pm 0.4$<br>$47.7 \pm 0.2$<br>$52.0 \pm 0.5$<br>$52.5 \pm 0.4$<br>$53.1 \pm 0.4$ | $12.9 \pm 0.4$<br>$23.2 \pm 0.3$<br>$27.3 \pm 0.5$<br>$\qquad \qquad \blacksquare$ | $24.1 \pm 0.3$<br>$28.0 \pm 0.3$<br>$29.2\pm0.6$   |
| <b>Full Data</b> |                                                                                           | 88.1                                                                                                                                                           |                                                                                                                                                                |                                                                                                                                                                | 56.2                                                                                                                                         |                                                                                    | 37.6                                               |

<span id="page-5-0"></span>Table 1: Test accuracy of ConvNets on CIFAR-10/100 and Tiny-ImageNet, trained on synthetic samples generated by various models with different numbers of images per class (IPC). Our algorithm (PDD) improves upon baseline methods through its multi-stage distillation pipeline, narrowing the performance gap relative to training on the full dataset. PDD results are reported for 5 stages.

K-Center [\(Farahani & Hekmatfar, 2009\)](#page-10-15) selects the samples to minimize the maximum distance between a data point and its center. Distillation baselines are introduced in Section [2.](#page-1-0)

Architectures. Our experimental settings follow that of [Cazenavette et al.](#page-10-12) [\(2022\)](#page-10-12): we employ a ConvNet for distillation, with three convolutional blocks for CIFAR-10 and CIFAR-100 and four convolutional blocks for Tiny-ImageNet, each containing a 128-kernel convolutional layer, an instance normalization layer [\(Ulyanov et al., 2016\)](#page-11-12), a ReLU activation function [\(Nair & Hinton, 2010\)](#page-10-16) and an average pooling layer. We include ResNet-18 and ResNet-10 [\(He et al., 2016\)](#page-10-17) to assess the transferability of the synthetic images to other architectures.

Distillation Settings. We adopt two representative baseline methods on which we apply our framework: IDC and MTT, which are widely used state-of-the-art dataset distillation methods. During the matching process, we adopt the optimal hyper-parameter reported in the original paper of each dataset distillation method in each stage of PDD without further tuning. We report the number of images PDD distills at each stage and also report the number of synthetic sets  $P$  in our results to enable a comparison between PDD and the baselines. Note that the number of synthetic sets has a monotonic effect on the models' testing accuracies.

Evaluation. Once the synthetic subsets have been constructed for each dataset, they are used to train randomly initialized networks from scratch, followed by evaluation on their corresponding testing sets. For PDD, we sequentially train models after each interval on all synthetic samples that have already been generated up to the current interval. For each experiment, we report the mean and the standard deviation of the testing accuracy of 5 trained networks. To train networks from scratch at evaluation time, we use the SGD optimizer with a momentum of 0.9 and a weight decay of  $5 \times 10^{-4}$ . For IDC, the learning rate is set to be 0.01. For MTT, the learning rate is simultaneously optimized with the synthetic images. During the evaluation time, we follow the augmentation strategies of each method to train networks from scratch.

### 5.2 EVALUATING DISTILLED DATASETS

Setup. We demonstrate the effectiveness of the proposed multi-stage distillation by applying PDD to MTT and IDC to distill CIFAR-10/100 and Tiny-ImageNet.

Table [1](#page-5-0) compares PDD with state-of-the-art baselines for different values of Images Per Class (IPC) distilled in 5 stages. We specify baselines' IPC and PDD's IPC to be 10 and 50 for all the benchmarks. For Tiny-ImageNet, we only conduct experiments with MTT as IDC's distillation time is prohibitively expensive in this higher resolution. Based on the default settings, single-stage IDC distills 4 epochs of training on the real images; MTT distills 15 epochs for CIFAR-10, 20 for CIFAR-100, and 40 for Tiny-ImageNet.

<span id="page-6-0"></span>Image /page/6/Figure/1 description: This image contains three line graphs comparing the accuracy of different models based on the number of stages. Graph (a) shows results for CIFAR-10, with two lines representing 'per-stage IPC' values of 10 and 50. The accuracy for IPC 10 ranges from approximately 65% to 72%, and for IPC 50, it ranges from approximately 71% to 81%. Graph (b) shows results for CIFAR-100, with lines for IPC 10 and 20. Accuracy for IPC 10 ranges from about 40% to 46%, and for IPC 20, it ranges from about 40% to 52%. Graph (c) shows results for Tiny-ImageNet, with lines for IPC 10 and 2. Accuracy for IPC 10 ranges from about 19% to 27%, and for IPC 2, it ranges from about 7% to 24%. All graphs have 'Number of stages' on the x-axis and 'Accuracy' on the y-axis, with a horizontal red line indicating a performance benchmark.

Figure 3: ConvNets' test accuracy on CIFAR-10, CIFAR-100 and Tiny-ImageNet after training on samples distilled by PDD + MTT with multiple stages with larger per-stage IPCs. Left: performance on CIFAR-10; Middle: performance on CIFAR-100; Right: performance on Tiny-ImageNet. The red lines indicate the performance of training on full data on CIFAR-10, CIFAR-100 an Tiny-ImageNet, respectively.

Comparing to Single Stage Distillation. We see that PDD consistently improves the performance across all data selection and distillation baselines with the same IPCs, especially when we distill longer training dynamics (i.e., 15 epochs with MTT) on the real images in each stage. Specifically, PDD + MTT outperforms MTT by significant margins of  $1.6\%/2.3\%$  on CIFAR-10 IPC-10/50,  $3.5\%/4.3\%$ on CIFAR-100, and 4.1%/1.2% on Tiny-ImageNet. After applying PDD to IDC, we witness an substantial improvement on performance across different datasets: 0.4%/2.0% on CIFAR-10 IPC-10/50, respectively, and 0.7%/0.6% on CIFAR-100 IPC-10/50, respectively.

Scaling up synthetic datasets: towards bridging the gap to training on the full data. In Figure [3a](#page-6-0) and [3b,](#page-6-0) we extend our experiments with MTT by maintaining a constant per-stage IPC while progressively increasing the number of stages. This setting enables scaling of synthesis process to generate larger total IPC, because the images generated in earlier stages are employed in subsequent stages. We conduct these experiments on CIFAR-10 and CIFAR-100, respectively, and set the perstage IPC to  $10/50$  for CIFAR-10,  $10/20$  for CIFAR-100, and  $2/10$  for Tiny-ImageNet. Remarkably, PDD considerably bridges the gap to training on the full dataset by achieving 90% of the full accuracy with only 5% of the full data size on CIFAR-10 (which means that IPC = 250) and 10% of full data size on CIFAR-100 (which means that IPC = 50). Notably, for CIFAR-100, we utilize  $20\%$ of the complete dataset, resulting in an IPC value of 100, yet achieve a comparable performance. On Tiny-ImageNet, applying PDD with MTT could also reach 80% of the performance obtained by training on the full data after distilling 50 images per class.

#### 5.3 CROSS-ARCHITECTURE GENERALIZATION

Next, we evaluate the generalization performance of PDD on architectures that are different from the one we used to distill CIFAR-10. Following the settings of cross-architecture experiments in the original papers, we use batch normalization layers when evaluating on IDC, and use instance normalization layers for MTT. We follow the same evaluation pipeline for each baseline method to acquire and present the test accuracy in Table [2.](#page-6-1)

We can see that the images distilled by using PDD improves other architectures' performance  $(1.7\%/1.5\%$  on ResNet-10 and  $1.8\%/0.9\%$  on

<span id="page-6-1"></span>Table 2: Performance on other architectures of networks with synthetic datasets generated on ConvNets by baselines versus PDD + baselines.

| (Total) IPC    | Method              | ResNet-10           | ResNet-18           |
|----------------|---------------------|---------------------|---------------------|
|                |                     | 10                  | IDC                 |
| <b>PDD+IDC</b> | <b>63.2 \pm 1.4</b> |                     | <b>63.9 \pm 0.6</b> |
| MTT            | 46.4 \pm 0.4        |                     | 45.2 \pm 0.3        |
| <b>PDD+MTT</b> | <b>47.1 \pm 0.3</b> |                     | <b>46.0 \pm 0.4</b> |
| 50             | IDC                 | 70.7 \pm 0.6        | 69.8 \pm 0.3        |
|                | <b>PDD+IDC</b>      | <b>72.4 \pm 0.4</b> | <b>71.6 \pm 0.3</b> |
|                | MTT                 | 63.1 \pm 0.4        | 62.6 \pm 0.4        |
|                | <b>PDD+MTT</b>      | <b>64.6 \pm 0.9</b> | <b>63.5 \pm 0.5</b> |

ResNet-18) when using IPC = 50, and show considerable improvement  $(0.2\%/0.7\%$  on ResNet-10 and  $0.3\%/0.8\%$  on ResNet-18) compared to using the single-stage MTT and IDC when the total IPC is 10. These results indicate our distilled images from multiple stages are robust to changes in network architectures.

#### 5.4 ABLATION STUDIES

**Effect of Progressive Training.** When training a model on the  $P$  synthetic subsets, PDD progressively trains on the union of the first i synthetic sets, for  $i = 1, \dots, P$ . To demonstrate the effectiveness of this progressive way of training, we explore multiple choices of training pipelines with the PDD generated synthetic sets: (1) *Union*: we train on the union of the synthetic sets generated in all P stages, i.e.,  $\cup_{j=1}^P S_j$ ; (2) *Sequential*: we train on different  $S_i$  in the order they are generated; (3) *Progressive*: we progressively train on union of the first i synthetic sets, i.e.,  $\bigcup_{j=1}^{i} S_j$ .

Table [3](#page-7-0) compares the above training methods when evaluating the synthetic sets PDD distilled for CIFAR-10 with a fixed per-stage IPC  $= 10$  and different numbers of stages  $P$ . For all the base distillation algorithms, namely MTT and IDC, progressive training is consistently better than union and outperforms sequential training with a large margin in particular for larger  $P$ . This confirms the necessity of progressive training to prevent forgetting the previously learned information. Note that PDD + MTT performs poorly with the union pipeline because MTT learns the learning rate for each set of synthetic images, so a single learning rate is not suitable for training on the union.

Importance of transitions and conditioning. There are two key designs in PDD that are essential for the success of multi-stage dataset distillation: (1) *transition* between stages by generating a new synthetic subset based on the training trajectory starting from the point where training on the union of the previous synthetic subsets is finished; and (2) *conditioning* on synthetic images distilled in earlier stages when generating a new synthetic set for the current training stage. In Table [4,](#page-7-1) we show both components are crucial by comparing the test

<span id="page-7-0"></span>Table 3: Effect of training on PDD distilled subsets. Testing accuracy on CIFAR-10 after being trained on 10 IPC per stage distilled by PDD + different base methods. In 'Training' column, U, S, P correspond to training on  $\cup_{j=1}^P S_j$ , or  $S_i$ , or  $\cup_{j=1}^i S_j$ , at stage i, respectively.

| <span style="font-weight:bold;">P</span> | Training | <b>Test Accuracy</b>  |                       |
|------------------------------------------|----------|-----------------------|-----------------------|
|                                          |          | <b>MTT + PDD</b>      | <b>IDC + PDD</b>      |
| 1                                        | -        | 65.3 $\pm$ 0.7        | 67.5 $\pm$ 0.5        |
| 2                                        | U        | 60.4 $\pm$ 0.6        | 71.1 $\pm$ 0.2        |
|                                          | S        | 64.1 $\pm$ 1.0        | 68.5 $\pm$ 0.1        |
|                                          | P        | <b>68.7</b> $\pm$ 0.8 | <b>71.4</b> $\pm$ 0.2 |
| 3                                        | U        | 65.4 $\pm$ 0.7        | <b>74.2</b> $\pm$ 0.4 |
|                                          | S        | 67.4 $\pm$ 1.1        | 68.2 $\pm$ 0.7        |
|                                          | P        | <b>71.5</b> $\pm$ 0.4 | 74.0 $\pm$ 0.3        |
| 4                                        | U        | 63.2 $\pm$ 0.7        | 75.4 $\pm$ 0.3        |
|                                          | S        | 66.0 $\pm$ 0.9        | 69.9 $\pm$ 0.5        |
|                                          | P        | <b>73.1</b> $\pm$ 0.6 | 75.4 $\pm$ 0.1        |
| 5                                        | U        | 65.9 $\pm$ 0.4        | 76.2 $\pm$ 0.6        |
|                                          | S        | 67.4 $\pm$ 0.8        | 69.9 $\pm$ 0.5        |
|                                          | P        | <b>74.2</b> $\pm$ 0.5 | <b>76.5</b> $\pm$ 0.2 |

accuracy of ConvNet after being trained on the PDD distilled datasets with both or without one of the two designs. For PDD + MTT and both variants, we fix the number of images per class to distill in each stage to be 10. We observe a decreased performance of PDD when it distills images for each training stage independent of the previous stages, and the difference is more significant when we distill longer training intervals with more stages.

<span id="page-7-1"></span>Table 4: ConvNet's performance on CIFAR-10 with differ-Table 5: Models' testing accuracy on ent synthesis modes (*i.e.*, w/o transition and w/o conditioning) using PDD + MTT.

CIFAR-10. PDD with different numbers of stages  $(P)$  and per-stage IPC.

| P | w/o transition | w/o conditioning | PDD            | P  | per-stage IPC | Accuracy       |
|---|----------------|------------------|----------------|----|---------------|----------------|
| 1 | $65.3 \pm 0.7$ | $65.3 \pm 0.7$   | $65.3 \pm 0.7$ | 1  | 10            | $65.3 \pm 0.7$ |
| 2 | $66.0 \pm 0.6$ | $67.9 \pm 0.5$   | $68.7 \pm 0.8$ | 2  | 5             | $65.5 \pm 0.9$ |
| 3 | $66.3 \pm 0.4$ | $69.8 \pm 0.9$   | $71.5 \pm 0.4$ | 5  | 2             | $66.9 \pm 0.4$ |
| 4 | $65.6 \pm 0.5$ | $71.4 \pm 0.5$   | $73.1 \pm 0.6$ | 10 | 1             | $64.4 \pm 0.6$ |
| 5 | $63.6 \pm 0.7$ | $71.9 \pm 0.7$   | $74.2 \pm 0.5$ |    |               |                |

Distilling more training stages vs more images per stage. Given a fixed total number of images per class, we can distill longer training dynamics by having more stages, or choose to distill more images in each stage to capture the dynamics better. To understand which of the above two strategies leads to better performance, we study four different combinations of the number of stages and per-stage IPC, and record the models' test accuracy in Table [5.](#page-7-1) We observe that establishing more stages can generally improve the results, as long as per-stage IPC is not too small (IPC  $= 1$  per stage leads

to degraded performance). In particular, with 10 as a fixed number of images in total, best result corresponds to  $P = 5$  and per-stage IPC = 2.

Discarding easy-to-learn examples at later stages. Next, we confirm that easier-to-learn examples can be dropped from the distillation pipeline in later intervals. To do so, we use the forgetting score [\(Toneva et al., 2019\)](#page-11-10) defined as the number of times the prediction of every example changes from being correctly classified to incorrectly classified during the training. Examples with higher forgetting scores are more difficult to learn for the network and are learned later during the training [\(Toneva et al., 2019\)](#page-11-10).

We separate training examples into multiple partitions based on their forgetting scores, with an increment of 3. More specifically, at the  $i$ -th stage only the examples with a number of forgetting events between  $3 \times (i - 1)$  and  $3 \times i$ . Subsequently, we apply PDD to distill the corresponding partition of data examples at each stage, starting from the partition that contains examples with the lowest forgetting scores and progressing to those with the highest scores. Table [6](#page-8-0) shows that when PDD explicitly distills examples with increasing learning difficulty at different stages, models trained on the distilled images have comparable test performance as when the distillation is based on the full training set at all stages. This observation not only confirms that PDD naturally creates a curriculum with its synthetic sets but also confirms the

<span id="page-8-0"></span>Table 6: ConvNet's performance on CIFAR-10 trained on synthetic set with 10 images per class using MTT with PDD by distilling from easy to difficult samples. In  $i$ -th stage we select samples with forgetting score within  $[3(i - 1), 3i)$ . We report the portion of training samples used in each setting.

| P | Data Used     | Testing Accuracy |
|---|---------------|------------------|
| 1 | 36.5%<br>100% | 65.9%<br>65.3%   |
| 3 | 55.8%<br>100% | 71.6%<br>71.5%   |
| 5 | 66.4%<br>100% | 73.7%<br>74.2%   |

possibility of reducing the distillation cost of PDD as the training examples used in each stage can be significantly reduced.

#### 5.5 CONTINUAL LEARNING

In this section, we adopt a class incremental setting [\(Zhao et al., 2021;](#page-11-2) [Zhao & Bilen, 2021b\)](#page-11-3) to show that PDD can improve the performance in the application of continual learning. We apply PDD on MTT to distill CIFAR-100 across 5 phases, in each of which we can only access 20

Table 7: Continual learning performance using distilled samples generated by different methods on CIFAR-100.

<span id="page-8-1"></span>

| Methods        | Stage 1     | Stage 2     | Stage 3     | Stage 4     | Stage 5     |
|----------------|-------------|-------------|-------------|-------------|-------------|
| DSA            | 52.5        | 45.7        | 40.4        | 35.0        | 31.1        |
| Herding        | 48.4        | 43.3        | 39.6        | 36.4        | 33.1        |
| MTT            | 55.7        | 52.1        | 48.3        | 43.0        | 41.2        |
| <b>PDD+MTT</b> | <b>61.2</b> | <b>56.6</b> | <b>51.5</b> | <b>48.3</b> | <b>45.1</b> |

classes with 20 images distilled in total per class. During the evaluation, a model will be trained sequentially on samples available at each stage. Table [7](#page-8-1) shows the performance using different methods, which demonstrates that PDD + MTT consistently outperforms MTT at each stage and showcases PDD's ability to improve baselines' performance in the application of continual learning.

#### 5.6 SYNTHESIZED SAMPLES VISUALIZATION

In Figure [4,](#page-8-2) we provide examples of synthetic samples on CIFAR-10 using  $PDD + MTT$  at different stages. We distill CIFAR-10 in 5 stages with a per-stage IPC of 10. From the images we can observe that the synthetic samples at later stages show diversified patterns, demonstrating lower saturation in color and more abstract textures. This evolution of visual patterns indicates a shift in the focus of the distillation process and thus provides an empirical support to our multistage design. Figure [A5](#page-12-0) shows all the samples from Stage 1 to 5 where the transition of distilled patterns on all classes are clearly presented.

<span id="page-8-2"></span>Image /page/8/Figure/14 description: The image displays a grid of abstract images, organized into three rows labeled "Stage 1", "Stage 3", and "Stage 5". Each row contains multiple columns of images. The images themselves are colorful and appear to be visualizations of neural network activations or generated images, with blurry shapes and textures. The top row, "Stage 1", predominantly features images that resemble cars. The second row, "Stage 3", contains images that are more varied, with some appearing to be birds and others more abstract. The bottom row, "Stage 5", also shows a mix of images, with several resembling birds and others being abstract representations.

Figure 4: Synthesized images of CIFAR-10 using  $PDD + MTT$  from Stage 1, 3 and 5. The images from classes "automobile" and "birds" at each stage are selected for demonstration.

# 6 CONCLUSION

In this work, we proposed a progressive dataset distillation framework, PDD, that generates multiple sets of synthetic samples sequentially, conditioned on the previous ones, to capture dynamics of different training intervals. Extensive experiments confirm the effectiveness of PDD in improving the performance of existing dataset distillation methods on various benchmark datasets.

#### **REFERENCES**

- <span id="page-10-12"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750–4759, 2022.
- <span id="page-10-14"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. *arXiv preprint arXiv:2305.01649*, 2023.
- <span id="page-10-3"></span>Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, pp. 5378–5396. PMLR, 2022.
- <span id="page-10-15"></span>Reza Zanjirani Farahani and Masoud Hekmatfar. *Facility location: concepts, models, algorithms and case studies*. Springer Science & Business Media, 2009.
- <span id="page-10-17"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 770–778, 2016.
- <span id="page-10-7"></span>Katherine Hermann and Andrew Lampinen. What shapes feature representations? exploring datasets, architectures, and training. *Advances in Neural Information Processing Systems*, 33:9995–10006, 2020.
- <span id="page-10-6"></span>Wei Hu, Lechao Xiao, Ben Adlam, and Jeffrey Pennington. The surprising simplicity of the early-time learning dynamics of neural networks. *Advances in Neural Information Processing Systems*, 33: 17116–17128, 2020.
- <span id="page-10-5"></span>Dimitris Kalimeris, Gal Kaplun, Preetum Nakkiran, Benjamin Edelman, Tristan Yang, Boaz Barak, and Haofeng Zhang. Sgd on neural networks learns functions of increasing complexity. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-10-4"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pp. 11102–11118. PMLR, 2022.

<span id="page-10-10"></span>Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.

- <span id="page-10-11"></span>Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-10-13"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *arXiv preprint arXiv:2210.16774*, 2022.
- <span id="page-10-0"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022.
- <span id="page-10-16"></span>Vinod Nair and Geoffrey E Hinton. Rectified linear units improve restricted boltzmann machines. In *Proceedings of the 27th international conference on machine learning (ICML-10)*, pp. 807–814, 2010.
- <span id="page-10-8"></span>Behnam Neyshabur, Ryota Tomioka, and Nathan Srebro. In search of the real inductive bias: On the role of implicit regularization in deep learning. *arXiv preprint arXiv:1412.6614*, 2014.
- <span id="page-10-1"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. In *International Conference on Learning Representations*, 2021a. URL [https:](https://openreview.net/forum?id=l-PrrQrK0QR) [//openreview.net/forum?id=l-PrrQrK0QR](https://openreview.net/forum?id=l-PrrQrK0QR).
- <span id="page-10-2"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021b.
- <span id="page-10-9"></span>Omead Pooladzandi, David Davini, and Baharan Mirzasoleiman. Adaptive second order coresets for data-efficient machine learning. In *International Conference on Machine Learning*, pp. 17848– 17869. PMLR, 2022.

- <span id="page-11-4"></span>Harshay Shah, Kaustav Tamuly, Aditi Raghunathan, Prateek Jain, and Praneeth Netrapalli. The pitfalls of simplicity bias in neural networks. *Advances in Neural Information Processing Systems*, 33:9573–9585, 2020.
- <span id="page-11-10"></span>Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J. Gordon. An empirical study of example forgetting during deep neural network learning. In *International Conference on Learning Representations*, 2019. URL [https://openreview.](https://openreview.net/forum?id=BJlxm30cKm) [net/forum?id=BJlxm30cKm](https://openreview.net/forum?id=BJlxm30cKm).
- <span id="page-11-12"></span>Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*, 2016.
- <span id="page-11-6"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 12196–12205, 2022.
- <span id="page-11-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-11-11"></span>Max Welling. Herding dynamical weights to learn. In *Proceedings of the 26th Annual International Conference on Machine Learning*, pp. 1121–1128, 2009.
- <span id="page-11-5"></span>Yu Yang, Hao Kang, and Baharan Mirzasoleiman. Towards sustainable learning: Coresets for data-efficient deep learning. In *Proceedings of the 40th International Conference on Machine Learning*, volume 202 of *Proceedings of Machine Learning Research*, pp. 39314–39330. PMLR, 23–29 Jul 2023. URL <https://proceedings.mlr.press/v202/yang23g.html>.
- <span id="page-11-8"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021a.
- <span id="page-11-3"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021b.
- <span id="page-11-9"></span>Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *arXiv preprint arXiv:2204.07513*, 2022.
- <span id="page-11-7"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pp. 6514–6523, 2023.
- <span id="page-11-2"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021.
- <span id="page-11-1"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In Alice H. Oh, Alekh Agarwal, Danielle Belgrave, and Kyunghyun Cho (eds.), *Advances in Neural Information Processing Systems*, 2022. URL [https://openreview.net/forum?](https://openreview.net/forum?id=2clwrA2tfik) [id=2clwrA2tfik](https://openreview.net/forum?id=2clwrA2tfik).

## A1 EXPERIMENT DETAILS

#### A1.1 EXPERIMENT SETTINGS

On CIFAR-10, the networks are trained for  $\frac{2000}{P+1}$  epochs at each stage. Consequently, the total iterations taken is  $\frac{P(P+1)}{2} \frac{2000}{P+1} \times \frac{n}{B} = \frac{1000Pn}{B}$ , where B is the batch size and n is the number of images newly distilled at each stage. This quantity proves to be adequate in achieving favorable outcomes without inflating the computational burden of network training. Notably, it aligns with utilizing all available images for a training duration of 1000 epochs. Additionally, it is important to note that augmenting the number of epochs could lead to further enhancements in the test accuracy of the trained networks. For CIFAR-100, the networks undergo training for 500 epochs during each stage to facilitate improved convergence.

# A2 MORE VISUALIZATION

In Figure [A5](#page-12-0) we visualize the synthetic samples of CIFAR-10 distilled at stages 1 to 5 using PDD + MTT. We observe a significant shift of visual features in these distill images. The images distilled at the first stage are the most colorful among all the distilled samples, while the images distilled at later stages contain more abstract features and less focus on colours. These figures show that PDD helps distill diverse features according to different stages.

<span id="page-12-0"></span>Image /page/12/Figure/3 description: The image displays five stages of feature visualization, labeled Stage 1 through Stage 5. Each stage consists of a grid of small images, likely representing learned features from a neural network. Stage 1, Stage 2, and Stage 3 are arranged horizontally across the top, with Stage 4 and Stage 5 positioned below them. The grids are densely packed with colorful, abstract patterns that appear to be visualizations of different concepts or objects learned at each stage of processing.

Figure A5: Visualization of synthesized samples from Stage 1 to Stage 5.

# A3 MORE EXPERIMENT RESULTS

## A3.1 RESULTS ON MORE METHODS

We further apply PDD to DC [\(Zhao et al., 2021\)](#page-11-2) and DSA [\(Zhao & Bilen, 2021b\)](#page-11-3) to distill images from CIFAR-10. Table [A8](#page-12-1) shows the ConvNet's accuracy after trained on the distilled images. On DC and DSA, compared to using the single stage synthesis, PDD + DC and PDD + DSA generates samples that lead to higher performance, improving the baselines' performance by 2.4% and 0.7%, respectively.

<span id="page-12-1"></span>Table A8: ConvNets' test accuracy on CIFAR-10 after trained on synthetic samples generated by DC and DSA with different numbers of images per class.

| Dataset IPC | CIFAR-10 50                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |  |  |
|-------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|
| DC          | $53.9  																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																Full88.1$ |  |  |  |
# Data Distillation for Text Classification

Yongqi Li

The Hong Kong Polytechnic University, China <EMAIL>

## ABSTRACT

Deep learning techniques have achieved great success in many fields, while at the same time deep learning models are getting more complex and expensive to compute. It severely hinders the wide applications of these models. In order to alleviate this problem, model distillation emerges as an effective means to compress a large model into a smaller one without a significant drop in accuracy. In this paper, we study a related but orthogonal issue, data distillation, which aims to distill the knowledge from a large training dataset down to a smaller and synthetic one. It has the potential to address the large and growing neural network training problem based on the small dataset. We develop a novel data distillation method for text classification. We evaluate our method on eight benchmark datasets. The results that the distilled data with the size of 0.1% of the original text data achieves approximately 90% performance of the original is rather impressive.

## CCS CONCEPTS

• Information systems  $\rightarrow$  Data mining.

## KEYWORDS

Text Classification; Data Distillation

## ACM Reference Format:

Yongqi Li and Wenjie Li. 2021. Data Distillation for Text Classification. In SIGIR '21: ACM SIGIR Conference on Research and Development in Information Retrieval, June 03–05, 2021, Woodstock, NY. ACM, New York, NY, USA, [5](#page-4-0) pages.<https://doi.org/10.1145/1122445.1122456>

# 1 INTRODUCTION

Deep learning [\[10,](#page-4-1) [11\]](#page-4-2) has achieved incredible success over the past years in a variety of applications ranging from computer vision [\[8\]](#page-4-3) to natural language processing [\[14\]](#page-4-4). To solve increasingly complex and difficult problems, deep learning models have shown a clear trend toward deeper and larger. The huge computational complexity and massive storage requirements post a big challenge to effectively train deep models by using massive training data. As everyone knows, the latest developed language model GPT-3 trained on 45 TB data contains about 175 billion parameters [\[2\]](#page-4-5), which makes it difficult to train, to fine-tune and even to use.

To provide efficient deep models for practical use, previous studies on knowledge distillation [\[9\]](#page-4-6) have attempted to compress a large

SIGIR '21, June 03–05, 2021, Woodstock, NY

© 2021 Association for Computing Machinery.

ACM ISBN 978-1-4503-XXXX-X/18/06. . . \$15.00

<https://doi.org/10.1145/1122445.1122456>

Wenjie Li The Hong Kong Polytechnic University, China <EMAIL>

<span id="page-0-0"></span>Image /page/0/Figure/20 description: The image illustrates two methods for creating a smaller neural network: model distillation and data distillation. In model distillation, a large network is trained and then compressed to create a small network. In data distillation, big data is compressed to create small data, which is then used to train a small network. Both processes result in a small network.

Figure 1: Illustration of two flows to transfer the knowledge.

and deep model down to a smaller one, which would not result in a significant drop in accuracy. We refer to the efforts along this line as model distillation. In model distillation, the student model mimics the teacher model to obtain a competitive or even a superior performance [\[7,](#page-4-7) [16\]](#page-4-8). Recently, another related but orthogonal task, data distillation starts to attract people's attention. Different from model distillation that transfers knowledge from a large model to a small model, data distillation aims to encapsulate the knowledge of a large dataset into a small and synthetic dataset. The difference between them is visualized in Figure [1](#page-0-0) above. On the one hand, to explore data distillation is of interest as a tool to study neural network generalization under small sample conditions. On the other hand, it has the potential to address the large and growing neural network training problem if the adequate neural networks can be quickly trained on the small distilled datasets rather than the original massive datasets. Moreover, data distillation helps to protect data privacy since the distilled dataset is a set of synthetic data, which will not disclose the original data.

As a matter of fact, besides data distillation, a plenty of methods are targeted to reduce the size of datasets for different purposes. For instance, active learning aims to reduce the required size of training data by labeling only the examples that are determined to be the most important [\[3,](#page-4-9) [17\]](#page-4-10). In nearest-neighbor classification, prototype selection [\[5\]](#page-4-11) are also investigated to improve classification efficiency. In general, the above-mentioned methods attempt to select samples from the true distributions, i.e., the subsets of the original training sets. Differently, data distillation seeks to generate not to select a small dataset that contains most of the knowledge of the original dataset. Selecting a subset of the original dataset has a clear upper bound, because it always happens that some knowledge of the deserted data is not contained in the selected dataset. However, there is a possibility to generate new data to cover all the original knowledge. Please also note that, compared with another

Permission to make digital or hard copies of all or part of this work for personal or classroom use is granted without fee provided that copies are not made or distributed for profit or commercial advantage and that copies bear this notice and the full citation on the first page. Copyrights for components of this work owned by others than ACM must be honored. Abstracting with credit is permitted. To copy otherwise, or republish, to post on servers or to redistribute to lists, requires prior specific permission and/or a fee. Request <NAME_EMAIL>.

<span id="page-1-0"></span>Image /page/1/Figure/2 description: This figure illustrates a machine learning process involving small and big data. On the left, under the heading 'Small data', there are three neural network models labeled \u03b8
0, \u03b8
1, and \u03b8
T1. Each model receives input data pairs (x, y) and undergoes parameter updating and gradient backpropagation, indicated by arrows labeled 'Eqn.(1)' and 'Eqn.(5)'. The data flow is shown from left to right, with data pairs \u0078
1, \u0079
1 to \u0078
T1, \u0079
T1. On the right, under the heading 'Big data', there are multiple data points labeled \u0078
1, \u0078
2, ..., \u0078
TT2, and corresponding output data points \u0079
1, \u0079
2, ..., \u0079
TT2. A single neural network model is shown receiving the big data inputs \u0078
1 to \u0078
TT2, with data flow indicated by a downward arrow. Gradient backpropagation is shown with a dashed red arrow labeled 'Eqn.(5)', and parameter updating is shown with a purple arrow labeled 'Eqn.(5)'. A legend on the right clarifies the arrows: a downward arrow signifies 'Data flow', a dashed upward arrow signifies 'Gradient backpropagation', and a rightward arrow signifies 'Parameter updating'.

Figure 2: Illustration of our proposed text data distillation method.

popular generation technique based on generative adversarial networks [\[6\]](#page-4-12), data distillation mainly focuses on knowledge transfer from a large dataset to a small dataset rather than creating lifelike samples. The challenge in developing effective data distillation solutions is distinctive.

In this paper, we propose a viable data distillation method for text classification, with the aim of distilling a large class labelled dataset into a small one without a significant drop in classification accuracy. For each class, we randomly initialize a handful of samples in the form of numeric matrixes, called distilled data. We then design an optimization scheme to update the numeric distilled data towards the direction of the original data. It is of particular concern to us how to build the bridge to connect the two training processes that use the distilled data and the original data, respectively. We come up with the following idea. When we use the distilled data to train the model, the network weights are derived from a differentiable function taking the synthetic distilled data as the dependent variable by design. Given this function, the gradients can be backpropagated to the distilled data when using the original data to train the network, as illustrated in Figure [2.](#page-1-0) By repeating the above processes alternately, the distilled data is continuously updated to better approximate the original data. Finally, the distilled synthetic data can be used to train any other model as the normal training data.

We evaluate our method on eight benchmark datasets. The results are quite encouraging. The distilled data with the size of 0.1% of the original data actually achieves approximate 90% performance of the original and it significantly outperforms the heuristically selected examples with the same size.

## 2 APPROACH

Let the original text classification dataset denoted as  $\mathcal{D} = \{x_i, y_i\}_{i=1}^N$ , where  $x_i$  is a piece of text,  $y_i$  is its class label, and N is the number of samples in  ${\mathcal D}.$  We expect to transfer the knowledge in  ${\mathcal D}$  down to a new, much-reduced synthetic dataset  $\tilde{\mathcal{D}} = {\{\tilde{x}_i, \tilde{y}_i\}_{i=1}^M}$ , where M is the number of samples in  $\tilde{D}$  and  $M \ll N$ .

The discrete nature of text data makes data distillation a challenging problem. We choose to generate human-unreadable numeric

matrixes instead of extracting real text words to form the distilled data, considering the target is to generate data for neural networks to learn knowledge from it rather than for people to read and understand. More important, the numeric matrixes can be dealt with in the same way as the parameters of a network, thus a gradient descent based method can be applied to update and optimize it. Even though, it is non-trivial to develop a suitable framework and to design an appropriate training objective that can effectively update the distilled data towards the original data. Inspired by the previous works [\[1,](#page-4-13) [18\]](#page-4-14), we made efforts to conduct a function where the input is the distilled data and output is a well-trained model, so that the gradients can be backpropagated to the distilled data via this function when using the original data to train the model.

In more details, we first randomly initialize the distilled data  $\tilde{\mathcal{D}}$  as  $\tilde{\mathcal{D}}_0$ . For each class, we create a handful of matrixes. Then we apply minibatch stochastic gradient descent to train a text classification model  $\Theta_0$  on the initial distilled data. Specifically, we divide the distilled data  $\tilde{\mathcal{D}}_0$  into batches, denoted as  $\{\tilde{x}_t\}_{t=1}^{T_1}$ , where  $T_1$  is the number of batches. We input the batches of data to the model to update its parameters. Given a batch of data  $\tilde{x}_t$ , the parameters are updated as follows,

$$
\Theta_t = \Theta_{t-1} - \alpha * \partial_{\Theta_{t-1}} L(\tilde{x}_t, \Theta_{t-1}), \tag{1}
$$

where  $\alpha$  denotes the learning rate and  $L()$  denotes the loss function. We refer to the model trained after the  $T_1$  $T_1$  batches as  $\Theta_{T_1}^{-1}$ . Basically,  $\Theta_{T_1}$  is derived from  $\Theta_0$  via a series of gradient descent steps by,

$$
\Theta_{T_1} = F(\Theta_0, \alpha, \tilde{\mathcal{D}}), \tag{2}
$$

where  $F()$  denotes the training process. Note that  $F(\Theta_0, \alpha, \tilde{\mathcal{D}})$  is a differentable function on its independent variable.

We then transform the text  $x_i$  in the original text data  $\mathcal D$  to an embedding matrix. By convention, we pad  $x_i$  to a fixed length and embed each word in  $x_i$  to an embedding vector. Similarly, we divide the data into batches, denoted as  $\{x_t\}_{t=1}^{\widetilde{T_2}}$ . We input a batch of data  $x_t$  to the model  $\Theta_t$  and calculate the loss  $L(x_t, \Theta_{T_1})$  as follows,

<span id="page-1-2"></span>
$$
L(x_t, \Theta_{T_1}) = L(x_t, F(\Theta_0, \alpha, \tilde{\mathcal{D}})).
$$
\n(3)

<span id="page-1-1"></span><sup>&</sup>lt;sup>1</sup>In practice, the training process can be extended to multi epochs.

Table 1: Statistics of 8 Datasets.

<span id="page-2-2"></span>

| Dataset                | Classes | Train<br>Samples | Test<br>Samples | Task                   |
|------------------------|---------|------------------|-----------------|------------------------|
| DBpedia                | 14      | 560k             | 70k             | Ontology<br>Extraction |
| Yahoo! Answers         | 10      | 1400k            | 60k             | QA                     |
| Sogou News             | 5       | 450k             | 60k             | News<br>Classification |
| AG' News               | 4       | 120k             | 7.6k            |                        |
| Yelp Review Full       | 5       | 650k             | 50k             | Sentiment<br>Analysis  |
| Yelp Review Polarity   | 2       | 560k             | 38k             |                        |
| Amazon Review Full     | 5       | 3,600k           | 400k            |                        |
| Amazon Review Polarity | 2       | 3,000k           | 650k            |                        |

As mentioned before,  $F(\Theta_0, \alpha, \tilde{\mathcal{D}})$  is differentiable, thus the current loss function  $L(x_t, \Theta_{T_1})$  is also a differentiable function of the distilled data  $\tilde{\mathcal{D}}$ . Our training objective is to find out an optimal  $\tilde{\mathcal{D}_*}$  that minimizes the loss, formulated as,

<span id="page-2-1"></span>
$$
\tilde{\mathcal{D}}_* = \arg\min_{\tilde{\mathcal{D}}} L(x_t, \Theta_T) = \arg\min_{\tilde{\mathcal{D}}} L(x_t, F(\Theta_0, \alpha, \tilde{\mathcal{D}})). \tag{4}
$$

Since the distilled data  $\tilde{\mathcal{D}}$  is numeric like the parameters of a network,  $L(x_t, \Theta_T)$  is a differentiable function of the synthetic distilled data according to Equation [\(3\)](#page-1-2). It makes workable to update the numeric  $\tilde{\mathcal{D}}$  using the following gradient descent algorithm,

<span id="page-2-0"></span>
$$
\tilde{\mathcal{D}}_t = \tilde{\mathcal{D}_{t-1}} - \alpha * \partial_{\tilde{\mathcal{D}}} L(x_t, \Theta_{T_1}).
$$
\n(5)

Through the above training steps, the distilled data  $\tilde{\mathcal{D}}$  is updated towards the minimal loss, which means that the difference of the results between using  $\tilde{\mathcal{D}}$  and  $\mathcal{D}$  is reduced. Using the batches of data  ${x_t}_{t=1}^{T_2}$ , the distilled data is updated from  $\tilde{D}_0$  to  $\tilde{D}_{T_2}$  via Equation. [\(5\)](#page-2-0). At the end, we store the distilled data  $\tilde{\mathcal{D}}_{T_2}$  and it can be used to train neural networks as normal text data.

In short, the number of the samples in  $\tilde{\mathcal{D}}_{T_2}$  is M, which is much smaller than that of the original data  $D$ . At the same time, the distilled data  $\tilde{\mathcal{D}}_{T_2}$  contains knowledge of the original data as much as possible according to the training objective in Equation [\(4\)](#page-2-1) and through the training process illustrated in Equation [\(5\)](#page-2-0). To evaluate the proposed method, we can train an arbitrary text classification model on both the distilled data and the original data, and compare their performance on a same test data.

#### 3.1 Datasets

#### 3.2 Experimental Settings

We used eight publicly benchmark datasets [\[4,](#page-4-15) [15,](#page-4-16) [19\]](#page-4-17) to evaluate our proposed text data distillation method. These datasets are sourced from various tasks, including sentiment analysis, news classification, question answering and ontology extraction. The statistics of the datasets are summarized in Table [1.](#page-2-2) We applied our method to the training set of the datasets and obtained the much-reduced distilled training data.

#### 3.2 Experimental Settings

Evaluation Protocols. To evaluate the utility of the distilled data, we train a text classification model with the same network structure on the original data and our distilled data, respectively. And then we calculate the accuracy of the two well trained model on the same test set. We also compare the distilled data with the same size of the data randomly selected from the original data.

Implement Details. We use the pre-trained GloVe word vec-tors<sup>[2](#page-2-3)</sup>, which is trained on the Twitter data and the dimension is 100 for all of the datasets except for the Sogou News dataset. Because there are too many words out of the vocabulary in the Sogou News dataset, we randomly initialized the word vectors. We follow the work [\[19\]](#page-4-17) that released the eight benchmark datasets to apply the TextCNN network. We do not use any extra regularization method, like L2 normalization or dropout. Note that Equation [\(5\)](#page-2-0) involves high order derivative, which requires expensive memory and extensive computation. We therefore apply the back-gradient optimization technique that formulates the necessary second order terms into efficient Hessian-vector products [\[13\]](#page-4-18) so that they can be can be easily calculated with modern automatic differentiation systems such as PyTorch [\[12\]](#page-4-19).

#### 3.3 Experiment Results

The test set accuracies of the text classification models trained on the full training data, the randomly selected data, and the distilled data are summarized in Table [2.](#page-3-0) The numbers of samples in the random data and the distilled data are the same, i.e., 0.1% of the original training data in DBpedia, yahoo! Answers, Sogou News, AG' News, Yelp Review Full, and Yelp Review Polarity datasets. Considering the larger scale of Amazon Review Full and Amazon Review Polarity, we set the size to 0.01% of the original training data on these two datasets. The findings from this set of experiments are as follows.

(1) It is observed that the text classification model trained on the full data obtains the best performance. This is not surprising, since the sizes of the random data and distilled the data are much smaller than that of the full data. Moreover, the random data is only a part of the full data and the distilled data is updated towards the full data. Therefore, the full data can be regarded as the upper bound.

(2) Although the size of the distilled data is much smaller than the full data, the model trained on it can still achieve a comparative performance. In terms of accuracy, the distilled data obtains 81.78%, 98.35%, 92.06%, 97.11%, 94.00%, 94.25%, 83.47%, and 93.76% relative to the full data (referred to as the upper bound) on the DBpedia, yahoo! Answers, Sogou News, AG' News, Yelp Review Full, Yelp Review Polarity, Amazon Review Full, and Amazon Review Polarity, respectively. In average, up to 91.84% of the accuracy trained on the full data is achieved. This impressive result verifies the effectiveness of our proposed method and demonstrates the great potential of data distillation. It is worth mentioning that on the two Amazon Review datasets, the distilled data only covers 0.01% of the full training data, while it is 0.1% on the other datasets. Therefore, the gap between the distilled data and full data on these two datasets is a big larger than that on the other datasets.

(3) The distilled data significantly surpasses the random data. As claimed before, generating synthetic samples is more possible to get close to the upper bound because it aims to compresses all knowledge into a small data rather than select a subset. We also

<span id="page-2-3"></span><sup>2</sup><https://nlp.stanford.edu/projects/glove/.>

<span id="page-3-0"></span>Table 2: Accuracy on 8 benchmark datasets. Full data and Random data denote the original and randomly selecting a subset of the training data, respectively. The size of the Random data and our Distilled data is same. And the size is 0.01% of the Full data on Amazon Review Full, Polarity datasets, while it is 0.1% on other datasets.

| Method         | DBpedia | Yahoo!<br>Answers | Sogou<br>News | AG's News | Yelp<br>Review Full | Yelp<br>Review Polarity | Amazon<br>Review Full | Amazon<br>Review Polarity |
|----------------|---------|-------------------|---------------|-----------|---------------------|-------------------------|-----------------------|---------------------------|
| Full data      | 0.9779  | 0.6700            | 0.9404        | 0.8819    | 0.6045              | 0.9219                  | 0.5464                | 0.9102                    |
| Random data    | 0.6910  | 0.5293            | 0.8278        | 0.7133    | 0.3582              | 0.7763                  | 0.2603                | 0.6952                    |
| Distilled data | 0.7998  | 0.6590            | 0.8658        | 0.8564    | 0.5682              | 0.8689                  | 0.4561                | 0.8534                    |

<span id="page-3-2"></span><span id="page-3-1"></span>Image /page/3/Figure/3 description: The image displays eight subfigures labeled (a) through (h), each presenting a line graph plotting accuracy against epochs. Subfigures (a) through (d) show two lines each, representing 'Distilled Data' and 'Random Data'. Subfigures (e) through (h) also show two lines each, labeled 'Distilled Data' and 'Random Data'. The y-axis for all these subfigures represents accuracy, ranging from approximately 0.1 to 0.9. The x-axis for subfigures (a) through (h) represents epochs, ranging from 1 to 6. To the right of these subfigures, there is a larger graph labeled 'Figure 4: Accuracy versus Data size (%)'. This graph plots accuracy on the y-axis against data size on the x-axis, with data size ranging from 1 to 10. The accuracy increases from approximately 0.72 to 0.87 as the data size increases. A dashed red line at 0.94 is labeled 'Full data'.

<span id="page-3-3"></span>Figure 3: Accuracy versus the training epochs. (a)-(h) performance over DBpedia, Yahoo! Answers, Sogou News, AG' News, Yelp Review Full, Yelp Review Polarity, Amazon Review Full, and Amazon Review Polarity respectively.

find that although the samples in the random data only account for a small percentage of the full data, it also supports to train a decent classification model successfully in some datasets. For example, for each class on the AG's News dataset there are only 30 samples. The classification model trained on the small random data still achieves 0.7133 accuracy. It is demonstrated that there are many redundancies and repetitive knowledge, and further shows the necessary of data distillation. From the Hotsehotter of the training epochs ( $\sqrt{2\pi}$ ) and  $\theta$ ) and  $\theta$  (a) sometimes are training epochs to learn knowledge, and the training epochs to learn knowledge, the training epochs to learn training epochs to

We further compare the performance of the model trained on the random data and the distilled data versus the training epochs. By analyzing Figure [3,](#page-3-1) we gain the following insights.

(1) It is easier to train neural networks on the distilled data compared with the random data. For example, in Figures [3\(a\)](#page-3-2) and [3\(a\),](#page-3-2) the accuracy of the model trained on our distilled data increases fast to reaches its highest level. This demonstrates that the distilled data facilitate the effective training process, which is also verified in [\[18\]](#page-4-14). It is contributed to our optimization scheme that generates a more smooth optimization space when distilling knowledge.

(2) The accuracy of the model trained on the random data gradually ascends but still has a big gap to reach its best performance as reported in Table [2.](#page-3-0) For example, the accuracy of the model trained on the random data after 6 training epochs is 0.1345 in Figure [3\(a\),](#page-3-2) and more training epochs are needed in order to reach its best accuracy 0.6910. It is understandable that the model trained on the

<span id="page-3-4"></span>Image /page/3/Figure/9 description: The image contains three plots. Plot (d) shows accuracy over epochs for distilled data and random data, with distilled data reaching an accuracy of about 0.85 by epoch 6, while random data reaches about 0.5. Plots (a) and (b) show accuracy versus data size (in %00) for Sogou News and AG's News datasets, respectively. Both plots show an increasing trend in accuracy as data size increases, with a dashed red line indicating "Full data" accuracy. For Sogou News (a), accuracy increases from about 0.73 at 1%00 to about 0.86 at 10%00. For AG's News (b), accuracy increases from about 0.77 at 1%00 to about 0.85 at 10%00. The "Full data" accuracy is indicated as approximately 0.94 for Sogou News and 0.88 for AG's News.

Figure 4: Accuracy versus the size of the distilled data.

since there are only a few numbers of samples for each class. We also find that in Figure [3\(f\),](#page-3-3) the model trained on the random data converges as fast as on the distilled data. This might because there are only two classes, thus it is easier for a classification model to distinguish samples.

#### 3.4 Data Size Analysis

In addition to compare with the full data and the random data, we also conduct experiments to explore the influence of the distilled data size on the performance. Towards this end, we report the accuracy with the data size varying from 0.01% to 0.1% of the full data, as shown in Figure [4.](#page-3-4) It is observed that when the size of the distilled data increases, the accuracy of the model trained on it raises and gets closer to the upper bound, i.e., the model trained on the full data. It may be due to the fact that it is much likely for the larger size of data to cover the knowledge distilled from the full data. Although we formulate the necessary second order terms into efficient Hessian-vector products as mentioned before, the computing consumption is still large. Thus, we only extend the size of the distilled data to 0.1% of the full data. This is the limitation of the current data distillation method and requires further improvement.

## 4 CONCLUSION

In this paper, we explore a novel problem, data distillation, which aims to distill knowledge from a large training dataset down to a smaller and synthetic one. We propose a viable data distillation method for text classification. An optimization scheme is designed to update the numeric distilled data towards the direction of the original data. The experimental result that the distilled data with the size of 0.1% of the original text data achieves approximately 90%

<span id="page-4-0"></span>Data Distillation for Text Classification

performance of the original is rather impressive. It also shows that the small distilled data facilitates the effective training process.

## REFERENCES

- <span id="page-4-13"></span>[1] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. 2020. Flexible Dataset Distillation: Learn Labels Instead of Images. arXiv preprint arXiv:2006.08572 (2020).
- <span id="page-4-5"></span>[2] Tom B Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. 2020. Language models are few-shot learners. arXiv preprint arXiv:2005.14165 (2020).
- <span id="page-4-9"></span>[3] David A Cohn, Zoubin Ghahramani, and Michael I Jordan. 1996. Active learning with statistical models. Journal of Artificial Intelligence Research 4, 1 (1996), 129–145.
- <span id="page-4-15"></span>[4] Cunxiao Du, Zhaozheng Chen, Fuli Feng, Lei Zhu, Tian Gan, and Liqiang Nie. 2019. Explicit interaction model towards text classification. In Proceedings of the Conference on Artificial Intelligence. AAAI, 6359–6366.
- <span id="page-4-11"></span>[5] Salvador Garcia, Joaquin Derrac, Jose Cano, and Francisco Herrera. 2012. Prototype selection for nearest neighbor classification: Taxonomy and empirical study. IEEE Transactions on Pattern Analysis and Machine Intelligence 34, 3 (2012), 417–435.
- <span id="page-4-12"></span>[6] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. 2014. Generative Adversarial Nets. In Proceedings of the International Conference on Neural Information Processing Systems. Curran Associates, Inc., 2672–2680.
- <span id="page-4-7"></span>[7] Jianping Gou, Baosheng Yu, Stephen John Maybank, and Dacheng Tao. 2020. Knowledge distillation: A survey. arXiv preprint arXiv:2006.05525 (2020).
- <span id="page-4-3"></span>[8] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. 2016. Deep Residual Learning for Image Recognition. In Proceedings of the Conference on Computer Vision and Pattern Recognition. IEEE, 770–778.

- <span id="page-4-6"></span>[9] Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. 2015. Distilling the knowledge in a neural network. arXiv preprint arXiv:1503.02531 (2015).
- <span id="page-4-1"></span>[10] Sepp Hochreiter and Jürgen Schmidhuber. 1997. Long short-term memory. Neural computation 9, 8 (1997), 1735–1780.
- <span id="page-4-2"></span>[11] Yann LeCun, Yoshua Bengio, and Geoffrey Hinton. 2015. Deep learning. Nature 521, 7553 (2015), 436–444.
- <span id="page-4-19"></span>[12] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, et al. 2019. PyTorch: An Imperative Style, High-Performance Deep Learning Library. In Proceedings of the International Conference on Neural Information Processing Systems. Curran Associates, Inc., 8026–8037.
- <span id="page-4-18"></span>[13] Barak A Pearlmutter. 1994. Fast exact multiplication by the Hessian. Neural computation 6, 1 (1994), 147–160.
- <span id="page-4-4"></span>[14] Hao Peng, Jianxin Li, Yu He, Yaopeng Liu, Mengjiao Bao, Lihong Wang, Yangqiu Song, and Qiang Yang. 2018. Large-scale hierarchical text classification with recursively regularized deep graph-cnn. In Proceedings of the World Wide Web Conference. ACM, 1063–1072.
- <span id="page-4-16"></span>[15] Chao Qiao, Bo Huang, Guocheng Niu, Daren Li, Daxiang Dong, Wei He, Dianhai Yu, and Hua Wu. 2018. A New Method of Region Embedding for Text Classification.. In Proceedings of the International Conference on Learning Representations.
- <span id="page-4-8"></span>[16] Xuemeng Song, Fuli Feng, Xianjing Han, Xin Yang, Wei Liu, and Liqiang Nie. 2018. Neural compatibility modeling with attentive knowledge distillation. In Proceedings of the International Conference on Research and Development in Information Retrieval. ACM, 5–14.
- <span id="page-4-10"></span>[17] Damiano Spina, Maria-Hendrike Peetz, and Maarten de Rijke. 2015. Active learning for entity filtering in microblog streams. In Proceedings of the International Conference on Research and Development in Information Retrieval. ACM, 975–978.
- <span id="page-4-14"></span>[18] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. 2018. Dataset distillation. arXiv preprint arXiv:1811.10959 (2018).
- <span id="page-4-17"></span>[19] Xiang Zhang, Junbo Zhao, and Yann LeCun. 2015. Character-level convolutional networks for text classification. In Proceedings of the International Conference on Neural Information Processing Systems. Curran Associates, Inc., 649–657.
# Dataset Distillation of 3D Point Clouds via Distribution Matching

<PERSON><PERSON>-<PERSON> Yim $^\dagger,$  Dongwook Kim $^\dagger,$  <PERSON>ae-<PERSON> Sim $^\star$ Ulsan National Institute of Science and Technology {yimjae0, donguk071, jysim}@unist.ac.kr

<sup>†</sup>Equal contribution  $\star$  Corresponding author

## Abstract

Large-scale datasets are usually required to train deep neural networks, but it increases the computational complexity hindering the practical applications. Recently, dataset distillation for images and texts has been attracting a lot of attention, that reduces the original dataset to a synthetic dataset to alleviate the computational burden of training while preserving essential task-relevant information. However, the dataset distillation for 3D point clouds remains largely unexplored, as the point clouds exhibit fundamentally different characteristics from that of images, making the dataset distillation more challenging. In this paper, we propose a distribution matching-based distillation framework for 3D point clouds that jointly optimizes the geometric structures as well as the orientations of the synthetic 3D objects. To address the semantic misalignment caused by unordered indexing of points, we introduce a Semantically Aligned Distribution Matching loss computed on the sorted features in each channel. Moreover, to address the rotation variation, we jointly learn the optimal rotation angles while updating the synthetic dataset to better align with the original feature distribution. Extensive experiments on widely used benchmark datasets demonstrate that the proposed method consistently outperforms existing dataset distillation methods, achieving superior accuracy and strong cross-architecture generalization.

## 1 Introduction

With the increasing demand for large-scale training datasets, the cost of model retraining from scratch has become a major challenge leading to the research of dataset distillation [\[21\]](#page-10-0). The objective of dataset distillation is to produce a significantly smaller synthetic dataset from a large original dataset, that preserves the essential task-relevant information contained in the original dataset. Hence the models trained on the reduced synthetic dataset are encouraged to achieve comparable performance to those trained on the original dataset. Existing dataset distillation methods [\[3,](#page-9-0) [6,](#page-9-1) [8,](#page-9-2) [11,](#page-9-3) [29,](#page-10-1) [28,](#page-10-2) [30,](#page-10-3) [17,](#page-9-4) [26,](#page-10-4) [5,](#page-9-5) [9\]](#page-9-6) can be broadly classified into gradient matching, trajectory matching, and distribution matching approaches. The gradient matching and trajectory matching methods aim to ensure that the synthetic dataset produces similar optimization dynamics to the original dataset. To this end, the gradient matching method [\[29\]](#page-10-1) minimizes the difference between the gradients computed on the synthetic and original datasets. However, it potentially overlooks longterm dependencies in the training process. Rather than comparing individual gradients, the trajectory matching methods [\[3,](#page-9-0) [6,](#page-9-1) [8,](#page-9-2) [11\]](#page-9-3) encourage the models trained on the synthetic dataset follow similar optimization trajectories to those trained on the original dataset. Note that these methods train the networks while optimizing the synthetic dataset, and significantly increase the computational complexity. To alleviate the computational burden in dataset distillation, the distribution matching techniques [\[28,](#page-10-2) [30,](#page-10-3) [17,](#page-9-4) [26,](#page-10-4) [5\]](#page-9-5) have been introduced. They randomly initialize the networks without

training to extract the features, and compare the feature distributions between the original and synthetic datasets.

While dataset distillation has been extensively studied for structured data such as images [\[29,](#page-10-1) [3,](#page-9-0) [28\]](#page-10-2) and texts [\[10,](#page-9-7) [12,](#page-9-8) [13\]](#page-9-9), its application to 3D point clouds remains almost unexplored. PCC [\[27\]](#page-10-5) simply applied an existing distillation method for images [\[29\]](#page-10-1) to the 3D point clouds without considering inherent characteristics of 3D point clouds, however it still suffers from high computational complexity. Unlike the structured data, 3D point clouds consist of unordered and irregularly distributed points in 3D space. Therefore, semantically similar regions across different 3D models are often associated with inconsistent orders (indices of points), that makes direct feature comparison incorrect worsening the performance of distribution matching based dataset distillation. We refer to this issue as *semantic misalignment*. Moreover, the datasets of 3D point clouds also suffers from *rotational variation*. 3D point clouds are often captured or synthesized under arbitrary poses due to the absence of canonical orientations. Such rotational difference causes the objects of the same class yield different features from one another, significantly increasing the intra-class variability. As a result, it becomes difficult to construct a representative synthetic dataset that faithfully follows the feature distribution of the original dataset.

In this paper, we propose an optimization framework combining Semantically Aligned Distribution Matching (SADM) and orientation optimization for dataset distillation of 3D point clouds. A major challenge in this setting is achieving effective feature alignment despite the unordered nature of point clouds and their arbitrary orientations. The SADM loss addresses the issue of misaligned semantic structures by sorting the point-wise feature values within each channel before computing the distance between two feature distributions. In parallel, we employ learnable rotation parameters to address the orientation variation by estimating the optimal poses of 3D models while generating the synthetic dataset. By simultaneously enforcing semantic consistency and orientation alignment, the proposed method achieves superior performance of dataset distillation compared with the existing methods, as demonstrated by extensive experiments on standard 3D point cloud classification benchmarks.

The key contributions of this paper are summarized as follows:

- To the best of our knowledge, we are the first to propose a distribution matching-based dataset distillation method of 3D point clouds, that jointly optimizes the shapes and orientations of synthetic dataset.
- We devised the SADM loss on the sorted features in each channel to preserve the semantic alignment between compared 3D objects.
- We validated the proposed method through extensive experiments on the four benchmark datasets widely used for 3D point clouds classification, and showed the superiority of the proposed method over the existing dataset distillation techniques.

## 2 Related Works

#### 2.1 3D Point Data Analysis

3D point clouds are generally unordered exhibiting irregular characteristics, that makes it difficult to apply the convolution operations in deep neural networks commonly used for images. Early approaches [\[25,](#page-10-6) [32\]](#page-10-7) convert the point clouds into structured representation of multi-view images or voxel grids to enable the use of conventional deep learning architectures, such as convolutional neural networks (CNNs). VoxelNet [\[32\]](#page-10-7) voxelizes the point clouds, but it causes increased memory consumption and quantization errors. To directly process point clouds without conversion to intermediate structured representations, PointNet [\[14\]](#page-9-10) was introduced as a pioneering work that learns the features directly from unordered raw point clouds. However, it suffers from the limitation in capturing local geometric relationships, and hierarchical CNNs have been developed tailored to 3D point clouds. PointNet++[\[15\]](#page-9-11) captures local geometric structures through iterative sampling and grouping strategies. On the other hand, PointConv [\[23\]](#page-10-8) generalizes the convolution operations to irregular point clouds by adaptively weighting the neighboring points based on their spatial distribution. Meanwhile, DGCNN [\[22\]](#page-10-9) constructs dynamic graphs from point clouds to adaptively capture the semantic information. Attempts have been also made to apply the transformer [\[20\]](#page-10-10) to 3D point clouds processing, where the Point Transformer [\[31\]](#page-10-11) utilizes the attention mechanism to capture the long-range dependencies.

Image /page/2/Figure/0 description: This diagram illustrates a method for semantically aligned distribution matching between real and synthetic datasets. The process begins with a 'Real Dataset' labeled T, which is processed by a 'PointNet Feature Extractor'. Simultaneously, 'Synthetic Data' labeled S undergoes a 'Rotation' transformation and is also processed by a 'PointNet Feature Extractor'. Both feature extractors output feature representations that are then fed into a 'Semantically Aligned Distribution Matching' module. This module involves sorting functions and matrix multiplication, leading to the calculation of two loss functions, L\_beta and L\_alpha, which are combined. The diagram also includes a legend explaining symbols for matrix multiplication, sorting function, backward and forward passes, and shared MLP layers.

<span id="page-2-0"></span>Figure 1: The overall framework of the proposed dataset distillation method for 3D point clouds.

## 2.2 Coreset Selection

Coreset selection is a technique designed to select representative samples from a given dataset, while maintaining the model's performance even with the sampled data. The random selection method [\[16\]](#page-9-12) randomly chooses a subset of data samples from the whole dataset. It is simple but suffers from the robustness due to the lack of informative criteria for sampling. The K-center method [\[18\]](#page-9-13) selects data points iteratively that maximize the minimum distance to the set of already selected ones, taking into account the data distribution. The Herding method  $[2, 1]$  $[2, 1]$  $[2, 1]$  iteratively takes data points that minimize the discrepancy between the mean embeddings of the selected subset and the entire dataset in the feature space.

#### 2.3 Dataset Distillation

Dataset distillation [\[21\]](#page-10-0) methods can be largely categorized into the gradient matching [\[29\]](#page-10-1), trajectory matching [\[3,](#page-9-0) [6,](#page-9-1) [8,](#page-9-2) [11\]](#page-9-3), and distribution matching [\[28,](#page-10-2) [30,](#page-10-3) [17,](#page-9-4) [26,](#page-10-4) [5\]](#page-9-5) approaches. The gradient matching, first introduced by DC [\[29\]](#page-10-1), minimizes the difference between the gradients computed from the original and synthetic datasets, respectively, guiding the synthetic dataset to follow the training direction of the original dataset. The trajectory matching methods [\[3,](#page-9-0) [6,](#page-9-1) [8,](#page-9-2) [11\]](#page-9-3), initially proposed by MTT [\[3\]](#page-9-0), encourage the models trained on the synthetic dataset to follow the optimization trajectories similar to those trained on the original dataset rather than comparing individual gradients. ATT [\[11\]](#page-9-3) automatically adjusts the length of the training trajectories between the synthetic and original datasets, enabling more effective and precise matching. The distribution matching, introduced by DM [\[28\]](#page-10-2), focuses on minimizing the distance between the feature distributions of the original and synthetic datasets. DataDAM [\[17\]](#page-9-4) enhances the distribution matching by aligning the feature maps using attention mechanism, achieving unbiased feature representation with low computational overhead. Furthermore, M3D [\[26\]](#page-10-4) employs the Gaussian kernel function in the distribution matching loss, enabling the alignment across higher-order statistical characteristics of the feature distributions. Recently, a gradient matching-based point clouds distillation method [\[27\]](#page-10-5) has been introduced, however it simply applied the existing image-based method without considering the unique characteristics of 3D point clouds.

## 3 Methodology

We propose a novel dataset distillation method for 3D point clouds that first addresses the key challenges of semantic misalignment and rotational variation. We perform SADM that effectively aligns the features of semantically consistent structures across different 3D models. To handle the rotational variation, we also estimate optimal orientations while generating the synthetic dataset. Figure [1](#page-2-0) shows the overall framework of the proposed method.

## 3.1 Preliminaries

Problem Definition. The objective of dataset distillation is to compress the task-relavant information in the original dataset  $\mathcal{T} = {\{\mathbf{t}_i\}}_{i=1}^{|\mathcal{T}|}$  and generate a much smaller synthetic dataset  $\mathcal{S} = {\{\mathbf{s}_i\}}_{i=1}^{|\mathcal{S}|}$ , where  $|\mathcal{S}| \ll |\mathcal{T}|$ , such that a model trained on  $\mathcal S$  achieves close performance to that trained on  $\mathcal T$ . Given a 3D point cloud sample p following a real data distribution with the corresponding class label

l, the optimal synthetic dataset  $S^*$  can be obtained via

$$
\mathcal{S}^* = \underset{\mathcal{S}}{\arg\min} \mathbb{E}_{\mathbf{p}} \left[ ||\mathcal{L}(\phi_{\mathcal{T}}(\mathbf{p}), l) - \mathcal{L}(\phi_{\mathcal{S}}(\mathbf{p}), l)||^2 \right], \tag{1}
$$

where L denotes a task-specific loss function, such as the cross-entropy loss, and  $\phi_T$  and  $\phi_S$  represent the models to estimate the class label which are trained on  $\mathcal T$  and  $\mathcal S$ , respectively.

Distribution Matching. Distribution matching (DM) strategy focuses on aligning the feature distributions derived from the original and synthetic datasets, respectively, via

$$
\mathcal{S}^* = \underset{\mathcal{S}}{\arg\min} D(\phi(\mathcal{T}), \phi(\mathcal{S})),\tag{2}
$$

where  $\phi$  is the feature extractor and D denotes a distance function. We employ a randomly initialized, untrained network, which has been demonstrated to sufficiently capture the structural information for distribution alignment [\[28\]](#page-10-2). Also, the Maximum Mean Discrepancy (MMD) [\[7\]](#page-9-16) loss  $\mathcal{L}_{\text{MMD}}$  is often used as  $D$ , given by

<span id="page-3-1"></span>
$$
\mathcal{L}_{\text{MMD}}(\mathcal{T}, \mathcal{S}) = K(\mathcal{T}, \mathcal{T}) + K(\mathcal{S}, \mathcal{S}) - 2K(\mathcal{T}, \mathcal{S}),
$$
\n(3)

where  $K(\cdot, \cdot)$  is a kernel function. We used the Gaussian kernel in this work as

$$
K(\boldsymbol{\mathcal{T}}, \boldsymbol{\mathcal{S}}) = \frac{1}{|\boldsymbol{\mathcal{T}}| \cdot |\boldsymbol{\mathcal{S}}|} \sum_{\mathbf{t} \in \boldsymbol{\mathcal{T}}} \sum_{\mathbf{s} \in \boldsymbol{\mathcal{S}}} \exp\left(-\frac{\|\phi(\mathbf{t}) - \phi(\mathbf{s})\|^2}{2\sigma}\right).
$$
 (4)

#### 3.2 Semantically Aligned Distribution Matching

Whereas the pixels of image exhibit well structured spatial relationships with one another and consistently indexed across different images, the points in 3D point clouds are unordered with different indices across different models. Therefore, the conventional distribution matching methods cannot be directly applied to 3D point clouds, since the features of semantically similar structures are not aligned between two compared models. Motivated by the importance of preserving semantic correspondence, we investigate the relationship between the feature values and semantic significance in 3D point clouds. We first extracted point-wise features using a randomly initialized network  $\phi$ , and sorted the feature values for each of 1024 channels ac-

Image /page/3/Figure/11 description: The image displays a grid of 3x3 3D models. The first two columns show airplane models from different angles, with red dots highlighting certain areas. The third and fourth columns show guitar models from various perspectives, also with red dots indicating specific regions. The fifth and sixth columns feature elongated, flat rectangular prism models, again with red dots scattered across their surfaces.

<span id="page-3-0"></span>Figure 2: Visualization of the points corresponding to the largest (top), 200th largest (middle), and 500th largest (bottom) features in each channel.

cording to their size. Figure [2](#page-3-0) visualizes the points corresponding to the largest (top), 200th largest (middle), and 500th largest (bottom) features, respectively. We observe that, even without model training, the points associated with the largest features consistently capture semantically meaningful regions, such as edges and corners, across different classes. In contrast, the points yielding low-ranked features are distributed around less informative regions. This suggests that the features reflect the relative importance of points in characterizing the structures of 3D objects, and the points with similar orders of sorted features tend to capture semantically related regions across different 3D models.

In order to preserve the semantic correspondence across different 3D models, we propose a semantically aligned distribution matching (SADM) loss. Specifically, given a 3D point cloud object p, we extract the features using  $\phi$  via

$$
\phi(\mathbf{p}) = [\mathbf{f}_1, \mathbf{f}_2, \dots, \mathbf{f}_C], \quad \mathbf{f}_i \in \mathbb{R}^N,
$$
\n(5)

where N denotes the number of points in p, C is the number of feature channels, and  $f_i =$  $\{f_{i,1}, f_{i,2}, ..., f_{i,N}\}\$ represents the feature vector of the *i*-th channel where  $f_{i,j}$  is the feature value of the j-th point. We then perform channel-wise sorting to the extracted features of  $f_{i,j}$ 's in the descending order of their sizes, and obtain the sorted feature vector  $\tilde{\bf f}_i=\left\{\tilde{f}_{i,1},\tilde{f}_{i,2},...,\tilde{f}_{i,N}\right\}$  such that  $\tilde{f}_{i,j} \geq \tilde{f}_{i,j+1}$ . Then we have the set of the sorted feature vectors as

$$
\tilde{\phi}(\mathbf{p}) = \left[\tilde{\mathbf{f}}_1, \tilde{\mathbf{f}}_2, \dots, \tilde{\mathbf{f}}_C\right], \quad \tilde{\mathbf{f}}_i \in \mathbb{R}^N.
$$
\n(6)

Note that t in the original dataset  $\tau$  and s in the synthetic dataset S exhibit different orders of points in general, and therefore the features in  $\phi(t)$  and  $\phi(s)$  are inconsistently aligned with each other worsening the desired behavior of comparison in [\(3\)](#page-3-1). On the contrary, the sorted features  $\phi(t)$ and  $\phi(s)$  exhibit consistent ordering of features in each channel reflecting their relative semantic importance, and thus facilitates reliable feature comparison across different 3D objects. To measure the discrepancy between the sorted features, we redefine the Gaussian kernel as

$$
\tilde{K}(\boldsymbol{\mathcal{T}}, \boldsymbol{\mathcal{S}}) = \frac{1}{|\boldsymbol{\mathcal{T}}| \cdot |\boldsymbol{\mathcal{S}}|} \sum_{\mathbf{t} \in \boldsymbol{\mathcal{T}}} \sum_{\mathbf{s} \in \boldsymbol{\mathcal{S}}} \exp\left(-\frac{||\tilde{\phi}(\mathbf{t}) - \tilde{\phi}(\mathbf{s})||^2}{2\sigma}\right).
$$
\n(7)

Then we devise the loss  $\mathcal{L}_{\alpha}$  from  $\mathcal{L}_{MMD}$  [\(3\)](#page-3-1), given by

$$
\mathcal{L}_{\alpha}(\mathcal{T}, \mathcal{S}) = \tilde{K}(\mathcal{T}, \mathcal{T}) + \tilde{K}(\mathcal{S}, \mathcal{S}) - 2\tilde{K}(\mathcal{T}, \mathcal{S}).
$$
\n(8)

We additionally employ  $\mathcal{L}_{\beta}$  to boost the role of the most significant feature in each channel, defined as

$$
\mathcal{L}_{\beta}(\boldsymbol{\mathcal{T}},\boldsymbol{\mathcal{S}})=\tilde{K}_{\text{top}}(\boldsymbol{\mathcal{T}},\boldsymbol{\mathcal{T}})+\tilde{K}_{\text{top}}(\boldsymbol{\mathcal{S}},\boldsymbol{\mathcal{S}})-2\tilde{K}_{\text{top}}(\boldsymbol{\mathcal{T}},\boldsymbol{\mathcal{S}}),
$$
\n(9)

where  $\tilde{K}_{top}(\cdot, \cdot)$  denotes the Gaussian kernel computed with only the most largest feature in each channel. The SADM loss is then formulated as a weighted sum of the two losses, given by

<span id="page-4-0"></span>
$$
\mathcal{L}_{\text{SADM}}(\mathcal{T}, \mathcal{S}) = \lambda_1 \mathcal{L}_{\alpha}(\mathcal{T}, \mathcal{S}) + \lambda_2 \mathcal{L}_{\beta}(\mathcal{T}, \mathcal{S}), \tag{10}
$$

where  $\lambda_1, \lambda_2$  are the weighting parameters. By using  $\mathcal{L}_{SADM}$  instead of  $\mathcal{L}_{MMD}$ , we facilitate reliable matching of feature distributions considering sematic structures of 3D point clouds, thereby improving the performance dataset distillation.

# 3.3 Estimation of Optimal Rotations

3D objects usually exhibit different orientations from each other. Therefore, while generating optimal 3D objects in the synthetic dataset in terms of their geometric shapes, we also estimate their optimal rotations best representing various orientations of 3D objects in the original dataset. In practice, we introduce three rotation angles,  $\theta_x$ ,  $\theta_y$ , and  $\theta_z$ , corresponding to rotations around the x-, y-, and z-axes, respectively. These angles are treated as learnable parameters, allowing the orientation of each synthetic 3D object in  $S$  to be adaptively adjusted during dataset distillation. Therefore, instead of minimizing  $\mathcal{L}_{SADM}(\mathcal{T},\mathcal{S})$  in [\(10\)](#page-4-0), we minimize  $\mathcal{L}_{SADM}(\mathcal{T},\hat{\mathcal{S}})$ , where  $\hat{\mathcal{S}}$  is the synthetic dataset whose 3D objects are rotated toward the estimated optimal orientations.

Note that we optimize the synthetic 3D objects as well as their rotation parameters simultaneously, during the dataset distillation process. Specifically, at each iteration, we randomly initialize the network parameters and construct the synthetic dataset  $\mathcal S$  by randomly selecting samples from the original dataset. We then form  $\hat{\mathcal{S}}$  by rotating the objects in  $\mathcal{S}$  according to the angles  $\theta_x$ ,  $\theta_y$ , and  $\theta_z$ , initially set to zero. For each class, a mini-batch is sampled from  $\tau$ , with a batch size of 8 per class. The corresponding synthetic mini-batch is sampled from  $\hat{S}$ , with the batch size determined by the number of point cloud objects per class (PPC). The joint optimization process iteratively updates both the geometric structure of the synthetic dataset and their rotation parameters by minimizing  $\mathcal{L}_{SADM}(\mathcal{T},\mathcal{S})$ . This ensures that the synthetic dataset preserves the geometric characteristics of the original dataset while aligning their orientations more effectively. The benefit of this joint optimization is justified in the following proposition.

**Proposition 1.** *Jointly optimizing the synthetic dataset* S and the rotation parameters  $\theta = (\theta_x, \theta_y, \theta_z)$ *guarantees a lower or equal loss to that of optimizing* S *alone.*

$$
\min_{\mathcal{S}, \theta} \mathcal{L}_{\text{SADM}}(\mathcal{T}, \mathcal{R}_{\theta}(\mathcal{S})) \le \min_{\mathcal{S}} \mathcal{L}_{\text{SADM}}(\mathcal{T}, \mathcal{S}), \tag{11}
$$

*where*  $\mathcal{R}_{\theta}$  *denotes the rotation operator according to*  $\theta$ *.* 

*Proof Sketch.* The proof is provided in Appendix A. Allowing optimization over both  $S$  and  $\theta$ enlarges the feasible set compared to optimizing only  $\mathcal S$ . The feasible set for the joint optimization includes the feasible set of the original optimization as a subset, since optimizing only  $\mathcal S$  is equivalent to the special case of the joint optimization with the fixed  $(\theta_x, \theta_y, \theta_z) = (0, 0, 0)$ . Consequently, the minimum loss achieved by jointly optimizing over  $S$  and  $\theta$  must be less than or equal to the minimum loss achieved by optimizing only  $S$ .

| ັ<br>ັ<br>in bold and underlined, respectively. |                                                  |                                                  |                                                  |                                                  |                                                   |                                                  |                                                  |                                                  |                                                  |                                                  |                                                    |                                                  |
|-------------------------------------------------|--------------------------------------------------|--------------------------------------------------|--------------------------------------------------|--------------------------------------------------|---------------------------------------------------|--------------------------------------------------|--------------------------------------------------|--------------------------------------------------|--------------------------------------------------|--------------------------------------------------|----------------------------------------------------|--------------------------------------------------|
| <b>Datasets</b>                                 | ModelNet10 [24]                                  |                                                  | ModelNet40 [24]                                  |                                                  | ShapeNet [4]                                      |                                                  |                                                  | ScanObjectNN [19]                                |                                                  |                                                  |                                                    |                                                  |
| <b>PPC</b><br>Ratio $(\%)$                      | 0.25                                             | 3<br>0.75                                        | 10<br>2.5                                        | 0.4                                              | 1.2                                               | 10<br>4.0                                        | 0.15                                             | 0.45                                             | 10<br>1.5                                        | 0.15                                             | 0.45                                               | 10<br>1.5                                        |
| Whole                                           |                                                  | 91.41                                            |                                                  |                                                  | 87.84                                             |                                                  |                                                  | 82.49                                            |                                                  |                                                  | 63.84                                              |                                                  |
| Random<br>Herding<br>K-center                   | $35.5 + 4.7$<br>$40.1 \pm 5.2$<br>$40.1 \pm 5.2$ | $75.2 + 1.7$<br>$78.0 \pm 1.3$<br>$77.6 \pm 1.9$ | $85.3 + 1.1$<br>$86.9 \pm 0.6$<br>$83.2 \pm 1.4$ | $34.6 + 1.8$<br>$54.4 \pm 2.0$<br>$54.4 \pm 2.0$ | $60.0 + 1.32$<br>$68.5 \pm 1.0$<br>$63.0 \pm 2.7$ | $74.1 + 0.4$<br>$78.8 \pm 0.4$<br>$65.3 \pm 1.1$ | $34.0 + 3.0$<br>$49.5 \pm 2.3$<br>$49.5 \pm 2.3$ | $54.8 + 1.5$<br>$59.8 \pm 0.9$<br>$51.4 \pm 1.7$ | $63.1 + 1.0$<br>$66.9 \pm 0.5$<br>$47.8 \pm 0.7$ | $13.9 + 1.4$<br>$15.7 \pm 1.7$<br>$15.7 \pm 1.7$ | $20.4 \pm 1.3$<br>$27.7 \pm 1.4$<br>$19.8 \pm 0.8$ | $34.8 + 1.1$<br>$38.7 \pm 1.7$<br>$24.0 \pm 1.0$ |
| <b>DM</b><br>DC                                 | $31.9 + 4.3$<br>$43.9 \pm 5.5$                   | $77.6 + 1.6$<br>$75.0 \pm 2.4$                   | $86.1 \pm 0.8$<br>$86.1 \pm 1.1$                 | $32.3 + 4.8$<br>$52.0 \pm 1.7$                   | $62.0 \pm 1.9$<br>$66.6 \pm 1.3$                  | $75.2 \pm 0.5$<br>$75.6 \pm 0.5$                 | $27.8 + 4.0$<br>$49.8 \pm 1.3$                   | $56.1 + 1.2$<br>$60.1 \pm 1.4$                   | $64.3 \pm 0.7$<br>$64.6 \pm 0.7$                 | $14.9 \pm 2.0$<br>$18.6 \pm 1.6$                 | $23.3 \pm 1.5$<br>$24.3 \pm 2.5$                   | $35.6 + 1.4$<br>$35.3 \pm 1.2$                   |
| Ours                                            | $44.7 + 6.1$                                     | $84.4 \pm 1.2$                                   | $87.8\pm1.0$                                     | $55.8 \pm 1.5$                                   | $72.1 \pm 0.8$                                    | $80.1 \pm 0.4$                                   | $50.2 \pm 1.4$                                   | $63.7 \pm 0.7$                                   | $68.4 \pm 0.5$                                   | $17.4 \pm 1.5$                                   | $31.6 \pm 1.0$                                     | $43.9 + 1.9$                                     |

<span id="page-5-0"></span>Table 1: Comparison of quantitative performance. DC, DM, and the proposed method were initialized with random selection [\[16\]](#page-9-12) for fair comparison. The best and the second best scores are highlighted

<span id="page-5-1"></span>Table 2: Comparison of cross-architecture generalization performance evaluated on PointNet++ [\[15\]](#page-9-11), DGCNN [\[22\]](#page-10-9), PointConv [\[23\]](#page-10-8), and PT [\[31\]](#page-10-11), respectively.

| <b>Datasets</b>           |                                  | ModelNet10 [24]                   |                                  |                                  | ModelNet40 [24]                  |                                              |    | ShapeNet [4]                                                                                  |                |                                                                                 | ScanObjectNN [19] |                                  |
|---------------------------|----------------------------------|-----------------------------------|----------------------------------|----------------------------------|----------------------------------|----------------------------------------------|----|-----------------------------------------------------------------------------------------------|----------------|---------------------------------------------------------------------------------|-------------------|----------------------------------|
| Method                    | DМ                               | DC                                | Ours                             | DМ                               | DC                               | Ours                                         | DМ | DC                                                                                            | Ours           | DМ                                                                              | DC                | Ours                             |
| PointNet++                | $35.0 \pm 8.3$                   | $51.5 \pm 7.8$                    |                                  | 82.9 $\pm$ 1.4   14.1 $\pm$ 2.7  | $46.9 \pm 6.0$                   |                                              |    | 68.5 $\pm$ 0.5   20.3 $\pm$ 3.7 34.3 $\pm$ 3.4 53.2 $\pm$ 1.5   15.5 $\pm$ 3.6 11.6 $\pm$ 2.2 |                |                                                                                 |                   | $22.0 \pm 3.9$                   |
| <b>DGCNN</b><br>PointConv | $56.6 \pm 5.3$<br>$33.4 \pm 6.2$ | $62.6 \pm 2.2$<br>$40.0 \pm 10.5$ | $79.0 \pm 2.2$<br>$56.6 \pm 4.9$ | $34.8 \pm 3.5$<br>$20.0 \pm 5.7$ | $52.3 \pm 2.7$<br>$37.9 \pm 3.9$ | 66.9 $\pm$ 1.1   13.8 $\pm$ 2.2              |    | 51.3 $\pm$ 6.2   17.4 $\pm$ 3.0 34.0 $\pm$ 4.8                                                | $47.4 \pm 2.3$ | $38.0 \pm 2.7$ $52.8 \pm 1.6$   $17.7 \pm 2.4$ $12.5 \pm 1.3$<br>$15.2 \pm 1.7$ | $14.2 \pm 1.8$    | $19.0 \pm 1.5$<br>$16.8 \pm 1.5$ |
| PТ                        | $48.5 \pm 6.6$                   | $61.8 \pm 1.6$                    |                                  | $77.6 \pm 1.6$   26.7 $\pm$ 5.0  |                                  | $47.5 \pm 2.7$ 61.6 $\pm$ 1.1 34.2 $\pm$ 5.3 |    |                                                                                               |                | $42.6 \pm 2.5$ 55.5 $\pm$ 0.5 17.1 $\pm$ 0.8                                    | $14.9 \pm 0.5$    | $21.9 \!\pm\! 2.7$               |

## 4 Experimental Results

## 4.1 Experimental Setup

The proposed method was evaluated on the ModelNet10 [\[24\]](#page-10-12), ModelNet40 [\[24\]](#page-10-12), ShapeNet [\[4\]](#page-9-17), and ScanObjectNN [\[19\]](#page-9-18) datasets. The ModelNet10, ModelNet40, and ShapeNet are synthetic datasets generated from CAD models, containing 10, 40, and 55 classes, respectively. The ScanObjectNN consists of real-world scanned objects from 15 classes. Following previous methods [\[29,](#page-10-1) [28\]](#page-10-2), we measure the classification accuracy trained on the distilled synthetic dataset. We ensure the fairness by training the network 10 times and report the mean and standard deviation of accuracy. We evaluate the performance across different PPC values of 1, 3, and 10, respectively. Each point cloud object contains 1,024 points, which is a common standard used in the 3D point clouds classification tasks [\[14,](#page-9-10) [15\]](#page-9-11). The dataset distillation process for the synthetic dataset was optimized for 1,500 iterations, where the performance was evaluated at every 250 iteration by training the PointNet on the synthetic dataset and testing it on the original test dataset.

## 4.2 Performance Comparison

## 4.2.1 Dataset Distillation

We compare the performance of the proposed method with that of 1) the three most representative coreset selection methods: random selection [\[16\]](#page-9-12), Herding [\[2,](#page-9-14) [1\]](#page-9-15), and K-Center [\[18\]](#page-9-13), 2) two existing image dataset distillation methods of DC [\[29\]](#page-10-1) and DM [\[28\]](#page-10-2), and 3) the only existing 3D point clouds dataset distillation method of PCC [\[27\]](#page-10-5). Table [1](#page-5-0) compares the quantitative performance of the proposed method with that of the existing methods. DM and DC were initialized with random selection. The synthetic datasets were optimized using PointNet [\[14\]](#page-9-10), and classification performance was evaluated on PointNet to demonstrate their validity. Among the coreset selection methods, Herding achieves the best overall performance. When PPC is set to 1, Herding and K-Center yield identical results since K-Center selects the first data point using the same algorithm to Herding. As PPC increases, Herding consistently outperforms other coreset selection methods, showing its effectiveness. However, since the coreset selection methods rely only on sample selection without further optimization, they struggle to capture complex feature distributions. The features used in DM are extracted from the layer before the classifier, and they discard substantial information through the global max pooling process of PointNet. This information loss weakens the effectiveness of the distribution matching, preventing DM from accurately capturing the feature distribution of the original dataset. DC, on the other hand, aims to match the gradients of the network. However, PointNet is significantly larger than typical networks in image-based distillation tasks, making it difficult for DC to precisely align the gradients. As a result, it consistently underperforms compared

<span id="page-6-0"></span>Table 3: Performance comparison across different ini-Table 4: Comparison of training time (in tialization strategies. The accuracies of three PPC hours) at the PPC value of 3. values of 1, 3, and 10 are averaged. MN10: Model-Net10. MN40: ModelNet40. SN: ShapeNet. SONN: ScanObjectNN.

| Init     | Method         | <b>MN10</b>  | <b>MN40</b>  | <b>SN</b>    | <b>SONN</b>  |
|----------|----------------|--------------|--------------|--------------|--------------|
| Noise    | DC             | 18.77        | 6.05         | 5.24         | 12.73        |
|          | DM             | 26.53        | 12.04        | 12.09        | 13.55        |
|          | Ours           | <b>70.69</b> | <b>65.25</b> | <b>58.44</b> | <b>29.03</b> |
| Random   | DC             | 68.32        | 64.71        | 58.15        | 26.07        |
|          | DM             | 65.17        | 56.50        | 49.39        | 24.57        |
|          | Ours           | <b>72.48</b> | <b>69.31</b> | <b>60.76</b> | <b>31.01</b> |
| K-center | DC             | 69.57        | 61.85        | 42.88        | 21.65        |
|          | DM             | 63.04        | 49.93        | 44.82        | 21.59        |
|          | Ours           | <b>72.72</b> | <b>68.20</b> | <b>60.70</b> | <b>28.25</b> |
| Herding  | $DC^{\dagger}$ | 71.50        | 66.79        | 59.23        | 26.98        |
|          | DM             | 64.76        | 59.16        | 52.61        | 27.58        |
|          | Ours           | <b>72.93</b> | <b>69.67</b> | <b>62.25</b> | <b>31.08</b> |

<span id="page-6-1"></span>

| <b>Method</b> | <b>MN10</b> | <b>MN40</b> | <b>SN</b>   | <b>SONN</b> |
|---------------|-------------|-------------|-------------|-------------|
| DC            | 1.52        | 5.43        | 7.57        | 2.19        |
| DM            | <b>0.04</b> | <b>0.11</b> | <b>0.15</b> | <b>0.05</b> |
| Ours          | <b>0.08</b> | <b>0.26</b> | <b>0.35</b> | <b>0.11</b> |

<span id="page-6-2"></span>Table 5: Ablation study of the proposed SADM loss. Experiments were performed at the PPC value of 3. "Random" denotes the results of the random selection [\[16\]](#page-9-12), and SA is the proposed semantic alignment.

|               | <b>MN10</b>  | <b>MN40</b>  | SN           | <b>SONN</b>  |
|---------------|--------------|--------------|--------------|--------------|
| Random        | 75.17        | 59.96        | 54.84        | 20.42        |
| w/o <b>SA</b> | 75.01        | 59.96        | 53.91        | 20.20        |
| w/ <b>SA</b>  | <b>84.42</b> | <b>72.08</b> | <b>63.74</b> | <b>31.84</b> |

† DC [\[29\]](#page-10-1) with Herding initialization corresponds to PCC [\[27\]](#page-10-5)

to the proposed method. The proposed method outperforms both the coreset selection and existing distillation techniques. When the PPC is small, accurately capturing the original distribution is challenging, leading less noticeable improvement. As the PPC increases, the proposed method more effectively matches the feature distributions significantly improving the performance.

#### 4.2.2 Cross-Architecture Generalization

We also present the cross-architecture generalization performance in Table [2,](#page-5-1) where the synthetic dataset is optimized using PointNet [\[14\]](#page-9-10) and evaluated on different backbone networks including PointNet++[\[15\]](#page-9-11), DGCNN[\[22\]](#page-10-9), PointConv [\[23\]](#page-10-8), and PT [\[31\]](#page-10-11), respectively. Unlike PointNet, the architectures used for generalization performance comparison follow hierarchical structure similar to CNN-based networks in image processing, exhibiting significantly different characteristics from that of PointNet. This difference makes it essential for the distilled dataset to retain rich and transferable features that generalize well toward different architectures. Due to significant information loss caused by the pooling operation, DM primarily captures the features specific to the PointNet, failing to preserve the original feature distribution. Consequently, the synthetic dataset lacks diverse structural details, resulting in poor generalization performance, particularly on hierarchical models. DC, while performing better than DM, also suffers from key limitations. It matches the gradients obtained from a trained PointNet, the synthetic dataset becomes overfitted to that specific architecture and fails to generalize toward other networks. Consequently, the generalization ability of DC degrades when applied to other architectures. In contrary, the proposed method addresses these limitations by directly matching the feature distributions extracted from randomly initialized network. By comparing feature maps containing richer and less architecture-biased information, the synthetic dataset captures the features relevant to the PointNet while following the original data distribution more closely. Wherease DC suffers from the overfitting and DM loses critical information, the proposed method yields higher adaptability showing strong generalization performance across various backbone networks.

#### 4.2.3 Initialization Strategies

Table [3](#page-6-0) presents the classification performance on PointNet for DC [\[29\]](#page-10-1), DM [\[28\]](#page-10-2), and PCC [\[27\]](#page-10-5) under different initialization strategies, including uniform noise, random selection [\[16\]](#page-9-12), Herding [\[2,](#page-9-14) [1\]](#page-9-15), and K-Center [\[18\]](#page-9-13). Note that the results of PCC are obtained by our implementation, where the PCC is equivalent to performing DC with Herding initialization. When initialized with noise, DC fails to effectively optimize the synthetic dataset because the network has far more parameters than typical networks used for image dataset distillation, making it difficult to align the gradients. Therefore, the original dataset cannot properly guide the training process, leading to poor convergence. In contrast, the structured initializations such as random selection, Herding, or K-Center improve the

Image /page/7/Figure/0 description: The image displays a grid of 3D point cloud models of various objects, categorized by type. The categories, listed at the top, are Airplane, Bathtub, Bed, Car, Guitar, Keyboard, Lamp, and Monitor. Each category has a row of smaller point cloud models at the top, followed by four rows of larger, more detailed point cloud models of the same object type. The point clouds are colored with a gradient from dark blue to yellow, suggesting depth or some other attribute. The overall presentation is a comparative visualization of different object reconstructions or representations.

<span id="page-7-0"></span>Figure 3: Comparison of synthetic datasets distilled from the ModelNet40 [\[24\]](#page-10-12) dataset. Point clouds were colorized according to the y-coordinates. From top to bottom, the orginal dataset (first), initialized point cloud models (second), and the distilled synthetic datasets by using DC [\[29\]](#page-10-1) (third), DM [\[28\]](#page-10-2) (fourth), and the proposed method (fifth).

performance, where the Herding performs the best by selecting the most representative samples. Unlike DC, DM directly matches the feature representations rather than relying on the gradients and hence less sensitive to the initialization strategies. However, due to the information loss, DM consistently underperforms DC even with the structured initializations. In contrast, the proposed method achieves favorable performance, even when initialized from uniform noise. Furthermore, the proposed method significantly outperforms other approaches under the heuristic initialization strategy, such as random selection. This demonstrates that the proposed method is robust with diverse initialization schemes and effectively resolves the instability issues inherent in the existing dataset distillation methods.

## 4.2.4 Training Time

Table [4](#page-6-1) compares the training times between the proposed method and the existing methods. DM [\[28\]](#page-10-2) achieves the fastest training time as it uses only a small subset of the overall features, however it often ignores many useful features leading to performance degradation. In contrast, DC [\[29\]](#page-10-1) incurs a significantly higher computational cost as it aligns the gradients across all the parameters. Notably, for the largest dataset of ShapeNet [\[4\]](#page-9-17), DC requires over 7 hours for training 50 times slower that DM that completes the training in just 9 minutes (0.15 hours). Since the proposed method is based on the distribution matching approach, its training time is marginally increased compared to DM, yet achieves significant improvement in dataset distillation performance.

### 4.2.5 Qualitative Comparison of Synthetic Datasets

Figure [3](#page-7-0) compares the resulting synthetic datasets distilled by using the proposed method and the existing methods, respectively. The first row shows the original datasets and the second row shows initialized point cloud objects. DC [\[29\]](#page-10-1) fails to deviate significantly from the initialized objects and causes noise. Similarly, DM [\[28\]](#page-10-2) also maintains the original shape of the initialized objects while shifting certain points only, that hinders to capture meaningful structural changes. In contrast, as shown in the bottom row, the proposed method successfully preserves the overall semantic structures of 3D objects in each class, while selectively learning essential features. For example, in the airplane class, we observe significant changes in the edges and corners, that are critical for class discrimination, especially around the wings. Also, in the guitar class, the proposed method jointly optimizes the shape and orientation of the synthesized 3D objects.

## 4.3 Ablation Study

### 4.3.1 Effect of SADM Loss

Table [5](#page-6-2) presents the ablation study on the impact of the proposed SADM loss on the classification performance using PointNet [\[14\]](#page-9-10). Without preserving the semantic alignment, the unordered nature of point clouds restricts effective feature matching, resulting in nearly identical performance to that of the random selection [\[16\]](#page-9-12). That means the model has not been properly optimized. Otherwise, when the semantic alignment is applied, accuracy improves across all datasets, as the aligned featured provide consistent and structured representation that facilitates more effective feature matching during the optimization. This result confirms that preserving the inherent semantic alignment via feature sorting facilitates the network to exploit well-aligned corresponding features and significantly improves the classification performance.

### 4.3.2 Effect of Optimal Rotation Estimation

Table [6](#page-8-0) presents the ablation study to evaluate the impact of joint optimization of synthetic dataset and rotation angles. We categorized the test datsets into the aligned, mixed, and rotated groups to analyze the effectiveness of the proposed rotation estimation. The aligned group consists of the datasets where the objects maintain consistent orientations, including ModelNet10 and ShapeNet [\[4\]](#page-9-17). The mixed group includes Model-Net40 [\[24\]](#page-10-12) where only certain classes exhibit rotation variations. The rotated group is ScanObjectNN [\[19\]](#page-9-18) where the objects exhibit arbitrary rotations across all classes. The baseline (first row) shows the performance

<span id="page-8-0"></span>Table 6: Ablation study of the proposed optimal rotation estimation. Experiments were performed at the PPC value of 3.

| <b>S</b> | <b>θ</b> | <b>Aligned</b> | <b>Mixed</b> | <b>Rotated</b> |
|----------|----------|----------------|--------------|----------------|
| -        | -        | 65.01          | 59.96        | 20.42          |
| -        | ✓        | 66.22          | 62.17        | 24.89          |
| ✓        | -        | 73.96          | 71.51        | 29.72          |
| ✓        | ✓        | <b>74.35</b>   | <b>72.08</b> | <b>31.84</b>   |

without optimization which is identical to that of the random selection. In the second row, we present the results where only the orientation of the synthetic data is optimized using the proposed SADM loss, without updating the synthetic dataset. This leads to performance improvements across all the groups compared to random selection. The improvement becomes more significant as the classes with rotation variation become more dominant. Optimizing the synthetic dataset further boosts the performance, with the best results achieved when both the synthetic data and rotation angles are optimized simultaneously. In particular, the accuracy on the rotated dataset improves significantly, demonstrating that the proposed joint optimization enables the model to be resilient to rotation variations.

### 4.4 Limitation

While the proposed SADM loss enhances the semantic consistency by sorting the features, it does not completely align semantically meaningful regions across different point cloud objects. Estimating optimal rotations also improves the complexity, particularly when the datasets are already well-aligned to the canonical axes.

# 5 Conclusion

We proposed a semantically-aligned and orientation-aware dataset distillation framework for 3D point clouds. To alleviate the inconsistency of point ordering between compared 3D objects, we devised a Semantically Aligned Distribution Matching loss where the sorted features in each channel are compared. We also employed learnable parameters of the rotation angles to estimate the optimal orientation of synthetic objects. The geometric structures and orientations of the synthetic objects are jointly optimized during the dataset distillation. Experimental results evaluated on the four widely used benchmark datasets of ModelNet10 [\[24\]](#page-10-12), ModelNet40 [\[24\]](#page-10-12), ShapeNet [\[4\]](#page-9-17), and ScanObjectNN [\[19\]](#page-9-18) demonstrate that the proposed method outperforms existing distillation methods while maintaining strong cross-architecture generalization. Furthermore, we also validated the effectiveness of each component of the proposed method through extensive ablation studies.

# References

- <span id="page-9-15"></span>[1] Eden Belouadah and Adrian Popescu. Scail: Classifier weights scaling for class incremental learning. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, 2020.
- <span id="page-9-14"></span>[2] Francisco M Castro, Manuel J Marín-Jiménez, Nicolás Guil, Cordelia Schmid, and Karteek Alahari. End-to-end incremental learning. In *Proceedings of the European Conference on Computer Vision*, 2018.
- <span id="page-9-0"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022.
- <span id="page-9-17"></span>[4] Angel X Chang, Thomas Funkhouser, Leonidas Guibas, Pat Hanrahan, Qixing Huang, Zimo Li, Silvio Savarese, Manolis Savva, Shuran Song, Hao Su, et al. Shapenet: An information-rich 3d model repository. 2015.
- <span id="page-9-5"></span>[5] Wenxiao Deng, Wenbin Li, Tianyu Ding, Lei Wang, Hongguang Zhang, Kuihua Huang, Jing Huo, and Yang Gao. Exploiting inter-sample and inter-feature relations in dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2024.
- <span id="page-9-1"></span>[6] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2023.
- <span id="page-9-16"></span>[7] Arthur Gretton, Karsten M Borgwardt, Malte J Rasch, Bernhard Schölkopf, and Alexander Smola. A kernel two-sample test. *The Journal of Machine Learning Research*, 13(1):723–773, 2012.
- <span id="page-9-2"></span>[8] Ziyao Guo, Kai Wang, George Cazenavette, HUI LI, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *In International Conference on Learning Representations*, 2024.
- <span id="page-9-6"></span>[9] Hongcheng Li, Yucan Zhou, Xiaoyan Gu, Bo Li, and Weiping Wang. Diversified semantic distribution matching for dataset distillation. In *Proceedings of the ACM International Conference on Multimedia*, 2024.
- <span id="page-9-7"></span>[10] Yongqi Li and Wenjie Li. Data distillation for text classification. *arXiv preprint arXiv:2104.08448*, 2021.
- <span id="page-9-3"></span>[11] Dai Liu, Jindong Gu, Hu Cao, Carsten Trinitis, and Martin Schulz. Dataset distillation by automatic training trajectories. In *Proceedings of the European Conference on Computer Vision*. Springer, 2024.
- <span id="page-9-8"></span>[12] Aru Maekawa, Naoki Kobayashi, Kotaro Funakoshi, and Manabu Okumura. Dataset distillation with attention labels for fine-tuning bert. In *Proceedings of the Annual Meeting of the Association for Computational Linguistics (ACL)*, pages 119–127, 2023.
- <span id="page-9-9"></span>[13] Aru Maekawa, Satoshi Kosugi, Kotaro Funakoshi, and Manabu Okumura. Dilm: Distilling dataset into language model for text-level dataset distillation. In *Proceedings of the Annual Conference of the North American Chapter of the Association for Computational Linguistics (NAACL)*, pages 3138–3153, 2024.
- <span id="page-9-10"></span>[14] Charles R Qi, Hao Su, Kaichun Mo, and Leonidas J Guibas. Pointnet: Deep learning on point sets for 3d classification and segmentation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2017.
- <span id="page-9-11"></span>[15] Charles Ruizhongtai Qi, Li Yi, Hao Su, and Leonidas J Guibas. Pointnet++: Deep hierarchical feature learning on point sets in a metric space. In *Proceedings of the Advances in Neural Information Processing Systems*, 2017.
- <span id="page-9-12"></span>[16] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. icarl: Incremental classifier and representation learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2017.
- <span id="page-9-4"></span>[17] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2023.
- <span id="page-9-13"></span>[18] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *In International Conference on Learning Representations*, 2018.
- <span id="page-9-18"></span>[19] Mikaela Angelina Uy, Quang-Hieu Pham, Binh-Son Hua, Duc Thanh Nguyen, and Sai-Kit Yeung. Revisiting point cloud classification: A new benchmark dataset and classification model on real-world data. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2019.

- <span id="page-10-10"></span>[20] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez, Łukasz Kaiser, and Illia Polosukhin. Attention is all you need. In *Proceedings of the Advances in Neural Information Processing Systems*, 2017.
- <span id="page-10-0"></span>[21] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-10-9"></span>[22] Yue Wang, Yongbin Sun, Ziwei Liu, Sanjay E Sarma, Michael M Bronstein, and Justin M Solomon. Dynamic graph cnn for learning on point clouds. *In TRANSACTIONS ON GRAPHICS*, 2019.
- <span id="page-10-8"></span>[23] Wenxuan Wu, Zhongang Qi, and Li Fuxin. Pointconv: Deep convolutional networks on 3d point clouds. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2019.
- <span id="page-10-12"></span>[24] Zhirong Wu, Shuran Song, Aditya Khosla, Fisher Yu, Linguang Zhang, Xiaoou Tang, and Jianxiong Xiao. 3d shapenets: A deep representation for volumetric shapes. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2015.
- <span id="page-10-6"></span>[25] Tan Yu, Jingjing Meng, and Junsong Yuan. Multi-view harmonized bilinear network for 3d object recognition. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2018.
- <span id="page-10-4"></span>[26] Hansong Zhang, Shikun Li, Pengju Wang, Dan Zeng, and Shiming Ge. M3d: Dataset condensation by minimizing maximum mean discrepancy. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 38, 2024.
- <span id="page-10-5"></span>[27] Wenxiao Zhang, Ziqi Wang, Li Xu, Xun Yang, and Jun Liu. Informative point cloud dataset extraction for classification via gradient-based points moving. In *Proceedings of the ACM International Conference on Multimedia*, 2024.
- <span id="page-10-2"></span>[28] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, 2023.
- <span id="page-10-1"></span>[29] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *In International Conference on Learning Representations*, 2021.
- <span id="page-10-3"></span>[30] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2023.
- <span id="page-10-11"></span>[31] Hengshuang Zhao, Li Jiang, Jiaya Jia, Philip HS Torr, and Vladlen Koltun. Point transformer. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2021.
- <span id="page-10-7"></span>[32] Yin Zhou and Oncel Tuzel. Voxelnet: End-to-end learning for point cloud based 3d object detection. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2018.
# DATASET CONDENSATION WITH LATENT SPACE KNOWLEDGE FACTORIZATION AND SHARING

Ha<PERSON> $^{1*}$ , <PERSON> $^{1*}$ , <PERSON> $^{1,2}$  $KAIST<sup>1</sup>$ , AITRICS<sup>2</sup>, South Korea {haebeom.lee, markhi, sjhwang82}@kaist.ac.kr

## ABSTRACT

In this paper, we introduce a novel approach for systematically solving dataset condensation problem in an efficient manner by exploiting the regularity in a given dataset. Instead of condensing the dataset directly in the original input space, we assume a generative process of the dataset with a set of learnable *codes* defined in a compact latent space followed by a set of tiny *decoders* which maps them differently to the original input space. By combining different codes and decoders interchangeably, we can dramatically increase the number of synthetic examples with essentially the same parameter count, because the latent space is much lower dimensional and since we can assume as many decoders as necessary to capture different styles represented in the dataset with negligible cost. Such knowledge factorization allows efficient sharing of information between synthetic examples in a systematic way, providing far better trade-off between compression ratio and quality of the generated examples. We experimentally show that our method achieves new state-of-the-art records by significant margins on various benchmark datasets such as SVHN, CIFAR10, CIFAR100, and TinyImageNet.

# 1 INTRODUCTION

Deep learning has been successful in numerous machine learning problems thanks to the recent progress in parallel processing and the huge amount of real-world data collected from various sources. However, in some machine learning applications it is required to rehearse the training process repeatedly, such as hyperparameter optimization [\(Bengio,](#page-7-0) [2000;](#page-7-0) [Franceschi et al.,](#page-7-1) [2017\)](#page-7-1), neural architecture search [\(Liu et al.,](#page-8-0) [2018\)](#page-8-0) and continual learning [\(Lopez-Paz & Ranzato,](#page-8-1) [2017\)](#page-8-1). In those cases, it is prohibitive to keep and rehearse all the examples in huge datasets, giving rise to the need for compressing each dataset into a small set of representative examples. The conventional approaches resort to selecting a coreset [\(Phillips,](#page-8-2) [2016;](#page-8-2) [Toneva et al.,](#page-8-3) [2018;](#page-8-3) [Borsos et al.,](#page-7-2) [2020\)](#page-7-2), but their assumption is limited as there may not exist strongly representative examples in the original dataset [\(Zhao & Bilen,](#page-8-4) [2021\)](#page-8-4) and also suffer from the difficulties of combinatorial optimizations [\(Borsos et al.,](#page-7-2) [2020\)](#page-7-2). Recently, more popular approaches focus on *dataset condensation* [\(Wang](#page-8-5) [et al.,](#page-8-5) [2018;](#page-8-5) [Zhao et al.,](#page-8-6) [2021\)](#page-8-6) which directly parameterize and optimize the synthetic dataset with gradient descent. They have shown to perform well compared to coreset selection approaches and generate plausibly looking examples as well.

However, despite its great potential to other adjacent fields, most of the existing dataset condensation methodologies do not focus on exploiting the regularity in the dataset, such as what the underlying data generating process would be. They usually parameterize a set of synthetic examples directly in input space and minimize a distance between real and synthetic dataset in either parameter [\(Zhao](#page-8-6) [et al.,](#page-8-6) [2021\)](#page-8-6) or feature space [\(Zhao & Bilen,](#page-8-7) [2022\)](#page-8-7). Such lack of assumptions on the underlying data generating process prevents them from efficiently sharing knowledge among the synthetic examples. Therefore, those approaches may require much larger space consumption to capture the same amount of information compared to when we can properly organize the data generating process. This intuition gives rise to questions about whether we can further improve the efficiency in solving the problem by exploiting the regularity in the given dataset.

<sup>∗</sup> : Equal Contribution

Image /page/1/Figure/1 description: The image illustrates a process involving latent codes and decoders to generate synthetic examples. On the left, it shows multiple planes labeled 'Directly parameterizing in input space', each with a grid pattern and different colored diagonal stripes. In the center, there are several rectangular blocks representing 'Latent codes', connected by lines to multiple trapezoidal shapes labeled 'Decoders', which have various checkered patterns. An arrow points to the right, leading to multiple planes labeled 'Synthetic examples generated', similar to the input planes but with different colored diagonal stripe patterns.

<span id="page-1-0"></span>Figure 1: (Left) Conventional dataset condensation approaches that directly parameterize the synthetic examples in input space. (Right) Our approach that parameterize latent codes and multiple decoders that can generate much more number of examples with essentially the same parameter count.

To this end, we naturally assume that each datapoint is generated from a *code* in a compact latent space followed by a sharable mapping function from that space to the original input space (i.e. *decoder*), both of which are learned in an end-to-end manner. In this way, we can dramatically increase the number of generated synthetic examples with the same parameter count as the latent space can be much lower dimensional than the original input space, and also since we can assume as many decoders as appropriate to capturing various *styles* represented in the dataset. See Figure [1](#page-1-0) for the concept. Note that the additional per-class parameter count introduced by the decoders is negligible because those decoders are very tiny and can be shared across all the synthetic examples and classes. Furthermore, the knowledge is shared among the generated synthetic examples through the shared latent space and the set of shared decoders applicable to any latent codes interchangeably, dramatically improving the trade-off between compression ratio and quality of the synthetic dataset. To our knowledge, this paper is first to successfully employ such latent space approach to systematically solving the dataset condensation problem.

In algorithmic side, we show that the previous approaches that subsample synthetic examples for computational efficiency (e.g. gradient matching [\(Zhao et al.,](#page-8-6) [2021\)](#page-8-6) or distribution matching [\(Zhao](#page-8-7) [& Bilen,](#page-8-7) [2022\)](#page-8-7)) actually produce biased gradients, preventing the synthetic examples from being diversified. We further experimentally observe that subsampling of the real examples produce highvariance gradients, degrading the quality of the generated synthetic examples. Those observations indicate that we have to perform full batch training for both real and synthetic dataset. We thus adopt distribution matching [\(Zhao & Bilen,](#page-8-7) [2022\)](#page-8-7) as it does not require expensive second order computations unlike gradient matching [\(Zhao et al.,](#page-8-6) [2021\)](#page-8-6). We also prestore and reuse some of the information needed to perform distribution matching for further improving the training efficiency.

We experimentally show that our method, which we name as Knowledge Factorization and Sharing (KFS), achieves new state-of-the-art records on various benchmark datasets such as SVHN, CI-FAR10, and CIFAR100 and TinyImageNet with different amount of parameter count allowed (e.g. 1, 10, or 50 images per class). We also show that the synthetic examples generated from KFS produce decent performances on other network architectures than what they have been trained on (e.g. from ConvNet-3 to DenseNet-121), and it provides superior performance over various amount of training budget at evaluation time without overfitting. We summarize the contribution of this paper as follows:

- We introduce KFS, a novel approach for solving dataset condensation problem that factorizes the synthetic data generating process into a set of learnable latent codes and decoders, dramatically increasing the number of synthetic examples with a limited parameter count.
- We show that the assumed generative process effectively allows knowledge sharing between the synthetic examples such that they achieve far better trade-off between the compression ratio versus the quality of the generated examples.
- We further show that the existing dataset condensation approaches suffer from the issue of biased and high-variance gradient, and experimentally verify that full batch training significantly improves the quality of the synthetic dataset.
- The proposed KFS achieves new state-of-the-art records in all datasets we considered, outperforming the previous state-of-the-art by significant margins.

# 2 RELATED WORK

Dataset condensation. [Wang et al.](#page-8-5) [\(2018\)](#page-8-5) firstly introduced a differentiable approach to dataset condensation problem through a bilevel optimization formulation. However, in their work only a few

inner-gradient steps are assumed for computational efficiency and additionally a shared initialization is learned and entangled with the learned data. [Zhao et al.](#page-8-6) [\(2021\)](#page-8-6) overcome the limitation by trying to match the network weights and approximating the expensive bi-level optimization with shorthorizon approximation, resulting in gradient matching between real and synthetic examples for each step. [Zhao & Bilen](#page-8-4) [\(2021\)](#page-8-4) further develop the idea by learning a differentiable siamese networks for augmenting the synthetic examples. [Cazenavette et al.](#page-7-3) [\(2022\)](#page-7-3) recently propose to match much longer segments of learning trajectories, yielding competitive performances by guiding networks to a more similar state. On the other hand, instead of matching the network weights, Zhao  $\&$  Bilen [\(2022\)](#page-8-7) match distributions of real and synthetic dataset in a feature space. Although they sometimes show infereior performance to gradient matching, their approach is computationally more efficient as they do not require to compute expensive second-order derivatives such as vector-Jacobian products (VJPs) required by gradient matching. Simiilarly, [Wang et al.](#page-8-8) [\(2022\)](#page-8-8) propose to layer-wisely align features with auxiliary discriminative loss and bi-level formulation for adjusting parameter updates. On the other hand, [Nguyen et al.](#page-8-9) [\(2020;](#page-8-9) [2021\)](#page-8-10) propose to make use of the connection between infinitely wide neural networks and kernel ridge regression, but their approach requires hundreds of GPUs for training. Perhaps the most relevant to our work is recently proposed IDC [\(Kim et al.,](#page-7-4) [2022\)](#page-7-4). They generate various synthetic data from a single set of condensed examples by segmenting and upscaling the condensed examples. Their approach can be seen as exploiting the regularity in the dataset, for instance, spatially nearby pixels look similar to each other. Although they achieve strong performances on many datasets, it is questionable whether the proposed generative process is sophisticated enough to fully exploit the regularity in the dataset. In this work, we make a more natural and general assumption on the data generating process, yielding new state-of-the-art records on all the datasets we consider.

**Generative models.** Our work is closely related to generative models such as VAE (Kingma  $\&$ [Welling,](#page-8-11) [2013\)](#page-8-11) and GAN [\(Goodfellow et al.,](#page-7-5) [2014\)](#page-7-5). They maximize the likelihood of data by learning a compact latent space and a shared decoder, similarly to our work. However, it is questionable whether maximizing the data likelihood would be sufficient for the target task such as classification. Also, whereas it has been shown that modality is crucial for the performance of condensed dataset [\(Kim et al.,](#page-7-4) [2022\)](#page-7-4), they are known to suffer from the posterior collapse (or mode collapse) problem [\(Razavi et al.,](#page-8-12) [2019a\)](#page-8-12). [Van Den Oord et al.](#page-8-13) [\(2017\)](#page-8-13) and [Razavi et al.](#page-8-14) [\(2019b\)](#page-8-14) overcome the issue by proposing VQ-VAE that models discrete representations in the latent space. The discreteness of the approach makes them closely related to the dataset condensation, but it is questionable whether such generative models can outperform the dataset condensation approaches recently introduced. For instance, [Such et al.](#page-8-15) [\(2020\)](#page-8-15) combine GAN with meta-learning to generate synthetic examples, but they only slightly outperform [Wang et al.](#page-8-5) [\(2018\)](#page-8-5).

Object files and schemata. Our approach is also closely related to object files and schemata discussed in [Goyal & Bengio](#page-7-6) [\(2020\)](#page-7-6); [Goyal et al.](#page-7-7) [\(2021b;](#page-7-7)[a\)](#page-7-8). Object files typically refer to the state of each object (e.g. states in RL or an object in an image) and schemata determine how they behave and update the object files correspondingly (e.g. actions in RL or styles in images). By carefully factorizing those two different types of knowledge, the knowledge structure becomes more efficient as we can apply the same schema to various objects, and more effectively generalizable to out-ofdistribution because the architecture has better *systematicity*. Also, the interactions between object files and schemata are inherently sparse, i.e. in real-world scenarios, explaining a phenomenon usually requires only a few rules given a set of objects. In our case, a single pair of object file and schema is assumed to be sufficient to generate a single synthetic image.

# 3 APPROACH

We next present our approach, Knowledge Factorization and Sharing (KFS), that efficiently factorize the data generating process into latent codes and decoders so that knowledge is effectively shared among the synthetic examples generated.

<span id="page-2-0"></span>

## 3.1 KNOWLEDGE FACTORIZATION AND SHARING IN LATENT SPACE

It was observed by [Kim et al.](#page-7-4) [\(2022\)](#page-7-4) that what determines the performance of condensed dataset is its modality rather than resolution. We follow the same principle but we want to find even better trade-off between increasing the modality of the dataset and preserving or even improving the quality of generated images.

Latent code - decoder factorization. Our approach starts from assuming that the datapoints lie in a compact latent space whose dimension can be much lower than that of the actual input space. Thanks to its lower dimensionality, we can dramatically increase the number of datapoints given a limited parameter count (e.g. the number of parameters used by synthetic images per each class). We can also preserve the quality of the synthetic images to the extent to which the assumption holds, which has been proven to be generally the case. The only space overhead is additional parameters for a tiny decoder that maps those latent codes into the input space. Note that this decoder can be shared across all the datapoints and classes, and even if the decoder is relatively big we can flexibly adjust the number of latent codes so that the total parameter counts are comparable.

Let us take an illustrative example. Suppose we want to condense a dataset consisting of images of shape  $3\times32\times32 = 3072$  (e.g. SVHN or CIFAR10). Instead of learning those 3072 parameters in the pixel space, we instead learn much smaller codes in, say  $12 \times 4 \times 4 = 192$  dimensional latent space and decode them back to the original pixel space with a shared decoder. Given the same parameter count, this allows us to generate 16 times more number of images than the existing approaches  $(192 \times 16 = 3072)$ . Note that the per-class parameter count of shared decoder is negligible. In the above example, we could use a tiny 3-layer deconvolutional architecture with channels decreasing as  $12 \rightarrow 9 \rightarrow 6 \rightarrow 3$  and  $2 \times 2$  kernel, resulting in only 738 parameters in total. Since this decoder can be shared across all classes, dividing it with the number of classes (e.g. 10) results in a negligible amount of per-class parameter count (e.g. 73.8) compared to the actual image size.

Sharing various styles with multiple decoders. The above factorization means that now we decouple the compressed expression of the instances from how to decode them back. This decoding process can be thought of as adding details to the latent codes so that the decoded instances have sufficient information in the original input space. For instance, given a compressed expression of a car, the decoder may add in or supplement background details or colors to complete an image.

This insight implies that the number of decoders can be as many as how various *styles* exists in the actual dataset. For instance, given the same compressed expression of an airplane, the background may be ocean, sky, forest, or sunset. By assuming various decoders, we could expect each of the decoders to explain each of those distinct styles in the dataset respectively. Furthermore, since the knowledge about how to add styles to the latent codes can be shared across all instances and classes, we can dramatically increase the number of synthetic images without increasing parameter count too much, similarly to cartesian product between a pair of sets. For instance, if there are 8 shared decoders and 16 latent codes for each of 10 classes, we can generate total  $8 \times 16 = 128$  synthetic images with only around 3131 parameter count for each class, which is less than 2% increase from the parameter count of a single image  $3 \times 32 \times 32 = 3072$ .

## 3.2 TRAINING WITH UNBIASED AND LOW-VARIANCE DISTRIBUTION MATCHING

Basically, our approach is compatible with any learning-based dataset condensation approaches that allows to differentiate w.r.t latent codes and decoder parameters by backpropagating through the generated examples, such as gradient matching [\(Zhao et al.,](#page-8-6) [2021\)](#page-8-6) or distribution matching [\(Zhao &](#page-8-7) [Bilen,](#page-8-7) [2022\)](#page-8-7). In this paper, we propose to use distribution matching as it does not require expensive vector-Jacobian products (VJPs) computations whereas gradient matching requires to compute VJP for every training step.

**Distribution matching.** Here we introduce notations for further discussion. We denote a set of  $N$ real examples for each class  $c = 1, \ldots, C$  as  $\mathcal{X}_c = \{x_{c,1}, \ldots, x_{c,N}\}\$ . Similarly, a set of M latent codes for each class  $c$  is  $\Theta_c = \{\theta_{c,1}, \dots, \theta_{c,M}\}\$  and we collect them into  $\Theta = \{\Theta_1, \dots, \Theta_C\}$ . Note that the dimension of  $\theta$  is much lower than that of x.  $g(x)$  is a shared feature extractor taking x as an input.  $f(\theta; \phi_d)$  is d-th decoder parameterized by  $\phi_d$  that takes a latent code  $\theta$  as an input. We assume there are D decoders and collect their parameters into  $\phi = {\phi_1, \ldots, \phi_D}$ . With these notations, we consider the following empirical MMD loss [\(Gretton et al.,](#page-7-9) [2012;](#page-7-9) [Zhao & Bilen,](#page-8-7) [2022\)](#page-8-7).

<span id="page-3-0"></span>
$$
L(\Theta, \phi) = \frac{1}{C} \sum_{c=1}^{C} \frac{1}{2} \left\| \frac{1}{N} \sum_{n=1}^{N} g(x_{c,n}) - \frac{1}{DM} \sum_{d=1}^{D} \sum_{m=1}^{M} g(f(\theta_{c,m}; \phi_d)) \right\|_{2}^{2}
$$
(1)

Following [Zhao & Bilen](#page-8-7) [\(2022\)](#page-8-7), we sample  $g(\cdot)$  from random initializations rather than from a set of pretrained networks, which is computationally more efficient and works well in practice.

**Bias due to subsampling of synthetic examples.** [Zhao & Bilen](#page-8-7) [\(2022\)](#page-8-7) further approximate the first term (i.e. mean of  $g(x)$  over n) and the second term (i.e. mean of  $g(f(\theta; \phi))$  over d and m) by random subsampling for computational efficiency. However, whereas the gradient  $\nabla_{\Theta,\phi}L(\Theta,\phi)$ is unbiased w.r.t the random subsampling of indices of real data  $\{n\}_{n=1}^{N}$  or classes  $\{c\}_{c=1}^{C}$ , the gradient is actually biased w.r.t the random subsampling of indices of latent codes  $\{m\}_{m=1}^{\overline{M}}$  or decoders  $\{d\}_{d=1}^D$ . Specifically, suppose for simplicity we randomly sample a single pair of  $\overline{m} \sim$ Uniform(1, M) and  $d \sim$  Uniform(1, D) and denote the corresponding loss as  $\tilde{L}$ . Then we can easily compute the bias of gradient  $\mathbb{E}_{m,d}[\nabla_{\Theta,\phi}\hat{L}(\Theta,\phi)] - \nabla_{\Theta,\phi}L(\Theta,\phi)$  as

<span id="page-4-0"></span>
$$
\nabla_{\Theta,\phi} \frac{1}{C} \sum_{c=1}^{C} \frac{1}{2} \Bigg\{ \frac{1}{DM} \sum_{d=1}^{D} \sum_{m=1}^{M} g(f(\theta_{c,m}; \phi_d))^{\mathsf{T}} g(f(\theta_{c,m}; \phi_d)) - \frac{1}{D^2 M^2} \sum_{d=1}^{D} \sum_{d'=1}^{D} \sum_{m=1}^{M} \sum_{m'=1}^{M} g(f(\theta_{c,m}; \phi_d))^{\mathsf{T}} g(f(\theta_{c,m'}; \phi_{d'})) \Bigg\}.
$$
(2)

See Appendix  $\overline{B}$  $\overline{B}$  $\overline{B}$  for the derivation. The above result suggests that the bias comes from ignoring the interactions within the latent codes and within the decoders, respectively. The biased gradient fails to minimize the inner product of the embeddings computed from different codes and decoders, such as  $g(f(\theta_{c,m};\phi))^T g(f(\theta_{c,m'};\phi))$  and  $g(f(\theta_{c,m};\phi_d))^T g(f(\theta_{c,m};\phi_{d'}))$ , preventing the codes and decoders from being diversified. Intuitively, if we sample  $\theta$ , then each individual  $\theta \in \Theta_c$  will try to explain the whole class set  $\mathcal{X}_c$  at a time without considering the existence of other codes  $\theta' \in \Theta_c \setminus \{\theta\}$  due to the sampling. The same intuition holds for sampling the decoder  $f(\cdot;\phi_d)$ . Therefore, we should not sample  $m$  and  $d$ . However, despite of its importance, most of the recent works do not consider correcting the bias [\(Zhao et al.,](#page-8-6) [2021;](#page-8-6) [Zhao & Bilen,](#page-8-4) [2021;](#page-8-4) [2022;](#page-8-7) [Cazenavette](#page-7-3) [et al.,](#page-7-3) [2022;](#page-7-3) [Wang et al.,](#page-8-8) [2022;](#page-8-8) [Kim et al.,](#page-7-4) [2022\)](#page-7-4).

Variance due to subsampling of real examples. We also found that random subsampling of real examples is detrimental to performance due to large variance. Again, suppose for simplicity we randomly sample a single index  $n \sim \text{Uniform}(1, N)$  independently for each class c and denote the corresponding loss as L. Then the variance of the gradient  $Var_n(\nabla_{\Theta, \phi} L(\Theta, \phi))$  is

<span id="page-4-1"></span>
$$
\frac{1}{C^2} \sum_{c=1}^{C} V_c^{\mathsf{T}} \left\{ \frac{1}{N} \sum_{n=1}^{N} g(x_{c,n}) g(x_{c,n})^{\mathsf{T}} - \left( \frac{1}{N} \sum_{n=1}^{N} g(x_{c,n}) \right) \left( \frac{1}{N} \sum_{n=1}^{N} g(x_{c,n}) \right)^{\mathsf{T}} \right\} V_c \tag{3}
$$

where  $V_c = \frac{1}{DM} \sum_{d=1}^{M} \sum_{m=1}^{M} \nabla_{\Theta, \phi} g(f(\theta_{c,m}; \phi_d))$ . See Appendix [C](#page-11-0) for the derivation. The middle term in the big braces is nothing but the variance of embeddings of real examples  $g(x_{c,n})$  over  $n = 1, \ldots, N$ . The problem is that since we sample  $g(\cdot)$  from random initializations according to the original distribution matching algorithm [\(Zhao & Bilen,](#page-8-7) [2022\)](#page-8-7), the variance of  $g(x_{c,n})$  tends to be very high compared to the discriminative power of the representation  $g(\cdot)$  across the classes. We empirically observe that such relatively high variance makes the gradient  $\nabla_{\Theta,\phi} L(\Theta,\phi)$  too noisy for the training process to find a good solution.

Full batch training. Based on the above analysis, we propose to perform full batch training for both real and synthetic dataset for unbiased and low-variance training. In case of real dataset, we basically need to compute the embedding mean of real examples  $\frac{1}{N} \sum_{n=1}^{N} g(x_{c,n})$  across the classes and random weights  $w_1, \ldots, w_K$ , with K denoting the total training steps. Instead of repeatedly computing them for every experiment, we speed up our experiments by precomputing those means once and storing them in an external device with the corresponding random weights, so that we can quickly restore them for the later experiments. In case of synthetic dataset, we simply distribute the classes over multiple GPUs and accumulate the gradients over all the processes. When the size of each class is too large to fit in a single GPU, we compute the embedding mean over the whole synthetic examples but backpropagate through only a subset of them for handling memory issue, while keeping the gradient unbiased.

**Discussion.** Although the distribution matching objective in Eq. [\(1\)](#page-3-0) works well in practice, it is not clear why it should work with repeated sampling of  $g(\cdot)$  from random initializations. A simple explanation may be that for each class, a well-condensed synthetic dataset should lead to a similar embedding mean to that of real dataset regardless of those random initializations. However, further justification and understanding of the objective is required, which we leave as a future work.

| Dataset<br>Images / Class<br>Param. / Class | SVHN                                               |                                                    |                                                    | CIFAR10                                            |                                                    |                                                    | CIFAR100                                           |                                                    | TinyImageNet                                       |                                                    |
|---------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|
|                                             | 1<br>3072                                          | 10<br>30720                                        | 50<br>153600                                       | 1<br>3072                                          | 10<br>30720                                        | 50<br>153600                                       | 1<br>3072                                          | 10<br>30720                                        | 1<br>12288                                         | 10<br>122880                                       |
| Random                                      | 14.6 <span style="vertical-align:sub;">±1.6</span> | 35.1 <span style="vertical-align:sub;">±4.1</span> | 70.9 <span style="vertical-align:sub;">±0.9</span> | 14.4 <span style="vertical-align:sub;">±2.0</span> | 26.0 <span style="vertical-align:sub;">±1.2</span> | 43.4 <span style="vertical-align:sub;">±1.0</span> | 4.2 <span style="vertical-align:sub;">±0.3</span>  | 14.6 <span style="vertical-align:sub;">±0.5</span> | 1.4 <span style="vertical-align:sub;">±0.1</span>  | 5.0 <span style="vertical-align:sub;">±0.2</span>  |
| Herding                                     | 20.9 <span style="vertical-align:sub;">±1.3</span> | 50.5 <span style="vertical-align:sub;">±3.3</span> | 72.6 <span style="vertical-align:sub;">±0.8</span> | 21.5 <span style="vertical-align:sub;">±1.2</span> | 31.6 <span style="vertical-align:sub;">±0.7</span> | 40.4 <span style="vertical-align:sub;">±0.6</span> | 8.4 <span style="vertical-align:sub;">±0.3</span>  | 17.3 <span style="vertical-align:sub;">±0.3</span> | 2.8 <span style="vertical-align:sub;">±0.2</span>  | 6.3 <span style="vertical-align:sub;">±0.2</span>  |
| DC                                          | 31.2 <span style="vertical-align:sub;">±1.4</span> | 76.1 <span style="vertical-align:sub;">±0.6</span> | 82.3 <span style="vertical-align:sub;">±0.3</span> | 28.3 <span style="vertical-align:sub;">±0.5</span> | 44.9 <span style="vertical-align:sub;">±0.5</span> | 53.9 <span style="vertical-align:sub;">±0.5</span> | 12.8 <span style="vertical-align:sub;">±0.3</span> | 25.2 <span style="vertical-align:sub;">±0.3</span> | -                                                  | -                                                  |
| DSA                                         | 27.5 <span style="vertical-align:sub;">±1.4</span> | 79.2 <span style="vertical-align:sub;">±0.5</span> | 84.4 <span style="vertical-align:sub;">±0.4</span> | 28.8 <span style="vertical-align:sub;">±0.7</span> | 52.1 <span style="vertical-align:sub;">±0.5</span> | 60.6 <span style="vertical-align:sub;">±0.5</span> | 13.9 <span style="vertical-align:sub;">±0.3</span> | 32.3 <span style="vertical-align:sub;">±0.3</span> | -                                                  | -                                                  |
| DM                                          | 20.3 <span style="vertical-align:sub;">±2.1</span> | 73.5 <span style="vertical-align:sub;">±1.0</span> | 84.2 <span style="vertical-align:sub;">±0.0</span> | 26.0 <span style="vertical-align:sub;">±0.8</span> | 48.9 <span style="vertical-align:sub;">±0.6</span> | 63.0 <span style="vertical-align:sub;">±0.4</span> | 11.4 <span style="vertical-align:sub;">±0.3</span> | 29.7 <span style="vertical-align:sub;">±0.3</span> | 3.9 <span style="vertical-align:sub;">±0.2</span>  | 12.9 <span style="vertical-align:sub;">±0.4</span> |
| KIP to NN                                   | 57.3 <span style="vertical-align:sub;">±0.1</span> | 75.0 <span style="vertical-align:sub;">±0.1</span> | 80.5 <span style="vertical-align:sub;">±0.1</span> | 49.9 <span style="vertical-align:sub;">±0.2</span> | 62.7 <span style="vertical-align:sub;">±0.3</span> | 68.6 <span style="vertical-align:sub;">±0.2</span> | 15.7 <span style="vertical-align:sub;">±0.2</span> | 28.3 <span style="vertical-align:sub;">±0.1</span> | -                                                  | -                                                  |
| CAFE + DSA                                  | 42.9 <span style="vertical-align:sub;">±3.0</span> | 77.9 <span style="vertical-align:sub;">±0.6</span> | 82.3 <span style="vertical-align:sub;">±0.4</span> | 31.6 <span style="vertical-align:sub;">±0.8</span> | 50.9 <span style="vertical-align:sub;">±0.5</span> | 62.3 <span style="vertical-align:sub;">±0.4</span> | 14.0 <span style="vertical-align:sub;">±0.3</span> | 31.5 <span style="vertical-align:sub;">±0.2</span> | -                                                  | -                                                  |
| Traj. Matching                              | -                                                  | -                                                  | -                                                  | 46.3 <span style="vertical-align:sub;">±0.8</span> | 65.3 <span style="vertical-align:sub;">±0.7</span> | 71.6 <span style="vertical-align:sub;">±0.2</span> | 24.3 <span style="vertical-align:sub;">±0.3</span> | 40.1 <span style="vertical-align:sub;">±0.4</span> | 8.8 <span style="vertical-align:sub;">±0.3</span>  | 23.2 <span style="vertical-align:sub;">±0.2</span> |
| IDC                                         | 68.1 <span style="vertical-align:sub;">±0.1</span> | 87.3 <span style="vertical-align:sub;">±0.2</span> | 90.2 <span style="vertical-align:sub;">±0.1</span> | 50.0 <span style="vertical-align:sub;">±0.4</span> | 67.5 <span style="vertical-align:sub;">±0.5</span> | 74.5 <span style="vertical-align:sub;">±0.1</span> | -                                                  | 44.8 <span style="vertical-align:sub;">±0.2</span> | -                                                  | -                                                  |
| <b>KFS</b> (ours)                           | 82.9 <span style="vertical-align:sub;">±0.4</span> | 91.4 <span style="vertical-align:sub;">±0.2</span> | 92.2 <span style="vertical-align:sub;">±0.1</span> | 59.8 <span style="vertical-align:sub;">±0.5</span> | 72.0 <span style="vertical-align:sub;">±0.3</span> | 75.0 <span style="vertical-align:sub;">±0.2</span> | 40.0 <span style="vertical-align:sub;">±0.5</span> | 50.6 <span style="vertical-align:sub;">±0.2</span> | 22.7 <span style="vertical-align:sub;">±0.2</span> | 27.8 <span style="vertical-align:sub;">±0.2</span> |
| Full dataset                                | -                                                  | 95.4 <span style="vertical-align:sub;">±0.1</span> | -                                                  | -                                                  | 84.8 <span style="vertical-align:sub;">±0.1</span> | -                                                  | -                                                  | 56.2 <span style="vertical-align:sub;">±0.3</span> | -                                                  | 37.6 <span style="vertical-align:sub;">±0.4</span> |

<span id="page-5-0"></span>Table 1: Classification accuracies  $(\mathscr{C})$  on ConvNet-3. For our method, we report mean and standard deviation over 15 evaluations (3 training runs and 5 evaluations for each run).

<span id="page-5-1"></span>Table 2: Cross architecture experiments. Conv3, RN10, and DN121 denote ConvNet-3, ResNet-10, and DenseNet-121, respectively. We train on ConvNet-3 and evaluate on the three architectures.

| Dataset        | Images / Class<br>Test Architecture | 1                         |                           |                           | 10                        |                           |                           | 50                                        |                                           |                           |
|----------------|-------------------------------------|---------------------------|---------------------------|---------------------------|---------------------------|---------------------------|---------------------------|-------------------------------------------|-------------------------------------------|---------------------------|
|                |                                     | Conv3                     | RN10                      | DN121                     | Conv3                     | RN10                      | DN121                     | Conv3                                     | RN10                                      | DN121                     |
| <b>SVHN</b>    | DSA                                 | $27.5 	ext{ 	extpm } 1.4$ | $13.2 	ext{ 	extpm } 1.1$ | $13.3 	ext{ 	extpm } 1.4$ | $79.2 	ext{ 	extpm } 0.5$ | $19.5 	ext{ 	extpm } 1.5$ | $23.1 	ext{ 	extpm } 1.9$ | $84.4 	ext{ 	extpm } 0.4$                 | $41.6 	ext{ 	extpm } 2.1$                 | $58.0 	ext{ 	extpm } 3.1$ |
|                | DM                                  | $20.3 	ext{ 	extpm } 2.1$ | $10.5 	ext{ 	extpm } 2.8$ | $13.6 	ext{ 	extpm } 1.0$ | $73.5 	ext{ 	extpm } 1.0$ | $28.2 	ext{ 	extpm } 1.5$ | $24.8 	ext{ 	extpm } 2.5$ | $84.2 	ext{ 	extpm } 0.0$                 | $54.7 	ext{ 	extpm } 1.3$                 | $58.4 	ext{ 	extpm } 2.7$ |
|                | IDC                                 | $68.1 	ext{ 	extpm } 0.1$ | $39.6 	ext{ 	extpm } 1.5$ | $39.9 	ext{ 	extpm } 2.9$ | $87.3 	ext{ 	extpm } 0.2$ | $83.3 	ext{ 	extpm } 0.2$ | $82.8 	ext{ 	extpm } 0.2$ | $90.2 	ext{ 	extpm } 0.1$                 | $89.1 	ext{ 	extpm } 0.2$                 | $91.0 	ext{ 	extpm } 0.3$ |
|                | <b>KFS</b> (ours)                   | $82.9 	ext{ 	extpm } 0.4$ | $75.7 	ext{ 	extpm } 0.8$ | $81.0 	ext{ 	extpm } 0.7$ | $91.4 	ext{ 	extpm } 0.2$ | $90.3 	ext{ 	extpm } 0.2$ | $89.7 	ext{ 	extpm } 0.2$ | $92.2 	ext{ 	extpm } 0.1$                 | $90.9 	ext{ 	extpm } 0.2$                 | $90.2 	ext{ 	extpm } 0.2$ |
|                | Full dataset                        | $95.4 	ext{ 	extpm } 0.1$ | $93.8 	ext{ 	extpm } 0.5$ | $89.1 	ext{ 	extpm } 0.8$ | $95.4 	ext{ 	extpm } 0.1$ | $93.8 	ext{ 	extpm } 0.5$ | $89.1 	ext{ 	extpm } 0.8$ | $95.4 	ext{ 	extpm } 0.1$                 | $93.8 	ext{ 	extpm } 0.5$                 | $89.1 	ext{ 	extpm } 0.8$ |
| <b>CIFAR10</b> | DSA                                 | $28.8 	ext{ 	extpm } 0.7$ | $25.1 	ext{ 	extpm } 0.8$ | $25.9 	ext{ 	extpm } 1.8$ | $52.1 	ext{ 	extpm } 0.5$ | $31.4 	ext{ 	extpm } 0.9$ | $32.9 	ext{ 	extpm } 1.0$ | $60.6 	ext{ 	extpm } 0.5$                 | $49.0 	ext{ 	extpm } 0.7$                 | $53.4 	ext{ 	extpm } 0.8$ |
|                | DM                                  | $26.0 	ext{ 	extpm } 0.8$ | $13.7 	ext{ 	extpm } 1.6$ | $12.9 	ext{ 	extpm } 1.8$ | $48.9 	ext{ 	extpm } 0.6$ | $31.7 	ext{ 	extpm } 1.1$ | $32.2 	ext{ 	extpm } 0.8$ | $63.0 	ext{ 	extpm } 0.4$                 | $49.1 	ext{ 	extpm } 0.7$                 | $53.7 	ext{ 	extpm } 0.7$ |
|                | IDC                                 | $50.0 	ext{ 	extpm } 0.4$ | $41.9 	ext{ 	extpm } 0.6$ | $39.8 	ext{ 	extpm } 1.2$ | $67.5 	ext{ 	extpm } 0.5$ | $63.5 	ext{ 	extpm } 0.1$ | $61.6 	ext{ 	extpm } 0.6$ | $74.5 	ext{ 	extpm } 0.1$                 | $72.4 	ext{ 	extpm } 0.5$                 | $71.8 	ext{ 	extpm } 0.6$ |
|                | <b>KFS</b> (ours)                   | $59.8 	ext{ 	extpm } 0.5$ | $47.0 	ext{ 	extpm } 0.8$ | $49.5 	ext{ 	extpm } 1.3$ | $72.0 	ext{ 	extpm } 0.3$ | $70.3 	ext{ 	extpm } 0.3$ | $71.4 	ext{ 	extpm } 0.4$ | $	extbf{75.0} 	ext{ 	extpm } 	extbf{0.2}$ | $	extbf{75.1} 	ext{ 	extpm } 	extbf{0.3}$ | $76.3 	ext{ 	extpm } 0.4$ |
|                | Full dataset                        | $84.8 	ext{ 	extpm } 0.1$ | $87.9 	ext{ 	extpm } 0.2$ | $90.5 	ext{ 	extpm } 0.3$ | $84.8 	ext{ 	extpm } 0.1$ | $87.9 	ext{ 	extpm } 0.2$ | $90.5 	ext{ 	extpm } 0.3$ | $84.8 	ext{ 	extpm } 0.1$                 | $87.9 	ext{ 	extpm } 0.2$                 | $90.5 	ext{ 	extpm } 0.3$ |

# 4 EXPERIMENTS

We next experimentally validate the efficacy of KFS on various datasets and learning scenarios.

**Datasets.** We consider the following benchmark datasets such as SVHN  $(32\times32)$  [\(Netzer et al.,](#page-8-16) [2011\)](#page-8-16), CIFAR10 (32×32) [\(Krizhevsky et al.,](#page-8-17) [2009\)](#page-8-17), CIFAR100 (32×32) [\(Krizhevsky et al.,](#page-8-17) [2009\)](#page-8-17), and TinyImageNet (64×64) [\(Le & Yang,](#page-8-18) [2015\)](#page-8-18). Baselines. We consider coreset selection approaches such as Random and Herding. For dataset condensation approaches, we compare with gradient (or trajectory) matching such as DC [\(Zhao et al.,](#page-8-6) [2021\)](#page-8-6), DSA [\(Zhao & Bilen,](#page-8-4) [2021\)](#page-8-4), Trajectory Matcing [\(Cazenavette et al.,](#page-7-3) [2022\)](#page-7-4), and IDC [\(Kim et al.,](#page-7-4) 2022), as well as distribution matching such as DM [\(Zhao & Bilen,](#page-8-7) [2022\)](#page-8-7) and CAFE [\(Wang et al.,](#page-8-8) [2022\)](#page-8-8). We also compare against KIP [\(Nguyen](#page-8-10) [et al.,](#page-8-10) [2021\)](#page-8-10). **Experimental setup.** For fair comparison, we use essentially the same parameter count to the baselines. For instance, if there are 10 classes with 10 "Images / Class" (in Table [1](#page-5-0) and Table [2\)](#page-5-1) and the image shape is  $3\times32\times32$ , then the total per-class parameter count is 30720. In this case our KFS assumes 16 codes of shape  $12\times4\times4$  with the 8 decoders requiring only 73.8 additional parameters per class (see section [3.1\)](#page-2-0). See Appendix [A](#page-9-0) for further details about experimental setup.

Quantitative analysis. Table [1](#page-5-0) shows the performance of the baselines and our KFS from each dataset with varying number of images (or parameter count) per class. We can see that KFS outperforms all the previous methods by significant margins in all datasets and settings. KFS especially performs well when the given parameter count per class is small. For instance, when "Images / Class" = 1, KFS shows 14.8% and 9.8% performance improvements over IDC on SVHN and CI-FAR10 respectively, the previous state-of-the-art recently introduced by [Kim et al.](#page-7-4) [\(2022\)](#page-7-4). This reconfirms the observation from [Kim et al.](#page-7-4) [\(2022\)](#page-7-4) that increasing the modality in the synthetic dataset is more important than polishing a few high-quality examples when the parameter count is very limited. However, KFS provides much higher modality (i.e. more number of synthetic examples) than IDS with essentially the same parameter count because KFS can compress each instance far more tightly by systematically factorizing and sharing the knowledge across the synthetic examples.

Cross-architecture generalization. Table [2](#page-5-1) shows how the baselines and KFS perform when the network architecture becomes different at the evaluation time (ResNet-10 and DenseNet-121) from the one that used for condensation (ConvNet-3). We can see that the performance of KFS is more robust to the change of network architectures. For instance, when "Images/Class" = 1 in SVHN

Image /page/6/Figure/1 description: This image contains four line graphs comparing the test accuracy of different methods (KFS, IDC, DSA, DM) against the budget at evaluation time (total training steps) on two datasets: SVHN and CIFAR10, with varying numbers of images per class. The first graph, titled "SVHN (Images/Class=10)", shows KFS achieving the highest accuracy, starting around 81% and increasing to over 92% with a budget of 20K steps. IDC follows, peaking at around 87% before declining. DSA shows a steady increase from about 77% to 80% and then a decline to around 74%. DM starts at approximately 72%, peaks at 74% around 500 steps, and then declines to about 69%. The second graph, "SVHN (Images/Class=50)", shows a similar trend, with KFS reaching over 92%, IDC peaking at around 89%, DSA reaching about 86% and then declining, and DM peaking at around 84% before declining. The third graph, "CIFAR10 (Images/Class=1)", shows KFS reaching over 72%, IDC peaking at around 66%, DSA reaching about 50% and then declining slightly, and DM peaking at around 47% before declining. The fourth graph, "CIFAR10 (Images/Class=10)", shows KFS reaching over 71%, IDC peaking at around 67%, DSA reaching about 53% and then declining slightly, and DM peaking at around 50% before increasing slightly. All graphs have "Test Acc." on the y-axis and "Budget at evaluation time (Total training steps)" on the x-axis, with budgets ranging from 100 to 20K steps.

<span id="page-6-0"></span>Figure 2: The evaluation results across various amount of budgets defined as the number of training steps allowed for each evaluation. Batchsize is set to  $\min(N_{total}, 256)$  where  $N_{total}$  is the number of all examples in each synthetic dataset.

Image /page/6/Figure/3 description: The image displays a grid of 32 images, arranged in two rows of four columns. The top row features images of the digit '9' in the first column, the digit '2' in the second column, airplanes in the third column, and horses in the fourth column. The bottom row shows images of the digit '4' in the first column, the digit '5' in the second column, cars in the third column, and deer in the fourth column. Each column contains four images, and the images within each column are of the same category.

<span id="page-6-1"></span>Figure 3: (Top row) Synthetic images (SVHN and CIFAR10) generated by varying latent codes while fixing a decoder. (Bottom row) Synthetic images generated by varying decoders while fixing a code.

dataset, the performance drop of KFS is only 7.2% and 1.9% for ResNet-10 and DenseNet-121 respectively, whereas the drop is 28.5% and 28.2% for IDC. Such robustness means that the learned synthetic examples from KFS are more natural as they are less specifically tailored to a specific network architecture. Similarly, when "Images/Class" = 50 in CIFAR10 dataset, the performance of KFS even improves as the architecture changes, while the performance of other baselines decreases.

Training budgets at evaluation time. One may wonder whether the good performance of KFS is solely due to the larger size of the synthetic dataset rather than the quality of each example, such that KFS inherently requires much longer evaluation time than the baselines. To address this question, we consider various amount of budget at evaluation time defined as total training steps allowed. In Figure [2](#page-6-0) we can clearly see that KFS provides significantly better performances than the baselines across all range of budgets: from the limited budgets (100-500 training steps) to the longer training steps (1K-20K steps), demonstrating that the quality of synthetic examples generated from KFS is also comparable to or even better than that of the baselines. Interestingly, whereas the baselines often suffer from overfitting (e.g., SVHN dataset), our KFS has no such issue hence can provide more diverse options to users about how much longer to train their models for better performance.

Qualitative analysis. We next visualize the synthetic images generated from KFS in Figure [3.](#page-6-1) We can see from the top row that KFS can generate various modes of the data distribution by varing the learned latent codes while fixing a decoder. For instance, in SVHN dataset the digits show various styles with different adjacent numbers, and in CIFAR10 dataset the orientations and shapes of objects are diverse, demonstrating the ability of the latent codes to capture multi-modality of each data distribution. We also fix a code and vary the decoders in the bottom row. The synthetic examples show various backgrounds and styles of the objects such as strokes and colors. Overall,

the visualization clearly shows how the factorization between latent codes and decoders allows KFS to efficiently share knowledge between the synthetic examples.

# 5 CONCLUSION

In this paper, we introduced a novel method for solving dataset condensation problem in a systematic and efficient way. Instead of parameterizing synthetic examples directly in input space, we proposed to fully exploit the regularity in a given dataset, such that we assume a generative process of synthetic examples based on the factorization between latent codes and decoders. Based on it, we showed how to increase the number of synthetic examples with essentially the same parameter count by combining the codes and decoders interchangeably. We further demonstrated how the knowledge is efficiently shared between synthetic examples with the framework, achieving superior trade-off between compression ratio and quality of the synthetic examples. Possible future directions include scaling up the method to achieve or even surpass the performances of the full dataset, and further developing the idea to multi-task or meta-learning setting where the goal is to share knowledge about how to compress a dataset between multiple tasks.

## REFERENCES

- <span id="page-7-0"></span>Yoshua Bengio. Gradient-based optimization of hyperparameters. *Neural computation*, 12(8):1889– 1900, 2000.
- <span id="page-7-2"></span>Zalan Borsos, Mojmir Mutny, and Andreas Krause. Coresets via bilevel optimization for continual ´ learning and streaming. *Advances in Neural Information Processing Systems*, 33:14879–14890, 2020.
- <span id="page-7-3"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750–4759, 2022.
- <span id="page-7-1"></span>Luca Franceschi, Michele Donini, Paolo Frasconi, and Massimiliano Pontil. Forward and reverse gradient-based hyperparameter optimization. In *International Conference on Machine Learning*, pp. 1165–1173. PMLR, 2017.
- <span id="page-7-5"></span>Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. *Advances in neural information processing systems*, 27, 2014.
- <span id="page-7-6"></span>Anirudh Goyal and Yoshua Bengio. Inductive biases for deep learning of higher-level cognition. *CoRR*, abs/2011.15091, 2020. URL <https://arxiv.org/abs/2011.15091>.
- <span id="page-7-8"></span>Anirudh Goyal, Aniket Didolkar, Nan Rosemary Ke, Charles Blundell, Philippe Beaudoin, Nicolas Heess, Michael C Mozer, and Yoshua Bengio. Neural production systems. In M. Ranzato, A. Beygelzimer, Y. Dauphin, P.S. Liang, and J. Wortman Vaughan (eds.), *Advances in Neural Information Processing Systems*, volume 34, pp. 25673–25687. Curran Associates, Inc., 2021a.
- <span id="page-7-7"></span>Anirudh Goyal, Alex Lamb, Phanideep Gampa, Philippe Beaudoin, Charles Blundell, Sergey Levine, Yoshua Bengio, and Michael Curtis Mozer. Factorizing declarative and procedural knowledge in structured, dynamical environments. In *International Conference on Learning Representations*, 2021b.
- <span id="page-7-9"></span>Arthur Gretton, Karsten M Borgwardt, Malte J Rasch, Bernhard Scholkopf, and Alexander Smola. ¨ A kernel two-sample test. *The Journal of Machine Learning Research*, 13(1):723–773, 2012.
- <span id="page-7-4"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *Proceedings of the 39th International Conference on Machine Learning*, volume 162 of *Proceedings of Machine Learning Research*, pp. 11102–11118. PMLR, 17–23 Jul 2022.
- <span id="page-7-10"></span>Diederik P Kingma and Jimmy Ba. Adam: A method for stochastic optimization. *arXiv preprint arXiv:1412.6980*, 2014.

- <span id="page-8-11"></span>Diederik P Kingma and Max Welling. Auto-encoding variational bayes. *arXiv preprint arXiv:1312.6114*, 2013.
- <span id="page-8-17"></span>Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-8-18"></span>Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-8-0"></span>Hanxiao Liu, Karen Simonyan, and Yiming Yang. Darts: Differentiable architecture search. In *International Conference on Learning Representations*, 2018.
- <span id="page-8-1"></span>David Lopez-Paz and Marc'Aurelio Ranzato. Gradient episodic memory for continual learning. *Advances in neural information processing systems*, 30, 2017.
- <span id="page-8-16"></span>Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Bo Wu, and Andrew Y Ng. Reading digits in natural images with unsupervised feature learning. 2011.
- <span id="page-8-9"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. *arXiv preprint arXiv:2011.00050*, 2020.
- <span id="page-8-10"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186– 5198, 2021.
- <span id="page-8-2"></span>Jeff M. Phillips. Coresets and sketches. *CoRR*, abs/1601.00617, 2016.
- <span id="page-8-12"></span>Ali Razavi, Aaron van den Oord, Ben Poole, and Oriol Vinyals. Preventing posterior collapse with delta-VAEs. In *International Conference on Learning Representations*, 2019a.
- <span id="page-8-14"></span>Ali Razavi, Aaron Van den Oord, and Oriol Vinyals. Generating diverse high-fidelity images with vq-vae-2. *Advances in neural information processing systems*, 32, 2019b.
- <span id="page-8-15"></span>Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *Proceedings of the 37th International Conference on Machine Learning*, volume 119 of *Proceedings of Machine Learning Research*, pp. 9206–9216. PMLR, 13–18 Jul 2020.
- <span id="page-8-3"></span>Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. In *International Conference on Learning Representations*, 2018.
- <span id="page-8-13"></span>Aaron Van Den Oord, Oriol Vinyals, et al. Neural discrete representation learning. *Advances in neural information processing systems*, 30, 2017.
- <span id="page-8-8"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 12196–12205, 2022.
- <span id="page-8-5"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *CoRR*, abs/1811.10959, 2018.
- <span id="page-8-4"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021.
- <span id="page-8-7"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching, 2022. URL [https:](https://openreview.net/forum?id=T2F5aBbSEUQ) [//openreview.net/forum?id=T2F5aBbSEUQ](https://openreview.net/forum?id=T2F5aBbSEUQ).
- <span id="page-8-6"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021.

<span id="page-9-0"></span>

# A EXPERIMENTAL SETUP

We provide some additional information about the experimental setup for reproducing our results.

<span id="page-9-1"></span>Table 3: The architecture of Low-Resolution Decoder in pytorch style.  $H, W$  are height and weight of target image. Here the size of the latent codes are assumed to be  $12 \times H/8 \times W/8$ .

| <b>Output Size</b>         | <b>Lavers</b>                                                                  |
|----------------------------|--------------------------------------------------------------------------------|
| $12 \times H/8 \times W/8$ | <b>Latent Codes</b>                                                            |
| $9 \times H/4 \times W/4$  | $ConvTranspose2d(in_{channels}=12, out_{channels}=9, kernel_size=2, stride=2)$ |
| $6 \times H/2 \times W/2$  | $ConvTranspose2d(in-channels=9, out-channels=6, kernal_size=2, stride=2)$      |
| $3 \times H \times W$      | $ConvTranspose2d(in_{channels}=6, out_{channels}=3, kernal_size=2, stride=2)$  |
| $3 \times H \times W$      | Sigmoid                                                                        |

<span id="page-9-2"></span>Table 4: The architecture of High-Resolution Decoder in pytorch style.  $H, W$  are height and weight of target image. Here the size of the latent codes are assumed to be  $12 \times H/4 \times W/4$ .

| Output Size              | Layers                                                                     |
|--------------------------|----------------------------------------------------------------------------|
| $12 	imes H/4 	imes W/4$ | Latent Codes                                                               |
| $6 	imes H/2 	imes W/2$  | $ConvTranspose2d(in_channels=12, out_channels=6, kernel_size=2, stride=2)$ |
| $3 	imes H 	imes W$      | $ConvTranspose2d(in_channels=6, out_channels=3, kernel_size=2, stride=2)$  |
| $3 	imes H 	imes W$      | Sigmoid                                                                    |

<span id="page-9-3"></span>Table 5: The hyperparameter configurations of KFS for each setting. "I/C" denotes "Image / Class". "Low-R" refers to the decoder in Table [3,](#page-9-1) and "High-R" refers to the decoder in Table. [4](#page-9-2)

| Dataset              | I/C | Code Shape               | # of Code  | Decoder Type | # of Decoder | Over-Param. $(\%)$ |
|----------------------|-----|--------------------------|------------|--------------|--------------|--------------------|
|                      |     | $12 \times 4 \times 4$   | 13         | Low- $R$ .   | 8            | $0.47\%$           |
| <b>SVHN</b>          | 10  | $12 \times 4 \times 4$   | 160        | $Low-R$ .    | 12           | 2.88\%             |
|                      | 50  | $12 \times 8 \times 8$   | <b>200</b> | High-R.      | 16           | $0.88\%$           |
|                      |     | $12 \times 4 \times 4$   | 13         | Low-R.       | 8            | $0.47\%$           |
| CIFAR <sub>10</sub>  | 10  | $12 \times 4 \times 4$   | 160        | $Low-R$ .    | 12           | 2.88\%             |
|                      | 50  | $12 \times 8 \times 8$   | 200        | High-R.      | 16           | $0.88\%$           |
| CIFAR <sub>100</sub> |     | $12 \times 4 \times 4$   | 16         | $Low-R$ .    | 8            | $1.92\%$           |
|                      | 10  | $12 \times 4 \times 4$   | 160        | $Low-R$ .    | 12           | $0.29\%$           |
| <b>TinyImageNet</b>  |     | $12 \times 8 \times 8$   | 16         | Low-R.       | 8            | $0.24\%$           |
|                      | 10  | $12 \times 16 \times 16$ | 64         | High-R.      | 16           | $0.04\%$           |

- See Table [3,](#page-9-1) [4](#page-9-2) for detailed implementations of decoders.
- See Table [5](#page-9-3) for code shape, # of code, decoder type, # of decoder, and corresponding proportion of over-parameterization on each setting.
- We pre-train a single decoder using autoencoding objective for 2,000 steps with 256 minibatch. We use Adam optimizer (Kingma  $\&$  Ba, [2014\)](#page-7-10) with constant learning rate of 0.01. After pre-training, the parameter of pre-trained decoder is copied to all the others.
- We train decoders and codes using the full distribution matching objective in Eq. [\(1\)](#page-3-0) for 20,000 steps. We also use Adam optimizer with constant learning rate of 0.01 and 0.1 for decoders and latent codes, respectively.
- We train classification models for 200 epochs with 256 mini-batch on our condensed datasets. We use SGD with momentum optimizer, where the initial learning rate, momentum, and weight decay are set to 0.01, 0.9, and 0.0005, respectively. We decay the learning rate by factor of 0.2 for two times at 133 and 166-th epoch.

Preprint.

<span id="page-10-0"></span>

# B DERIVATION OF EQ. $(2)$

For notational brevity, let  $L_c(\Theta, \phi) := \frac{1}{2} \|\phi\|$  $\frac{1}{N} \sum_{n=1}^{N} g(x_{c,n}) - \frac{1}{DM} \sum_{d=1}^{D} \sum_{m=1}^{M} g(f_{c,m,d})$ 2 where  $f_{c,m,d} := f(\theta_{c,m}; \phi_d)$ . We first compute  $\nabla_{\Theta, \phi} L_c(\Theta, \phi)$ .

$$
\nabla_{\Theta,\phi} L_c(\Theta,\phi)
$$

$$
= \nabla_{\Theta,\phi} \frac{1}{2} \left\| \frac{1}{N} \sum_{n=1}^N g(x_{c,n}) - \frac{1}{DM} \sum_{d=1}^D \sum_{m=1}^M g(f_{c,m,d}) \right\|_2^2
$$

$$
= -\frac{1}{NDM} \sum_{n=1}^N \sum_{d=1}^D \sum_{m=1}^M \nabla_{\Theta,\phi} g(f_{c,m,d})^{\mathsf{T}} g(x_{c,n}) + \frac{1}{D^2 M^2} \sum_{d=1}^D \sum_{d'=1}^D \sum_{m=1}^M \sum_{m'=1}^M \nabla_{\Theta,\phi} g(f_{c,m',d'})^{\mathsf{T}} g(f_{c,m,d}) \tag{4}
$$

On the other hand, suppose we sample  $m \sim \text{Uniform}(1, M)$  and  $d \sim \text{Uniform}(1, D)$ , then

<span id="page-10-1"></span>
$$
\mathbb{E}_{m,d}[\nabla_{\Theta,\phi}\hat{L}_{c}(\Theta,\phi)]
$$

$$
= \mathbb{E}_{m,d} \left[ \nabla_{\Theta,\phi}\frac{1}{2} \left\| \frac{1}{N} \sum_{n=1}^{N} g(x_{c,n}) - g(f_{c,m,d}) \right\|_{2}^{2} \right]
$$

$$
= \mathbb{E}_{m,d} \left[ -\frac{1}{N} \sum_{n=1}^{N} g(x_{c,n})^{\mathsf{T}} \nabla_{\Theta,\phi} g(f_{c,m,d}) + \nabla_{\Theta,\phi} g(f_{c,m,d})^{\mathsf{T}} g(f_{c,m,d}) \right]
$$

$$
= -\frac{1}{NDM} \sum_{n=1}^{N} \sum_{d=1}^{D} \sum_{m=1}^{M} \nabla_{\Theta,\phi} g(f_{c,m,d})^{\mathsf{T}} g(x_{c,n}) + \frac{1}{DM} \sum_{d=1}^{D} \sum_{m=1}^{M} \nabla_{\Theta,\phi} g(f_{c,m,d})^{\mathsf{T}} g(f_{c,m,d}) \right]
$$
 $(5)$ 

Therefore, subtracting Eq.  $(4)$  from Eq.  $(5)$ , we have

<span id="page-10-2"></span>
$$
\mathbb{E}_{m,d}[\nabla_{\Theta,\phi}\hat{L}(\Theta,\phi)] - \nabla_{\Theta,\phi}L(\Theta,\phi) = \frac{1}{C} \sum_{c=1}^{C} (\mathbb{E}_{m,d}[\nabla_{\Theta,\phi}\hat{L}_c(\Theta,\phi)] - \nabla_{\Theta,\phi}L_c(\Theta,\phi)) = \nabla_{\Theta,\phi} \frac{1}{C} \sum_{c=1}^{C} \frac{1}{2} \left\{ \frac{1}{DM} \sum_{d=1}^{D} \sum_{m=1}^{M} g(f_{c,m,d})^{\mathsf{T}} g(f_{c,m,d}) - \frac{1}{D^2 M^2} \sum_{d=1}^{D} \sum_{d'=1}^{D} \sum_{m=1}^{M} \sum_{m'=1}^{M} g(f_{c,m,d})^{\mathsf{T}} g(f_{c,m',d'}) \right\}.
$$

<span id="page-11-0"></span>

# C DERIVATION OF EQ. [\(3\)](#page-4-1)

We want to compute

$$
\begin{split} &\text{Var}_{n}(\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)) \\ &= \mathbb{E}_{n}\left[\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)^{\mathsf{T}}\right] - \mathbb{E}_{n}\left[\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)\right]\mathbb{E}_{n}\left[\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)\right]^{\mathsf{T}} \end{split}
$$

Suppose we sample  $n \sim$  Uniform $(1, N)$  for each class c independently. Then we have

$$
\mathbb{E}_{n}[\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)]
$$
$$
= \mathbb{E}_{n}[\nabla_{\Theta,\phi}\frac{1}{C}\sum_{c=1}^{C}\frac{1}{2}\|g(x_{c,n})-\frac{1}{DM}\sum_{d=1}^{D}\sum_{m=1}^{M}g(f_{c,m,d})\|_{2}^{2}]
$$
$$
= \mathbb{E}_{n}[\frac{1}{C}\sum_{c=1}^{C}(-\frac{1}{DM}\sum_{d=1}^{D}\sum_{m=1}^{M}\nabla_{\Theta,\phi}g(f_{c,m,d})^{\mathsf{T}}g(x_{c,n})+\frac{1}{D^{2}M^{2}}\sum_{d=1}^{D}\sum_{d'=1}^{D}\sum_{m=1}^{M}\sum_{m'=1}^{M}\nabla_{\Theta,\phi}g(f_{c,m',d'})^{\mathsf{T}}g(f_{c,m,d}) )]
$$
$$
= \frac{1}{C}\sum_{c=1}^{C}\frac{1}{DM}\sum_{d=1}^{D}\sum_{m=1}^{M}\nabla_{\Theta,\phi}g(f_{c,m,d})^{\mathsf{T}}(-\mathbb{E}_{n}[g(x_{c,n})]+\frac{1}{DM}\sum_{d'=1}^{D}\sum_{m'=1}^{M}g(f_{c,m',d'}))
$$
$$
= \frac{1}{C}\sum_{c=1}^{C}V_{c}^{\mathsf{T}}(-\mathbb{E}_{n}[g(x_{c,n})]+\frac{1}{DM}\sum_{d'=1}^{D}\sum_{m'=1}^{M}g(f_{c,m',d'}))
$$

where  $V_c := \frac{1}{DM} \sum_{d=1}^{D} \sum_{m=1}^{M} \nabla_{\Theta, \phi} g(f_{c,m,d})$ . Therefore,

The following equation illustrates the Pythagorean theorem:

$$
\mathbb{E}_{n}\left[\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)\right]\mathbb{E}_{n}\left[\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)\right]^{T}
$$

$$
=\frac{1}{C}\sum_{c=1}^{C}V_{c}^{T}\left(-\mathbb{E}_{n}[g(x_{c,n})]+\frac{1}{DM}\sum_{d=1}^{D}\sum_{m=1}^{M}g(f_{c,m,d})\right).
$$

$$
=\frac{1}{C}\sum_{c'=1}^{C}\left(-\mathbb{E}_{n}[g(x_{c',n})]+\frac{1}{DM}\sum_{d'=1}^{D}\sum_{m'=1}^{M}g(f_{c',m',d'})\right)^{T}V_{c'}
$$

$$
=\frac{1}{C^{2}}\sum_{c=1}^{C}\sum_{c'=1}^{C}V_{c}^{T}\left(\mathbb{E}_{n}[g(x_{c,n})]\mathbb{E}_{n}[g(x_{c',n})]^{T}-\frac{2}{DM}\sum_{d=1}^{D}\sum_{m=1}^{M}\mathbb{E}_{n}[g(x_{c,n})]^{T}g(f_{c',m,d})\right)
$$

$$
+\frac{1}{D^{2}M^{2}}\sum_{d=1}^{D}\sum_{d'=1}^{D}\sum_{m=1}^{M}\sum_{m'=1}^{M}g(f_{c,m,d})^{T}g(f_{c',m',d'})\right)V_{c'}
$$
(6)

On the other hand, we have

On the other hand, we have

<span id="page-11-2"></span><span id="page-11-1"></span>On the other hand, we have

$$
\mathbb{E}_{n}[\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)^{T}]
$$

$$
= \mathbb{E}_{n}[\left\{\frac{1}{C}\sum_{c=1}^{C}\frac{1}{DM}\sum_{d=1}^{D}\sum_{m=1}^{M}\nabla_{\Theta,\phi}g(f_{c,m,d})^{T}(-g(x_{c,n})+\frac{1}{DM}\sum_{d'=1}^{D}\sum_{m'=1}^{M}g(f_{c,m',d'}))\right\}]
$$

$$
= \frac{1}{C^{2}}\sum_{c=1}^{C}\sum_{c'=1}^{C}V_{c}^{T}(\mathbb{E}_{n}[g(x_{c,n})g(x_{c',n})^{T}]-\frac{2}{DM}\sum_{d=1}^{D}\sum_{m=1}^{M}\mathbb{E}_{n}[g(x_{c,n})]^{T}g(f_{c',m,d}) + \frac{1}{D^{2}M^{2}}\sum_{d=1}^{D}\sum_{d'=1}^{D}\sum_{m=1}^{M}\sum_{m'=1}^{M}g(f_{c,m,d})^{T}g(f_{c',m',d'}))V_{c'}
$$
(7)

Subtracting Eq.  $(6)$  from Eq.  $(7)$ , we have

$$
\begin{split}\n\text{Var}_{n}(\nabla_{\Theta,\phi}\tilde{L}(\Theta,\phi)) \\
&= \frac{1}{C^{2}} \sum_{c=1}^{C} \sum_{c'=1}^{C} V_{c}^{\mathsf{T}} \left( \mathbb{E}_{n} \left[ g(x_{c,n})g(x_{c',n}) \right]^{\mathsf{T}} - \mathbb{E}_{n} [g(x_{c,n})] \mathbb{E}_{n} [g(x_{c',n})]^{\mathsf{T}} \right) V_{c'} \\
&= \frac{1}{C^{2}} \sum_{c=1}^{C} V_{c}^{\mathsf{T}} \left( \mathbb{E}_{n} [g(x_{c,n})g(x_{c,n})^{\mathsf{T}}] - \mathbb{E}_{n} [g(x_{c,n})] \mathbb{E}_{n} [g(x_{c,n})]^{\mathsf{T}} \right) V_{c} \\
&= \frac{1}{C^{2}} \sum_{c=1}^{C} V_{c} \left\{ \frac{1}{N} \sum_{n=1}^{N} g(x_{c,n})g(x_{c,n})^{\mathsf{T}} - \left( \frac{1}{N} \sum_{n=1}^{N} g(x_{c,n}) \right) \left( \frac{1}{N} \sum_{n=1}^{N} g(x_{c,n}) \right)^{\mathsf{T}} \right\} V_{c}^{\mathsf{T}}\n\end{split}
$$

because  $g(x_{c,n})$  and  $g(x_{c',n})$  are independent of each other as we sample n independently for each class.
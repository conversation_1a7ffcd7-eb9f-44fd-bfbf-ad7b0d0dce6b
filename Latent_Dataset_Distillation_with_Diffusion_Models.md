# Latent Dataset Distillation with Diffusion Models

<PERSON>\$^{1,2,3}\$

<span id="page-0-0"></span><PERSON><sup>1,2,3</sup> <PERSON><sup>1,3</sup> <PERSON><sup>1</sup> <PERSON><PERSON><sup>1,2</sup> <PERSON>^{1,2}$ 

<PERSON><PERSON>
1,2

<sup>1</sup> German Research Center for Artificial Intelligence (DFKI), Germany <sup>2</sup> RPTU Kaiserslautern-Landau, Germany <sup>3</sup> Equal Contribution *<EMAIL>*

## Abstract

*Machine learning traditionally relies on increasingly larger datasets. Yet, such datasets pose major storage challenges and usually contain non-influential samples, which could be ignored during training without negatively impacting the training quality. In response, the idea of distilling a dataset into a condensed set of synthetic samples, i.e., a distilled dataset, emerged. One key aspect is the selected architecture, usually ConvNet, for linking the original and synthetic datasets. However, the final accuracy is lower if the employed model architecture differs from that used during distillation. Another challenge is the generation of high-resolution images (128x128 and higher). To address both challenges, this paper proposes Latent Dataset Distillation with Diffusion Models (LD3M) that combine diffusion in latent space with dataset distillation. Our novel diffusion process is tailored for this task and significantly improves the gradient flow for distillation. By adjusting the number of diffusion steps, LD3M also offers a convenient way of controlling the trade-off between distillation speed and dataset quality. Overall, LD3M consistently outperforms state-ofthe-art methods by up to 4.8 p.p. and 4.2 p.p. for 1 and 10 images per class, respectively, and on several ImageNet subsets and high resolutions (128x128 and 256x256).*

## 1. Introduction

The undeniable success of employing larger datasets has led to a growing body of research  $[4, 8, 16, 52, 53, 61]$  $[4, 8, 16, 52, 53, 61]$  $[4, 8, 16, 52, 53, 61]$  $[4, 8, 16, 52, 53, 61]$  $[4, 8, 16, 52, 53, 61]$  $[4, 8, 16, 52, 53, 61]$  $[4, 8, 16, 52, 53, 61]$  $[4, 8, 16, 52, 53, 61]$  $[4, 8, 16, 52, 53, 61]$  $[4, 8, 16, 52, 53, 61]$  $[4, 8, 16, 52, 53, 61]$  trying to solve the question: Is the large-scale size of a dataset essential for training models capable of solving complex visual tasks? Using large-scale datasets to train modern neural networks demands special equipment and infrastructure. It further poses challenges regarding storing and data pre-processing [\[20\]](#page-8-3). Moreover, recent studies have shown

that even popular large-scale datasets contain non-influential samples, which could be ignored during training without impacting the final model  $[30]$ . These insights led to methods that try to decrease the magnitude of training sets, e.g., importance sampling and coreset selection [\[10,](#page-8-5) [23,](#page-8-6) [38,](#page-9-2) [40\]](#page-9-3).

Following the idea of knowledge distillation [\[25\]](#page-8-7), dataset distillation [\[63\]](#page-10-1) aims to generate a small set of representative synthetic samples from the original training set. Since then, the idea of condensing large datasets into a few synthesized images, e.g., one image per class, has rapidly gained traction. It has found applications in the privacy domain or accelerated neural architecture search [\[7,](#page-8-8) [14,](#page-8-9) [37,](#page-9-4) [60\]](#page-9-5).

However, conventional methods like Dataset Condensation [\[69\]](#page-10-2), Distribution Matching [\[68\]](#page-10-3), and Matching Training Trajectories [\[6\]](#page-8-10) still face major limitations. The fundamental problems are the generalization across architectures and high image resolutions, i.e.,  $128 \times 128$  or  $256 \times 256$ . Models show peak performance when their architecture matches the one used for distillation. Yet, the performance deteriorates if other architectures are applied. Furthermore, dataset distillation performs poorly on images with resolutions larger than  $32 \times 32$ . This arises primarily from the common practice of distilling the raw pixel values [\[8\]](#page-8-1). Optimizing pixels directly leads to datasets that overfit the network used during the distillation process [\[3\]](#page-8-11).

To address these challenges, we propose Latent Dataset Distillation with Diffusion Models (LD3M), which integrates state-of-the-art image generative models, namely diffusion models, with dataset distillation. LD3M avoids distilling into the pixel space and finds better representations in latent space that can be decoded into synthetic images. Training multiple architectures with the synthetic samples leads to significantly higher accuracy overall, addressing the lack of generalization of traditional distillation methods. The consistency of the latent space also allows for the improved quality of higher-resolution samples compared to other state-of-the-art methods.

<span id="page-1-0"></span>One benefit of LD3M is that it exploits a pre-trained diffusion model out of the box. Moreover, LD3M is compatible with any distillation algorithm, turning LD3M into a flexible and more powerful alternative to raw distillation methods [\[6,](#page-8-10) [68,](#page-10-3) [69\]](#page-10-2). The general formulation of LD3M enables the use of any diffusion model, making it easily extensible and adaptable to new developments in the field of diffusion models. Also, using a pre-trained LDM allows for a straightforward initialization of the distilled dataset. In other words, the original images are passed through the encoder to obtain their respective latent codes. This is an advantage over current GAN-based dataset distillation methods like GLaD [\[8\]](#page-8-1) that use costly GAN inversions  $[5, 72]$  $[5, 72]$  $[5, 72]$ .

We evaluate our approach on varying numbers of images per class, multiple model architectures, and different image resolutions, all in the context of image classification. Our results show that LD3M distills notably faster and generates datasets of much higher quality for training various architectures than the state-of-the-art latent dataset distillation method GLaD [\[57\]](#page-9-6). Overall, our work:

- introduces LD3M as a latent distillation method with pre-trained diffusion models bridging latent and synthetic images. LD3M can be used out of the box with existing dataset distillation methods.
- provides an alternative and straightforward diffusion process suitable for dataset distillation. LD3M lays the groundwork for future research and is easily extendable to new diffusion models.
- significantly improves the quality of synthetic datasets compared to the state-of-the-art method GLaD in various dataset distillation experiments.

## 2. Background

This section briefly reviews the most important distillation algorithms and discusses their relation to LD3M.

#### 2.1. Dataset Distillation Algorithms

Distillation algorithms compress datasets into a small set of synthetic samples, the size of which is defined by the number of Images Per Class (IPC). Initially, the synthetic images are learnable, similar to weights in neural networks. After the distillation, the synthetic images constitute an expressive representation of a class in the original dataset. Most approaches initialize the synthetic images with IPC-many random images from each class. The core idea is then to use a simple ConvNet and to align its training progress on the synthetic set with the progress on the original dataset  $[31,35,67]$  $[31,35,67]$  $[31,35,67]$ . This alignment can be expressed as an optimization process that updates the synthetic images to maximize similarity in training progress. In this section, we describe three commonly

used methods, namely Dataset Condensation [\[69\]](#page-10-2), Distribution Matching [\[68\]](#page-10-3), and Matching Training Trajectories [\[6\]](#page-8-10). Each method represents a different type of distillation algorithm: gradient matching, feature matching, and parameter matching.

Dataset Condensation (DC) ensures alignment by deriving the gradients via a classification error [\[69\]](#page-10-2). It calculates the loss on real and the respective synthetic data. Next, it minimizes the distance between the two loss gradients.

Distribution Matching (DM) obtains gradients by minimizing the produced features on the real and synthetic datasets. It enforces the feature extractor (ConvNet) to produce similar features for real and synthetic images [\[68\]](#page-10-3).

Matching Training Trajectories (MTT) does not rely on the gradients obtained by calculating the classification error or the feature distance. Instead, it uses the network, i.e., ConvNet, parameters [\[6\]](#page-8-10). It trains multiple model instantiations on the original dataset, called experts, and records the history of parameters at predetermined intervals, called expert trajectories. For dataset distillation, it samples a random set of parameters from this history at a given timestamp. Next, it trains a new network initialized with the parameters on the respective synthetic images. Finally, it minimizes the distance between the trajectory on the real dataset and the trajectory on the synthetic one. As a result, it tries to mimic the original dataset's training path (trajectory of parameters) with the synthetic images.

#### 2.2. Diffusion Models for Image Generation

The premise of diffusion models is to generate reallooking data by progressively adding noise to an initial sample and subsequently reversing the process by eliminating the noise. This reversal aids in approximating a complex target data distribution gradually  $[41–43, 65]$  $[41–43, 65]$  $[41–43, 65]$  $[41–43, 65]$ . The main characteristic of diffusion models, which sets them apart from previous generative models, is their dependency on previous time steps. Through iterative refinement, the generative model keeps track of small perturbations and corrects them instead of predicting a large and challenging transformation [\[26,](#page-8-14) [49,](#page-9-10) [65\]](#page-10-6). These iterative steps unfold forward and backward in time:

Forward Diffusion - The original input data is progressively degraded by adding noise, moving forward in time.

Backward Diffusion - Iteratively denoise the previously degraded data, effectively reversing the noise addition process and thus moving backward in time.

To reduce the computational requirements for generating high-resolution images, Rombach et al. propose to move the diffusion process towards the latent space representations of an autoencoder structure, which they called Latent Diffusion Model (LDM) [\[54\]](#page-9-11). Usually, a pre-trained autoencoder [\[18\]](#page-8-15) is used to compress input data into a low-dimensional latent code. Next, LDM uses the diffusion pipeline in the latent <span id="page-2-1"></span>space and employs a decoder to translate the result, the processed latent code, back to pixel space.

This application of diffusion in latent space is essential for LD3M, as we want to optimize the latent codes that generate synthetic images. LD3M directly exploits two main components of the LDM: the denoising U-Net and the decoder, which translates the latent code to image space. The encoder will be used once to instantiate each class's initial latent codes.

## 2.3. Dataset Distillation with Generative Models

The Generative Latent Distillation (GLaD) incorporates a deep generative prior to the distillation process, serving as a form of regularization [\[8\]](#page-8-1). This is achieved by optimizing the latent codes of a pre-trained generative model instead of directly focusing on the raw pixel values. GLaD uses StyleGAN-XL [\[57\]](#page-9-6), a large-scale and resource-costly GAN as the pre-trained generator.

Like most distillation methods, it initializes its synthetic samples with latent codes of real images. Distilling the latent codes of an image in StyleGAN-XL is similar to a pseudoinversion task, connecting to the broader research domain of GAN inversion  $[5, 72]$  $[5, 72]$  $[5, 72]$ . The central concept involves mapping an actual input image into GAN latent codes, where selecting a particular latent code balances expressiveness and reconstruction fidelity, as evidenced in prior research [\[62,](#page-10-7)[74\]](#page-10-8). The cost-intensive GAN inversion process is a significant drawback compared to our method LD3M, as LD3M can initialize the initial latent codes by straightforwardly applying a pre-trained encoder.

Another major drawback is the selection of the various latent spaces StyleGAN-XL offers. The authors discovered that distilling into even the more expansive  $W^+$  latent space proved overly constraining [\[1\]](#page-8-16). This latent space limited synthetic samples to realistic pre-trained images. However, synthetic samples do not necessitate realism. As a result, the authors utilized "Fn" spaces, an alternative method to allow a more diverse and flexible set of images generated by the latent codes  $[51, 73]$  $[51, 73]$  $[51, 73]$ . This technique focuses on refining the latent representation of the  $n$ -th hidden layer within the StyleGAN-XL's "synthesis" network and concurrently adjusting all subsequent  $W^+$  modulation codes. Building upon this, concurrent work, such as Zhong et al. [\[71\]](#page-10-10), undertake an extensive greedy search spanning different hierarchical latent spaces of StyleGAN-XL. In contrast, LD3M offers a single latent code space efficient enough to express high-quality synthetic images.

## 3. LD3M

In this work, we introduce a novel method called Latent Dataset Distillation with Diffusion Models (LD3M) that leverages the generative power of diffusion models for highresolution image distillation. As in GLaD, we focus on

<span id="page-2-0"></span>Image /page/2/Figure/8 description: This is a diagram illustrating a generative model. On the top left, a 'Distilled Conditioning Code' labeled 'c' is shown. Below it, a 'Distilled Latent Code' labeled 'z' feeds into a 'Forward Diffusion Process' which outputs 'zT'. This 'zT' and the conditioning code 'c' are fed into a 'Denoising U-Net' which undergoes (T-1) iterations, outputting 'z0'. 'z0' then goes into a 'Decoder' which produces a 'Synthetic Image'. In parallel, 'Real Images' are fed into a training process, resulting in a model that is compared for 'Similar Test Performance' with another model trained on 'Synthetic Images'.

Figure 1. Overview of the LD3M framework. Two components influence the generation of the synthetic images: The distilled latent codes and the distilled conditioning codes. The distilled latent codes are perturbated via Gaussian noise to the initial state  $z_T$ . Next, it is iteratively denoised  $(T - 1)$  times with the pre-trained denoising U-Net of the LDM. Within each computation of the intermediate state  $z_t$ , we add a linearly decreasing influence of  $z_T$  to allow an enhanced gradient flow to the distilled latent codes while making the conditioning also learnable. The pre-trained decoder translates the final latent code  $z_0$  back to pixel space. Note that LD3M can be used with any existing distillation algorithm, e.g., DC, DM, or MTT.

dataset distillation in the latent space to solve the generalization problem of unseen architectures and high-resolution images, i.e., 256x256. Nonetheless, instead of using a large StyleGAN-XL as a backbone, we will refer to a lighter and more expressive generative model, namely a diffusion model [\[13,](#page-8-17) [44,](#page-9-13) [59\]](#page-9-14). In more detail, we exploit a pre-trained Latent Diffusion Model (LDM) that maps the latent space to image space without fine-tuning  $[43, 54, 65]$  $[43, 54, 65]$  $[43, 54, 65]$  $[43, 54, 65]$  $[43, 54, 65]$ . Since LDM is a conditional diffusion model, the generative process contains two crucial elements: the latent codes and their associated conditioning code. Therefore, the challenge is to find the latent code and the conditioning code to generate the distilled dataset. In the following, we will explain the sampling process, which takes the latent and conditioning code as input and generates the synthetic image. Next, we describe how we initialize the latent codes before dataset distillation and, finally, how we use gradient checkpointing to reduce VRAM consumption. A general overview of our approach is shown in [Figure 1.](#page-2-0)

#### 3.1. Sampling Process

We use a pre-trained LDM without fine-tuning. Thus, we focus primarily on the backward diffusion process  $p$ , which starts from an initial state  $z_T$ , usually Gaussian noise  $\varepsilon \in \mathbb{C}$  $\mathcal{N}(\mathbf{0}, \mathbf{I})$ , and performs the inference conditioned on c, which is in our case a pre-trained embedding of the class label [\[43,](#page-9-9) [65\]](#page-10-6). The forward diffusion process is only needed to derive  $z_T$  from the learned and distilled latent representations  $\mathcal{Z}$ . Therefore, we can view  $z_T$  as the distorted representation of  $Z$ , which the LDM refines. We approximate  $p$  with a <span id="page-3-2"></span>parameterized time-conditional process  $p_{\theta}$ , such that

$$
p_{\theta}(\mathbf{z}_{0:T}|\mathbf{c}) = p(\mathbf{z}_T) \prod_{t=1}^T p_{\theta}(\mathbf{z}_{t-1}|\mathbf{z}_t, \mathbf{c}) \qquad (1)
$$

$$
p(\mathbf{z}_T) = \mathcal{N}(\mathbf{z}_T \mid \mathbf{0}, \mathbf{I})
$$
 (2)

$$
p_{\theta}(\mathbf{z}_{t-1}|\mathbf{z}_t, \mathbf{c}) = \mathcal{N}(\mathbf{z}_{t-1} | \mu_{\theta}(\mathbf{c}, \mathbf{z}_t, \gamma_t), \sigma_t^2 \mathbf{I}).
$$
 (3)

As standard in the literature [\[34,](#page-9-15) [56\]](#page-9-16), we predict the parameterized mean by subtracting the scaled noise between two subsequent time steps with

$$
\mu_{\theta}(\mathbf{c}, \mathbf{z}_t, \gamma_t) = \frac{1}{\sqrt{\alpha_t}} \left( \mathbf{z}_t - \frac{1 - \alpha_t}{\sqrt{1 - \gamma_t}} f_{\theta} \left( \mathbf{c}, \mathbf{z}_t, \gamma_t \right) \right), \tag{4}
$$

where  $f_{\theta}$  (**c**,  $z_t$ ,  $\gamma_t$ ) is the noise prediction of  $\varepsilon_t$  with a timeconditional U-Net at time step  $t$ . Together with the variance  $\sigma_t^2$ , we can calculate the subsequent state  $z_{t-1}$  via

<span id="page-3-0"></span>
$$
\mathbf{z}_{t-1} \leftarrow \mu_{\theta}(\mathbf{c}, \mathbf{z}_t, \gamma_t) + \sigma_t^2 \varepsilon_t.
$$
 (5)

For dataset distillation, we make the embedded conditioning c as well as the latent code  $Z$  leading to the initial state  $z_T$  learnable. Since the LDM is not trained from scratch, only modifying the condition state c is insufficient and does not allow the LDM enough freedom to generate expressive synthetic images, which necessitates the need to make  $Z$ learnable  $[33, 50]$  $[33, 50]$  $[33, 50]$ . We will show this quantitatively in the experiments section. Our initial experiments indicate that making only the conditioning c of the LDM learnable predominantly produces images resembling real data.

A significant challenge in making the latent code  $\mathcal{Z}$ , which leads to the initial state  $z_T$ , learnable arises from the numerous time steps during the backward diffusion process. For instance, LDM employs  $T = 200$  steps during training. This extensive computation chain leads to vanishing gradients for  $z_T$ , impeding an effective distillation [\[27\]](#page-8-18). We modify [Equation 5](#page-3-0) to counteract this by including residual connections in the computational graph, thereby enhancing gradient flow. Specifically, we integrate the initial state  $z_T$ into the computation of the intermediate states  $z_t$ . Additionally, we systematically diminish its influence as  $t$  approaches 0. This adjustment ensures an enhanced gradient propagation crucial for the generation of diverse and representative synthetic latent codes  $\mathcal{Z}$ :

<span id="page-3-1"></span>
$$
\mathbf{z}_{t-1} \leftarrow \left( (1 - \frac{t}{T}) \cdot \mu_{\theta}(\mathbf{c}, \mathbf{z}_t, \gamma_t) + \frac{t}{T} \cdot \mathbf{z}_T \right) + \sigma_t^2 \varepsilon_t.
$$
 (6)

Note that this reformulation is not bound to LDMs and can be used by any diffusion model type for future work. However, we focused on LDM because of several advantages: (a) there exists a pre-trained model, (b) it is foundational for diffusion models (especially in exploiting latent space), and (c) it serves as a proof of concept.

#### 3.2. Initializing Latent Codes

We initialize the synthetic images with random samples representing the respective class. More specifically, we derive the latent codes leading to the selected random images. We share this challenge with GLaD, which uses StyleGAN-XL before applying dataset distillation algorithms. GLaD uses GAN-inversion techniques to solve this problem, which is time-consuming. To obtain the latent code for a GAN, one has to solve the optimization problem  $\mathcal{Z}^* = \arg \min_{\mathcal{Z}} \mathcal{L}(y, G(\mathcal{Z}))$ . A solution can be optimization-based, learning-based, or hybrid-based, all needing careful adjustments and testing [\[8,](#page-8-1) [64\]](#page-10-11). Moreover, finding solutions is time-consuming as an iterative sampling of possible  $\mathcal{Z}^*$  or extra training is required.

In contrast, LD3M can initialize the latent codes straightforwardly by applying the pre-trained encoder to the random images. Furthermore, LD3M initializes the embedded condition information by applying the pre-trained embedding network. As a result, initialization with LD3M is more straightforward and computationally more efficient than GLaD and omits careful adjustments before applying dataset distillation.

#### 3.3. Memory Saving via Checkpointing

Due to the substantial VRAM requirements for the forward pass in modern generative models, a straightforward use is not feasible with limited GPUs [\[20\]](#page-8-3). Thus, like GLaD [\[8\]](#page-8-1), we employ gradient checkpointing [\[9\]](#page-8-19).

At each distillation iteration, we generate the synthetic images  $S = G(\mathcal{Z})$  without gradients. Then, we calculate the distillation loss  $\mathcal L$  and the gradients for the synthetic images  $(\partial \mathcal{L}/\partial \mathcal{S})$ . Next, we delete the computation graph and its gradient. To compute  $\partial \mathcal{L}/\partial \mathcal{Z}$ , we recompute the forward pass through  $G, \mathcal{S} = G(\mathcal{Z})$ , this time tracking gradients such to obtain  $\partial S/\partial Z$ . The application of the chain rule delivers  $\partial \mathcal{L}/\partial \mathcal{Z} = (\partial \mathcal{L}/\partial \mathcal{S})(\partial \mathcal{S}/\partial \mathcal{Z})$  which is used to update the latent codes and the conditioning.

Since our generative model  $G$  applies multiple diffusion steps, we also employ checkpointing within  $G(\mathcal{Z})$  for each noise prediction  $\epsilon_t$  of the U-Net. It saves  $\partial z_t/\partial z_{t+1}$ . The application of the chain rule leads to

$$
\partial \mathcal{L}/\partial \mathcal{Z} = (\partial \mathcal{L}/\partial \mathbf{z}_0) \cdot \prod_{t=1}^T \frac{\partial \mathbf{z}_{t-1}}{\partial \mathbf{z}_t} \cdot (\partial \mathbf{z}_T/\partial \mathcal{Z}) \tag{7}
$$

Similarly, the condition information c is derived by

$$
\partial \mathcal{L}/\partial \mathbf{c} = (\partial \mathcal{L}/\partial \mathbf{z}_0) \cdot \prod_{t=1}^T \frac{\partial \mathbf{z}_{t-1}}{\partial \mathbf{z}_t} \cdot (\partial \mathbf{z}_T/\partial \mathbf{c}) \qquad (8)
$$

#### 4. Experiments

We follow a similar setup as Cazenavette et al. [\[8\]](#page-8-1) and evaluate the cross-architecture performance for IPC=1 (MTT,

<span id="page-4-1"></span><span id="page-4-0"></span>

| Distil. Space | Alg. |                 |                 |                 |                 |                 |                 |                 |                 |                 |                 |  |
|---------------|------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|-----------------|--|
|               |      | ImNet-A         | ImNet-B         | ImNet-C         | ImNet-D         | ImNet-E         | ImNette         | ImWoof          | ImNet-Birds     | ImNet-Fruits    | ImNet-Cats      |  |
| Pixel         | MTT  | $33.4 sup 1.5$ | $34.0 sup 3.4$ | $31.4 sup 3.4$ | $27.7 sup 2.7$ | $24.9 sup 1.8$ | $24.1 sup 1.8$ | $16.0 sup 1.2$ | $25.5 sup 3.0$ | $18.3 sup 2.3$ | $18.7 sup 1.5$ |  |
|               | DC   | $38.7 sup 4.2$ | $38.7 sup 1.0$ | $33.3 sup 1.9$ | $26.4 sup 1.1$ | $27.4 sup 0.9$ | $28.2 sup 1.4$ | $17.4 sup 1.2$ | $28.5 sup 1.4$ | $20.4 sup 1.5$ | $19.8 sup 0.9$ |  |
|               | DM   | $27.2 sup 1.2$ | $24.4 sup 1.1$ | $23.0 sup 1.4$ | $18.4 sup 0.7$ | $17.7 sup 0.9$ | $20.6 sup 0.7$ | $14.5 sup 0.9$ | $17.8 sup 0.8$ | $14.5 sup 1.1$ | $14.0 sup 1.1$ |  |
| GLaD          | MTT  | $39.9 sup 1.2$ | $39.4 sup 1.3$ | $34.9 sup 1.1$ | $30.4 sup 1.5$ | $29.0 sup 1.1$ | $30.4 sup 1.5$ | $17.1 sup 1.1$ | $28.2 sup 1.1$ | $21.1 sup 1.2$ | $19.6 sup 1.2$ |  |
|               | DC   | $41.8 sup 1.7$ | $42.1 sup 1.2$ | $35.8 sup 1.4$ | $28.0 sup 0.8$ | $29.3 sup 1.3$ | $31.0 sup 1.6$ | $17.8 sup 1.1$ | $29.1 sup 1.0$ | $22.3 sup 1.6$ | $21.2 sup 1.4$ |  |
|               | DM   | $31.6 sup 1.4$ | $31.3 sup 3.9$ | $26.9 sup 1.2$ | $21.5 sup 1.0$ | $20.4 sup 0.8$ | $21.9 sup 1.1$ | $15.2 sup 0.9$ | $18.2 sup 1.0$ | $20.4 sup 1.6$ | $16.1 sup 0.7$ |  |
| LD3M          | MTT  | $40.9 sup 1.1$ | $41.6 sup 1.7$ | $34.1 sup 1.7$ | $31.5 sup 1.2$ | $30.1 sup 1.3$ | $32.0 sup 1.3$ | $19.9 sup 1.2$ | $30.4 sup 1.5$ | $21.4 sup 1.1$ | $22.1 sup 1.0$ |  |
|               | DC   | $42.3 sup 1.3$ | $42.0 sup 1.1$ | $37.1 sup 1.8$ | $29.7 sup 1.3$ | $31.4 sup 1.1$ | $32.9 sup 1.2$ | $18.9 sup 0.6$ | $30.2 sup 1.4$ | $22.6 sup 1.3$ | $21.7 sup 0.8$ |  |
|               | DM   | $35.8 sup 1.1$ | $34.1 sup 1.0$ | $30.3 sup 1.2$ | $24.7 sup 1.0$ | $24.5 sup 0.9$ | $26.8 sup 1.7$ | $18.1 sup 0.7$ | $23.0 sup 1.8$ | $24.5 sup 1.9$ | $17.0 sup 1.1$ |  |

Table 1. Cross-architecture performance with 1 image per class on all ImageNet (128×128) subsets. We used the unseen architectures AlexNet, VGG11, ResNet18, and ViT. The average performance of these models was then evaluated on actual validation sets. Using LDM as a deep generative prior markedly enhanced the ability of all tested methods to generalize across various architectures in all the datasets examined. In 9 out of 10 subsets (marked with blue), LD3M reaches the best overall performance. The best performance within one distillation algorithm is marked in bold in each subset, primarily achieved by LD3M.

DC, DM) and IP=10 (DC, DM) with image size  $128 \times 128$ . Furthermore, we run a high-resolution dataset distillation evaluation with DC and image size  $256 \times 256$  for IPC=1 as well as a low-resolution dataset distillation evaluation on CIFAR-10. Finally, we compare the results visually with GLaD and analyze the impact of different latent and conditioning code initializations, various time steps and different components of LD3M. We maintain a consistent set of hyper-parameters across experiments to guarantee a fair comparison (more details in the appendix).

## 4.1. Setup

Datasets. We evaluate the mean and standard deviation of the accuracy on 10-class subsets of ImageNet-1k [\[12\]](#page-8-20). The 10-class subsets were derived from previous dataset distillation work, i.e. ImageNet-Birds, ImageNet-Fruits, and ImageNet-Cats [\[6\]](#page-8-10). In addition, we employ two other commonly used subsets, namely ImageNette and Image-Woof [\[28\]](#page-8-21). Lastly, the remaining used subsets are based on the evaluation performance of a ResNet-50 model (pretrained on ImageNet) [\[8\]](#page-8-1). These subsets are ImageNet-A, consisting of the top-10 classes; ImageNet-B, consisting of the next 10 and so on for ImageNet-C, ImageNet-D, and ImageNet-E. More details can be found in the supplementary materials.

Evaluation Protocol. We first distill synthetic datasets using the designated algorithms and then assess the quality of the datasets by measuring the performance across unseen network architectures. To evaluate a synthetic dataset with a specific architecture, we train a new network from scratch on the distilled dataset and subsequently test its performance on the validation set. This entire process is replicated five times, with the report including the mean validation accuracy plus or minus one standard deviation.

Network Architectures. As in GLaD [\[8\]](#page-8-1) and prior dataset distillation work  $[11, 47, 48]$  $[11, 47, 48]$  $[11, 47, 48]$  $[11, 47, 48]$  $[11, 47, 48]$ , we use the ConvNet-5 and ConvNet-6 architecture to distill the  $128 \times 128$  and  $256 \times 256$  datasets, respectively [\[21\]](#page-8-23). Similarly, we use AlexNet [\[32\]](#page-8-24), VGG-11 [\[58\]](#page-9-21), ResNet-18 [\[24\]](#page-8-25), and a Vision Transformer [\[15\]](#page-8-26) for evaluating unseen architectures.

Latent Diffusion Model. For the diffusion model, we use the ImageNet pre-trained LDM [\[54\]](#page-9-11), which guarantees a fair comparison with the ImageNet pre-trained StyleGAN-XL used in GLaD [\[8\]](#page-8-1). We use the pre-trained autoencoder with  $2\times$  compression of the LDM without further adjustments to compress into and decompress out of the latent space. We employ for all experiments a max. time step  $T = 10$  for image size  $128 \times 128$  and  $T = 20$  for  $256 \times 256$ . Besides this hyper-parameter, no adjustment to the pre-trained LDM was necessary. In addition, we want to highlight that using a preexisting diffusion model is not a limitation but an advantage, showing that a pre-trained diffusion model can be used out of the box (and empirically more consistent as GLaD w.r.t. various experiments).

## 4.2. Results

Cross-Architecture Evaluation, IPC=1. Our method LD3M consistently surpasses the performance of GLaD on unseen architectures for IPC=1 (see [Table 1\)](#page-4-0). Following [\[8\]](#page-8-1), we test all ten aforementioned subsets of ImageNet and apply the distillation algorithms MTT, DC, and DM. The enhanced performance of our LD3M is consistent across most subsets, except for slight deviations in MTT on ImageNet-C and DC on ImageNet-B. Notably, these exceptions still closely align with the original performance metrics and fall within the standard deviation range, underlining the robustness of our approach. The enhancement in performance is most pronounced when employing the DM distillation algorithm. LD3M with LDM outperforms its GAN-based GLaD counterpart by approximately four p.p. across most subsets. This significant improvement underscores the effectiveness of LDMs in capturing complex data distributions in synthetic images more effectively than StyleGAN-XL in HR image distillation and further improves the gap between pixel-based

<span id="page-5-4"></span><span id="page-5-0"></span>

| Distil. Space Alg. |           | All                          |                                                                                                             | ImNet-A ImNet-B ImNet-C ImNet-D |                                                        |                              | ImNet-E                        |
|--------------------|-----------|------------------------------|-------------------------------------------------------------------------------------------------------------|---------------------------------|--------------------------------------------------------|------------------------------|--------------------------------|
| Pixel              | DC.<br>DM | $42.3 + 3.5$<br>$44.4 + 0.5$ | $52.3 + 0.7$<br>$52.6 + 0.4$                                                                                | $50.6 + 0.5$                    | $45.1 + 8.3$ $40.1 + 7.6$ $36.1 + 0.4$<br>$47.5 + 0.7$ | $35.4 + 0.4$                 | $38.1 + 0.4$<br>$36.0 + 0.5$   |
| GLaD               | DC.<br>DM |                              | $45.9 \pm 1.0$ $53.1 \pm 1.4$ $50.1 \pm 0.6$<br>$45.8 \pm 0.6$ $52.8 \pm 1.0$ $51.3 \pm 0.6$ $49.7 \pm 0.4$ |                                 | $48.9 \pm 1.1$                                         | $38.9 + 1.0$<br>$36.4 + 0.4$ | $38.4 \pm 0.7$<br>$38.6 + 0.7$ |
| LD3M               | DC.<br>DM | $47.1 + 1.2$                 | $55.2 + 1.0$<br>$47.3 + 2.1$ $57.0 + 1.3$ $52.3 + 1.1$                                                      | $51.8 + 1.4$                    | $49.9 + 1.3$<br>$48.2 + 4.9$                           | $39.5 + 1.0$<br>$39.5 + 1.5$ | $39.0 \pm 1.3$<br>$39.4 + 1.8$ |

Table 2. Cross-architecture performance with 10 images per class on the subsets ImageNet A to E. LD3M (marked with blue) achieved the best performance per subset. The best performance within one distillation algorithm is marked in bold in each subset, primarily achieved by LD3M.

<span id="page-5-1"></span>

| <b>Distillation Space</b> | All            | ImNet-A      | $ImNet-B$    | $ImNet-C$      | ImNet-D      | ImNet-E      |
|---------------------------|----------------|--------------|--------------|----------------|--------------|--------------|
| Pixel                     | $29.5 + 3.1$   | $38.3 + 4.7$ | $32.8 + 4.1$ | $27.6 + 3.3$   | $25.5 + 1.2$ | $23.5 + 2.4$ |
| GLaD (ImageNet)           | $34.4 + 2.6$   | $37.4 + 5.5$ | $41.5 + 1.2$ | $35.7 + 4.0$   | $27.9 + 1.0$ | $29.3 + 1.2$ |
| GLaD (Random)             | $34.5 + 1.6$   | $39.3 + 2.0$ | $40.3 + 1.7$ | $35.0 + 1.7$   | $27.9 + 1.4$ | $29.8 + 1.4$ |
| GLaD (FFHO)               | $34.0 + 2.1$   | $38.3 + 5.2$ | $40.2 + 1.1$ | $34.9 + 1.1$   | $27.2 + 0.9$ | $29.4 + 2.1$ |
| LD3M (ImageNet)           | $36.3 \pm 1.6$ | $42.1 + 2.2$ | $42.1 + 1.5$ | $35.7 + 1.7$   | $30.5 + 1.4$ | $30.9 + 1.2$ |
| LD3M (Random)             | $36.5 \pm 1.6$ | $42.0 + 2.0$ | $41.9 + 1.7$ | $37.1 \pm 1.4$ | $30.5 + 1.5$ | $31.1 + 1.4$ |
| LD3M (FFHO)               | $36.3 + 1.5$   | $42.0 + 1.6$ | $41.9 + 1.6$ | $36.5 + 2.2$   | $30.5 + 0.9$ | $30.6 + 1.1$ |

Table 3.  $256 \times 256$  distilled HR images using the DC distillation algorithm and IPC=1. For both scenarios (LDM and GAN), we evaluate pre-trained generators on ImageNet [\[12\]](#page-8-20), FFHQ [\[29\]](#page-8-27), and randomly initialized. The best overall performance for each subset is marked in bold; the top 3 performances are in blue.

<span id="page-5-2"></span>

| Dist. Method | Dist. Space             | AlexNet                  | ResNet18                 | VGG11                    | ViT                      | Average                  |
|--------------|-------------------------|--------------------------|--------------------------|--------------------------|--------------------------|--------------------------|
| DC           | Pixel                   | 25.9 $\pm$ 0.2           | 27.3 $\pm$ 0.5           | 28.0 $\pm$ 0.5           | 22.9 $\pm$ 0.3           | 26.0 $\pm$ 0.4           |
| DC           | GLaD (Rand G)           | <b>30.1</b> $\pm$ 0.5    | 27.3 $\pm$ 0.2           | 28.0 $\pm$ 0.9           | 21.2 $\pm$ 0.6           | 26.6 $\pm$ 0.5           |
| DC           | GLaD (Trained G)        | 26.0 $\pm$ 0.7           | <b>27.6</b> $\pm$ 0.6    | 28.2 $\pm$ 0.6           | 23.4 $\pm$ 0.2           | 26.3 $\pm$ 0.5           |
| DC           | <b>LD3M</b> (Trained G) | 27.2 $\pm$ 0.8           | 26.6 $\pm$ 0.9           | <b>31.5</b> $\pm$ 0.3    | <b>29.0</b> $\pm$ 0.2    | <b>28.6</b> $\pm$ 0.6    |
| DM           | Pixel                   | 22.9 $\pm$ 0.2           | 22.2 $\pm$ 0.7           | 23.8 $\pm$ 0.5           | 21.3 $\pm$ 0.5           | 22.6 $\pm$ 0.5           |
| DM           | GLaD (Rand G)           | 23.7 $\pm$ 0.3           | 21.7 $\pm$ 1.0           | 24.3 $\pm$ 0.8           | 21.4 $\pm$ 0.2           | 22.8 $\pm$ 0.6           |
| DM           | GLaD (Trained G)        | 25.1 $\pm$ 0.5           | <b>22.5</b> $\pm$ 0.7    | 24.8 $\pm$ 0.8           | 23.0 $\pm$ 0.1           | 23.8 $\pm$ 0.5           |
| DM           | <b>LD3M</b> (Trained G) | 27.2 $\pm$ 0.4           | 17.0 $\pm$ 0.7           | 25.4 $\pm$ 0.4           | 23.8 $\pm$ 0.3           | 23.4 $\pm$ 0.5           |
| Dist. Method | Dist. Space             | AlexNet                  | ResNet18                 | VGG11                    | ViT                      | Average                  |
| <b>MTT</b>   | Gauss. noise            | 28.7 $	extpm$ 1.6        | 34.1 $	extpm$ 1.5        | 32.2 $	extpm$ 0.6        | 29.1 $	extpm$ 1.8        | 31.0 $	extpm$ 1.4        |
|              | Random image            | <b>30.1</b> $	extpm$ 1.4 | <b>35.6</b> $	extpm$ 1.4 | <b>32.2</b> $	extpm$ 0.3 | <b>30.0</b> $	extpm$ 1.2 | <b>32.0</b> $	extpm$ 1.3 |
| <b>DC</b>    | Gauss. noise            | 13.1 $	extpm$ 1.5        | 11.6 $	extpm$ 1.8        | 13.8 $	extpm$ 2.2        | 13.7 $	extpm$ 2.7        | 13.1 $	extpm$ 2.1        |
|              | Random image            | <b>31.6</b> $	extpm$ 1.3 | <b>30.4</b> $	extpm$ 0.6 | <b>31.8</b> $	extpm$ 1.2 | <b>37.7</b> $	extpm$ 1.5 | <b>32.9</b> $	extpm$ 2.1 |
| <b>DM</b>    | Gauss. noise            | 13.4 $	extpm$ 2.0        | 12.4 $	extpm$ 1.8        | 13.4 $	extpm$ 1.4        | 14.4 $	extpm$ 2.1        | 13.4 $	extpm$ 1.8        |
|              | Random image            | <b>31.9</b> $	extpm$ 1.3 | <b>23.2</b> $	extpm$ 2.2 | <b>25.9</b> $	extpm$ 2.0 | <b>26.1</b> $	extpm$ 1.4 | <b>26.8</b> $	extpm$ 1.7 |

Table 4. CIFAR-10 Performance on unseen architectures, IPC=1.

and latent-based dataset distillation.

Cross-Architecture Evaluation, IPC=10. Further supporting our findings, [Table 2](#page-5-0) illustrates the superior performance of LD3M over GLaD in scenarios with IPC=10 using the distillation algorithms DC and DM on ImageNet A to E. The trend observed is similar to the IPC=1 scenario, with LD3M demonstrating a clear advantage over GLaD. The exception of DM on ImageNet-C is marginal and still outperforms pixel-based distillation, but also shows that the application of LDM for IPC=10 has the most significant improvements in DM compared to DC, e.g., ca. 4 p.p. improvement on ImageNet-A. LD3M reaches an overall average accuracy of 47.08 % with DC and 47.28 % with DM compared to 45.88 % and 45.76 % of GLaD. This experiment highlights the consistency and reliability of the LDM approach across different settings.

**High-Resolution.** In the context of  $256 \times 256$  data, as shown in [Table 3,](#page-5-1) a similar pattern of improved performance is evident. LD3M achieved all top-3 results per subset. To explore the versatility of the LDMs that can be used in LD3M,

<span id="page-5-3"></span>

Table 5. Performance with different initializations of the latent and conditioning codes per class on ImageNette for the IPC=1 setting with 5,000 iterations. MTT with random initialization reaches comparable performance (still under initialization with randomly selected images and their labels), whereas random initialization leads to synthetic datasets with significantly reduced quality for DC and DM.

we experimented with various training configurations, including models pre-trained on ImageNet, FFHQ, and a randomly initialized model (no pre-training). Consistent with the findings using GANs, these varied LDM configurations demonstrated improved cross-architectural generalization compared to direct pixel-space distillation.

**CIFAR-10.** Like Cazenavette et al.  $[8]$ , we evaluated LD3M on CIFAR-10 in [Table 4.](#page-5-2) While it presents a less challenging task (lower resolution), we observe that LD3M is still on par with DM while significantly better with DC.

Initializations. In [Table 5,](#page-5-3) we investigate the influence of different initializations of the latent and conditioning code, which was either Gaussian noise or initialized with a randomly selected image and its label of the respective class. As a result, we can see that the performance of LD3M benefits from real images, especially for the distillation algorithms DM and DC, while MTT seems more robust.

**Visual Comparison.** As presented in [Figure 2,](#page-6-0) visual comparisons depict the results of GLaD and the LD3M. These comparisons reveal that the generated images by LD3M are notably more abstract and exhibit higher contrast than those produced by GLaD. This highlights a more flexible learning space for dataset distillation algorithms.

[Figure 3](#page-6-1) shows high-resolution distillation ( $256 \times 256$ ) under different initializations. Again, LD3M produces notably more abstract images with higher contrast. Surprisingly, LD3M generates more stable images under different initializations, as GLaD's generated images vary more. GLaD's generation becomes more noisy towards random initialization (see from left to right). Additional visualizations for qualitative examination of all other experiments are provided in the supplemental materials of this work.

Diffusion Steps Analysis. Our investigation on the tradeoff between runtime and accuracy is shown in [Figure 4.](#page-6-2) It illustrates the diffusion steps versus the accuracy of our LDM-based distillation method across different maximum time steps  $T$  in the diffusion process, specifically evaluated on the ImageNet A-E datasets using the MTT algorithm. There is a notable increase in accuracy for setting the maxi-

<span id="page-6-0"></span>Image /page/6/Figure/0 description: This image displays a grid of images comparing two different generative models, GLaD and LD3M. The top row, labeled GLaD, shows clear images of various objects and animals, including a person holding a fish, an English Springer Spaniel, a cassette player, a chainsaw, a church, a French horn, a garbage truck, a gas pump, a golf ball, and a parachute. The bottom row, labeled LD3M, shows blurry and distorted versions of the same subjects, corresponding to the labels below each column: Tench, English Springer, Cassette Player, Chainsaw, Church, French Horn, Garbage Truck, Gas Pump, Golf Ball, and Parachute.

Figure 2. Visual comparison of LD3M versus GLaD for 1000 iterations with MTT on ImageNette. LD3M produces more abstract and contrast-richer synthetic images.

<span id="page-6-1"></span>Image /page/6/Figure/2 description: This image displays a grid of six generated images of a rainbow lorikeet, alongside a reference image of the same bird. The grid is organized into two rows and three columns. The top row is labeled "GLaD" and shows three images: the first is a stylized, geometric representation of the bird, while the second and third are progressively more abstract and pixelated versions. The bottom row is labeled "LD3M" and also shows three images, all of which are abstract and pixelated representations of the bird. The columns are labeled "ImageNet", "FFHQ", and "Random" from left to right. To the right of the generated images is the "Reference Image (from orig. data)", which is a clear, close-up photograph of a rainbow lorikeet's head and chest, showing its vibrant blue, green, yellow, and red plumage.

Figure 3. Example  $256 \times 256$  images of a distilled class (ImageNet-B: Lorikeet) with differently initialized generators GLaD and LD3M. The various initializations, i.e., which dataset was used for training the generators, are denoted at the bottom.

mum time step up to 40 for the diffusion process. However, performance is significantly declining as we progress beyond 40. An example is shown in [Figure 5](#page-6-3) for the freight car class from ImageNet-C. This decline could be attributed to vanishing gradients during the diffusion process, suggesting that strategies other than the linear addition of the initial state could avoid the performance decline.

Overall, LD3M with  $T = 20$  needs 574 minutes versus 693 minutes for GLaD. Furthermore, on an A100-40GB, LD3M with  $T = 20$  needs 73.58% max. GPU allocation / 29.4GB vs GLaD with 77.94% max. GPU allocation / 31.2GB. Also, LD3M can distill with less memory by adjusting the number of diffusion steps (i.e., from  $T = 2$  to  $T = 35$ ), which is not doable with GLaD (fixed GAN). The ability to tailor the diffusion process according to specific accuracy and runtime opens up new possibilities for dataset distillation across various computational settings. It allows for strategically allocating computational resources, ensuring the models are accurate and practically viable for real-world applications. While dataset distillation remains computationally expensive, LD3M introduces a flexible and significantly lighter alternative to GLaD.

Ablation Study. In our initial experiments, we evaluated the impact of incorporating different aspects of the LDM, as described in our methodology section and shown in [Ta](#page-6-4)[ble 6.](#page-6-4) We used LD3M on ImageNette and MTT for 1,000 distillation steps and an Image Per Class (IPC) of 1. We

<span id="page-6-2"></span>Image /page/6/Figure/7 description: This is a line graph showing the relationship between Max. Time Steps on the x-axis and Accuracy [%] on the left y-axis, and Distillation Time [s] on the right y-axis. The graph displays two main lines: a blue line labeled "LD3M" representing accuracy, and a dashed blue line labeled "GLaD" also representing accuracy. The blue line for LD3M starts at approximately 35% accuracy at 2 Max. Time Steps, rises to a peak around 36.2% at 20 Max. Time Steps, and then gradually decreases to approximately 33.5% at 100 Max. Time Steps. The dashed blue line for GLaD is a constant 34.8% accuracy across all Max. Time Steps. The shaded blue area around the LD3M line indicates a confidence interval. A red line, labeled "LD3M" and "GLaD" (both referring to the same red line), represents Distillation Time, starting at 90 seconds at 2 Max. Time Steps and increasing linearly to 140 seconds at 100 Max. Time Steps. The graph indicates that LD3M accuracy is generally higher than GLaD accuracy for lower Max. Time Steps, but they converge around 75 Max. Time Steps. The distillation time increases steadily with Max. Time Steps for both methods.

Figure 4. Distilled dataset performance (average of ImageNet A-E via MTT and IPC=1) for different max. time steps employed in the diffusion process with mean and standard deviation (light blue dotted). It illustrates the trade-off between runtime and accuracy over increasing time steps. LD3M shows improved performance for the first 40 time steps, while the performance deteriorates for greater time steps. GLaD needs around 140 seconds per iteration on the same hardware (NVIDIA RTX3090) with 34.72 % average accuracy, shown as dotted lines. Best trade-off marked with "X".

<span id="page-6-3"></span>Image /page/6/Figure/9 description: The image displays a sequence of five images, each labeled with a time step T=20, T=40, T=60, T=80, and T=100. The images show a progression from a somewhat recognizable scene to abstract forms. The first two images (T=20 and T=40) appear to depict a crowd of people in front of buildings, with reflections on a wet surface below. As the time steps increase, the images become more abstract and distorted. The image at T=60 shows a dark figure with blue and red elements. The image at T=80 is predominantly black with some green and white. The final image at T=100 is a dark, elongated shape with yellow and green highlights.

Figure 5. Example images of a distilled class (Freight Car) for different time steps settings. We observe that distillation can collapse for higher time step settings (see the three images on the right).

<span id="page-6-4"></span>

| Method                      | All               | AlexNet           | VGG-11            | ResNet-18         | ViT               |
|-----------------------------|-------------------|-------------------|-------------------|-------------------|-------------------|
| GLaD                        | 26.6 ± 1.6        | 28.7 ± 0.3        | <b>29.2 ± 1.2</b> | <b>30.8 ± 2.9</b> | 17.8 ± 1.5        |
| LDM learn. conditioning (c) | 15.8 ± 1.5        | 14.2 ± 2.6        | 15.1 ± 1.6        | 16.5 ± 4.9        | 16.8 ± 4.0        |
| + learn. latent ( $z_T$ )   | 22.3 ± 2.0        | 22.8 ± 2.0        | 26.3 ± 0.9        | 23.4 ± 3.2        | 17.5 ± 2.0        |
| + interpolation (Eq. 6)     | <b>28.1 ± 3.3</b> | <b>29.2 ± 1.9</b> | <b>29.2 ± 1.2</b> | <b>30.6 ± 1.3</b> | <b>25.1 ± 1.7</b> |

Table 6. Comparison of different LDM variations with MTT on ImageNette for 1,000 distillation steps and IPC=1. Tested were the distillation of the LDM by making only the conditioning learnable, one LDM with also learnable initial latent representation  $({\bf z}_T)$ , and lastly, one LDM which incorporates the initial latent representation in the calculation of intermediate latent states.

also compared against GLaD and evaluated the results with unseen architectures. By applying only learnable conditioning, the resulting performance of LD3M is just roughly five p.p. above random chance. However, the real breakthrough was observed when we allowed the latent representation

<span id="page-7-0"></span>within the LD3M to be learnable. This adjustment significantly improved accuracy across all models but still lagged behind those achieved with GLaD. Another enhancement was implementing the modified formula for calculating the intermediate states  $z_t$  in the LDM. This involved incorporating the initial state  $z_T$  into the calculation of intermediate states  $z_t$ , enabling LD3M to surpass the GLaD performance.

To investigate the impact of the modified [Equation 6,](#page-3-1) we measured the gradient norms of the latent code during distillation for both cases, with and without the incorporation of the initial state  $z_T$ . The increase from the non-modified version to the modified version is approximately 66.87 %. This outcome emphasizes the importance of the modified formula for combating vanishing gradients.

## 5. Related Work

Diffusion Models vs GANs. In the study by Dhariwal et al., it was established that diffusion models exhibit superior performance in image generation tasks compared to GANs [\[13\]](#page-8-17). Since then, the concept of diffusion models disrupted various other related fields, such as image restoration [\[34,](#page-9-15)[39,](#page-9-22)[41,](#page-9-8)[55\]](#page-9-23), layout-to-image [\[70\]](#page-10-12), inpainting [\[36,](#page-9-24)[66\]](#page-10-13), medical imaging  $[45, 46]$  $[45, 46]$  $[45, 46]$  and more  $[2, 43, 44, 65]$  $[2, 43, 44, 65]$  $[2, 43, 44, 65]$  $[2, 43, 44, 65]$  $[2, 43, 44, 65]$  $[2, 43, 44, 65]$  $[2, 43, 44, 65]$ . This development of the superior performance of diffusion models over GANs across various fields inspired this work. Unlike GANs, diffusion models do not require extensive regularization and optimization strategies to mitigate issues like optimization instability and mode collapse [\[19\]](#page-8-29).

LDMs for GLaD. The closest work to ours is presented by Duan et al. [\[17\]](#page-8-30), which uses the pre-trained autoencoder structure of the LDM to derive latent codes in GLaD. As a result, they also utilized the straightforward encoding strategy by applying the encoder to initial images. A notable distinction, however, lies in their lack of the diffusion process. Thus, they do not exploit the benefits of diffusion models, effectively limiting the capability of the generator to that of a basic autoencoder. Additionally, the experimental setup in their study diverges from the experiments established in GLaD, presenting challenges in conducting a direct and fair comparative analysis. For instance, they evaluate their approach with more latent codes per class (LPC), defined by how many latent vectors would have the same storage as one IPC, i.e., 12 LPC for IPC=1 or 120 LPC for IPC=10. Compared to our evaluation and that of GLaD, we have one latent code for one IPC.

Diffusion Models in Dataset Distillation. Another notable mention is the work of Gu. et al.  $[22]$ , which proposed a Minimax Diffusion process for dataset distillation. While they present a promising hierarchical diffusion control mechanism to enable dataset distillation, it is another dataset distillation algorithm and, therefore, orthogonal to our work. Also, we enable a more straightforward mechanism by linearly adding the initial latent representation to

enable distillation. For future work, we see great potential in using our method before applying the Minimax Diffusion process, as LD3M can be applied with any dataset distillation method.

## 6. Limitations

While LD3M improves dataset distillation compared to GLaD, it is essential to acknowledge certain limitations. A primary concern arises from the linear addition in the diffusion process (see [Equation 6\)](#page-3-1), which may not combat the vanishing gradient problem for larger time steps, as observed in our experiments [\[27\]](#page-8-18). Further alternative strategies for integrating the initial state  $z_T$  in the diffusion process should be evaluated to address this issue, e.g., non-linear progress towards  $0$  as  $t$  approaches  $0$ . These alternative approaches could offer more nuanced and dynamic ways to manage the influence of  $z_T$  across different stages of the diffusion, potentially mitigating the problem of vanishing gradients and enhancing the overall efficacy of the distillation process.

## 7. Societal Impact

We focus on publishing a new generative model for dataset distillation. As such, the generation of synthetic images and their associated privacy benefits pose no immediate threat to individuals or organizations. Yet, the application of dataset distillation might have an environmental impact (e.g., using huge models for image distillation).

## 8. Conclusion and Future Work

In this work, we introduced LD3M, a novel latent dataset distillation approach. LD3M benefits from the generative capabilities of diffusion models for synthesizing highresolution images by introducing a modified formulation of the sampling process tailored for dataset distillation. Our results across diverse testing scenarios show that LD3M surpasses the state-of-the-art approach GLaD. In comparison, LD3M leads to enhanced image quality and faster distillation speed. Furthermore, LD3M allows fine-tuning the number of time steps to balance runtime and image quality. Moreover, the initialization of the latent code can be performed straightforwardly with the autoencoder applied to a random image of the respective class, which is an improvement over the GAN inversion necessary in GLaD. In conclusion, LD3M constitutes a transformative step in latent dataset distillation, broadening its applicability and enhancing its practicality.

For future work, other diffusion models should be investigated in LD3M, as well as alternative formulations to our modified equation (i.e., non-linear). Additionally, we will evaluate higher IPCs (e.g., 100, 200, 1000) and other scenarios such as Continual Learning and Privacy.

## Acknowledgment

This work was supported by the BMBF project SustainML (Grant 101070408).

## References

- <span id="page-8-16"></span>[1] Rameen Abdal, Yipeng Qin, and Peter Wonka. Image2stylegan: How to embed images into the stylegan latent space? In *Proceedings of the IEEE/CVF international conference on computer vision*, pages 4432–4441, 2019. [3](#page-2-1)
- <span id="page-8-28"></span>[2] Omer Bar-Tal, Lior Yariv, Yaron Lipman, and Tali Dekel. Multidiffusion: Fusing diffusion paths for controlled image generation. *openreview*, 2023. [8](#page-7-0)
- <span id="page-8-11"></span>[3] Manel Baradad Jurjo, Jonas Wulff, Tongzhou Wang, Phillip Isola, and Antonio Torralba. Learning to see by looking at noise. *NeurIPS*, 34:2556–2569, 2021. [1](#page-0-0)
- <span id="page-8-0"></span>[4] Samy Bengio, Krzysztof Dembczynski, Thorsten Joachims, Marius Kloft, and Manik Varma. Extreme classification (dagstuhl seminar 18291). In *Dagstuhl Reports*, volume 8. Schloss Dagstuhl-Leibniz-Zentrum fuer Informatik, 2019. [1](#page-0-0)
- <span id="page-8-12"></span>[5] Andrew Brock, Theodore Lim, James M Ritchie, and Nick Weston. Neural photo editing with introspective adversarial networks. *ICLR*, 2017. [2,](#page-1-0) [3](#page-2-1)
- <span id="page-8-10"></span>[6] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022. [1,](#page-0-0) [2,](#page-1-0) [5](#page-4-1)
- <span id="page-8-8"></span>[7] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Wearable imagenet: Synthesizing tileable textures via dataset distillation. In *CVPR*, 2022. [1](#page-0-0)
- <span id="page-8-1"></span>[8] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *CVPR*, pages 3739–3748, 2023. [1,](#page-0-0) [2,](#page-1-0) [3,](#page-2-1) [4,](#page-3-2) [5,](#page-4-1) [6](#page-5-4)
- <span id="page-8-19"></span>[9] Tianqi Chen, Bing Xu, Chiyuan Zhang, and Carlos Guestrin. Training deep nets with sublinear memory cost. *arXiv preprint arXiv:1604.06174*, 2016. [4](#page-3-2)
- <span id="page-8-5"></span>[10] Dominik Csiba and Peter Richtárik. Importance sampling for minibatches. *The Journal of Machine Learning Research*, 19(1):962–982, 2018. [1](#page-0-0)
- <span id="page-8-22"></span>[11] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, 2023. [5](#page-4-1)
- <span id="page-8-20"></span>[12] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009. [5,](#page-4-1) [6](#page-5-4)
- <span id="page-8-17"></span>[13] Prafulla Dhariwal and Alexander Nichol. Diffusion models beat gans on image synthesis. *Advances in neural information processing systems*, 34:8780–8794, 2021. [3,](#page-2-1) [8](#page-7-0)
- <span id="page-8-9"></span>[14] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *ICML*, 2022. [1](#page-0-0)
- <span id="page-8-26"></span>[15] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *ICLR*, 2020. [5](#page-4-1)

- <span id="page-8-2"></span>[16] Danny Driess, Fei Xia, Mehdi SM Sajjadi, Corey Lynch, Aakanksha Chowdhery, Brian Ichter, Ayzaan Wahid, Jonathan Tompson, Quan Vuong, Tianhe Yu, et al. Palm-e: An embodied multimodal language model. *arXiv preprint arXiv:2303.03378*, 2023. [1](#page-0-0)
- <span id="page-8-30"></span>[17] Yuxuan Duan, Jianfu Zhang, and Liqing Zhang. Dataset distillation in latent space. *arXiv preprint arXiv:2311.15547*, 2023. [8](#page-7-0)
- <span id="page-8-15"></span>[18] Patrick Esser, Robin Rombach, and Bjorn Ommer. Taming transformers for high-resolution image synthesis. In *CVPR*, pages 12873–12883, 2021. [2](#page-1-0)
- <span id="page-8-29"></span>[19] Stanislav Frolov, Tobias Hinz, Federico Raue, Jörn Hees, and Andreas Dengel. Adversarial text-to-image synthesis: A review. *Neural Networks*, 144:187–209, 2021. [8](#page-7-0)
- <span id="page-8-3"></span>[20] Deep Ganguli, Danny Hernandez, Liane Lovitt, Amanda Askell, Yuntao Bai, Anna Chen, Tom Conerly, Nova Dassarma, Dawn Drain, Nelson Elhage, et al. Predictability and surprise in large generative models. In *2022 ACM Conference on Fairness, Accountability, and Transparency*, 2022. [1,](#page-0-0) [4](#page-3-2)
- <span id="page-8-23"></span>[21] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *CVPR*, 2018. [5](#page-4-1)
- <span id="page-8-31"></span>[22] Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. *arXiv preprint arXiv:2311.15529*, 2023. [8](#page-7-0)
- <span id="page-8-6"></span>[23] Chengcheng Guo, Bo Zhao, and Yanbing Bai. Deepcore: A comprehensive library for coreset selection in deep learning. In *International Conference on Database and Expert Systems Applications*, pages 181–195. Springer, 2022. [1](#page-0-0)
- <span id="page-8-25"></span>[24] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, 2016. [5](#page-4-1)
- <span id="page-8-7"></span>[25] Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. Distilling the knowledge in a neural network. *NeurIPS Workshop*, 2015. [1](#page-0-0)
- <span id="page-8-14"></span>[26] Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising diffusion probabilistic models. *NeurIPS*, 2020. [2](#page-1-0)
- <span id="page-8-18"></span>[27] Sepp Hochreiter. The vanishing gradient problem during learning recurrent neural nets and problem solutions. *International Journal of Uncertainty, Fuzziness and Knowledge-Based Systems*, 6(02):107–116, 1998. [4,](#page-3-2) [8](#page-7-0)
- <span id="page-8-21"></span>[28] Jeremy Howard. A smaller subset of 10 easily classified classes from imagenet, and a little more french. *URL https://github. com/fastai/imagenette*, 2019. [5](#page-4-1)
- <span id="page-8-27"></span>[29] Tero Karras, Samuli Laine, and Timo Aila. A style-based generator architecture for generative adversarial networks. In *CVPR*, pages 4401–4410, 2019. [6](#page-5-4)
- <span id="page-8-4"></span>[30] Angelos Katharopoulos and François Fleuret. Not all samples are created equal: Deep learning with importance sampling. In *ICML*, pages 2525–2534. PMLR, 2018. [1](#page-0-0)
- <span id="page-8-13"></span>[31] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022. [2](#page-1-0)
- <span id="page-8-24"></span>[32] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Advances in neural information processing systems*, 25, 2012. [5](#page-4-1)

- <span id="page-9-17"></span>[33] Mingi Kwon, Jaeseok Jeong, and Youngjung Uh. Diffusion models already have a semantic latent space. *arXiv preprint arXiv:2210.10960*, 2022. [4](#page-3-2)
- <span id="page-9-15"></span>[34] Haoying Li, Yifan Yang, Meng Chang, Shiqi Chen, Huajun Feng, Zhihai Xu, Qi Li, and Yueting Chen. Srdiff: Single image super-resolution with diffusion probabilistic models. *Neurocomputing*, 479:47–59, 2022. [4,](#page-3-2) [8](#page-7-0)
- <span id="page-9-7"></span>[35] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *arXiv preprint arXiv:2302.14416*, 2023. [2](#page-1-0)
- <span id="page-9-24"></span>[36] Andreas Lugmayr, Martin Danelljan, Andres Romero, Fisher Yu, Radu Timofte, and Luc Van Gool. Repaint: Inpainting using denoising diffusion probabilistic models. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 11461–11471, 2022. [8](#page-7-0)
- <span id="page-9-4"></span>[37] Wojciech Masarczyk and Ivona Tautkute. Reducing catastrophic forgetting with learning on synthetic data. In *CVPR*, pages 252–253, 2020. [1](#page-0-0)
- <span id="page-9-2"></span>[38] Baharan Mirzasoleiman, Jeff Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In *International Conference on Machine Learning*, pages 6950–6960. PMLR, 2020. [1](#page-0-0)
- <span id="page-9-22"></span>[39] B Moser, Stanislav Frolov, Federico Raue, Sebastian Palacio, and Andreas Dengel. Waving goodbye to low-res: A diffusionwavelet approach for image super-resolution. *arXiv preprint arXiv:2304.01994*, 2023. [8](#page-7-0)
- <span id="page-9-3"></span>[40] Brian Moser, Federico Raue, Jörn Hees, and Andreas Dengel. Less is more: Proxy datasets in nas approaches. In *CVPR*, pages 1953–1961, 2022. [1](#page-0-0)
- <span id="page-9-8"></span>[41] Brian B Moser, Stanislav Frolov, Federico Raue, Sebastian Palacio, and Andreas Dengel. Yoda: You only diffuse areas. an area-masked diffusion approach for image super-resolution. *arXiv preprint arXiv:2308.07977*, 2023. [2,](#page-1-0) [8](#page-7-0)
- [42] Brian B Moser, Federico Raue, Stanislav Frolov, Sebastian Palacio, Jörn Hees, and Andreas Dengel. Hitchhiker's guide to super-resolution: Introduction and recent advances. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023. [2](#page-1-0)
- <span id="page-9-9"></span>[43] Brian B Moser, Arundhati S Shanbhag, Federico Raue, Stanislav Frolov, Sebastian Palacio, and Andreas Dengel. Diffusion models, image super-resolution and everything: A survey. *arXiv preprint arXiv:2401.00736*, 2024. [2,](#page-1-0) [3,](#page-2-1) [8](#page-7-0)
- <span id="page-9-13"></span>[44] Soumik Mukhopadhyay, Matthew Gwilliam, Vatsal Agarwal, Namitha Padmanabhan, Archana Swaminathan, Srinidhi Hegde, Tianyi Zhou, and Abhinav Shrivastava. Diffusion models beat gans on image classification. *arXiv preprint arXiv:2307.08702*, 2023. [3,](#page-2-1) [8](#page-7-0)
- <span id="page-9-25"></span>[45] Gustav Müller-Franzes, Jan Moritz Niehues, Firas Khader, Soroosh Tayebi Arasteh, Christoph Haarburger, Christiane Kuhl, Tianci Wang, Tianyu Han, Sven Nebelung, Jakob Nikolas Kather, et al. Diffusion probabilistic models beat gans on medical images. *arXiv preprint arXiv:2212.07501*, 2022. [8](#page-7-0)
- <span id="page-9-26"></span>[46] Ivars Namatevs, Kaspars Sudars, Arturs Nikulins, Anda Slaidina, Laura Neimane, Oskars Radzins, and Edgars Edelmers. Denoising diffusion algorithm for single image inplaine superresolution in cbct scans of the mandible. In *2023 IEEE 64th*

*International Scientific Conference on Information Technology and Management Science of Riga Technical University (ITMS)*, pages 1–6. IEEE, 2023. [8](#page-7-0)

- <span id="page-9-19"></span>[47] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [5](#page-4-1)
- <span id="page-9-20"></span>[48] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *NeurIPS*, 2021. [5](#page-4-1)
- <span id="page-9-10"></span>[49] Alexander Quinn Nichol and Prafulla Dhariwal. Improved denoising diffusion probabilistic models. In *ICML*. PMLR, 2021. [2](#page-1-0)
- <span id="page-9-18"></span>[50] Yong-Hyun Park, Mingi Kwon, Jaewoong Choi, Junghyo Jo, and Youngjung Uh. Understanding the latent space of diffusion models through the lens of riemannian geometry. *arXiv preprint arXiv:2307.12868*, 2023. [4](#page-3-2)
- <span id="page-9-12"></span>[51] Gaurav Parmar, Yijun Li, Jingwan Lu, Richard Zhang, Jun-Yan Zhu, and Krishna Kumar Singh. Spatially-adaptive multilayer selection for gan inversion and editing. In *CVPR*, pages 11399–11409, 2022. [3](#page-2-1)
- <span id="page-9-0"></span>[52] Aditya Ramesh, Prafulla Dhariwal, Alex Nichol, Casey Chu, and Mark Chen. Hierarchical text-conditional image generation with clip latents. *arXiv preprint arXiv:2204.06125*, 1(2):3, 2022. [1](#page-0-0)
- <span id="page-9-1"></span>[53] Tal Ridnik, Emanuel Ben-Baruch, Asaf Noy, and Lihi Zelnik-Manor. Imagenet-21k pretraining for the masses. *arXiv preprint arXiv:2104.10972*, 2021. [1](#page-0-0)
- <span id="page-9-11"></span>[54] Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Björn Ommer. High-resolution image synthesis with latent diffusion models. In *CVPR*, 2022. [2,](#page-1-0) [3,](#page-2-1) [5](#page-4-1)
- <span id="page-9-23"></span>[55] Chitwan Saharia, Jonathan Ho, William Chan, Tim Salimans, David J Fleet, and Mohammad Norouzi. Image superresolution via iterative refinement. *arXiv:2104.07636*, 2021. [8](#page-7-0)
- <span id="page-9-16"></span>[56] Chitwan Saharia, Jonathan Ho, William Chan, Tim Salimans, David J Fleet, and Mohammad Norouzi. Image superresolution via iterative refinement. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 45(4):4713–4726, 2022. [4](#page-3-2)
- <span id="page-9-6"></span>[57] Axel Sauer, Katja Schwarz, and Andreas Geiger. Styleganxl: Scaling stylegan to large diverse datasets. In *ACM SIG-GRAPH 2022 conference proceedings*, pages 1–10, 2022. [2,](#page-1-0) [3](#page-2-1)
- <span id="page-9-21"></span>[58] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *ICLR*, 2014. [5](#page-4-1)
- <span id="page-9-14"></span>[59] Michał Stypułkowski, Konstantinos Vougioukas, Sen He, Maciej Zikeba, Stavros Petridis, and Maja Pantic. Diffused heads: Diffusion models beat gans on talking-face generation. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 5091–5100, 2024. [3](#page-2-1)
- <span id="page-9-5"></span>[60] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *ICML*, 2020. [1](#page-0-0)

- <span id="page-10-0"></span>[61] Hugo Touvron, Thibaut Lavril, Gautier Izacard, Xavier Martinet, Marie-Anne Lachaux, Timothée Lacroix, Baptiste Rozière, Naman Goyal, Eric Hambro, Faisal Azhar, et al. Llama: Open and efficient foundation language models. *arXiv preprint arXiv:2302.13971*, 2023. [1](#page-0-0)
- <span id="page-10-7"></span>[62] Omer Tov, Yuval Alaluf, Yotam Nitzan, Or Patashnik, and Daniel Cohen-Or. Designing an encoder for stylegan image manipulation. *ACM Transactions on Graphics (TOG)*, 40(4):1–14, 2021. [3](#page-2-1)
- <span id="page-10-1"></span>[63] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1](#page-0-0)
- <span id="page-10-11"></span>[64] Weihao Xia, Yulun Zhang, Yujiu Yang, Jing-Hao Xue, Bolei Zhou, and Ming-Hsuan Yang. Gan inversion: A survey. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 45(3):3121–3138, 2022. [4](#page-3-2)
- <span id="page-10-6"></span>[65] Ling Yang, Zhilong Zhang, Yang Song, Shenda Hong, Runsheng Xu, Yue Zhao, Wentao Zhang, Bin Cui, and Ming-Hsuan Yang. Diffusion models: A comprehensive survey of methods and applications. *ACM Computing Surveys*, 56(4):1– 39, 2023. [2,](#page-1-0) [3,](#page-2-1) [8](#page-7-0)
- <span id="page-10-13"></span>[66] Tao Yu, Runseng Feng, Ruoyu Feng, Jinming Liu, Xin Jin, Wenjun Zeng, and Zhibo Chen. Inpaint anything: Segment anything meets image inpainting. *arXiv preprint arXiv:2304.06790*, 2023. [8](#page-7-0)
- <span id="page-10-5"></span>[67] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021. [2](#page-1-0)
- <span id="page-10-3"></span>[68] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-10-2"></span>[69] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 2020. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-10-12"></span>[70] Guangcong Zheng, Xianpan Zhou, Xuewei Li, Zhongang Qi, Ying Shan, and Xi Li. Layoutdiffusion: Controllable diffusion model for layout-to-image generation. In *CVPR*, pages 22490–22499, June 2023. [8](#page-7-0)
- <span id="page-10-10"></span>[71] Xinhao Zhong, Hao Fang, Bin Chen, Xulin Gu, Tao Dai, Meikang Qiu, and Shu-Tao Xia. Hierarchical features matter: A deep exploration of gan priors for improved dataset distillation, 2024. [3](#page-2-1)
- <span id="page-10-4"></span>[72] Jun-Yan Zhu, Philipp Krähenbühl, Eli Shechtman, and Alexei A Efros. Generative visual manipulation on the natural image manifold. In *ECCV*, pages 597–613. Springer, 2016. [2,](#page-1-0) [3](#page-2-1)
- <span id="page-10-9"></span>[73] Peihao Zhu, Rameen Abdal, John Femiani, and Peter Wonka. Barbershop: Gan-based image compositing using segmentation masks. *arXiv preprint arXiv:2106.01505*, 2021. [3](#page-2-1)
- <span id="page-10-8"></span>[74] Peihao Zhu, Rameen Abdal, Yipeng Qin, John Femiani, and Peter Wonka. Improved stylegan embedding: Where are the good latents? *arXiv preprint arXiv:2012.09036*, 2020. [3](#page-2-1)

| Parameter                | Value                                                |
|--------------------------|------------------------------------------------------|
| DSA Augmentations        | Color / Crop / Cutout / Flip / Scale / Rotate        |
| Iteration (Distillation) | 5,000 (128 $\times$ 128) / 10,000 (256 $\times$ 256) |
| Momentum                 | 0.5                                                  |
| Batch Real               | 256                                                  |
| Batch Train              | 256                                                  |
| Batch Test               | 128                                                  |

Table 7. Common hyper-parameters for training the distillation algorithms used in this work.

## Supplementary Material

## Hyper-Parameters for Distillation Algorithms

LDM. For all our LDM experiments, we set the unconditional guidance scale to default value 3. For  $128 \times 128$ images, we used max. time steps of 10, and for  $256 \times 256$ images, we used 20.

**DC.** We utilize a learning rate of  $10^{-3}$  throughout our DC experiments to update the latent code representation and the conditioning information.

DM. In every DM experiment conducted, we adopt a learning rate of  $10^{-2}$ , applying it to updates of the latent code representation alongside the conditioning information.

MTT. For MTT experiments, a uniform learning rate of 10 is applied to update the latent code representation and the conditioning information. We buffered 5,000 trajectories for expert training, each with 15 training epochs. We used ConvNet-5 and InstanceNorm. During dataset distillation, we used three expert epochs, max. start epoch of 5 and 20 synthetic steps.

Image /page/11/Figure/0 description: The image is a grid of 10 rows and 10 columns, displaying various generated images. The rows are labeled on the left side with text: ImNet-A, ImNet-B, ImNet-C, ImNet-D, ImNet-E, ImNette, ImWoof, ImNet-Birds, ImNet-Fruits, and ImNet-Cats. Each cell in the grid contains a distinct, often abstract or distorted, image. The images appear to be generated by different neural network models, as suggested by the row labels. The overall impression is a visual comparison of image generation capabilities across different categories or models.

Figure 6. Images distilled by MTT in LD3M for IPC=1.

Image /page/12/Figure/0 description: The image is a grid of 10 rows and 6 columns, displaying various images. Each row is labeled on the left side with a category: ImNet-A, ImNet-B, ImNet-C, ImNet-D, ImNet-E, ImNette, ImWoof, ImNet-Birds, ImNet-Fruits, and ImNet-Cats. The images within the grid appear to be generated or processed, with many exhibiting a kaleidoscopic or fractal-like appearance, often centered around a circular or spherical motif. Some images show hints of recognizable objects like flowers, landscapes, or abstract patterns, rendered in a variety of colors and textures. The overall presentation suggests a comparison or display of results from different 'ImNet' models or categories.

Figure 7. Images distilled by DC in LD3M for IPC=1.

Image /page/13/Figure/0 description: The image is a grid of 8 rows and 8 columns, displaying various images. Each row is labeled on the left side with text, starting from the top with "ImNet-A" and going down to "ImNet-Cats". The images within the grid appear to be abstract patterns with a repeating dotted or circular motif, and some exhibit color variations such as yellow, green, blue, red, and white against a darker background. The overall impression is a visual comparison of different image generation models or datasets, as suggested by the row labels.

Figure 8. Images distilled by DM in LD3M for IPC=1.

Image /page/14/Picture/0 description: This is a grid of images, likely generated by a machine learning model. The grid is organized into rows and columns, with each cell containing a distinct image. The images themselves are varied, depicting subjects such as dogs, fields of yellow flowers, close-ups of plants or flowers, hamsters, Venetian canals with gondolas, orcas and dolphins in water, and birds in natural settings. Many of the images appear to have a somewhat abstract or distorted quality, suggesting they are synthetic rather than photographic. The overall impression is a diverse collection of generated visual content.

Figure 9. Images distilled by DC in LD3M for IPC=10 and ImageNet-A.

Image /page/15/Picture/0 description: This is a grid of images, likely generated by a machine learning model. The grid is organized into 8 rows and 10 columns. The top rows feature images of birds, possibly pigeons or doves, in various poses and colors. The next few rows display colorful parrots, followed by images of cows or cattle. Subsequent rows show close-ups of flowers, then images of buses or trams on city streets. There are also images of hedgehogs, and then close-ups of dogs, possibly Pomeranians. The lower rows contain images of car dashboards with various gauges and displays, and the bottom rows show images of shorebirds or seabirds on rocky or sandy terrain.

Figure 10. Images distilled by DC in LD3M for IPC=10 and ImageNet-B.

Image /page/16/Picture/0 description: This is a grid of 50 images, arranged in 10 rows and 5 columns. The images appear to be generated by a machine learning model, as they are somewhat abstract and varied. The top row shows urban scenes with buildings and vehicles. The second row features close-ups of birds, some in flight and some perched. The third row displays images of ships and water, possibly naval scenes. The fourth row contains abstract, circular patterns with dark and light textures. The fifth row shows colorful birds, possibly bee-eaters, perched on branches. The sixth row features close-ups of fish with vibrant colors, swimming in water. The seventh row displays images of lions or large cats, with a focus on their faces and manes. The eighth row shows images of birds, possibly waterfowl, in natural settings. The ninth row features images of butterflies and flowers, with delicate details and soft colors. The bottom row shows close-ups of small birds, likely finches, perched on branches with greenery.

Figure 11. Images distilled by DC in LD3M for IPC=10 and ImageNet-C.

Image /page/17/Picture/0 description: This is a grid of 60 images, arranged in 10 rows and 6 columns. The images appear to be generated by a machine learning model, as they are somewhat abstract and stylized. The subjects vary widely, including ostriches, dogs, birds, horses, butterflies, owls, and toucans. Some images are clearer than others, with some showing more detail and recognizable features than others. The overall impression is a diverse collection of animal imagery with a consistent artistic style.

Figure 12. Images distilled by DC in LD3M for IPC=10 and ImageNet-D.

Image /page/18/Picture/0 description: The image is a grid of 42 smaller images, arranged in 7 rows and 6 columns. The smaller images appear to be generated by an AI model, as they are abstract and somewhat distorted. The top three rows feature images of birds, including toucans and penguins. The middle rows contain images of what appear to be printers and electronic equipment, as well as some abstract patterns. The bottom three rows display images of tarantulas and butterflies. The overall impression is a diverse collection of AI-generated imagery.

Figure 13. Images distilled by DC in LD3M for IPC=10 and ImageNet-E.

Image /page/19/Picture/0 description: This is a grid of 40 images, arranged in 5 rows and 8 columns. The images appear to be generated by a diffusion model, as they are somewhat abstract and varied. The top rows contain images that seem to depict birds or animals in abstract forms, with a mix of colors and textures. The middle rows feature landscapes, particularly fields of yellow flowers under a blue sky, and some abstract representations of nature. The lower rows show scenes that resemble Venice canals with gondolas, and images of birds, possibly penguins or ducks, in water. The overall impression is a diverse collection of AI-generated imagery, with varying degrees of clarity and realism.

Figure 14. Images distilled by DM in LD3M for IPC=10 and ImageNet-A.

Image /page/20/Picture/0 description: The image is a grid of 42 smaller images, arranged in 7 rows and 6 columns. The smaller images appear to be generated by a machine learning model, as they are abstract and somewhat distorted. Many of the images feature colorful birds, possibly parrots, with vibrant plumage. Other images show abstract patterns, possibly of natural elements like flowers or coral, and some appear to be urban scenes with buses and buildings. The overall impression is a collection of AI-generated art with a recurring theme of colorful birds.

Figure 15. Images distilled by DM in LD3M for IPC=10 and ImageNet-B.

Image /page/21/Picture/0 description: The image is a grid of 70 small images, arranged in 7 rows and 10 columns. The images appear to be generated by a machine learning model, as they are abstract and somewhat distorted. Many of the images contain depictions of birds, often in natural settings with foliage. Some images also show what appear to be urban or industrial scenes with buildings and water. The overall color palette is varied, with blues, greens, yellows, and reds present in different images. The images are not perfectly clear and have a somewhat painterly or impressionistic quality.

Figure 16. Images distilled by DM in LD3M for IPC=10 and ImageNet-C.

Image /page/22/Picture/0 description: A grid of 70 images, each depicting a different animal or object, is presented. The images are arranged in 10 rows and 7 columns. The animals featured include dogs, birds, horses, and butterflies. The overall quality of the images is somewhat distorted and abstract, with a mix of colors and shapes that suggest the original subjects but are not clearly defined. The bottom of the image contains text that reads "Figure 17. Images distilled by DM in LD3M for IPC=10 and ImageNet-D."

Figure 17. Images distilled by DM in LD3M for IPC=10 and ImageNet-D.

Image /page/23/Picture/0 description: The image is a grid of 60 small images, arranged in 6 rows and 10 columns. The images appear to be generated by a diffusion model, as they are abstract and somewhat distorted. Many of the images contain elements that resemble animals, particularly penguins and birds, as well as some abstract patterns and objects. The overall color palette is varied, with some images featuring bright colors and others being darker and more muted. The bottom row of images shows more distinct natural scenes with butterflies and flowers.

Figure 18. Images distilled by DM in LD3M for IPC=10 and ImageNet-E.

Image /page/24/Figure/0 description: The image displays a grid of 256x256 distilled images, organized into five main rows labeled ImNet-A, ImNet-B, ImNet-C, ImNet-D, and ImNet-E. Each of these main rows is further subdivided into three sub-rows, labeled ImageNet, Rand, and FFHQ. This arrangement suggests a comparison of image generation results from different training datasets (ImageNet, FFHQ) and random initialization across various ImNet models. The figure caption indicates it is a comparison of 256x256 distilled images with differently trained LDMs (ImageNet, FFHQ, and random).

Figure 19. Comparison of  $256 \times 256$  distilled images with differently trained LDMs (ImageNet, FFHQ, and random).

| Dataset         | 0                     | 1                   | 2                  | 3                    | 4               | 5                        | 6                      | 7                     | 8                 | 9                     |
|-----------------|-----------------------|---------------------|--------------------|----------------------|-----------------|--------------------------|------------------------|-----------------------|-------------------|-----------------------|
| ImageNet-A      | Leonberg              | Probiscis<br>Monkey | Rapeseed           | Three-Toed<br>Sloth  | Cliff Dwelling  | Yellow<br>Lady's Slipper | Hamster                | Gondola               | Orca              | Limpkin               |
| ImageNet-B      | Spoonbill             | Website             | Lorikeet           | Hyena                | Earthstar       | Trollybus                | Echidna                | Pomeranian            | Odometer          | Ruddy<br>Turnstone    |
| ImageNet-C      | Freight Car           | Hummingbird         | Fireboat           | Disk Brake           | Bee Eater       | Rock Beauty              | Lion                   | European<br>Gallinule | Cabbage Butterfly | Goldfinch             |
| ImageNet-D      | Ostrich               | Samoyed             | Snowbird           | Brabancon<br>Griffon | Chickadee       | Sorrel                   | Admiral                | Great<br>Gray Owl     | Hornbill          | Ringlet               |
| ImageNet-E      | Spindle               | Toucan              | Black Swan         | King<br>Penguin      | Potter's Wheel  | Photocopier              | Screw                  | Tarantula             | Sscilloscope      | Lycaenid              |
| ImageNette      | Tench                 | English<br>Springer | Cassette<br>Player | Chainsaw             | Church          | French Horn              | Garbage<br>Truck       | Gas Pump              | Golf Ball         | Parachute             |
| ImageWoof       | Australian<br>Terrier | Border Terrier      | Samoyed            | Beagle               | Shih-Tzu        | English<br>Foxhound      | Rhodesian<br>Ridgeback | Dingo                 | Golden Retriever  | English<br>Sheepdog   |
| ImageNet-Birds  | Peacock               | Flamingo            | Macaw              | Pelican              | King<br>Penguin | Bald Eagle               | Toucan                 | Ostrich               | Black Swan        | Cockatoo              |
| ImageNet-Fruits | Pineapple             | Banana              | Strawberry         | Orange               | Lemon           | Pomegranate              | Fig                    | Bell Pepper           | Cucumber          | Granny Smith<br>Apple |
| ImageNet-Cats   | Tabby<br>Cat          | Bengal<br>Cat       | Persian<br>Cat     | Siamese Cat          | Egyptian<br>Cat | Lion                     | Tiger                  | Jaguar                | Snow<br>Leopard   | Lynx                  |

|  | Table 8. Class listings for our ImageNet subsets. |  |
|--|---------------------------------------------------|--|
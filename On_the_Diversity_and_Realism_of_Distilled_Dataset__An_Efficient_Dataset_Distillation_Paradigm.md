# On the Diversity and Realism of Distilled Dataset: An Efficient Dataset Distillation Paradigm

<span id="page-0-1"></span>Peng <PERSON><sup>2,1</sup> <PERSON><PERSON><sup>1</sup> <PERSON><PERSON><sup>3,∗</sup> <PERSON><sup>1,†</sup> <sup>1</sup>Westlake University  $\frac{2}{2}$ Zhejiang University <sup>3</sup> Independent Researcher

<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>

Image /page/0/Figure/3 description: This figure illustrates two paradigms for data synthesis in machine learning and presents a comparative bar chart of their performance. The left panel, labeled "Optimization-based Paradigm," shows an "Outer Loop" that updates an "Inner Loop" using a noisy image and a blurry image. The middle panel, labeled "Proposed Paradigm," depicts a process where images with bounding boxes are used to "Synthesize" new images, resulting in a set of synthesized images and "Soft Label" data represented by a bar chart. The right panel is a bar chart comparing "Ours" (presumably the proposed paradigm) against "SOTA" (State-of-the-Art) methods across different models: ResNet-18 (orange), MobileNet-V2 (green), and EfficientNet-B0 (blue). The y-axis represents "Top-1 Accuracy (%)" and the x-axis represents "Data Synthesis Time (ms)." For "Ours," the accuracies are 42%, 40%, and 31% with synthesis times of 40ms, 65ms, and 73ms, respectively. For "SOTA," the accuracies are 21%, 15%, and 12% with synthesis times of 2113ms, 3783ms, and 4412ms, respectively.

<span id="page-0-0"></span>Figure 1. Proposed paradigm vs. optimization-based paradigm. Left is the mainstream optimization-based dataset distillation and middle is our proposed non-optimizing paradigm. Right is top-1 validation accuracy vs. synthesis time per image on ImageNet-1K with  $IPC = 10$ (10 Images Per Class). Models used for distillation include ResNet-18, EfficientNet-B0, and MobileNet-V2; we use ResNet-18 for evaluation.

# Abstract

*Contemporary machine learning, which involves training large neural networks on massive datasets, faces significant computational challenges. Dataset distillation, as a recent emerging strategy, aims to compress real-world datasets for efficient training. However, this line of research currently struggles with large-scale and high-resolution datasets, hindering its practicality and feasibility. Thus, we re-examine existing methods and identify three properties essential for real-world applications: realism, diversity, and efficiency. As a remedy, we propose RDED, a novel computationallyefficient yet effective data distillation paradigm, to enable both diversity and realism of the distilled data. Extensive empirical results over various model architectures and datasets demonstrate the advancement of RDED: we can distill a dataset to* 10 *images per class from full ImageNet-1K [\[6\]](#page-8-0) within* 7 *minutes, achieving a notable* 42% *accuracy with ResNet-18 [\[14\]](#page-8-1) on a single RTX-4090 GPU (while the SOTA only achieves* 21% *but requires* 6 *hours). Code:* <https://github.com/LINs-lab/RDED>*.*

# 1. Introduction

The success of modern deep learning could be largely attributed to the fact of scaling and increasing both neural architectures and training datasets [\[12,](#page-8-2) [14,](#page-8-1) [16,](#page-8-3) [17\]](#page-8-4). Though this pattern shows great potential to propel artificial intelligence forward, the challenge of high computational requirements remains a noteworthy concern [\[4,](#page-8-5) [39,](#page-9-0) [45\]](#page-9-1). Dataset distillation methods have recently emerged [\[39,](#page-9-0) [45\]](#page-9-1) and attracted attention for their exceptional performance [\[1,](#page-8-6) [2,](#page-8-7) [38,](#page-9-2) [50](#page-9-3)[–52\]](#page-9-4). The key idea is compressing original full datasets by synthesizing and optimizing a small dataset, where training a model using the synthetic dataset can achieve a similar performance to the original.

However, these methods suffer a high computational burden [\[5,](#page-8-8) [44\]](#page-9-5) due to the bi-level optimization-based paradigm. Moreover, the synthetic images exhibit certain *non-realistic* features (see Figure [2b](#page-2-0) and [2d\)](#page-2-0) that have materialized due to overfitting to a specific architecture used during the optimization process, which leads to difficulties in generalizing to other architectures [\[2,](#page-8-7) [31\]](#page-9-6).

A notable work [\[2\]](#page-8-7) investigates the relationship between realism and expressiveness in synthetic datasets. The findings reveal a trade-off: more realistic images come at the

<sup>†</sup> Corresponding author.

<sup>∗</sup> Work done during Daiwei's visit to Westlake University.

<span id="page-1-3"></span>sacrifice of expressiveness. While realism aids in generalizing across different architectures, it hurts distillation performance. Conversely, prioritizing expressiveness over realism can enhance distillation performance but may impede crossarchitecture generalization.

Inspired by these insights, we introduce an Realistic, Diverse, and Efficient Dataset Distillation (RDED) method. Our goal is to achieve diversity (expressiveness) and realism simultaneously across varying datasets, ranging from CIFAR-10 to ImageNet-1K. Specifically, we directly crop and select realistic patches from the original data to maintain realism. To ensure the greatest possible diversity, we stitch the selected patches into the new images as the synthetic dataset. It is noteworthy that our method is non-optimizationbased, so it can also achieve high efficiency, making it wellsuited for processing large-scale, high-resolution datasets.

The key contributions of this work can be summarized as:

- We first investigate the limitations of existing dataset distillation methods and define three key properties for effective dataset distillation on large-scale high-resolution datasets: realism, diversity, and efficiency.
- We introduce the definitions of diversity ratio and realism score backed by  $V$ -information theory [\[42\]](#page-9-7), together with an optimization-free efficient paradigm, to enhance diversity and realism of the distilled data.
- Extensive experiments demonstrate the effectiveness of our method: it not only achieves a top-1 validation accuracy that is twice the current SOTA—SRe<sup>2</sup>L  $[44]$ , but it also operates at a speed 52 times faster (see Figure [1\)](#page-0-0).

<span id="page-1-1"></span>

## 2. Related Work

Dataset distillation, as proposed by Wang et al. [\[39\]](#page-9-0), condenses large datasets into smaller ones without sacrificing performance. These methods fall into four main categories.

Bi-level optimization-based distillation. A line of work seeks to minimize the surrogate models learned from both synthetic and original datasets, depending on their metrics, namely, the matching gradients [\[18,](#page-8-9) [49,](#page-9-8) [51\]](#page-9-9), features [\[38\]](#page-9-2), distribution [\[50,](#page-9-3) [52\]](#page-9-4), and training trajectories [\[1,](#page-8-6) [4,](#page-8-5) [5,](#page-8-8) [8,](#page-8-10) [13,](#page-8-11) [45\]](#page-9-1). Notably, trajectory matching-based techniques have demonstrated remarkable performance across various benchmarks with low IPC. However, the synthetic data often overfit to a specific model architecture, struggling to generalize to others.

Distillation with prior regularization. Cazenavette et al. [\[2\]](#page-8-7) suggest that direct pixel space parameterization is a key factor for the architecture transferability issue, and propose GLaD to integrate a generative prior for dataset distillation to enhance generalization across any distillation method. However, bi-level optimization-based methods, especially

those that entail prior regularization, face computational challenges and memory issues [\[5\]](#page-8-8).

Uni-level optimization-based distillation. Kernel ridgeregression methods [\[23,](#page-8-12) [53\]](#page-9-10), with uni-level optimization, effectively reduce training costs [\[53\]](#page-9-10) and enhancing perfor-mance [\[23\]](#page-8-12). However, due to the resource-intensive nature of inverting matrix operations, scaling these methods to larger IPC remains challenging. Unlike NTK-based solutions, Yin et al. [\[44\]](#page-9-5) propose to decouple the bi-level optimization of dataset condensation into two single-level learning procedures, resulting in a more efficient framework.

CoreSet selection-based distillation. CoreSet selection, akin to traditional dataset distillation, focuses on identifying representative samples using provided images and labels. Various difficulty-based metrics are proposed to assess the sample importance, e.g., the forgetting score [\[37\]](#page-9-11), memorization [\[10\]](#page-8-13), EL2N score [\[27\]](#page-8-14), diverse ensembles [\[25\]](#page-8-15).

## 3. On the Limits of Dataset Distillation

We start by clearly defining the concept of dataset distillation and then reveal the primary challenges in this field.

### 3.1. Preliminary

The goal of dataset distillation is to synthesize a smaller distilled dataset, denoted as  $S = (X, Y) = \{x_j, y_j\}_{j=1}^{|S|}$ , that captures the essential characteristics of a larger dataset  $\mathcal{T} = (\hat{X}, \hat{Y}) = \{\hat{\mathbf{x}}_i, \hat{y}_i\}_{i=1}^{|\mathcal{T}|}$ . Here, the distilled dataset S is generated by an algorithm  $\hat{A}$  such that  $\hat{S} \in \mathcal{A}(\mathcal{T})$ , where the size of S is considerably smaller than  $T$  (i.e.,  $|S| \ll |T|$ ). Each  $y_i \in Y$  corresponds to the synthetic distilled label for the sample  $x_j \in X$ , and a similar definition can be applied to  $(\hat{\mathbf{x}}_i \in \hat{X}, \hat{y}_i \in \hat{Y})$ . The key motivation for dataset distillation is to create a dataset  $S$  that allows models to achieve performance within an acceptable deviation  $\epsilon$  from those trained on  $T$ . Formally, this is expressed as:

<span id="page-1-2"></span>
$$
\sup\left\{|\ell(\phi_{\theta_{\mathcal{T}}}(\mathbf{x}),y) - \ell(\phi_{\theta_{\mathcal{S}}}(\mathbf{x}),y)|\right\}_{(\mathbf{x},y)\sim\mathcal{T}} \leq \epsilon,\quad(1)
$$

where  $\theta_{\mathcal{T}}$  is the parameter set of the neural network  $\phi$  optimized on  $\mathcal{T}$ :

$$
\boldsymbol{\theta}_{\mathcal{T}} = \arg\min_{\boldsymbol{\theta}} \mathbb{E}_{(\mathbf{x},y)\in\mathcal{T}} \left[ \ell(\phi_{\boldsymbol{\theta}}(\mathbf{x}), y) \right],\tag{2}
$$

with  $\ell$  representing the loss function. A similar definition applies to  $\theta_{\mathcal{S}}$ .

<span id="page-1-0"></span>The properties of optimal dataset distillation. The effectiveness and utility of dataset distillation methods rely on key properties outlined in Definition [1.](#page-1-0) These properties are crucial for creating datasets efficiently, which in turn, enhances model training and generalization.

<span id="page-2-4"></span><span id="page-2-0"></span>Image /page/2/Picture/0 description: This image displays a grid of six subfigures, each labeled with a letter from (a) to (f) and a corresponding method name. Subfigure (a) shows a random selection from an original dataset, featuring a black cat, sliced bananas, pomegranates, and green guavas. Subfigure (b) presents results from the MTT [1] method, showing four abstract, colorful images. Subfigure (c) displays results from the GLaD [2] method, with images of pineapples, sliced bananas, dragon fruit, and figs. Subfigure (d) shows results from the SRe2L [44] method, with abstract representations of pineapples, a bird-like figure, and figs. Subfigure (e) presents results from the Herding [40] method, featuring pineapples, bananas, a pomegranate, and figs. Subfigure (f) shows results from the RDED (Ours) method, with a collage of pineapples, bananas, pomegranates, and figs.

Figure 2. Visualization of images synthesized using various dataset distillation methods. We consider the ImageNet-Fruits [\[1\]](#page-8-6) dataset, comprising a total of 10 distinct fruit types, with a resolution of  $128 \times 128$ . There are four specific classes for each method, namely, 1) *Pineapple*, 2) *Banana*, 3) *Pomegranate*, and 4) *Fig*. Note that MTT [\[1\]](#page-8-6), GLaD [\[2\]](#page-8-7), SRe<sup>2</sup>L [\[44\]](#page-9-5), and Herding [\[40\]](#page-9-12), are four representative methods of conventional dataset distillation paradigms discussed in Section [2](#page-1-1) and Section [3.2](#page-2-1) (see Appendix [A](#page-10-0) for more visualization). In general, ensuring both superior *realism* and *diversity* simultaneously is challenging for methods other than ours and GLaD.

<span id="page-2-3"></span>

| Method  | Property                            |                                     |                                     | Dataset                             |                                     |
|---------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|
|         | Diversity                           | Realism                             | Efficiency                          | Large-scale                         | High-resolution                     |
| MTT     | <span style="color:green;">✓</span> | <span style="color:black;">✕</span> | <span style="color:black;">✕</span> | <span style="color:black;">✕</span> | <span style="color:black;">✕</span> |
| GLaD    | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:black;">✕</span> | <span style="color:black;">✕</span> | <span style="color:green;">✓</span> |
| $SRe2L$ | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> |
| Herding | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> |
| Ours    | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> | <span style="color:green;">✓</span> |

Table 1. Properties and performance of various representative SOTA dataset distillation methods. We give a summary of the properties of different methods and their performance on largescale or high-resolution datasets, where  $\checkmark$ ,  $\checkmark$ , and  $\checkmark$ , denote "Superior", "Satisfactory", and "Bad" respectively.

Definition 1 (Properties of distilled data). *Consider a family of observer models* V [1](#page-2-2) *. The core attributes of a distilled dataset*  $\mathcal{S} = (X, Y) \in \mathcal{A}(\mathcal{T})$  *are defined as follows:* 

*1. Diversity: Essential for robust learning and generalization, a high-quality dataset should cover a wide range*

*of samples* X *and labels* Y *[\[17,](#page-8-4) [28,](#page-8-16) [34\]](#page-9-13). This ensures exposure to diverse features and contexts.*

- *2. Realism: Critical for cross-architecture generalization, realistic distilled samples* X *and labels* Y *should be accurately predicted and matched by various observer models from* V*. It is important to avoid features or annotations that are overly tailored to a specific model [\[1,](#page-8-6) [50,](#page-9-3) [51\]](#page-9-9).*
- *3. Efficiency: A determinant for the feasibility of dataset distillation, addressing the computational and memory challenges is crucial for scaling the distillation algorithm* A *to large datasets [\[5,](#page-8-8) [44\]](#page-9-5).*

<span id="page-2-1"></span>

## 3.2. Pitfalls of Conventional Dataset Distillation

In response to the properties of the optimal dataset distillation, in this section, we conduct a comprehensive examination of four conventional dataset distillation paradigms discussed in Section [2.](#page-1-1) Limitations are detailed below and summarized in Table [1](#page-2-3) (see more details in Appendix [A\)](#page-10-0).

• Bi-level optimization-based distillation. Conventional dataset distillation methods [\[1,](#page-8-6) [50,](#page-9-3) [51\]](#page-9-9) suffer from noiselike *non-realistic* patterns (see Figure [2b\)](#page-2-0) in distilled high-

<span id="page-2-2"></span><sup>&</sup>lt;sup>1</sup>V includes different observer models, for instance, humans  $\phi_h$  and pre-trained models  $\phi_{\theta_{\mathcal{T}}}$ . Here,  $\phi_{\mathrm{h}}$  is an abstraction representing human predictive behavior.

<span id="page-3-4"></span>resolution images and overfit the specific architecture used in training [\[2\]](#page-8-7), which hurt its cross-architecture generalization ability [\[2\]](#page-8-7). However, these methods suffer a *high computational burden* [\[5,](#page-8-8) [44\]](#page-9-5) due to the bi-level optimizationbased paradigm.

- Distillation with prior regularization. Cazenavette et al. [\[2\]](#page-8-7) identify the source of the architecture overfitting issue, and thus enhances the realism (see Figure [2c\)](#page-2-0) of synthetic images and the cross-architecture generalization. The current remedy inherits the *low efficiency* of bi-level optimization-based distillation, and thus still cannot generalize to large-scale datasets.
- Uni-level optimization-based distillation. As a remedy for the former research, Yin et al. [\[44\]](#page-9-5)—as the latest progress in the field—alleviate the *efficiency* and *realism* challenges (see Figure  $2d$ ) and propose  $SRe<sup>2</sup>L$  to distill large-scale, high-resolution datasets, e.g., ImageNet-1K. Yet, SRe<sup>2</sup>L is hampered by a limited *diversity* problem arising from its synthesis approach, which involves extracting knowledge from a pre-trained model containing only partial information of the original dataset [\[43\]](#page-9-14).
- CoreSet selection-based distillation. CoreSet selection methods [\[34,](#page-9-13) [35,](#page-9-15) [40\]](#page-9-12) serve to *efficiently* distill datasets by isolating a CoreSet containing *realistic* images (see Figure [2e\)](#page-2-0). However, the advances come at the cost of limited information representation (*data diversity*) [\[27\]](#page-8-14), leading to a catastrophically degraded performance [\[35\]](#page-9-15).

# 4. Methodology

To tackle the remaining concern of distilling high-resolution and large-scale image datasets, in this section, we articulate an novel unified dataset distillation paradigm—RDED—that prioritizes both *diversity* and *realism* within the distilled dataset, yet being *efficient*.

### 4.1. Enhancing Data Diversity and Realism

Establishing a V-information-based objective for distilled data. Drawing on the artificial intelligence learning principles of *parsimony* and *self-consistency* from [\[24\]](#page-8-17), we strive to ensure that the models trained on the distilled dataset embody these principles. To achieve this, we aim to construct a representation  $Y$  of the input data  $X$  that is structured (*parsimony*) and rich in information (*self-consistency*). Consequently, we reinterpret the objective of dataset distillation in  $(1)$ , as the structured and sufficient information inherent in the original full dataset  $\mathcal{T}$ :

<span id="page-3-0"></span>
$$
S = \underset{(X,Y)\in\mathcal{A}(\mathcal{T})}{\arg \max} I_{\mathcal{V}}(X \to Y), \tag{3}
$$

where  $I_V$  denotes the predictive V-information [\[42\]](#page-9-7) from X to  $Y$ , which can be further defined as:

<span id="page-3-1"></span>
$$
I_{\mathcal{V}}(X \to Y) = \underbrace{H_{\mathcal{V}}(Y|\varnothing)}_{\text{diversity}} - \underbrace{H_{\mathcal{V}}(Y|X)}_{\text{realism}},\tag{4}
$$

where  $H_V(Y|X)$  and  $H_V(Y|\emptyset)$  denote, respectively, the predictive conditional V-entropy  $[42]$  with observed side information X and no side information  $\varnothing$ .

Explicitizing the diversity and realism via  $\mathcal V$ -information. Building upon Definition [1,](#page-1-0) maximizing  $H<sub>V</sub>(Y | \varnothing)$  can enhance the uncertainty/diversity of representations Y measured by the observer models in  $V$ . Simultaneously, minimizing  $H<sub>V</sub>(Y|X)$  aims to improve the predictiveness/realism of the data pairs  $(X, Y)$  [\[9,](#page-8-18) [42\]](#page-9-7). Therefore, the objective of [\(3\)](#page-3-0) is equivalent to maximize the first term  $H<sub>V</sub>(Y|\varnothing)$  while minimizing the second term  $H<sub>V</sub>(Y|X)$  in [\(4\)](#page-3-1), to achieve the improved data diversity and realism.

Approximating and maximizing  $\mathcal V$ -information. For the sake of computational feasibility, we restrict ourselves to the case where the predictive family  $V$  includes only humans and a single pre-trained observer model associated with dataset T, denoted as  $V = \{\phi_h, \phi_{\theta_{\mathcal{T}}}\}\$ . Given the computational challenges of solving  $(3)$  by maximizing both terms in  $(4)$ simultaneously, we decouple the terms in  $(4)$ , resulting in:

<span id="page-3-2"></span>
$$
\inf_{f \in \mathcal{V}} \begin{cases} \mathbb{E}_{y \sim Y} \left[ -\log f[\varnothing](y) \right] \\ -\mathbb{E}_{\mathbf{x}, y \sim X, Y} \left[ -\log f[\mathbf{x}](y) \right] \end{cases} , \tag{5}
$$

where  $f[x]$  is probability measure on Y based on the received information x, and  $f[x](y) \in \mathbb{R}$  is the value of the density evaluated at  $y \in Y$ . Then, we seek proxies to approximate the decoupled two terms in [\(5\)](#page-3-2) independently.

<span id="page-3-3"></span>Proposition 1 (Proxies on the diversity and realism of distilled data). *Given a distilled dataset*  $\mathcal{S} = (X, Y)$ *, we derive the following approximations to maximize the diversity term*  $H<sub>V</sub>(Y|*\emptyset*)$  *and the realism term*  $-H<sub>V</sub>(Y|X)$ *:* 

*1. The diversity ratio*  $H_V(Y|\varnothing)/H_V(\mathcal{T}|\varnothing)$  *is posited as a lower bound of the information preservation ratio from the original dataset*  $T$  *to the distilled one*  $S$ *, justified by:* 

$$
H_{\mathcal{V}}(Y|\varnothing) \le H_{\mathcal{V}}(\mathcal{S}|\varnothing) \le H_{\mathcal{V}}(\mathcal{T}|\varnothing). \tag{6}
$$

*Therefore, we maximize diversity through preserving more information from the original dataset* T *.*

*2. The realism score for a distilled sample* x *and label* y *from a pair* (x, y) *is defined as:*

$$
-\ell(\phi_{\boldsymbol{\theta}_{\mathcal{T}}}(\mathbf{x}),\phi_{h}(\mathbf{x})) - \ell(\phi_{\boldsymbol{\theta}_{\mathcal{T}}}(\mathbf{x}),y). \tag{7}
$$

*To enhance the realism score for each distilled pair*  $(\mathbf{x}, y)$ *, we prioritize the distillation of sample* x *with higher*  $-\ell(\phi_{\theta_{\mathcal{T}}}(\mathbf{x}), \phi_h(\mathbf{x}))$  and assign the label  $y = \phi_{\theta_{\mathcal{T}}}(\mathbf{x})$ .

Summary. *To bolster the two properties—diversity and realism—of our distilled dataset, we employ two practical proxies, namely 1) the diversity ratio, and 2) the realism score, as the approximation to design distillation algorithm* A *(see Appendix [B](#page-10-1) for more (theoretical) analysis).*

<span id="page-4-4"></span><span id="page-4-0"></span>Image /page/4/Figure/0 description: This figure illustrates a process involving image analysis and reconstruction. It begins with 'Cropped Patches' of an image, which are then 'Resized'. These processed patches are 'Scored by Observer', indicated by a neural network diagram and bar graphs representing scores. The scored patches contribute to a 'Key Patch Set', from which a subset of 'Top-(N x IPC) Key Patches' is selected. These key patches are then used to generate a 'Reconstructed Image', shown as a collage of four cat images. Below the reconstructed image, there is a section labeled 'Soft Label' with a bar graph representing class probabilities for labels like 'tiger', 'cat', 'horse', 'dog', and so on.

Figure 3. Visualization of our proposed two-stage dataset distillation framework. Stage 1: We crop each original image into several patches and rank them using the realism scores calculated by the observer model. Then, we choose the top-1-scored patch as the key patch. For the key patches within a class, we re-select the top- $N \times \text{IPC}$  patches based on their scores, where  $N = 4$  in this case. Stage 2: We consolidate every  $N$  selected patches from Stage 1 into a single new image that shares the same resolution with each original image, resulting in IPC-numbered distilled images per class. These images are then relabeled using the pre-trained observer model.

Overview of our dataset distillation paradigm. To enhance the diversity and realism of our distilled dataset, we introduce a novel two-stage paradigm that practically utilizes the proposed two proxies in Proposition [1](#page-3-3) (See Figure [3](#page-4-0) and Algorithm [1\)](#page-5-0). In particular, our objective is to preserve the information within a large number of sample pairs exhibiting high realism scores from the original full dataset  $\mathcal T$  into the distilled dataset  $S$ . This process unfolds in two stages:

- First stage in Section [4.2](#page-4-1) *extracts major information* (i.e., key sample pairs) with *high realism score* from T .
- In the second stage (see Section [4.3\)](#page-4-2), we aim to *compress the extracted information* from the first stage into finite pixel space to form distilled images and *relabel* them.

<span id="page-4-1"></span>

## 4.2. Extracting Key Patches from Original Dataset

To extract the explicit key information from the original full dataset, we capture the key patches with high realism scores at the pixel space level and sample space level respectively.

Extracting key patch per image. Motivated by the common practice in Vision Transformer [\[7,](#page-8-19) [48\]](#page-9-16) that *image patches* are sufficient to capture object-related information, we propose to learn the most realistic patch,  $\xi_{i,\star}$ , from a set of patches  $\{\xi_{i,k}\}\$ , which are extracted from a given image  $\hat{\mathbf{x}}_i \in \hat{X}$ . The whole procedure can be formulated as:

$$
\xi_{i,\star} = \underset{\xi_{i,k} \sim p(\xi_{i,k}|\hat{\mathbf{x}}_i)}{\arg \max} -\ell(\phi_{\boldsymbol{\theta}_{\mathcal{T}}}(\xi_{i,k}), \phi_{\mathrm{h}}(\xi_{i,k})), \quad (8)
$$

where the label  $\phi_h(\xi_{i,k})$  annotated by humans is given as  $y_i$ . Therefore,  $s_{i,k} := -\ell(\phi_{\theta_{\mathcal{T}}}(\xi_{i,k}), y_i)$  represents the realism score for the patch  $\xi_{i,k}$  and  $s_{i,*}$  denotes the highest one.

Let  $\mathcal{T}_c := \{(\hat{\mathbf{x}}, \hat{y}) | (\hat{\mathbf{x}}, \hat{y}) \in \mathcal{T}, \hat{y} = c\}$  denote a sub-dataset comprising all samples associated with class c from the full dataset  $\mathcal T$ . Given the key patches and its corresponding scores  $(\xi_{i,\star}, s_{i,\star})$  for  $\hat{\mathbf{x}}_i \in \mathcal{T}_c$ , we form them into a set  $\mathcal{Q}_c$ .

Capturing inner-class information. Solely relying on information extraction at the pixel space level is inadequate in averting information redundancy at the sample space level. To further extract key information from the original dataset, we consider a sample space level selection to further scrutiny of the selected patches from the previous stage.

More precisely, certain patches—denoted as  $Q'_c$ —are selected based on a given pruning criteria  $\bar{s}_\star$  defined over  $\mathcal{Q}_c$ , aiming to capture the most impactful patches for class  $c$ , whose socres are larger than  $\bar{s}_\star$ . This process is iteratively repeated for all classes of  $\mathcal T$ .

Practical implementation. In practice, extracting all key patches from the entire  $\mathcal{T}_c$  and subsequently selecting the top patches based on scoring presents two significant challenges:

- Iterating through each image in  $\mathcal{T}_c$  to identify crucial patches incurs a *considerable computational overhead*.
- Utilizing a score-based selection strategy typically introduces *distribution bias* within the chosen subset of the original dataset  $\mathcal{T}_c$ , which hurts data diversity and adversely affects generalization (see Section [5.5](#page-7-0) for more details).

To address the aforementioned issues, we propose the adop-tion of the random uniform data selection strategy<sup>[2](#page-4-3)</sup> to derive a pre-selected subset  $\mathcal{T}'_c \subset \mathcal{T}_c$  (see settings in Section [5.1\)](#page-5-1). The subsequent inner-class information-capturing process is then performed exclusively on this subset  $\mathcal{T}'_c$ .

<span id="page-4-2"></span>

## 4.3. Information Reconstruction of Patches

To effectively save the previously extracted key information in the limited pixel space and label space of the distilled dataset, we propose to reconstruct the information in patches.

<span id="page-4-3"></span> $2$ This treatment is motivated by the findings  $[3, 34, 35]$  $[3, 34, 35]$  $[3, 34, 35]$  $[3, 34, 35]$  $[3, 34, 35]$  on the impact of various data selection strategies, where random uniform selection is a *lightweight* yet *unbiased* data selection strategy.

<span id="page-5-3"></span>Images reconstruction. The patch size is typically smaller than the dimensions of an expected distilled image, where directly utilizing the patches selected as distilled images may lead to sparse information in the pixel space.

Therefore, for a given class c with a selected patch set  $\mathcal{Q}'_c$ , we randomly retrieve  $N$  patches<sup>[3](#page-5-2)</sup> without replacement to form a final image  $x_i$  by applying the following operation:

$$
\mathbf{x}_j = \text{concatenate}(\{\xi_{i,\star}\}_{i=1}^N \subset \mathcal{Q}'_c). \tag{9}
$$

Labels reconstruction. The previous investigation [\[47\]](#page-9-17) highlights a critical limitation associated with single-label annotations, wherein a random crop of an image may encompass an entirely different object than the ground truth, thereby introducing noisy or even erroneous supervision during the training process. Consequently, relying solely on the simplistic one-hot label proves inadequate for representing an informative image, consequently constraining the effectiveness and efficiency of model learning [\[44\]](#page-9-5).

Inspired by this observation, we propose to re-label the squeezed multi-patches within the distilled images  $x_j$ , thereby encapsulating the informative label for the distilled images. It can be achieved by employing the soft labelling approach [\[32\]](#page-9-18) to generate region-level soft labels  $y_{j,m} = \ell(\phi_{\theta_{\mathcal{T}}}(\mathbf{x}_{j,m}))$ , where  $\mathbf{x}_{j,m}$  is the m-th region in the distilled image and  $y_{j,m}$  is the corresponding soft label.

Training with reconstructed labels. We train the student model  $\phi_{\theta s}$  on the distilled data using the following objective:

$$
\mathcal{L} = -\sum_{j} \sum_{m} y_{j,m} \log \phi_{\theta_{\mathcal{S}}}(\mathbf{x}_{j,m}). \tag{10}
$$

<span id="page-5-4"></span>

# 5. Experiment

This section assesses the efficacy of our proposed method over SOTA methods across diverse datasets and neural architectures, followed by extensive ablation studies.

<span id="page-5-1"></span>

### 5.1. Experimental Setting

We list the settings below (see more details in Appendix [D\)](#page-12-0).

**Datasets.** For low-resolution data  $(32 \times 32)$ , we evaluate our method on two datasets, i.e., CIFAR-10 [\[20\]](#page-8-21) and CIFAR-100 [\[19\]](#page-8-22). For high-resolution data, we conduct experiments on two large-scale datasets including Tiny-ImageNet  $(64 \times 64)$  [\[21\]](#page-8-23) and full ImageNet-1K (224  $\times$  224) [\[6\]](#page-8-0). Moreover, given the fact that most existing dataset distillation methods cannot be extended to large-scale high-resolution datasets, we further consider four widely used ImageNet-1K

<span id="page-5-0"></span>Algorithm 1 RDED: An efficient framework for highresolution dataset distillation (see Appendix [C](#page-12-1) for more implementation details)

**Input:** Original full dataset  $\mathcal{T}$ , a corresponding pretrained observer model  $\phi_{\theta_{\mathcal{T}}}$  and initial  $\mathcal{S} = \emptyset$ .

trained observer model  $\phi_{\theta_{\mathcal{T}}}$  and initial  $S = \emptyset$ .

for 
$$
\mathcal{T}'_c \subset \mathcal{T}_c \subset \mathcal{T}
$$
 do

for  $(\hat{\mathbf{x}}_i, \hat{y}_i) \in \mathcal{T}'_c$  do

Crop  $\hat{\mathbf{x}}_i$  into *K* patches  $\{\xi_{i,k}\}_{k=1}^K$ 

for *k* = 1 to *K* do

Calculate the score 
$$
s_{i,k} = -\ell(\phi_{\theta_{\mathcal{T}}}(\xi_{i,k}), \hat{y}_i)
$$

Select patch  $\xi_{i,*\text{}}$  from  $\{\xi_{i,k}\}_{k=1}^K$  via  $s_{i,*\text{}}$ 

Select top-( $N \times \text{IPC}$ ) patches

for *j* = 1 to IPC do

Squeeze *N* selected patches into  $\mathbf{x}_j$ 

Relabel  $\mathbf{x}_j$  with  $y_j$ 

 $S = S \cup \{(\mathbf{x}_j, y_j)\}$ 

Output: Small distilled dataset *S*

subsets in our evaluation: ImageNet-100 [\[18\]](#page-8-9), ImageNette and ImageWoof [\[1\]](#page-8-6).

Network architectures. Similar to the prior dataset distillation works [\[1,](#page-8-6) [5,](#page-8-8) [13,](#page-8-11) [44,](#page-9-5) [52\]](#page-9-4), we use ConvNet [\[13\]](#page-8-11), ResNet-18/ResNet-101 [\[14\]](#page-8-1), EfficientNet-B0 [\[36\]](#page-9-19), MobileNet-V2 [\[29\]](#page-9-20), as our backbone.

Baselines. We consider SOTA optimization-based dataset distillation methods that can scale to large high-resolution datasets for a broader practical impact:

- MTT [\[1\]](#page-8-6) is the first work that proposes trajectory matchingbased dataset distillation, which can work on *both low and high-resolution datasets*.
- IDM [\[52\]](#page-9-4) introduces an efficient dataset condensation method based on distribution matching, in contrast to computationally intensive optimization-oriented approaches [\[1,](#page-8-6) [51\]](#page-9-9), thus *scaling to ImageNet-100*.
- TESLA [\[5\]](#page-8-8) is the first dataset distillation method *scales to full ImageNet-1K*, which handles huge memory consumption of the MTT-based method with constant memory.
- DATM [\[13\]](#page-8-11) is the first to *outperform the original full dataset training performance* with large IPC.
- SRe<sup>2</sup>L [\[44\]](#page-9-5) is a recent work to *efficiently scale to ImageNet-1K*, and significantly outperforms existing methods on large high-resolution datasets. We consider it as our closest baseline.

Evaluation. Following previous research, we set IPC to 1, 10, and 50. To evaluate cross-architecture generalization, we use the distilled datasets from one neural architecture to train the other neural architectures from scratch and record the validation accuracy (see Table [4\)](#page-7-1). Furthermore, we evaluate

<span id="page-5-2"></span><sup>&</sup>lt;sup>3</sup>We use N and target resolution of distilled images to calculate and set the resolution of the patches, e.g., for an image with  $224 \times 224$  resolution and set  $N = 4$ , the resolution of patches is defined as  $112 \times 112$ .

<span id="page-6-2"></span><span id="page-6-1"></span>

|               |              | ConvNet                  |                          |                          |                          |                | ResNet-18      | ResNet-101     |                                  |                |
|---------------|--------------|--------------------------|--------------------------|--------------------------|--------------------------|----------------|----------------|----------------|----------------------------------|----------------|
| Dataset       | <b>IPC</b>   | <b>MTT</b>               | <b>IDM</b>               | <b>TESLA</b>             | <b>DATM</b>              | RDED (Ours)    | $SRe^2L$       | RDED (Ours)    | $SRe^2L$                         | RDED (Ours)    |
|               |              | $46.3 \pm 0.8$           | $45.6 \pm 0.7$           | $48.5 \pm 0.8$           | $46.9 \pm 0.5$           | $23.5 \pm 0.3$ | $16.6 \pm 0.9$ | $22.9 \pm 0.4$ | $13.7 \pm 0.2$                   | $18.7 \pm 0.1$ |
| CIFAR10       | 10           | $65.3 \pm 0.7$           | $58.6 \pm 0.1$           | $66.4 \pm 0.8$           | $66.8 \pm 0.2$           | $50.2 \pm 0.3$ | $29.3 \pm 0.5$ | $37.1 \pm 0.3$ | $24.3 \pm 0.6$                   | $33.7 \pm 0.3$ |
|               | 50           | $71.6 \pm 0.2$           | $67.5 \pm 0.1$           | $72.6 \pm 0.7$           | $76.1 \pm 0.3$           | $68.4 \pm 0.1$ | $45.0 \pm 0.7$ | $62.1 \pm 0.1$ | $34.9\pm0.1$                     | $51.6 \pm 0.4$ |
|               | $\mathbf{1}$ | $24.3 \pm 0.3$           | $20.1 \pm 0.3$           | $24.8 \pm 0.5$           | $27.9 \pm 0.2$           | $19.6 \pm 0.3$ | $6.6 \pm 0.2$  | $11.0 \pm 0.3$ | $6.2 \pm 0.0$                    | $10.8 \pm 0.1$ |
| CIFAR-100     | 10           | $40.1 \pm 0.4$           | $45.1 \pm 0.1$           | $41.7 \pm 0.3$           | $47.2 \pm 0.4$           | $48.1 \pm 0.3$ | $27.0 \pm 0.4$ | $42.6 \pm 0.2$ | $30.7 \pm 0.3$                   | $41.1 \pm 0.2$ |
|               | 50           | $47.7 \pm 0.2$           | $50.0 \pm 0.2$           | $47.9 \pm 0.3$           | $55.0 \pm 0.2$           | $57.0 \pm 0.1$ | $50.2 \pm 0.4$ | $62.6 \pm 0.1$ | $56.9 \pm 0.1$                   | $63.4 \pm 0.3$ |
|               | 1            | $47.7 \pm 0.9$           |                          |                          |                          | $33.8 \pm 0.8$ | $19.1 \pm 1.1$ | $35.8 \pm 1.0$ | $15.8 \pm 0.6$                   | $25.1 \pm 2.7$ |
| ImageNette    | 10           | $63.0 \pm 1.3$           |                          |                          | ۰                        | $63.2 \pm 0.7$ | $29.4 \pm 3.0$ | $61.4 \pm 0.4$ | $23.4 \pm 0.8$                   | $54.0 \pm 0.4$ |
|               | 50           |                          |                          |                          | ٠                        | $83.8 \pm 0.2$ | $40.9 \pm 0.3$ | $80.4 \pm 0.4$ | $36.5 \pm 0.7$                   | $75.0 \pm 1.2$ |
|               | 1            | $28.6 \pm 0.8$           | $\overline{\phantom{a}}$ | $\overline{a}$           | $\overline{\phantom{0}}$ | $18.5 \pm 0.9$ | $13.3 \pm 0.5$ | $20.8 \pm 1.2$ | $13.4 \pm 0.1$                   | $19.6 \pm 1.8$ |
| ImageWoof     | 10           | $35.8 \pm 1.8$           |                          |                          | $\overline{\phantom{0}}$ | $40.6 \pm 2.0$ | $20.2 \pm 0.2$ | $38.5 \pm 2.1$ | $17.7 \pm 0.9$                   | $31.3 \pm 1.3$ |
|               | 50           |                          |                          |                          | $\overline{\phantom{0}}$ | $61.5 \pm 0.3$ | $23.3 \pm 0.3$ | $68.5 \pm 0.7$ | $21.2 \pm 0.2$                   | $59.1 \pm 0.7$ |
|               |              | $8.8 \pm 0.3$            | $10.1 \pm 0.2$           |                          | $17.1 \pm 0.3$           | $12.0 \pm 0.1$ | $2.62 \pm 0.1$ | $9.7 \pm 0.4$  | $1.9 \pm 0.1$                    | $3.8 \pm 0.1$  |
| Tiny-ImageNet | 10           | $23.2 \pm 0.2$           | $21.9 \pm 0.3$           | ۰                        | $31.1 \pm 0.3$           | $39.6 \pm 0.1$ | $16.1 \pm 0.2$ | $41.9 \pm 0.2$ | $14.6 \pm 1.1$                   | $22.9 \pm 3.3$ |
|               | 50           | $28.0 \pm 0.3$           | $27.7 \pm 0.3$           | $\overline{\phantom{a}}$ | $39.7 \pm 0.3$           | $47.6 \pm 0.2$ | $41.1 \pm 0.4$ | $58.2 \pm 0.1$ | $\textbf{42.5} \pm \textbf{0.2}$ | $41.2 \pm 0.4$ |
|               |              | $\overline{\phantom{a}}$ | $11.2 \pm 0.5$           |                          | ٠                        | $7.1 \pm 0.2$  | $3.0 \pm 0.3$  | $8.1 \pm 0.3$  | $2.1 \pm 0.1$                    | $6.1 \pm 0.8$  |
| ImageNet-100  | 10           | $\overline{\phantom{a}}$ | $17.1 \pm 0.6$           |                          | $\overline{\phantom{0}}$ | $29.6 \pm 0.1$ | $9.5 \pm 0.4$  | $36.0 \pm 0.3$ | $6.4 \pm 0.1$                    | $33.9 \pm 0.1$ |
|               | 50           | $\overline{\phantom{a}}$ | $26.3 \pm 0.4$           | $\overline{a}$           | $\overline{\phantom{a}}$ | $50.2 \pm 0.2$ | $27.0 \pm 0.4$ | $61.6 \pm 0.1$ | $25.7 \pm 0.3$                   | $66.0 \pm 0.6$ |
|               |              | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $7.7 \pm 0.2$            | $\overline{\phantom{a}}$ | $6.4 \pm 0.1$  | $0.1 \pm 0.1$  | $6.6 \pm 0.2$  | $0.6 \pm 0.1$                    | $5.9 \pm 0.4$  |
| ImageNet-1K   | 10           |                          | $\overline{\phantom{a}}$ | $17.8 \pm 1.3$           | $\overline{\phantom{0}}$ | $20.4 \pm 0.1$ | $21.3 \pm 0.6$ | $42.0 \pm 0.1$ | $30.9 \pm 0.1$                   | $48.3 \pm 1.0$ |
|               | 50           | ٠                        | $\overline{\phantom{a}}$ | $27.9 \pm 1.2$           | $\overline{\phantom{a}}$ | $38.4 \pm 0.2$ | $46.8 \pm 0.2$ | $56.5 \pm 0.1$ | $60.8 \pm 0.5$                   | $61.2 \pm 0.4$ |

Table 2. Comparison with the SOTA baseline dataset distillation methods. We use identical neural networks for both dataset distillation and data evaluation. In general, following [\[1,](#page-8-6) [5,](#page-8-8) [52\]](#page-9-4), the ConvNet used for distillation are Conv-3 on CIFAR10 and CIFAR100, Conv-4 on Tiny-ImageNet and ImageNet-1K, Conv-5 on ImageNette and ImageWoof, Conv-6 on ImageNet-100. MTT and TESLA use a down-sampled version of image when distilling  $224 \times 224$  images [\[1,](#page-8-6) [5\]](#page-8-8). Following [\[44\]](#page-9-5), SRe<sup>2</sup>L and RDED use ResNet-18 to retrieve the distilled data, and evaluate on ResNet-18 and ResNet-101. Entries with "-" are absent due to scalability problems. See Appendix [C](#page-12-1) for more details.

the distillation efficiency in Table [3](#page-6-0) by estimating the runtime cost of distilling the image, as well as the peak GPU memory usage.

Implementation details of RDED. We employ a generalized configuration for  $T'$  (c.f. Section [4.2](#page-4-1) for definition), where the size  $|T'|$  is set as 300. We set  $N = 4$  (c.f. Section [4.3](#page-4-2) for definition) for high-resolution datasets and set  $N = 1$ for datasets with resolution less than  $64 \times 64$ .

## 5.2. Main Results

High-resolution datasets. To explore the potential of our approach for real-world applications, we first conduct experiments to compare with the state-of-the-art dataset distillation methods on Tiny-ImageNet and ImageNet-1K (including some subsets, e.g., ImageNet-100). Table [2](#page-6-1) demonstrates that *our proposed method significantly outperforms existing methods or exhibits comparable results with large* IPC = 10 *and* 50*.* However, when IPC comes to 1, our approach struggles to effectively retain the information present in the original dataset, consequently leading to suboptimal outcomes.

Low-resolution datasets. To validate the robustness of our method across different-resolution datasets, we conduct more experiments on diminutive datasets such as CIFAR-10 and CIFAR-100 (see Table [2\)](#page-6-1). Our RDED demonstrates

<span id="page-6-0"></span>

| Architecture    |       | Time Cost (ms) | Peak Memory (GB) |
|-----------------|-------|----------------|------------------|
| ResNet-18       | SRe2L | 2113.23        | 9.14             |
|                 | Ours  | 39.89          | 1.57             |
| MobileNet-V2    | SRe2L | 3783.16        | 12.93            |
|                 | Ours  | 64.97          | 2.35             |
| EfficientNet-B0 | SRe2L | 4412.42        | 11.92            |
|                 | Ours  | 73.16          | 2.34             |

Table 3. Synthesis time and memory consumption ImageNet-1K. We use a single RTX-4090 GPU for all methods to conduct experiments on ImageNet-1K. Time Cost represents the consumption (ms) for each image when generating 100 images simultaneously. Following the official implementation of  $SRe<sup>2</sup>L$  [\[44\]](#page-9-5), the peak value of GPU memory usage is measured with a batch size of 100.

superior performance compared to conventional methods, particularly in scenarios involving larger distilled datasets such as CIFAR-100 with  $IPC = 50$ . However, similar to high-resolution scenarios, its efficacy diminishes when confronted with smaller datasets.

### 5.3. Efficiency Comparison

Table [3](#page-6-0) distinctly showcases the *superior efficiency of our dataset distillation approach in comparison to previous methodologies, demonstrating a significant performance advantage over state-of-the-art methods*. Notably, we present a flexible peak memory scope, allowing dynamic

<span id="page-7-4"></span><span id="page-7-1"></span>

| Verifier\Observer |                                                | ResNet-18 EfficientNet-B0 MobileNet-V2 |                                  |
|-------------------|------------------------------------------------|----------------------------------------|----------------------------------|
| ResNet-18         | $SRe^2L 21.7 \pm 0.6$<br>Ours $ 42.3 \pm 0.6 $ | $11.7 + 0.2$<br>$31.0 \pm 0.1$         | $15.4 + 0.2$<br>$40.4 \pm 0.1$   |
| MobileNet-V2      | $SRe^2L 19.7 \pm 0.1$<br>Ours $34.4 \pm 0.2$   | $9.8 + 0.4$<br>$24.1 \pm 0.8$          | $10.2 + 2.6$<br>$33.8 \pm 0.6$   |
| EfficientNet-B0   | $SRe^2L 25.2 \pm 0.2$<br>Ours $ 42.8 \pm 0.5 $ | $11.4 + 2.5$<br>$33.3 \pm 0.9$         | $20.5 \pm 0.2$<br>$43.6 \pm 0.2$ |

Table 4. Evaluating ImageNet-1K top-1 accuracy on crossarchitecture generalization. Distill dataset with ResNet-18, EfficientNet-B0, and MobileNet-V2, and then versus transfer to each other architecture. We can not conduct experiments for  $SRe<sup>2</sup>L$ when the model using for distillation without batch normalization, which necessitates [\[44\]](#page-9-5). All methods are evaluated with  $IPC = 10$ .

adjustments to the batch size without compromising performance. This efficiency is attributed to the fact that the primary memory consumption in our distillation procedure occurs exclusively during the scoring process of patches, while this process can be executed in parallel for images within a mini-batch<sup>[4](#page-7-2)</sup>. Furthermore, the optimization-free nature of our RDED ensures that the distillation time for an image is solely dependent on the scoring cost determined by the pre-trained teacher model size.

### 5.4. Cross-architecture Generalization

To ensure the generalization capability of our distilled datasets, it is imperative to validate their effectiveness across multiple neural architectures not encountered when distilling datasets. Table [4](#page-7-1) examines our RDED with the SOTA SRe<sup>2</sup>L *and underscores the robust generalization ability of our method*. Our success stems from two key aspects:

- it enables high-realism distilled images (evidenced in [\[2\]](#page-8-7)).
- it exhibits insensitivity to variations in the teacher model.

<span id="page-7-0"></span>

## 5.5. Ablation Study

The effectiveness of RDED hinges on two pivotal factors: the size  $|\mathcal{T}'_c|$  of pre-selected subset  $\mathcal{T}'_c$  (c.f. Section [4.2\)](#page-4-1) and the number of patches  $N$  per distilled image (defined in Section [4.3\)](#page-4-2). In this section, we set  $IPC = 10$  and employ ResNet-18 as the network backbone to examine how these factors influence the diversity and realism of the distilled dataset (see Appendix [D.4](#page-13-0) for investigation on more factors).

On the impact of pre-selected subset size  $|\mathcal{T}'_c|$ . The experimental results in Figure [4](#page-7-3) gives a more intuitive demonstration on the impact of  $|\mathcal{T}'_c|$ , alongside the discussion in Section [4.2:](#page-4-1)

• The performance abruptly drops when  $|\mathcal{T}'_c|$  is equal to  $N \times \text{IPC}$ , i.e., the Stage [1](#page-5-0) in our Algorithm 1 becomes

<span id="page-7-3"></span>Image /page/7/Figure/12 description: The image contains two plots. The left plot shows the Top-1 Accuracy (%) on the y-axis against the Size of Subset |Tc| on the x-axis. There are three lines: a red line with data points at (0, 29), (100, 33), (200, 36), (300, 35), (400, 35), (500, 35), (600, 35); a blue line with data points at (0, 38), (100, 41), (200, 42), (300, 42), (400, 42), (500, 42), (600, 42); and a green line with data points at (0, 48), (100, 50), (200, 53), (300, 53), (400, 52), (500, 52), (600, 51). The right plot shows the Top-1 Accuracy (%) on the y-axis against the Number of Patches N on the x-axis. There are three lines: a red line with data points at (0, 29), (5, 42), (10, 32), (15, 26), (20, 22), (25, 19); a blue line with data points at (0, 37), (5, 45), (10, 39), (15, 32), (20, 27), (25, 20); and a green line with data points at (0, 52), (5, 54), (10, 53), (15, 51), (20, 49), (25, 46).

Figure 4. Ablation study on  $|\mathcal{T}'_c|$  and N, i.e., the pre-selected subset size  $\mathcal{T}'_c$  (left), and the number of patches N within each distilled image (right). The emerald  $\bullet$ , red  $\bullet$ , and blue  $\bullet$  denote ImageNet-10, ImageNet-100, and ImageNet-1K respectively.

the simple uniform random sampling. In this case, the diversity is maximized but the realism is poor, thus resulting in catastrophically degraded performance.

• As  $|\mathcal{T}'_c|$  continuously increases and exceeds a threshold, our framework collects more realistic images from  $\mathcal{T}'_c$  but their patterns may be repeated, thus hurting diversity and consequent performance.

Therefore, a proper  $|\mathcal{T}'_c|$  would balance the trade-off between data diversity and realism. In this instance, a value approaching 300 maximizes the total sum for our target in Equation [\(4\)](#page-3-1), as indicated in Figure [4.](#page-7-3)

On the impact of squeezing  $N$  patches into one distilled **image.** The number of patches  $N$  shares similar patterns as that of  $|\mathcal{T}'_c|$ . Specifically, though we can compress more patches from  $T$  into a distilled dataset  $S$  by increasing  $N$ increases to benefit the data diversity, it also results in a lower resolution for the source patches (see our explanation in Footnote [3\)](#page-5-2), thus hurting the realism. Therefore, a proper number of patches  $N$  is important to achieve our objective in [\(3\)](#page-3-0). Figure [4](#page-7-3) showcases that the validation performance rises to the highest on selected three datasets when  $N = 4$ .

# 6. Conclusion

In this work, we introduce an optimization-free and efficient paradigm which successfully distills a dataset with  $IPC =$ 10 from the entirety of ImageNet-1K, concurrently achieving 42% top-1 validation accuracy with ResNet-18. Furthermore, our method exhibits robust cross-architecture generalization, surpassing SOTA method by a factor of  $2\times$  in performance.

# Acknowledgement

We thank Xinyi Shang, Zexi Li and anonymous reviewers for their precious comments and feedback. This work was supported in part by the National Science and Technology Major Project (No. 2022ZD0115101), the Research Center for Industries of the Future (RCIF) at Westlake University, and the Westlake Education Foundation.

<span id="page-7-2"></span><sup>&</sup>lt;sup>4</sup>Conventional optimization-based dataset distillation methods  $[1, 13, 13]$  $[1, 13, 13]$  $[1, 13, 13]$  $[1, 13, 13]$ [44\]](#page-9-5) have to synthesize a batch of images simultaneously to guarantee its overall quality.

# References

- <span id="page-8-6"></span>[1] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-1) [2,](#page-1-3) [3,](#page-2-4) [6,](#page-5-3) [7,](#page-6-2) [8](#page-7-4)
- <span id="page-8-7"></span>[2] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3739–3748, 2023. [1,](#page-0-1) [2,](#page-1-3) [3,](#page-2-4) [4,](#page-3-4) [8,](#page-7-4) [6](#page-5-3)
- <span id="page-8-20"></span>[3] Cody Coleman, Christopher Yeh, Stephen Mussmann, Baharan Mirzasoleiman, Peter Bailis, Percy Liang, Jure Leskovec, and Matei Zaharia. Selection via proxy: Efficient data selection for deep learning. *arXiv preprint arXiv:1906.11829*, 2019. [5](#page-4-4)
- <span id="page-8-5"></span>[4] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dcbench: Dataset condensation benchmark. *Advances in Neural Information Processing Systems*, 35:810–822, 2022. [1,](#page-0-1) [2,](#page-1-3) [3](#page-2-4)
- <span id="page-8-8"></span>[5] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [1,](#page-0-1) [2,](#page-1-3) [3,](#page-2-4) [4,](#page-3-4) [6,](#page-5-3) [7](#page-6-2)
- <span id="page-8-0"></span>[6] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [1,](#page-0-1) [6,](#page-5-3) [2,](#page-1-3) [3](#page-2-4)
- <span id="page-8-19"></span>[7] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, Jakob Uszkoreit, and Neil Houlsby. An image is worth 16x16 words: Transformers for image recognition at scale. In *International Conference on Learning Representations*, 2021. [5](#page-4-4)
- <span id="page-8-10"></span>[8] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3749–3758, 2023. [2](#page-1-3)
- <span id="page-8-18"></span>[9] Kawin Ethayarajh, Yejin Choi, and Swabha Swayamdipta. Understanding dataset difficulty with V-usable information. In *International Conference on Machine Learning*, pages 5988–6008. PMLR, 2022. [4](#page-3-4)
- <span id="page-8-13"></span>[10] Vitaly Feldman and Chiyuan Zhang. What neural networks memorize and why: Discovering the long tail via influence estimation. *Advances in Neural Information Processing Systems*, 33:2881–2891, 2020. [2](#page-1-3)
- <span id="page-8-27"></span>[11] Edward W Forgy. Cluster analysis of multivariate data: efficiency versus interpretability of classifications. *biometrics*, 21:768–769, 1965. [4](#page-3-4)
- <span id="page-8-2"></span>[12] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. *Advances in neural information processing systems*, 27, 2014. [1](#page-0-1)
- <span id="page-8-11"></span>[13] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation

via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023. [2,](#page-1-3) [6,](#page-5-3) [8,](#page-7-4) [3](#page-2-4)

- <span id="page-8-1"></span>[14] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [1,](#page-0-1) [6,](#page-5-3) [4](#page-3-4)
- <span id="page-8-25"></span>[15] Yang He, Lingao Xiao, Joey Tianyi Zhou, and Ivor Tsang. Multisize dataset condensation. *arXiv preprint arXiv:2403.06075*, 2024. [3](#page-2-4)
- <span id="page-8-3"></span>[16] Sergey Ioffe and Christian Szegedy. Batch normalization: Accelerating deep network training by reducing internal covariate shift. In *International conference on machine learning*, pages 448–456. pmlr, 2015. [1,](#page-0-1) [4](#page-3-4)
- <span id="page-8-4"></span>[17] Jared Kaplan, Sam McCandlish, Tom Henighan, Tom B Brown, Benjamin Chess, Rewon Child, Scott Gray, Alec Radford, Jeffrey Wu, and Dario Amodei. Scaling laws for neural language models. *arXiv preprint arXiv:2001.08361*, 2020. [1,](#page-0-1) [3](#page-2-4)
- <span id="page-8-9"></span>[18] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022. [2,](#page-1-3) [6](#page-5-3)
- <span id="page-8-22"></span>[19] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [6](#page-5-3)
- <span id="page-8-21"></span>[20] Alex Krizhevsky, Vinod Nair, and Geoffrey Hinton. Cifar-10 and cifar-100 datasets. *URl: https://www. cs. toronto. edu/kriz/cifar. html*, 6(1):1, 2009. [6](#page-5-3)
- <span id="page-8-23"></span>[21] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [6](#page-5-3)
- <span id="page-8-26"></span>[22] Ze Liu, Han Hu, Yutong Lin, Zhuliang Yao, Zhenda Xie, Yixuan Wei, Jia Ning, Yue Cao, Zheng Zhang, Li Dong, et al. Swin transformer v2: Scaling up capacity and resolution. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 12009–12019, 2022. [4](#page-3-4)
- <span id="page-8-12"></span>[23] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *Advances in Neural Information Processing Systems*, 35:13877–13891, 2022. [2](#page-1-3)
- <span id="page-8-17"></span>[24] Yi Ma, Doris Tsao, and Heung-Yeung Shum. On the principles of parsimony and self-consistency for the emergence of intelligence. *Frontiers of Information Technology & Electronic Engineering*, 23(9):1298–1323, 2022. [4](#page-3-4)
- <span id="page-8-15"></span>[25] Kristof Meding, Luca M Schulze Buschoff, Robert Geirhos, and Felix A Wichmann. Trivial or impossible–dichotomous data difficulty masks model differences (on imagenet and beyond). *arXiv preprint arXiv:2110.05922*, 2021. [2](#page-1-3)
- <span id="page-8-24"></span>[26] Aaron van den Oord, Yazhe Li, and Oriol Vinyals. Representation learning with contrastive predictive coding. *arXiv preprint arXiv:1807.03748*, 2018. [2](#page-1-3)
- <span id="page-8-14"></span>[27] Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. *Advances in Neural Information Processing Systems*, 34:20596–20607, 2021. [2,](#page-1-3) [4](#page-3-4)
- <span id="page-8-16"></span>[28] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, et al. Learning

transferable visual models from natural language supervision. In *International conference on machine learning*, pages 8748–8763. PMLR, 2021. [3](#page-2-4)

- <span id="page-9-20"></span>[29] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4510–4520, 2018. [6,](#page-5-3) [4](#page-3-4)
- <span id="page-9-21"></span>[30] Claude Elwood Shannon. A mathematical theory of communication. *The Bell system technical journal*, 27(3):379–423, 1948. [2](#page-1-3)
- <span id="page-9-6"></span>[31] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. *arXiv preprint arXiv:2311.17950*, 2023. [1](#page-0-1)
- <span id="page-9-18"></span>[32] Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In *European Conference on Computer Vision*, pages 673–690. Springer, 2022. [6,](#page-5-3) [3](#page-2-4)
- <span id="page-9-24"></span>[33] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [4](#page-3-4)
- <span id="page-9-13"></span>[34] Ben Sorscher, Robert Geirhos, Shashank Shekhar, Surya Ganguli, and Ari Morcos. Beyond neural scaling laws: beating power law scaling via data pruning. *Advances in Neural Information Processing Systems*, 35:19523–19536, 2022. [3,](#page-2-4) [4,](#page-3-4) [5](#page-4-4)
- <span id="page-9-15"></span>[35] Haoru Tan, Sitong Wu, Fei Du, Yukang Chen, Zhibin Wang, Fan Wang, and Xiaojuan Qi. Data pruning via moving-onesample-out. *arXiv preprint arXiv:2310.14664*, 2023. [4,](#page-3-4) [5](#page-4-4)
- <span id="page-9-19"></span>[36] Mingxing Tan and Quoc Le. Efficientnet: Rethinking model scaling for convolutional neural networks. In *International conference on machine learning*, pages 6105–6114. PMLR, 2019. [6,](#page-5-3) [4](#page-3-4)
- <span id="page-9-11"></span>[37] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018. [2](#page-1-3)
- <span id="page-9-2"></span>[38] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196– 12205, 2022. [1,](#page-0-1) [2](#page-1-3)
- <span id="page-9-0"></span>[39] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2](#page-1-3)
- <span id="page-9-12"></span>[40] Max Welling. Herding dynamical weights to learn. In *Proceedings of the 26th Annual International Conference on Machine Learning*, pages 1121–1128, 2009. [3,](#page-2-4) [4,](#page-3-4) [6](#page-5-3)
- <span id="page-9-22"></span>[41] Sebastien C Wong, Adam Gatt, Victor Stamatescu, and Mark D McDonnell. Understanding data augmentation for classification: when to warp? In *2016 international conference on digital image computing: techniques and applications (DICTA)*, pages 1–6. IEEE, 2016. [3](#page-2-4)
- <span id="page-9-7"></span>[42] Yilun Xu, Shengjia Zhao, Jiaming Song, Russell Stewart, and Stefano Ermon. A theory of usable information under

computational constraints. *arXiv preprint arXiv:2002.10689*, 2020. [2,](#page-1-3) [4,](#page-3-4) [1](#page-0-1)

- <span id="page-9-14"></span>[43] Hongxu Yin, Pavlo Molchanov, Jose M Alvarez, Zhizhong Li, Arun Mallya, Derek Hoiem, Niraj K Jha, and Jan Kautz. Dreaming to distill: Data-free knowledge transfer via deepinversion. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 8715–8724, 2020. [4](#page-3-4)
- <span id="page-9-5"></span>[44] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *arXiv preprint arXiv:2306.13092*, 2023. [1,](#page-0-1) [2,](#page-1-3) [3,](#page-2-4) [4,](#page-3-4) [6,](#page-5-3) [7,](#page-6-2) [8](#page-7-4)
- <span id="page-9-1"></span>[45] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023. [1,](#page-0-1) [2,](#page-1-3) [3](#page-2-4)
- <span id="page-9-23"></span>[46] Sangdoo Yun, Dongyoon Han, Seong Joon Oh, Sanghyuk Chun, Junsuk Choe, and Youngjoon Yoo. Cutmix: Regularization strategy to train strong classifiers with localizable features. In *Proceedings of the IEEE/CVF international conference on computer vision*, pages 6023–6032, 2019. [3](#page-2-4)
- <span id="page-9-17"></span>[47] Sangdoo Yun, Seong Joon Oh, Byeongho Heo, Dongyoon Han, Junsuk Choe, and Sanghyuk Chun. Re-labeling imagenet: from single to multi-labels, from global to localized labels. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 2340–2350, 2021. [6](#page-5-3)
- <span id="page-9-16"></span>[48] Sergey Zagoruyko and Nikos Komodakis. Paying more attention to attention: Improving the performance of convolutional neural networks via attention transfer. *arXiv preprint arXiv:1612.03928*, 2016. [5](#page-4-4)
- <span id="page-9-8"></span>[49] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 11950–11959, 2023. [2](#page-1-3)
- <span id="page-9-3"></span>[50] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514– 6523, 2023. [1,](#page-0-1) [2,](#page-1-3) [3](#page-2-4)
- <span id="page-9-9"></span>[51] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [2,](#page-1-3) [3,](#page-2-4) [6](#page-5-3)
- <span id="page-9-4"></span>[52] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023. [1,](#page-0-1) [2,](#page-1-3) [6,](#page-5-3) [7,](#page-6-2) [3](#page-2-4)
- <span id="page-9-10"></span>[53] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022.  $\overline{2}$  $\overline{2}$  $\overline{2}$

# On the Diversity and Realism of Distilled Dataset: An Efficient Dataset Distillation Paradigm

# Supplementary Material

<span id="page-10-0"></span>

## A. Distilled Images Comparison

Several additional examples of distilled images are presented in Figure [6.](#page-15-0) Besides, we conduct a meticulous comparison between our proposed RDED and the closest approach,  $SRe<sup>2</sup>L$ . The distilled images generated by  $SRe<sup>2</sup>L$  are scrutinized in Figure [7,](#page-16-0) revealing two noteworthy observations:

- $SRe<sup>2</sup>L$  exhibits a limitation in generating diverse features within each distilled image.
- The diversity and realism of distilled images within each class are notably lacking.

In contrast, our proposed method, RDED, demonstrates a superior capability to achieve high diversity in both the features within individual images and across images within each class, all while maintaining a high level of realism.

<span id="page-10-1"></span>

## B. V-information Theory

### B.1. Definitions

The following definitions, as outlined by Xu et al. [\[42\]](#page-9-7), establish the groundwork for our discussion:

**Definition 2** (Predictive Family). *Let*  $\Omega = \{f : \mathcal{X} \cup \{\emptyset\} \rightarrow$  $P(Y)$ *}.*  $V \subseteq \Omega$  *is a predictive family if it satisfies* 

<span id="page-10-2"></span>
$$
\forall f \in \mathcal{V}, \forall P \in \text{range}(f), \quad \exists f' \in \mathcal{V}, \tag{11}
$$

s.t.  $\forall x \in X, f'[x] = P, f'[\varnothing] = P.$ 

A predictive family denotes a collection of permissible predictive models (observers) available to an agent, often constrained by computational or statistical limitations. Xu et al. [\[42\]](#page-9-7) term the supplementary criterion in [\(2\)](#page-10-2) as *optional ignorance*. In essence, this implies that within the framework of the subsequent prediction game we delineate, the agent possesses the discretion to disregard the provided side information at thier discretion.

Definition 3. *Consider random variables* X *and* Y *with corresponding sample spaces* X *and* Y*. Let* ∅ *denote a null input that imparts no information about* Y *. Within the context of a predictive family*  $V \subseteq \Omega = \{f : \mathcal{X} \cup \emptyset \rightarrow \emptyset\}$  $P(Y)$ }, the **predictive**  $V$ -entropy is defined as:

<span id="page-10-4"></span>
$$
H_{\mathcal{V}}(Y|\varnothing) = \inf_{f \in \mathcal{V}} \mathbb{E}_{y \sim Y} \left[ -\log f[\varnothing](y) \right]. \tag{12}
$$

*Similarly, the conditional* V*-entropy is expressed as:*

<span id="page-10-3"></span>
$$
H_{\mathcal{V}}(Y|X) = \inf_{f \in \mathcal{V}} \mathbb{E}[-\log f[\mathbf{x}](y)].
$$
 (13)

*Here,* log *quantifies the entropies in nats.*

In essence,  $f[x]$  and  $f[\emptyset]$  generate probability distributions over the labels. The objective is to identify  $f \in V$  that maximizes the log-likelihood of the label data, both with [\(13\)](#page-10-3) and without the input [\(12\)](#page-10-4).

Definition 4. *Consider random variables* X *and* Y *with respective sample spaces* X *and* Y*. Within the context of a predictive family* V*, the* V*-information is defined as:*

$$
I_{\mathcal{V}}(X \to Y) = H_{\mathcal{V}}(Y|\varnothing) - H_{\mathcal{V}}(Y|X).
$$
 (14)

Given the finite nature of the dataset, the estimated  $V$ information may deviate from its true value. Xu et al. [\[42\]](#page-9-7) establish PAC bounds for this estimation error, with less complex  $V$  and larger datasets yielding more precise bounds. Besides, several key properties of  $V$ -information, enumerated by Xu et al. [\[42\]](#page-9-7), include:

- *Non-Negativity:*  $I_V(X \rightarrow Y) \geq 0$
- *Independence:* If X is independent of  $Y, I_V(X \to Y) =$  $I_{\mathcal{V}}(Y \to X) = 0.$
- *Monotonicity:* For  $V \subseteq U$ ,  $H_V(Y|\emptyset) \geq H_U(Y|\emptyset)$  and  $H_{\mathcal{V}}(Y|X) > H_{\mathcal{U}}(Y|X).$

#### B.2. Intuition of V-information on Distilled Dataset

Maximizing the V-information  $I_{\mathcal{V}}(X \to Y)$  for real-world datasets proves intractable, primarily attributed to the inherent disparity between the boundless information sources and the constrained capabilities of observers within the predictive family  $V$ . A promising avenue arises, however, in the form of distilled datasets, wherein information is derived from a finite original full dataset. This ensures the existence of an optimal predictive family  $\mathcal{V} \subseteq \Omega$  exemplified by observer models trained on the original full dataset. Consequently, the realism of the distilled dataset can be precisely assessed by leveraging this (almost) optimal predictive family.

Furthermore, the upper bound of diversity in the distilled dataset can be reliably guaranteed by the finite information (diversity) encapsulated within the original full dataset. This stands in stark contrast to the challenging task of limiting the diversity inherent in real-world datasets.

Data realism and  $V$ -information. Consider an observer (predictive) family  $\mathcal V$  capable of mapping image input X to its corresponding label output  $Y$ . If we transform the images  $X$  into encrypted versions or introduce additional noisy features beyond their natural background noise, predicting Y given  $X$  with the same  $V$  becomes more challenging.

To capture this intuition, a framework termed  $V$ -information [\[42\]](#page-9-7) generalizes Shannon information <sup>[5](#page-11-0)</sup>, measuring how much information can be extracted from  $X$  about  $Y$ when constrained to observers in V, denoted as  $I_V(X \to Y)$ . When  $V$  encompasses an infinite set of observers, corresponding to unbounded computation, V-information reduces to Shannon information.

Likewise, unrealistic output labels for  $Y$ , such as encrypted or noisy labels, or even simplistic one-hot labels, prove inadequate in representing the precise information contained within images  $X$ . This inadequacy leads to diminished predictive accuracy, even when employing robust observers from the set  $V$ .

Data diversity from the perspective of  $\mathcal V$ -information. V-information  $I_V(X \to Y)$  serves as a conceptual tool for gauging the interconnected information between images  $X$ and labels Y. Consequently, this measurement is inherently influenced by the overall amount of information within both images  $X$  and labels  $Y$ . However, in the context of natural image datasets like ImageNet-1K [\[6\]](#page-8-0), the diversity (information entropy) between images  $X$  and labels  $Y$  is notably imbalanced. Specifically, the labels Y often encompass considerably less information compared to the images  $X$ , thereby constraining V-information  $I_V(X \to Y)$ .

Summary. *Enhancing the diversity and realism of both the input* X *and the output* Y *in a dataset necessitates maximizing the* V-information  $I_V(X \to Y)$ *.* 

### B.3. Maximizing V-information in Practice

*Maximizing diversity of distilled data.* Consider a predictive family  $V = \{\phi_h, \phi_{\theta_{\mathcal{T}}}\}\$  and a distilled dataset  $\mathcal{S}_c$  =  $(X_c, Y_c)$  for class c dataset  $\mathcal{T}_c$ , we assume:

$$
\forall \mathcal{S}_c, \, \exists h \in \mathcal{H}, \, \text{s.t.} \, \mathcal{S}_c = \{ (\mathbf{x}_c, y_c) \mid y_c = h(\mathbf{x}_c) \}, \quad (15)
$$

where  $\mathcal{H} = \{h : \mathcal{X} \to \mathcal{Y}\}\$ . This assumption establishes the upper bound of diversity term for a distilled dataset  $S_c$ , defined by the  $\mathcal V$ -entropy as follows:

$$
H_V(Y_c|\varnothing) \\
= \inf_{f \\in V} \mathbb{E}[-\log f[\varnothing](y_c)] \\
= \inf_{f \\in V} \mathbb{E}[-\log f[\varnothing](h(\mathbf{x}_c))] \quad (16) \\
\leq \inf_{f \\in V} \mathbb{E}[-\log f[\varnothing](\mathbf{x}_c)] \\
= H_V(X_c|\varnothing)
$$

Given  $\mathcal{T}_c = (\hat{X}_c, \hat{Y}_c)$ , where  $(\hat{X}_c, \hat{Y}_c) := \{(\hat{\mathbf{x}}, \hat{y}) | (\hat{\mathbf{x}}, \hat{y}) \in$  $\mathcal{T}, \hat{y} = c$ , we have:

$$
H_V(\mathcal{T}_c|\varnothing)
$$
  
=  $H_V((\hat{X}_c, \hat{Y}_c)|\varnothing)$   
=  $\inf_{f o V} \mathbb{E}[-\log f[\varnothing](\hat{\mathbf{x}}_c, \hat{y}_c)]$   
=  $\inf_{f o V} \mathbb{E}[-\log f[\varnothing](\hat{\mathbf{x}}_c, c)]$   
=  $\inf_{f o V} \mathbb{E}[-\log f[\varnothing](\hat{\mathbf{x}}_c)]$   
 $\geq \inf_{f o V} \mathbb{E}[-\log f[\varnothing](\mathbf{x}_c)]$   
 $\geq H_V(Y_c|\varnothing).$ 

Consequently, the above theoretical analysis can be extended to the entire dataset T and obtain that:  $H_V(Y|\varnothing) \leq$  $H_{\mathcal{V}}(X|\emptyset) \leq H_{\mathcal{V}}(\mathcal{S}|\emptyset) \leq H_{\mathcal{V}}(\mathcal{T}|\emptyset) = C$ , where C is a constant for a certain  $T$ . Thus, we obtain:

$$
H_{\mathcal{V}}(Y|\varnothing) \propto H_{\mathcal{V}}(Y|\varnothing)/H_{\mathcal{V}}(\mathcal{T}|\varnothing) \le 1. \qquad (18)
$$

If we maximize the diversity term  $H<sub>V</sub>(Y | \varnothing)$ , then the ratio  $H_{\mathcal{V}}(Y|\varnothing)/H_{\mathcal{V}}(\mathcal{T}|\varnothing) = 1$  and  $H_{\mathcal{V}}(\mathcal{S}|\varnothing) = H_{\mathcal{V}}(\mathcal{T}|\varnothing)$ .  $\Box$ 

*Maximizing realism of distilled data.* Given a predictive family  $\mathcal{V} = \{\phi_h, \phi_{\boldsymbol{\theta}_\mathcal{T}}\}$  and a distilled dataset  $\mathcal{S} = (X, Y)$ , our objective is to minimize the realism term defined by the conditional V-entropy:

$$
H_{\mathcal{V}}(Y|X)
$$

$$
= \inf_{f \in \mathcal{V}} \mathbb{E}[-\log f[\mathbf{x}](y)] \quad (19)
$$

$$
\leq \mathbb{E}[-\log \phi_{h}[\mathbf{x}](y)] + \mathbb{E}[-\log \phi_{\theta_{\mathcal{T}}}[\mathbf{x}](y)].
$$

To estimate the density value  $f[x](y)$ , we adopt the approach proposed by Oord et al. [\[26\]](#page-8-24):

$$
f[\mathbf{x}](y) = \frac{\exp(-\ell(f(\mathbf{x}), y))}{\mathbb{E}_{y' \in Y}[\exp(-\ell(f(\mathbf{x}), y'))]},
$$
 (20)

leading to:

$$
H_V(Y|X)
$$

$$
\leq \mathbb{E}[-\log \frac{\exp(-\ell(\phi_h(\mathbf{x}), y))}{\mathbb{E}_{y' \in Y}[\exp(-\ell(\phi_h(\mathbf{x}), y'))]}] + \mathbb{E}[-\log \frac{\exp(-\ell(\phi_{\theta_T}(\mathbf{x}), y))}{\mathbb{E}_{y' \in Y}[\exp(-\ell(\phi_{\theta_T}(\mathbf{x}), y'))]}\tag{21}
$$

Assuming the function  $\ell(\cdot)$  is symmetric, i.e.,

$$
\forall z_1, z_2, \text{ s.t. } \ell(z_1, z_2) = \ell(z_2, z_1), \tag{22}
$$

<span id="page-11-0"></span> $5$ The conventional approach of using Shannon [\[30\]](#page-9-21)'s mutual information  $I(X; Y)$  is not suitable in this context. This metric remains unchanged after the transformation of  $X$ , as it permits unbounded computation, including any necessary for the inverse transformation of images.

thus, we derive an alternative objective for minimization:

$$
H_V(Y|X)
$$
  
$$
\propto \mathbb{E}[-\log \frac{\exp(-\ell(\phi_h(\mathbf{x}), \phi_{\theta_{\tau}}(\mathbf{x})))}{\mathbb{E}_{\mathbf{x} \in X}[\exp(-\ell(\phi_h(\mathbf{x}), \phi_{\theta_{\tau}}(\mathbf{x}))))}]
$$
  
$$
+ \mathbb{E}[-\log \frac{\exp(-\ell(\phi_{\theta_{\tau}}(\mathbf{x}), y))}{\mathbb{E}_{y' \in Y}[\exp(-\ell(\phi_{\theta_{\tau}}(\mathbf{x}), y'))]}]
$$
  

$$
\propto \mathbb{E}[-\log \exp(-\ell(\phi_h(\mathbf{x}), \phi_{\theta_{\tau}}(\mathbf{x}))))]
$$
  
$$
+ \mathbb{E}[-\log \exp(-\ell(\phi_{\theta_{\tau}}(\mathbf{x}), y))]
$$
  
$$
= \mathbb{E}[\ell(\phi_h(\mathbf{x}), \phi_{\theta_{\tau}}(\mathbf{x})) + \ell(\phi_{\theta_{\tau}}(\mathbf{x}), y)].
$$

This analysis underpins our strategy to enhance the realism of distilled data by minimizing  $H<sub>V</sub>(Y|X)$ , we focus on samples **x** that minimize  $\ell(\phi_h(\mathbf{x}), \phi_{\theta_{\mathcal{T}}}(\mathbf{x}))$  and set  $y = \phi_{\theta_{\mathcal{T}}}(\mathbf{x})$ .

<span id="page-12-1"></span>

# C. Detailed Implementation

### C.1. Pre-training Observer Models

Following prior studies [\[1,](#page-8-6) [13,](#page-8-11) [44,](#page-9-5) [52\]](#page-9-4), we employ pretrained observer models to distill the dataset, as illustrated in Table [2:](#page-6-1) 1) ResNet-18 for ImageNet-10, ImageNette, ImageWoof, ImageNet-100, ImageNet-1K; 2) modified ResNet-18 for CIFAR-10, CIFAR-100 and Tiny-ImageNet; 3) ConvNet-3 for CIFAR-10, CIFAR-100; 4) ConvNet-4 for Tiny-ImageNet; 5) ConvNet-5 for ImageWoof, ImageNette; 6) ConvNet-6 for ImageNet-100.

### C.2. Implementing RDED algorithm.

To gain an intuitive understanding the Algorithm [1](#page-5-0) of our proposed RDED, we expound on the implementation details in this section. Given a comprehensive real dataset  $\mathcal{T}$ , such as ImageNet-1K [\[6\]](#page-8-0), we define three tasks involving distilling this dataset into smaller datasets with distinct IPC values, specifically,  $IPC = 50$ , 10, and 1. Remarkably, our RDED demonstrates the capability to encompass multisize distilled datasets through a single distillation process, effectively handling those with  $IPC = 50$ , 10, and 1.

Extracting key patches. For each class set  $\mathcal{T}_c$  we uniformly pre-select a subset contains 300 images denoted as  $\mathcal{T}'_c = {\hat{\mathbf{x}}_i}_{i=1}^{300}$ . Each pre-selected image  $\hat{\mathbf{x}}_i$  undergoes random cropping into  $K = 5$  patches<sup>[6](#page-12-2)</sup>. These patches are represented as  $\{\xi_{i,k}\}_{k=1}^{K=5}$ , and the realism score  $s_{i,k}$  =  $-\ell(\phi_{\theta_{\mathcal{T}}}(\xi_{i,k}), y_i)$  is calculated for each patch  $\xi_{i,k}$ , resulting in a set of scores  $\{s_{i,k}\}_{k=1}^{K=5}$ . Subsequently, the key patch  $\xi_{i,\star}$  with the highest realism score  $s_{i,\star}$  is selected to represent the corresponding image  $x_i$ . This process yields a key patch set with scores  $\{\xi_{i,\star}, s_{i,\star}\}_{i=1}^{300}$ , which is stored for future use.

Capturing class information. We prioritize key patches, denoted as  $\{\xi_{i,\star}\}_{i=1}^{300}$ , based on their associated scores  ${s_{i,\star}}_{i=1}^{300}$  to construct a well-ordered set  ${\{\xi_{j,\star}\}}_{j=1}^{300}$ . In addressing the initial task of synthesizing a refined dataset with IPC = 50, we strategically choose the top- $(200 =$ IPC  $\times$  N) key patches from the set, denoted as  $\{\xi_{j,\star}\}_{j=1}^{200}$ . Likewise, for the two subsequent tasks, characterized by  $IPC = 10$  and  $IPC = 1$ , we iteratively refine the selection by opting for the top-40 and top-4 key patches, denoted as  $\{\xi_{j,\star}\}_{j=1}^{40}$  and  $\{\xi_{j,\star}\}_{j=1}^4$ , respectively.

Images reconstruction. To construct the ultimate image  $x_j$ , we systematically draw  $N = 4$  distinct patches  $\{\xi_{j,\star}\}_{j=1}^{N=4}$  without replacement and concatenate them. This procedure is iterated times to generate the ultimate distilled image set  $\{\mathbf x_j\}_{j=1}^{\text{IPC}}$ .

Labels reconstruction. In accordance with the methodology presented in  $SRe^{2}L$  [\[44\]](#page-9-5), we undertake the process of relabeling the distilled images through the generation and storage of region-level soft labels, denoted as  $y_i$ , employing Fast Knowledge Distillation [\[32\]](#page-9-18). To achieve this, for each distilled image  $x_i$ , we perform random cropping into several patches, concurrently documenting their coordinates on the image  $x_j$ . Subsequently, soft labels  $y_{j,m}$  are generated and stored for each  $m$ -th patch, ultimately culminating in the aggregation of these labels to form the comprehensive  $y_i$ .

## C.3. Training on Distilled Dataset

Following prior investigations [\[4,](#page-8-5) [44,](#page-9-5) [45\]](#page-9-1), we employ dataaugmentation techniques, namely RandomCropResize [\[41\]](#page-9-22) and CutMix [\[46\]](#page-9-23). Further elucidation is available in our publicly accessible code repository at *https://to-be-released*.

<span id="page-12-0"></span>

# D. Experiment

In this section, unless otherwise specified, we adopt ResNet-18 as the default neural network backbone for both the distillation process and subsequent evaluation. The parameters  $IPC = 10$  and pre-selected subset size  $|\mathcal{T}'_c| = 300$  are consistently applied. For high-resolution datasets, we set the number of patches  $N = 4$  within one distilled image, while for datasets with a resolution lower than  $64 \times 64$ , we use  $N = 1$ . All settings are consistent with those in Section [5.](#page-5-4)

### D.1. Multisize Dataset Distillation

In their recent work, He et al. [\[15\]](#page-8-25) introduced Multisize Dataset Condensation (MDC), a novel approach that consolidates multiple condensation processes into a unified procedure. This innovative method produces datasets with varying sizes, offering dual advantages:

• DC eliminates the necessity for extra condensation processes when distilling multiple datasets with varying IPC.

<span id="page-12-2"></span><sup>&</sup>lt;sup>6</sup>We empirically set  $K = 5$ , although smaller values, such as  $K = 1$ , can be chosen for expedited implementation of our algorithm RDED.

<span id="page-13-3"></span>

|                   | RDED (Ours)    |                 |                |                |               | SRe <sup>2</sup> L |                 |                |
|-------------------|----------------|-----------------|----------------|----------------|---------------|--------------------|-----------------|----------------|
| Verifier\Observer | ResNet-18      | EfficientNet-B0 | MobileNet-V2   | $VGG-11$       | Swin-V2-Tiny  | ResNet-18          | EfficientNet-B0 | MobileNet-V2   |
| ResNet-18         | $42.3 + 0.6$   | $31.0 + 0.1$    | $40.4 + 0.1$   | $36.6 + 0.1$   | $17.2 + 0.2$  | $21.7 + 0.6$       | $11.7 + 0.2$    | $15.4 \pm 0.2$ |
| EfficientNet-B0   | $42.8 + 0.5$   | $33.3 + 0.9$    | $43.6 \pm 0.2$ | $35.8 + 0.5$   | $14.8 + 0.1$  | $25.2 + 0.2$       | $11.4 + 2.5$    | $20.5 \pm 0.2$ |
| MobileNet-V2      | $34.4 + 0.2$   | $24.1 \pm 0.8$  | $33.8 \pm 0.6$ | $28.7 \pm 0.2$ | $11.8 + 0.3$  | $19.7 \pm 0.1$     | $9.8 + 0.4$     | $10.2 \pm 2.6$ |
| $VGG-11$          | $22.7 \pm 0.1$ | $16.5 \pm 0.8$  | $21.6 \pm 0.2$ | $23.5 \pm 0.3$ | $7.8 \pm 0.1$ | $16.5 \pm 0.1$     | $9.3 \pm 0.1$   | $10.6 \pm 0.1$ |
| Swin-V2-Tiny      | $17.8 \pm 0.1$ | $19.7 + 0.3$    | $18.1 \pm 0.2$ | $15.3 + 0.4$   | $12.1 + 0.2$  | $9.6 + 0.3$        | $10.2 + 0.1$    | $7.4 + 0.1$    |

Table 5. Evaluating ImageNet-1K top-1 accuracy on cross-architecture generalization. Distill dataset with VGG-11 [\[33\]](#page-9-24), Swin-V2-Tiny [\[22\]](#page-8-26), ResNet-18 [\[14\]](#page-8-1), EfficientNet-B0 [\[36\]](#page-9-19), MobileNet-V2 [\[29\]](#page-9-20), and then versus transfer to other each other architecture.

• It facilitates a reduction in storage requirements by reusing condensed images.

Remarkably, our proposed RDED, also exhibits a mechanism that enables the synthesis of distilled datasets with adaptable IPC without incurring additional computational overhead (c.f. Section [C\)](#page-12-1). For a comprehensive comparison, the superior performance of our RDED over MDC on larger distilled datasets is demonstrated in Table [6.](#page-13-1)

<span id="page-13-1"></span>

| Method \ IPC | CIFAR-10    |             |             | CIFAR-100   |             |             |
|--------------|-------------|-------------|-------------|-------------|-------------|-------------|
|              | 1           | 10          | 50          | 1           | 10          | 50          |
| MDC          | <b>47.8</b> | <b>62.6</b> | <b>74.6</b> | <b>26.3</b> | 41.4        | 53.7        |
| Ours         | 23.5        | 50.2        | 68.4        | 19.6        | <b>50.2</b> | <b>57.0</b> |

Table 6. Comparison with Multisize Dataset Condensation. The top-1 validation accuracy is evaluated when both MDC and our RDED are targeting at distilling dataset with  $IPC = 50$ . The other two distilled datasets with  $IPC = 10$  and  $IPC = 1$  are subsets from the one with  $IPC = 50$ . The neural network backbone used for distillation and evaluation is Conv-3.

## D.2. CoreSet Selection Baselines

In our investigation, we assess the top-1 validation accuracy resulting from the application of three CoreSet selection strategies for dataset distillation: 1) Random; 2) Herding [\[40\]](#page-9-12); 3) K-Means [\[11\]](#page-8-27). The outcomes, as depicted in Table [7,](#page-13-2) indicate catastrophically poor performance when employing these selection methods directly in the context of dataset distillation.

<span id="page-13-2"></span>

| Dataset       | Random         | Herding        | K-Means        |
|---------------|----------------|----------------|----------------|
| ImageNet-10   | $36.7 \pm 0.1$ | $33.8 \pm 0.4$ | $36.5 \pm 0.3$ |
| ImageNet-100  | $10.8 \pm 0.2$ | $12.6 \pm 0.1$ | $13.5 \pm 0.4$ |
| ImageNet-1K   | $4.4 \pm 0.1$  | $5.8 \pm 0.1$  | $5.5 \pm 0.1$  |
| Tiny-ImageNet | $7.5 \pm 0.1$  | $9.0 \pm 0.3$  | $8.9 \pm 0.2$  |
| CIFAR-100     | $10.9 \pm 0.1$ | $13.3 \pm 0.3$ | $12.9 \pm 0.1$ |
| CIFAR-10      | $25.1 \pm 0.5$ | $28.4 \pm 0.1$ | $27.7 \pm 0.2$ |

Table 7. Comparison of different CoreSet selection-based dataset distillation baselines. Experiments are carried out to evaluate three widely used coreset selection methods.

### D.3. Cross-architecture Generalization

We expanded our experimental evaluations by incorporating various neural network architectures that lack batch normalization [\[16,](#page-8-3) [44\]](#page-9-5). This extension aims to thoroughly assess the cross-architecture generalization capabilities of our proposed RDED. The results presented in Table [5](#page-13-3) unequivocally demonstrate the superior performance of RDED in comparison to the SOTA method  $SRe<sup>2</sup>L$ . Notably, our algorithm exhibits remarkable effectiveness even in scenarios characterized by substantial architectural disparities, such as knowledge transfer from ResNet-18 to Swin-V2-Tiny.

<span id="page-13-0"></span>

## D.4. Detailed Ablation Study

In addition to the experiments detailed in Section [5.5,](#page-7-0) we conduct a more comprehensive ablation study, delving into the various approaches and hyperparameters employed in our proposed RDED.

On the impact of  $|\mathcal{T}'_c|$  and N. To assess the influence of the pre-selected subset size  $|\mathcal{T}'_c|$  and the number of patches within each distilled image  $N$ , our experiments are extended to lower-resolution datasets, namely Tiny-ImageNet, CIFAR-10, and CIFAR-100. Figure [5](#page-13-4) illustrates that the configurations with  $|T'_c| = 300$  and  $N = 1$  are suitable for lowresolution datasets.

<span id="page-13-4"></span>Image /page/13/Figure/15 description: The image contains two line graphs. The left graph plots "Top-1 Accuracy (%)" on the y-axis against "Size of Subset |Tc|" on the x-axis, ranging from 0 to 400. It shows three lines: a purple line peaking around 44% at 200, a teal line fluctuating between 40% and 42%, and a yellow line rising from 32% to a peak of 39% at 300, then dropping slightly. The right graph plots "Top-1 Accuracy (%)" on the y-axis against "Number of Patches N" on the x-axis, ranging from 0 to 25. It shows three lines: a purple line decreasing from 43% to 15%, a teal line decreasing from 40% to 12%, and a yellow line decreasing from 36% to 21%. The y-axis scale for both graphs ranges from 15 to 45.

Figure 5. Ablation study on  $|\mathcal{T}'_c|$  and N, i.e., the pre-selected subset size  $\mathcal{T}'_c$  (left), and the number of patches N within each distilled image (right). The lemon  $\bullet$ , purple  $\bullet$ , and turquoise  $\bullet$ denote CIFAR-10, CIFAR-100, and Tiny-ImageNet respectively.

<span id="page-14-0"></span>

| Dataset       | Original       | $+EKP$         | $+CCI$         | $+IR$                    | $+LR$          |
|---------------|----------------|----------------|----------------|--------------------------|----------------|
| ImageNet-10   | $30.6 \pm 0.4$ | $34.5 \pm 1.1$ | $39.6 \pm 1.6$ | $49.9 \pm 1.5$           | $54.3 \pm 2.7$ |
| ImageNet-100  | $8.2 \pm 0.2$  | $9.8 \pm 0.1$  | $15.0 \pm 0.5$ | $24.1 \pm 0.1$           | $35.9 \pm 0.1$ |
| $ImageNet-1K$ | $3.2 \pm 0.1$  | $3.8 \pm 0.1$  | $7.2 \pm 0.3$  | $15.2 \pm 0.1$           | $42.1 \pm 0.1$ |
| Tiny-ImageNet | $6.9 \pm 0.1$  | $8.8 \pm 0.1$  | $15.7 \pm 0.2$ | -                        | $41.9 \pm 0.2$ |
| CIFAR-100     | $11.8 \pm 0.1$ | $13.2 \pm 0.3$ | $18.6 \pm 0.3$ | $\overline{\phantom{a}}$ | $42.6 \pm 0.1$ |
| CIFAR-10      | $27.7 \pm 0.6$ | $26.8 \pm 0.2$ | $27.8 \pm 0.5$ | $\overline{\phantom{a}}$ | $35.8 \pm 0.0$ |

Table 8. Effectiveness of accumulated techniques in RDED. The validation accuracy undergoes a gradual evolution as we sequentially apply the four techniques in our RDED. Entries marked with "-" are absent because of the  $N = 1$  setting for low-resolution datasets, rendering the Images Reconstruction (IR) step impractical.

Effectiveness of each technique in RDED. To validate the effectiveness of all four components within our RDED, we conduct additional ablation studies for each of them, namely, Extracting Key Patches (EKP), Capturing Class Information (CCI), Images Reconstruction (IR), and Labels Reconstruction (LR), corresponding to the techniques outlined in Sections [4.2](#page-4-1) and [4.3.](#page-4-2) Table [8](#page-14-0) illustrates that all four techniques employed in RDED are essential for achieving the remarkable final performance. Furthermore, a plausible hypothesis suggests that LR plays a crucial role in generating more informative (diverse) and aligned (realistic) labels for distilled images, thereby significantly enhancing performance.

<span id="page-14-1"></span>

| Dataset       | Random     | Herding           | K-Means           | Realism           |
|---------------|------------|-------------------|-------------------|-------------------|
| ImageNet-10   | 44.7 ± 2.5 | 47.9 ± 0.3        | 49.3 ± 1.1        | <b>53.3 ± 0.1</b> |
| ImageNet-100  | 29.8 ± 0.7 | 29.7 ± 0.5        | 28.9 ± 0.1        | <b>36.0 ± 0.3</b> |
| ImageNet-1K   | 37.9 ± 0.5 | 38.4 ± 0.1        | 38.2 ± 0.1        | <b>42.0 ± 0.1</b> |
| Tiny-ImageNet | 40.2 ± 0.0 | 41.1 ± 0.1        | 40.1 ± 0.1        | <b>41.9 ± 0.2</b> |
| CIFAR-100     | 41.4 ± 0.5 | <b>42.6 ± 0.1</b> | 41.8 ± 0.1        | <b>42.6 ± 0.1</b> |
| CIFAR-10      | 34.3 ± 0.1 | 35.5 ± 0.6        | <b>37.9 ± 0.3</b> | 35.8 ± 0.1        |

Table 9. Comparison of different patch selection strategies in RDED. Experiments are conducted to compare our proposed realism-score-based data selection strategy over three widely used coreset selection methods.

Effectiveness of selecting patches through realism socre. Table [9](#page-14-1) demonstrates that our realism-score-based selection method, specifically the Capturing Class Information (CCI) technique outlined in Algorithm [1,](#page-5-0) consistently outperforms alternative approaches, except for CIFAR-10. A plausible inference is that the selection of more realistic images contributes to the observer model's ability to reconstruct correspondingly realistic labels (cf. Section [4.3\)](#page-4-2), thereby optimizing our objective [\(3\)](#page-3-0).

<span id="page-15-0"></span>Image /page/15/Picture/0 description: This image displays a grid of fruit images, organized into six rows labeled (a) through (f). Row (a) shows a random selection of original dataset images, featuring items like strawberries, oranges, a lemon, a pomegranate, a fig branch, and a green bell pepper. Rows (b) through (e) present generated images from different methods: MTT [1], GLaD [2], SRe2L [44], and Herding [40], respectively. These rows showcase stylized or abstract representations of fruits and vegetables. Row (f), labeled RDED (Ours), displays a collection of generated fruit images, including pineapples, bananas, strawberries, oranges, lemons, apples, figs, and bell peppers, with a more realistic appearance compared to the previous generated rows. The overall image serves as a visual comparison of different image generation techniques applied to a fruit dataset.

Figure 6. Visualization of images synthesized using various dataset distillation methods. We consider the ImageNet-Fruits [\[1\]](#page-8-6) dataset, comprising a total of 10 distinct fruit types.

<span id="page-16-0"></span>Image /page/16/Picture/0 description: The image displays two rows of generated images, labeled (a) and (b). Row (a) shows abstract, artistic renderings of what appear to be birds or animals in various poses, with a consistent color palette of browns, yellows, and reds. Row (b) presents a grid of realistic fruit images, organized into four sections. The top section features pineapples, the second section displays bananas, the third section showcases pomegranates, and the bottom section exhibits figs. The overall presentation is a comparison of generative image techniques, with (a) representing a specific method (SRe2L [44]) and (b) likely representing a different method or dataset.

(b) RDED (Ours)

Figure 7. Visualization of images synthesized using two dataset distillation methods. We consider a subset of the ImageNet-Fruits [\[1\]](#page-8-6) dataset, comprising a total of 4 distinct fruit types.
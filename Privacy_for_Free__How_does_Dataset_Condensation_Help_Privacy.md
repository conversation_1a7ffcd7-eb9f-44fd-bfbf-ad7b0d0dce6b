# Privacy for Free: How does Dataset Condensation Help Privacy?

<PERSON><PERSON><sup>1\*</sup> <PERSON><sup>2</sup> <PERSON><PERSON><PERSON><sup>3</sup>

## Abstract

To prevent unintentional data leakage, research community has resorted to data generators that can produce differentially private data for model training. However, for the sake of the data privacy, existing solutions suffer from either expensive training cost or poor generalization performance. Therefore, we raise the question whether training efficiency and privacy can be achieved simultaneously. In this work, we for the first time identify that dataset condensation (DC) which is originally designed for improving training efficiency is also a better solution to replace the traditional data generators for private data generation, thus providing privacy for free. To demonstrate the privacy benefit of DC, we build a connection between DC and differential privacy, and theoretically prove on linear feature extractors (and then extended to non-linear feature extractors) that the existence of one sample has limited impact  $(O(m/n))$  on the parameter distribution of networks trained on m samples synthesized from  $n(n \gg m)$  raw samples by DC. We also empirically validate the visual privacy and membership privacy of DC-synthesized data by launching both the loss-based and the state-of-the-art likelihoodbased membership inference attacks. We envision this work as a milestone for data-efficient and privacy-preserving machine learning.

## 1. Introduction

Machine learning models are notoriously known to suffer from a wide range of privacy attacks [\(<PERSON><PERSON> et al.,](#page-9-0) [2020\)](#page-9-0), such as model inversion attack [\(<PERSON><PERSON><PERSON> et al.,](#page-9-1) [2015\)](#page-9-1), membership inference attack (MIA) [\(Shokri et al.,](#page-10-0) [2017\)](#page-10-0), property inference attack [\(Melis et al.,](#page-9-2) [2019\)](#page-9-2), etc. The numerous con-

Image /page/0/Figure/8 description: The figure illustrates a privacy-preserving data sharing process. On the left, within a 'Data holder' section, an 'Original dataset' labeled 'T' is shown. An arrow labeled 'DC' points from the original dataset to a 'Privacy Barrier'. To the right of the barrier, in a 'Server' section, a 'Synthetic dataset' labeled 'S' is presented. From the synthetic dataset, two parallel processes emerge: 'Train' leading to a neural network model, and 'Analyse' leading to an image comparison. The 'Train' process is followed by a 'MIA' (Membership Inference Attack) which tests if an element 'x' belongs to the original dataset 'T', resulting in an accuracy of approximately 50%. The 'Analyse' process involves comparing an image, which results in a 'Not similar' outcome when compared to another image.

<span id="page-0-0"></span>Figure 1. DC-synthesized data can be used for privacy-preserving model training and cannot be recovered through MIA and visual comparison analysis.

cerns on data privacy make it impractical for data curators to directly distribute their private data for purpose of interest. Previously, generative models, e.g., generative adversarial networks (GANs) [\(Goodfellow et al.,](#page-9-3) [2014\)](#page-9-3), was supposed to be an alternative of data sharing. Unfortunately, the aforementioned privacy risks exist not only in training with raw data but also in training with synthetic data produced by generative models [\(Chen et al.,](#page-9-4) [2020b\)](#page-9-4). For example, it is easy to match the fake facial images synthesized by GANs with the real training samples from the same identity [\(Web](#page-10-1)[ster et al.,](#page-10-1) [2021\)](#page-10-1). To counter this issue, existing efforts [\(Xie](#page-10-2) [et al.,](#page-10-2) [2018;](#page-10-2) [Wang et al.,](#page-10-3) [2021;](#page-10-3) [Cao et al.,](#page-9-5) [2021;](#page-9-5) [Harder et al.,](#page-9-6) [2021\)](#page-9-6) applied differential privacy (DP) [\(Dwork et al.,](#page-9-7) [2006\)](#page-9-7) to develop differentially private data generators (called DPgenerators), because DP is the *de facto* privacy standard which provides theoretical guarantees of privacy leakage. Data produced by DP-generators can then be applied to various downstream tasks, e.g., data analysis, visualization, training privacy-preserving classifier, etc.

However, due to the noise introduced by DP, the data produced by DP-generators are of low quality, which impedes the utility as training data, i.e., accuracy of the models trained on these data. Thus, more data generated by DPgenerators are needed to obtain good generalization performance, which inevitably decreases the training efficiency.

Recently, the research of dataset condensation (DC) [\(Wang](#page-10-4) [et al.,](#page-10-4) [2018;](#page-10-4) [Sucholutsky & Schonlau,](#page-10-5) [2019;](#page-10-5) [Such et al.,](#page-10-6) [2020;](#page-10-6) [Bohdal et al.,](#page-9-8) [2020;](#page-9-8) [Zhao et al.,](#page-10-7) [2021;](#page-10-7) [Zhao &](#page-10-8) [Bilen,](#page-10-8) [2021b;](#page-10-8)[a;](#page-10-9) [Nguyen et al.,](#page-10-10) [2021a](#page-10-10)[;b;](#page-10-11) [Jin et al.,](#page-9-9) [2022;](#page-9-9) [Cazenavette et al.,](#page-9-10) [2022;](#page-9-10) [Wang et al.,](#page-10-12) [2022\)](#page-10-12) emerges, which aims to condense a large training set into a small synthetic set that is comparable to the original one in terms of training deep neural networks (DNNs). Different from traditional

Work done during internship at Sony AI. <sup>1</sup>Department of Computer Science and Engineering, Shanghai Jiao Tong University  $2$ School of Informatics, The University of Edinburgh  $3$ Sony AI. Correspondence to: Lingjuan Lyu <<EMAIL>>.

*Proceedings of the*  $39<sup>th</sup>$  *International Conference on Machine Learning*, Baltimore, Maryland, USA, PMLR 162, 2022. Copyright 2022 by the author(s).

generative models that are trained to generate real-looking samples with high fidelity, these DC methods generate informative training samples for data-efficient learning. In this work, we for the first time investigate the feasibility of protecting data privacy using DC techniques. We find that DC can not only accelerate model training but also offer privacy for free. Figure [1](#page-0-0) illustrates how DC methods can be applied to protect membership privacy and visual privacy. Specifically, we first analyse the relationship between DC-synthesized data and original ones (Proposition [4.3](#page-3-0) and [4.4\)](#page-4-0), and theoretically prove on linear DC extractors that the change caused by removing or adding one element in  $n$  raw samples to the parameter distribution of models trained on  $m(m \ll n)$  DC-synthesized samples (i.e., privacy loss) is bounded by  $O(m/n)$  (Proposition [4.10\)](#page-5-0), which satisfies that one element does not greatly change the model parameter distribution (the concept of DP). The conclusions are further analytically and empirically generalized to non-linear feature extractors. Then, we empirically validate that models trained on DC-synthesized data are robust to both vanilla loss-based MIA and the state-of-the-art likelihood-based MIA [\(Carlini et al.,](#page-9-11) [2022\)](#page-9-11). Finally, we study the visual privacy of DC-synthesized data in case of adversary's direct matching attack. All the results show that DC-synthesized data are not perceptually similar to the original data as our Proposition [4.4](#page-4-0) indicates, and cannot be reversed to the original data through similarity metrics (e.g., LPIPS).

Through empirical evaluations on image datasets, we validate that DC-synthesized data can preserve both *data efficiency* and *membership privacy* when being used for model training. For example, on FashionMNIST, DC-synthesized data enable models to achieve a test accuracy of at least 33.4% higher than that achieved by DP-generators under the same empirical privacy budget. Meanwhile, to achieve a test accuracy of the same level, DC only needs to synthesize at most 50% data of the size required by GAN-based methods, which speeds up the training by at least 2 times.

In summary, our contributions are three-fold:

- To the best of our knowledge, we are the first to introduce the emerging dataset condensation techniques into privacy community and provide systematical audit on state-of-the-art DC methods.
- We build the connection between dataset condensation and differential privacy, and contribute theoretical analysis with both linear and non-linear feature extractors.
- Extensive experiments on image datasets empirically validate that DC methods reduce the adversary advantage of membership privacy to zero, and DCsynthesized data are perceptually irreversible to original data in terms of similarity metrics of  $L_2$  and LPIPS.

## 2. Background and Related Work

In this section, we briefly present dataset condensation and the membership privacy issues in machine learning models.

### 2.1. Dataset Condensation

Orthogonal to model knowledge distillation [\(Hinton et al.,](#page-9-12) [2015\)](#page-9-12), [Wang et al.](#page-10-4) firstly proposed dataset distillation (DD) which aims to distill knowledge from a large training set into a small synthetic set. The synthetic set can be used to efficiently train deep neural networks with a moderate decrease of testing accuracy. Recent works significantly advanced this research area by proposing Dataset Condensation (DC) with gradient matching [\(Zhao et al.,](#page-10-7) [2021;](#page-10-7) [Zhao](#page-10-8) [& Bilen,](#page-10-8) [2021b\)](#page-10-8), Distribution Matching (DM) [\(Zhao &](#page-10-9) [Bilen,](#page-10-9) [2021a\)](#page-10-9) and introducing Kernel Inducing Points (KIP) [\(Nguyen et al.,](#page-10-10) [2021a;](#page-10-10)[b\)](#page-10-11). For example, the synthetic sets (50 images per class) generated by DM can be used to train a 3 layer convolutional neural networks from scratch and obtain over 60% testing accuracies on CIFAR10 [\(Krizhevsky et al.,](#page-9-13) [2009\)](#page-9-13) and over 98% testing accuracies on MNIST [\(LeCun](#page-9-14) [et al.,](#page-9-14) [1998\)](#page-9-14). In this work, we mainly focus on synthetic sets generated by DSA [\(Zhao & Bilen,](#page-10-8) [2021b\)](#page-10-8), DM [\(Zhao](#page-10-9) [& Bilen,](#page-10-9) [2021a\)](#page-10-9) and KIP [\(Nguyen et al.,](#page-10-10) [2021a\)](#page-10-10), because 1) DSA and DM are improved DC and KIP is improved DD, and 2) the performance of DD and DC are significantly lower than DSA, DM and KIP.

We formulate dataset condensation problem using the symbols presented in [\(Zhao & Bilen,](#page-10-9) [2021a\)](#page-10-9). Given a large-scale dataset (target dataset)  $\mathcal{T} = \{(\boldsymbol{x}_i, y_i)\}$  which consists of  $|\mathcal{T}|$ samples from C classes, the objective of dataset condensation (or distillation) is to learn a synthetic set  $\mathcal{S} = \{(\mathbf{s}_i, y_i)\}\$ with  $|S|$  synthetic samples so that the deep neural networks can be trained on  $S$  and achieve comparable testing performance to those trained on  $\mathcal{T}$ :

$$
\mathbb{E}_{\boldsymbol{x}\sim P_{\mathcal{D}}}[\mathcal{L}(\phi_{\boldsymbol{\theta}}\tau(\boldsymbol{x}),y)] \simeq \mathbb{E}_{\boldsymbol{x}\sim P_{\mathcal{D}}}[\mathcal{L}(\phi_{\boldsymbol{\theta}}\tau(\boldsymbol{x}),y)],\quad(1)
$$

where  $P_{\mathcal{D}}$  is the real data distribution,  $\phi_{\theta}(\cdot)$  and  $\phi_{\theta}(\cdot)$ are models trained on  $\mathcal T$  and  $\mathcal S$  respectively.  $\mathcal L(\cdot, \cdot)$  is the loss function, e.g. cross-entropy loss.

To achieve this goal, [Wang et al.](#page-10-4) proposed a meta-learning based method which parameterizes the model updated on synthetic set as  $\theta^S(\mathcal{S})$  and then learns the synthetic data by minimizing the validation loss on original training data  $\mathcal{T}$ :

$$
\underset{\mathcal{S}}{\arg\min} \mathcal{L}^{\mathcal{T}}(\boldsymbol{\theta}^{\mathcal{S}}(\mathcal{S})),\tag{2}
$$

where  $\theta^{\mathcal{S}}(\mathcal{S}) = \arg \min_{\theta} \mathcal{L}^{\mathcal{S}}(\theta)$ . The meta-learning algorithm has to recurrently unroll the computation graph  $\theta^{\mathcal{S}}$  with respect to  $\mathcal{S}$ , which is expensive and unscalable. [\(Nguyen et al.,](#page-10-10) [2021a\)](#page-10-10) proposed Kernel Inducing Points (KIP) which leverages the neural tangent kernel (NTK) [\(Ja](#page-9-15)[cot et al.,](#page-9-15) [2018\)](#page-9-15) to replace the expensive network parameter

updating. With NTK,  $\theta^{\mathcal{S}}$  has a closed-form solution. Thus, KIP learns synthetic data by minimizing the kernel ridge regression loss:

$$
\underset{X_s}{\text{arg min}} \frac{1}{2} \| y_t - K_{X_t X_s} (K_{X_s X_s} + \lambda I)^{-1} y_s \|^2, \quad (3)
$$

where  $X_s$  and  $X_t$  are the synthetic and real images from S and  $\mathcal{T}$ ,  $y_s$  and  $y_t$  are corresponding labels.  $K_{UV}$  represents the NTK matrix  $(K(u, v))_{(u, v) \in U, V}$  for two sets U and V. For a neural network  $\phi_{\theta}$ , the definition of  $K(u, v)$  on elements u and v is  $K(u, v) = \nabla_{\theta} \phi_{\theta}(u) \cdot \nabla_{\theta} \phi_{\theta}(v)$ .

[Zhao et al.](#page-10-7) proposed a novel DC framework to condense the real dataset into a small synthetic set by matching the gradients when inputting real and synthetic batches into the same model, which can be expressed as follows:

<span id="page-2-0"></span>
$$
\argmin_{\mathcal{S}} \text{E}_{\boldsymbol{\theta}_0 \sim P_{\boldsymbol{\theta}_0}} \left[ \sum_{t=0}^{T-1} D(\nabla_{\boldsymbol{\theta}} \mathcal{L}^{\mathcal{S}}(\boldsymbol{\theta}_t), \nabla_{\boldsymbol{\theta}} \mathcal{L}^{\mathcal{T}}(\boldsymbol{\theta}_t)) \right], \quad (4)
$$

where model  $\theta_t$  is updated by minimizing the loss  $\mathcal{L}^{\mathcal{S}}(\theta_t)$ alternatively, D computes distance between gradients. [\(Zhao & Bilen,](#page-10-8) [2021b\)](#page-10-8) enabled the learned synthetic images to be effectively used to train neural networks with data augmentation by introducing the differentiable Siamese augmentation (DSA)  $A_{\omega}(\cdot)$  and improved the matching loss in [\(4\)](#page-2-0) as follows:

$$
D(\nabla_{\boldsymbol{\theta}} \mathcal{L}(\boldsymbol{\theta}_t, \mathcal{A}_{\omega}(\mathcal{S})), \nabla_{\boldsymbol{\theta}} \mathcal{L}(\boldsymbol{\theta}_t, \mathcal{A}_{\omega}(\mathcal{T}))).
$$
 (5)

Although [\(Zhao et al.,](#page-10-7) [2021\)](#page-10-7) successfully avoided unrolling the recurrent computation graph in [\(Wang et al.,](#page-10-4) [2018\)](#page-10-4), it still needs to compute the expensive bi-level optimization and second-order derivative. To further simplify the learning of synthetic data, [\(Zhao & Bilen,](#page-10-9) [2021a\)](#page-10-9) proposed a simple yet effective dataset condensation method with distribution matching (DM). Specifically, the learned synthetic data  $S$ should have data distribution close to that of real data  $\mathcal T$  in randomly sampled embedding spaces:

<span id="page-2-1"></span>
$$
$\min_{\mathcal{S}} \mathbb{E}_{\boldsymbol{\vartheta} \sim P_{\boldsymbol{\vartheta}}, \omega \sim \Omega}$  \\  $\left\|\frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \psi_{\boldsymbol{\vartheta}}(\mathcal{A}(\boldsymbol{x}_i, \omega)) - \frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} \psi_{\boldsymbol{\vartheta}}(\mathcal{A}(s_j, \omega))\right\|^2,$
$$
(6)

where  $\psi_{\theta}$  represents randomly sampled embedding functions (namely feature extractors), e.g. randomly initialized neural networks and  $A(\cdot, \omega)$  is the differentiable Siamese augmentation. Experimental results show that this simple objective can lead to effective synthetic data that are comparable even better than those generated by existing methods.

### 2.2. Membership Privacy

For privacy analysis, we mainly focus on membership privacy as it directly relates to personal privacy. For example, inferring that an individual's facial image was in a shop's training dataset reveals the individual had visited the shop. [Shokri et al.](#page-10-0) have shown that DNNs' output can leak the membership privacy of the input (i.e., whether the input belongs to the training dataset) under *membership inference attack* (MIA). In general, MIA only needs black-box access to model parameters [\(Sablayrolles et al.,](#page-10-13) [2019\)](#page-10-13) and can be successful with logit [\(Yu et al.,](#page-10-14) [2021\)](#page-10-14) or hard label prediction [\(Li & Zhang,](#page-9-16) [2021;](#page-9-16) [Choquette-Choo et al.,](#page-9-17) [2021\)](#page-9-17).

Loss-based MIA. The loss-based MIA infers membership by the predicted loss: if the loss is lower than a threshold  $\tau$ , then the input is a member of the training data. Formally, the membership  $M(x)$  of an input x can be expressed as:

$$
M(\mathbf{x}) = \mathbb{1}(l(\mathbf{x}) \le \tau),\tag{7}
$$

where  $M(\mathbf{x}) = 1$  means x is a training member,  $\mathbb{1}(A) = 1$ if event A is true. The threshold  $\tau$  can be either chosen by locally trained shadow models [\(Shokri et al.,](#page-10-0) [2017\)](#page-10-0) or via optimal bayesian strategy [\(Sablayrolles et al.,](#page-10-13) [2019\)](#page-10-13).

Likelihood-based MIA. Recent works [\(Carlini et al.,](#page-9-11) [2022;](#page-9-11) [Rezaei & Liu,](#page-10-15) [2021\)](#page-10-15) pointed out that the evaluation of MIA should include False Postive Rate (FPR) instead of averaged metrics (e.g., attack accuracy, Area Under Curve (AUC) score of Receiver Operating Characteristic (ROC) curve), because MIA is a real threat only if the FPR is low (i.e., few data are inferred as member). Moreover, [Carlini et al.](#page-9-11) also discovered that loss-based MIAs are hardly effective under constraint of low FPR (e.g., FPR  $< 0.1\%$ ). Hence, they devised a more advanced MIA, i.e. Likelihood Ratio Attack (LiRA) based on the model output difference caused by membership of an input. We consider the *online LiRA attack* because of its high attack performance. Particularly, the adversary first prepares shadow models ahead of the attack by sampling  $N$  sub-datasets and training shadow models on each of the sampled dataset. Hence, for each data sample, there are  $\frac{N}{2}$  shadow models that are trained on it (called IN models) and the rest  $\frac{N}{2}$  that are not trained on it (called OUT models). The adversary then measures the means  $\mu_{in}$ ,  $\mu_{out}$ and the variances  $\sigma_{in}^2$ ,  $\sigma_{out}^2$  of model confidence for IN and OUT models, respectively. Here, the confidence of model  $f$ for  $(\mathbf{x}, y)$  is  $\phi(f(\mathbf{x})_y) = \phi(\exp(-l(f(\mathbf{x}), y)))$ , where l is the cross-entropy loss and  $\phi(p) = \log(\frac{p}{1-p})$ . To attack, the adversary queries the victim model  $f$  with a target example  $(x, y)$  to estimate the likelihood  $\Lambda$  defined as:

$$
\Lambda = \frac{p(\text{conf}_{obs} | \mathcal{N}(\boldsymbol{\mu}_{in}, \boldsymbol{\sigma}_{in}^2))}{p(\text{conf}_{obs} | \mathcal{N}(\boldsymbol{\mu}_{out}, \boldsymbol{\sigma}_{out}^2))},
$$
(8)

where conf<sub>obs</sub> =  $\phi(f(\mathbf{x})_y)$  is the confidence of victim model f on target example  $(x, y)$ . The adversary infers membership by thresholding the likelihood  $\Lambda$  with threshold  $\tau$  determined in advance.

<span id="page-3-5"></span>

# 3. Problem Statement

In practice, companies may utilize personal data for model training in order to provide better services. For example, data holders (e.g., smart retail stores, smart city facilities) may capture clients' data and send to cloud servers for model training. However, models trained on the raw data  $(i.e., T)$  can be attacked by MIA. In addition, transmitting raw data to servers suffers from potential data leakage (e.g., to honest-but-curious operators). Therefore, a better protocol is to first learn knowledge from data by, for instance, generating synthetic dataset S from the raw data (i.e.,  $T$ ), and then send  $S$  to the server for model training for downstream applications. Formally, we define the threat model as follows:

Adversary Goal. The adversary aims to examine the membership information of the target dataset  $T$ . Specifically, for a sample of interest x, the adversary infers whether  $x \in \mathcal{T}$ .

Adversary Knowledge. We assume a strong adversary (e.g., honest-but-curious server), who although has no access to  $T$  but has the *white-box* access to both the synthetic dataset S synthesized from the target dataset  $\mathcal T$  and the model  $f_{\mathcal{S}}$  trained on the synthetic dataset. The adversary also knows the data distribution of  $\mathcal{T}$ .

Adversary Capacity. The adversary has unlimited computational power to generate shadow synthetic datasets on data of same distribution of  $T$  and train shadow models on them.

Note that white-box access to the model parameters does not help MIA [\(Sablayrolles et al.,](#page-10-13) [2019\)](#page-10-13), so we omit other advantages brought by the white-box access to  $f_s$ .

#### 4. Theoretical Analysis

In this section, we theoretically analyse the relationship between the target dataset  $\mathcal T$  and the synthetic dataset  $\mathcal S$  of DM (improved DC) [\(Zhao & Bilen,](#page-10-9) [2021a\)](#page-10-9), and the privacy guarantees of  $\mathcal T$  that are thereby provided. The reason of choosing DM is because of its high condensation efficiency and utility for model training. We also verify the difference between DM and other DC methods (see Appendix [E.3\)](#page-17-0) in terms of the synthetic data distribution, indicating our theoretical results can be generalized to other methods to some extent. Theoretical analysis of other DC methods (e.g., DSA) is left as the future work.

Overview. We briefly present an overview of the analysis that consists of three parts. First, we clarify the assumptions and notations in Section [4.1.](#page-3-1) Then, in Section [4.2,](#page-3-2) we analyse the connection between synthetic and original

datasets for different DC initializations. Finally, in Section [4.3,](#page-4-1) with conclusion of Section [4.2,](#page-3-2) we study the privacy loss of models trained on DC-synthesized data in a DP manner: how does removing one sample in the original dataset impact models trained on synthetic dataset. Because of the randomness in model training, we base on the model parameter distribution assumption from [\(Sablayrolles et al.,](#page-10-13) [2019\)](#page-10-13) and compute the order of magnitude of the impact, which establishes the connection between DC and DP.

#### <span id="page-3-1"></span>4.1. Assumptions & Notations

The DM loss [\(6\)](#page-2-1) can be optimized for each class [\(Zhao](#page-10-9) [& Bilen,](#page-10-9) [2021a\)](#page-10-9). To simplify the notations, we consider only one class and omit the label vectors in the synthetic dataset  $S = \{s_1, \dots, s_{|S|}\}\$ and the target dataset  $\mathcal{T} =$  $\{x_1, \dots, x_{|\mathcal{T}|}\}.$  We consider the following two assumptions of the target dataset and the convergence of DM.

<span id="page-3-3"></span>Assumption 4.1. The linear span of the target dataset span(T) satisfies  $d_{\mathcal{T}} = \dim(\text{span}(\mathcal{T})) < d$ , where d is the data dimension,  $\dim(V)$  represents the dimension of vector space V, span $(\mathcal{T})$  is the vector subspace generated by all linear combinations of  $\mathcal{T}$ :

$$
\text{span}(\mathcal{T}) \coloneqq \{ \sum_{i=1}^{|T|} w_i \mathbf{x}_i | 1 \leq i \leq |\mathcal{T}| \,, w_i \in \mathbb{R}, \mathbf{x}_i \in \mathcal{T} \}. \tag{9}
$$

In practice,  $d_{\mathcal{T}}$  can be computed as the rank of matrix form of  $\mathcal T$ . This assumption generally holds for high dimensional data and can be directly verified for common datasets (e.g., CIFAR-10). Without loss of generality, we consider an orthogonal basis (under inner product of  $\mathbb{R}^d$ )  $\mathcal{E} = \{\mathbf{e}_1, \cdots, \mathbf{e}_d\}$  among which the first  $d_{\mathcal{T}}$  basis vectors  $\mathcal{E}_{\mathcal{T}} = \{e_1, \cdots, e_{d_{\mathcal{T}}}\}\$  form the orthogonal basis of vector subspace span $(\mathcal{T})$ .

<span id="page-3-4"></span>Assumption 4.2. [Convergence of DM]. We assume there exists at least one synthetic dataset  $S^* = \{s_1^*, \dots, s_{|S|}^*\}$ that minimizes [\(6\)](#page-2-1).

#### <span id="page-3-2"></span>4.2. Analysis of Synthetic Data

We first analyse synthetic data by linear extractors and then discuss the generalization to non-linear case (Remark [4.5\)](#page-4-2).

<span id="page-3-0"></span>Proposition 4.3 (Minimizer of DM Loss). *For a linear*  $\epsilon$ *extractor*  $\psi_{\boldsymbol{\theta}}: \mathbb{R}^d \to \mathbb{R}^k$  such that  $k < d$ ,  $\boldsymbol{\theta} \in \mathbb{R}^{k \times d}$ , under *Assumption [4.1](#page-3-3) and [4.2,](#page-3-4) the dataset* S ∗ *synthesized by DM from the target dataset* T *satisfies:*

*1) The barycenters of* S <sup>∗</sup> *and* T *coincide:*

$$
\frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \mathbf{x}_i - \frac{1}{|\mathcal{S}^*|} \sum_{i=1}^{|\mathcal{S}^*|} \mathbf{s}_i^* = \mathbf{0},
$$
 (10)

2) 
$$
\forall s_i^* \in S^*, s_i^* = s_{i,\mathcal{E}_{\mathcal{T}}}^* + s_{i,\mathcal{E}_{\mathcal{T}}}^*
$$
, where  $s_{i,\mathcal{E}_{\mathcal{T}}}^* \in span(\mathcal{T})$ ,

 $\mathbf{s}_{i,\mathcal{E}^\pm_\mathcal{T}}^* \in span(\mathcal{T})^\perp$  *that verifies* 

$$
\sum_{i=1}^{|\mathcal{S}|} \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}^{\pm}}^{*} = \mathbf{0}_{\mathcal{E}_{\mathcal{T}}^{\pm}}.
$$
 (11)

The proof of Proposition [4.3](#page-3-0) can be found in Appendix [A.](#page-11-0) Note that the minimizer is  $s_1^* = \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} x_i$  when  $|\mathcal{S}^*| =$ 1, indicating that the synthetic data falls into vector subspace span( $\mathcal T$ ), confirming the Proposition [4.3.](#page-3-0)

The DC initialization of synthetic dataset can be either real data sampled from  $T$  or random noise. Next, we study the impact of DM initialization and obtain the following results (proof can be found in Appendix [B\)](#page-11-1).

<span id="page-4-0"></span>Proposition 4.4 (Connection between  $S^*$  and  $T$ ). *Based on Proposition* [4.3,](#page-3-0) the minimizer synthetic dataset  $S^* =$  ${s_1^*, \cdots, s_{|\mathcal{S}|}^*}$  has the following properties for different ini*tialization strategies:*

*1) Real data initialization. Assume that* S *is initialized with* first  $|{\cal S}|$  samples of  ${\cal T}$ , i.e.,  ${\bf s}_i = {\bf x}_i$ , then we have

<span id="page-4-6"></span>
$$
\mathbf{s}_{i}^{*} = \mathbf{x}_{i} + \frac{1}{|\mathcal{T}|} \sum_{j=1}^{|\mathcal{T}|} \mathbf{x}_{j} - \frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} \mathbf{s}_{j} \in span(\mathcal{T}). \quad (12)
$$

*2) Random initialization. The synthetic data are initialized with noise of normal distribution, i.e.,*  $\forall s_i \in S, s_i \sim$  $\mathcal{N}(\mathbf{0}, \mathbf{I}_d)$ , and we assume the empirical mean is zeroed, i.e.,  $\frac{1}{|S|} \sum_{i=1}^{|S|} \mathbf{s}_i = \mathbf{0}$ , then we have

<span id="page-4-5"></span>
$$
\mathbf{s}_{i}^{*} = \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}}^{*} + \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}}^{*},\tag{13}
$$

where  $\mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}}^* = \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}} + \frac{1}{|\mathcal{T}|} \sum_{j=1}^{|\mathcal{T}|} \mathbf{x}_j \in span(\mathcal{T})$ , and  $\mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}^{\perp}}^* = \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}^{\perp}} \in (span(\mathcal{T}))^{\perp}.$ 

<span id="page-4-2"></span>*Remark* 4.5 (Non-linear Extractor)*.* Our results can be generalized to the non-linear extractors. [Giryes et al.](#page-9-18) proved that multi-layer random neural networks generate distancepreserving embedding of input data, so [\(6\)](#page-2-1) is minimized if and only if the distance between real and synthetic data is minimized. Take 2-layer random networks as an example [\(Estrach et al.,](#page-9-19) [2014\)](#page-9-19), there exist two constants  $0 < A \leq B$  such that  $\forall (\mathbf{x}, \mathbf{y}) \in (\mathbb{R}^d)^2$ ,  $A \|\mathbf{x} - \mathbf{y}\|_2 \leq$  $\|\rho(\theta \mathbf{x}) - \rho(\theta \mathbf{y})\|_2 \leq B \|\mathbf{x} - \mathbf{y}\|_2$ , where  $\rho$  is ReLU. We also analyse the case of 2-layer extractors (activated by ReLU) and found that the (pseudo)-barycenters of  $S^*$  and  $\mathcal T$  still coincide. Moreover, on convolutional extractors and the 2-layer extractors, we empirically verify Proposition [4.3](#page-3-0) in Appendix [D](#page-14-0) (see Figure [7\)](#page-16-0).

<span id="page-4-4"></span>*Remark* 4.6 (Impact of initialization on privacy)*.* Note that in case of real data initialization, a higher  $|S|$  results in lower distance between barycenters of initialized S and T, thus the changes brought to  $S$  become smaller when  $S$  becomes

larger. This explains the phenomenon that DM-generated images are more visually similar to the real images for higher ipc (images per class) [\(Zhao & Bilen,](#page-10-9) [2021a\)](#page-10-9). However, as we demonstrate in our experiments (Section [5.2\)](#page-6-0), the membership of data used for DC initialization can still be inferred by vanilla loss-based MIA. One countermeasure is to choose hard-to-infer samples [\(Carlini et al.,](#page-9-11) [2022\)](#page-9-11), i.e., samples whose model outputs are not affected by the membership, as initialization data.

On the other hand, data not used for initialization generate little effect (i.e., their weights in synthetic data are  $O(\frac{1}{|\mathcal{T}|})$ ) on synthetic data, just as the case of random initialization, where data in  $T$  also generate little effect on  $S^*$ . We demonstrate that the membership of those data cannot be inferred under both loss-based MIA and the state-of-the-art likelihood MIA (see Section [5.2\)](#page-6-0). Moreover, projection component of  $(\text{span}(\mathcal{T}))^{\perp}$  can further protect the privacy (e.g., visual privacy in Section [5.4\)](#page-7-0).

*Remark* 4.7 (Comparison between DC and GAN)*.* The generator of GAN is trained to minimize the distance between the real and the generated data distributions, which is similar to the objective of DC. However, GAN-generated data share the same constraints (i.e., bounded between 0 and 1) as the real data. DC-generated data do not need to satisfy these constraints. This enables the DC-generated data to contain more features and explains the higher accuracy of models trained on DC-generated data [\(Zhao et al.,](#page-10-7) [2021\)](#page-10-7). We also empirically compare the accuracy of model trained on GAN-generated data and DC-synthesized data (see Section [5.3\)](#page-7-1) , and found that DC-synthesized data outperform GAN-generated data for training better models with smaller amount of training data.

<span id="page-4-1"></span>

### 4.3. Privacy Bound of Models Trained on Synthetic Data

To understand how synthetic dataset protects membership privacy of  $T$  when being used for training model  $f_s$ , we estimate how model parameters change when removing one sample from  $T$  by adopting below assumption. With a little abuse of notation, we denote the minimizer set  $S^*$  by  $S$ when the context is clear.

<span id="page-4-3"></span>Assumption 4.8 (Distribution of model parameter [\(Sablay](#page-10-13)[rolles et al.,](#page-10-13) [2019\)](#page-10-13)). The distribution of model parameter  $\theta$ given training dataset  $\mathcal{T} = {\mathbf{x}_1, \cdots, \mathbf{x}_{|\mathcal{T}|}}$  and loss function *l* is:

$$
\mathbb{P}(\boldsymbol{\theta}|\mathcal{T}) = \frac{1}{K_{\mathcal{T}}} \exp\left(-\sum_{i=1}^{|\mathcal{T}|} l(\boldsymbol{\theta}, \mathbf{x}_i)\right),\tag{14}
$$

where  $K_{\mathcal{T}}$  is the constant normalizing the distribution.

Unlike widely known DP mechanisms (e.g., Gaussian mechanism) that transform the deterministic query function into

a randomized one, randomness brought by optimization algorithm (i.e., SGD) or hardware defaults leads to different parameters each time of training, which justifies Assumption [4.8](#page-4-3) and ensures the "uncertainty" in DP. In addition, we need the following assumption on the datasets  $S, \mathcal{T}$ and the loss function l introduced in the Assumption [4.8.](#page-4-3) The assumption is valid for finite datasets and common loss functions (e.g., cross-entropy) and is used to quantify the data bound and loss variation.

**Assumption 4.9.** We assume the data of  $\mathcal{T}$  and  $\mathcal{S}$  are bounded, i.e.,

$$
\exists B > 0, \forall \mathbf{x} \in \mathcal{T} \cup \mathcal{S}, \|\mathbf{x}\|_2 \le B. \tag{15}
$$

The loss function  $l(\theta, \cdot) : \mathbb{R}^d \to \mathbb{R}^+$  is *L*-Lipschitz according to the  $L_2$  norm, i.e.,

$$
\forall (\mathbf{x}, \mathbf{y}) \in \mathcal{B}(B)^2, \theta, |l(\theta, \mathbf{x}) - l(\theta, \mathbf{y})| \le L \left\| \mathbf{x} - \mathbf{y} \right\|_2,
$$
\n(16)

where  $\mathcal{B}(B) = \{ \mathbf{x} | ||\mathbf{x}|| \leq B \}$  is the close ball of space  $\mathbb{R}^d$ .

<span id="page-5-0"></span>With all previous assumptions, we have the following result. **Proposition 4.10.** *Suppose a target dataset*  $\mathcal{T}$  =  $\{x_1, \cdots, x_{|\mathcal{T}|}\}\$ and the leave-one-out dataset  $\mathcal{T}' = \mathcal{T} \backslash \{x\}$ *such that* x *is not used for initialization. The synthetic datasets are* S *and* S' *and*  $|S| = |S'| \ll |T|$ *.* De*note the model parameter distributions of* S *and* S <sup>0</sup> *by*  $p(\theta) = \mathbb{P}(\theta|\mathcal{S})$  and  $q(\theta) = \mathbb{P}(\theta|\mathcal{S}')$  respectively. Then, *the membership privacy leakage caused by removing* x *is*

$$
D_{KL}(p||q) = O(\frac{|\mathcal{S}|}{|\mathcal{T}|}).
$$
\n(17)

Proposition [4.10](#page-5-0) indicates that the adversary can only obtain limited information (i.e.,  $O(\frac{|\mathcal{S}|}{|\mathcal{T}|})$ ) by MIA when the synthetic data is much fewer than the original data ( $|S| \ll |\mathcal{T}|$ ), which explains why synthetic data  $S$  protects membership privacy of model  $f_s$ . The proof is in Appendix [C.](#page-13-0)

Connection to DP. Our privacy analysis is based on the impact of model parameter distribution by removing one element from the origin training dataset, which is similar to the definition of DP [\(Dwork et al.,](#page-9-7) [2006\)](#page-9-7). Formally, a  $\epsilon$ -differential privacy mechanism  $\mathcal M$  satisfies:

$$
\ln \frac{\mathbb{P}(\mathcal{M}(D) \in \mathcal{S}_{\mathcal{M}})}{\mathbb{P}(\mathcal{M}(D') \in \mathcal{S}_{\mathcal{M}})} \le \epsilon
$$
\n(18)

for all neighbor dataset pair  $(D, D')$  and all subset  $\mathcal{S}_{\mathcal{M}}$  of the range of  $M$ . Without knowledge of explicit form of model parameter distribution, we can only claim that the privacy budget  $\epsilon$  varies at the order of  $O(\frac{|S|}{|\mathcal{T}|}).$ 

In practice, we use an empirical budget  $\hat{\epsilon}$  through MIA [\(Kairouz et al.,](#page-9-20) [2015\)](#page-9-20) to measure the privacy guarantee against MIA. Typically, for a MIA that achieves FPR and TPR (True Positive Rate) against a model, the empirical budget is  $\hat{\epsilon} \geq \ln(TPR/FPR)$ . In other words, the model behaves  $\hat{\epsilon}$ -differentially private to an adversary that applies MIA (i.e., threat model in Section [3\)](#page-3-5).

Note that the empirical budget  $\hat{\epsilon}$  is *not* equivalent to the real budget  $\epsilon$  because of different threat models [\(Nasr et al.,](#page-9-21) [2021\)](#page-9-21). Nonetheless, we consider black-box MIA as the only privacy threat to the model, thus we can regard the DP budget  $\epsilon$  as a model privacy metric against MIA. In this way, we can compare  $\hat{\epsilon}$  and  $\epsilon$  by the definition of  $\hat{\epsilon}$ . In Section [5.3,](#page-7-1) we show that models trained on data synthesized by DC achieve  $\hat{\epsilon} \approx 2$  against threat from the state-of-the-art MIA (LiRA), and obtain accuracy much higher than differentially private generators [\(Chen et al.,](#page-9-22) [2020a;](#page-9-22) [Harder et al.,](#page-9-6) [2021\)](#page-9-6), indicating DC is a better option for efficient and privacypreserving model training.

# 5. Evaluation

In this section, we evaluate the membership privacy of  $f_{\mathcal{S}}$ for real data and random initialization. Then, we compare DC with previous DP-generators and GAN to demonstrate DC's better trade-off between privacy and utility. Finally, we investigate the visual privacy of DC-synthesized data.

### 5.1. Experimental Setup

Datasets & Architectures. We use three datasets: Fashion-MNIST [\(Xiao et al.,](#page-10-16) [2017\)](#page-10-16), CIFAR-10 [\(Krizhevsky et al.,](#page-9-13) [2009\)](#page-9-13) and CelebA [\(Liu et al.,](#page-9-23) [2015\)](#page-9-23) for gender classification. The CelebA images are center cropped to dimension  $64 \times 64$ , and we randomly sample 5,000 images for each class, which is same as CIFAR-10, while FashionMNIST contains 6, 000 images for each class. We adopt the same 3 layer convolutional neural networks used in [\(Zhao & Bilen,](#page-10-9) [2021a\)](#page-10-9) and [\(Nguyen et al.,](#page-10-11) [2021b\)](#page-10-11) as the feature extractor.

DC Settings. One important hyperparameter of DSA, DM and KIP is the ratio of image per class  $r_{inc} = \frac{|\mathcal{S}|}{|\mathcal{T}|}$ . We evaluate  $r_{inc} = 0.002, 0.01$  for all methods, and for DM we add an extra evaluation  $r_{inc} = 0.02$  due to its high efficiency on producing large synthetic set. Note that  $r_{ipo}$  influences the model training efficiency: the lower  $r_{inc}$ , the faster model training. We also consider ZCA preprocessing for KIP as it is reported to be effective for KIP performance improvement. Appendix [E.1](#page-16-1) contains more DC implementation details.

Baselines. As for non-private baseline, we adopt subset sampled from  $T$  (this baseline is termed real data) and data generated by conditional GAN (cGAN or GAN for short) [\(Mirza & Osindero,](#page-9-24) [2014\)](#page-9-24) which is trained on  $\mathcal T$ . For private baseline, we choose DP-generators including GS-WGAN [\(Chen et al.,](#page-9-22) [2020a\)](#page-9-22), DP-MERF [\(Harder et al.,](#page-9-6) [2021\)](#page-9-6) and DP-Sinkhorn [\(Cao et al.,](#page-9-5) [2021\)](#page-9-5). We compare the DC methods

with baselines in terms of privacy and efficiency.

MIA Settings & Attack Metrics. For each dataset, we randomly split it into two subsets of equal amount of samples and choose one subset as  $T$  (member data). We then synthesize dataset S on T, and train a model  $f_S$  (victim model) on S. The other subset becomes the non-member data used for testing the MIA performance. The above process is called *preparation of synthetic dataset*.

For loss-based MIA, we repeat the preparation of synthetic dataset 10 times with different random seeds. This gives us 10 groups of  $\mathcal{T}$ ,  $\mathcal{S}$  and  $f_{\mathcal{S}}$ . For each  $f_{\mathcal{S}}$ , we first select N member samples from  $T$  and  $N$  non-member samples, and choose an optimal threshold that maximizes the advantage score on the previously chosen 2N samples [\(Sablayrolles](#page-10-13) [et al.,](#page-10-13) [2019\)](#page-10-13). The threshold is then tested on another disjoint  $2N$  samples composed by N member samples and N non-member samples to compute the advantage score of loss-based MIA. We report the advantage (in percentage) defined as  $2 \times (acc - 50\%)$  where acc is the test accuracy of membership in percentage.

For LiRA [\(Carlini et al.,](#page-9-11) [2022\)](#page-9-11), we repeat the preparation of synthetic dataset  $N_m$  times with different random seeds, and obtain  $N_m$  shadow  $\mathcal{T}$ ,  $\mathcal{S}$  and  $f_{\mathcal{S}}$ . We set  $N_m = 256$ for DM and  $N_m = 64$  for KIP because of its lower training efficiency. We omit DSA for LiRA due to longer training time. To attack a victim model, we compute the likelihoods of each sample with  $N_m$  shadow  $f_{\mathcal{S}}$  and determine the threshold of likelihood according to the requirements of false positive. We use the Receiver Operating Characteristic (ROC) curve and Area Under Curve (AUC) score to evaluate the attack performance. Remark that we adopt the strongest (and unrealistic) attack assumption (i.e., the attacker knows the membership), so that we investigate the privacy of DCsynthesized data under the *worst* case.

<span id="page-6-0"></span>

#### 5.2. Membership Privacy of $f_{\mathcal{S}}$

<span id="page-6-1"></span>Table 1. Advantage (%) of loss-based MIA against models trained on real data (baseline) and data synthesized by DSA, DM and KIP with *real data* initialization.

| Method       | $r_{ipc}$ | FashionMNST       | <b>CIFAR-10</b>   | <b>CelebA</b>     |
|--------------|-----------|-------------------|-------------------|-------------------|
| Real         | 0.002     | $46.67 \pm 16.33$ | $72.00 \pm 24.00$ | $100.00 \pm 0.00$ |
| (baseline.   | 0.01      | $21.00 \pm 3.67$  | $92.80 \pm 5.31$  | $84.00 \pm 5.06$  |
| non-private) | 0.02      | $17.33 + 2.91$    | $82.60 \pm 5.59$  | $77.00 \pm 6.71$  |
|              | 0.002     | $78.17 \pm 3.20$  | $49.80 \pm 5.83$  | $37.00 \pm 12.69$ |
| DM           | 0.01      | $83.67 + 2.77$    | $64.20 + 4.77$    | $47.00 \pm 19.52$ |
|              | 0.02      | $83.00 \pm 2.56$  | $68.20 \pm 7.35$  | $53.00 \pm 14.18$ |
| <b>DSA</b>   | 0.002     | $74.40 \pm 2.65$  | $55.40 \pm 8.20$  | $30.50 \pm 8.16$  |
|              | 0.01      | $81.60 + 2.27$    | $56.60 + 2.95$    | $28.00 \pm 3.74$  |
| <b>KIP</b>   | 0.002     | $67.83 + 4.54$    | $42.40 \pm 4.80$  | $23.00 \pm 11.87$ |
| (w/o ZCA)    | 0.01      | $70.00 \pm 2.47$  | $51.40 \pm 5.73$  | $25.00 \pm 15.65$ |
| <b>KIP</b>   | 0.002     | $67.67 \pm 4.42$  | $50.40 \pm 5.35$  | $23.00 \pm 15.52$ |
| (w/ZCA)      | 0.01      | $64.00 \pm 4.23$  | $48.40 \pm 6.62$  | $17.00 \pm 18.47$ |

Real Data Initialization Leaks Membership Privacy. We begin with membership privacy leaked by  $f_{\mathcal{S}}$  as mentioned in Remark [4.6](#page-4-4) of Proposition [4.4.](#page-4-0) We aim to verify that DC with real data initialization still leaks membership privacy of the data used for initialization. Here, the data used for initialization are sampled from  $T$  during each time of preparation of synthetic dataset. We launch loss-based MIA against  $f<sub>S</sub>$  and adopt the real data baseline.

Table [1](#page-6-1) shows the advantage score of loss-based MIA. Here, we vary a little bit the attack setting: the advantage scores are computed with the data used for real initialization and the same amount of member data not used for initialization but involved in DC. We observe that, on CIFAR-10 and CelebA, the synthetic dataset with real data initialization achieves lower advantage scores comparing to directly using real data for training (baseline). This can be explained by Proposition [4.4,](#page-4-0) which tells us that the synthesized data deviates slightly from the real data used for initialization. However, on FashionMNIST, the baseline has lower advantage scores. We suspect this is because FashionMNIST images are greyscale and synthetic data contain more features that prone to be memorized by networks. Loss distribution in Figure [8](#page-17-1) in Appendix [E.2](#page-16-2) also demonstrates that *synthetic data with real data initialization can still leak membership privacy*.

Next, we show that models trained on data synthesized by DC with random initialization are robust against both lossbased MIA and LiRA (the state-of-the-art MIA).

MIA Robustness of Random Initialization. Because of random initialization, each member sample contributes equally to the synthetic dataset. Thus, in this case, we follows the loss-base attack setting and set  $N = 1000$ . Table [2](#page-7-2) provides the average and the standard variance of advantages for models trained on synthetic datasets by cGAN, DSA, DM and KIP. The advantages are around 0 for all  $r_{inc}$ , signifying that the adversary cannot infer membership of  $T$ . Nevertheless, as long as the adversary has access to the generated images (which is included in our threat model), the membership of the GAN generator's training data (i.e.,  $T$ ) can still be leaked (see Appendix [E.4\)](#page-17-2). Meanwhile, as we show later in Figure [3,](#page-8-0) models trained on DC-synthesized data achieve higher accuracy scores than baseline (i.e., cGAN-generated data), demonstrating the higher utility of DC-synthesized data.

LiRA is a more powerful MIA because it can achieve higher TPR at low FPR [\(Carlini et al.,](#page-9-11) [2022\)](#page-9-11), while the adversary's computational cost is higher. Figure [2](#page-8-1) provides the ROC curves of LiRA against  $f_{\mathcal{S}}$ . We can observe that the ROC curves are close to the diagonal (red line) for all datasets and  $r_{inc}$ . The AUC scores of ROC curves are around 0.5, indicating *there is negligible attack benefit (low TPR) for the attacker compared with random guess*. Recall that LiRA is evaluated on the whole dataset (half as member and other <span id="page-7-2"></span>Table 2. Advantage (%) of loss-based MIA against models trained on data synthesized by cGAN (baseline), DSA, DM and KIP with *random* initialization.

| <b>Methods</b> | $r_{inc}$ | <b>FashionMNST</b> | <b>CIFAR-10</b>  | CelebA           |
|----------------|-----------|--------------------|------------------|------------------|
| cGAN           | 0.002     | $0.29 \pm 0.89$    | $-0.44 + 1.88$   | $-0.57 \pm 0.97$ |
| (baseline,     | 0.01      | $0.18 \pm 1.21$    | $-0.58 \pm 2.09$ | $-0.81 \pm 0.95$ |
| non-private)   | 0.02      | $0.04 + 0.70$      | $-0.77 \pm 1.59$ | $-0.47 + 1.22$   |
|                | 0.002     | $-0.34 \pm 0.42$   | $0.31 \pm 1.93$  | $-0.66 \pm 1.44$ |
| DМ             | 0.01      | $-0.29 + 0.48$     | $1.06 \pm 1.20$  | $-0.56 \pm 1.52$ |
|                | 0.02      | $0.18 + 0.53$      | $0.72 + 0.70$    | $-0.67 + 1.18$   |
| <b>DSA</b>     | 0.002     | $0.09 + 0.51$      | $0.39 \pm 1.04$  | $-0.39 \pm 1.90$ |
|                | 0.01      | $0.52 + 0.55$      | $1.27 + 1.71$    | $-1.16 + 0.90$   |
| <b>KIP</b>     | 0.002     | $-1.13 + 1.84$     | $0.25 + 1.20$    | $-0.56 + 1.07$   |
| (w/o ZCA)      | 0.01      | $-0.95 + 0.96$     | $0.25 \pm 1.80$  | $-1.51 \pm 0.69$ |
| <b>KIP</b>     | 0.002     | $-0.56 \pm 2.02$   | $-0.64 \pm 1.86$ | $-1.06 \pm 1.10$ |
| (w/ZCA)        | 0.01      | $-1.69 \pm 1.96$   | $-0.22 \pm 1.27$ | $-1.80 \pm 1.91$ |

half as non-member), the minimum FPR value is  $\frac{1}{5000} = 2 \times$  $10^{-4}$  for CelebA,  $4 \times 10^{-5}$  for CIFAR-10 and  $3.33 \times 10^{-5}$ for FashionMNIST. Therefore, when FPR is close to 0, the ROC curves have different shapes for different datasets. However, we also notice that the TPR is close to FPR when FPR is around the minimum (e.g., FPR $\sim 10^{-4}$  for CelebA), demonstrating that *models trained on data synthesized by DC with random initialization is robust to LiRA at low FPR*.

<span id="page-7-1"></span>

#### 5.3. Comparison with Different Generators

<span id="page-7-3"></span>Table 3. Utility comparison of dataset synthesized by DPgenerators, DM and KIP. The utility is measured by the accuracy (%) of models trained on the synthetic dataset. The results are estimated on FashionMNIST.

| Method             | <b>DP</b> Budget        | $r_{inc}$        |                  |                  |
|--------------------|-------------------------|------------------|------------------|------------------|
|                    |                         | 0.002            | 0.01             | 0.02             |
| <b>GS-WGAN</b>     | $\epsilon = 10$         | $53.53 \pm 0.42$ | $51.85 \pm 0.54$ | $50.10 \pm 0.32$ |
| DP-MERF            | $\epsilon = 10$         | $52.18 + 0.37$   | $52.88 \pm 0.75$ | $50.73 \pm 0.66$ |
|                    | $\epsilon = 2$          | $60.41 \pm 0.78$ | $55.14 \pm 0.61$ | $56.39 \pm 0.45$ |
| DP-Sinkhorn        | $\epsilon = 10$         |                  |                  | $70.9*$          |
| $KIP$ (w/o $ZCA$ ) | $\hat{\epsilon} = 1.25$ | $73.70 \pm 1.13$ | $68.11 \pm 1.33$ |                  |
| $KIP$ (w/ $ZCA$ )  | $\hat{\epsilon} = 2.07$ | $74.37 \pm 0.96$ | $70.03 \pm 0.84$ |                  |
| DМ                 | $\hat{\epsilon} = 2.30$ | $80.59 \pm 0.62$ | $85.10 \pm 0.51$ | $86.13 \pm 0.34$ |

\* Results reported in the paper [\(Cao et al.,](#page-9-5) [2021\)](#page-9-5) ( $r_{inc} = 1$ ).

GAN Generator. Figure [3](#page-8-0) compares the accuracy scores of models trained on synthetic datasets (DC-synthesized with random initialization under  $r_{inc} = 0.01$ ). We can find that under the same constraint of training efficiency (i.e.,  $r_{inc} = 0.01$ ), the DM and DSA outperform the other methods. Note that models trained on KIP-synthesized data achieve lower accuracy than baseline because the loss is hard to converge for large  $r_{inc}$ . Nevertheless, for small  $r_{inc}$ , the KIP significantly outperforms baselines on CIFAR-10 and CelebA (see Figure [10](#page-18-0) in Appendix [E\)](#page-16-3).

Then, we aim to know how DC improves model training efficiency compared to cGAN. In other words, to achieve the same accuracy of  $f_{\mathcal{S}}$ , the difference between the  $r_{ipc}$ that DC requires and the  $r_{inc}$  that cGAN requires can be seen as the model training efficiency that DC improves. For different  $r_{inc}$  (the x-axis), Figure [4](#page-8-2) shows the accuracy of models trained on cGAN-generated dataset whose ratio is  $r_{inc}$  (the blue solid curve). The red and green horizontal lines represent the accuracy of  $f_{\mathcal{S}}$  trained on dataset synthesized by DSA and DM for  $r_{inc} = 0.01$ , respectively. We omit KIP here because of its lower utility than baselines. Therefore, the  $r_{inc}$  of the intersection point of the red (resp. green) line and the blue curve is the  $r_{inc}$  of cGANsgenerated dataset on which the models can be trained to achieve the same accuracy as DSA (resp. DM). We can see that, cGAN needs to generate more data to train a model that achieves the same accuracy as models trained on data synthesized by DM and DSA, because the  $r_{inc}$  values indicated in the x-axis are all higher than 0.01. It is worth noting that DC improves the training efficiency (measured by  $r_{inc}$ ) by at least 2 times than cGAN for  $r_{inc} = 0.01$ , because on FashionMNIST (the leftmost sub-figure in Figure [4\)](#page-8-2)), cGAN requires to generate synthetic dataset of  $r_{inc} = 0.02$ to achieve the same accuracy (0.85) as the DM-synthesized dataset ( $r_{inc} = 0.01$ ).

**DP-generators.** We estimate an empirical  $\hat{\epsilon}$  based on the ratio of TPR and FPR computed by LiRA. In Table [3,](#page-7-3) we compare the accuracy of models trained on DC-synthesized data and on data generated by recent DP-generators. We reproduce DP-MERF and GS-WGAN according to the official implementation and adopt the reported results of DP-Sinkhorn. We observe that the accuracy of models trained on data generated by the state-of-the-art DP-generator (DP-Sinkhorn) is still lower than DM-synthesized images, even the ratio for DP-Sinkhorn is  $r_{inc} = 1$ . The reason is that DP is designed to defend against the strongest adversary who has access to the training process of generator. Hence, data generated by DP-generators are of lower utility for model training because of the too strong defense requirement.

<span id="page-7-0"></span>

#### 5.4. Visual Privacy

The adversary can directly visualize synthetic data and compare with the target sample to infer the membership. We visualize the synthetic images and use  $L_2$  distance as well as the perceptual metric LPIPS [\(Zhang et al.,](#page-10-17) [2018\)](#page-10-17) with VGG backbone to measure the similarity between synthetic and real images. Figure [5](#page-8-3) shows examples of DM-generated images and the their (top 3) most similar real images, i.e., images of lowest  $L_2$  and LPIPS distance with the synthetic image on the top of the column. We observe that the real data share similar facial contour patterns with the synthetic images, but more fine-grained features, e.g., eye shape, are different, which explains why models trained on synthetic dataset protect the membership privacy of original data. This can also explain why current MIAs fail on models trained

Privacy for Free: How does Dataset Condensation Help Privacy?

Image /page/8/Figure/1 description: The image displays ROC curves for two methods, DM and KIP, across three datasets: FashionMNIST, CIFAR-10, and CelebA. The x-axis represents the False Positive Rate (FPR) on a logarithmic scale, ranging from 10^-5 to 10^-1. The y-axis represents the True Positive Rate (TPR) on a logarithmic scale, ranging from 10^-5 to 10^0. For the DM method, the plots show the effect of different `ripc` values (0.002, 0.01, 0.02) on the ROC curves for each dataset. For the KIP method, the plots compare the performance with and without ZCA whitening ('w/o zca' and 'w/ zca'), and also show the effect of different `ripc` values (0.002, 0.01). All plots show a general trend of increasing TPR with increasing FPR, with some curves exhibiting a plateau at low FPR values.

Figure 2. ROC curves of LiRA against models trained on data synthesized by DM (left three figures) and KIP (right three figures). The solid, dashed and dotted lines stand for results of  $r_{ipo} = 0.002, 0.01$  and 0.02, respectively. In KIP figures, the orange and blue lines represent the results of KIP with and without ZCA preprocessing, respectively. The red diagonal represents random guess and the AUC scores of ROC curves are all under 0.51.

Image /page/8/Figure/3 description: This is a bar chart that compares the accuracy of different methods on three datasets: FashionMNIST, CIFAR-10, and CelebA. For each dataset, there are two groups of bars: 'Real' and 'Random'. Within each group, there are five bars representing 'Baseline', 'DSA', 'DM', 'KIP (w/o ZCA)', and 'KIP (w/ ZCA)'. The y-axis represents accuracy, ranging from 0.60 to 0.90 for FashionMNIST and CelebA, and 0.25 to 0.60 for CIFAR-10. For FashionMNIST, under 'Real', Baseline accuracy is around 0.84, DSA is around 0.88, DM is around 0.86, KIP (w/o ZCA) is around 0.73, and KIP (w/ ZCA) is around 0.72. Under 'Random', Baseline is around 0.83, DSA is around 0.88, DM is around 0.85, KIP (w/o ZCA) is around 0.73, and KIP (w/ ZCA) is around 0.72. For CIFAR-10, under 'Real', Baseline is around 0.46, DSA is around 0.56, DM is around 0.56, KIP (w/o ZCA) is around 0.40, and KIP (w/ ZCA) is around 0.41. Under 'Random', Baseline is around 0.75, DSA is around 0.57, DM is around 0.57, KIP (w/o ZCA) is around 0.38, and KIP (w/ ZCA) is around 0.40. For CelebA, under 'Real', Baseline is around 0.79, DSA is around 0.81, DM is around 0.85, KIP (w/o ZCA) is around 0.72, and KIP (w/ ZCA) is around 0.72. Under 'Random', Baseline is around 0.79, DSA is around 0.85, DM is around 0.86, KIP (w/o ZCA) is around 0.72, and KIP (w/ ZCA) is around 0.72.

<span id="page-8-0"></span>Figure 3. Accuracy of models trained on data synthesized by DSA, DM, KIP and on data generated by baselines for  $r_{inc} = 0.01$ . The x-axis represents initialization strategy. For real data and random initialization, the baselines are real data and cGAN-generated data, respectively.

Image /page/8/Figure/5 description: This image contains three line graphs, each representing accuracy on the y-axis against 'ripc' on the x-axis. The graphs are titled "FashionMNIST", "CIFAR-10", and "CelebA". Each graph shows a blue line with a shaded area representing a range, and two horizontal dashed lines labeled "DSA" (red) and "DM" (green). In the "FashionMNIST" graph, the blue line starts at an accuracy of approximately 0.852 at r\_ipc=0.02 and increases to about 0.882 at r\_ipc=0.08. The "DSA" line is at 0.875 and the "DM" line is at 0.852. In the "CIFAR-10" graph, the blue line starts at an accuracy of approximately 0.555 at r\_ipc=0.09 and increases to about 0.578 at r\_ipc=0.15. The "DSA" line is at 0.875 and the "DM" line is at 0.578. In the "CelebA" graph, the blue line starts at an accuracy of approximately 0.822 at r\_ipc=0.02 and increases to about 0.875 at r\_ipc=0.04. The "DSA" line is at 0.84 and the "DM" line is at 0.86.

<span id="page-8-2"></span>Figure 4. Accuracy of models trained on cGAN-generated data for different  $r_{ipc}$ . The horizontal lines are accuracy of models trained on data synthesized by DM (green, dashed) and DSA (red, dash-dotted) for  $r_{inc} = 0.01$ .

on synthetic datasets: the generated synthetic training data have lost the private properties of real data and thus the adversary are not able to infer the privacy from models trained on such synthetic data.

## 6. Discussion and Conclusion

In this work, we make the first effort to introduce the emerging dataset condensation techniques into the privacy community and provide systematical audit including theoretical analysis of the privacy property and empirical evaluation, i.e. visual privacy examination and robustness against lossbased MIA and LiRA on FashionMNIST, CIFAR-10 and CelebA datasets.

Our future work will attempt to generalize the theoretical findings to other DC methods. This can be studied from the perspective of information loss (e.g. data compression ratio). Moreover, DC methods that satisfy formal DP formulation, e.g.,  $(\alpha, \epsilon)$ -Rényi DP ([Mironov,](#page-9-25) [2017\)](#page-9-25), are worth exploring.

<span id="page-8-1"></span>Image /page/8/Figure/11 description: This figure displays a grid of images categorized by synthetic data and top 3 similar data (L2 and LPIPS), with different values of r\_ipc (0.002, 0.01, and 0.02). The synthetic data section shows two images for each r\_ipc value. The top 3 similar data (L2) section contains three rows and two columns of images for each r\_ipc value, with associated numerical values: 0.1319, 0.1442, 0.1349, 0.0649, 0.1301, 0.0953 in the first row; 0.1323, 0.1486, 0.1375, 0.0660, 0.1451, 0.0984 in the second row; and 0.1329, 0.1496, 0.1423, 0.0676, 0.1512, 0.1035 in the third row. The top 3 similar data (LPIPS) section also has three rows and two columns of images for each r\_ipc value, with associated numerical values: 0.6128, 0.6190, 0.5515, 0.4813, 0.5828, 0.4054 in the first row; 0.6188, 0.6216, 0.5516, 0.4823, 0.5909, 0.4221 in the second row; and 0.6189, 0.6282, 0.5516, 0.4880, 0.5916, 0.4352 in the third row. The image is labeled with 'Synthetic' on the left vertical axis and 'Top 3 Similar Data (L2)' and 'Top 3 Similar Data (LPIPS)' on the left vertical axis.

<span id="page-8-3"></span>Figure 5. Examples of facial images that are most similar to synthetic data generated by DM with *random* initialization. The value above each image is the distance  $(L_2 \text{ and LPIPS})$  between the image and the synthetic data (first row). Lower distance indicates higher similarity. Even though these real images have similar face contour, blurred facial details (e.g., eyes, nose) make it difficult for the adversary to infer the membership.

The current efforts of DC mainly focus on image classification, thus another interesting direction is the extension of the privacy benefit brought by DC to more complicated vision tasks (e.g., object detection) and non-vision tasks (e.g. text and graph related applications). In essence, DC methods can generalize to other machine learning tasks, as they learn the synthetic data by summarizing the distribution or discriminative information of real training data. Hence, their privacy advantage should also generalize to other tasks.

## Acknowledgements

We would like to thank He Tong for the help in analyzing non-linear models and the anonymous reviewers for constructive feedback.

# References

- <span id="page-9-8"></span>Bohdal, O., Yang, Y., and Hospedales, T. Flexible dataset distillation: Learn labels instead of images. *NeurIPS Workshop*, 2020.
- <span id="page-9-5"></span>Cao, T., Bie, A., Vahdat, A., Fidler, S., and Kreis, K. Don't generate me: Training differentially private generative models with sinkhorn divergence. *Advances in Neural Information Processing Systems*, 34, 2021.
- <span id="page-9-11"></span>Carlini, N., Chien, S., Nasr, M., Song, S., Terzis, A., and Tramer, F. Membership inference attacks from first principles. In *43rd IEEE Symposium on Security and Privacy, SP 2022*. IEEE, 2022.
- <span id="page-9-10"></span>Cazenavette, G., Wang, T., Torralba, A., Efros, A. A., and Zhu, J.-Y. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022.
- <span id="page-9-22"></span>Chen, D., Orekondy, T., and Fritz, M. Gs-wgan: A gradient-sanitized approach for learning differentially private generators. In *Neural Information Processing Systems (NeurIPS)*, 2020a.
- <span id="page-9-4"></span>Chen, D., Yu, N., Zhang, Y., and Fritz, M. Gan-leaks: A taxonomy of membership inference attacks against generative models. In *CCS '20: 2020 ACM SIGSAC Conference on Computer and Communications Security*, pp. 343–362, 2020b.
- <span id="page-9-17"></span>Choquette-Choo, C. A., Tramer, F., Carlini, N., and Papernot, N. Label-only membership inference attacks. In *Proceedings of the 38th International Conference on Machine Learning*, volume 139 of *Proceedings of Machine Learning Research*, pp. 1964–1974. PMLR, 2021.
- <span id="page-9-7"></span>Dwork, C., McSherry, F., Nissim, K., and Smith, A. Calibrating noise to sensitivity in private data analysis. In *Theory of cryptography conference*, pp. 265–284, 2006.
- <span id="page-9-19"></span>Estrach, J. B., Szlam, A., and LeCun, Y. Signal recovery from pooling representations. In *International conference on machine learning*, pp. 307–315. PMLR, 2014.
- <span id="page-9-1"></span>Fredrikson, M., Jha, S., and Ristenpart, T. Model inversion attacks that exploit confidence information and basic countermeasures. In *CCS*, pp. 1322–1333, 2015.
- <span id="page-9-18"></span>Giryes, R., Sapiro, G., and Bronstein, A. M. Deep neural networks with random gaussian weights: A universal classification strategy? *IEEE Transactions on Signal Processing*, 64(13):3444–3457, 2016.
- <span id="page-9-3"></span>Goodfellow, I., Pouget-Abadie, J., Mirza, M., Xu, B., Warde-Farley, D., Ozair, S., Courville, A., and Bengio, Y. Generative adversarial nets. *Advances in neural information processing systems*, 27, 2014.

- <span id="page-9-6"></span>Harder, F., Adamczewski, K., and Park, M. Dp-merf: Differentially private mean embeddings with randomfeatures for practical privacy-preserving data generation. In *International Conference on Artificial Intelligence and Statistics*, pp. 1819–1827, 2021.
- <span id="page-9-12"></span>Hinton, G., Vinyals, O., and Dean, J. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2015.
- <span id="page-9-15"></span>Jacot, A., Hongler, C., and Gabriel, F. Neural tangent kernel: Convergence and generalization in neural networks. In *Advances in Neural Information Processing Systems 31: Annual Conference on Neural Information Processing Systems 2018, NeurIPS 2018*, pp. 8580–8589, 2018.
- <span id="page-9-9"></span>Jin, W., Zhao, L., Zhang, S., Liu, Y., Tang, J., and Shah, N. Graph condensation for graph neural networks. *ICLR*, 2022.
- <span id="page-9-20"></span>Kairouz, P., Oh, S., and Viswanath, P. The composition theorem for differential privacy. In *International conference on machine learning*, pp. 1376–1385. PMLR, 2015.
- <span id="page-9-13"></span>Krizhevsky, A., Hinton, G., et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-9-14"></span>LeCun, Y., Bottou, L., Bengio, Y., Haffner, P., et al. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998.
- <span id="page-9-16"></span>Li, Z. and Zhang, Y. Membership leakage in label-only exposures. In *CCS '21: 2021 ACM SIGSAC Conference on Computer and Communications Security*, pp. 880–895, 2021.
- <span id="page-9-23"></span>Liu, Z., Luo, P., Wang, X., and Tang, X. Deep learning face attributes in the wild. In *Proceedings of International Conference on Computer Vision (ICCV)*, December 2015.
- <span id="page-9-0"></span>Lyu, L., Yu, H., Zhao, J., and Yang, Q. Threats to federated learning. In *Federated Learning*, pp. 3–16. Springer, 2020.
- <span id="page-9-2"></span>Melis, L., Song, C., De Cristofaro, E., and Shmatikov, V. Exploiting unintended feature leakage in collaborative learning. In *SP*, pp. 691–706, 2019.
- <span id="page-9-25"></span>Mironov, I. Rényi differential privacy. In 30th IEEE Com*puter Security Foundations Symposium, CSF 2017, Santa Barbara, CA, USA, August 21-25, 2017*, pp. 263–275. IEEE Computer Society, 2017.
- <span id="page-9-24"></span>Mirza, M. and Osindero, S. Conditional generative adversarial nets. *arXiv preprint arXiv:1411.1784*, 2014.
- <span id="page-9-21"></span>Nasr, M., Songi, S., Thakurta, A., Papemoti, N., and Carlin, N. Adversary instantiation: Lower bounds for differentially private machine learning. In *2021 IEEE Symposium on Security and Privacy (SP)*, pp. 866–882. IEEE, 2021.

- <span id="page-10-10"></span>Nguyen, T., Chen, Z., and Lee, J. Dataset meta-learning from kernel ridge-regression. In *International Conference on Learning Representations*, 2021a.
- <span id="page-10-11"></span>Nguyen, T., Novak, R., Xiao, L., and Lee, J. Dataset distillation with infinitely wide convolutional networks. In *Thirty-Fifth Conference on Neural Information Processing Systems*, 2021b.
- <span id="page-10-18"></span>Powell, M. J. D. An efficient method for finding the minimum of a function of several variables without calculating derivatives. *Comput. J.*, 7(2):155–162, 1964.
- <span id="page-10-15"></span>Rezaei, S. and Liu, X. On the difficulty of membership inference attacks. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 7892–7900, 2021.
- <span id="page-10-13"></span>Sablayrolles, A., Douze, M., Schmid, C., Ollivier, Y., and Jégou, H. White-box vs black-box: Bayes optimal strategies for membership inference. In *International Conference on Machine Learning*, pp. 5558–5567, 2019.
- <span id="page-10-0"></span>Shokri, R., Stronati, M., Song, C., and Shmatikov, V. Membership inference attacks against machine learning models. In *2017 IEEE Symposium on Security and Privacy (SP)*, pp. 3–18, 2017.
- <span id="page-10-6"></span>Such, F. P., Rawal, A., Lehman, J., Stanley, K. O., and Clune, J. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. *ICML*, 2020.
- <span id="page-10-5"></span>Sucholutsky, I. and Schonlau, M. Soft-label dataset distillation and text dataset distillation. *arXiv preprint arXiv:1910.02551*, 2019.
- <span id="page-10-12"></span>Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X., and You, Y. Cafe: Learning to condense dataset by aligning features. *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022.
- <span id="page-10-4"></span>Wang, T., Zhu, J., Torralba, A., and Efros, A. A. Dataset distillation. *CoRR*, abs/1811.10959, 2018. URL [http:](http://arxiv.org/abs/1811.10959) [//arxiv.org/abs/1811.10959](http://arxiv.org/abs/1811.10959).
- <span id="page-10-3"></span>Wang, Y., Ding, Z., Xiao, Y., Kifer, D., and Zhang, D. Dpgen: Automated program synthesis for differential privacy. In *Proceedings of the 2021 ACM SIGSAC Conference on Computer and Communications Security*, pp. 393–411, 2021.
- <span id="page-10-1"></span>Webster, R., Rabin, J., Simon, L., and Jurie, F. This person (probably) exists. identity membership attacks against gan generated faces. *arXiv preprint arXiv:2107.06018*, 2021.

- <span id="page-10-16"></span>Xiao, H., Rasul, K., and Vollgraf, R. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-10-2"></span>Xie, L., Lin, K., Wang, S., Wang, F., and Zhou, J. Differentially private generative adversarial network. *arXiv preprint arXiv:1802.06739*, 2018.
- <span id="page-10-14"></span>Yu, D., Zhang, H., Chen, W., Yin, J., and Liu, T.-Y. How does data augmentation affect privacy in machine learning? In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 35, pp. 10746–10753, 2021.
- <span id="page-10-17"></span>Zhang, R., Isola, P., Efros, A. A., Shechtman, E., and Wang, O. The unreasonable effectiveness of deep features as a perceptual metric. In *CVPR*, 2018.
- <span id="page-10-9"></span>Zhao, B. and Bilen, H. Dataset condensation with distribution matching. *CoRR*, abs/2110.04181, 2021a.
- <span id="page-10-8"></span>Zhao, B. and Bilen, H. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, 2021b.
- <span id="page-10-7"></span>Zhao, B., Mopuri, K. R., and Bilen, H. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021.

<span id="page-11-0"></span>

# A. Proof of Proposition [4.3](#page-3-0)

We begin our analysis of DM with the linear extractor  $\psi_{\theta} : \mathbb{R}^d \to \mathbb{R}^k$  such that  $k < d$ ,  $\theta = [\theta_{i,j}] \in \mathbb{R}^{k \times d}$  and for an input x,  $\psi_{\theta}(\mathbf{x}) = \theta \mathbf{x}$ . We also omit the differentiable Siamese augmentation to simplify the analysis. As the representation extractors  $\psi_{\theta}$  in DM are randomly initialized, we assume that the extractor parameters follow the standard normal distribution and are identically and independently distributed (i.i.d), i.e.,  $\theta_{i,j} \stackrel{iid}{\sim} \mathcal{N}(0,1)$ . Thus, Equation [\(6\)](#page-2-1) becomes the expectation over  $\|\mathbf{d}_{DM}\|^2$  where  $\mathbf{d}_{DM}$  is defined as:

<span id="page-11-3"></span>
$$
\mathbf{d}_{DM} \coloneqq \theta \left( \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \mathbf{x}_i - \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \mathbf{s}_i \right).
$$
 (19)

Hence,  $L_{DM} = \mathbb{E}_{\theta \sim \mathcal{N}(0,1)} ||\mathbf{d}_{DM}||^2$ . The optimization of S with SGD relies on the gradient of [\(6\)](#page-2-1). Given a sampled model parameter  $\theta$ , for some synthetic sample  $s_i$ , we have:

$$
\frac{\partial \left\| \mathbf{d}_{DM} \right\|^2}{\partial \mathbf{s}_i} = -\frac{2}{|\mathcal{S}|} (\mathbf{d}_{DM})^\top \boldsymbol{\theta} = -\frac{2}{|\mathcal{S}|} \left( \frac{1}{|\mathcal{T}|} \sum_{j=1}^{|\mathcal{T}|} \mathbf{x}_j - \frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} \mathbf{s}_j \right)^\top \cdot \boldsymbol{\theta}^\top \boldsymbol{\theta}.
$$
 (20)

Hence, we obtain:

<span id="page-11-2"></span>
$$
\frac{\partial L_{DM}}{\partial \mathbf{s}_i} = \frac{\partial \mathbb{E}_{\boldsymbol{\theta}} \left\| \mathbf{d}_{DM} \right\|^2}{\partial \mathbf{s}_i} = \mathbb{E}_{\boldsymbol{\theta}} \frac{\partial \left\| \mathbf{d}_{DM} \right\|^2}{\partial \mathbf{s}_i} = -\frac{2}{|\mathcal{S}|} \left( \frac{1}{|\mathcal{T}|} \sum_{j=1}^{|\mathcal{T}|} \mathbf{x}_j - \frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} \mathbf{s}_j \right)^{\top} \cdot \mathbb{E}[\boldsymbol{\theta}^\top \boldsymbol{\theta}], \tag{21}
$$

where  $\mathbb{E}[\theta^\top \theta] = k \mathbf{I}_d$  by definition of  $\theta$ , and  $\mathbf{I}_d$  is the identity matrix of  $\mathbb{R}^d$ . Equation [\(21\)](#page-11-2) indicates that the optimization direction of synthetic sample  $s_i$  is the direction of moving barycenter of S to the barycenter of T. To conceptually interpret, the optimization of [\(6\)](#page-2-1) will move the initialized S until the barycenter coincides with that of  $\mathcal T$  because of the existence of minimizer (Assumption [4.2\)](#page-3-4) where the left hand-side of  $(21)$  should be 0.

<span id="page-11-1"></span>

#### B. Proof of Proposition [4.4](#page-4-0)

**Case of real data initialization.** Suppose that each  $s_i \in S$  is sampled from T and we can consider  $s_i = x_i$  as initialization for simplicity. According to [\(21\)](#page-11-2), all  $s_i$  are optimized until the barycenters of S and T coincide. Observe that each  $s_j^* \in \text{span}(\mathcal{T})$ , because the projection components of span $(\mathcal{T})^{\perp}$  remain zero throughout the optimization process of DM. Thus, one solution of minimizer elements  $s_i^* \in S^*$  with the real data initialization can be:

$$
\mathbf{s}_{i}^{*} = \mathbf{x}_{i} + \frac{1}{|\mathcal{T}|} \sum_{j=1}^{|\mathcal{T}|} \mathbf{x}_{j} - \frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} \mathbf{s}_{j}.
$$
 (22)

When  $|S|$  and  $|T|$  are large (e.g.,  $> 50$ ), we can consider  $s_i^* \approx x_i$ , thus *initialization with real data in DM still risks of membership privacy leakage*.

Case of random initialization. The synthetic data are initialized as vectors of multivariate normal distribution, i.e.,

$$
\forall \mathbf{s}_i \in \mathcal{S}, \mathbf{s}_i \sim \mathcal{N}(\mathbf{0}, \mathbf{I}_d). \tag{23}
$$

Each synthetic sample  $s_i$  can be written as a vector  $[s_{i,1}, \cdots, s_{i,d}]$  under the basis  $\mathcal E$  where  $\forall j$ ,  $s_{i,j} \stackrel{i.i.d.}{\sim} \mathcal N(0,1)$ , because  $s_i$ 's covariance matrix remains identity matrix under any orthogonal transformation (i.e., orthogonal basis). Thus, we can decompose  $d_{DM}$  to the projections on subspace span(T) and its orthogonal complement. Formally, we have

$$
\|\mathbf{d}_{DM}\|^2 = \|\boldsymbol{\theta}_{1:d_{\mathcal{T}}} \text{Proj}_{\mathcal{E}_{\mathcal{T}}}(\Delta_{\mathcal{S},\mathcal{T}})\|^2 + \left\|\boldsymbol{\theta}_{d_{\mathcal{T}}:d} \text{Proj}_{\mathcal{E}_{\mathcal{T}}^{\perp}}(\Delta_{\mathcal{S},\mathcal{T}})\right\|^2 + 2 < \boldsymbol{\theta}_{1:d_{\mathcal{T}}} \text{Proj}_{\mathcal{E}_{\mathcal{T}}}(\Delta_{\mathcal{S},\mathcal{T}}), \boldsymbol{\theta}_{d_{\mathcal{T}}:d} \text{Proj}_{\mathcal{E}_{\mathcal{T}}^{\perp}}(\Delta_{\mathcal{S},\mathcal{T}}) >
$$

$$
\|\mathbf{d}_{DM}\|_{\mathcal{E}_{\mathcal{T}}^{\perp}}^2 \quad (24)
$$

where  $\theta_{a:b}$  represents the submatrix composed by a-th column to the b-th column, Proj<sub>V</sub> is the projection operator onto subspace  $V$  and

$$
\Delta_{\mathcal{S},\mathcal{T}} \coloneqq \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \mathbf{x}_i - \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \mathbf{s}_i. \tag{25}
$$

Note that

$$
\text{Proj}_{\mathcal{E}_{\mathcal{T}}}(\Delta_{\mathcal{S},\mathcal{T}}) = \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \text{Proj}_{\mathcal{E}_{\mathcal{T}}^{\perp}}(\mathbf{s}_i),\tag{26}
$$

because  $x_i \in span(\mathcal{T}) = span(\mathcal{E}_{\mathcal{T}})$  for each  $x_i$ . Let  $s_{i,\mathcal{E}_{\mathcal{T}}^{\perp}} = Proj_{\mathcal{E}_{\mathcal{T}}^{\perp}}(s_i)$ , then we have

$$
\mathbb{E}_{\theta} \frac{\partial \|\mathbf{d}_{DM}\|_{\mathcal{E}_{\mathcal{T}}^{\perp}}^2}{\partial \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}^{\perp}}} = \frac{2}{|\mathcal{S}|^2} (\sum_{j=1}^{|\mathcal{S}|} \mathbf{s}_{j,\mathcal{E}_{\mathcal{T}}^{\perp}})^{\top} \mathbb{E}_{\theta} [(\theta_{d_{\mathcal{T}}:d})^{\top} \theta_{d_{\mathcal{T}}:d}],
$$
\n(27)

because  $\mathbb{E}_{\theta}[\theta_{1:d_{\mathcal{T}}}^{\top}\theta_{d_{\mathcal{T}}:d}] = 0$ . Therefore, the expectation of the above equation is the optimization direction of the projection of  $s_j$  on the subspace  $(span(\mathcal{T}))^{\perp}$ :

$$
\frac{\partial L_{DM}}{\partial \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}^{\perp}}} = \mathbb{E}_{\theta} \frac{\partial ||\mathbf{d}_{DM}||_{\mathcal{E}_{\mathcal{T}}^{\perp}}^2}{\partial \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}^{\perp}}} = \frac{2 \mathbb{E}[(\theta_{d_{\mathcal{T}}:d})^{\top} \theta_{d_{\mathcal{T}}:d}]}{|\mathcal{S}|^2} (\sum_{j=1}^{|\mathcal{S}|} \mathbf{s}_{j,\mathcal{E}_{\mathcal{T}}^{\perp}})^{\top}.
$$
\n(28)

Note that  $\mathbb{E}[(\theta_{d_T:d})^\top \theta_{d_T:d}] = k \mathbf{I}_{d-d_T}$ , thus the optimization direction is aligned with the barycenter of all  $\mathbf{s}_{i,\mathcal{E}_T^{\perp}}$  and will converge to 0 when

$$
\frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}^{\perp}} = \mathbf{0}_{\mathcal{E}_{\mathcal{T}}^{\perp}}.
$$
\n(29)

Since the initialization of S is essentially noise of standard normal distribution, the empirical average of  $s_{i,\mathcal{E}^{\perp}_{\tau}}$  is close to 0 (by law of large numbers), thus we can consider that the projection component of minimizer  $s_{i,\mathcal{E}_{\mathcal{T}}}^*$  is close to the initialized value, i.e.,

$$
\forall \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}}^* \in \mathcal{S}^*, \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}}^* \approx \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}}.\tag{30}
$$

Similar as the case of real data initialization, the projection components on span(T) of  $s_i$  are optimized to verify the first property of Proposition [4.3,](#page-3-0) i.e., the projection component of *i*-th minimizer  $s_{i,\mathcal{E}_{\mathcal{T}}}^*$  becomes

$$
\mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}}^{*} = \mathbf{s}_{i,\mathcal{E}_{\mathcal{T}}} + \frac{1}{|\mathcal{T}|} \sum_{j=1}^{|\mathcal{T}|} \mathbf{x}_{j} - \frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} \mathbf{s}_{j,\mathcal{E}_{\mathcal{T}}}.
$$
(31)

#### B.1. Empirical verification

We empirically verify our conclusions for random and real data initializations in Figure [6.](#page-13-1) The images are synthesized from CIFAR-10 by DM using linear extractor of embedding dimension 2048, and each line contains images from the same class. On the right side, we plot the images synthesized with random initialization and real data initialization. We can observe that images synthesized with random initialization resemble combination of noise and class-dependent background, which verifies our conclusion of random initialization: synthetic data with random initialization are composed of barycenter of original data in space span and initialized noise in space span $(\mathcal{T})^{\perp}$  (see [\(13\)](#page-4-5)). Note that even in this case, models trained on synthetic data can still achieve validation accuracy around 27%.

On the other hand, real data initialization generates little changes on the images used for initialization, which verifies the conclusion of real data initialization: synthetic data with real data initialization are composed of images used for initialization and the barycenter distance vector (see [\(12\)](#page-4-6)).

Besides linear extractor, we also investigate the impact of activation function. On the left of Figure [6,](#page-13-1) we show images synthesized by DM using ReLU-activated extractor (ReLU on top of linear extractor). We can see that the existence of

<span id="page-13-1"></span>

# Privacy for Free: How does Dataset Condensation Help Privacy?

Image /page/13/Figure/1 description: The image displays a comparison of two types of neural network extractors: a ReLU-activated Extractor and a Linear Extractor. Each extractor is shown with two different initialization methods: Random initialization and Real data initialization. Both extractors are presented as grids of small images, likely representing features or outputs learned by the network. The ReLU-activated Extractor, under both random and real data initialization, shows a grid of 8x10 images. The images under real data initialization appear more coherent and recognizable, showing various objects like animals, vehicles, and landscapes. In contrast, the images under random initialization for the ReLU-activated Extractor are more abstract and noisy. The Linear Extractor is also presented with two grids of 8x10 images. The images under real data initialization for the Linear Extractor are somewhat recognizable but less distinct than those from the ReLU-activated Extractor. The images under random initialization for the Linear Extractor are very abstract and noisy, similar to the random initialization of the ReLU-activated Extractor, but perhaps even less structured.

Figure 6. Synthetic images with random noise/real data initialization and with linear/ReLU-activated extractor of CIFAR-10.

ReLU results in better convergence of DC and thus better image quality for both random and real data initialization. A potential reason is that ReLU changes the DC optimization and can lead to different local minima other than that found by using linear extractor. That is, data synthesized in this case are possibly composed by barycenter of a certain group of similar images (e.g., images of brown horse heading towards right with grass background) within the same class and a orthogonal noise vector. For example, CIFAR-10 synthetic images of class "horse" (third last line of leftmost figure in Figure [6\)](#page-13-1) are noisy but contain different backgrounds which should be the barycenter of different image group of class "horse": there are numerous horse images in CIFAR-10 where the horse head towards the right or left. This observation also confirms that ReLU improves generalization of neural networks. Appendix [D](#page-14-0) encompasses more detailed analysis for non-linear extractor.

<span id="page-13-0"></span>

### C. Proof of Proposition [4.10](#page-5-0)

We aim to quantify the membership privacy leakage of a member x with the Kullback-Leibler (KL) divergence of model parameter distributions. Without loss of generality, we study how the last element  $x_{|T|}$  influences the model parameter distribution. Let T' denote  $\mathcal{T} \setminus \{ \mathbf{x}_{|\mathcal{T}|} \}$ , where  $\mathcal{T} = \{ \mathbf{x}_1, \cdots, \mathbf{x}_{|\mathcal{T}|} \}$ . The synthetic datasets by DM based on T and T' are noted as S and S', respectively, and  $|S| = |S'|$ . In addition, we denote  $p(\theta) = \mathbb{P}(\theta|S)$  and  $q(\theta) = \mathbb{P}(\theta|S')$ . The KL divergence between  $p$  and  $q$  is

<span id="page-13-3"></span>
$$
D_{KL}(p||q) = \int_{\theta} p(\theta) \ln \frac{p(\theta)}{q(\theta)} d\theta = \int_{\theta} \frac{1}{K_{\mathcal{S}}} \exp\left(-\sum_{i=1}^{|\mathcal{S}|} l(\theta, \mathbf{s}_1)\right) \ln \frac{p(\theta)}{q(\theta)} d\theta, \tag{32}
$$

where

<span id="page-13-2"></span>
$$
\ln \frac{p(\theta)}{q(\theta)} = \sum_{i=1}^{|\mathcal{S}|} l(\theta, \mathbf{s}'_i) - \sum_{i=1}^{|\mathcal{S}|} l(\theta, \mathbf{s}_i) + K_{\mathcal{S}'} - K_{\mathcal{S}}
$$
  
= 
$$
\sum_{i=1}^{|\mathcal{S}|} (l(\theta, \mathbf{s}'_i) - l(\theta, \mathbf{s}_i)) + K_{\mathcal{S}'} - K_{\mathcal{S}}
$$
  
$$
\leq L \sum_{i=1}^{|\mathcal{S}|} ||\mathbf{s}'_i - \mathbf{s}_i||_2 + |K_{\mathcal{S}'} - K_{\mathcal{S}}|.
$$
 (33)

According to the assumption [4.8,](#page-4-3)  $K_S$  (similar for  $K_{S}$ ) is:

$$
K_{\mathcal{S}} := \int_{\theta} \exp\left(-\sum_{i=1}^{|\mathcal{S}|} l(\theta, \mathbf{s}_i)\right) d\theta. \tag{34}
$$

Since  $x_{|\mathcal{T}|}$  is not used for real data initialization, according to the Proposition [4.4,](#page-4-0) if S and S' share the same initialization

type and initialized values, we have for each  $i$ 

$$
\|\mathbf{s}'_i - \mathbf{s}_i\|_2 = \left\| \frac{1}{|\mathcal{T}| - 1} \sum_{j=1}^{|\mathcal{T}| - 1} \mathbf{x}_j - \frac{1}{|\mathcal{T}|} \sum_{j=1}^{|\mathcal{T}|} \mathbf{x}_j \right\|_2
$$
  
$$
= \frac{1}{|\mathcal{T}|} \left\| \frac{1}{|\mathcal{T}| - 1} \sum_{j=1}^{|\mathcal{T}| - 1} \mathbf{x}_j - \mathbf{x}_{|\mathcal{T}|} \right\|_2
$$
  
$$
\leq \frac{2B}{|\mathcal{T}|}. \quad (35)
$$

Thus, we have

$$
L\sum_{i=1}^{|\mathcal{S}|} \|\mathbf{s}'_i - \mathbf{s}_i\|_2 \le \frac{2LB|\mathcal{S}|}{|\mathcal{T}|}.
$$
 (36)

The second term on the right side of [\(33\)](#page-13-2) can be processed similarly:

$$
|K_{\mathcal{S}'} - K_{\mathcal{S}}| = \left| \int_{\theta} \exp\left(-\sum_{i=1}^{|\mathcal{S}'|} l(\theta, \mathbf{s}'_i)\right) - \exp\left(-\sum_{i=1}^{|\mathcal{S}|} l(\theta, \mathbf{s}_i)\right) d\theta \right|
$$
  
= 
$$
\left| \int_{\theta} \left[ \exp\left(\sum_{i=1}^{|\mathcal{S}|} (l(\theta, \mathbf{s}_i) - l(\theta, \mathbf{s}'_i))\right) - 1 \right] \exp\left(-\sum_{i=1}^{|\mathcal{S}|} l(\theta, \mathbf{s}_i)\right) d\theta \right|.
$$
 (37)

From previous analysis, we know that

$$
\sum_{i=1}^{|\mathcal{S}|} (l(\boldsymbol{\theta}, \mathbf{s}_i) - l(\boldsymbol{\theta}, \mathbf{s}'_i)) \le \frac{2LB|\mathcal{S}|}{|\mathcal{T}|}.
$$
\n(38)

Since  $exp(x) - 1 = O(x)$  in the neighborhood of 0, we have

$$
|K_{\mathcal{S}'} - K_{\mathcal{S}}| = O(\frac{2LB|\mathcal{S}|K_{\mathcal{S}}}{|\mathcal{T}|}) = O(\frac{|\mathcal{S}|}{|\mathcal{T}|}).
$$
\n(39)

Note that  $K_S$  should decrease as |S| increases because an additional synthetic sample s introduces a factor  $\exp(-l(\theta, s)) \leq 1$ in the integral. We omit it here and assume  $K_S$  varies little when  $|S|$  changes. Together with [\(32\)](#page-13-3) and [\(33\)](#page-13-2), we obtain the privacy bound by KL divergence:

$$
D_{KL}(p||q) = O(\frac{|\mathcal{S}|}{|\mathcal{T}|}).
$$
\n(40)

<span id="page-14-0"></span>

# D. Generalization to Non-Linear Extractor

We consider 2-layer network as the extractor, i.e., linear extractor with ReLU activation, and show that the (pseudo) barycenters of S and  $\mathcal T$  also coincide as claimed by Proposition [4.3.](#page-3-0) We then empirically validate the conclusion by plotting the  $L_2$  distance between S and T during the condensation by DM for different  $r_{ipo}$  on CIFAR-10 (see Figure [7\)](#page-16-0).

#### D.1. Analysis for 2-layer Network as Extractor

With activation function ReLU (noted as  $\rho$ ), Equation [\(19\)](#page-11-3) becomes:

<span id="page-14-1"></span>
$$
\mathbf{d}_{DM}^{ReLU} \coloneqq \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \rho(\boldsymbol{\theta} \cdot \mathbf{x}_i) - \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \rho(\boldsymbol{\theta} \cdot \mathbf{s}_i). \tag{41}
$$

Since  $\theta_{i,j} \stackrel{iid}{\sim} \mathcal{N}(0,1)$  for each element  $\theta_{i,j}$  of  $\theta$ , for an input  $\mathbf{x} = [x_j]_{1 \leq j \leq d} \in \mathbb{R}^d$ , we have

$$
\mathbf{y} = \boldsymbol{\theta} \cdot \mathbf{x} = \left[\sum_{j=1}^{d} \theta_{i,j} x_j\right]_{1 \le i \le k} = [y_i]_{1 \le i \le k} \in \mathbb{R}^k,
$$
\n(42)

where  $y_i \stackrel{iid}{\sim} \mathcal{N}(0, \sum_{j=1}^d x_j^2)$ . Since  $\rho(x) := \max(0, x)$ , we have  $\rho(\mathbf{y}) = [\max(0, y_i)]_{1 \le i \le k}$ . Define  $Y = \max(0, X)$ where the random variable  $X \sim \mathcal{N}(0, \sigma^2)$ . Then, Y follows the same distribution of  $B|X|$ , where  $B \sim Bernoulli(\frac{1}{2})$ independent of X and  $\mathbb{E}_X[Y] = \mathbb{E}_B[B]\mathbb{E}_X[[X]].$  Therefore, for each  $i$ ,  $\max(0, y_i) = B_i|y_i| = B_i$  $\begin{array}{c} \hline \end{array}$  $\sum_{i=1}^{d}$  $\sum_{j=1} \theta_{i,j} x_j$  $\begin{array}{c} \hline \end{array}$ , and we can obtain

$$
\rho(\mathbf{y}) = \rho(\boldsymbol{\theta}\mathbf{x}) = \mathbf{B} \odot |\boldsymbol{\theta}\mathbf{x}| \tag{43}
$$

where ⊙ is element-wise multiplication,  $\mathbf{B} = [B_i]_{1 \le i \le k}$  and  $B_i \stackrel{iid}{\sim} Bernoulli(\frac{1}{2})$ . With this in mind, Equation [\(41\)](#page-14-1) becomes:

$$
\mathbf{d}_{DM}^{ReLU} = \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \rho(\boldsymbol{\theta} \cdot \mathbf{x}_i) - \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \rho(\boldsymbol{\theta} \cdot \mathbf{s}_i) = \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \mathbf{B}_i^x \odot |\boldsymbol{\theta} \mathbf{x}_i| - \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \mathbf{B}_i^s \odot |\boldsymbol{\theta} \mathbf{s}_i|,
$$
(44)

where vectors of Bernoulli random variable for each data samples  $B_i$  are independent. To simplify notation, we consider  $k = 1$ . The vector of Bernoulli random variable reduces to single random variable  $B_i$ , and the bold symbol d becomes d. Moreover, let sgn(x) denote the sign of x, and we can see that  $|x| = \text{sgn}(x)x$  for a real number x. Thus, with  $k = 1$ , we can reduce  $d_{DM}^{ReLU}$  to the similar form of [\(19\)](#page-11-3):

$$
d_{DM}^{ReLU} = \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} B_i^x \text{sgn}(\boldsymbol{\theta} \mathbf{x}_i) \boldsymbol{\theta} \mathbf{x}_i - \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} B_i^s \text{sgn}(\boldsymbol{\theta} \mathbf{s}_i) \boldsymbol{\theta} \mathbf{s}_i
$$

$$
= \theta \left( \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} B_i^x \text{sgn}(\boldsymbol{\theta} \mathbf{x}_i) \mathbf{x}_i - \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} B_i^s \text{sgn}(\boldsymbol{\theta} \mathbf{s}_i) \mathbf{s}_i \right).
$$
 (45)

Recall that for each  $j$ ,  $\partial L_{DM}/\partial s_j = \mathbb{E}_{\theta}[(d_{DM}^{ReLU})^2/\partial s_j] = \mathbb{E}_{\theta}[2(\partial d_{DM}^{ReLU}/\partial s_j)d_{DM}^{ReLU}]$ , and we have

$$
\frac{\partial d_{DM}^{ReLU}}{\partial \mathbf{s}_j} = -\frac{1}{|\mathcal{S}|} B_j^s \text{sgn}(\boldsymbol{\theta} \mathbf{s}_j) \boldsymbol{\theta}^\top. \tag{46}
$$

Thus, the gradient of  $L_{DM}$  on  $s_j$  becomes:

$$
\frac{L_{DM}}{\mathbf{s}_{j}} = \mathbb{E}_{B,\theta}[-\frac{2}{|\mathcal{S}|}\boldsymbol{\theta}^{\top}\boldsymbol{\theta}(\frac{1}{|\mathcal{T}|}\sum_{i=1}^{|\mathcal{T}|}B_{j}^{s}\text{sgn}(\boldsymbol{\theta}\mathbf{s}_{j})B_{i}^{x}\text{sgn}(\boldsymbol{\theta}\mathbf{x}_{i})\mathbf{x}_{i} - \frac{1}{|\mathcal{S}|}\sum_{i=1}^{|\mathcal{S}|}B_{j}^{s}\text{sgn}(\boldsymbol{\theta}\mathbf{s}_{j})B_{i}^{s}\text{sgn}(\boldsymbol{\theta}\mathbf{s}_{i})\mathbf{s}_{i})]
$$

$$
= -\frac{2}{|\mathcal{S}|}(\frac{1}{|\mathcal{T}|}\sum_{i=1}^{|\mathcal{T}|}\mathbb{E}_{B}[B_{j}^{s}B_{i}^{x}]\mathbb{E}_{\boldsymbol{\theta}}[\text{sgn}(\boldsymbol{\theta}\mathbf{s}_{j})\text{sgn}(\boldsymbol{\theta}\mathbf{x}_{i})\boldsymbol{\theta}^{\top}\boldsymbol{\theta}]\mathbf{x}_{i} - \frac{1}{|\mathcal{S}|}\sum_{i=1}^{|\mathcal{S}|}\mathbb{E}_{B}[B_{j}^{s}B_{i}^{s}]\mathbb{E}_{\boldsymbol{\theta}}[\text{sgn}(\boldsymbol{\theta}\mathbf{s}_{j})\text{sgn}(\boldsymbol{\theta}\mathbf{s}_{i})\boldsymbol{\theta}^{\top}\boldsymbol{\theta}]\mathbf{s}_{i}).
$$
(47)

Let  $M(\mathbf{x}, \mathbf{y})$  denote  $\mathbb{E}_{\theta}[\text{sgn}(\theta \mathbf{x}) \text{sgn}(\theta \mathbf{y}) \theta^{\top} \theta] \in \mathbb{R}^{d \times d}$ , then the above equation becomes:

$$
\frac{L_{DM}}{\mathbf{s}_{j}} = -\frac{2}{|\mathcal{S}|} \left( \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \frac{1}{4} M(\mathbf{s}_{j}, \mathbf{x}_{i}) \mathbf{x}_{i} - \frac{1}{|\mathcal{S}|} \sum_{i=1, i \neq j}^{|\mathcal{S}|} \frac{1}{4} M(\mathbf{s}_{j}, \mathbf{s}_{i}) \mathbf{s}_{i} - \frac{1}{2|\mathcal{S}|} \mathbf{s}_{j} \right), \tag{48}
$$

because  $M(x, y) = I_d$  if  $x = y$ . Note that if  $x = -y$ , then  $M(x, y) = -I_d$ . In fact, we can prove that

$$
\mathbf{x}^{\top} M(\mathbf{x}, \mathbf{y}) \mathbf{y} = \mathbb{E}_{\theta} [(\text{sgn}(\theta \mathbf{x}) \theta \mathbf{x})^{\top} (\text{sgn}(\theta \mathbf{y}) \theta \mathbf{y})] = \frac{\|\mathbf{x}\|_2 \|\mathbf{y}\|_2}{\pi} [(\pi - 2\phi) \cos(\phi) + 2 \sin(\phi)],\tag{49}
$$

<span id="page-16-0"></span>Privacy for Free: How does Dataset Condensation Help Privacy?

Image /page/16/Figure/1 description: This image contains two plots side-by-side, both titled with the type of network used: "Convolutional Network" on the left and "2-layer Network" on the right. The y-axis for both plots is labeled "Distance" and ranges from 0 to 7. The x-axis for both plots is labeled "Iteration" and ranges from 0 to 5000. A legend on the right indicates that different colored lines represent different values of "r\_ipc": light blue for 0.01 and orange for 0.02. The legend also distinguishes between "Initialization" types: solid lines represent "real data" and dashed lines represent "random" initialization. Both plots show a decrease in distance as the iteration count increases, with the distance generally stabilizing after around 1000-2000 iterations. The "Convolutional Network" plot shows a larger initial distance and a slower convergence compared to the "2-layer Network" plot, especially for the "random" initialization.

Figure 7. Distance ( $\|\cdot\|_2$ ) between barycenters of S and T deceases with the iteration round, which verifies first property of Proposition [4.3.](#page-3-0) The solid and dashed lines represent real data and random initialization, respectively. The blue and orange lines represent the cases where  $r_{inc}$  equals to 0.01 and 0.02, respectively.

which can be seen as a matrix depending on the angle  $\phi$  between x and y. Even though each original data  $x_i$  is varied by  $M(s_i, x_i)$ , their average can still be seen as a pseudo-barycenter, and the above equation signifies that each  $s_i$  is updated towards minimizing the distance between the pseudo-barycenters of  $T$  and  $S$ , which verifies the first property of Proposition [4.3](#page-3-0) on non-linear extractor. This further validates the privacy property of DM which is based on the connection between  $S$ and  $\mathcal T$ .

Next, we empirically verify the Proposition [4.3](#page-3-0) with tests on CIFAR-10.

# D.2. Empirical verification of Proposition [4.3](#page-3-0) for non-linear extractor

Figure [7](#page-16-0) shows the distance  $\parallel$  $\frac{1}{|S|} \sum_{i=1}^{|S|} \mathbf{s}_i - \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \mathbf{x}_i \bigg\|_2$  for each DM iteration on CIFAR-10. We can observe that the barycenter distance decreases with the iteration round, and achieves to the minimum. Note that the right subfigure of Figure [7](#page-16-0) shows that the barycenters of  $\mathcal T$  and  $\mathcal S$  synthesized on 2-layer network (i.e., linear model activated by ReLU) have distance around 0, validating the theoretical analysis above. As for convolutional network (ConvNet), the distance decreases slower than 2-layer network. We suspect that the convolutional layers will lead the optimization to a local minimum. Figure [7](#page-16-0) also validates the impact of  $r_{inc}$  and initialization to the distance of barycenters of S and T: 1) when the iteration round is around 0, the distance of 100 image per class is smaller than that of 50 image per class, 2) the real initialization has much lower distance than of random initialization at the beginning of DM optimization.

<span id="page-16-3"></span>

# E. Additional Experimental Details and Results

All experiments are conducted with Pytorch 1.10 on a Ubuntu 20.04 server.

<span id="page-16-1"></span>

### E.1. Details of Hyperparameters and Settings.

DC Settings. We reproduced DM [\(Zhao & Bilen,](#page-10-9) [2021a\)](#page-10-9) and adopt large learning rates to accelerate the condensation (i.e., 10, 50, 100 as learning rate for  $r_{inc} = 0.002, 0.01, 0.02$ , respectively). For DSA [\(Zhao & Bilen,](#page-10-8) [2021b\)](#page-10-8), we adopt the default setting <sup>[1](#page-16-4)</sup> for all datasets. For KIP, we reproduced in Pytorch according to the official code of [\(Nguyen et al.,](#page-10-10) [2021a\)](#page-10-10), and set learning rate 0.04 and 0.1 for  $r_{inc} = 0.002$  and 0.01, respectively. Note that we omit  $r_{inc} = 0.02$  for KIP and DSA due to the low efficiency. We also apply differentiable siamese augmentations [\(Zhao & Bilen,](#page-10-8) [2021b\)](#page-10-8) for both DM and KIP.

<span id="page-16-2"></span>

## E.2. Loss distribution of data used for DC initialization and test data on $f_{\mathcal{S}}$

In Figure [8,](#page-17-1) we show the distribution of  $f_S$  losses evaluated on data used for DC initialization (Real Init) and data not used for initialization (Other). We can observe that the losses of data used for initialization are smaller than other data, showing that the membership of data used for DC initialization are easier to be inferred. The distribution difference also explains the high advantage scores in Table [1.](#page-6-1)

<span id="page-16-4"></span><sup>1</sup><https://github.com/VICO-UoE/DatasetCondensation>

Image /page/17/Figure/1 description: This image displays a grid of 12 histograms, organized into 4 rows and 3 columns. The rows are labeled on the left as 'DSA Probability', 'DM Probability', 'KIP (w/o zca) Probability', and 'KIP (w/ zca) Probability'. The columns are labeled at the top as 'FashionMNIST', 'CIFAR-10', and 'CelebA'. Each histogram shows the distribution of 'Loss' on the x-axis, with the 'CelebA' column using a 'Loss (log-scale)' for its x-axis. Within each histogram, there are two sets of bars representing 'Label' categories: 'Real Init' (blue) and 'Other' (orange). The y-axis for all histograms represents 'Probability', ranging from 0.0 to 0.4. The histograms generally show a peak at low loss values, with the 'Real Init' category having a higher probability at these low values, and the 'Other' category having a broader distribution across higher loss values.

Figure 8. Loss distribution of data used for DC initialization (Real Init) are smaller than data not used for initialization (Other).

<span id="page-17-0"></span>

#### E.3. Visualization of DC-synthesized data distribution

Figure [9](#page-17-3) shows the t-SNE visualization of CIFAR-10 and CelebA data synthesized by GAN and DC methods (DSA, DM and KIP without ZCA preprocessing). We clip the DC-synthesized into 0 and 1 for fair comparison with GAN-synthesized data. Note that the generated data distributions of DM and DSA are more similar than KIP and GAN, explaining why DM-synthesized data and DSA-synthesized data enable models to achieve higher accuracy under same  $r_{inc}$ .

<span id="page-17-2"></span>

#### E.4. MIA against cGANs

Our threat model assumes that the adversary has white-box access to the synthetic dataset. We apply the MIA against GANs proposed by [Chen et al.](#page-9-4) (called GAN-leak). The main intuition is that member data are easier to be reconstructed by GAN

<span id="page-17-3"></span><span id="page-17-1"></span>Image /page/17/Figure/7 description: This image contains two scatter plots side-by-side, both with x and y axes ranging from 0.0 to 1.0. The left plot is titled "CIFAR-10" and the right plot is titled "CelebA". Both plots display data points categorized by color and shape, as indicated by a legend on the right. The legend shows that blue dots represent "Original" data, orange stars represent "GAN", green circles represent "DM", red triangles represent "DSA", and purple plus signs represent "KIP (w/o zca)". The distribution of these points differs between the two plots, suggesting a comparison of data representations or model performances on the CIFAR-10 and CelebA datasets.

Figure 9. Distribution visualization of CIFAR-10 (left) and CelebA (right) synthesized by GAN, DSA, DM and KIP (without ZCA preprocessing).

Privacy for Free: How does Dataset Condensation Help Privacy?

Image /page/18/Figure/1 description: This image contains three bar charts, one for FashionMNIST, one for CIFAR-10, and one for CelebA. Each chart displays accuracy on the y-axis, ranging from 0.60 to 0.90 for FashionMNIST and CelebA, and 0.25 to 0.60 for CIFAR-10. The x-axis for each dataset is divided into 'Real' and 'Random' categories. Within each category, there are five bars representing different methods: Baseline, DSA, DM, KIP (w/o ZCA), and KIP (w/ ZCA). The bars have different patterns: blue with dots for Baseline, orange with cross-hatching for DSA, green with diagonal lines for DM, red with stars for KIP (w/o ZCA), and purple with circles for KIP (w/ ZCA). Error bars are shown on top of each bar. For FashionMNIST, under 'Real', the accuracies are approximately 0.77, 0.83, 0.82, 0.74, and 0.74. Under 'Random', they are approximately 0.77, 0.84, 0.82, 0.74, and 0.74. For CIFAR-10, under 'Real', the accuracies are approximately 0.32, 0.50, 0.46, 0.38, and 0.38. Under 'Random', they are approximately 0.32, 0.48, 0.46, 0.38, and 0.44. For CelebA, under 'Real', the accuracies are approximately 0.63, 0.70, 0.79, 0.68, and 0.70. Under 'Random', they are approximately 0.63, 0.70, 0.79, 0.68, and 0.70.

Figure 10. Accuracy of models trained on data synthesized by different DC methods and on data generated by baselines for  $r_{inc} = 0.002$ .

generators  $G$ , so the MIA is based on the (calibrated) reconstructed loss  $L_{cal}$ :

<span id="page-18-0"></span>
$$
M(\mathbf{x}) = \mathbb{1}(L_{cal}(\mathbf{x}, \mathcal{G}(\mathbf{z})) \le \tau). \tag{50}
$$

The adversary optimizes  $L_{cal}$  by varying z to estimate whether x belongs to the training dataset. According to the adversary' knowledge, the attack can be divided into black-box attack, partial black-box attack and white-box attack. We conducted the white-box attack for scenarios where the adversary has access to the generators. The results on CelebA are in Table [4,](#page-18-1) indicating that vanilla GAN can be used to infer the membership of training data. [Chen et al.](#page-9-4) also validated that partial black-box attack can achieve similar attack performance as white-box, because the adversary has access to z and can leverage non-differentiable optimization, e.g., the Powell's Conjugate Direction Method [\(Powell,](#page-10-18) [1964\)](#page-10-18)), to approximately minimize  $L_{cal}$ .

Table 4. Results of GAN-leak attack against cGANs averaged over 10 shadow models.

| <b>Dataset</b> | ROC AUC          | Advantage $(\%)$ |
|----------------|------------------|------------------|
| CelebA         | $56.06 \pm 2.03$ | $22.98 \pm 4.27$ |

<span id="page-18-1"></span>

# E.5. Comparison of accuracy for models trained on synthetic dataset for $r_{ipc} = 0.002$

Figure [10](#page-18-0) presents the accuracy comparison results of models trained on data synthesized by DC and baseline methods for  $r_{inc} = 0.002$ . We can see that KIP significantly outperforms baselines and achieves similar performance with DSA and DM on CIFAR-10. Moreover, we can observe that the ZCA preprocessing is effective for improving the utility of KIP-synthesized dataset.
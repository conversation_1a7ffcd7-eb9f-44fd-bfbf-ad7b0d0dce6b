<span id="page-0-0"></span><PERSON> <sup>1</sup> <PERSON><PERSON><PERSON> <sup>12</sup> <PERSON> <sup>1</sup> <PERSON><PERSON><PERSON><PERSON> <sup>1</sup> <PERSON><PERSON><PERSON> <sup>134</sup> <PERSON>  $13$  R <PERSON>  $14$ 

### Abstract

We argue that the estimation of mutual information between high dimensional continuous random variables can be achieved by gradient descent over neural networks. We present a Mutual Information Neural Estimator (MINE) that is linearly scalable in dimensionality as well as in sample size, trainable through back-prop, and strongly consistent. We present a handful of applications on which MINE can be used to minimize or maximize mutual information. We apply MINE to improve adversarially trained generative models. We also use MINE to implement the Information Bottleneck, applying it to supervised classification; our results demonstrate substantial improvement in flexibility and performance in these settings.

# 1. Introduction

Mutual information is a fundamental quantity for measuring the relationship between random variables. In data science it has found applications in a wide range of domains and tasks, including biomedical sciences [\(<PERSON><PERSON> et al.,](#page-9-0) [1997\)](#page-9-0), blind source separation (BSS, e.g., independent component analysis, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., [2004\)](#page-8-0), information bottleneck (<PERSON><PERSON>, [<PERSON><PERSON><PERSON> et al.,](#page-9-0) [2000\)](#page-9-0), feature selection [\(Kwak & Choi,](#page-8-0) [2002;](#page-8-0) [Peng et al.,](#page-9-0) [2005\)](#page-9-0), and causality [\(Butte & Kohane,](#page-8-0) [2000\)](#page-8-0).

Put simply, mutual information quantifies the dependence of two random variables  $X$  and  $Z$ . It has the form,

$$
I(X;Z) = \int_{\mathcal{X} \times \mathcal{Z}} \log \frac{d\mathbb{P}_{XZ}}{d\mathbb{P}_{X} \otimes \mathbb{P}_{Z}} d\mathbb{P}_{XZ}, \quad (1)
$$

where  $\mathbb{P}_{XZ}$  is the joint probability distribution, and  $\mathbb{P}_X$  =  $\int_{\mathcal{Z}} d\mathbb{P}_{XZ}$  and  $\mathbb{P}_{Z} = \int_{\mathcal{X}} d\mathbb{P}_{XZ}$  are the marginals. In contrast to correlation, mutual information captures non-linear statistical dependencies between variables, and thus can act as a measure of true dependence [\(Kinney & Atwal,](#page-8-0) [2014\)](#page-8-0).

Despite being a pivotal quantity across data science, mutual information has historically been difficult to compute [\(Panin](#page-9-0)[ski,](#page-9-0) [2003\)](#page-9-0). Exact computation is only tractable for discrete variables (as the sum can be computed exactly), or for a limited family of problems where the probability distributions are known. For more general problems, this is not possible. Common approaches are non-parametric (e.g., binning, likelihood-ratio estimators based on support vector machines, non-parametric kernel-density estimators; see, [Fraser & Swinney,](#page-8-0) [1986;](#page-8-0) [Darbellay & Vajda,](#page-8-0) [1999;](#page-8-0) [Suzuki](#page-9-0) [et al.,](#page-9-0) [2008;](#page-9-0) [Kwak & Choi,](#page-8-0) [2002;](#page-8-0) [Moon et al.,](#page-9-0) [1995;](#page-9-0) [Kraskov](#page-8-0) [et al.,](#page-8-0) [2004\)](#page-8-0), or rely on approximate gaussianity of data distribution (e.g., Edgeworth expansion, [Van Hulle,](#page-9-0) [2005\)](#page-9-0). Unfortunately, these estimators typically do not scale well with sample size or dimension [\(Gao et al.,](#page-8-0) [2014\)](#page-8-0), and thus cannot be said to be general-purpose. Other recent works include [Kandasamy et al.](#page-8-0)  $(2017)$ ; Singh & Póczos  $(2016)$ ; [Moon et al.](#page-9-0) [\(2017\)](#page-9-0).

In order to achieve a general-purpose estimator, we rely on the well-known characterization of the mutual information as the Kullback-Leibler (KL-) divergence [\(Kullback,](#page-8-0) [1997\)](#page-8-0) between the joint distribution and the product of the marginals (i.e.,  $I(X; Z) = D_{KL}(\mathbb{P}_{XZ} \mid \mid \mathbb{P}_X \otimes \mathbb{P}_Z)$ ). Recent work uses a dual formulation to cast the estimation of f-divergences (including the KL-divergence, see [Nguyen](#page-9-0) [et al.,](#page-9-0) [2010\)](#page-9-0) as part of an adversarial game between competing deep neural networks [\(Nowozin et al.,](#page-9-0) [2016\)](#page-9-0). This approach is at the cornerstone of generative adversarial networks (GANs, [Goodfellow et al.,](#page-8-0) [2014\)](#page-8-0), which train a generative model without any explicit assumptions about the underlying distribution of the data.

In this paper we demonstrate that exploiting dual optimization to estimate divergences goes beyond the minimax objective as formalized in GANs. We leverage this strategy to offer a general-purpose parametric neural estimator of mutual information based on dual representations of the KL-divergence [\(Ruderman et al.,](#page-9-0) [2012\)](#page-9-0), which we show is valuable in settings that do not necessarily involve an adversarial game. Our estimator is scalable, flexible, and completely trainable via back-propagation. The contribu-

 $1$ Montréal Institute for Learning Algorithms (MILA), University of Montréal <sup>2</sup>Department of Mathematics and Statistics, McGill University <sup>3</sup>Canadian Institute for Advanced Research (CIFAR) <sup>4</sup>The Institute for Data Valorization (IVADO). Correspondence to: Mohamed Ishmael Belghazi <<EMAIL>>.

*Proceedings of the*  $35<sup>th</sup>$  *International Conference on Machine Learning*, Stockholm, Sweden, PMLR 80, 2018. Copyright 2018 by the author(s).

<span id="page-1-0"></span>tions of this paper are as follows:

- We introduce the Mutual Information Neural Estimator (MINE), which is scalable, flexible, and completely trainable via back-prop, as well as provide a thorough theoretical analysis.
- We show that the utility of this estimator transcends the minimax objective as formalized in GANs, such that it can be used in mutual information estimation, maximization, and minimization.
- We apply MINE to palliate mode-dropping in GANs and to improve reconstructions and inference in Adversarially Learned Inference (ALI, [Dumoulin et al.,](#page-8-0) [2016\)](#page-8-0) on large scale datasets.
- We use MINE to apply the Information Bottleneck method [\(Tishby et al.,](#page-9-0) [2000\)](#page-9-0) in a continuous setting, and show that this approach outperforms variational bottleneck methods [\(Alemi et al.,](#page-8-0) [2016\)](#page-8-0).

## 2. Background

## 2.1. Mutual Information

Mutual information is a Shannon entropy-based measure of dependence between random variables. The mutual information between  $X$  and  $Z$  can be understood as the decrease of the uncertainty in  $X$  given  $Z$ :

$$
I(X;Z) := H(X) - H(X | Z),
$$
 (2)

where H is the Shannon entropy, and  $H(X | Z)$  is the conditional entropy of  $Z$  given  $X$ . As stated in Eqn. [1](#page-0-0) and the discussion above, the mutual information is equivalent to the Kullback-Leibler (KL-) divergence between the joint,  $\mathbb{P}_{XZ}$ , and the product of the marginals  $\mathbb{P}_X \otimes \mathbb{P}_Z$ :

$$
I(X,Z) = D_{KL}(\mathbb{P}_{XZ} \mid \mid \mathbb{P}_X \otimes \mathbb{P}_Z), \tag{3}
$$

where  $D_{KL}$  is defined as<sup>1</sup>,

$$
D_{KL}(\mathbb{P} \mid \mid \mathbb{Q}) := \mathbb{E}_{\mathbb{P}} \left[ \log \frac{d\mathbb{P}}{d\mathbb{Q}} \right]. \tag{4}
$$

whenever  $\mathbb P$  is absolutely continuous with respect to  $\mathbb Q^2$ .

The intuitive meaning of Eqn. 3 is clear: the larger the divergence between the joint and the product of the marginals, the stronger the dependence between  $X$  and  $Z$ . This divergence, hence the mutual information, vanishes for fully independent variables.

<sup>2</sup> and infinity otherwise.

### 2.2. Dual representations of the KL-divergence.

A key technical ingredient of MINE are *dual representations* of the KL-divergence. We will primarily work with the Donsker-Varadhan representation [\(Donsker & Varadhan,](#page-8-0) [1983\)](#page-8-0), which results in a tighter estimator; but will also consider the dual f-divergence representation [\(Keziou,](#page-8-0) [2003;](#page-8-0) [Nguyen et al.,](#page-9-0) [2010;](#page-9-0) [Nowozin et al.,](#page-9-0) [2016\)](#page-9-0).

The Donsker-Varadhan representation. The following theorem gives a representation of the KL-divergence [\(Donsker & Varadhan,](#page-8-0) [1983\)](#page-8-0):

Theorem 1 (Donsker-Varadhan representation). *The KL divergence admits the following dual representation:*

$$
D_{KL}(\mathbb{P} \mid \mid \mathbb{Q}) = \sup_{T:\Omega \to \mathbb{R}} \mathbb{E}_{\mathbb{P}}[T] - \log(\mathbb{E}_{\mathbb{Q}}[e^T]), \quad (5)
$$

*where the supremum is taken over all functions* T *such that the two expectations are finite.*

*Proof.* See the Supplementary Material.

A straightforward consequence of Theorem 1 is as follows. Let F be *any* class of functions  $T : \Omega \to \mathbb{R}$  satisfying the integrability constraints of the theorem. We then have the lower-bound<sup>3</sup>:

$$
D_{KL}(\mathbb{P} \mid \mid \mathbb{Q}) \ge \sup_{T \in \mathcal{F}} \mathbb{E}_{\mathbb{P}}[T] - \log(\mathbb{E}_{\mathbb{Q}}[e^T]). \tag{6}
$$

Note also that the bound is *tight* for optimal functions  $T^*$ that relate the distributions to the *Gibbs density* as,

$$
d\mathbb{P} = \frac{1}{Z}e^{T^*}d\mathbb{Q}, \text{ where } Z = \mathbb{E}_{\mathbb{Q}}[e^{T^*}]. \tag{7}
$$

The f-divergence representation. It is worthwhile to compare the Donsker-Varadhan representation to the fdivergence representation proposed in [Nguyen et al.](#page-9-0) [\(2010\)](#page-9-0); [Nowozin et al.](#page-9-0) [\(2016\)](#page-9-0), which leads to the following bound:

$$
D_{KL}(\mathbb{P} \mid \mid \mathbb{Q}) \ge \sup_{T \in \mathcal{F}} \mathbb{E}_{\mathbb{P}}[T] - \mathbb{E}_{\mathbb{Q}}[e^{T-1}]. \tag{8}
$$

Although the bounds in Eqns. 6 and 8 are tight for sufficiently large families  $F$ , the Donsker-Varadhan bound is *stronger* in the sense that, for any fixed T, the right hand side of Eqn. 6 is larger<sup>4</sup> than the right hand side of Eqn. 8. We refer to the work by [Ruderman et al.](#page-9-0) [\(2012\)](#page-9-0) for a derivation of both representations in Eqns. 6 and 8 from the unifying perspective of Fenchel duality. In Section [3](#page-2-0) we discuss versions of MINE based on these two representations, and numerical comparisons are performed in Section [4.](#page-3-0)

<sup>&</sup>lt;sup>1</sup>Although the discussion is more general, we can think of  $\mathbb P$ and  $\mathbb Q$  as being distributions on some compact domain  $\Omega \subset \mathbb R^d$ , with density p and q respect the Lebesgue measure  $\lambda$ , so that  $D_{KL} = \int p \log \frac{p}{q} d\lambda.$ 

<sup>3</sup>The bound in Eqn. 6 is known as the *compression lemma* in the PAC-Bayes literature [\(Banerjee,](#page-8-0) [2006\)](#page-8-0).

<sup>&</sup>lt;sup>4</sup>To see this, just apply the identity  $x \ge e \log x$  with  $x =$  $\mathbb{E}_{\mathbb{Q}}[e^T].$ 

# <span id="page-2-0"></span>3. The Mutual Information Neural Estimator

In this section we formulate the framework of the Mutual Information Neural Estimator (MINE). We define MINE and present a theoretical analysis of its consistency and convergence properties.

## 3.1. Method

Using both Eqn. [3](#page-1-0) for the mutual information and the dual representation of the KL-divergence, the idea is to choose  $\mathcal F$ to be the family of functions  $T_{\theta} : \mathcal{X} \times \mathcal{Z} \rightarrow \mathbb{R}$  parametrized by a deep neural network with parameters  $\theta \in \Theta$ . We call this network the *statistics network*. We exploit the bound:

$$
I(X;Z) \ge I_{\Theta}(X,Z),\tag{9}
$$

where  $I_{\Theta}(X, Z)$  is the *neural information measure* defined as

$$
I_{\Theta}(X, Z) = \sup_{\theta \in \Theta} \mathbb{E}_{\mathbb{P}_{XZ}}[T_{\theta}] - \log(\mathbb{E}_{\mathbb{P}_X \otimes \mathbb{P}_Z}[e^{T_{\theta}}]). \quad (10)
$$

The expectations in Eqn. 10 are estimated using empirical samples<sup>5</sup> from  $\mathbb{P}_{XZ}$  and  $\mathbb{P}_X \otimes \mathbb{P}_Z$  or by shuffling the samples from the joint distribution along the batch axis. The objective can be maximized by gradient ascent.

It should be noted that Eqn. 10 actually *defines* a new class information measures, The expressive power of neural network insures that they can approximate the mutual information with arbitrary accuracy.

In what follows, given a distribution  $\mathbb{P}$ , we denote by  $\mathbb{P}^{(n)}$ as the empirical distribution associated to n *i.i.d.* samples.

Definition 3.1 (Mutual Information Neural Estimator (MINE)). Let  $\mathcal{F} = \{T_{\theta}\}_{\theta \in \Theta}$  be the set of functions parametrized by a neural network. MINE is defined as,

$$
\widehat{I(X;Z)}_n = \sup_{\theta \in \Theta} \mathbb{E}_{\mathbb{P}_{XZ}^{(n)}}[T_{\theta}] - \log(\mathbb{E}_{\mathbb{P}_{X}^{(n)} \otimes \hat{\mathbb{P}}_{Z}^{(n)}}[e^{T_{\theta}}]). \tag{11}
$$

Details on the implementation of MINE are provided in Algorithm 1. An analogous definition and algorithm also hold for the  $f$ -divergence formulation in Eqn. [8,](#page-1-0) which we refer to as MINE-f. Since Eqn. [8](#page-1-0) lower-bounds Eqn. [6,](#page-1-0) it generally leads to a *looser* estimator of the mutual information, and numerical comparisons of MINE with MINE-f can be found in Section [4.](#page-3-0) However, in a mini-batch setting, the SGD gradients of MINE are biased. We address this in the next section.

| <b>Algorithm 1 MINE</b> |  |  |
|-------------------------|--|--|
|-------------------------|--|--|

| $\theta \leftarrow$ initialize network parameters                                                                                                                                      |
|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| repeat                                                                                                                                                                                 |
| Draw b minibatch samples from the joint distribution:                                                                                                                                  |
| $(\bm{x}^{(1)}, \bm{z}^{(1)}), \ldots, (\bm{x}^{(b)}, \bm{z}^{(b)}) \sim \mathbb{P}_{XZ}$                                                                                              |
| Draw $b$ samples from the $Z$ marginal distribution:                                                                                                                                   |
| $\bar{z}^{(1)}, \ldots, \bar{z}^{(b)} \sim \mathbb{P}_Z$                                                                                                                               |
| Evaluate the lower-bound:                                                                                                                                                              |
| $\mathcal{V}(\theta) \leftarrow \frac{1}{b} \sum_{i=1}^{b} T_{\theta}(\bm{x}^{(i)}, \bm{z}^{(i)}) - \log(\frac{1}{b} \sum_{i=1}^{b} e^{T_{\theta}(\bm{x}^{(i)}, \bar{\bm{z}}^{(i)})})$ |
| Evaluate bias corrected gradients (e.g., moving aver-<br>age):                                                                                                                         |
| $\widehat{G}(\theta) \leftarrow \widetilde{\nabla}_{\theta} \mathcal{V}(\theta)$                                                                                                       |
| Update the statistics network parameters:                                                                                                                                              |
| $\theta \leftarrow \theta + \widehat{G}(\theta)$                                                                                                                                       |
| <b>until</b> convergence                                                                                                                                                               |

## 3.2. Correcting the bias from the stochastic gradients

A naive application of stochastic gradient estimation leads to the gradient estimate:

$$
\widehat{G}_B = \mathbb{E}_B[\nabla_\theta T_\theta] - \frac{\mathbb{E}_B[\nabla_\theta T_\theta e^{T_\theta}]}{\mathbb{E}_B[e^{T_\theta}]}.
$$
 (12)

where, in the second term, the expectations are over the samples of a minibatch  $B$ , leads to a biased estimate of the full batch gradient<sup>6</sup>.

Fortunately, the bias can be reduced by replacing the estimate in the denominator by an exponential moving average. For small learning rates, this improved MINE gradient estimator can be made to have arbitrarily small bias.

We found in our experiments that this improves all-around performance of MINE.

## 3.3. Theoretical properties

In this section we analyze the consistency and convergence properties of MINE. All the proofs can be found in the Supplementary Material.

## 3.3.1. CONSISTENCY

MINE relies on a choice of  $(i)$  a statistics network and  $(ii)$ *n* samples from the data distribution  $\mathbb{P}_{XZ}$ .

Definition 3.2 (Strong consistency). The estimator  $I(X; Z)_n$  is strongly consistent if for all  $\epsilon > 0$ , there exists a positive integer  $N$  and a choice of statistics network such that:

$$
\forall n \ge N, \quad |I(X,Z) - \widehat{I(X;Z)}_n| \le \epsilon, \ a.e.
$$

where the probability is over a set of samples.

<sup>&</sup>lt;sup>5</sup>Note that samples  $\bar{x} \sim \mathbb{P}_X$  and  $\bar{z} \sim \mathbb{P}_Z$  from the marginals are obtained by simply dropping x, z from samples  $(\bar{x}, z)$  and  $(x,\bar{z}) \sim \mathbb{P}_{XZ}$ .

 $6$ From the optimization point of view, the  $f$ -divergence formulation has the advantage of making the use of SGD with unbiased gradients straightforward.

<span id="page-3-0"></span>In a nutshell, the question of consistency is divided into two problems: an *approximation* problem related to the size of the family, F, and an *estimation* problem related to the use of empirical measures. The first problem is addressed by universal approximation theorems for neural networks [\(Hornik,](#page-8-0) [1989\)](#page-8-0). For the second problem, classical consistency theorems for extremum estimators apply [\(Van de Geer,](#page-9-0) [2000\)](#page-9-0) under mild conditions on the parameter space.

This leads to the two lemmas below. The first lemma states that the neural information measures  $I_{\Theta}(X, Z)$ , defined in Eqn. [10,](#page-2-0) can approximate the mutual information with arbitrary accuracy:

**Lemma 1** (approximation). Let  $\epsilon > 0$ . There exists a neural *network parametrizing functions*  $T_{\theta}$  *with parameters*  $\theta$  *in* some compact domain  $\Theta \subset \mathbb{R}^k$ , such that

$$
|I(X,Z) - I_{\Theta}(X,Z)| \le \epsilon, \ a.e.
$$

The second lemma states the almost sure convergence of MINE to a neural information measure as the number of samples goes to infinity:

**Lemma 2** (estimation). Let  $\epsilon > 0$ . Given a family of neural *network functions*  $T_{\theta}$  *with parameters*  $\theta$  *in some bounded*  $domain \,\Theta \subset \mathbb{R}^k$ , there exists an  $N \in \mathbb{N}$ , such that

$$
\forall n \ge N, \quad | \widehat{I(X;Z)}_n - I_{\Theta}(X,Z) | \le \epsilon, \, a.e. \tag{13}
$$

Combining the two lemmas with the triangular inequality, we have,

### Theorem 2. *MINE is strongly consistent.*

### 3.3.2. SAMPLE COMPLEXITY

In this section we discuss the *sample complexity* of our estimator. Since the focus here is on the empirical estimation problem, we assume that the mutual information is well enough approximated by the neural information measure  $I_{\Theta}(X, Z)$ . The theorem below is a refinement of Lemma 2: it gives how many samples we need for an empirical estimation of the neural information measure at a given accuracy and with high confidence.

We make the following assumptions: the functions  $T_{\theta}$  are  $L$ -Lipschitz with respect to the parameters  $\theta$ , and both  $T_{\theta}$  and  $e^{T_{\theta}}$  are M-bounded (i.e.,  $|T_{\theta}|, e^{T_{\theta}} \leq M$ ). The domain  $\Theta \subset$  $\mathbb{R}^d$  is bounded, so that  $\|\theta\| \leq K$  for some constant K. The theorem below shows a sample complexity of  $\widetilde{O}\left(\frac{d \log d}{\epsilon^2}\right)$ , where  $d$  is the dimension of the parameter space.

**Theorem 3.** *Given any values*  $\epsilon$ ,  $\delta$  *of the desired accuracy and confidence parameters, we have,*

$$
\Pr\left(|\widehat{I(X;Z)}_n - I_\Theta(X,Z)| \le \epsilon\right) \ge 1 - \delta,\qquad(14)
$$

*whenever the number* n *of samples satisfies*

$$
n \ge \frac{2M^2(d\log(16KL\sqrt{d}/\epsilon) + 2dM + \log(2/\delta))}{\epsilon^2}.
$$
\n(15)

# 4. Empirical comparisons

Before diving into applications, we perform some simple empirical evaluation and comparisons of MINE. The objective is to show that MINE is effectively able to estimate mutual information and account for non-linear dependence.

### 4.1. Comparing MINE to non-parametric estimation

We compare MINE and MINE- $f$  to the  $k$ -NN-based nonparametric estimator found in [Kraskov et al.](#page-8-0) [\(2004\)](#page-8-0). In our experiment, we consider multivariate Gaussian random variables,  $X_a$  and  $X_b$ , with componentwise correlation,  $corr(X_a^i, X_b^j) = \delta_{ij} \rho$ , where  $\rho \in (-1, 1)$  and  $\delta_{ij}$  is Kronecker's delta. As the mutual information is invariant to continuous bijective transformations of the considered variables, it is enough to consider standardized Gaussians marginals. We also compare MINE (using the Donsker-Varadhan representation in Eqn.  $6$ ) and MINE- $f$  (based on the f-divergence representation in Eqn. [8\)](#page-1-0).

Our results are presented in Figs. 1. We observe that both MINE and Kraskov's estimation are virtually indistinguishable from the ground truth when estimating the mutual information between bivariate Gaussians. MINE shows marked improvement over Krakov's when estimating the mutual information between twenty dimensional random variables. We also remark that MINE provides a tighter estimate of the mutual information than MINE- $f$ .

Image /page/3/Figure/22 description: The image displays two plots comparing different methods for estimating mutual information. The left plot, titled "Mutual Information of 2-dimensional variables," shows the mutual information I(Xa; Xb) on the y-axis against the correlation coefficient \u03c1 on the x-axis. The x-axis ranges from -0.99 to 0.99, with tick marks at intervals of 0.10. Four lines are plotted: MINE (blue solid), MINE-f (green solid), Kraskov (red solid), and True MI (blue dashed). The mutual information is low for correlations close to 0 and increases as the absolute value of the correlation approaches 1. The right plot, titled "Mutual Information of 20-dimensional variables," presents a similar comparison but for 20-dimensional variables. The y-axis scale is much larger, ranging from 0 to 40. The trend is similar, with mutual information increasing with the absolute value of the correlation. In both plots, the MINE and MINE-f methods generally track the True MI closely, while the Kraskov method shows lower estimates, especially for the 20-dimensional case at high correlations.

Figure 1. Mutual information between two multivariate Gaussians with component-wise correlation  $\rho \in (-1, 1)$ .

### 4.2. Capturing non-linear dependencies

An important property of mutual information between random variables with relationship  $Y = f(X) + \sigma \odot \epsilon$ , where f is a deterministic non-linear transformation and  $\epsilon$  is random noise, is that it is invariant to the deterministic nonlinear transformation, but should only depend on the amount of noise,  $\sigma \odot \epsilon$ . This important property, that guarantees the quantification dependence without bias for the relationship, <span id="page-4-0"></span>is called *equitability* [\(Kinney & Atwal,](#page-8-0) [2014\)](#page-8-0). Our results (Fig. 2) show that MINE captures this important property.

Image /page/4/Figure/2 description: A heatmap displays the relationship between three functions (x, x^3, and sin(x)) and a variable labeled sigma, ranging from 0.0 to 0.9. The heatmap uses a gradient of purple to pink, with darker shades indicating lower values and lighter shades indicating higher values. The functions are listed vertically on the left side of the heatmap, and the sigma values are listed horizontally along the bottom.

Figure 2. MINE is invariant to choice of deterministic nonlinear transformation. The heatmap depicts mutual information estimated by MINE between 2-dimensional random variables  $X \sim \mathcal{U}(-1, 1)$ and  $Y = f(X) + \sigma \odot \epsilon$ , where  $f(x) \in \{x, x^3, \sin(x)\}\$ and  $\epsilon \sim \mathcal{N}(0, I).$ 

# 5. Applications

In this section, we use MINE to present applications of mutual information and compare to competing methods designed to achieve the same goals. Specifically, by using MINE to maximize the mutual information, we are able to improve mode representation and reconstruction of generative models. Finally, by minimizing mutual information, we are able to effectively implement the information bottleneck in a continuous setting.

## 5.1. Maximizing mutual information to improve GANs

Mode collapse [\(Che et al.,](#page-8-0) [2016;](#page-8-0) [Dumoulin et al.,](#page-8-0) [2016;](#page-8-0) [Donahue et al.,](#page-8-0) [2016;](#page-8-0) [Salimans et al.,](#page-9-0) [2016;](#page-9-0) [Metz et al.,](#page-9-0) [2017;](#page-9-0) [Saatchi & Wilson,](#page-9-0) [2017;](#page-9-0) [Nguyen et al.,](#page-9-0) [2017;](#page-9-0) [Lin](#page-9-0) [et al.,](#page-9-0) [2017;](#page-9-0) [Ghosh et al.,](#page-8-0) [2017\)](#page-8-0) is a common pathology of generative adversarial networks (GANs, [Goodfellow et al.,](#page-8-0) [2014\)](#page-8-0), where the generator fails to produces samples with sufficient diversity (i.e., poorly represent some modes).

GANs as formulated in [Goodfellow et al.](#page-8-0) [\(2014\)](#page-8-0) consist of two components: a discriminator,  $D : \mathcal{X} \to [0,1]$  and a generator,  $G : \mathcal{Z} \to \mathcal{X}$ , where X is a domain such as a compact subspace of  $\mathbb{R}^n$ . Given  $Z \in \mathcal{Z}$  follows some simple prior distribution (e.g., a spherical Gaussian with density,  $\mathbb{P}_Z$ ), the goal of the generator is to match its output distribution to a target distribution,  $\mathbb{P}_X$  (specified by the data samples). The discriminator and generator are optimized through the *value function*,

$$
\min_{G} \max_{D} V(D, G) :=
$$
  
$$
\mathbb{E}_{\mathbb{P}_X}[D(X)] + \mathbb{E}_{\mathbb{P}_Z}[\log(1 - D(G(Z)))].
$$
 (16)

A natural approach to diminish mode collapse would be regularizing the generator's loss with the neg-entropy of the samples. As the sample entropy is intractable, we propose to use the mutual information as a proxy.

Following [Chen et al.](#page-8-0) [\(2016\)](#page-8-0), we write the prior as the concatenation of noise and code variables,  $Z = [\epsilon, c]$ . We propose to palliate mode collapse by maximizing the mutual information between the samples and the code.  $I(G([\epsilon, c]); c) = H(G([\epsilon, c])) - H(G([\epsilon, c]) | c)$ . The generator objective then becomes,

$$
\underset{G}{\arg\max} \mathbb{E}[\log(D(G([\epsilon, \mathbf{c}])))] + \beta I(G([\epsilon, \mathbf{c}]); \mathbf{c}). \quad (17)
$$

As the samples  $G([\epsilon, c])$  are differentiable w.r.t. the parameters of G, and the statistics network being a differentiable function, we can maximize the mutual information using back-propagation and gradient ascent by only specifying this additional loss term. Since the mutual information is theoretically unbounded, we use adaptive gradient clipping (see the Supplementary Material) to ensure that the generator receives learning signals similar in magnitude from the discriminator and the statistics network.

Related works on mode-dropping Methods to address mode dropping in GANs can readily be found in the literature. [Salimans et al.](#page-9-0) [\(2016\)](#page-9-0) use mini-batch discrimination. In the same spirit, [Lin et al.](#page-9-0) [\(2017\)](#page-9-0) successfully mitigates mode dropping in GANs by modifying the discriminator to make decisions on multiple real or generated samples. [Ghosh et al.](#page-8-0) [\(2017\)](#page-8-0) uses multiple generators that are encouraged to generate different parts of the target distribution. [Nguyen et al.](#page-9-0) [\(2017\)](#page-9-0) uses two discriminators to minimize the KL and reverse KL divergences between the target and generated distributions. [Che et al.](#page-8-0) [\(2016\)](#page-8-0) learns a reconstruction distribution, then teach the generator to sample from it, the intuition being that the reconstruction distribution is a de-noised or *smoothed* version of the data distribution, and thus easier to learn. [Srivastava et al.](#page-9-0) [\(2017\)](#page-9-0) minimizes the reconstruction error in the latent space of bi-directional GANs [\(Dumoulin et al.,](#page-8-0) [2016;](#page-8-0) [Donahue et al.,](#page-8-0) [2016\)](#page-8-0). [Metz](#page-9-0) [et al.](#page-9-0) [\(2017\)](#page-9-0) includes many steps of the discriminator's optimization as part of the generator's objective. While [Chen](#page-8-0) [et al.](#page-8-0) [\(2016\)](#page-8-0) maximizes the mutual information between the code and the samples, it does so by minimizing a variational upper bound on the conditional entropy [\(Barber & Agakov,](#page-8-0) [2003\)](#page-8-0) therefore ignoring the entropy of the samples. [Chen](#page-8-0) [et al.](#page-8-0) [\(2016\)](#page-8-0) makes no claim about mode-dropping.

Experiments: Spiral, 25-Gaussians datasets We apply MINE to improve mode coverage when training a generative adversarial network (GAN, [Goodfellow et al.,](#page-8-0) [2014\)](#page-8-0). We demonstrate using Eqn. 17 on the spiral and the 25- Gaussians datasets, comparing two models, one with  $\beta = 0$ (which corresponds to the orthodox GAN as in [Goodfellow](#page-8-0) [et al.](#page-8-0) [\(2014\)](#page-8-0)) and one with  $\beta = 1.0$ , which corresponds to mutual information maximization.

Our results on the spiral (Fig. [3\)](#page-5-0) and the 25-Gaussians (Fig. [4\)](#page-5-0) experiments both show improved mode coverage over the baseline with no mutual information objective. This

<span id="page-5-0"></span>Image /page/5/Figure/1 description: The image displays two scatter plots side-by-side, labeled (a) GAN and (b) GAN+MINE. Both plots show data points forming spiral patterns. In plot (a), there are two distinct spirals, one in blue and one in orange, with the orange spiral appearing more spread out and overlapping the blue spiral significantly. In plot (b), the spirals are more intermingled and less clearly defined than in plot (a), with both blue and orange points distributed throughout the spiral structure. The axes in both plots range from -2.0 to 2.0.

Figure 3. The generator of the GAN model without mutual information maximization after 5000 iterations suffers from mode collapse (has poor coverage of the target dataset) compared to GAN+MINE on the spiral experiment.

Image /page/5/Picture/3 description: The image displays three blurry green heatmaps arranged horizontally. Each heatmap is labeled below with text. The first heatmap, labeled "(a) Original data", shows a grid of approximately 4x5 distinct green dots. The second heatmap, labeled "(b) GAN", shows a less structured pattern with fewer, larger, and more spread-out green blobs. The third heatmap, labeled "(c) GAN+MINE", closely resembles the first heatmap, showing a grid of approximately 4x5 distinct green dots, suggesting a successful reconstruction or generation of the original data pattern.

Figure 4. Kernel density estimate (KDE) plots for GAN+MINE samples and GAN samples on 25 Gaussians dataset.

confirms our hypothesis that maximizing mutual information helps against mode-dropping in this simple setting.

Experiment: Stacked MNIST Following [Che et al.](#page-8-0) [\(2016\)](#page-8-0); [Metz et al.](#page-9-0) [\(2017\)](#page-9-0); [Srivastava et al.](#page-9-0) [\(2017\)](#page-9-0); [Lin](#page-9-0) [et al.](#page-9-0) [\(2017\)](#page-9-0), we quantitatively assess MINE's ability to diminish mode dropping on the stacked MNIST dataset which is constructed by stacking three randomly sampled MNIST digits. As a consequence, stacked MNIST offers 1000 modes. Using the same architecture and training protocol as in [Srivastava et al.](#page-9-0) [\(2017\)](#page-9-0); [Lin et al.](#page-9-0) [\(2017\)](#page-9-0), we train a GAN on the constructed dataset and use a pre-trained classifier on 26,000 samples to count the number of modes in the samples, as well as to compute the KL divergence between the sample and expected data distributions. Our results in Table 1 demonstrate the effectiveness of MINE in preventing mode collapse on Stacked MNIST.

## 5.2. Maximizing mutual information to improve inference in bi-directional adversarial models

Adversarial bi-directional models were introduced in Adversarially Learned Inference (ALI, [Dumoulin et al.,](#page-8-0) [2016\)](#page-8-0) and BiGAN [\(Donahue et al.,](#page-8-0) [2016\)](#page-8-0) and are an extension of GANs which incorporate a reverse model,  $F : \mathcal{X} \to \mathcal{Z}$ jointly trained with the generator. These models formulate the problem in terms of the value function in Eqn. [16](#page-4-0) between two joint distributions,  $p(x, z) = p(z | x)p(x)$  and  $q(x, z) = q(x | z)p(z)$  induced by the forward (encoder)

|                 | <b>Stacked MNIST</b> |                      |  |  |
|-----------------|----------------------|----------------------|--|--|
|                 | Modes<br>(Max 1000)  | KL.                  |  |  |
| <b>DCGAN</b>    | 99.0                 | 3.40                 |  |  |
| AL J            | 16.0                 | 5.40                 |  |  |
| Unrolled GAN    | 48.7                 | 4.32                 |  |  |
| <b>VEEGAN</b>   | 150.0                | 2.95                 |  |  |
| PacGAN          | $1000.0 \pm 0.0$     | $0.06 \pm 1.0e^{-2}$ |  |  |
| GAN+MINE (Ours) | $1000.0 \pm 0.0$     | $0.05 \pm 6.9e^{-3}$ |  |  |

Table 1. Number of captured modes and Kullblack-Leibler divergence between the training and samples distributions for DC-GAN [\(Radford et al.,](#page-9-0) [2015\)](#page-9-0), ALI [\(Dumoulin et al.,](#page-8-0) [2016\)](#page-8-0), Unrolled GAN [\(Metz et al.,](#page-9-0) [2017\)](#page-9-0), VeeGAN [\(Srivastava et al.,](#page-9-0) [2017\)](#page-9-0), PacGAN [\(Lin et al.,](#page-9-0) [2017\)](#page-9-0).

and reverse (decoder) models, respectively<sup>7</sup>.

One goal of bi-directional models is to do inference as well as to learn a good generative model. Reconstructions are one desirable property of a model that does both inference and generation, but in practice ALI can lack fidelity (i.e., reconstructs less faithfully than desired, see [Li et al.,](#page-8-0) [2017;](#page-8-0) [Ulyanov et al.,](#page-9-0) [2017;](#page-9-0) [Belghazi et al.,](#page-8-0) [2018\)](#page-8-0). To demonstrate the connection to mutual information, it can be shown (see the Supplementary Material for details) that the reconstruction error,  $R$ , is bounded by,

$$
\mathcal{R} \le D_{KL}(q(\boldsymbol{x}, \boldsymbol{z}) \mid p(\boldsymbol{x}, \boldsymbol{z})) - I_q(\boldsymbol{x}, \boldsymbol{z}) + H_q(\boldsymbol{z}) \tag{18}
$$

If the joint distributions are matched,  $H_q(z)$  tends to  $H_p(z)$ , which is fixed as long as the prior,  $p(z)$ , is itself fixed. Subsequently, maximizing the mutual information minimizes the expected reconstruction error.

Assuming that the generator is the same as with GANs in the previous section, the objectives for training a bi-directional adversarial model then become:

$$
\arg\max_{D} \mathbb{E}_{q(\boldsymbol{x},\boldsymbol{z})}[\log D(\boldsymbol{x},\boldsymbol{z})] + \mathbb{E}_{p(\boldsymbol{x},\boldsymbol{z})}[\log (1 - D(\boldsymbol{x}, \boldsymbol{z}))]
$$

$$
\arg\max_{F,G} \mathbb{E}_{q(\boldsymbol{x},\boldsymbol{z})}[\log (1 - D(\boldsymbol{x}, \boldsymbol{z}))] + \mathbb{E}_{p(\boldsymbol{x},\boldsymbol{z})}[\log D(\boldsymbol{x}, \boldsymbol{z})] + \beta I_q(\boldsymbol{x}, \boldsymbol{z}). \quad (19)
$$

Related works [Ulyanov et al.](#page-9-0) [\(2017\)](#page-9-0) improves reconstructions quality by forgoing the discriminator and expressing the adversarial game between the encoder and decoder. [Kumar et al.](#page-8-0) [\(2017\)](#page-8-0) augments the bi-directional objective by considering the reconstruction and the corresponding encodings as an additional fake pair. [Belghazi](#page-8-0) [et al.](#page-8-0) [\(2018\)](#page-8-0) shows that a Markovian hierarchical generator in a bi-directional adversarial model provide a hierarchy of

 $7$ We switch to density notations for convenience throughout this section.

Image /page/6/Figure/1 description: The image displays three grids of colorful digits on a black background, labeled (a) Training set, (b) DCGAN, and (c) DCGAN+MINE. Each grid contains 5 rows and 10 columns of digits, with each digit appearing to be composed of red, green, and blue color channels, giving them a slightly blurred or layered appearance. The digits themselves are varied, showing a range of numbers from 0 to 9.

Figure 5. Samples from the Stacked MNIST dataset along with generated samples from DCGAN and DCGAN with MINE. While DCGAN only shows a very limited number of modes, the inclusion of MINE generates a much better representative set of samples.

reconstructions with increasing levels of fidelity (increasing reconstruction quality). [Li et al.](#page-8-0) [\(2017\)](#page-8-0) shows that the expected reconstruction error can be diminished by minimizing the conditional entropy of the observables given the latent representations. The conditional entropy being intractable for general posterior, [Li et al.](#page-8-0) [\(2017\)](#page-8-0) proposes to augment the generator's loss with an adversarial cycle consistency loss [\(Zhu et al.,](#page-9-0) [2017\)](#page-9-0) between the observables and their reconstructions.

Experiment: ALI+MINE In this section we compare MINE to existing bi-directional adversarial models. As the decoder's density is generally intractable, we use three different metrics to measure the fidelity of the reconstructions with respect to the samples;  $(i)$  the euclidean reconstruction error, *(ii) reconstruction accuracy*, which is the proportion of labels preserved by the reconstruction as identified by a pre-trained classifier;  $(iii)$  the Multi-scale structural similarity metric (MS-SSIM, [Wang et al.,](#page-9-0) [2004\)](#page-9-0) between the observables and their reconstructions.

We train MINE on datasets of increasing order of complexity: a toy dataset composed of 25-Gaussians, MNIST [\(Le-](#page-8-0)[Cun,](#page-8-0) [1998\)](#page-8-0), and the CelebA dataset [\(Liu et al.,](#page-9-0) [2015\)](#page-9-0). Fig. [6](#page-7-0) shows the reconstruction ability of MINE compared to ALI. Although ALICE does perfect reconstruction (which is in its explicit formulation), we observe significant mode-dropping in the sample space. MINE does a balanced job of reconstructing along with capturing all the modes of the underlying data distribution.

Next, we measure the fidelity of the reconstructions over ALI, ALICE, and MINE. Tbl. 2 compares MINE to the existing baselines in terms of euclidean reconstruction errors, reconstruction accuracy, and MS-SSIM. On MNIST, MINE outperforms ALI in terms of reconstruction errors by a good margin and is competitive to ALICE with respect to reconstruction accuracy and MS-SSIM. Our results show that MINE's effect on reconstructions is even more dramatic when compared to ALI and ALICE on the CelebA dataset.

## 5.3. Information Bottleneck

The Information Bottleneck (IB, [Tishby et al.,](#page-9-0) [2000\)](#page-9-0) is an information theoretic method for extracting relevant infor-

| Model          | Recons.<br>Error | Recons.<br>Acc.(%) | MS-SSIM |
|----------------|------------------|--------------------|---------|
| <b>MNIST</b>   |                  |                    |         |
| ALI            | 14.24            | 45.95              | 0.97    |
| ALICE(\$l_2\$) | 3.20             | 99.03              | 0.97    |
| ALICE(Adv.)    | 5.20             | 98.17              | 0.98    |
| <b>MINE</b>    | 9.73             | 96.10              | 0.99    |
| CelebA         |                  |                    |         |
| ALI            | 53.75            | 57.49              | 0.81    |
| ALICE(\$l_2\$) | 8.01             | 32.22              | 0.93    |
| ALICE(Adv.)    | 92.56            | 48.95              | 0.51    |
| <b>MINE</b>    | 36.11            | 76.08              | 0.99    |

Table 2. Comparison of MINE with other bi-directional adversarial models in terms of euclidean reconstruction error, reconstruction accuracy, and MS-SSIM on the MNIST and CelebA datasets. MINE does a good job compared to ALI in terms of reconstructions. Though the explicit reconstruction based baselines (ALICE) can sometimes do better than MINE in terms of reconstructions related tasks, they consistently lag behind in MS-SSIM scores and reconstruction accuracy on CelebA.

mation, or yielding a representation, that an input  $X \in \mathcal{X}$ contains about an output  $Y \in \mathcal{Y}$ . An optimal representation of X would capture the relevant factors and compress X by diminishing the irrelevant parts which do not contribute to the prediction of  $Y$ . IB was recently covered in the context of deep learning [\(Tishby & Zaslavsky,](#page-9-0) [2015\)](#page-9-0), and as such can be seen as a process to construct an approximation of the minimally sufficient statistics of the data. IB seeks an encoder,  $q(Z | X)$ , that induces the Markovian structure  $X \to Z \to Y$ . This is done by minimizing the IB Lagrangian,

$$
\mathcal{L}[q(Z \mid X)] = H(Y|Z) + \beta I(X, Z), \quad (20)
$$

which appears as a standard cross-entropy loss augmented with a regularizer promoting minimality of the representation [\(Achille & Soatto,](#page-8-0) [2017\)](#page-8-0). Here we propose to estimate the regularizer with MINE.

Related works In the discrete setting, [\(Tishby et al.,](#page-9-0) [2000\)](#page-9-0) uses the Blahut-Arimoto Algorithm [\(Arimoto,](#page-8-0) [1972\)](#page-8-0), which

<span id="page-7-0"></span>Image /page/7/Figure/1 description: Figure 6 displays four sets of scatter plots, labeled (a) ALI, (b) ALICE (l2), (c) ALICE (A), and (d) ALI+MINE. Each set consists of two rows of plots. The top row shows reconstructions, and the bottom row shows samples from the priors. The plots in the top row generally show a grid-like structure of points, with some variation in the spread and clustering depending on the method. The plots in the bottom row also show a grid-like structure, with ALI+MINE exhibiting the most distinct and well-formed grid. The text indicates that ALI+MINE captures all modes of the data in sample space, while ALICE with adversarial loss has the best reconstruction at the expense of poor sample quality.

ples from adversarially learned inference (ALI) and variations intended to increase improve reconstructions. Shown left to right are the baseline (ALI), ALICE with the  $l_2$  loss to minimize the reconstruction error, ALICE with an adversarial loss, and ALI+MINE. Top to bottom are the reconstructions and samples from the priors. AL-ICE with the adversarial loss has the best reconstruction, though at the expense of poor sample quality, where as ALI+MINE captures all the modes of the data in sample space.

can be understood as cyclical coordinate ascent in function spaces. While IB is successful and popular in a discrete setting, its application to the continuous setting was stifled by the intractability of the continuous mutual information. Nonetheless, IB was applied in the case of jointly Gaussian random variables in [\(Chechik et al.,](#page-8-0) [2005\)](#page-8-0).

In order to overcome the intractability of  $I(X;Z)$  in the continuous setting, [Alemi et al.](#page-8-0) [\(2016\)](#page-8-0); [Kolchinsky](#page-8-0) [et al.](#page-8-0) [\(2017\)](#page-8-0); [Chalk et al.](#page-8-0) [\(2016\)](#page-8-0) exploit the variational bound of [Barber & Agakov](#page-8-0) [\(2003\)](#page-8-0) to approximate the conditional entropy in  $I(X; Z)$ . These approaches differ only on their treatment of the marginal distribution of the bottleneck variable: [Alemi et al.](#page-8-0) [\(2016\)](#page-8-0) assumes a standard multivariate normal marginal distribution, [Chalk](#page-8-0) [et al.](#page-8-0) [\(2016\)](#page-8-0) uses a Student-t distribution, and [Kolchinsky](#page-8-0) [et al.](#page-8-0) [\(2017\)](#page-8-0) uses non-parametric estimators. Due to their reliance on a variational approximation, these methods require a tractable density for the approximate posterior, while MINE does not.

Experiment: Permutation-invariant MNIST classification Here, we demonstrate an implementation of the IB objective on permutation invariant MNIST using MINE. We compare to the Deep Variational Bottleneck (DVB, [Alemi](#page-8-0) [et al.,](#page-8-0) [2016\)](#page-8-0) and use the same empirical setup. As the DVB relies on a variational bound on the conditional entropy, it therefore requires a tractable density. [Alemi et al.](#page-8-0) [\(2016\)](#page-8-0) opts for a conditional Gaussian encoder  $z = \mu(x) + \sigma \odot \epsilon$ , where  $\epsilon \sim \mathcal{N}(0, I)$ . As MINE does not require a tractable density, we consider three type of encoders:  $(i)$  a Gaussian encoder as in [Alemi et al.](#page-8-0) [\(2016\)](#page-8-0); (ii) an *additive noise encoder,*  $\mathbf{z} = enc(\mathbf{x} + \sigma \odot \epsilon)$ ; and *(iii)* a *propagated noise encoder,*  $z = enc([x, \epsilon])$ . Our results can be seen in Tbl. 3, and this shows MINE as being superior in these settings.

# 6. Conclusion

We proposed a mutual information estimator, which we called the mutual information neural estimator (MINE), that is scalable in dimension and sample-size. We demonstrated

| Model                   | Misclass. rate(%) |
|-------------------------|-------------------|
| Baseline                | 1.38%             |
| Dropout                 | 1.34%             |
| Confidence penalty      | 1.36%             |
| Label Smoothing         | 1.40%             |
| DVB                     | 1.13%             |
| DVB + Additive noise    | 1.06%             |
| MINE(Gaussian) (ours)   | 1.11%             |
| MINE(Propagated) (ours) | 1.10%             |
| MINE(Additive) (ours)   | 1.01%             |

Table 3. Permutation Invariant MNIST misclassification rate using [Alemi et al.](#page-8-0) [\(2016\)](#page-8-0) experimental setup for regularization by confidence penalty [\(Pereyra et al.,](#page-9-0) [2017\)](#page-9-0), label smoothing [\(Pereyra](#page-9-0) [et al.,](#page-9-0) [2017\)](#page-9-0), Deep Variational Bottleneck(DVB) [\(Alemi et al.,](#page-8-0) [2016\)](#page-8-0) and MINE. The misclassification rate is averaged over ten runs. In order to control for the regularizing impact of the additive Gaussian noise in the additive conditional, we also report the results for DVB with additional additive Gaussian noise at the input. All non-MINE results are taken from [Alemi et al.](#page-8-0) [\(2016\)](#page-8-0).

the efficiency of this estimator by applying it in a number of settings. First, a term of mutual information can be introduced alleviate mode-dropping issue in generative adversarial networks (GANs, [Goodfellow et al.,](#page-8-0) [2014\)](#page-8-0). Mutual information can also be used to improve inference and reconstructions in adversarially-learned inference (ALI, [Du](#page-8-0)[moulin et al.,](#page-8-0) [2016\)](#page-8-0). Finally, we showed that our estimator allows for tractable application of Information bottleneck methods [\(Tishby et al.,](#page-9-0) [2000\)](#page-9-0) in a continuous setting.

## 7. Acknowledgements

We would like to thank Martin Arjovsky, Caglar Gulcehre, Marcin Moczulski, Negar Rostamzadeh, Thomas Boquet, Ioannis Mitliagkas, Pedro Oliveira Pinheiro for helpful comments, as well as Samsung and IVADO for their support.

# <span id="page-8-0"></span>References

- Achille, A. and Soatto, S. Emergence of invariance and disentanglement in deep representations. *arXiv preprint 1706.01350v2[cs.LG]*, 2017.
- Alemi, A. A., Fischer, I., Dillon, J. V., and Murphy, K. Deep variational information bottleneck. *arXiv preprint arXiv:1612.00410*, 2016.
- Arimoto, S. An algorithm for computing the capacity of arbitrary discrete memoryless channels. *IEEE Transactions on Information Theory*, 18(1):14–20, 1972.

Banerjee, A. On baysian bounds. *ICML*, pp. 81–88, 2006.

- Barber, D. and Agakov, F. The im algorithm: a variational approach to information maximization. In *Proceedings of the 16th International Conference on Neural Information Processing Systems*, pp. 201–208. MIT Press, 2003.
- Belghazi, M. I., Rajeswar, S., Mastropietro, O., Mitrovic, J., Rostamzadeh, N., and Courville, A. Hierarchical adversarially learned inference. *arXiv preprint arXiv:1802.01071*, 2018.
- Butte, A. J. and Kohane, I. S. Mutual information relevance networks: functional genomic clustering using pairwise entropy measurements. In *Pac Symp Biocomput*, volume 5, pp. 26, 2000.
- Chalk, M., Marre, O., and Tkacik, G. Relevant sparse codes with variational information bottleneck. In *Advances in Neural Information Processing Systems*, pp. 1957–1965, 2016.
- Che, T., Li, Y., Jacob, A. P., Bengio, Y., and Li, W. Mode regularized generative adversarial networks. *arXiv preprint arXiv:1612.02136*, 2016.
- Chechik, G., Globerson, A., Tishby, N., and Weiss, Y. Information bottleneck for gaussian variables. *Journal of Machine Learning Research*, 6(Jan):165–188, 2005.
- Chen, X., Duan, Y., Houthooft, R., Schulman, J., Sutskever, I., and Abbeel, P. Infogan: Interpretable representation learning by information maximizing generative adversarial nets. In *Advances in Neural Information Processing Systems*, pp. 2172– 2180, 2016.
- Clevert, D., Unterthiner, T., and Hochreiter, S. Fast and accurate deep network learning by exponential linear units (elus). *CoRR*, abs/1511.07289, 2015.
- Darbellay, G. A. and Vajda, I. Estimation of the information by an adaptive partitioning of the observation space. *IEEE Transactions on Information Theory*, 45(4):1315–1321, 1999.
- Donahue, J., Krähenbühl, P., and Darrell, T. Adversarial feature learning. *arXiv preprint arXiv:1605.09782*, 2016.
- Donsker, M. and Varadhan, S. Asymptotic evaluation of certain markov process expectations for large time, iv. *Communications on Pure and Applied Mathematics*, 36(2):183?212, 1983.
- Dumoulin, V., Belghazi, I., Poole, B., Lamb, A., Arjovsky, M., Mastropietro, O., and Courville, A. Adversarially learned inference. *arXiv preprint arXiv:1606.00704*, 2016.
- Fraser, A. M. and Swinney, H. L. Independent coordinates for strange attractors from mutual information. *Physical review A*, 33(2):1134, 1986.

- Gao, S., Ver Steeg, G., and Galstyan, A. Efficient estimation of mutual information for strongly dependent variables. *Arxiv preprint arXiv:1411.2003[cs.IT]*, 2014.
- Ghosh, A., Kulharia, V., Namboodiri, V., Torr, P. H., and Dokania, P. K. Multi-agent diverse generative adversarial networks. *arXiv preprint arXiv:1704.02906*, 2017.
- Goodfellow, I., Pouget-Abadie, J., Mirza, M., Xu, B., Warde-Farley, D., Ozair, S., Courville, A., and Bengio, Y. Generative adversarial nets. In *Advances in neural information processing systems*, pp. 2672–2680, 2014.
- Györfi, L. and van der Meulen, E. C. Density-free convergence properties of various estimators of entropy. *Computational Statistics and Data Analysis*, 5:425?436, 1987.
- Hornik, K. Multilayer feedforward networks are universal approximators. *Neural Networks*, 2:359–366, 1989.
- Hyvärinen, A., Karhunen, J., and Oja, E. Independent component *analysis*, volume 46. John Wiley & Sons, 2004.
- Ioffe, S. and Szegedy, C. Batch normalization: Accelerating deep network training by reducing internal covariate shift. *CoRR*, abs/1502.03167, 2015. URL [http://arxiv.org/abs/](http://arxiv.org/abs/1502.03167) [1502.03167](http://arxiv.org/abs/1502.03167).
- Kandasamy, K., Krishnamurthy, A., Poczos, B., Wasserman, L., and Robins, J. Nonparametric von mises estimators for entropies, divergences and mutual informations. *NIPS*, 2017.
- Keziou, A. Dual representation of  $\varphi$ -divergences and applications. 336:857–862, 05 2003.
- Kingma, D. P. and Ba, J. Adam: A method for stochastic optimization. *CoRR*, abs/1412.6980, 2014. URL [http:](http://arxiv.org/abs/1412.6980) [//arxiv.org/abs/1412.6980](http://arxiv.org/abs/1412.6980).
- Kinney, J. B. and Atwal, G. S. Equitability, mutual information, and the maximal information coefficient. *Proceedings of the National Academy of Sciences*, 111(9):3354–3359, 2014.
- Kolchinsky, A., Tracey, B. D., and Wolpert, D. H. Nonlinear information bottleneck. *arXiv preprint arXiv:1705.02436*, 2017.
- Kraskov, A., Stögbauer, H., and Grassberger, P. Estimating mutual information. *Physical review E*, 69(6):066138, 2004.
- Kullback, S. *Information theory and statistics*. Courier Corporation, 1997.
- Kumar, A., Sattigeri, P., and Fletcher, P. T. Improved semisupervised learning with gans using manifold invariances. *arXiv preprint arXiv:1705.08850*, 2017.
- Kwak, N. and Choi, C.-H. Input feature selection by mutual information based on parzen window. *IEEE transactions on pattern analysis and machine intelligence*, 24(12):1667–1671, 2002.
- LeCun, Y. The mnist database of handwritten digits. *http://yann. lecun. com/exdb/mnist/*, 1998.
- Li, C., Liu, H., Chen, C., Pu, Y., Chen, L., Henao, R., and Carin, L. Towards understanding adversarial learning for joint distribution matching. *arXiv preprint arXiv:1709.01215*, 2017.

- <span id="page-9-0"></span>Lin, Z., Khetan, A., Fanti, G., and Oh, S. Pacgan: The power of two samples in generative adversarial networks. *arXiv preprint arXiv:1712.04086*, 2017.
- Liu, Z., Luo, P., Wang, X., and Tang, X. Deep learning face attributes in the wild. In *Proceedings of the IEEE International Conference on Computer Vision*, pp. 3730–3738, 2015.
- Maes, F., Collignon, A., Vandermeulen, D., Marchal, G., and Suetens, P. Multimodality image registration by maximization of mutual information. *IEEE transactions on Medical Imaging*, 16(2):187–198, 1997.
- Metz, L., Poole, B., Pfau, D., and Sohl-Dickstein, J. Unrolled generative adversarial networks. 2017. URL [https:](https://openreview.net/pdf?id=BydrOIcle) [//openreview.net/pdf?id=BydrOIcle](https://openreview.net/pdf?id=BydrOIcle).
- Moon, K., Sricharan, K., and Hero III, A. O. Ensemble estimation of mutual information. *arXiv preprint arXiv:1701.08083*, 2017.
- Moon, Y.-I., Rajagopalan, B., and Lall, U. Estimation of mutual information using kernel density estimators. *Physical Review E*, 52(3):2318, 1995.
- Nguyen, T., Le, T., Vu, H., and Phung, D. Dual discriminator generative adversarial nets. In *Advances in Neural Information Processing Systems*, pp. 2667–2677, 2017.
- Nguyen, X., Wainwright, M. J., and Jordan, M. I. Estimating divergence functionals and the likelihood ratio by convex risk minimization. *IEEE Transactions on Information Theory*, 56 (11):5847–5861, 2010.
- Nowozin, S., Cseke, B., and Tomioka, R. f-gan: Training generative neural samplers using variational divergence minimization. In *Advances in Neural Information Processing Systems*, pp. 271–279, 2016.
- Paninski, L. Estimation of entropy and mutual information. *Neural computation*, 15(6):1191–1253, 2003.
- Peng, H., Long, F., and Ding, C. Feature selection based on mutual information criteria of max-dependency, max-relevance, and min-redundancy. *IEEE Transactions on pattern analysis and machine intelligence*, 27(8):1226–1238, 2005.
- Pereyra, G., Tucker, G., Chorowski, J., Kaiser, Ł., and Hinton, G. Regularizing neural networks by penalizing confident output distributions. *ICLR Workshop*, 2017.
- Radford, A., Metz, L., and Chintala, S. Unsupervised representation learning with deep convolutional generative adversarial networks. *arXiv preprint arXiv:1511.06434*, 2015.
- Ruderman, A., Reid, M., García-García, D., and Petterson, J. Tighter variational representations of f-divergences via restriction to probability measures. *arXiv preprint arXiv:1206.4664*, 2012.
- Saatchi, Y. and Wilson, A. G. Bayesian gan. In *Advances in Neural Information Processing Systems*, pp. 3625–3634, 2017.
- Salimans, T., Goodfellow, I. J., Zaremba, W., Cheung, V., Radford, A., and Chen, X. Improved techniques for training gans. *arXiv preprint arXiv:1606.03498*, 2016.
- Shalev-Schwartz, S. and Ben-David, S. *Understanding Machine Learning - from Theory to Algorithms.* Cambridge university press, 2014.

- Singh, S. and Póczos, B. Finite-sample analysis of fixed-k nearest neighbor density functional estimators. *arXiv preprint 1606.01554*, 2016.
- Srivastava, A., Valkov, L., Russell, C., Gutmann, M., and Sutton, C. Veegan: Reducing mode collapse in gans using implicit variational learning. *arXiv preprint arXiv:1705.07761*, 2017.
- Suzuki, T., Sugiyama, M., Sese, J., and Kanamori, T. Approximating mutual information by maximum likelihood density ratio estimation. In *New challenges for feature selection in data mining and knowledge discovery*, pp. 5–20, 2008.
- Tishby, N. and Zaslavsky, N. Deep learning and the information bottleneck principle. In *Information Theory Workshop (ITW), 2015 IEEE*, pp. 1–5. IEEE, 2015.
- Tishby, N., Pereira, F. C., and Bialek, W. The information bottleneck method. *arXiv preprint physics/0004057*, 2000.
- Ulyanov, D., Vedaldi, A., and Lempitsky, V. Adversarial generatorencoder networks. *arXiv preprint arXiv:1704.02304*, 2017.
- Van de Geer, S. *Empirical Processes in M-estimation*. Cambridge University Press, 2000.
- Van Hulle, M. M. Edgeworth approximation of multivariate differential entropy. *Neural computation*, 17(9):1903–1910, 2005.
- Wang, Z., Bovik, A. C., Sheikh, H. R., and Simoncelli, E. P. Image quality assessment: from error visibility to structural similarity. *IEEE transactions on image processing*, 13:600–612, 2004.
- Zhu, J.-Y., Park, T., Isola, P., and Efros, A. A. Unpaired image-toimage translation using cycle-consistent adversarial networks. *arXiv preprint arXiv:1703.10593*, 2017.

# 8. Appendix

In this Appendix, we provide additional experiment details and spell out the proofs omitted in the text.

### 8.1. Experimental Details

### 8.1.1. ADAPTIVE CLIPPING

Here we assume we are in the context of GANs described in Sections [5.1](#page-4-0) and [5.2,](#page-5-0) where the mutual information shows up as a regularizer in the generator objective.

Notice that the generator is updated by two gradients. The first gradient is that of the generator's loss,  $\mathcal{L}_q$  with respect to the generator's parameters  $\theta$ ,  $g_u := \frac{\partial \mathcal{L}_g}{\partial \theta}$ . The second flows from the mutual information estimate to the generator,  $g_m := -\frac{\partial I(\widehat{X};\widehat{Z})}{\partial \theta}$ . If left unchecked, because mutual information is unbounded, the latter can ov generator puts all of its attention on maximizing the mutual information and ignores the adversarial game with the discriminator. We propose to adaptively clip the gradient from the mutual information so that its Frobenius norm is at most that of the gradient from the discriminator. Defining  $g_a$  to be the adapted gradient following from the statistics network to the generator, we have,

$$
g_a = \min(||g_u||, ||g_m||) \frac{g_m}{||g_m||}.
$$
\n(21)

Note that adaptive clipping can be considered in any situation where MINE is to be maximized.

### 8.1.2. GAN+MINE: SPIRAL AND 25-GAUSSIANS

In this section we state the details of experiments supporting mode dropping experiments on the spiral and 25-Gaussians dataset. For both the datasets we use 100,000 examples sampled from the target distributions, using a standard deviation of 0.05 in the case of 25-gaussians, and using additive noise for the spiral. The generator for the GAN consists of two fully connected layers with 500 units in each layer with batch-normalization [\(Ioffe & Szegedy,](#page-8-0) [2015\)](#page-8-0) and Leaky-ReLU as activation function as in [Dumoulin et al.](#page-8-0) [\(2016\)](#page-8-0). The discriminator and statistics networks have three fully connected layers with 400 units each. We use the Adam [\(Kingma & Ba,](#page-8-0) [2014\)](#page-8-0) optimizer with a learning rate of 0.0001. Both GAN baseline and GAN+MINE were trained for 5, 000 iterations with a mini batch-size of 100.

### 8.1.3. GAN+MINE: STACKED-MNIST

Here we describe the experimental setup and architectural details of stacked-MNIST task with GAN+MINE. We compare to the exact same experimental setup followed and reported in PacGAN[\(Lin et al.,](#page-9-0) [2017\)](#page-9-0) and VEEGAN[\(Srivastava et al.,](#page-9-0) [2017\)](#page-9-0). We use a pre-trained classifier to classify generated samples on each of the three stacked channels. Evaluation is done on 26,000 test samples as followed in the baselines. We train GAN+MINE for 50 epochs on 128, 000 samples. Details for generator and discriminator networks are given below in the table4 and tabl[e5.](#page-11-0) Specifically the statistics network has the same architecture as discriminator in DCGAN with ELU [\(Clevert et al.,](#page-8-0) [2015\)](#page-8-0) as activation function for the individual layers and without batch-normalization as highlighted in Table [6.](#page-11-0) In order to condition the statistics network on the  $z$  variable, we use linear MLPs at each layer, whose output are reshaped to the number of feature maps. The linear MLPs output is then added as a dynamic bias.

| <b>Generator</b>                        |                          |                    |               |                            |
|-----------------------------------------|--------------------------|--------------------|---------------|----------------------------|
| <b>Layer</b>                            | <b>Number of outputs</b> | <b>Kernel size</b> | <b>Stride</b> | <b>Activation function</b> |
| Input $z \sim \mathcal{U}(-1, 1)^{100}$ | 100                      |                    |               |                            |
| Fully-connected                         | 2*2*512                  |                    |               | ReLU                       |
| Transposed convolution                  | 4*4*256                  | $5 * 5$            | 2             | ReLU                       |
| Transposed convolution                  | 7*7*128                  | $5 * 5$            | 2             | ReLU                       |
| Transposed convolution                  | 14*14*64                 | $5 * 5$            | 2             | ReLU                       |
| Transposed convolution                  | 28*28*3                  | $5 * 5$            | 2             | Tanh                       |

Table 4. Generator network for Stacked-MNIST experiment using GAN+MINE.

## Mutual Information Neural Estimation

<span id="page-11-0"></span>

| <b>Discriminator</b> |                   |             |        |                     |
|----------------------|-------------------|-------------|--------|---------------------|
| Layer                | Number of outputs | Kernel size | Stride | Activation function |
| Input $x$            | $28 * 28 * 3$     |             |        |                     |
| Convolution          | $14*14*64$        | $5 * 5$     | 2      | ReLU                |
| Convolution          | $7*7*128$         | $5 * 5$     | 2      | ReLU                |
| Convolution          | $4*4*256$         | $5 * 5$     | 2      | ReLU                |
| Convolution          | $2*2*512$         | $5 * 5$     | 2      | ReLU                |
| Fully-connected      | 1                 | 1           | Valid  | Sigmoid             |

Table 5. Discriminator network for Stacked-MNIST experiment.

|                 | <b>Statistics Network</b> |             |        |                     |  |
|-----------------|---------------------------|-------------|--------|---------------------|--|
| Layer           | number of outputs         | kernel size | stride | activation function |  |
| Input x, z      |                           |             |        |                     |  |
| Convolution     | 14*14*16                  | 5 * 5       | 2      | ELU                 |  |
| Convolution     | 7*7*32                    | 5 * 5       | 2      | ELU                 |  |
| Convolution     | 4*4*64                    | 5 * 5       | 2      | ELU                 |  |
| Flatten         | -                         | -           | -      | -                   |  |
| Fully-Connected | 1024                      | 1           | Valid  | None                |  |
| Fully-Connected | 1                         | 1           | Valid  | None                |  |

Table 6. Statistics network for Stacked-MNIST experiment.

## 8.1.4. ALI+MINE: MNIST AND CELEBA

In this section we state the details of experimental setup and the network architectures used for the task of improving reconstructions and representations in bidirectional adversarial models with MINE. The generator and discriminator network architectures along with the hyper parameter setup used in these tasks are similar to the ones used in DCGAN [\(Radford et al.,](#page-9-0) [2015\)](#page-9-0).

Statistics network conditioning on the latent code was done as in the Stacked-MNIST experiments. We used Adam as the optimizer with a learning rate of 0.0001. We trained the model for a total of 35, 000 iterations on CelebA and 50, 000 iterations on MNIST, both with a mini batch-size of 100.

| <b>Encoder</b>       |                   |             |        |                     |
|----------------------|-------------------|-------------|--------|---------------------|
| Layer                | Number of outputs | Kernel size | Stride | Activation function |
| Input $[x,\epsilon]$ | 28*28*129         |             |        |                     |
| Convolution          | 14*14*64          | 5 * 5       | 2      | ReLU                |
| Convolution          | 7*7*128           | 5 * 5       | 2      | ReLU                |
| Convolution          | 4*4*256           | 5 * 5       | 2      | ReLU                |
| Convolution          | 256               | 4 * 4       | Valid  | ReLU                |
| Fully-connected      | 128               |             |        | None                |

Table 7. Encoder network for bi-directional models on MNIST.  $\epsilon \sim \mathcal{N}_{128}(0, I)$ .

| <b>Decoder</b>         |                   |             |        |                            |
|------------------------|-------------------|-------------|--------|----------------------------|
| Layer                  | Number of outputs | Kernel size | Stride | <b>Activation function</b> |
| Input z                | 128               |             |        |                            |
| Fully-connected        | 4*4*256           |             |        | ReLU                       |
| Transposed convolution | 7*7*128           | 5 * 5       | 2      | ReLU                       |
| Transposed convolution | 14*14*64          | 5*5         | 2      | ReLU                       |
| Transposed convolution | 28*28*1           | 5 * 5       | 2      | Tanh                       |

Table 8. Decoder network for bi-directional models on MNIST.  $z \sim \mathcal{N}_{256}(0, I)$ 

Mutual Information Neural Estimation

| <b>Discriminator</b> |                   |             |        |                     |
|----------------------|-------------------|-------------|--------|---------------------|
| Layer                | Number of outputs | Kernel size | Stride | Activation function |
| Input $x$            | $28 * 28 * 3$     |             |        |                     |
| Convolution          | $14*14*64$        | $5 * 5$     | 2      | LeakyReLU           |
| Convolution          | $7*7*128$         | $5 * 5$     | 2      | LeakyReLU           |
| Convolution          | $4*4*256$         | $5 * 5$     | 2      | LeakyReLU           |
| Flatten              | -                 | -           | -      |                     |
| Concatenate $z$      | -                 | -           | -      |                     |
| Fully-connected      | 1024              | -           | -      | LeakyReLU           |
| Fully-connected      | 1                 | -           | -      | Sigmoid             |

Table 9. Discriminator network for bi-directional models experiments MINE on MNIST.

| <b>Statistics Network</b> |                   |             |        |                     |
|---------------------------|-------------------|-------------|--------|---------------------|
| Layer                     | number of outputs | kernel size | stride | activation function |
| Input $x, z$              |                   |             |        |                     |
| Convolution               | 14*14*64          | 5 * 5       | 2      | LeakyReLU           |
| Convolution               | 7*7*128           | 5 * 5       | 2      | LeakyReLU           |
| Convolution               | 4*4*256           | 5 * 5       | 2      | LeakyReLU           |
| Flatten                   | -                 | -           | -      | -                   |
| Fully-connected           | 1                 | -           | -      | None                |

Table 10. Statistics network for bi-directional models using MINE on MNIST.

| <b>Encoder</b>       |                   |             |        |                     |
|----------------------|-------------------|-------------|--------|---------------------|
| Layer                | Number of outputs | Kernel size | Stride | Activation function |
| Input $[x,\epsilon]$ | 64*64*259         | 5 * 5       | 2      | ReLU                |
| Convolution          | 32*32*64          | 5 * 5       | 2      | ReLU                |
| Convolution          | 16*16*128         | 5 * 5       | 2      | ReLU                |
| Convolution          | 8*8*256           | 5 * 5       | 2      | ReLU                |
| Convolution          | 4*4*512           | 5 * 5       | 2      | ReLU                |
| Convolution          | 512               | 4*4         | Valid  | ReLU                |
| Fully-connected      | 256               | -           | -      | None                |

Table 11. Encoder network for bi-directional models on CelebA.  $\pmb{\epsilon} \sim \mathcal{N}_{256}(0,I).$ 

| <b>Decoder</b>                                     |                   |             |        |                     |
|----------------------------------------------------|-------------------|-------------|--------|---------------------|
| Layer                                              | Number of outputs | Kernel size | Stride | Activation function |
| Input $\boldsymbol{z} \sim \mathcal{N}_{256}(0,I)$ | 256               |             |        |                     |
| Fully-Connected                                    | 4*4*512           |             |        | ReLU                |
| Transposed convolution                             | $8*8*256$         | $5 * 5$     |        | ReLU                |
| Transposed convolution                             | 16*16*128         | $5 * 5$     | 2      | ReLU                |
| Transposed convolution                             | 32*32*64          | $5 * 5$     |        | ReLU                |
| Transposed convolution                             | $64*64*3$         | $5 * 5$     |        | Tanh                |

Table 12. Decoder network for bi-directional model(ALI, ALICE) experiments using MINE on CelebA.

Mutual Information Neural Estimation

| Layer           | Number of outputs | Kernel size | Stride | Activation function |
|-----------------|-------------------|-------------|--------|---------------------|
| Input $x$       | $64 * 64 * 3$     | -           | -      | -                   |
| Convolution     | 32*32*64          | $5 * 5$     | 2      | LeakyReLU           |
| Convolution     | 16*16*128         | $5 * 5$     | 2      | LeakyReLU           |
| Convolution     | 8*8*256           | $5 * 5$     | 2      | LeakyReLU           |
| Convolution     | 4*4*512           | $5 * 5$     | 2      | LeakyReLU           |
| Flatten         | -                 | -           | -      | -                   |
| Concatenate z   | -                 | -           | -      | -                   |
| Fully-connected | 1024              | -           | -      | LeakyReLU           |
| Fully-connected | 1                 | -           | -      | Sigmoid             |

Table 13. Discriminator network for bi-directional models on CelebA.

| <b>Statistics Network</b> |                   |             |        |                     |
|---------------------------|-------------------|-------------|--------|---------------------|
| Layer                     | number of outputs | kernel size | stride | activation function |
| Input $x, z$              |                   |             |        |                     |
| Convolution               | $32*32*16$        | $5 * 5$     | 2      | ELU                 |
| Convolution               | $16*16*32$        | $5 * 5$     | 2      | ELU                 |
| Convolution               | $8*8*64$          | $5 * 5$     | 2      | ELU                 |
| Convolution               | $4*4*128$         | $5 * 5$     | 2      | ELU                 |
| Flatten                   | -                 | -           | -      | -                   |
| Fully-connected           | 1                 | -           | -      | None                |

Table 14. Statistics network for bi-directional models on CelebA.

## 8.1.5. INFORMATION BOTTLENECK WITH MINE

J.

In this section we outline the network details and hyper-parameters used for the information bottleneck task using MINE. To keep comparison fair all hyperparameters and architectures are those outlined in [Alemi et al.](#page-8-0) [\(2016\)](#page-8-0). The statistics network is shown, a two layer MLP with additive noise at each layer and 512 ELUs [\(Clevert et al.,](#page-8-0) [2015\)](#page-8-0) activations, is outlined in table15.

| <b>Statistics Network</b> |                          |                            |
|---------------------------|--------------------------|----------------------------|
| <b>Layer</b>              | <b>number of outputs</b> | <b>activation function</b> |
| input $[x, z]$            | -                        | -                          |
| Gaussian noise(std=0.3)   | -                        | -                          |
| dense layer               | 512                      | ELU                        |
| Gaussian noise(std=0.5)   | -                        | -                          |
| dense layer               | 512                      | ELU                        |
| Gaussian noise(std=0.5)   | -                        | -                          |
| dense layer               | 1                        | None                       |

Table 15. Statistics network for Information-bottleneck experiments on MNIST.

## 8.2. Proofs

## 8.2.1. DONSKER-VARADHAN REPRESENTATION

Theorem 4 (Theorem [1](#page-1-0) restated). *The KL divergence admits the following dual representation:*

$$
D_{KL}(\mathbb{P} \mid \mid \mathbb{Q}) = \sup_{T:\Omega \to \mathbb{R}} \mathbb{E}_{\mathbb{P}}[T] - \log(\mathbb{E}_{\mathbb{Q}}[e^T]),\tag{22}
$$

*where the supremum is taken over all functions* T *such that the two expectations are finite.*

<span id="page-14-0"></span>*Proof.* A simple proof goes as follows. For a given function T, consider the Gibbs distribution G defined by  $dG = \frac{1}{Z}e^{T}dQ$ , where  $Z = \mathbb{E}_{\mathbb{Q}}[e^T]$ . By construction,

$$
\mathbb{E}_{\mathbb{P}}[T] - \log Z = \mathbb{E}_{\mathbb{P}}\left[\log \frac{d\mathbb{G}}{d\mathbb{Q}}\right]
$$
\n(23)

Let  $\Delta$  be the gap,

$$
\Delta := D_{KL}(\mathbb{P} \mid \mid \mathbb{Q}) - \left( \mathbb{E}_{\mathbb{P}}[T] - \log(\mathbb{E}_{\mathbb{Q}}[e^T]) \right)
$$
\n(24)

Using Eqn 23, we can write  $\Delta$  as a KL-divergence:

$$
\Delta = \mathbb{E}_{\mathbb{P}} \left[ \log \frac{d\mathbb{P}}{d\mathbb{Q}} - \log \frac{d\mathbb{G}}{d\mathbb{Q}} \right] = \mathbb{E}_{\mathbb{P}} \log \frac{d\mathbb{P}}{d\mathbb{G}} = D_{KL}(\mathbb{P} \mid \mid \mathbb{G}) \tag{25}
$$

The positivity of the KL-divergence gives  $\Delta \geq 0$ . We have thus shown that for any T,

$$
D_{KL}(\mathbb{P} \mid \mid \mathbb{Q}) \ge \mathbb{E}_{\mathbb{P}}[T] - \log(\mathbb{E}_{\mathbb{Q}}[e^T]) \tag{26}
$$

and the inequality is preserved upon taking the supremum over the right-hand side. Finally, the identity (25) also shows that this bound is *tight* whenever  $\mathbb{G} = \mathbb{P}$ , namely for optimal functions  $T^*$  taking the form  $T^* = \log \frac{d\mathbb{P}}{d\mathbb{Q}} + C$  for some constant  $C \in \mathbb{R}$ .  $\Box$ 

## 8.2.2. CONSISTENCY PROOFS

This section presents the proofs of the Lemma and consistency theorem stated in the consistency in Section [3.3.1.](#page-2-0)

In what follows, we assume that the input space  $\Omega = \mathcal{X} \times \mathcal{Z}$  is a compact domain of  $\mathbb{R}^d$ , and all measures are absolutely continuous with respect to the Lebesgue measure. We will restrict to families of feedforward functions with continuous activations, with a single output neuron, so that a given architecture defines a continuous mapping  $(\omega, \theta) \to T_{\theta}(\omega)$  from  $\Omega \times \Theta$  to  $\mathbb{R}$ .

To avoid unnecessary heavy notation, we denote  $\mathbb{P} = \mathbb{P}_{XZ}$  and  $\mathbb{Q} = \mathbb{P}_X \otimes \mathbb{P}_Z$  for the joint distribution and product of marginals, and  $\mathbb{P}_n, \mathbb{Q}_n$  for their empirical versions. We will use the notation  $\hat{I}(T)$  for the quantity:

$$
\hat{I}(T) = \mathbb{E}_{\mathbb{P}}[T] - \log(\mathbb{E}_{\mathbb{Q}}[e^T])
$$
\n(27)

so that  $I_{\Theta}(X, Z) = \sup_{\theta \in \Theta} \hat{I}(T_{\theta}).$ 

**Lemma 3** (Lemma [1](#page-3-0) restated). Let  $\eta > 0$ . There exists a family of neural network functions  $T_{\theta}$  with parameters  $\theta$  in some compact  $domain \,\Theta \subset \mathbb{R}^k$ , such that

$$
|I(X,Z) - I_{\Theta}(X,Z)| \le \eta \tag{28}
$$

*where*

$$
I_{\Theta}(X,Z) = \sup_{\theta \in \Theta} \mathbb{E}_{\mathbb{P}_{XZ}}[T_{\theta}] - \log(\mathbb{E}_{\mathbb{P}_{X} \otimes \mathbb{P}_{Z}}[e^{T_{\theta}}])
$$
\n(29)

*Proof.* Let  $T^* = \log \frac{dP}{dQ}$ . By construction,  $T^*$  satisfies:

$$
\mathbb{E}_{\mathbb{P}}[T^*] = I(X, Z), \qquad \mathbb{E}_{\mathbb{Q}}[e^{T^*}] = 1 \tag{30}
$$

For a function T, the (positive) gap  $I(X, Z) - \hat{I}(T)$  can be written as

$$
I(X, Z) - \hat{I}(T) = \mathbb{E}_{\mathbb{P}}[T^* - T] + \log \mathbb{E}_{\mathbb{Q}}[e^T] \le \mathbb{E}_{\mathbb{P}}[T^* - T] + \mathbb{E}_{\mathbb{Q}}[e^T - e^{T^*}]
$$
\n(31)

where we used the inequality  $\log x \leq x - 1$ .

Fix  $\eta > 0$ . We first consider the case where  $T^*$  is *bounded* from above by a constant M. By the universal approximation theorem (see corollary 2.2 of [Hornik](#page-8-0) [\(1989\)](#page-8-0)<sup>8</sup>), we may choose a feedforward network function  $T_{\hat{\theta}} \leq M$  such that

$$
\mathbb{E}_{\mathbb{P}}|T^*-T_{\hat{\theta}}| \leq \frac{\eta}{2} \quad \text{and} \quad \mathbb{E}_{\mathbb{Q}}|T^*-T_{\hat{\theta}}| \leq \frac{\eta}{2}e^{-M}
$$
\n(32)

Since exp is Lipschitz continuous with constant  $e^M$  on  $(-\infty, M]$ , we have

$$
\mathbb{E}_{\mathbb{Q}}|e^{T^*} - e^{T_{\hat{\theta}}}| \le e^M \mathbb{E}_{\mathbb{Q}}|T^* - T_{\hat{\theta}}| \le \frac{\eta}{2}
$$
\n(33)

<sup>&</sup>lt;sup>8</sup>Specifically, the argument relies on the density of feedforward network functions in the space  $L^1(\Omega,\mu)$  of integrable functions with respect the measure  $\mu = \mathbb{P} + \mathbb{Q}$ .

<span id="page-15-0"></span>From Equ [31](#page-14-0) and the triangular inequality, we then obtain:

$$
|I(X,Z) - \hat{I}(T_{\hat{\theta}})| \leq \mathbb{E}_{\mathbb{P}}|T^* - T_{\hat{\theta}}| + \mathbb{E}_{\mathbb{Q}}|e^{T^*} - e^{T_{\hat{\theta}}}| \leq \frac{\eta}{2} + \frac{\eta}{2} \leq \eta
$$
\n(34)

In the general case, the idea is to partition  $\Omega$  in two subset  $\{T^* > M\}$  and  $\{T^* \leq M\}$  for a suitably chosen large value of M. For a given subset  $S \subset \Omega$ , we will denote by  $\mathbb{1}_S$  its characteristic function,  $\mathbb{1}_S(\omega) = 1$  if  $\omega \in S$  and 0 otherwise.  $T^*$  is integrable with respect to  $\mathbb{P}^9$ , and  $e^{T^*}$  is integrable with respect to Q, so by the dominated convergence theorem, we may choose M so that the expectations  $\mathbb{E}_{\mathbb{P}}[T^*\mathbb{1}_{T^*>M}]$  and  $\mathbb{E}_{\mathbb{Q}}[e^{T^*}\mathbb{1}_{T^*>M}]$  are lower than  $\eta/4$ . Just like above, we then use the universal approximation theorem to find a feed forward network function  $T_{\hat{\theta}}$ , which we can assume without loss of generality to be upper-bounded by M, such that

$$
\mathbb{E}_{\mathbb{P}}|T^*-T_{\hat{\theta}}| \leq \frac{\eta}{2} \quad \text{and} \quad \mathbb{E}_{\mathbb{Q}}|T^*-T_{\hat{\theta}}|\mathbb{1}_{T^*\leq M} \leq \frac{\eta}{4}e^{-M}
$$
\n
$$
(35)
$$

We then write

$$
\mathbb{E}_{\mathbb{Q}}[e^{T^*} - e^{T_{\hat{\theta}}}] = \mathbb{E}_{\mathbb{Q}}[(e^{T^*} - e^{T_{\hat{\theta}}})\mathbb{1}_{T^* \leq M}] + \mathbb{E}_{\mathbb{Q}}[(e^{T^*} - e^{T_{\hat{\theta}}})\mathbb{1}_{T^* > M}]
$$
  

$$
\leq e^M \mathbb{E}_{\mathbb{Q}}[|T^* - T_{\hat{\theta}}|\mathbb{1}_{T^* \leq M}] + \mathbb{E}_{\mathbb{Q}}[e^{T^*} \mathbb{1}_{T^* > M}]
$$
  

$$
\leq \frac{\eta}{4} + \frac{\eta}{4}
$$
 (36)  

$$
\leq \frac{\eta}{2}
$$
 (37)

$$
\leq \frac{7}{2} \tag{37}
$$

where the inequality in the second line arises from the convexity and positivity of exp. Eqns. 35 and 36, together with the triangular inequality, lead to Eqn. 34, which proves the Lemma.

 $\Box$ 

**Lemma 4** (Lemma [2](#page-3-0) restated). Let  $\eta > 0$ . Given a family F of neural network functions  $T_{\theta}$  with parameters  $\theta$  in some compact domain  $\Theta \subset \mathbb{R}^k$ , there exists  $N \in \mathbb{N}$  such that

$$
\forall n \ge N, \quad \Pr\left(|\widehat{I(X;Z)}_n - I_{\mathcal{F}}(X,Z)| \le \eta\right) = 1\tag{38}
$$

*Proof.* We start by using the triangular inequality to write,

$$
|\widehat{I(X;Z)}_n - \sup_{T_\theta \in \mathcal{F}} \widehat{I}(T_\theta)| \le \sup_{T_\theta \in \mathcal{F}} |\mathbb{E}_{\mathbb{P}}[T_\theta] - \mathbb{E}_{\mathbb{P}_n}[T_\theta]| + \sup_{T_\theta \in \mathcal{F}} |\log \mathbb{E}_{\mathbb{Q}}[e^{T_\theta}] - \log \mathbb{E}_{\mathbb{Q}_n}[e^{T_\theta}]|
$$
(39)

The continuous function  $(\theta, \omega) \to T_{\theta}(\omega)$ , defined on the compact domain  $\Theta \times \Omega$ , is bounded. So the functions  $T_{\theta}$  are uniformly bounded by a constant M, i.e  $|T_\theta| \le M$  for all  $\theta \in \Theta$ . Since log is Lipschitz continuous with constant  $e^M$  in the interval  $[e^{-M}, e^M]$ , we have

$$
|\log \mathbb{E}_{\mathbb{Q}}[e^{T_{\theta}}] - \log \mathbb{E}_{\mathbb{Q}_n}[e^{T_{\theta}}]| \leq e^{M} |\mathbb{E}_{\mathbb{Q}}[e^{T_{\theta}}] - \mathbb{E}_{\mathbb{Q}_n}[e^{T_{\theta}}]|
$$
\n(40)

Since  $\Theta$  is compact and the feedforward network functions are continuous, the families of functions  $T_{\theta}$  and  $e^{T_{\theta}}$  satisfy the uniform law of large numbers [\(Van de Geer,](#page-9-0) [2000\)](#page-9-0). Given  $\eta > 0$  we can thus choose  $N \in \mathbb{N}$  such that  $\forall n \ge N$  and with probability one,

$$
\sup_{T_{\theta} \in \mathcal{F}} |\mathbb{E}_{\mathbb{P}}[T_{\theta}] - \mathbb{E}_{\mathbb{P}_n}[T_{\theta}] \le \frac{\eta}{2} \quad \text{and} \quad \sup_{T_{\theta} \in \mathcal{F}} |\mathbb{E}_{\mathbb{Q}}[e^{T_{\theta}}] - \mathbb{E}_{\mathbb{Q}_n}[e^{T_{\theta}}]| \le \frac{\eta}{2} e^{-M} \tag{41}
$$

Together with Eqns. 39 and 40, this leads to

$$
|\widehat{I(X;Z)}_n - \sup_{T_{\theta} \in \mathcal{F}} \widehat{I}(T_{\theta})| \le \frac{\eta}{2} + \frac{\eta}{2} = \eta
$$
\n<sup>(42)</sup>

 $\Box$ 

 $\Box$ 

### Theorem 5 (Theorem [2](#page-3-0) restated). *MINE is strongly consistent.*

*Proof.* Let  $\epsilon > 0$ . We apply the two Lemmas to find a a family of neural network function  $\mathcal F$  and  $N \in \mathbb N$  such that [\(28\)](#page-14-0) and (38) hold with  $\eta = \epsilon/2$ . By the triangular inequality, for all  $n \geq N$  and with probability one, we have:

$$
|I(X,Z) - I(\widehat{X};Z)_n| \leq |I(X,Z) - \sup_{T_\theta \in \mathcal{F}} \widehat{I}(T_\theta)| + |\widehat{I(X;Z)}_n - I_{\mathcal{F}}(X,Z)| \leq \epsilon
$$
\n
$$
(43)
$$

which proves consistency.

<sup>9</sup>This can be seen from the identity (Györfi & van der Meulen, [1987\)](#page-8-0)

$$
\mathbb{E}_{\mathbb{P}}\left|\log\frac{d\mathbb{P}}{d\mathbb{Q}}\right|\leq D_{KL}(\mathbb{P}\mid\mid\mathbb{Q})+4\sqrt{D_{KL}(\mathbb{P}\mid\mid\mathbb{Q})}
$$

### 8.2.3. SAMPLE COMPLEXITY PROOF

**Theorem 6** (Theorem [3](#page-3-0) restated). Assume that the functions  $T_{\theta}$  in  $\mathcal F$  are L-Lipschitz with respect to the parameters  $\theta$ ; and that both  $T_\theta$  and  $e^{T_\theta}$  are M-bounded (i.e.,  $|T_\theta|, e^{T_\theta} \leq M$ ). The domain  $\Theta \subset \mathbb{R}^d$  is bounded, so that  $\|\theta\| \leq K$  for some constant K. Given any *values* , δ *of the desired accuracy and confidence parameters, we have,*

$$
\Pr\left(|\widehat{I(X;Z)}_n - I_{\mathcal{F}}(X,Z)| \le \epsilon\right) \ge 1 - \delta\tag{44}
$$

*whenever the number* n *of samples satisfies*

$$
n \ge \frac{2M^2(d\log(16KL\sqrt{d}/\epsilon) + 2dM + \log(2/\delta))}{\epsilon^2} \tag{45}
$$

*Proof.* The assumptions of Lemma [2](#page-3-0) apply, so let us begin with Eqns. [39](#page-15-0) and [40.](#page-15-0) By the Hoeffding inequality, for all function f,

$$
\Pr\left(|\mathbb{E}_{\mathbb{Q}}[f] - \mathbb{E}_{\mathbb{Q}_n}[f]| > \frac{\epsilon}{4}\right) \le 2\exp\left(-\frac{\epsilon^2 n}{2M^2}\right) \tag{46}
$$

To extend this inequality to a uniform inequality over *all* functions  $T_{\theta}$  and  $e^{T_{\theta}}$ , the standard technique is to choose a minimal cover of the domain  $\Theta \subset \mathbb{R}^d$  by a finite set of small balls of radius  $\eta$ ,  $\Theta \subset \cup_j B_\eta(\theta_j)$ , and to use the union bound. The minimal cardinality of such covering is bounded by the *covering number* Nη(Θ) of Θ, known to satisfy[\(Shalev-Schwartz & Ben-David,](#page-9-0) [2014\)](#page-9-0)

$$
N_{\eta}(\Theta) \le \left(\frac{2K\sqrt{d}}{\eta}\right)^d \tag{47}
$$

Successively applying a union bound in Eqn 46 with the set of functions  $\{T_{\theta_j}\}_j$  and  $\{e^{T_{\theta_j}}\}_j$  gives

$$
\Pr\left(\max_{j} |\mathbb{E}_{\mathbb{Q}}[T_{\theta_j}] - \mathbb{E}_{\mathbb{Q}_n}[T_{\theta_j}]| > \frac{\epsilon}{4}\right) \le 2N_{\eta}(\Theta) \exp\left(-\frac{\epsilon^2 n}{2M^2}\right)
$$
\n(48)

and

$$
\Pr\left(\max_{j} |\mathbb{E}_{\mathbb{Q}}[e^{T_{\theta_{j}}}] - \mathbb{E}_{\mathbb{Q}_{n}}[e^{T_{\theta_{j}}}]| > \frac{\epsilon}{4}\right) \le 2N_{\eta}(\Theta) \exp\left(-\frac{\epsilon^{2}n}{2M^{2}}\right)
$$
\n(49)

We now choose the ball radius to be  $\eta = \frac{\epsilon}{8L} e^{-2M}$ . Solving for *n* the inequation,

$$
2N_{\eta}(\Theta) \exp\left(-\frac{\epsilon^2 n}{2M^2}\right) \le \delta \tag{50}
$$

we deduce from Eqn 48 that, whenever Eqn 45 holds, with probability at least  $1 - \delta$ , for all  $\theta \in \Theta$ ,

$$
|\mathbb{E}_{\mathbb{Q}}[T_{\theta}] - \mathbb{E}_{\mathbb{Q}_n}[T_{\theta}]| \leq |\mathbb{E}_{\mathbb{Q}}[T_{\theta}] - \mathbb{E}_{\mathbb{Q}}[T_{\theta_j}]| + |\mathbb{E}_{\mathbb{Q}}[T_{\theta_j}] - \mathbb{E}_{\mathbb{Q}_n}[T_{\theta_j}]| + |\mathbb{E}_{\mathbb{Q}_n}[T_{\theta_j}] - \mathbb{E}_{\mathbb{Q}_n}[T_{\theta}]|
$$
  
$$
\leq \frac{\epsilon}{8} e^{-2M} + \frac{\epsilon}{4} + \frac{\epsilon}{8} e^{-2M}
$$
  
$$
\leq \frac{\epsilon}{2}
$$
(51)

Similarly, using Eqn [40](#page-15-0) and 49, we obtain that with probability at least  $1 - \delta$ ,

$$
|\log \mathbb{E}_{\mathbb{Q}}[e^{T_{\theta}}] - \log \mathbb{E}_{\mathbb{Q}_n}[e^{T_{\theta}}]| \le \frac{\epsilon}{2}
$$
\n(52)

and hence using the triangular inequality,

$$
|\widehat{I(X;Z)}_n - I_{\mathcal{F}}(X,Z)| \le \epsilon \tag{53}
$$

 $\Box$ 

### 8.2.4. BOUND ON THE RECONSTRUCTION ERROR

Here we clarify relationship between reconstruction error and mutual information, by proving the bound in Equ [18.](#page-5-0) We begin with a definition:

**Definition 8.1** (Reconstruction Error). We consider encoder and decoder models giving conditional distributions  $q(z|x)$  and  $p(x|z)$  over the data and latent variables. If  $q(x)$  denotes the marginal data distribution, the reconstruction error is defined as

$$
\mathcal{R} = \mathbb{E}_{\mathbf{x} \sim q(\mathbf{x})} \mathbb{E}_{\mathbf{z} \sim q(\mathbf{z}|\mathbf{x})} [-\log p(\mathbf{x}|\mathbf{z})] \tag{54}
$$

Mutual Information Neural Estimation

Image /page/17/Figure/1 description: The image displays four scatter plots, labeled (a) ALI, (b) ALICE (L2), (c) ALICE (A), and (d) MINE. Each plot shows points colored in a gradient from blue to red, arranged in distinct lines or clusters. Plot (a) shows points scattered in a fan-like pattern. Plot (b) shows a similar fan-like pattern but with a denser concentration of points along a central horizontal line. Plot (c) displays points arranged in parallel diagonal lines. Plot (d) shows points arranged in radial lines emanating from a central point, forming a star-like pattern.

Figure 7. Embeddings from adversarially learned inference (ALI) and variations intended to increase the mutual information. Shown left to right are the baseline (ALI), ALICE with the L2 loss to minimize the reconstruction error, ALI with an additional adversarial loss, and MINE.

We can rewrite the reconstruction error in terms of the joints  $q(x, z) = q(z|x)p(x)$  and  $p(x, z) = p(x|z)p(z)$ . Elementary manipulations give:

$$
\mathcal{R} = \mathbb{E}_{(\boldsymbol{x}, \boldsymbol{z}) \sim q(\boldsymbol{x}, \boldsymbol{z})} \log \frac{q(\boldsymbol{x}, \boldsymbol{z})}{p(\boldsymbol{x}, \boldsymbol{z})} - \mathbb{E}_{(\boldsymbol{x}, \boldsymbol{z}) \sim q(\boldsymbol{x}, \boldsymbol{z})} \log q(\boldsymbol{x}, \boldsymbol{z}) + \mathbb{E}_{\boldsymbol{z} \sim q(\boldsymbol{z})} \log p(\boldsymbol{z})
$$
(55)

where  $q(z)$  is the aggregated posterior. The first term is the KL-divergence  $D_{KL}(q || p)$ ; the second term is the joint entropy  $H_q(x, z)$ . The third term can be written as

$$
\mathbb{E}_{\boldsymbol{z}\sim q(\boldsymbol{z})}\log p(\boldsymbol{z}) = -D_{KL}(q(\boldsymbol{z})\mid p(\boldsymbol{z})) - H_q(\boldsymbol{z})
$$

Finally, the identity

$$
H_q(\bm{x}, \bm{z}) - H_q(\bm{z}) := H_q(\bm{z}|\bm{x}) = H_q(\bm{z}) - I_q(\bm{x}, \bm{z})
$$
\n(56)

yields the following expression for the reconstruction error:

$$
\mathcal{R} = D_{KL}(q(\boldsymbol{x}, \boldsymbol{z}) \mid p(\boldsymbol{x}, \boldsymbol{z})) - D_{KL}(q(\boldsymbol{z}) \mid p(\boldsymbol{z})) - I_q(\boldsymbol{x}, \boldsymbol{z}) + H_q(\boldsymbol{z}) \tag{57}
$$

Since the KL-divergence is positive, we obtain the bound:

$$
\mathcal{R} \leq D_{KL}(q(\boldsymbol{x}, \boldsymbol{z}) \mid p(\boldsymbol{x}, \boldsymbol{z})) - I_q(\boldsymbol{x}, \boldsymbol{z}) + H_q(\boldsymbol{z}) \tag{58}
$$

which is tight whenever the induced marginal  $q(z)$  matches the prior distribution  $p(z)$ .

## 8.3. Embeddings for bi-direction 25 Gaussians experiments

Here (Fig. 7) we present the embeddings for the experiments corresponding to Fig. [6.](#page-7-0)
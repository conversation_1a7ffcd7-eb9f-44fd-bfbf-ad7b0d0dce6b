# Fair Graph Distillation

Qizhang Feng<sup>1</sup>, <PERSON><PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><sup>1</sup>, <PERSON><sup>1</sup>, <PERSON><sup>3</sup>, <PERSON><PERSON><sup>4</sup> <sup>1</sup>Texas A&M University, <sup>2</sup>University of Science and Technology of China, <sup>3</sup>University of Florida, <sup>3</sup>Rice University

## Abstract

As graph neural networks (GNNs) struggle with large-scale graphs due to high computational demands, graph data distillation promises to alleviate this issue by distilling a large real graph into a smaller distilled graph while maintaining comparable prediction performance for GNNs trained on both graphs. However, we observe that GNNs trained on distilled graphs may exhibit more severe group fairness issues than GNNs trained on real graphs for vanilla and fair GNNs training. Motivated by these observations, we propose *fair graph distillation* (FGD), an advanced graph distillation approach to generate fair distilled graphs. The challenge lies in the deficiency of sensitive attributes for nodes in the distilled graph, making most debiasing methods (e.g., regularization and adversarial debiasing) intractable for distilled graphs. We develop a simple yet effective bias metric, named coherence, for distilled graphs. Based on the proposed coherence metric, we introduce a framework for fair graph distillation using a bi-level optimization algorithm. Extensive experiments demonstrate that the proposed algorithm can achieve better prediction performance-fairness trade-offs across various datasets and GNN architectures.

## 1 Introduction

Real-world data, like chemical molecules, social networks, and transportation networks, can be represented as graphs [\[Han et al., 2022a,](#page-9-0) [Ling et al., 2023a,](#page-10-0) [Jiang et al., 2022a,](#page-9-1) [Ying et al., 2018,](#page-11-0) [Ling et al., 2023b,](#page-10-1) [Tong et al., 2020,](#page-11-1) [Han et al., 2022b\]](#page-9-2). Graph neural networks (GNNs) excel at capturing structural information but struggle with large-scale graphs due to memory consumption and computational expense caused by the neighborhood explosion problem[\[Hamilton et al., 2017,](#page-9-3) [Liu et al., 2023c\]](#page-10-2). This cost becomes unaffordable in situations requiring repeated GNN training, such as neural architecture search and continual learning [\[Liu et al., 2018,](#page-10-3) [Zhou et al., 2019,](#page-11-2) [Li and](#page-10-4) [Hoiem, 2017,](#page-10-4) [Liu et al., 2023b\]](#page-10-5). Dataset distillation is a promising solution to address computation challenges by generating small, informative distilled data for neural network training in downstream tasks [\[Jin et al., 2021,](#page-10-6) [2022,](#page-10-7) [Zhao et al., 2021a,](#page-11-3)[b,](#page-11-4) [Nguyen et al., 2021\]](#page-10-8). Techniques like dataset condensation [\[Zhao et al., 2021b,](#page-11-4) [Jin et al., 2021\]](#page-10-6) can significantly reduce training data size without major performance degradation in the image and graph domains. However, focusing solely on prediction performance may introduce fairness issues, as sensitive information can be condensed into distilled data for prediction. A natural question is raised: *Is the model trained on the distilled graph fair, and if not, how can we achieve fair graph distillation?*

In this work, we focus on the group fairness<sup>[1](#page-0-0)</sup> for node classification tasks under binary sensitive attribute setting. We discover that GNNs trained on distilled small graphs exhibit more severe group fairness issues than those on real graphs. In other words, graph distillation can even *amplify* graph

<span id="page-0-0"></span> $1$ Group fairness ensures equitable treatment of diverse demographic groups by algorithms [Mehrabi et al.](#page-10-9) [\[2021\]](#page-10-9). Such as in mortality prediction, issues arise when true positive rates significantly differ between sensitive groups. Group fairness metrics will be introduced in Section [5.1.](#page-6-0)

<sup>37</sup>th Conference on Neural Information Processing Systems (NeurIPS 2023).

data bias, which challenges the applicability of graph distillation in high-stake applications [\[Mehrabi](#page-10-9) [et al., 2021,](#page-10-9) [Suresh and Guttag, 2019\]](#page-10-10). To this end, we propose a fair graph distillation framework to offer a significantly reduced graph size and also better utility-fairness trade-off while maintaining predictive performance.

Many debias methods explicitly use sensitive attributes, but these are inherently missing in distilled graphs because they are excluded from the data attributes and the meaning of the attributes may change during the optimization process. In this paper, we point out the relationship between the space of real graphs and the space of distilled graphs and develop a simple estimator of sensitive attributes and introduce a bias measurement called consistency. We then propose a bi-level optimization algorithm for fair graph distillation: the outer loop generates a fair and informative distilled graph using gradient matching and coherence loss, while GNNs train on distilled graphs in the inner loop. In a nutshell, the contributions can be summarized as follows:

- To our knowledge, this is the first paper to identify group fairness issues in conventional graph distillation methods with binary sensitive attributes, motivating the formulation of a fair graph distillation problem in node classification tasks.
- We discover the relationship between the space of real graphs and the space of distilled graphs. We develop a bias metric called coherence for distilled graphs and propose a bi-level optimization framework using this metric to achieve fair graph distillation.
- We perform extensive experiments on various real-world datasets and GNN architectures to validate the effectiveness of the proposed FGD algorithm. Results demonstrate that FGD achieves a better accuracy-fairness trade-off compared to vanilla graph distillation methods and numerous baselines.

### 2 Preliminaries

#### 2.1 Notations

We consider node classification tasks given a graph dataset  $G = \{A, X, Y, S\}$  with N nodes. Here,  $A \in \{0,1\}^{N \times N}$  is the adjacency matrix, and  $A_{ij} = 1$ represents there exists an edge between node  $i$  and  $j$ .  $\overrightarrow{X} \in \mathbb{R}^{N \times D}$  is the node feature matrix, where D is non-sensitive feature dimension for each node.  $Y \in$  $\{0, 1, \cdots, C-1\}^N$  denotes the node labels over C classes. For simplicity, we consider a binary sensitive attribute<sup>[2](#page-1-0)</sup>  $S \in \{0,1\}^N$ .  $\Pi^s$  is the sensitive membership diagonal matrix.  $\Pi_{ii}^s = 1$  if and only if *i*-th node belongs to sensitive group s. The distilled small graph dataset is marked as  $\mathcal{G}' = \{A', X', Y'\}$  which contains N' nodes and  $N' \ll N$ . Note that elements of the distilled adjacency matrix satisfy  $A'_{ij} \in [0,1]$  and no sensitive attributes exist in  $\mathcal{G}'$ . The latent node representation of real graph is  $Z$ , and the span space of it is  $\text{span}(\boldsymbol{Z}) \coloneqq \big\{ \sum_{i=1}^N w_i \boldsymbol{z}_i | 1 \leq i \leq N, w_i \in \mathbf{R} \big\}.$  Similar definition of  $Z'$  and span $(Z')$  for distilled graph.

<span id="page-1-1"></span>Image /page/1/Figure/8 description: This is a scatter plot showing the relationship between AUC on the x-axis and \Delta\_{DP} on the y-axis. There are six data points, each represented by a different marker and color, and connected by dashed red arrows. The legend indicates that orange markers represent 'Vanilla' datasets and blue markers represent 'Real' datasets. Specifically, there are three 'Vanilla' points: a circle at approximately (0.64, 0.09), a star at approximately (0.64, 0.04), and an inverted triangle at approximately (0.64, 0.015). There are also three 'Real' points: a circle at approximately (0.76, 0.06), a star at approximately (0.74, 0.002), and an inverted triangle at approximately (0.78, 0.006). The dashed arrows suggest a progression or comparison between these points.

Figure 1: AUC and  $\Delta_{DP}$  of the GNN trained on real graph data and distilled graph data. Both utility and fairness performance deteriorates after vanilla graph distillation.

#### 2.2 Graph Distillation via Gradient Matching

The purpose of graph distillation is to generate a *distilled* graph  $G'$  such that the GNN model, denoted as  $GNN_{\theta}$  with parameters  $\theta$ , trained on distilled graph performs *comparably* to the model trained on the real graph G. The objective can be formulated as the following bi-level optimization problem:

$$
\min_{\mathcal{G}'} \mathcal{L} \left( \text{GNN}_{\theta^{\mathcal{G}'}} \left( \mathbf{A}, \mathbf{X} \right), \mathbf{Y} \right) \text{ s.t } \theta^{\mathcal{G}'} = \arg\min_{\theta} \mathcal{L} \left( \text{GNN}_{\theta} \left( \mathbf{A}', \mathbf{X}' \right), \mathbf{Y}' \right) \tag{1}
$$

where  $\theta^{\mathcal{G}'}$  denotes the optimal parameter trained on distilled small graph  $\mathcal{G}',$  and  $\mathcal{L}(\cdot,\cdot)$  denotes the loss function. To tackle the above optimization problem, the gradient matching method [Zhao et al.](#page-11-3)

<span id="page-1-0"></span> ${}^{2}$ The sensitive attribute S represents the attribute that the respondents do not want to be disclosed, such as gender or age. Sensitive attribute  $S$  is not included in the normal features  $X$ .

[\[2021a\]](#page-11-3) is proposed. The intuition is to let the GNN parameters  $\theta^{\mathcal{G}'}$  trained on distilled graph follow a similar path to the GNN parameters  $\theta^{\mathcal{G}}$  trained on the real graph during model optimization. The gradient of the GNN parameters is forced to be the same over the real and distilled graphs:

<span id="page-2-0"></span>
$$
\min_{\mathcal{G}'} \left[ \sum_{t=0}^{T-1} D\left(\nabla_{\theta} \mathcal{L}(\mathcal{G}), \nabla_{\theta} \mathcal{L}(\mathcal{G}')\right) \right],\tag{2}
$$

where  $D(\cdot, \cdot)$  is a distance function, T is the number of steps of model parameters trajectory, and  $\theta_t^G$ ,  $\theta_t^{G'}$  denotes the model parameters trained on G and G' at time step t, respectively. The gradient calculated on G and G' is denoted as  $\nabla_{\theta} \mathcal{L}(\mathcal{G}) = \nabla_{\theta} \mathcal{L}(\text{GNN}_{\theta_t}(\mathbf{A}, \mathbf{X}), \mathbf{Y})$  and  $\nabla_{\theta} \mathcal{L}(\mathcal{G}') :=$  $\nabla_{\theta} \mathcal{L} \left( \text{GNN}_{\theta_t} \left( \boldsymbol{A}^\prime, \boldsymbol{X}^\prime \right), \boldsymbol{Y}^\prime \right)$ , respectively.

## 3 Bias Measurement for Distilled Graph

In this section, we empirically demonstrate the fairness issue in the distilled graph. Motivated by this, we pursue fair graph distillation. Although distilled graphs lack sensitive attributes  $S'$ , we observe that between the node representations of the real and distilled graphs: their barycenter remain consistent, and their spaces are also consistent. We leverage this phenomenon to develop a simple, effective bias measurement for distilled graphs.

### 3.1 Is Graph Distillation Really Fair?

Our empirical investigation assesses the fairness of graph distillation across various datasets and architectures. We compare the utility (AUC) and fairness (demographic parity ( $\Delta_{DP}$ ) [\[Beutel et al.,](#page-9-4) [2017\]](#page-9-4)) of GNNs trained on real graphs and those trained on distilled graphs created by the vanilla graph distillation method. The utility and fairness performance are shown in Figure [1.](#page-1-1) We can find that: For datasets like Pokec-n, German, and Credit, distilled graph-based GNNs have higher  $\Delta_{DP}$  and lower AUC performance, suggesting a compromise in fairness and prediction performance. We also notice that, for Pokec-z and Recidivism datasets, these GNNs exhibit lower  $\Delta_{DP}$  and significantly lower AUC performance (shown in Table [1\)](#page-6-1), indicating a trade-off between improved fairness and reduced prediction performance. We observe similar results when using other fair GNN models. More details can be found in Appendix [E.](#page-14-0) Motivated by these observations, we aim to find a better prediction performance and fairness trade-off via chasing the fair graph distillation method.

### 3.2 Geometric Connections in Data Distillation

The distilled data can be generated via minimizing gradient distance in Equation [2.](#page-2-0) To simplify the analysis, we consider  $D(\cdot, \cdot)$  as Euclidean distance and those model parameters during optimization trajectory satisfying  $\theta \sim \mathcal{P}_{\theta}$ , where  $\mathcal{P}_{\theta}$ is certain but unknown parameters' distribution. Therefore, the objective can be transformed as

<span id="page-2-1"></span>
$$
\min_{\mathcal{G}'} \mathbb{E}_{\theta \sim \mathcal{P}_{\theta}} \big[ || \nabla_{\theta} \mathcal{L}(\mathcal{G}) - \nabla_{\theta} \mathcal{L}(\mathcal{G}') ||^2 \big].
$$

We consider three assumptions of the model parameters' distribution and the convergence for loss minimization.

<span id="page-2-2"></span>Assumption 3.1 (Model parameters' distribution). We assume that each model parameter in the last softmax layer satisfies the same distribution.

<span id="page-2-3"></span>Assumption 3.2 (Loss minimization). We assume that exists at least one distilled dataset that minimizes Equation [3.](#page-2-1)

Theorem 3.3 (Consistent Span Space). *We empirically show*

<span id="page-2-4"></span>Image /page/2/Figure/14 description: The image displays two diagrams, labeled (a) and (b), illustrating geometric intuition of some concept related to "S^0" and "S^1". Both diagrams show a circular arc with points distributed along it. In diagram (a), there are two sets of points, one orange on the upper left and one blue on the lower right, with a gray set of points in between them on the arc. Lines connect the origin to points on the arc, with labels like "rho\_1^0" and "rho\_1^1". There are also curved arrows indicating "Coh(Z')" and labels "z\_0'" and "z\_1'". Diagram (b) is similar but shows slightly different configurations and labels, including "rho\_0'" and "rho\_1'". The overall presentation suggests a comparison or evolution between the two states represented by (a) and (b).

Figure 2: Geometric intuition of sensitive attribute estimation. The projection distance indicates the extent to which the node belongs to the sensitive group. (a) Unfair node representations have a large coherence bias. (b) Fair node representations have a small coherence bias.

*that span*( $\mathbf{Z}'$ )  $\approx$  *span*( $\mathbf{Z}$ ) via calculating the principle angles between them. We also provides the *rigorous proof of* z ′ ∈ *span*(Z) *under distribution matching in Appendix [D.](#page-13-0)*

. (3)

<span id="page-2-5"></span>Theorem 3.4 (Consistent Geometric Barycenters). *Under Assumptions [3.1](#page-2-2) and [3.2,](#page-2-3) the barycenter of the last representation for the optimal distilled graph and the real graphs are consistent, i.e.*  $\frac{1}{N}\sum_{i=1}^N \boldsymbol{z}_i = \frac{1}{N'}\sum_{i=1}^{N'} \boldsymbol{z}'_i$ . Please see proof in Appendix [B.](#page-12-0)

### 3.3 Sensitive Attribute Estimation

The consistent span space and geometric barycenter suggest that we can estimate sensitive attributes from the representations of both distilled and real graphs. We frame sensitive attribute estimation as a classification problem: Given a data representation  $z^i \in \mathbf{Z}'$ , what is the probability that  $z'$  belongs to the sensitive group?

Ridge regression for distance measurement. Notice that the representation of each sensitive group for the real graph is known, we define  $Z_0$  and  $Z_1$  as the representation matrix for sensitive group  $s = 0$  and  $s = 1$ . To measure the probability that z' belongs to these two sensitive groups, we first find the closest vector  $z'_{proj} = Z_s^{\top} q \in \text{Span}(Z_s)$  to approximate the representation  $z'$ , and then use the norm of  $z' - z'_{proj}$  to measure the distance between  $z'$  and sensitive group  $Z_s$ . Specifically, we adopt ridge regression to find the optimal coefficient vector  $q$ <sup>\*</sup>, which can be formulated as

$$
Dist(\mathbf{z}', \mathbf{Z}_s) = ||\mathbf{z}' - \mathbf{Z}_s^\top \mathbf{q}^*||_2 \tag{4}
$$

$$
\text{s.t } \mathbf{q}^* = \underset{\mathbf{q}}{\arg\min} \gamma \|\mathbf{z}' - \mathbf{Z}_s^\top \mathbf{q}\|_2^2 + \|\mathbf{q}\|_2^2,\tag{5}
$$

where  $\gamma$  is the hyperparameter for ridge regression. For the optimal  $q^*$ , we have

$$
\boldsymbol{p}^s = \boldsymbol{z}' - \boldsymbol{Z}_s^\top \boldsymbol{q}^* = \boldsymbol{z}' - \gamma \boldsymbol{Z}_s^\top (\boldsymbol{I} + \gamma \boldsymbol{Z}_s \boldsymbol{Z}_s^\top)^{-1} \boldsymbol{Z}_s \boldsymbol{z}',\tag{6}
$$

where  $\top$  represents matrix transpose,  $p^s$  is approximately the projection onto the orthogonal complement of the subspace span $(Z_s)$ . The proof is in Appendix [C.](#page-13-1)

Sensitive attribute soft estimation. Since  $p^s = z' - \gamma Z_s^{\top} (I + \gamma Z_s Z_s^{\top})^{-1} Z_s z'$  can be viewed as approximately the projection of z onto the orthogonal complement of sensitive group  $Z_s$ ,  $\|\boldsymbol{p}^s\|_2$  is small if z is in sensitive group  $Z_s$  and large otherwise. The probability of the given data representation  $z$  belongs to the sensitive group  $Z_s$  can further be inferred via a softmax function:

$$
\pi^{s}(z') = \frac{\exp(-\lambda \|\pmb{p}^{s}\|_{2})}{\sum_{s=0}^{1} \exp(-\lambda \|\pmb{p}^{s}\|_{2})} \in [0,1],
$$
\n(7)

where  $\lambda$  is the temperature hyperparameter. The sensitive attribute probability of  $z'$  for distilled graph can be estimated as probability distribution  $[\pi^{s=0}(z'), \pi^{s=1}(z')]$ , where  $\pi^{s=0}(z') + \pi^{s=1}(z') = 1$ .

<span id="page-3-1"></span>

### 3.4 Bias Measurement

Given the estimated sensitive attribute probability for  $z'$  of each distilled node, how can we measure the bias for them? For a fair representation, we can not distinguish which representation is more likely to be a specific sensitive group. Therefore, we adopt a simple surrogate bias measurement, named coherence, the variance of the estimated sensitive group membership. Given the whole distilled data representation  $\mathbf{Z}' = [z_1, z_N]^\top$ , the bias can be defined as:

$$
Cohs(\mathbf{Z}') = \widehat{Var}(\boldsymbol{\pi}^s(\mathbf{Z}')) = \frac{1}{N'}\sum_{n=1}^{N'}\left(\boldsymbol{\pi}^s(\mathbf{z}'_n) - \frac{1}{N'}\sum_{n=1}^{N'}\boldsymbol{\pi}^s(\mathbf{z}'_n)\right)
$$

Note that  $Coh^{s=0}(\mathbf{Z}') = Coh^{s=1}(\mathbf{Z}')$ , and we adopt abbreviation  $Coh(\mathbf{Z}')$ <sup>[3](#page-3-0)</sup>.

Geometric intuition. The intuition of sensitive attribute estimation, as illustrated in Figure [2,](#page-2-4) can be grasped from a geometric standpoint. In a toy example with a two-dimensional data representation,  $z' \in \mathbb{R}^2$ , we consider two demographic groups for a binary sensitive attribute. The subspace spanned by the data representations from these groups is denoted by  $S^0$  and  $S^1$ . Data representations to be estimated are  $z'_0$  and  $z'_1$ .  $p_0^0$ ,  $p_1^0$  and  $p_0^1$ ,  $p_1^1$  is the projection of  $z'_0$  and  $z'_1$  onto the orthogonal complement of  $S^0$  and  $S^1$ . As for fair data representation, zero coherent encourages all representations aligned with a "line" so that all representations are with the same normalized similarity with sensitive groups. Figure [2](#page-2-4) (a) shows the case in which the data representation is biased where  $z'_0$  and  $z'_1$  can be easily distinguished. Figure [2](#page-2-4) (b) shows that fairer data representation as they are less separable.

<span id="page-3-0"></span><sup>&</sup>lt;sup>3</sup> For multiple-value sensitive attribute, we can use average coherence  $Coh(\mathbf{Z}')$  across all sensitive groups.

Image /page/4/Figure/0 description: The image is a legend for a diagram. It shows colored circles and arrows representing different elements. An orange circle is labeled "S=0", a blue circle is labeled "S=1", and a gray circle is labeled "Synthetic node". A blue arrow is labeled "Forward Pass". A dark green arrow is labeled "BP Gradient of Real Graph". A light green arrow is labeled "BP Gradient of Distilled Graph".

Image /page/4/Figure/1 description: This figure illustrates a deep learning model for graph synthesis. The model consists of a 'Synthesizer' module that takes input features X' and generates synthetic graph features A' using an MLP. The core of the model involves an 'Inner Loop Optimization Update' process where a graph G is processed through multiple GNN layers. The output of these layers is then used to calculate a 'Cross-Entropy Loss' (LCE). Additionally, a 'Gradient Matching Loss' (LGM) is computed between the gradients of the original graph G and a synthesized graph G'. An 'Outer Loop Optimization Update' refines the synthesizer's parameters based on these losses, incorporating a 'Coherence Loss' (LCoh) which measures the consistency of node embeddings. The process is iterative, aiming to generate realistic and coherent synthetic graphs.

Figure 3: An overview of the proposed framework. The synthesizer generates the attribute matrix and adjacency matrix of the distilled small graph  $\mathcal{G}'$ . The Cross-Entropy loss  $\mathcal{L}_{CE}$  guides the update of GNNs model during the inner optimization loop. Gradient matching loss  $\mathscr{L}_{GM}$  and coherence loss  $\mathcal{L}_{Coh}$  guide the update of the synthesizer during the outer optimization loop for utility and fairness.

## 4 Methodology

<span id="page-4-1"></span>

### 4.1 Problem Statement

Based on the proposed coherence metric, we argue that if  $Coh(\mathbf{Z}')$  is reduced, bias in the distilled graph can be mitigated. As a result, if GNNs are trained on such distilled graphs, the bias issues in downstream tasks could also be alleviated. The problem is formally defined as: Given an undirected attributed network  $\mathcal{G} = \{A, X, Y, S\}$ , our goal is to obtain an debiased distilled graph  $\mathcal{G}' = \{A', X', Y'\}$  via reducing Coh, so that the fairness issues of GNNs trained on  $\mathcal{G}'$  is mitigated. Hence the overall objective goal for generating a fair and condensed graph is:

$$
\min_{\mathcal{G}'} \mathcal{L}_{GM} + \alpha \mathcal{L}_{Coh} \left( \text{GNN}_{\theta^{\mathcal{G}'}} \left( \mathbf{A}', \mathbf{X}' \right), \text{GNN}_{\theta^{\mathcal{G}'}} \left( \mathbf{A}, \mathbf{X} \right) \right)
$$
\n
$$
\text{s.t } \theta^{\mathcal{G}'} = \arg\min_{\theta} \mathcal{L}_{CE} \left( \text{GNN}_{\theta} \left( \mathbf{A}', \mathbf{X}' \right), \mathbf{Y}' \right) \tag{8}
$$

### 4.2 Fair Graph Distillation Loss

Gradient Matching Loss. We adopt gradient matching, as shown in equation [\(2\)](#page-2-0), for graph distillation to distill useful information for node classification tasks. However, treating both  $X'$  and A' as learnable parameter <sup>[4](#page-4-0)</sup> and directly optimizing A' is unaffordable due to  $O(N^2)$  computation complexity. Following previous work [Jin et al.](#page-10-6) [\[2021\]](#page-10-6), we parameterize  $A'$  as a function of  $X'$ :

$$
A'_{i,j} = \text{Sigmoid}\left(\frac{\text{MLP}_{\phi}([x'_i; x'_j]) + \text{MLP}_{\phi}([x'_j; x'_i])}{2}\right),\tag{9}
$$

where  $A'_{i,j}$  is *i*-th row, *j*-th column of  $A'$ , MLP<sub> $\phi$ </sub> is a multi-layer neural network parameterized with  $\phi$  and  $[\cdot; \cdot]$  denotes concatenation. Note that  $A'$  is controlled to be symmetric since  $A'_{i,j} = A'_{j,i}$ . Sigmoid function pushes  $A'$  close to 0 or 1 to encourage its sparsity. For simplicity, we denote the parameterized adjacency matrix as  $A'_{\phi}$ . In this way, we can reduce the complexity to  $O(N)$ .

The distance metric  $D$  measures the similarity of gradients over the real graph and distilled graph. We adopt the summation of the gradient distance over all layers as the final gradient distance:

$$
D\left(\nabla_{\theta}\mathcal{L}(\mathcal{G}), \nabla_{\theta}\mathcal{L}(\mathcal{G})'\right) = \sum_{i} \left(1 - \frac{\nabla_{\theta}\mathcal{L}(\mathcal{G})_{i} \cdot \nabla_{\theta}\mathcal{L}(\mathcal{G})'_{i}}{\|\nabla_{\theta}\mathcal{L}(\mathcal{G})_{i}\| \|\nabla_{\theta}\mathcal{L}(\mathcal{G})'_{i}\|}\right) \tag{10}
$$

<span id="page-4-0"></span><sup>&</sup>lt;sup>4</sup>The distilled label  $Y'$  is sampled from real label Y with the same class probability, and it is fixed.

where  $\nabla_{\theta} \mathcal{L}(\mathcal{G})_i$  is the i-th column vectors of the gradient matrices. Hence the loss objective for the graph distillation module is given by:

$$
\mathcal{L}_{GM} = \mathbf{E}_{\theta \sim P_{\theta}} \left[ \sum_{t=0}^{T-1} D \left( \nabla_{\theta} \mathcal{L}(\mathcal{G}), \nabla_{\theta} \mathcal{L}(\mathcal{G}') \right) \right]
$$
(11)

where  $\mathcal{G}' = \{ \mathbf{X}', \mathbf{A}', \mathbf{Y}' \}, t$  is the training epoch, and  $\theta_t$  is well-trained GNNs model parameters. To reduce the impact of parameter initialization, the initial model parameters  $\theta_0$  are sampled from a distribution of random initialization.

Coherence loss. In Section [3.4,](#page-3-1) we introduce coherence as a bias metric for distilled data. To mitigate bias, we use coherence bias as a regularization for fair synthesis. This calculation employs real graph node attribute X and distilled node attribute  $X'$  to estimate sensitive group memberships but overlooks structural bias in graph data. Given the GNN propagation mechanism, bias can exist in both node attributes and graph structure [Dong et al.](#page-9-5) [\[2022\]](#page-9-5). Even without attribute bias, node representation may still be biased if structural bias is present.

Work by [Dong et al.](#page-9-5) [\[2022\]](#page-9-5) suggests that structural bias can be measured through graph representation bias. Leveraging this, we aim for low coherence in node attributes and representations to fully remove bias from our distilled graph. Specifically, we introduce attribute and structural coherence to decrease attribute and structural bias, respectively, by minimizing the variance in sensitive group membership estimation for node attributes and representations. Given a real graph data  $\mathcal{G} = \{A, X, Y, S\}$  and a distilled graph  $\mathcal{G}' = \{A', X', Y'\}$ , we feed them into a L-layer GNN, where the l-th layer latent representation in the GNN is denoted as  $Z_l$ . The latent representation for node attribute after *l*-hop propagation contains both attribute bias as well as structural bias. Note the node attribute  $X$  and  $\bm{X}'$  before propagation as  $\bm{Z}_0$  and  $\bm{Z}'_0$ , we get a set of latent presentation  $\{\bm{Z}_0,\bm{Z}_1,...,\bm{Z}_L\}$  for  $\bm{\mathcal{G}}$  and  $\{Z'_0, Z'_1, ..., Z'_L\}$  for  $\mathcal{G}'$ . The objective to measure bias of  $\tilde{Z}'_l$  is:

$$
Coh(\mathbf{Z}'_l) = \widehat{Var}(\boldsymbol{\pi}^j(\mathbf{Z}'_l)) = \widehat{Var}\left(\frac{\exp(-\lambda \|\mathbf{C}^j\mathbf{Z}'_l\|_2)}{\sum_j \exp(-\lambda \|\mathbf{C}^j\mathbf{Z}'_l\|_2)}\right),\tag{12}
$$

where  $C^j = \gamma_j \left(I + \gamma_j Z_l \Pi^j Z_l^T\right)^{-1}$ .  $\Pi^j$  is introduced in Sec [4.1.](#page-4-1) Since we consider the binary sensitive attribute, j is set as 0 without losing generality and is omitted in the notations as  $\pi^{j}(\cdot)$  :=  $\pi(\cdot)$ . After considering all the latent representations, the coherence loss objective is defined as the summation of all coherence over all layers, i.e.,

$$
\mathcal{L}_{Coh} = \sum_{l=0}^{L} Coh(\mathbf{Z}'_l) = \sum_{l=0}^{L} \widehat{Var}(\boldsymbol{\pi}(\mathbf{Z}'_l)).
$$
\n(13)

**Prediction loss for GNN training.** The GNNs model is trained on distilled graph  $G'$  =  $\{A', X', Y'\}$  with prediction loss. We adopt L-layer GNNs model, where  $\theta$  is the parameter of the GNN. We also adopt cross-entropy loss by default:

$$
\mathcal{L}_{CE} = \mathcal{L} \left( \text{GNN}_{\theta} \left( \mathbf{A}', \mathbf{X}' \right), \mathbf{Y}' \right), \tag{14}
$$

### 4.3 Final Objective and Training Algorithm

Outer loop optimization. In the outer loop, we optimize the fair graph synthesizer with gradient matching loss and coherence loss:

<span id="page-5-0"></span>
$$
\min_{\mathbf{X}',\mathbf{A}'} \mathcal{L}_{GM} + \alpha \mathcal{L}_{Coh},\tag{15}
$$

where  $\alpha$  is a hyperparameter to regularize the debiasing intensity. The distilled node attribute  $X'$  and the distilled node label  $Y'$  are initialized with the nodes uniformly sampling from real graph data  $\mathcal{G}$ .

**Inner loop optimization.** The GNN parameter  $\theta$  is optimized in the inner loop:

<span id="page-5-1"></span>
$$
\min_{\theta} \mathcal{L}_{CE} \left( \text{GNN}_{\theta} \left( \boldsymbol{A}^{\prime}, \boldsymbol{X}^{\prime} \right), \boldsymbol{Y}^{\prime} \right). \tag{16}
$$

Instead of using the real graph data  $G$  to calculate the loss, we use the distilled graph  $G'$ . It empirically shows good performance and better efficiency. But the adversarial training baseline uses  $\mathcal G$  as it needs the sensitive attribute for discriminator training.

<span id="page-6-1"></span>Table 1: Comparison on utility and bias mitigation between GNNs with real graph data (denoted as Real), the distilled small graph without debiasing (denoted as Vanilla), and debiased distilled graph (denoted as FGD) as input. ↑ denotes the larger, the better; ↓ denotes the opposite. The best ones are in bold. The better performers in Vanilla and FGDare underlined.

|            |                                      |                   | <b>GCN</b>        |                   |                     | SGC               |                   |                   | GraphSAGE         |                   |
|------------|--------------------------------------|-------------------|-------------------|-------------------|---------------------|-------------------|-------------------|-------------------|-------------------|-------------------|
|            |                                      | Real              | Vanilla           | FGD               | Real                | Vanilla           | FGD               | Real              | Vanilla           | FGD               |
|            | $ACC \uparrow$                       | $70.96 \pm 0.4\%$ | $66.36 \pm 1.0\%$ | 66.58±0.7%        | $70.79 \pm 0.1\%$   | $68.31 \pm 0.7\%$ | 68.36±0.3%        | $70.59 \pm 0.3\%$ | $67.13 \pm 0.4\%$ | 67.83±0.6%        |
|            | AUC $\uparrow$                       | $78.19 \pm 0.2\%$ | 70.30±0.4%        | 70.48±0.3%        | $77.16 \pm 0.0\%$   | 73.51±0.6%        | 72.92±0.4%        | 77.91±0.2%        | 70.47±0.0%        | 70.26±0.5%        |
| Pokec-z    | F1                                   | 72.16±0.5%        | 65.66±0.7%        | 66.48±0.8%        | $71.21 \pm 0.0\%$   | 68.09±0.4%        | $67.94 \pm 0.6\%$ | $72.07 \pm 0.4\%$ | 66.34±0.8%        | $66.62 \pm 0.7\%$ |
|            | $\overline{\Delta}_{DP} \downarrow$  | $4.13 \pm 1.3\%$  | $2.84 \pm 1.1\%$  | $1.75 \pm 1.1\%$  | $4.64 \pm 0.1\%$    | $7.60 \pm 1.7\%$  | $5.77 \pm 0.2\%$  | $4.54 \pm 1.3\%$  | $3.74 \pm 0.8\%$  | $2.17 \pm 1.6\%$  |
|            | $\Delta_{EO}$                        | $4.57 \pm 1.7\%$  | $2.19 \pm 1.3\%$  | $1.19 + 1.0\%$    | $5.26 \pm 0.1\%$    | 7.88±1.9%         | $4.78 + 0.2\%$    | $5.25 \pm 1.2\%$  | $2.50 + 1.2\%$    | $2.56 \pm 1.2\%$  |
|            | $ACC+$                               | $71.97 \pm 0.3\%$ | $50.10 \pm 2.7\%$ | 54.80±1.5%        | $71.16 \pm 0.0\%$   | 68.06±0.9%        | $68.19 \pm 0.8\%$ | $71.91 \pm 0.3\%$ | 52.80±2.2%        | 58.40±1.6%        |
|            | AUC $\uparrow$                       | $78.15 \pm 0.2\%$ | $51.09 \pm 2.3\%$ | $63.75 \pm 0.5\%$ | $76.34 {\pm} 0.0\%$ | 69.96±0.3%        | 70.18±0.5%        | 77.56±0.1%        | 53.95±2.8%        | $60.06 \pm 2.3\%$ |
| Pokec-n    | $F1$ <sup><math>\dagger</math></sup> | $69.92 \pm 0.4\%$ | 44.21±4.6%        | 49.90±5.1%        | $67.63 \pm 0.0\%$   | $63.95 \pm 0.1\%$ | 64.03±0.1%        | $70.01 \pm 0.3\%$ | 58.75±6.1%        | 62.36±2.0%        |
|            | $\Delta_{DP} \downarrow$             | $0.59 \pm 0.4\%$  | $4.02 \pm 0.6\%$  | $0.66 \pm 0.5\%$  | $4.3 \pm 0.1\%$     | $5.00 \pm 0.5\%$  | $4.7 \pm 0.5\%$   | $0.99 \pm 0.4\%$  | $2.38 \pm 2.8\%$  | $2.09 \pm 1.6\%$  |
|            | $\Delta_{EO} \downarrow$             | $1.04\pm0.6\%$    | $5.20 \pm 1.0\%$  | $1.20 \pm 1.2\%$  | $2.26 \pm 0.1\%$    | $4.6 \pm 0.9\%$   | $4.26 \pm 1.2\%$  | $1.64\pm0.6\%$    | $2.81 \pm 3.0\%$  | $2.02 \pm 1.2\%$  |
|            | $ACC+$                               | 74.37±0.4%        | 72.83±0.8%        | 70.50±0.1%        | $72.62 \pm 1.6\%$   | 70.24±0.2%        | 70.06±0.1%        | 74.24±0.2%        | 72.00±0.5%        | 71.43±0.9%        |
|            | AUC $\uparrow$                       | 74.31±0.2%        | 57.75±4.8%        | 57.89±5.5%        | 74.94±1.2%          | 54.82±0.4%        | 53.92±1.4%        | $71.37 \pm 0.4\%$ | 57.32±0.4%        | 58.76±5.9%        |
| German     | $F1$ 1                               | $84.24 \pm 0.1\%$ | 83.51±0.4%        | 83.09±0.4%        | 83.13±0.3%          | 82.43±0.0%        | 82.36±0.0%        | 84.18±0.1%        | 83.12±0.4%        | 82.93±0.3%        |
|            | $\overline{\Delta_{DP}}$             | $4.8 \pm 3.9\%$   | $5.38 \pm 3.3\%$  | $1.93 \pm 0.1\%$  | $3.36 \pm 2.8\%$    | $2.10 \pm 2.8\%$  | $1.3 \pm 0.9\%$   | $3.00 \pm 0.8\%$  | $5.33 \pm 2.9\%$  | $0.76 \pm 0.5\%$  |
|            | $\Delta_{EO}$                        | $2.50 \pm 2.7\%$  | $1.04 \pm 1.1\%$  | $0.61 \pm 0.4\%$  | $9.38 \pm 9.0\%$    | $2.44 \pm 0.1\%$  | $0.28{\pm}0.2\%$  | $1.64 \pm 0.5\%$  | $2.89 \pm 1.0\%$  | $0.52 + 0.4\%$    |
|            | $ACC+$                               | $80.54 \pm 0.0\%$ | 76.92±1.9%        | 77.91±0.1%        | $79.66 \pm 0.1\%$   | 77.41±0.0%        | 77.36±0.7%        | $80.52 \pm 0.0\%$ | 77.91±0.5%        | 77.86±0.1%        |
|            | AUC 1                                | $75.89 \pm 0.0\%$ | 68.82±2.1%        | 68.87±0.2%        | 73.39±0.1%          | 71.78±0.0%        | 72.02±0.1%        | 75.89±0.0%        | 71.12±0.2%        | 71.36±0.1%        |
| Credit     | $F1 \uparrow$                        | 88.41±0.0%        | 85.47±2.0%        | 87.33±0.4%        | 88.09±0.0%          | 85.46±0.0%        | 85.37±0.7%        | 88.41±0.0%        | 87.00±0.9%        | 86.79±0.5%        |
|            | $\overline{\Delta}_{DP} \downarrow$  | $5.41 \pm 0.6\%$  | $12.04 \pm 5.4\%$ | $4.94 \pm 4.8\%$  | $2.78 \pm 0.3\%$    | $9.58 \pm 0.4\%$  | $7.28 \pm 2.8\%$  | $6.22 \pm 0.6\%$  | $8.77 \pm 2.0\%$  | $4.15 \pm 0.8\%$  |
|            | $\Delta_{EO}$                        | $3.12\pm0.6\%$    | $9.58 \pm 5.5\%$  | 3.56±3.4%         | $1.19 + 0.3\%$      | $7.14 \pm 0.5\%$  | 5.56±0.1%         | 3.92±0.5%         | $6.72 \pm 4.0\%$  | $3.34 \pm 0.9\%$  |
| Recidivism | $ACC \uparrow$                       | $94.45 \pm 0.0\%$ | 70.89±1.8%        | 70.09±2.6%        | $85.10 \pm 0.1\%$   | 73.32±0.2%        | 73.10±0.5%        | $94.48 \pm 0.0\%$ | 73.66±1.0%        | $70.63 \pm 2.0\%$ |
|            | AUC 1                                | $97.76 \pm 0.0\%$ | 71.95±2.8%        | 75.81±4.3%        | $92.24 \pm 0.1\%$   | $72.23 \pm 0.6\%$ | 72.44±0.1%        | $97.78 \pm 0.0\%$ | 75.84±1.2%        | 70.47±5.7%        |
|            | F1                                   | $92.32 \pm 0.0\%$ | 55.30±3.9%        | 52.97±8.3%        | 76.94±0.3%          | $60.77 \pm 0.8\%$ | $60.45 \pm 0.6\%$ | $92.35 \pm 0.1\%$ | 60.87±2.4%        | $56.39 \pm 6.1\%$ |
|            | $\Delta_{DP} \downarrow$             | $6.52 \pm 0.1\%$  | $2.68 \pm 1.0\%$  | $0.54 \pm 0.3\%$  | 7.89±0.0%           | $2.85 \pm 0.9\%$  | $1.08\pm0.7\%$    | $6.61 \pm 0.1\%$  | $0.97 \pm 0.8\%$  | $0.10\pm0.1\%$    |
|            | $\Delta_{EO}$ +                      | $3.45 \pm 0.4\%$  | $1.41 \pm 0.5\%$  | $0.46 \pm 0.2\%$  | $8.55 \pm 0.2\%$    | $2.69 \pm 0.6\%$  | $1.32{\pm}0.8\%$  | $3.56 \pm 0.3\%$  | $0.93 \pm 0.8\%$  | $0.48 + 0.4\%$    |

## 5 Experiments

We design experiments to validate the effectiveness of the proposed framework FGDand answer the following research questions: RQ.1 How well can FGD mitigate the bias in the distilled graph and alleviate the fairness issue of the GNNs trained on the distilled small graph? RQ.2 How well can FGD balance the trade-off between accuracy and bias mitigation compared with other debiasing baselines? RQ.3 Can FGD further improve the utility or bias mitigation as an add-on module to other bias mitigation methods?

### 5.1 Experimental setting

Datasets. We use five real-world datasets, including Pokec-z, Pokec-n [\[Dai and Wang, 2021,](#page-9-6) [Takac](#page-11-5) [and Zabovsky, 2012\]](#page-11-5), German, Credit, and Recidivism [\[Agarwal et al., 2021\]](#page-9-7). The detailed setting of the datasets is in Appendix [F](#page-14-1)

GNN Models. We adopt three popular GNN variants in our experiments, including GCN [\[Kipf and](#page-10-11) [Welling, 2016\]](#page-10-11), SGC [\[Wu et al., 2019\]](#page-11-6), GraphSAGE [\[Hamilton et al., 2017\]](#page-9-3).

Baselines. Given the absence of fair graph distillation work, we compare our approach with four baselines. (1) *Real* uses real graph data to train a GNN model. (2) *Vanilla* applies a vanilla graph distillation algorithm [\[Jin et al., 2022\]](#page-10-7) for distilled graph data learning and GNN model training. (3) *FairGNN*[\[Dai and Wang, 2021\]](#page-9-6) trains a fair GNN model adversarially on real graph data, aiming to achieve good prediction while fooling a discriminator. (4) *EDITS* [\[Dong et al., 2022\]](#page-9-5) is a modelagnostic debiasing method that rewires graph data for fair GNNs.

<span id="page-6-0"></span>Evaluation Metrics. We evaluate model performance from model utility and bias measurements. Good performance represents low bias and high model utility. For model utility metrics, we adopt accuracy (ACC), the area under the receiver operating characteristic curve (AUC), and F1 score to measure prediction performance. For bias measurement, we adopt two commonly used fairness metrics, i.e., demographic parity ( $\Delta_{DP}$ ) and equal opportunity ( $\Delta_{EO}$ ) [\[Beutel et al., 2017,](#page-9-4) [Louizos](#page-10-12) [et al., 2015\]](#page-10-12). Denote the binary label as  $y \in \{0, 1\}$ , and sensitive attribute as  $s \in \{0, 1\}$ .  $\hat{y} \in \{0, 1\}$ denotes the model prediction. The violation of DP and EO are given by  $\Delta_{DP} = |P(\hat{y} = 1 | s =$  $(0) - P(\hat{y} = 1 | s = 1) |$ , and  $\Delta_{EO} = |P(\hat{y} = 1 | y = 1, s = 0) - P(\hat{y} = 1 | y = 1, s = 1) |$ .

<span id="page-7-1"></span>Image /page/7/Figure/0 description: The image displays a grid of scatter plots, with two rows and five columns. Each column represents a different dataset: Pokec-z, Pokec-n, German, Credit, and Recidivism. The top row plots \$\Delta\_{DP}\$ against AUC, and the bottom row plots \$\Delta\_{EO}\$ against AUC. Within each plot, there are data points for three methods: FGD (blue circles), Vanilla (orange stars), and FairGNN (green circles). The German and Credit plots in the top row also include data points for EDITS (red circles). The Pokec-z dataset in the top row shows FGD points at approximately (0.705, 0.021) and (0.710, 0.023), Vanilla at (0.705, 0.035), and FairGNN at (0.705, 0.022), (0.710, 0.027), (0.715, 0.035), and (0.718, 0.038). The Pokec-n dataset in the top row shows FGD points at approximately (0.635, 0.017), (0.645, 0.023), and (0.650, 0.024), Vanilla at (0.500, 0.039), and FairGNN at (0.550, 0.018), (0.575, 0.027), and (0.600, 0.034). The German dataset in the top row shows FGD points at approximately (0.515, 0.013), (0.540, 0.023), (0.570, 0.020), and (0.595, 0.035), Vanilla at (0.580, 0.049), FairGNN at (0.515, 0.016), (0.530, 0.018), (0.555, 0.030), and (0.580, 0.040), and EDITS at (0.510, 0.008). The Credit dataset in the top row shows FGD points at approximately (0.650, 0.030), (0.660, 0.040), (0.675, 0.065), (0.690, 0.085), and (0.695, 0.090), Vanilla at (0.690, 0.115), FairGNN at (0.670, 0.070), (0.685, 0.080), and (0.695, 0.088), and EDITS at (0.685, 0.050). The Recidivism dataset in the top row shows FGD points at approximately (0.700, 0.005), (0.725, 0.007), and (0.750, 0.008), Vanilla at (0.725, 0.025), and FairGNN at (0.750, 0.033), (0.775, 0.037), and (0.790, 0.040). The Pokec-z dataset in the bottom row shows FGD points at approximately (0.705, 0.015) and (0.710, 0.018), Vanilla at (0.705, 0.022), and FairGNN at (0.705, 0.026), (0.715, 0.039). The Pokec-n dataset in the bottom row shows FGD points at approximately (0.635, 0.015), (0.645, 0.017), and (0.650, 0.018), Vanilla at (0.500, 0.050), and FairGNN at (0.550, 0.029), (0.575, 0.038), and (0.600, 0.051). The German dataset in the bottom row shows FGD points at approximately (0.540, 0.004), (0.570, 0.006), and (0.595, 0.007), Vanilla at (0.580, 0.010), FairGNN at (0.540, 0.007), (0.555, 0.012), and (0.580, 0.016), and EDITS at (0.510, 0.007). The Credit dataset in the bottom row shows FGD points at approximately (0.650, 0.022), (0.660, 0.030), (0.675, 0.050), (0.690, 0.068), and (0.695, 0.070), Vanilla at (0.690, 0.085), FairGNN at (0.670, 0.055), (0.685, 0.065), and (0.695, 0.072), and EDITS at (0.685, 0.035). The Recidivism dataset in the bottom row shows FGD points at approximately (0.700, 0.002), (0.725, 0.003), and (0.750, 0.004), Vanilla at (0.725, 0.015), and FairGNN at (0.750, 0.032), (0.775, 0.037), and (0.790, 0.042).

Figure 4: Trade-off comparison between FGDand other baselines for five real-world graph datasets.

### 5.2 Debiasing distilled Graph

In response to **RQ.1**, we assess FGD's bias mitigation and prediction performance across various GNN architectures and datasets, as shown in Table [1.](#page-6-1) We compare the  $\Delta_{DP}$  and  $\Delta_{EO}$  values of GNNs trained on real graphs (*Real*), vanilla distilled graphs (*Vanilla*), and debiased distilled graphs via FGD (*FGD*). Key findings include: (1) Models trained on *Real* graphs consistently outperform *Vanilla* and *FGD* in utility, though *FGD*'s utility matches or surpasses *Vanilla*. (2) FGD consistently yields lower bias than *Vanilla*, and outperforms *Real* on 4 out of 5 datasets, excluding Poken-n.

We compare the coherence bias of distilled graphs generated by *Vanilla* and *FGD* methods across five real-world datasets with the GCN architecture (Table [2\)](#page-7-0). Our analysis reveals that FGD reduces unfairness, reflected in lower coherence bias in the distilled graphs. This consistency confirms the effectiveness of coherence bias as a measure of distilled graph bias.

### 5.3 Trade-Off Comparison

In response to RQ.2, we compare the tradeoff between model utility and bias mitigation against other baselines using the GCN architecture. We utilize the Pareto frontier [Ishizaka](#page-9-8) [and Nemery](#page-9-8) [\[2013\]](#page-9-8) to evaluate our approach's utility-fairness trade-off, using different hyperparameters. The Pareto frontier graphically represents optimal trade-offs in multi-objective optimization. We use AUC as the utility metric and  $\Delta_{DP}$  and  $\Delta_{EO}$  as fairness metrics. Higher AUC and lower  $\Delta_{DP}/\Delta_{EO}$  are preferred, so models with Pareto frontier curves closer to the bottom right corner (AUC on the horizontal axis and  $\Delta_{DP}/\Delta_{EO}$  on the vertical) have better trade-off performance.

<span id="page-7-0"></span>Table 2: Coherence bias comparison between vanilla distilled graph (denoted as Vanilla) and fair distilled graph (denoted as FGD). The lower, the better. The best ones are marked in bold. The architecture model is GCN.

|            | Vanilla  | FGD               |
|------------|----------|-------------------|
| Pokec-z    | 0.009468 | 0.002123(-77.57%) |
| Pokec-n    | 0.004464 | 0.000432(-90.32%) |
| German     | 0.012772 | 0.003489(-72.68%) |
| Credit     | 0.011864 | 0.002866(-75.84%) |
| Recidivism | 0.000098 | 0.000038(-61.22%) |

Figure [4](#page-7-1) shows the results for models trained on the real graph, the distilled graph debiased by baseline methods (vanilla graph distillation, FairGNN, and EDITS<sup>[5](#page-7-2)</sup>) and the distilled graph debiased by FGD. We can observe: (1) From a model utility perspective, FGD performs comparably to other baselines, like vanilla graph distillation, FairGNN, and EDITS<sup>[6](#page-7-3)</sup>, suggesting it preserves sufficient information for node classification. (2) In terms of bias mitigation, all baselines show effectiveness, with FGD exhibiting the best results. (3) When considering the utility-fairness trade-off, FGD's Pareto front curve lies at the bottom right corner of all baselines, signifying it offers the best balance. Thus, FGD outperforms other baselines in balancing model utility and bias mitigation.

### 5.4 Add-on Module

<span id="page-7-2"></span><sup>5</sup>EDITS publishes fair graph for German, Credit and Recidivism dataset on [Github](https://github.com/yushundong/EDITS/tree/620440b45818fef34f582ac6698a15000e25ed67/pre_processed)

<span id="page-7-3"></span><sup>6</sup>Out of memory (OOM) issue appears when running EDITS on Pokec-z and Pokec-n datasets.

In addition to its superior trade-off performance, our method, FGD, can enhance other debiasing baselines like FairGNN and EDITS by acting as an add-on debias module. This compatibility is due to the fact that these baselines can replace the cross-entropy loss in the GNN training module. To answer RQ.3, we conducted experiments on the Credit dataset comparing FairGNN/EDITS performance with and without FGD. As shown in Figure [5,](#page-8-0) FairGNN/EDITS coupled with FGD delivers better utility-fairness

<span id="page-8-0"></span>Image /page/8/Figure/1 description: Two scatter plots are shown side-by-side, both titled "Credit". The x-axis for both plots is labeled "AUC" and ranges from 0.65 to 0.70. The left plot's y-axis is labeled "ΔDP" and ranges from 0.00 to 0.10. The right plot's y-axis is labeled "ΔEO" and ranges from 0.00 to 0.08. Both plots display four sets of data points, each represented by a different color and marker, and connected by lines. The legend for both plots indicates the data sets as "FairGNN" (blue circles), "Ours+FairGNN" (orange circles), "EDITS" (green circles), and "FGD+EDITS" (red circles). In the left plot, "FairGNN" has points at approximately (0.67, 0.07) and (0.69, 0.10). "Ours+FairGNN" has points at approximately (0.66, 0.01), (0.67, 0.02), and (0.69, 0.02). "EDITS" has a point at approximately (0.69, 0.055). "FGD+EDITS" has points at approximately (0.675, 0.02), (0.68, 0.02), and (0.695, 0.035). In the right plot, "FairGNN" has points at approximately (0.67, 0.07) and (0.69, 0.08). "Ours+FairGNN" has points at approximately (0.66, 0.008), (0.675, 0.02), and (0.695, 0.025). "EDITS" has a point at approximately (0.69, 0.05). "FGD+EDITS" has points at approximately (0.675, 0.02), (0.68, 0.022), and (0.695, 0.035).

Figure 5: Trade-off comparison between FairGNN, EDITS and FairGNN+FGD, EDITS+FGD on Credit dataset.

trade-off, demonstrating FGD's potential to boost other debias methods.

## 6 Related Work

Dataset Distillation & Knowledge Distillation. Dataset Distillation (DD) and Knowledge Distillation (KD) are methods to improve the efficiency of training deep neural networks. DD synthesizes a small dataset encapsulating the knowledge of a larger one, achieving comparable model performance [\[Wang et al., 2018,](#page-11-7) [Kim et al., 2022,](#page-10-13) [Lee et al., 2022,](#page-10-14) [Zhao et al., 2021a,](#page-11-3) [Yang et al., 2022\]](#page-11-8). It employs a bi-level optimization approach, with dataset condensation (DC) speeding up the process via gradient matching of model parameters. DD also helps with repeated training or privacy applications like continual learning, neural architecture search, and privacy-preserving scenarios. Meanwhile, graph data condensation methods have been developed for node and graph classification tasks [\[Jin et al., 2022\]](#page-10-7). KD, on the other hand, enhances computational efficiency through model compression and acceleration. It trains a compact student model using the knowledge from a larger teacher model [Gou et al.](#page-9-9) [\[2021\]](#page-9-9)To address the scarcity and high complexity of labeled data in GNNs, knowledge distillation (KD) was introduced to enhance existing GNNs [Liu et al.](#page-10-15) [\[2023a\]](#page-10-15), [Wang et al.](#page-11-9) [\[2023\]](#page-11-9), also for fairness problem [Dong et al.](#page-9-10) [\[2023\]](#page-9-10). While KD focuses on model compression, DD targets data compression, each improving efficiency from model-centric and data-centric perspectives.

Fair Graph Learning. Fairness in machine learning has attracted many research efforts[\[Chuang](#page-9-11)] [and Mroueh, 2020,](#page-9-11) [Zhang et al., 2018,](#page-11-10) [Du et al., 2021,](#page-9-12) [Jiang et al., 2022b,](#page-9-13) [Han et al., 2023,](#page-9-14) [Jiang et al.,](#page-9-15) [2023\]](#page-9-15). Many technologies are introduced in graph neural networks to achieve fair graph learning in node classification tasks, including optimization with regularization [\[Jiang et al., 2022a\]](#page-9-1), rebalancing [\[Zeng et al., 2021\]](#page-11-11), adversarial learning [\[Dai and Wang, 2021,](#page-9-6) [Bose and Hamilton, 2019,](#page-9-16) [Fisher et al.,](#page-9-17) [2020\]](#page-9-17) and graph rewiring [\[Köse and Shen, 2021,](#page-10-16) [Dong et al., 2022\]](#page-9-5). For link prediction, dyadic fairness and corresponding graph rewiring solutions are also developed in [\[Li et al., 2021\]](#page-10-17). Another line of work focuses on solving the individual fairness problem on the graph data [Song et al.](#page-10-18) [\[2022\]](#page-10-18), [Dong et al.](#page-9-18) [\[2021\]](#page-9-18), [Kang et al.](#page-10-19) [\[2020\]](#page-10-19).

## 7 Conclusion

Despite the ability of graph distillation to condense valuable graph data, this study finds that the vanilla method can worsen fairness issues. Therefore, we introduce a fair graph distillation process to generate fair distilled graph data. As the distilled graph lacks the nodes' sensitive attributes, conventional fair methods cannot be directly applied. However, we identify a consistent geometric phenomenon in graph distillation to estimate these sensitive attributes. We also introduce a new bias metric, coherence, and propose a bi-level optimization framework, FGD, for fair graph distillation. Experimental results validate FGD's effectiveness in mitigating bias while maintaining model utility across various GNN architectures and datasets. Future work will focus on addressing individual fairness issues and non-binary sensitive attribute conditions, among other aspects, as discussed in Appendix [H.](#page-16-0)

## Acknowledgements

We are deeply grateful to the National Science Foundation for their unwavering support. This research was substantially facilitated by the funding from grants IIS-1939716 and IIS-1900990.

## References

- <span id="page-9-7"></span>C. Agarwal, H. Lakkaraju, and M. Zitnik. Towards a unified framework for fair and stable graph representation learning. In *Uncertainty in Artificial Intelligence*, pages 2114–2124. PMLR, 2021.
- <span id="page-9-4"></span>A. Beutel, J. Chen, Z. Zhao, and E. H. Chi. Data decisions and theoretical implications when adversarially learning fair representations. *arXiv preprint arXiv:1707.00075*, 2017.
- <span id="page-9-16"></span>A. Bose and W. Hamilton. Compositional fairness constraints for graph embeddings. In *International Conference on Machine Learning*, pages 715–724. PMLR, 2019.
- <span id="page-9-11"></span>C.-Y. Chuang and Y. Mroueh. Fair mixup: Fairness via interpolation. In *International Conference on Learning Representations*, 2020.
- <span id="page-9-6"></span>E. Dai and S. Wang. Say no to the discrimination: Learning fair graph neural networks with limited sensitive attribute information. In *Proceedings of the 14th ACM International Conference on Web Search and Data Mining*, pages 680–688, 2021.
- <span id="page-9-18"></span>Y. Dong, J. Kang, H. Tong, and J. Li. Individual fairness for graph neural networks: A ranking based approach. In *Proceedings of the 27th ACM SIGKDD Conference on Knowledge Discovery & Data Mining*, pages 300–310, 2021.
- <span id="page-9-5"></span>Y. Dong, N. Liu, B. Jalaian, and J. Li. Edits: Modeling and mitigating data bias for graph neural networks. In *Proceedings of the ACM Web Conference 2022*, pages 1259–1269, 2022.
- <span id="page-9-10"></span>Y. Dong, B. Zhang, Y. Yuan, N. Zou, Q. Wang, and J. Li. Reliant: Fair knowledge distillation for graph neural networks. In *Proceedings of the 2023 SIAM International Conference on Data Mining (SDM)*, pages 154–162. SIAM, 2023.
- <span id="page-9-12"></span>M. Du, S. Mukherjee, G. Wang, R. Tang, A. H. Awadallah, and X. Hu. Fairness via representation neutralization. *arXiv preprint arXiv:2106.12674*, 2021.
- <span id="page-9-17"></span>J. Fisher, A. Mittal, D. Palfrey, and C. Christodoulopoulos. Debiasing knowledge graph embeddings. In *Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing (EMNLP)*, pages 7332–7345, 2020.
- <span id="page-9-9"></span>J. Gou, B. Yu, S. J. Maybank, and D. Tao. Knowledge distillation: A survey. *International Journal of Computer Vision*, 129:1789–1819, 2021.
- <span id="page-9-3"></span>W. Hamilton, Z. Ying, and J. Leskovec. Inductive representation learning on large graphs. *Advances in neural information processing systems*, 30, 2017.
- <span id="page-9-0"></span>X. Han, Z. Jiang, N. Liu, and X. Hu. G-mixup: Graph data augmentation for graph classification. *arXiv preprint arXiv:2202.07179*, 2022a.
- <span id="page-9-2"></span>X. Han, Z. Jiang, N. Liu, Q. Song, J. Li, and X. Hu. Geometric graph representation learning via maximizing rate reduction. In *Proceedings of the ACM Web Conference 2022*, pages 1226–1237, 2022b.
- <span id="page-9-14"></span>X. Han, Z. Jiang, H. Jin, Z. Liu, N. Zou, Q. Wang, and X. Hu. Retiring \$\delta \text{DP}\$: New distribution-level metrics for demographic parity. *Transactions on Machine Learning Research*, 2023. ISSN 2835-8856. URL <https://openreview.net/forum?id=LjDFIWWVVa>.
- <span id="page-9-8"></span>A. Ishizaka and P. Nemery. *Multi-criteria decision analysis: methods and software*. John Wiley & Sons, 2013.
- <span id="page-9-1"></span>Z. Jiang, X. Han, C. Fan, Z. Liu, N. Zou, A. Mostafavi, and X. Hu. Fmp: Toward fair graph message passing against topology bias. *arXiv preprint arXiv:2202.04187*, 2022a.
- <span id="page-9-13"></span>Z. Jiang, X. Han, C. Fan, F. Yang, A. Mostafavi, and X. Hu. Generalized demographic parity for group fairness. In *International Conference on Learning Representations*, 2022b.
- <span id="page-9-15"></span>Z. Jiang, X. Han, H. Jin, G. Wang, R. Chen, N. Zou, and X. Hu. Chasing fairness under distribution shift: a model weight perturbation approach. 2023.

- <span id="page-10-6"></span>W. Jin, L. Zhao, S. Zhang, Y. Liu, J. Tang, and N. Shah. Graph condensation for graph neural networks. *arXiv preprint arXiv:2110.07580*, 2021.
- <span id="page-10-7"></span>W. Jin, X. Tang, H. Jiang, Z. Li, D. Zhang, J. Tang, and B. Yin. Condensing graphs via one-step gradient matching. In *Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, pages 720–730, 2022.
- <span id="page-10-19"></span>J. Kang, J. He, R. Maciejewski, and H. Tong. Inform: Individual fairness on graph mining. In *Proceedings of the 26th ACM SIGKDD international conference on knowledge discovery & data mining*, pages 379–389, 2020.
- <span id="page-10-13"></span>J.-H. Kim, J. Kim, S. J. Oh, S. Yun, H. Song, J. Jeong, J.-W. Ha, and H. O. Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022.
- <span id="page-10-11"></span>T. N. Kipf and M. Welling. Semi-supervised classification with graph convolutional networks. *arXiv preprint arXiv:1609.02907*, 2016.
- <span id="page-10-16"></span>Ö. D. Köse and Y. Shen. Fairness-aware node representation learning. *arXiv preprint arXiv:2106.05391*, 2021.
- <span id="page-10-14"></span>S. Lee, S. Chun, S. Jung, S. Yun, and S. Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022.
- <span id="page-10-17"></span>P. Li, Y. Wang, H. Zhao, P. Hong, and H. Liu. On dyadic fairness: Exploring and mitigating bias in graph connections. In *International Conference on Learning Representations*, 2021.
- <span id="page-10-4"></span>Z. Li and D. Hoiem. Learning without forgetting. *IEEE transactions on pattern analysis and machine intelligence*, 40(12):2935–2947, 2017.
- <span id="page-10-0"></span>H. Ling, Z. Jiang, M. Liu, S. Ji, and N. Zou. Graph mixup with soft alignments. In *International Conference on Machine Learning*. PMLR, 2023a.
- <span id="page-10-1"></span>H. Ling, Z. Jiang, Y. Luo, S. Ji, and N. Zou. Learning fair graph representations via automated data augmentations. In *International Conference on Learning Representations*, 2023b.
- <span id="page-10-3"></span>H. Liu, K. Simonyan, and Y. Yang. Darts: Differentiable architecture search. *arXiv preprint arXiv:1806.09055*, 2018.
- <span id="page-10-15"></span>J. Liu, T. Zheng, G. Zhang, and Q. Hao. Graph-based knowledge distillation: A survey and experimental evaluation. *arXiv preprint arXiv:2302.14643*, 2023a.
- <span id="page-10-5"></span>Z. Liu, Z. Jiang, S. Zhong, K. Zhou, L. Li, R. Chen, S.-H. Choi, and X. Hu. Editable graph neural network for node classifications. *arXiv preprint arXiv:2305.15529*, 2023b.
- <span id="page-10-2"></span>Z. Liu, K. Zhou, Z. Jiang, L. Li, R. Chen, S.-H. Choi, and X. Hu. DSpar: An embarrassingly simple strategy for efficient GNN training and inference via degree-based sparsification. *Transactions on Machine Learning Research*, 2023c. ISSN 2835-8856. URL [https://openreview.net/](https://openreview.net/forum?id=SaVEXFuozg) [forum?id=SaVEXFuozg](https://openreview.net/forum?id=SaVEXFuozg).
- <span id="page-10-12"></span>C. Louizos, K. Swersky, Y. Li, M. Welling, and R. Zemel. The variational fair autoencoder. *arXiv preprint arXiv:1511.00830*, 2015.
- <span id="page-10-9"></span>N. Mehrabi, F. Morstatter, N. Saxena, K. Lerman, and A. Galstyan. A survey on bias and fairness in machine learning. *ACM Computing Surveys (CSUR)*, 54(6):1–35, 2021.
- <span id="page-10-8"></span>T. Nguyen, R. Novak, L. Xiao, and J. Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021.
- <span id="page-10-18"></span>W. Song, Y. Dong, N. Liu, and J. Li. Guide: Group equality informed individual fairness in graph neural networks. In *Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, pages 1625–1634, 2022.
- <span id="page-10-10"></span>H. Suresh and J. V. Guttag. A framework for understanding unintended consequences of machine learning. *arXiv preprint arXiv:1901.10002*, 2, 2019.

- <span id="page-11-5"></span>L. Takac and M. Zabovsky. Data analysis in public social networks. In *International scientific conference and international workshop present day trends of innovations*, volume 1. Present Day Trends of Innovations Lamza Poland, 2012.
- <span id="page-11-1"></span>A. Tong, J. Huang, G. Wolf, D. Van Dijk, and S. Krishnaswamy. Trajectorynet: A dynamic optimal transport network for modeling cellular dynamics. In *International conference on machine learning*, pages 9526–9536. PMLR, 2020.
- <span id="page-11-7"></span>T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-11-9"></span>Y. Wang, B. Hooi, Y. Liu, and N. Shah. Graph explicit neural networks: Explicitly encoding graphs for efficient and accurate inference. In *Proceedings of the Sixteenth ACM International Conference on Web Search and Data Mining*, pages 348–356, 2023.
- <span id="page-11-6"></span>F. Wu, A. Souza, T. Zhang, C. Fifty, T. Yu, and K. Weinberger. Simplifying graph convolutional networks. In *International conference on machine learning*, pages 6861–6871. PMLR, 2019.
- <span id="page-11-8"></span>S. Yang, Z. Xie, H. Peng, M. Xu, M. Sun, and P. Li. Dataset pruning: Reducing training data by examining generalization influence. *arXiv preprint arXiv:2205.09329*, 2022.
- <span id="page-11-0"></span>R. Ying, R. He, K. Chen, P. Eksombatchai, W. L. Hamilton, and J. Leskovec. Graph convolutional neural networks for web-scale recommender systems. In *Proceedings of the 24th ACM SIGKDD international conference on knowledge discovery & data mining*, pages 974–983, 2018.
- <span id="page-11-11"></span>Z. Zeng, R. Islam, K. N. Keya, J. Foulds, Y. Song, and S. Pan. Fair representation learning for heterogeneous information networks. In *Proceedings of the International AAAI Conference on Web and Social Media*, volume 15, pages 877–887, 2021.
- <span id="page-11-10"></span>B. H. Zhang, B. Lemoine, and M. Mitchell. Mitigating unwanted biases with adversarial learning. In *Proceedings of the 2018 AAAI/ACM Conference on AI, Ethics, and Society*, pages 335–340, 2018.
- <span id="page-11-3"></span>B. Zhao, K. R. Mopuri, and H. Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021a.
- <span id="page-11-4"></span>B. Zhao, K. R. Mopuri, and H. Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021b.
- <span id="page-11-2"></span>K. Zhou, Q. Song, X. Huang, and X. Hu. Auto-gnn: Neural architecture search of graph neural networks. *arXiv preprint arXiv:1909.03184*, 2019.

## A Training Algorithm

The training algorithm for fair graph distillation is shown in Algorithm [1.](#page-12-1)

<span id="page-12-1"></span>Algorithm 1 Fair Graph Distillation

**Input:** Training graph data  $G = \{X, A, S, Y\}$ , hyperparameters  $\alpha$ , temperature  $\gamma$ , number of alternative optimization step  $T_{alt}$ , distilled label  $Y'$ . Initialize  $X^{\dagger}$  based on real attributes, synthesizer model  $\phi$ , and GNNs model  $\theta$ . for  $t = 1$  to  $T_{alt}$  do 1. Train GNNs model using distilled graph  $G'_{t}$  and Equation [\(15\)](#page-5-0) to obtain  $GNN_{\theta_{t}}$ . 2. Given GNNs model  $GNN_{\theta_t}$ , calculate the gradient distance  $D(\nabla_{\theta} \mathcal{L}(\mathcal{G}), \nabla_{\theta} \mathcal{L}(\mathcal{G}_t'))$  over the real graph  $\mathcal G$  and distilled graph  $\mathcal G'_t$ . 3. Calculate coherence loss based on GNNs model  $GNN_{\theta_t}$ , real graph  $G$  and distilled graph  $G'_t$ . 4. Train synthesizer model using prediction loss as Equation [\(16\)](#page-5-1).

end for **Output:** The fair distilled graph  $\mathcal{G}' = \{A', X', Y'\}.$ 

<span id="page-12-0"></span>

## B Proof of Theorem [3.4](#page-2-5)

We consider GNNs model to learn node presentation  $z_i$  in the real graph G and then followed a linear classifier  $W = [w_0, \dots, w_{C-1}]$  and softmax layer, where  $w_j$  is the weight vector connected to the  $j$ -tj output neuron. We first focus on the relation between the latent representation and the gradient of the linear classification layer. It is easy to obtain the cross-entropy loss  $J_i$  ( $J'_i$ ) for *i*-th node with label  $y_i$  in real graph  $\mathcal G$  (distilled graph  $\mathcal G'$ ) as follows:

$$
J_i = -\log \frac{\exp(\boldsymbol{w}_{y_i}^{\top} \cdot \boldsymbol{z}_i)}{\sum_k \exp(\boldsymbol{w}_k^{\top} \cdot \boldsymbol{z}_i)},
$$
\n(17)

Then we define gradient over weight vector as  $g_{i,j} = \frac{\partial J_i}{\partial w_j}$  and  $g'_{i,j} = \frac{\partial J'_i}{\partial w_j}$  in the real and distilled graph. If  $j = y_i$ , we can obtain

$$
g_{i,y_i} = -\frac{\sum_k \exp(\boldsymbol{w}_{k}^{\top} \cdot \boldsymbol{z}_i)}{\exp(\boldsymbol{w}_{y_i}^{\top} \cdot \boldsymbol{z}_i)} \cdot \frac{\exp(\boldsymbol{w}_{y_i}^{\top} \cdot \boldsymbol{z}_i) \sum_k \exp(\boldsymbol{w}_{k}^{\top} \cdot \boldsymbol{z}_i) - \exp^2(\boldsymbol{w}_{y_i}^{\top} \cdot \boldsymbol{z}_i)}{\left(\sum_k \exp(\boldsymbol{w}_{k}^{\top} \cdot \boldsymbol{z}_i)\right)^2} \cdot \boldsymbol{z}_i
$$
$$
= -\boldsymbol{z}_i + \frac{\exp(\boldsymbol{w}_{y_i}^{\top} \cdot \boldsymbol{z}_i)}{\sum_k \exp(\boldsymbol{w}_{k}^{\top} \cdot \boldsymbol{z}_i)} \cdot \boldsymbol{z}_i \quad (18)
$$

Similarly, for  $j \neq y$ , we have

$$
\boldsymbol{g}_{i,j} = \frac{\exp(\boldsymbol{w}_{y_i}^{\top} \cdot \boldsymbol{z}_i)}{\sum_k \exp(\boldsymbol{w}_k^{\top} \cdot \boldsymbol{z}_i)} \cdot \boldsymbol{z}_i, \tag{19}
$$

In other words, the gradient of the loss for i-th node with label  $y_i$  with respect to the weight vector connected to the  $j$ -th output neuron is given by

$$
\boldsymbol{g}_{i,j} = \frac{\exp(\boldsymbol{w}_{y_i}^\top \cdot \boldsymbol{z}_i)}{\sum_k \exp(\boldsymbol{w}_k^\top \cdot \boldsymbol{z}_i)} \cdot \boldsymbol{z}_i - \mathbb{1}_{j=y_i} \boldsymbol{z}_i.
$$
 (20)

Based on Assumption [3.1,](#page-2-2) each model parameter in the last softmax layer satisfies the same distribution. In other words, the expectation of all predictions are the same, i.e.,

$$
\mathbb{E}_{\mathcal{P}_{\theta}}\Big[\frac{\exp(\boldsymbol{w}_0^{\top}\cdot\boldsymbol{z}_i)}{\sum_k \exp(\boldsymbol{w}_k^{\top}\cdot\boldsymbol{z}_i)}\Big]=\cdots=\mathbb{E}_{\mathcal{P}_{\theta}}\Big[\frac{\exp(\boldsymbol{w}_{C-1}^{\top}\cdot\boldsymbol{z}_i)}{\sum_k \exp(\boldsymbol{w}_k^{\top}\cdot\boldsymbol{z}_i)}\Big].
$$
 (21)

Note that the gradient calculation is based on backpropagation, the gradient for the last linear classification layer is quite critical for the gradient of other layers. Hence we consider the gradient of the last linear classification layer in the real graph, shown by

<span id="page-13-2"></span>
$$
\mathbb{E}_{{\theta \sim \mathcal{P}_{{\theta}}}} [ \nabla_{{\mathbf{w}_j}} \mathcal{L}(\mathcal{G}) ] = \mathbb{E}_{{\theta \sim \mathcal{P}_{{\theta}}}} [ \frac{1}{N} \sum_{i=1}^N \mathbf{g}_{i,j} ]
$$
$$
= \frac{1}{NC} \sum_{i=1}^N \mathbf{z}_i - \frac{1}{N} \sum_{{\{i: y_i = j\}}} \mathbf{z}_i, \tag{22}
$$

Similarly, we have the gradient of the last linear classification layer in the distilled graph as follows:

$$
\mathbb{E}_{{\theta} \sim \mathcal{P}_{{\theta}}} [ \nabla_{{\boldsymbol{w}}_j} \mathcal{L}(\mathcal{G}') ] = \mathbb{E}_{{\theta} \sim \mathcal{P}_{{\theta}}} [ \frac{1}{N'} \sum_{i=1}^N \boldsymbol{g}'_{i,j} ]
$$
$$
= \frac{1}{N'C} \sum_{i=1}^{N'} \boldsymbol{z}'_i - \frac{1}{N'} \sum_{{\{i: y'_i = j\}}} \boldsymbol{z}'_i, (23)
$$

Under assumption [3.2,](#page-2-3) it is easy to know that the optimal solution to minimizing the objective  $\min_{\mathcal{G}'} \mathbb{E}_{\theta \sim \mathcal{P}_{\mathbf{W}}} \left[ || \nabla_{\mathbf{W}} \mathcal{L}(\mathcal{G}) - \nabla_{\mathbf{W}} \mathcal{L}(\mathcal{G}') ||^2 \right]$  satisfy  $\nabla_{\mathbf{W}} \mathcal{L}(\mathcal{G}) = \nabla_{\mathbf{W}} \mathcal{L}(\mathcal{G}')$ . Since the distilled label is sampling to keep class label probability, we have  $\frac{|\{i:y_i=j\}|}{N'} = \frac{|\{i:y_i=j\}|}{N'}$  for any class index i. Therefore, based on Equations [\(22\)](#page-13-2) and [\(23\)](#page-13-3), we have the optimal distilled graph satisfy

<span id="page-13-3"></span>
$$
\frac{1}{N} \sum_{i=1}^{N} z_i = \frac{1}{N'} \sum_{i=1}^{N'} z'_i.
$$
 (24)

<span id="page-13-1"></span>

## C Proof of Ridge Regression

Define objective function  $J = \gamma \|z' - \pmb{Z}_s^\top \pmb{q}\|_2^2 + \|\pmb{q}\|_2^2$ , it is easy to obtain

$$
\frac{\partial J}{\partial \mathbf{q}} = -2\gamma \mathbf{Z}_s (z' - \mathbf{Z}_s^\top \mathbf{q}) + 2\mathbf{q} = 0 \tag{25}
$$

Therefore, the optimal  $q^* = \gamma (I + \gamma Z_s Z_s^{\top})^{-1} Z_s z'$ . Therefore, the projection of representation  $z'$ in the complement space of sensitive group  $Z_s$  is given by

$$
\mathbf{z}' - \mathbf{Z}_s^\top \mathbf{q}^* = \mathbf{z}' - \gamma \mathbf{Z}_s^\top (\mathbf{I} + \gamma \mathbf{Z}_s \mathbf{Z}_s^\top)^{-1} \mathbf{Z}_s \mathbf{z}' \tag{26}
$$

<span id="page-13-0"></span>

## D More Results on Consistent Span Space

We conduct experiments to measure the distance between  $span(Z)$  and  $span(Z')$  using principle angles between subspaces and emperically shows that  $span(Z) \approx span(Z')$  in the real dataset.

The concept of principal angle is used in linear algebra to measure the similarity between two subspaces of a vector space. It helps quantify how close or far apart these subspaces are. Given subspace,  $\mathbf{L}, \mathbf{M} \subseteq \mathbb{R}^n$ , with  $\dim \mathbf{L} = l \geq \dim \mathbf{M} = m$ , there are m principal angles between L and M denoted as  $0 \le \theta_1 \le \theta_2 \le \cdots \le \theta_m \le \frac{\pi}{2}$  between L and M are recursively defined, where

$$
\cos(\theta_i) := \min\left\{ \frac{\langle \mathbf{x}, \mathbf{y} \rangle}{\|\mathbf{x}\| \|\mathbf{y}\|} \mid \mathbf{x} \in \mathbf{L}, \mathbf{y} \in \mathbf{M}, \mathbf{x} \perp \mathbf{x}_k, \mathbf{y} \perp \mathbf{y}_k, k = 1, \cdots, i - 1 \right\}.
$$
 (27)

Notably, when the two subspaces are aligned, the principal angels are close to 0. We report the average principal angles of  $span(Z)$  and  $span(Z')$  on all datasets as following:

- Pokec-z:  $1.08 \times 10^{-6}$
- Pokec-n:  $1.03 \times 10^{-6}$
- German:  $4.84 \times 10^{-7}$

- Credit:  $2.57 \times 10^{-7}$
- Recidivism:  $3.87 \times 10^{-7}$

In the experiments, the principal angles of  $span(Z)$  and  $span(Z')$  on all dataset are nearly 0. This indicates that the distance between space  $span(Z')$  and space  $span(Z)$  are quite small in practice.

Additionally, we would like to mention that [1] provides the rigorous proof of  $z' \in span(Z)$ for distribution matching under several assumptions (although we can not prove it under gradient matching setting). According to formulas 21 from [1], it is assumed that (1) the \*\*linear extractor\*\*  $\psi_{\theta} : \mathbb{R}^d \to \mathbb{R}^k$  such that  $k < d, \theta = [\theta_{i,j}] \in \mathbb{R}^{k \times d}, \theta_{i,j} \stackrel{iid}{\sim} \mathcal{N}(0,1)$  and for an input  $\mathbf{z}, \psi_{\theta}(\mathbf{x}) = \theta \mathbf{z}.$ When using distribution match method for data condensation, we have:

$$
\frac{\partial L}{\partial \mathbf{z}'_i} = \frac{\partial E_{\theta}||d||^2}{\partial \mathbf{z}'_i} = -\frac{2}{|N'|}\left(\frac{1}{N}\sum_{j=1}^N \mathbf{z}_j - \frac{1}{N'}\sum_{j=1}^{N'} \mathbf{z}'_j\right)^T \cdot E[\boldsymbol{\theta}^t \boldsymbol{\theta}]
$$

where  $d := \theta \left( \frac{1}{N} \sum_{j=1}^{N} \mathbf{z}_j - \frac{1}{N'} \sum_{j=1}^{N'} \mathbf{z}'_j \right), \quad \mathbb{E} \left[ \boldsymbol{\theta}^\top \boldsymbol{\theta} \right] = k \mathbf{I}_d$  by definition of  $\theta$ , and  $\mathbf{I}_d$  is the identity matrix of  $\mathbb{R}^d$ . the projection components of span $(Z)^{\perp}$  remain zero throughout the optimization process of DM. And we use  $z_i$  to initialize  $z'_i$ , thus  $z' \in span(Z)$ . However, in the implementation we use gradient matching instead of distribution matching.

Table 3: Statistical Information on Datasets

| Dataset    | # Nodes | # Attributes | # Edges   | Avg. degree | Sens   | Label          |
|------------|---------|--------------|-----------|-------------|--------|----------------|
| Pokec-n    | 6,185   | 59           | 21,844    | 7.06        | Region | Working field  |
| Pokec-z    | 7,659   | 59           | 29,476    | 7.70        | Region | Working field  |
| German     | 1,000   | 27           | 21,242    | 44.50       | Gender | Credit status  |
| Credit     | 30,000  | 13           | 1,436,858 | 95.80       | Age    | Future default |
| Recidivism | 18,876  | 18           | 321,308   | 34.00       | Race   | Bail decision  |

<span id="page-14-0"></span>

## E Preliminary Motivation

We have added experiments comparing the fairness performance of various fair GNNs trained on synthetic and real graph data. Specifically, we report the results (using demographic parity (DP), equal opportunity (EO), and individual unfairness (IND) [Song et al.](#page-10-18) [\[2022\]](#page-10-18) as metrics) with EDITS [Dong](#page-9-5) [et al.](#page-9-5) [\[2022\]](#page-9-5), FairGNN [Dai and Wang](#page-9-6) [\[2021\]](#page-9-6), InFoRM [Kang et al.](#page-10-19) [\[2020\]](#page-10-19), and REDRESS [Dong](#page-9-18) [et al.](#page-9-18) [\[2021\]](#page-9-18) on five datasets in our paper. EDITS is a pre-processing debiasing method, FairGNN is an in-processing debiasing method, and InFoRM and REDRESS focus on individual fairness. We encountered out-of-memory (OOM) issues when implementing GUIDE and REDRESS on an NVIDIA GeForce RTX A5000 (24GB GPU memory), so we used InFoRM as the baseline. Due to the extensive training time required for REDRESS, we only report results on the German dataset for REDRESS. We use demographic parity (DP), equal opportunity (EO), and individual unfairness (IND) as metrics.Table [4](#page-15-0) demonstrates the result. From Table [4,](#page-15-0) we can see that in terms of the group fairness metrics (DP, EO), the fairness problem becomes uniformly worse on the Credit, German, and Pokecn datasets for all debiasing methods. For the Recidivism dataset, the distilled graph shows fewer fairness issues (lower DP or EO), especially for the EDITS method. This may result from the drop in utility of the model trained on the distilled graph (AUC is too low). As shown in Figure 4 of our paper, FGD can achieve a better performance-fairness trade-off compared to the baselines.

<span id="page-14-1"></span>

## F Dataset Statistics

Pokec. The Pokec dataset consists of millions of anonymized user profiles from Slovakia's most popular social network in 2012, with information such as gender, age, hobbies, interests, education, and working field. The dataset was sampled into Pokec-z and Pokec-n based on user province, with region as the sensitive attribute. The task is to predict user working field.

|                |                  |                 | Recidivism  |            | Credit      |       | German      |                 | Pokecn      |            | Pokecz      |
|----------------|------------------|-----------------|-------------|------------|-------------|-------|-------------|-----------------|-------------|------------|-------------|
|                |                  | Real            | Distillated | Real       | Distillated | Real  | Distillated | Real            | Distillated | Real       | Distillated |
| <b>EDITS</b>   | AUC <sub>1</sub> | 0.971           | 0.658       | 0.740      | 0.704       | 0.668 | 0.506       | <b>OOM</b>      | <b>OOM</b>  | <b>OOM</b> | <b>OOM</b>  |
|                | DP⊥              | 0.067           | 0.005       | 0.027      | 0.063       | 0.009 | 0.024       | <b>OOM</b>      | <b>OOM</b>  | <b>OOM</b> | OOM         |
|                | EOT              | 0.038           | 0.011       | 0.018      | 0.028       | 0.008 | 0.030       | $_{\text{COM}}$ | OOM         | OOM        | OOM         |
|                | AUC <sub>1</sub> | 0.977           | 0.788       | 0.759      | 0.720       | 0.742 | 0.645       | 0.782           | 0.676       | 0.784      | 0.723       |
| FairGNN        | DPT              | 0.065           | 0.046       | 0.062      | 0.123       | 0.010 | 0.013       | 0.005           | 0.044       | 0.042      | 0.037       |
|                | <b>EOT</b>       | 0.037           | 0.046       | 0.037      | 0.091       | 0.001 | 0.011       | 0.006           | 0.062       | 0.051      | 0.038       |
| <b>InFoRM</b>  | AUC <sub>1</sub> | 0.906           | 0.708       | 0.741      | 0.717       | 0.642 | 0.538       | 0.743           | 0.644       | 0.751      | 0.708       |
|                | DP.L             | 0.011           | 0.118       | 0.004      | 0.174       | 0.085 | 0.018       | 0.009           | 0.009       | 0.020      | 0.048       |
|                | EOT              | 0.024           | 0.092       | 0.001      | 0.135       | 0.153 | 0.017       | 0.013           | 0.015       | 0.018      | 0.038       |
|                | IND⊥             | 8098            | 3596022     | 2699       | 338149      | 4360  | 24888       | 6466            | 272013      | 6828       | 199853      |
| <b>REDRESS</b> | AUC <sub>1</sub> | $_{\text{COM}}$ | <b>OOM</b>  | OOM        | OOM         | 0.719 | 0.483       | <b>OOM</b>      | OOM         | <b>OOM</b> | OOM         |
|                | DPT              | 00M             | <b>OOM</b>  | <b>OOM</b> | <b>OOM</b>  | 0.005 | 0.043       | <b>OOM</b>      | <b>OOM</b>  | <b>OOM</b> | <b>OOM</b>  |
|                | <b>EOT</b>       | $\overline{OM}$ | <b>OOM</b>  | OOM        | <b>OOM</b>  | 0.010 | 0.073       | OOM             | <b>OOM</b>  | <b>OOM</b> | OOM         |
|                | IND.L            | OOM             | <b>OOM</b>  | <b>OOM</b> | <b>OOM</b>  | 9728  | 186366      | <b>OOM</b>      | <b>OOM</b>  | <b>OOM</b> | <b>OOM</b>  |

<span id="page-15-0"></span>Table 4: Utility and group fairness comparison between real graph and distilled graph with various debias method. Bold value indicates worse fairness performance.

<span id="page-15-1"></span>Table 5: Parameter study of  $\alpha$ . All the value is in scale of  $\times 10^2$ .

| $\alpha$ | AUC   | $\Delta_{DP}$ | $\Delta_{E O}$ | <b>Bias</b> |
|----------|-------|---------------|----------------|-------------|
| 0.04     | 74.75 | 0.84          | 0.88           | 0.19        |
| 0.5      | 69.42 | 0.66          | 0.43           | 0.15        |
| 0.6      | 69.37 | 0.58          | 0.16           | 0.14        |
| 1.0      | 65.35 | 0.00          | 0.00           | 0.11        |

German. The German Graph credit dataset has 1,000 client records with attributes like Gender and LoanAmount, used to classify individuals as good or bad credit risks. The similarity between node attributes is calculated using Minkowski distance and nodes are connected if the similarity is 80% of the maximum similarity.

Credit. Credit dataset, consisting of 30,000 individuals with features such as education, credit history, age, and derived spending and payment patterns. The similarity between two node attributes is calculated using Minkowski distance as the similarity measure and the credit defaulter graph network is constructed by connecting nodes with a similarity of 70% of the maximum similarity between all nodes.

Recidivism. The US state court bail outcome dataset (1990-2009) contains 18,876 defendant records with past criminal records, demographic attributes, etc. The similarity between node attributes is calculated using Minkowski distance and nodes are connected if the similarity is 60% of the maximum similarity.

## G More Experimental Details

### G.1 Parameter Study

Here we aim to study the sensitivity of FGD w.r.t. hyper-parameters. Specifically, we show the parameter study of  $\alpha$  on Recidivism dataset. Here  $\alpha$  controls the intensity to regularize the coherence bias of the distilled small graph. The results in Table [5](#page-15-1) indicate that  $\alpha$  can control the debiasing and utility performance of the distilled small graph.

### G.2 Implementation Details

Synthesizer training. We adopt Adam optimizer for synthesizer training with 0.0002 learning rate. MLP<sub> $\phi$ </sub> consists of 3 linear layer with 128 hidden dimension. The outer loop number is 16 while the inner loop is 4 for each epoch. For each experiment, we train with a maximum of 1200 epochs and 3 independent runs. The temperature parameter  $\gamma$  is set to 10.  $\bm{X}'$  and  $\phi$  are optimized alternatively.

GNN training. We adopt Adam optimizer for GNN training with 0.005 learning rate. All GNN models are 2 layers with 256 hidden dimensions. For Pokec-z, Pokec-n, German, Credit, and Recidivism the training epochs are 1500, 1500, 4000, 1000, and 1000 respectively.

<span id="page-16-1"></span>Image /page/16/Figure/0 description: The image displays two scatter plots, labeled (a) and (b). Plot (a) shows three clusters of points: blue points labeled 'Real s=0', orange points labeled 'Real s=1', and green points labeled 'Syn'. There are also two star markers: a red star labeled 'Real\_center' and a purple star labeled 'Syn\_center'. The x-axis ranges from approximately -1.5 to 2.5, and the y-axis ranges from approximately -0.1 to 0.2. Plot (b) shows points arranged in a semi-circular pattern. The points are colored blue ('Real s=0'), orange ('Real s=1'), green ('Vanilla'), and red ('FGD'). The x-axis ranges from 0.00 to 2.00, and the y-axis ranges from -1.00 to 1.00.

Figure 6: (a) shows the visualization of node representations from real graph and distilled graph, as well as their barycenter, on Credit dataset, after PCA. (b) shows the visualization of geometric intuition of node from real graph and distilled graph on Credit dataset.

### G.3 More Visualization

We also visualize the node representation using PCA. We could observe that the barycenter of node from real graph and distilled graph is very close. And The distribution of node representation after being normalized to the circumference is consistent with the geometric intuition shown in Figure [6.](#page-16-1)

<span id="page-16-0"></span>

## H Limitations and Future Work

### H.1 Non-binary Sensitive Attribute

For categorical sensitive attributes, if only one sensitive membership group's embeddings are far away from others, then the mean embeddings will still be close to the majority embeddings, especially for many categories, resulting in low variance (coherence). We argue that only this group with distant embedding (a small portion of samples) can have their sensitive attribute detected using embedding distributions. From a metric perspective, if we adopt the maximized  $\Delta_{DP}$  over any sensitive attribute group pair, the bias should be large due to considering the worst case. The proposed coherence may not work well in this scenario, and an advanced coherence can be developed for this case, e.g., the maximized variance over any sensitive group pair. We leave the advanced coherence development for categorical, multiple, or even continuous sensitive attributes in future work.

#### H.2 Individual Fairness

From Table [4,](#page-15-0) we find that all datasets suffer from a surprisingly more severe individual fairness problem (much higher IND score) when the model is trained on the distilled graph, even if we use InFoRM or REDRESS. This could be an interesting direction for future work, and we will add discussion with references in the related work section.

### H.3 Other Tasks

Our paper mainly focuses node classification tasks and it is possible to extend our method to other tasks or other group fairness problems. For instance, FGD may alleviate group fairness issues in link prediction tasks by reducing the coherence bias among different link groups. Exploring other tasks (e.g., recommendation, graph classification) or other fairness metrics (e.g., individual fairness, rank fairness) could be interesting for future work.
# Condensed Composite Memory Continual Learning

<PERSON> and <PERSON> *Institute of Signal Processing and System Theory University of Stuttgart* Stuttgart, Germany {felix.wiewel, bin.yang}@iss.uni-stuttgart.de

*Abstract*—Deep Neural Networks (DNNs) suffer from a rapid decrease in performance when trained on a sequence of tasks where only data of the most recent task is available. This phenomenon, known as catastrophic forgetting, prevents DNNs from accumulating knowledge over time. Overcoming catastrophic forgetting and enabling continual learning is of great interest since it would enable the application of DNNs in settings where unrestricted access to all the training data at any time is not always possible, e.g. due to storage limitations or legal issues. While many recently proposed methods for continual learning use some training examples for rehearsal, their performance strongly depends on the number of stored examples. In order to improve performance of rehearsal for continual learning, especially for a small number of stored examples, we propose a novel way of learning a small set of synthetic examples which capture the essence of a complete dataset. Instead of directly learning these synthetic examples, we learn a weighted combination of shared components for each example that enables a significant increase in memory efficiency. We demonstrate the performance of our method on commonly used datasets and compare it to recently proposed related methods and baselines.

*Index Terms*—Continual Learning, Dataset Condensation, Rehearsal

## I. INTRODUCTION

DNNs have achieved remarkable results on many challenging problems, e.g. computer vision, but still struggle on some important aspects when compared with the way humans learn. One crucial aspect where DNNs fall short is the continuous accumulation of knowledge when trained on a sequence of disjoint tasks. Exposed to such a setting where only the training data of the most recent task is available, DNNs suffer from the phenomenon of catastrophic forgetting [\[1\]](#page-7-0)–[\[3\]](#page-7-1), i.e. a rapid decrease in performance on previously learned tasks whenever a new task is trained. Although this phenomenon has been known since the early 1990s, it has only recently seen renewed interest in the scientific community in the form of many publications on methods to overcome catastrophic forgetting and some studies of the phenomenon itself. Solving the challenges associated with it would benefit a lot of applications and DNNs in general. One could move away from the current paradigm where all data must be accessible before training of a DNN can begin on to a setting where it can accumulate knowledge over time. Not only could this enable autonomous agents that continually learn from their environment but also applications where legal or privacy aspects prohibit the accumulation of large data sets of personal data. In the latter case, a spatial aggregation of all private data in one place could be replaced with a temporal aggregation through a DNN that is sequentially trained on small isolated parts of the whole dataset. Applications like these and the general advancement of DNNs and their abilities make the study of catastrophic forgetting and how to overcome it, also known as continual, continuous or life long learning in the literature, a relevant and interesting field in the area of artificial intelligence. Many different methods for continual learning published in the recent years can be roughly classified in three distinct categories.

I) Regularization based methods use some sort of loss term for a DNN in order to consolidate the knowledge acquired on previously learned classes. Canonical examples for this class of methods are *Elastic Weight Consolidation* (EWC) [\[4\]](#page-7-2) and *Synaptic Intelligence* (SI) [\[5\]](#page-7-3). Both use a weighted quadratic loss in order to penalize deviations from previously learned parameters. While EWC uses an estimate of the Fisher Information, SI uses the movement of a DNNs parameters and corresponding decrease in loss during training in order to measure the importance of a parameter to previously learned tasks.

II) Structural methods change the structure of a DNN in order to enable continual learning either by expanding it or by selectively disabling parts of the model in order to avoid interference between neural activities of different tasks. *Progressive Neural Networks* (PNN) [\[6\]](#page-7-4) expand a DNN by adding new layers and lateral connections between those and frozen ones that were trained on previous tasks. As the number of lateral connections increases dramatically when many new layers are added, scaling this method to bigger models is challenging. The only recently proposed method *A Neuromodulated Meta-Learning Algorithmn* (ANML) [\[7\]](#page-7-5) instead uses meta learning and a neuromodulatory network in order to gate inputs of a layer conditioned on the input.

III) Rehearsal with a small set of training examples is another common strategy for avoiding catastrophic forgetting. There exist several methods that use this approach in the literature. They mainly differ in the type of examples used for rehearsal, i.e. generated [\[8\]](#page-7-6), [\[9\]](#page-7-7), synthetic [\[10\]](#page-7-8), [\[11\]](#page-7-9) or stored images, in the way examples are selected [\[12\]](#page-7-10) and in the position at which the rehearsal data is injected into the model [\[13\]](#page-7-11) during training. Most methods, with the exception of generative replay, have the nice property that their performance scales with the number of examples that are stored for rehearsal. While *Bias Correction* (BiC) [\[14\]](#page-7-12) and *Remind* [\[15\]](#page-7-13) have shown that rehearsal can be scaled to a large scale, they store a significant amount of the training examples of the original dataset. Especially *Remind* stores roughly 960 thousand of the 1.2 million images of the Imagenet dataset in a compressed form. Although this is a viable way of increasing the performance of a rehearsal based method, it raises the question if there are more elegant ways than just trying to store more examples by means of an ever increasing compression ratio.

In this paper, we follow an alternative paradigm by learning synthetic examples for rehearsal directly in a compressed form. We base our approach<sup>[1](#page-1-0)</sup> on the inspiring work of Zhao et. al. [\[10\]](#page-7-8) which uses a novel gradient matching approach for condensation of a large dataset into a small number of synthetic examples. Instead of naively using dataset condensation to replace randomly selected samples in rehearsal, we propose an extension to the original method that is specifically designed for continual learning. We propose a novel way of how condensed examples are represented in order to further increase the memory efficiency. For this we use a weighted combination of learned components to represent each example. This enables us to share common features across different examples and therefore increase the memory efficiency significantly when compared to the original dataset condensation algorithm. Finally we demonstrate our methods performance on commonly used benchmarks for continual learning and compare it to recently proposed related methods and baselines.

## II. INCREMENTAL CLASS LEARNING

<span id="page-1-2"></span>While there are three commonly considered scenarios in the literature on continual learning, i.e. incremental task, incremental domain and incremental class learning, we focus on the latter one as it is the most challenging one. The reason for this is the lack of a task identifier that is commonly exploited in the other, less challenging, scenarios. In those the task identifier is commonly used to select a separate output layer for each task or domain. Since this layer then only has to solve the selected task, it effectively reduces the problem to a simpler one, e.g. a binary classification. While there might be a task identifier available at test time in some applications, it is not available in general and therefore restricts any method exploiting it to a limited number of applications. In incremental class learning, on the other hand, there is no task identifier and the DNN has only one output layer that is expanded with each newly learned class. In contrast to the scenarios where a task identifier is available, the DNN therefore has to solve a problem that becomes increasingly difficult with every added class. For detailed definitions of all scenarios and their challenges we refer the reader to [\[16\]](#page-7-14), [\[17\]](#page-7-15).

In incremental class learning, a sequence of  $N_T$  classification tasks  $\mathcal{T}_{1 \leq i \leq N_T} = {\mathbf{x}_j^i, y_j^i | 1 \leq j \leq M_i}$ , each represented by a set of  $M_i$  input-output pairs, is considered. The j-th input of the *i*-th task  $x_j^i \in \mathbb{R}^S$  is given by a real-valued vector, e.g. the  $S$  pixels of an image, and its corresponding

target value  $y_j^i \in \mathbb{Z}$  is an integer class label. These tasks typically contain only examples of two or more classes that are not present in any other task. A standard technique to construct these is to split an image classification dataset into disjoint sets. Commonly used examples are the MNIST [\[18\]](#page-7-16), FashionMNIST [\[19\]](#page-7-17), SVHN [\[20\]](#page-7-18) and CIFAR10 [\[21\]](#page-7-19) datasets that are split into five task containing two classes in increasing order, i.e.  $y_j^1 \in \{0, 1\}$ ,  $y_j^2 \in \{2, 3\}$ , ...,  $y_j^5 \in \{8, 9\}$ . The resulting datasets are called SplitMNIST, SplitFashionMNIST, SplitSVHN and SplitCIFAR10. Given these task definitions, the goal in incremental class learning is to train a DNN  $f_{\theta}(\mathbf{x})$ on the sequence  $\mathcal{T}_1, \mathcal{T}_2, \dots, \mathcal{T}_{N_T}$  such that an empirical risk [\[22\]](#page-7-20)  $\mathcal{L}$  is minimized over all of them once training on the sequence is complete. Formally this can be written as

$$
\boldsymbol{\theta}^{\star} = \arg \min_{\boldsymbol{\theta}} \sum_{i=1}^{N_T} \sum_{j=1}^{M_i} \mathcal{L}(f_{\boldsymbol{\theta}}(\mathbf{x}_j^i), y_j^i), \tag{1}
$$

where  $\mathcal{L}: \mathbb{R}^S \times \mathbb{Z} \to \mathbb{R}$  is typically chosen as the crossentropy loss between the DNNs prediction  $f_{\theta}(\mathbf{x}_j^i)$  and a onehot encoding of the class label  $y_j^i$ . This setup is different from a standard learning problem as during the training on task  $\mathcal{T}_i$ only its corresponding dataset is available to the DNN. This leads to two major challenges that have to be overcome for continual learning. First, the DNN has to be stable enough during training such that the knowledge learned on previous tasks  $\mathcal{T}_1, \ldots, \mathcal{T}_{i-1}$  is retained. Additionally, it has to be plastic enough to learn additional concepts of the newly trained tasks. Satisfying both of these contradicting requirements at the same time is known as the stability-plasticity dilemma in the literature [\[23\]](#page-7-21). Since DNNs tend to be very plastic, i.e. they can approximate almost any function with a large enough number of parameters rather quickly, they suffer from catastrophic forgetting and typically need additional measures to ensure stability.

## III. PROPOSED METHOD

As our method is based on dataset condensation, we first introduce the basic idea and gradient matching as a suitable loss in section [III-A.](#page-1-1) We then proceed to present the novel approach of a composite memory in section [III-B](#page-2-0) and finally summarize our method in [III-C.](#page-3-0)

<span id="page-1-1"></span>

### A. Dataset Condensation

Consider a given dataset  $\mathcal{D} = {\mathbf{x}_i^D, y_i^D}_{i=1}^M$  of M inputs  $\mathbf{x}_i \in \mathbb{R}^S$  and their corresponding targets  $\mathbf{y}_i \in \mathbb{Z}$ , a DNN  $f_{\theta}(\mathbf{x})$ with parameters  $\theta$  and an empirical risk  $\mathcal{L}(f_{\theta}(\mathbf{x}), y) \in \mathbb{R}$ . The goal of dataset condensation is to find a small set of synthetic input-output pairs  $S = {\mathbf{x}_{i}^{S}, y_{i}^{S}}_{i=1}^{N}$ , where  $N \ll M$ , such that the optimal parameters

$$
\boldsymbol{\theta}_D^* = \arg\min_{\boldsymbol{\theta}} \sum_{i=1}^M \mathcal{L}(f_{\boldsymbol{\theta}}(\mathbf{x}_i^D), y_i^D)
$$
 (2)

<span id="page-1-0"></span><sup>&</sup>lt;sup>1</sup>Code available at https://github.com/FelixWiewel/CCMCL

found on the original dataset are similar to the optimal parameters

$$
\boldsymbol{\theta}_{S}^{\star} = \arg\min_{\boldsymbol{\theta}} \sum_{i=1}^{N} \mathcal{L}(f_{\boldsymbol{\theta}}(\mathbf{x}_{i}^{S}), y_{i}^{S})
$$
(3)

obtained using the synthetic dataset  $S$ . Depending on how this similarity is measured, different algorithms for obtaining the small synthetic dataset  $S$  can be derived. The authors of [\[11\]](#page-7-9) propose to learn the synthetic examples and a learning rate in such a way that one gradient decent step on them leads to a high performance on the original test set. In order to find a solution that generalizes well on many different random initializations of the DNN parameters  $\theta$ , they further propose to minimize the expected empirical risk when training on  $S$ where the expectation is taken over many different random initializations. In their inspiring work [\[10\]](#page-7-8), Zhao et. al. propose two different ways of measuring the aforementioned similarity. First they argue that minimizing the distance in parameter space is a possible but computationally expensive method of learning  $S$  as it results in a bi-level optimization problem where the inner loop involves training a potentially largescale model many times. In order to avoid this they propose to exploit the dynamics during training on both the original dataset  $D$  and the synthetic one  $S$  by matching their gradients. The intuition behind this is that the trajectory of  $\theta$  through the parameter space when trained on  $S$  should be similar to a training on the original dataset  $D$ . This can be enforced by

$$
\min_{\mathcal{S}} \mathbb{E}_{\boldsymbol{\theta}_0 \sim P_{\boldsymbol{\theta}_0}} \left[ \sum_{t=1}^T D(\nabla_{\boldsymbol{\theta}} \mathcal{L}_{\mathcal{D}}(\boldsymbol{\theta}_t), \nabla_{\boldsymbol{\theta}} \mathcal{L}_{\mathcal{S}}(\boldsymbol{\theta}_t)) \right], \quad (4)
$$

where the expectation is taken over many different random initializations of  $\theta$  following the distribution  $P_{\theta_0}$ ,  $D(\cdot, \cdot)$  is some distance measure, and  $\nabla_{\theta} \mathcal{L}_{\mathcal{D}}(\theta_t)$  as well as  $\nabla_{\theta} \mathcal{L}_{\mathcal{S}}(\theta_t)$ ) are gradients of the empirical risk w.r.t.  $\theta$  on the original and synthetic datasets  $D$  and  $S$  at training step t. For a more detailed description on how to arrive at Eq. [4](#page-2-1) the reader is referred to [\[10\]](#page-7-8). While there are many possible distance measures  $D(\cdot, \cdot)$ , Zhao et. al. propose

$$
D(\mathbf{A}, \mathbf{B}) = \sum_{i=1}^{out} \left( 1 - \frac{\mathbf{A}_i \cdot \mathbf{B}_i}{\|\mathbf{A}_i\| \|\mathbf{B}_i\|} \right),\tag{5}
$$

where  $A_i$  and  $B_i$  are flattened vectors of the weights corresponding to the *i*-th output node and  $\|\cdot\|$  is the euclidean norm. For a fully connected layer with a weight matrix  $\mathbf{W} \in \mathbb{R}^{out \times in}$ , this results in the cosine similarity between the gradients of individual output neurons. For a convolutional layer with a kernel tensor  $\mathbf{W} \in \mathbb{R}^{h \times w \times in \times out}$ , a simple assignment to individual neurons is not possible. It is also interesting to note that the contribution of biases for both fully connected and convolutional layers is ignored. But similarly to Zhao et. al. we found that this distance enables the usage of a single learning rate for all layers in a network and therefore we adopt it for all our experiments.

Image /page/2/Figure/7 description: This is a line graph showing storage requirement versus the number of examples. The y-axis is on a logarithmic scale and ranges from 10^5 to 10^6. The x-axis ranges from 0 to 1000 examples. There are two lines plotted: one red line labeled 'Individual' and several blue lines labeled 'Ours' with different values of P (P=10, P=20, P=40, P=80, P=160, P=320). The 'Individual' line shows a rapidly increasing storage requirement that starts at approximately 10^5 for 0 examples and reaches approximately 8\*10^5 for 1000 examples. The 'Ours' lines show a much slower increase in storage requirement. For P=320, the storage requirement starts at approximately 2\*10^5 and reaches approximately 6\*10^5 for 1000 examples. For P=10, the storage requirement starts at approximately 10^5 and reaches approximately 2\*10^5 for 1000 examples. The other 'Ours' lines fall between these values.

<span id="page-2-3"></span>Fig. 1. Storage requirement for  $32 \times 32$  RGB-Images of the proposed composite memory for different values of  $P$  and individual storing. Note the logarithmic scale of the Y-axis.

<span id="page-2-0"></span>

### B. Composite Memory

Although dataset condensation using gradient matching as shown in Eq. [4](#page-2-1) yields quite impressive results, its memory efficiency can be improved by exploiting similarities between examples of one particular class. For this, we try to decompose the learned synthetic data into a fixed number of shared components. We base this approach on the observation that instances of one specific class typically posses common features that can be shared among them and therefore only need to be stored once. Individual examples can then be reconstructed from a weighted combination of these learned common features. Formally we propose to represent each example  $x_i$  as a function

<span id="page-2-2"></span>
$$
\mathbf{x}_{j} = \sigma \left( \sum_{i=1}^{P} w_{ij} \mathbf{c}_{i} \right), \qquad (6)
$$

<span id="page-2-1"></span>where  $w_{ij} \in \mathbb{R}$  is the mixing coefficient of the *i*-th component  $c_i \in \mathbb{R}^S$  and  $\sigma : \mathbb{R}^S \to \mathbb{R}^S$  is the element-wise applied Sigmoid function. This allows us to learn these shared features and their contributions to every example separately. In contrast to the original dataset condensation proposed in [\[10\]](#page-7-8), we can therefore significantly increase the memory efficiency by sharing features among the stored examples instead of explicitly saving them multiple times. In order to quantify this, we consider an example of storing  $Q$   $S$ -dimensional examples independently as proposed in [\[10\]](#page-7-8) and according to Eq. [6.](#page-2-2) Storing them individually requires  $B_{ind} = Q \times S$ real numbers. Assuming  $P$  shared features  $c_i$  our method requires storing  $B_{cm} = P \times (S + Q)$  real numbers. If  $S \gg Q$ , which typically holds true for high dimensional inputs and a small number of examples stored for rehearsal, the storage requirement  $B_{cm}$  of our method is approximately constant while  $B_{ind}$  grows linearly with the number of stored examples Q. Fig. [1](#page-2-3) visualizes the storage requirement of these two approaches in case of the CIFAR10 dataset. Even though it contains relatively small RGB images with  $32 \times 32$  pixels

the storage requirement of our method remains approximately constant as the number of stored examples Q increases.

While our method of representing examples by a weighted combination of components is similar to the well known Principal Component Analysis (PCA) [\[24\]](#page-7-22), it is fundamentally different in the way components are learned. Rather than trying to find a representation of the data that captures most of the variance in it, we use the gradient matching loss from Eq. [4](#page-2-1) in order to learn the components to match the gradients of an original training dataset. This effectively combines the data compression aspect of the PCA with dataset condensation and enables learning a set of synthetic training examples in a compressed form. Although this can improve performance, it also adds new hyper parameters, the number of learned components  $c_i$  and weights  $w_{ij}$ , to the dataset condensation algorithm which need to be selected. How we select them in this work is described in section [IV-C.](#page-4-0)

<span id="page-3-1"></span>

## Algorithm 1 Dataset condensation

**Require:** Buffer  $\mathcal{B}$ , task  $\mathcal{T}_i$ , composite memory  $\mathcal{M}$ , training optimizer  $opt_T$ , condensation optimizer  $opt_C$ , training batch size  $B_T$ , condensation batch size  $B_C$ , training learning rate  $\gamma_T$ , condensation learning rate  $\gamma_C$ , outer iterations  $K$ , inner iterations  $T$ , matching iterations  $I$ , training iterations J, DNN  $f_{\theta}$ 

Ensure: Updated buffer  $\beta$ 

- 1: Initialize composite memory  $c_i$  and  $w_{ij}$
- 2: for  $K$  iterations do
- 3: Reinitialize model  $f_{\theta}$
- 4: for T iterations do
- 5: Sample from one class of task  $\mathcal{B}_T \sim \mathcal{T}_i$
- 6: Sample from composite memory  $\mathcal{B}_M \sim \mathcal{M}$
- 7: Compute gradient  $\mathbf{g}_T = \nabla_{\theta} \sum_{\mathbf{x}, y \in \mathcal{B}_T} \mathcal{L}(f_{\theta}(\mathbf{x}), \mathbf{y})$
- 8: Compute gradient  $\mathbf{g}_M = \nabla_{\theta} \sum_{\mathbf{x}, y \in \mathcal{B}_M} \mathcal{L}(f_{\theta}(\mathbf{x}), \mathbf{y})$

9: **for**  $I$  iterations **do** 10: Update composite memory

- $\mathcal{M} \leftarrow opt_C(D(\mathbf{g}_T, \mathbf{g}_M), \gamma_C, I)$
- 11: end for
- 12: Sample from buffer  $B_B \sim B$
- 13: **for**  $J$  iterations **do**

```
14: Update DNN parameters
```

```
f_{\theta} \leftarrow opt_T(\sum_{\mathbf{x},y\in\mathcal{B}_B}\bigcap_{\mathcal{B}_T}\mathcal{L}(f_{\theta}(\mathbf{x}), \mathbf{y}), \gamma_T, J)15: end for
```

- 16: end for
- 17: end for
- 18: Update buffer  $\mathcal{B} \leftarrow \mathcal{B} \cap \mathcal{M}$

<span id="page-3-0"></span>

### C. Algorithm

For our method we combine both, dataset condensation as described in section [III-A](#page-1-1) and our novel composite memory outlined in [III-B,](#page-2-0) and adopt them to the incremental class learning problem as stated in section [II.](#page-1-2) Similarly to all rehearsal based methods, we utilize a small buffer  $\beta$  in order to retain what was previously learned and avoid catastrophic forgetting. But instead of storing input-output pairs of a task

<span id="page-3-2"></span>

## Algorithm 2 Training procedure

that are selected directly from the training data, we use dataset condensation to condense all available data into a small set of synthetic samples. Algorithm [1](#page-3-1) describes our approach to dataset condensation using a composite memory.

Given a task  $\mathcal{T}_i$ , we start by randomly initializing the composite memory for this task using a uniform distribution over the interval [0, 1] for the components  $c_i$  and a standard normal distribution for the mixing coefficients  $w_{ij}$ . Similar to [\[10\]](#page-7-8) we then randomly initialize the DNN that is used for condensation in an outer loop as we intend to learn synthetic examples that are useful for many different initializations. Next we sample minibatches of data from the task that we want to condense and data from our composite memory. It is crucial for our method and the original dataset condensation that this data is sampled from one class only as condensing examples from multiple classes at the same time is much more difficult and leads to worse results. While this restriction might be quite different from standard DNN training, it fits naturally into the incremental class learning problem where only a few classes are present in each task. This means we run Algorithm [1](#page-3-1) independently for each class of a given task. The sampled minibatches are then used to compute the corresponding gradients using the same DNN parameters  $\theta$ and loss function  $\mathcal{L}$ . Next the condensation optimizer opt<sub>C</sub> is used to minimize the distance between gradients computed on the training and synthetic data according to the distance  $D(\cdot, \cdot)$  from Eq. [4.](#page-2-1) We minimize this distance for I steps with a step size of  $\gamma_C$  in order to learn the components and mixing coefficients of our composite memory. Differently from the original dataset condensation proposed by Zhao et. al. we then sample examples from the rehearsal buffer  $B$ , combine them with the data sampled in step 5 and use the training optimizer  $opt_T$  to update the DNNs parameters with a learning rate of  $\gamma_T$  for J iterations. This is done to more closely resemble the DNNs training with a small rehearsal buffer during condensation and obtain synthetic samples that behave well in incremental class learning. Additionally it avoids training the DNN using only data from the current task which contains only a small number of classes and therefore can lead to overfitting on them. Besides the composite memory

<span id="page-4-1"></span>TABLE I HYPER PARAMETERS USED THROUGHOUT ALL OF OUR EXPERIMENTS.

| Parameter                             | Value    |
|---------------------------------------|----------|
| Training learning rate $\gamma_T$     | 0.01     |
| Training iterations $S$               | 500/1000 |
| Condensation learning rate $\gamma_C$ | 0.1      |
| Outer iterations $K$                  | 100      |
| Inner iterations $T$                  | 10       |
| Matching iterations $I$               | 10       |
| Training iterations $J$               | 1        |
| Training batch size $B_T$             | 128      |
| Condensation batch size $B_C$         | 256      |

this is another important difference between our method that is specifically designed for continual learning and the original dataset condensation. Finally the rehearsal buffer  $\beta$  is updated with the learned components and mixing coefficients.

The training procedure of our method as described in Algorithm [2](#page-3-2) differs from naive rehearsal. Instead of training on the data of a task directly, we first condense it and then train on the synthetic samples. We do this in order to speed up training as there are much fewer synthetic examples than original ones. During our experiments we also noticed this leads to a more stable training and overall better results.

## IV. EXPERIMENTS

### A. Datasets

In order to asses the effectiveness of our method we test it on a variety of datasets commonly used for continual learning. These are the MNIST and FashionMNIST dataset containing  $28 \times 28$  pixel gray scale images of digits and clothes as well as the SVHN and CIFAR10 datasets featuring  $32 \times 32$  pixel RGB images of house numbers from Google Street View and natural objects. All of these datasets have in common that they contain 10 classes that can be split up into a sequence of tasks as described in section [II.](#page-1-2) For this we choose to split them into five tasks containing two classes in ascending order, i.e.  $y_j^1 \in \{0, 1\}, y_j^2 \in \{2, 3\}, \dots, y_j^5 \in \{8, 9\}.$ 

#### B. DNN Architecture

In our experiments we use the same DNN for all datasets. It is based on a modular architecture that is popular in the field of few-shot learning [\[25\]](#page-7-23), [\[26\]](#page-7-24) and was also used by Zhao et. al. for their experiments on dataset condensation. It consists of 3 structurally identical blocks containing a convolutional layer with 128 filters of size  $3 \times 3$  that is followed by an Instance Normalization layer [\[27\]](#page-7-25), a ReLU activation and an average pooling block that averages blocks of size  $2 \times 2$ . The output layer is a fully connected dense layer with Softmax activation. Similar to [\[10\]](#page-7-8) our experiments suggest that normalization layers like InstanceNorm provide better results than none or batch normalization. We agree with their reasoning that the latter performs worse than other normalization techniques because batch statistics can not be estimated reliably for a small number of examples. Furthermore no data augmentation or other data pre-processing except normalization to the interval  $[0, 1]$  is applied to the inputs.

<span id="page-4-0"></span>

### C. Hyper parameters

We choose the same hyper parameters for all experiments and avoided elaborate tuning of them. The only exception to this are the number of training and outer loop iterations S and K. On the MNIST and FashionMNIST datasets a lower number of 500 training and 50 outer loop iterations are sufficient while on SVHN and CIFAR we double these in order to ensure convergence. Only a rough manual optimization was performed. During this we did not notice any particularly sensitive hyper parameters. The visual quality and performance increased with the number of iterations for all iteration parameters and an increase in the distillation batch size. This is to be expected as it leads to more and bigger mini batches being used during condensation. Our methods performance and its memory usage also depends strongly on the number of learned components  $_i$  and weights  $w_{ij}$ . While we select the same number of components for our method as the other methods are allowed to store examples in their buffer, we learn twice the number of weights associated to them. This leads to a slightly higher storage requirement when we store the same number of components as the other methods store examples. TABLE [I](#page-4-1) contains a summary of the hyper parameters used in our experiments.

### D. Baselines

We compare our method to several baselines and related methods. The simplest but still strong baseline is naive rehearsal of examples. For this, individual training samples are randomly selected and stored in the buffer. We select the same amount of examples per class in order to not introduce any bias towards a particular class. During training we randomly sample images from the rehearsal buffer and mix them with the same amount of training images from the new task in order to form a mini batch. This introduces a bias towards classes of the most recently trained task which is studied in more detail by [\[14\]](#page-7-12). In order to correct for this bias the authors propose their BiC method which we include in our experiments as a recently proposed rehearsal based method for continual learning. As we are dealing with small buffer sizes we use all stored examples instead of keeping a separate validation set for BiC. Another baseline is the application of dataset condensation as a replacement of random selection in naive rehearsal. Here the original algorithm from [\[10\]](#page-7-8) is changed slightly by including samples from the rehearsal buffer as described in section [III-C.](#page-3-0) To ensure a fair comparison of these methods with ours we use the same DNN architecture, shared hyper parameters and buffer size for all methods.

<span id="page-4-2"></span>

### E. Results

Fig. [2](#page-5-0) shows the accuracy on a sequence of five tasks after the final task was trained. Numerical values are provided in TABLE [II.](#page-7-26) Since we are interested in how the methods perform for different buffer sizes, we plot the average accuracy across all tasks over the number of examples stored in the buffer. A good performance for a small number of stored examples is generally desirable since it means more tasks can be stored in

Image /page/5/Figure/0 description: This image contains four line graphs, each representing the average accuracy of different methods across varying buffer sizes for four datasets: MNIST, FashionMNIST, SVHN, and CIFAR10. The y-axis for all graphs represents 'Avg. accuracy', ranging from approximately 0.6 to 0.95 for MNIST, 0.55 to 0.75 for FashionMNIST, 0.3 to 0.75 for SVHN, and 0.25 to 0.45 for CIFAR10. The x-axis represents 'Buffer size', with ranges of 20 to 100 for MNIST and FashionMNIST, and 100 to 500 for SVHN and CIFAR10. Four lines are plotted in each graph, representing different methods: 'Naive rehearsal' (red circles), 'BiC' (blue diamonds), 'Dataset cond.' (purple stars), and 'Ours' (gray diamonds). In general, the 'Ours' method consistently achieves the highest average accuracy across all datasets and buffer sizes, followed by 'Dataset cond.', then 'BiC', and finally 'Naive rehearsal' which shows the lowest accuracy. The gap between the methods tends to narrow as the buffer size increases.

<span id="page-5-0"></span>Fig. 2. Average accuracy and standard deviation of different methods on the tested datasets over five independent runs. For naive rehearsal and dataset condensation the buffer size is the total number of examples stored in the buffer after all tasks have been trained. For our method we convert the storage requirement into an equivalent number of examples in order to compare with the other methods.

a fixed size buffer without compromising on performance. The average accuracy and its standard deviation is determined over five independent runs with different random initializations. Our method outperforms naive rehearsal and BiC by a large margin on all datasets. This is especially evident for small buffer sizes. On the less challenging dataset MNIST the gap between our method and the other methods closes quite quickly as the buffer size increases. This is due to the model reaching an average accuracy that is close to the upper bound of training on all tasks simultaneously. For the more challenging datasets, e.g. SVHN and CIFAR10, our method can maintain its advantage for much longer since on these datasets much more examples in the rehearsal buffer are needed to reach the upper bound. Our composite memory as described in section [III-B](#page-2-0) improves performance significantly when compared with the original dataset condensation. While there is a small but consistent improvement on MNIST for buffer sizes larger than 40, a more significant increase in performance can be observed on the more challenging datasets FashionMNIST, SVHN and CIFAR10. We attribute this to dataset condensation being able to synthesize many more representative examples than simply storing them. This is due to the features of a particular class in these datasets being much more diverse than on MNIST whose classes are very narrowly defined. It is also interesting to note that our method using the composite memory is matched by dataset condensation for a buffer size of 20 on MNIST and FashionMNIST. Here only one component per class is stored which seems to be insufficient for outperforming dataset condensation that can save two independent examples.

### F. Qualitative visual comparison

Fig. [3](#page-6-0) shows images stored in the buffer for naive rehearsal, dataset condensation and our method using the composite memory described in section [III-B.](#page-2-0) These are obtained at the end of training on the incremental class learning sequence when the buffer is completely filled and all classes are trained. When comparing images stored by naive rehearsal with dataset condensation and our method, there is a noticeable difference in visual clarity. Images that are learned, i.e. dataset condensation and our method, lack some detail and clearly defined

textures. They also seem to be corrupted by noise. Although these images might be considered inadequate if the task we were trying to solve would be learning a generative model, they are arguably better images when it comes to using them for rehearsal in an incremental class learning setup. This is objectively quantified by the results discussed in section [IV-E.](#page-4-2) This can also be evaluated subjectively in case of the SVHN dataset. Here the images that are stored by naive rehearsal feature not only the centered digit that defines the class label but also neighboring digits of the house number. In the cases of methods that learn synthetic images for rehearsal, dataset condensation and our method, only the center digit is present. This indicates that the latter two methods learn only the important class defining features and ignore what is irrelevant for the task at hand. It is further interesting to compare dataset condensation with our method. The components learned using the composite memory seem to contain distinctive features of their class. But while some of them are bright others in the same image are dark. This can easily be seen in the case of FashionMNIST where some T-Shirts and Pullovers body has high and the arms have low intensity. This in addition to the weighted combination of all components results in much more diverse examples from our method when compared with dataset condensation. It is also interesting to note that images learned by dataset condensation lack diversity in their intensity.

## V. CONCLUSION

We proposed a novel rehearsal based method for continual learning which is based on dataset condensation. For this a small set of synthetic training examples is learned by matching the gradients it produces during training with those from examples of the original dataset. In order to even further increase the performance and memory efficiency of rehearsal with dataset condensation, we propose a composite memory that shares features of a particular class among the learned synthetic examples. This leads to a significant increase in memory efficiency and performance when compared to other rehearsal based methods as we can share common features and therefore only need to store them once.

Image /page/6/Figure/0 description: This image is a grid displaying examples of data from four different datasets: MNIST, FashionMNIST, SVHN, and CIFAR10. The grid is organized into four rows, labeled 'Rehearsal', 'Condensation', 'Components', and 'Composite images'. Each column represents one of the datasets. The MNIST column shows handwritten digits. The FashionMNIST column shows images of clothing items like shirts, pants, and shoes. The SVHN column displays images of house numbers with digits. The CIFAR10 column contains colorful images of various objects like animals, vehicles, and everyday items. The 'Rehearsal' row shows original examples from each dataset. The subsequent rows ('Condensation', 'Components', 'Composite images') appear to show processed or generated versions of these datasets, with varying degrees of clarity and abstraction.

<span id="page-6-0"></span>Fig. 3. Images stored in a buffer of size 100 for naive rehearsal, dataset condensation and composite memory. The first two row shows examples used in naive rehearsal. Images learned using dataset condensation as proposed by [\[10\]](#page-7-8) are shown in row two. The learned components of our method are shown in row three while the last row shows the 200 composite images that are a weighted combination of the components above. Note that all images were normalized into the range [0, 1] for plotting which leads to a loss in contrast for dataset condensation images and components as they are learned and individual pixels of them can be outside this interval.

### TABLE II

<span id="page-7-26"></span>AVERAGE ACCURACY AND STANDARD DEVIATION OF OUR EXPERIMENTS. \*OVERHEAD APPLIES ONLY TO OUR METHOD AND DESCRIBES THE ADDITIONAL STORAGE REQUIREMENT OF THE WEIGHTS  $w_{ij}$  measured IN EQUIVALENT EXAMPLES.

| Buffer size         | 20                | 40                 | 60                | 80                | 100               |
|---------------------|-------------------|--------------------|-------------------|-------------------|-------------------|
| Overhead*           | 0.10              | 0.41               | 0.92              | 1.63              | 2.55              |
| <b>MNIST</b>        |                   |                    |                   |                   |                   |
| Naive rehearsal     | 61.4              | 78.3               | 85.2              | 87.3              | 89.5              |
|                     | $	ext{	extpm}2.9$ | $	ext{	extpm}1.46$ | $	ext{	extpm}0.7$ | $	ext{	extpm}1.1$ | $	ext{	extpm}1.3$ |
| BiC                 | 67.4              | 81.6               | 87.2              | 88.8              | 90.5              |
|                     | $	ext{	extpm}4.4$ | $	ext{	extpm}2.9$  | $	ext{	extpm}1.3$ | $	ext{	extpm}0.9$ | $	ext{	extpm}1.6$ |
| Dataset cond.       | 85.9              | 91.1               | 92.7              | 93.8              | 94.1              |
|                     | $	ext{	extpm}0.8$ | $	ext{	extpm}0.8$  | $	ext{	extpm}0.3$ | $	ext{	extpm}0.3$ | $	ext{	extpm}0.3$ |
| Ours                | 86.0              | 92.6               | 93.5              | 94.3              | 95.0              |
|                     | $	ext{	extpm}2.7$ | $	ext{	extpm}0.4$  | $	ext{	extpm}0.4$ | $	ext{	extpm}0.2$ | $	ext{	extpm}0.1$ |
| <b>FashionMNIST</b> |                   |                    |                   |                   |                   |
| Naive rehearsal     | 54.9              | 61.4               | 67.1              | 69.8              | 71.6              |
|                     | $	ext{	extpm}0.9$ | $	ext{	extpm}1.2$  | $	ext{	extpm}1.4$ | $	ext{	extpm}1.1$ | $	ext{	extpm}1.7$ |
| BiC                 | 59.7              | 68.0               | 72.1              | 73.3              | 73.7              |
|                     | $	ext{	extpm}4.7$ | $	ext{	extpm}2.3$  | $	ext{	extpm}1.0$ | $	ext{	extpm}1.5$ | $	ext{	extpm}1.8$ |
| Dataset cond.       | 69.6              | 72.6               | 73.7              | 75.8              | 76.1              |
|                     | $	ext{	extpm}2.5$ | $	ext{	extpm}1.4$  | $	ext{	extpm}1.3$ | $	ext{	extpm}0.8$ | $	ext{	extpm}0.7$ |
| Ours                | 69.5              | 74.5               | 76.3              | 77.0              | 77.5              |
|                     | $	ext{	extpm}2.5$ | $	ext{	extpm}2.0$  | $	ext{	extpm}0.8$ | $	ext{	extpm}0.8$ | $	ext{	extpm}0.9$ |
| Buffer size         | 100               | 200                | 300               | 400               | 500               |
| Overhead*           | 0.65              | 2.60               | 5.86              | 10.4              | 16.3              |
| <b>SVHN</b>         |                   |                    |                   |                   |                   |
| Naive rehearsal     | 32.3              | 48.9               | 55.4              | 59.2              | 63.9              |
|                     | $	ext{	extpm}1.8$ | $	ext{	extpm}2.9$  | $	ext{	extpm}1.0$ | $	ext{	extpm}0.7$ | $	ext{	extpm}0.6$ |
| BiC                 | 43.1              | 60.1               | 65.3              | 70.0              | 72.7              |
|                     | $	ext{	extpm}1.6$ | $	ext{	extpm}2.0$  | $	ext{	extpm}0.9$ | $	ext{	extpm}0.9$ | $	ext{	extpm}1.0$ |
| Dataset cond.       | 53.2              | 63.4               | 69.4              | 70.9              | 71.3              |
|                     | $	ext{	extpm}0.8$ | $	ext{	extpm}1.0$  | $	ext{	extpm}1.1$ | $	ext{	extpm}0.3$ | $	ext{	extpm}1.1$ |
| Ours                | 67.0              | 72.9               | 74.1              | 74.7              | 75.8              |
|                     | $	ext{	extpm}0.8$ | $	ext{	extpm}0.7$  | $	ext{	extpm}0.7$ | $	ext{	extpm}0.5$ | $	ext{	extpm}0.5$ |
| <b>CIFAR10</b>      |                   |                    |                   |                   |                   |
| Naive rehearsal     | 24.0              | 30.4               | 34.7              | 38.4              | 41.2              |
|                     | $	ext{	extpm}0.8$ | $	ext{	extpm}0.5$  | $	ext{	extpm}0.5$ | $	ext{	extpm}0.5$ | $	ext{	extpm}0.5$ |
| BiC                 | 25.5              | 34.1               | 38.8              | 43.2              | 44.8              |
|                     | $	ext{	extpm}1.1$ | $	ext{	extpm}1.1$  | $	ext{	extpm}0.4$ | $	ext{	extpm}1.0$ | $	ext{	extpm}0.9$ |
| Dataset cond.       | 36.6              | 40.5               | 42.1              | 44.0              | 46.2              |
|                     | $	ext{	extpm}0.7$ | $	ext{	extpm}0.4$  | $	ext{	extpm}0.9$ | $	ext{	extpm}0.9$ | $	ext{	extpm}0.7$ |
| Ours                | 39.1              | 43.3               | 44.7              | 46.2              | 47.9              |
|                     | $	ext{	extpm}0.7$ | $	ext{	extpm}0.4$  | $	ext{	extpm}0.4$ | $	ext{	extpm}0.4$ | $	ext{	extpm}0.7$ |

### REFERENCES

- <span id="page-7-0"></span>[1] M. McCloskey and N. J. Cohen, "Catastrophic interference in connectionist networks: The sequential learning problem," in *Psychology of learning and motivation*. Elsevier, 1989, vol. 24, pp. 109–165.
- [2] R. M. French, "Catastrophic forgetting in connectionist networks," *Trends in cognitive sciences*, vol. 3, no. 4, pp. 128–135, 1999.
- <span id="page-7-1"></span>[3] S. Lewandowsky and S.-C. Li, "Catastrophic interference in neural networks: causes, solutions, and data," in *Interference and inhibition in cognition*. Elsevier, 1995, pp. 329–361.
- <span id="page-7-2"></span>[4] J. Kirkpatrick, R. Pascanu, N. Rabinowitz, J. Veness, G. Desjardins, A. A. Rusu, K. Milan, J. Quan, T. Ramalho, A. Grabska-Barwinska *et al.*, "Overcoming catastrophic forgetting in neural networks," *Proceedings of the national academy of sciences*, vol. 114, no. 13, pp. 3521–3526, 2017.
- <span id="page-7-3"></span>[5] F. Zenke, B. Poole, and S. Ganguli, "Continual learning through synaptic intelligence," *Proceedings of machine learning research*, vol. 70, p. 3987, 2017.
- <span id="page-7-4"></span>[6] A. A. Rusu, N. C. Rabinowitz, G. Desjardins, H. Soyer, J. Kirkpatrick, K. Kavukcuoglu, R. Pascanu, and R. Hadsell, "Progressive neural networks," *arXiv preprint arXiv:1606.04671*, 2016.

- <span id="page-7-5"></span>[7] S. Beaulieu, L. Frati, T. Miconi, J. Lehman, K. O. Stanley, J. Clune, and N. Cheney, "Learning to continually learn," *arXiv preprint arXiv:2002.09571*, 2020.
- <span id="page-7-6"></span>[8] H. Shin, J. K. Lee, J. Kim, and J. Kim, "Continual learning with deep generative replay," in *Advances in Neural Information Processing Systems*, 2017, pp. 2990–2999.
- <span id="page-7-7"></span>[9] M. van der Ven and A. S. Tolias, "Generative replay with feedback connections as a general strategy for continual learning," *CoRR*, vol. abs/1809.10635, 2018. [Online]. Available: [http://arxiv.org/abs/1809.](http://arxiv.org/abs/1809.10635) [10635](http://arxiv.org/abs/1809.10635)
- <span id="page-7-8"></span>[10] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," *arXiv preprint arXiv:2006.05929*, 2020.
- <span id="page-7-9"></span>[11] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-7-10"></span>[12] Z. Borsos, M. Mutnỳ, and A. Krause, "Coresets via bilevel optimization for continual learning and streaming," *arXiv preprint arXiv:2006.03875*, 2020.
- <span id="page-7-11"></span>[13] L. Pellegrini, G. Graffieti, V. Lomonaco, and D. Maltoni, "Latent replay for real-time continual learning," *arXiv preprint arXiv:1912.01100*, 2019.
- <span id="page-7-12"></span>[14] Y. Wu, Y. Chen, L. Wang, Y. Ye, Z. Liu, Y. Guo, and Y. Fu, "Large scale incremental learning," in *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, 2019, pp. 374–382.
- <span id="page-7-13"></span>[15] T. L. Hayes, K. Kafle, R. Shrestha, M. Acharya, and C. Kanan, "Remind your neural network to prevent catastrophic forgetting," in *European Conference on Computer Vision*. Springer, 2020, pp. 466–483.
- <span id="page-7-14"></span>[16] Y.-C. Hsu, Y.-C. Liu, A. Ramasamy, and Z. Kira, "Re-evaluating continual learning scenarios: A categorization and case for strong baselines," *arXiv preprint arXiv:1810.12488*, 2018.
- <span id="page-7-15"></span>[17] G. M. van de Ven and A. S. Tolias, "Three scenarios for continual learning," *arXiv preprint arXiv:1904.07734*, 2019.
- <span id="page-7-16"></span>[18] Y. LeCun, C. Cortes, and C. Burges, "Mnist handwritten digit database," *ATT Labs [Online]. Available: http://yann.lecun.com/exdb/mnist*, vol. 2, 2010.
- <span id="page-7-17"></span>[19] H. Xiao, K. Rasul, and R. Vollgraf, "Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms," *CoRR*, vol. abs/1708.07747, 2017. [Online]. Available: [http://arxiv.org/abs/1708.](http://arxiv.org/abs/1708.07747) [07747](http://arxiv.org/abs/1708.07747)
- <span id="page-7-18"></span>[20] Y. Netzer, T. Wang, A. Coates, A. Bissacco, B. Wu, and A. Y. Ng, "Reading digits in natural images with unsupervised feature learning," 2011.
- <span id="page-7-19"></span>[21] A. Krizhevsky, "Learning multiple layers of features from tiny images," Tech. Rep., 2009.
- <span id="page-7-20"></span>[22] V. Vapnik, "Principles of risk minimization for learning theory," in *Advances in neural information processing systems*, 1992, pp. 831–838.
- <span id="page-7-21"></span>[23] G. A. Carpenter and S. Grossberg, "Art 2: self-organization of stable category recognition codes for analog input patterns," *Appl. Opt.*, vol. 26, no. 23, pp. 4919–4930, Dec 1987. [Online]. Available: <http://ao.osa.org/abstract.cfm?URI=ao-26-23-4919>
- <span id="page-7-22"></span>[24] K. Pearson, "Liii. on lines and planes of closest fit to systems of points in space," *The London, Edinburgh, and Dublin Philosophical Magazine and Journal of Science*, vol. 2, no. 11, pp. 559–572, 1901.
- <span id="page-7-23"></span>[25] J. Snell, K. Swersky, and R. Zemel, "Prototypical networks for few-shot learning," in *Advances in neural information processing systems*, 2017, pp. 4077–4087.
- <span id="page-7-24"></span>[26] O. Vinyals, C. Blundell, T. Lillicrap, K. Kavukcuoglu, and D. Wierstra, "Matching networks for one shot learning," in *Proceedings of the 30th International Conference on Neural Information Processing Systems*, 2016, pp. 3637–3645.
- <span id="page-7-25"></span>[27] D. Ulyanov, A. Vedaldi, and V. S. Lempitsky, "Instance normalization: The missing ingredient for fast stylization," *CoRR*, vol. abs/1607.08022, 2016. [Online]. Available:<http://arxiv.org/abs/1607.08022>
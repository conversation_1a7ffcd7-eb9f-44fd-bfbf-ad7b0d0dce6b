# Diversity-Driven Synthesis: Enhancing Dataset Distillation through Directed Weight Adjustment

<PERSON><PERSON><PERSON><sup>1,2</sup> <PERSON><PERSON><sup>1,2,3</sup> <PERSON><PERSON> Hu<sup>4</sup> <PERSON><PERSON><sup>1,2,5</sup> <PERSON><sup>1,2 $\boxtimes$ </sup> <sup>1</sup> Centre for Frontier AI Research (CFAR), Agency for Science, Technology and Research (A\*STAR), Singapore <sup>2</sup> Institute of High Performance Computing, Agency for Science, Technology and Research (A\*STAR), Singapore  $3XiDian University, Xi'an, China <sup>4</sup>National University of Singapore, Singapore$ <sup>5</sup>Hubei University, WuHan, China

## Abstract

The sharp increase in data-related expenses has motivated research into condensing datasets while retaining the most informative features. Dataset distillation has thus recently come to the fore. This paradigm generates synthetic datasets that are representative enough to replace the original dataset in training a neural network. To avoid redundancy in these synthetic datasets, it is crucial that each element contains unique features and remains diverse from others during the synthesis stage. In this paper, we provide a thorough theoretical and empirical analysis of diversity within synthesized datasets. We argue that enhancing diversity can improve the parallelizable yet isolated synthesizing approach. Specifically, we introduce a novel method that employs dynamic and directed weight adjustment techniques to modulate the synthesis process, thereby maximizing the representativeness and diversity of each synthetic instance. Our method ensures that each batch of synthetic data mirrors the characteristics of a large, varying subset of the original dataset. Extensive experiments across multiple datasets, including CI-FAR, Tiny-ImageNet, and ImageNet-1K, demonstrate the superior performance of our method, highlighting its effectiveness in producing diverse and representative synthetic datasets with minimal computational expense. Our code is available at [https://github.com/AngusDujw/Diversity-Driven-Synthesis.](https://github.com/AngusDujw/Diversity-Driven-Synthesis)

# 1 Introduction

With the rapid growth in dataset size and the need for efficient data storage and processing [\[8,](#page-9-0) [17,](#page-10-0) [14,](#page-9-1) [13\]](#page-9-2), how to condense datasets while preserving their key characteristics becomes a significant challenge in machine learning community  $[12, 38]$  $[12, 38]$  $[12, 38]$ . Unlike previous research  $[29, 39, 50, 44]$  $[29, 39, 50, 44]$  $[29, 39, 50, 44]$  $[29, 39, 50, 44]$  $[29, 39, 50, 44]$  $[29, 39, 50, 44]$  $[29, 39, 50, 44]$  that focuses on constructing a representative subset through selecting from the original data, *Dataset Distillation* [\[43,](#page-11-4) [31,](#page-10-2) [20\]](#page-10-3) aims to synthesize a small and compact dataset that retains informative features from the original dataset. A model trained on the synthetic dataset is thus supposed to achieve comparable performance as one trained on the original dataset. The development of dataset distillation reduces data-related costs [\[7,](#page-9-4) [34,](#page-11-5) [49\]](#page-11-6) and helps us better understand how Deep Neural Networks (DNNs) extract knowledge from large-scale datasets.

Numerous studies dedicate significant effort to synthesizing distilled datasets more effectively. For example, Zhao *et al.* employ a gradient-matching approach [\[52,](#page-12-0) [54\]](#page-12-1) to guide the synthesis process. Trajectory-matching methods  $[1, 2, 5, 6]$  $[1, 2, 5, 6]$  $[1, 2, 5, 6]$  $[1, 2, 5, 6]$  $[1, 2, 5, 6]$  $[1, 2, 5, 6]$  $[1, 2, 5, 6]$  further align gradient trajectories to optimize the synthetic data. Additionally, distribution matching  $[42, 53, 55]$  $[42, 53, 55]$  $[42, 53, 55]$  $[42, 53, 55]$  $[42, 53, 55]$  and kernel inducing points methods [\[28,](#page-10-4) [25,](#page-10-5) [23,](#page-10-6) [24\]](#page-10-7) also contribute to synthesizing representative data. Despite the great progress achieved by these methods on datasets like CIFAR [\[16\]](#page-10-8), their extensive computational overhead (both GPU memory and GPU time) hinders the extension of these methods to large-scale datasets like ImageNet-1K [\[3\]](#page-9-9).

Email: <EMAIL>, <EMAIL>.  $\boxtimes$  represents the corresponding author.

<sup>38</sup>th Conference on Neural Information Processing Systems (NeurIPS 2024).

Image /page/1/Figure/0 description: The image contains two plots. The left plot is a t-SNE visualization on CIFAR-100, showing data points colored blue for SRe2L and red stars for DWA (ours), overlaid with contour lines representing density. The right plot is a bar chart titled "Performance Comparison". It compares the accuracy (%) of SRe2L and DWA (ours) on three datasets: Tiny-ImageNet, ImageNet-1K, and CIFAR100. For Tiny-ImageNet, SRe2L has an accuracy of 41.1% and DWA (ours) has 52.8%. For ImageNet-1K, SRe2L has 46.8% and DWA (ours) has 55.2%. For CIFAR100, SRe2L has 47.9% and DWA (ours) has 60.3%.

Figure 1: Left: t-SNE visualization of logit embeddings on CIFAR-100 [\[16\]](#page-10-8) dataset. The scatter plot illustrates the distribution of synthetic data instances distilled by SRe2L (blue dots) and our DWA method (red stars). The blue density contours represent the distribution of natural data instances. Our DWA method demonstrates a more diverse and widespread distribution compared to SRe2L [\[46\]](#page-11-8), indicating better generalization and coverage of the feature space. Right: The consequent performance improvement of DWA in various datasets. Experiments are conducted with 50 images per class.

<span id="page-1-0"></span>Several recent works [\[2,](#page-9-6) [46,](#page-11-8) [22,](#page-10-9) [51,](#page-12-4) [57\]](#page-12-5) have attempted to address the efficiency issues of dataset distillation. In particular, Yin *et al.* [\[46\]](#page-11-8) propose a lightweight distillation method, SRe2L, which successfully condenses the large-scale dataset ImageNet-1K. Unlike previous methods [\[1,](#page-9-5) [53,](#page-12-2) [15\]](#page-10-10) that treat the synthetic set as a unified entity to utilize the mutual influences among synthetic instances, SRe2L synthesizes each synthetic data instance individually. As such, SRe2L significantly reduces both GPU memory costs and computational overhead.

Individually synthesizing each data instance can efficiently parallelize opti-

mization tasks, thereby flexibly managing GPU memory usage and computational overhead. However, this approach may present challenges in ensuring the representativeness and diversity of each instance. If each instance is synthesized in isolation, there may be a risk of missing the holistic view of the data characteristics, which is crucial for the training of generalized neural networks. Intuitively, SRe2L might expect that random initialization of synthetic data would provide sufficient diversity to prevent homogeneity in the synthetic dataset. Nevertheless, our analysis, as demonstrated in [Figure 1,](#page-1-0) reveals that this initialization contributes only marginally to diversity. Conversely, the Batch Normalization (BN) loss [\[45\]](#page-11-9) in SRe2L plays the practical role in enhancing diversity of the distilled dataset.

Motivated by these findings, we further investigate the factors that enhance the diversity of synthetic datasets from a theoretical perspective. We reveal that the variance regularizer in the BN loss is the key factor ensuring diversity. Conversely, the mean regularizer within the same BN loss unexpectedly constrains diversity. To resolve this contradiction, we suggest a decoupled coefficient to specifically strengthen the variance regularizer's role in promoting diversity. Experimental results validate our hypothesis. We further propose a dynamic mechanism to adjust the weight parameters of the teacher model. Serving as the sole source of supervision from the original dataset, the teacher model guides the synthesis comprehensively. Our meticulously designed weight perturbation mechanism injects randomness without compromising the informative supervision, thereby improving overall performance. Importantly, our method incurs negligible additional computations  $( $0.1\%$ ).$ Intuitively, our method perturbs the weight in a direction that reflects the characteristics of a large subset, varying with each batch of synthesized data.

We conduct extensive experiments across various datasets, including CIFAR-10, CIFAR-100, Tiny-ImageNet, and ImageNet-1K, to verify the effectiveness of our proposed method. The superior performance of our method not only validates our hypothesis but also demonstrates its ability to enhance the diversity of synthetic datasets. This success guides further investigations into searching for representative synthetic datasets for lossless dataset distillation. Our contribution can be summarized as follows:

- We analyze the diversity of the synthetic dataset in dataset distillation both theoretically and empirically, identifying the importance of ensuring diversity in isolated synthesizing approaches.
- We propose a dynamic adjustment mechanism to enhance the diversity of the synthesized dataset, incurring negligible additional computations while significantly improving overall performance. Extensive experiments on various datasets verify the remarkable performance of our method.

# 2 Preliminaries

Notation and Objective. Given a real and large dataset  $\mathcal{T} = \{(\tilde{x}_i, y_i)\}_{i=1}^{|\mathcal{T}|}$ , Dataset Distillation aims to synthesize a tiny and compact dataset  $S = \{(\tilde{s}_i, y_i)\}_{i=1}^{|S|}$ . The samples in  $\mathcal T$  are drawn i.i.d from a natural distribution D, while the samples in S are optimized from scratch. We use  $\theta_{\mathcal{T}}$  and  $\theta_S$  to represent the converged weight trained on T and S, respectively. We define a neural network  $h = q \circ f$ , where q acts as the feature extractor and f as the classifier. The feature extractor and the classifier loaded with the corresponding weight parameters from  $\theta$  are denoted by  $q_\theta$  and  $f_\theta$ .

Throughout the paper, we explore the properties of synthesized datasets within the latent space. We transform both  $\tilde{x}, \tilde{s} \in \mathbb{R}^{C \times H \times W}$  from the pixel space, to the latent space,  $x, s \in \mathbb{R}^d$ , for better formulation. This transformation is given by  $x = g_{\theta_{\tau}}(\tilde{x})$  and  $s = g_{\theta_{\tau}}(\tilde{s})$ . The objective of Dataset Distillation is to ensure that a model h trained on the synthetic dataset S is able to achieve a comparable test performance as the model trained with  $\mathcal{T}$ , which can be formulated as,

$$
\mathop{\mathbb{E}}_{\boldsymbol{x}\sim\mathcal{D}}\left[\ell\left(h_{\theta_{\mathcal{T}}},\boldsymbol{x}\right)\right]\simeq \mathop{\mathbb{E}}_{\boldsymbol{x}\sim\mathcal{D}}\left[\ell\left(h_{\theta_{\mathcal{S}}},\boldsymbol{x}\right)\right],\tag{1}
$$

where  $\ell$  can be an arbitrary loss function. The expression  $\ell(h_{\theta_{\mathcal{T}}},x)$  should be interpreted as  $\ell(h_{\theta_{\mathcal{T}}}, \boldsymbol{x}, \boldsymbol{y})$ , where  $\boldsymbol{y}$  is the ground truth label.

**Synthesizing S.** A series of previous works mentioned in [Section 5](#page-8-0) have introduced various methods to synthesize  $S$ . Specifically, SRe2L  $[46]$  proposes an efficient and effective synthesizing method, which optimizes each synthetic instance  $s_i$  by solving the following minimization proble[m:](#page-2-0)

<span id="page-2-1"></span>
$$
\underset{\boldsymbol{s}_{i}\in\mathbb{R}^{d}}{\arg\min}\left[\ell\left(f_{\theta_{\mathcal{T}}},\boldsymbol{s}_{i}\right)+\lambda\mathcal{L}_{\mathrm{BN}}\left(f_{\theta_{\mathcal{T}}},\boldsymbol{s}_{i}\right)\right],\tag{2}
$$

where  $\mathcal{L}_{\rm BN}$  denotes the BN loss, and  $\lambda$  is the coefficient of  $\mathcal{L}_{\rm BN}$ . The detailed definition of  $\mathcal{L}_{\rm BN}$ can be found in [Equation 3.](#page-3-0) Minimizing the BN loss  $\mathcal{L}_{BN}$  significantly enhances the performance of SRe2L, which is designed to ensure that S aligns with the same normalization distribution as  $\mathcal{T}$ . However, we argue that another essential but overlooked aspect of the BN loss  $\mathcal{L}_{BN}$  is its role in introducing diversity to  $S$ , which also greatly benefits the final performance. In the following section, we will analyze this issue in greater detail.

# 3 Methodology

Diversity in the synthetic dataset  $S$  is essential for effective use of the limited distillation budget. This section reveals that the BN loss, referenced in [Equation 2,](#page-2-1) enhances  $S$ 's diversity. However, the suboptimal setting of BN loss limits this diversity. To overcome this, we propose a dynamic adjustment mechanism for the weight parameters of  $f_{\theta_{\tau}}$ , enhancing diversity during synthesis. Finally, we detail our algorithm and theoretically demonstrate its effectiveness. The pseudocode of our proposed DWA can be found in Algorithm [1.](#page-2-2)

Algorithm 1 Directed Weight Adjustment (DWA)

<span id="page-2-2"></span>Input: Original dataset  $T$ ; Number of iterations  $T$ ; Image per class ipc; Number of steps  $K$ , magnitude  $\rho$  to solve the weight adjustment  $\Delta\theta$ ; Learning rate  $\eta$ ; A network  $f_{\theta\tau}$  with weight

parameter  $\theta_{\mathcal{T}}$ ,  $f_{\theta_{\mathcal{T}}}$  is well trained on  $\mathcal{T}$ . 1: Initialize  $S = \{\}, \Delta \theta_0 = \mathbf{0}_{\dim(\theta_{\mathcal{T}})}$ 2: for  $i = 1$  to ipc do 3: Randomly select one instance for each class from  $\mathcal{T}$ , to initialize  $\mathcal{S}_0^i$ , *i.e.*, 4:  $\mathcal{S}_0^i = \{(\boldsymbol{x}_i, \boldsymbol{y}_i) \mid (\boldsymbol{x}_i, \boldsymbol{y}_i) \in \mathcal{T} \text{ and each } \boldsymbol{y}_i \text{ is unique}\}\$ 5: ▷ Compute the adjustment of weights  $\Delta\theta$  by solving [Equation 11](#page-4-0) 6: **for**  $k = 1$  to  $K$  **do** 7:  $\Delta \theta_k = \Delta \theta_{k-1} + \frac{\rho}{K} \nabla L_{\mathcal{S}_0^i} (f_{\theta_{\mathcal{T}} + \Delta \theta_{k-1}})$ 8:  $\Delta \theta = \Delta \theta_K$  ⊳ Directed Weight Adjustment ⇒ 9:  $\triangleright$  Optimize  $S^i$ 

10: **for**  $\hat{t} = 1$  to T **do** 

11: 
$$
\mathcal{S}_t^i = \mathcal{S}_{t-1}^i + \eta \nabla_{\mathcal{S}} \mathcal{L}(f_{\theta_{\mathcal{T}} + \widetilde{\Delta \theta}}, \mathcal{S}_{t-1}^i)
$$

12:  $S = S \cup \{S^i\}$ 

**Output:** Synthetic dataset  $S$ 

Directed Weight Adjustr

 $\triangleright$   $\mathcal{L}$  is defined in [Equation 15](#page-4-1)

<span id="page-2-0"></span>In the actual optimization process, operations occur within the pixel space using the entire network  $h_{\theta_{\mathcal{T}}}$ . However, as we discuss the optimization in the latent space, we only consider solutions within this space. Then, we transform the solution in latent space back into pixel space as  $\tilde{s} = g_{\theta \tau}^{-1}(s)$ .

<span id="page-3-4"></span>

## 3.1 Batch Normalization Loss Enhances Diversity of S

The BN loss  $\mathcal{L}_{BN}$  comprises mean ( $\mathcal{L}_{mean}$ ) and variance ( $\mathcal{L}_{var}$ ) components, defined as follows:

$$
\mathcal{L}_{\text{BN}} = \mathcal{L}_{\text{mean}} + \mathcal{L}_{\text{var}} \quad \text{where} \quad \mathcal{L}_{\text{mean}} \left( f_{\theta_{\mathcal{T}}}, \mathbf{s}_{i} \right) = \sum_{l} \|\mu_{l}(\mathbb{S}) - \mu_{l}(\mathcal{T})\|_{2},
$$
\n
$$
\text{and} \quad \mathcal{L}_{\text{var}} \left( f_{\theta_{\mathcal{T}}}, \mathbf{s}_{i} \right) = \sum_{l} \left\| \sigma_{l}^{2}(\mathbb{S}) - \sigma_{l}^{2}(\mathcal{T}) \right\|_{2}, \tag{3}
$$

where  $\mu_l$  and  $\sigma_l^2$  refer to the channel mean and variance in the *l*-th layer, respectively.  $s_i$  is optimized within a mini-batch S, where  $s_i \in \mathbb{S}$  and  $\mathbb{S} \subset \mathcal{S}$ . Each component of  $\mathcal{L}_{\rm BN}$  operates from its own perspective to enhance dataset distillation. First, the mean component  $\mathcal{L}_{\text{mean}}$  regularizes the synthetic data s, ensuring its values align closely with those of the representative centroid of  $\mathcal T$  in latent space. Second, the variance component  $\mathcal{L}_{var}$  encourages the synthetic data in S to differ from each other, thereby maintaining the variance  $\sigma_l^2(\hat{s})$  $\sigma_l^2(\hat{s})$  $\sigma_l^2(\hat{s})$ . Thus, this BN loss-driven synthesis can be decoupled as

<span id="page-3-0"></span>
$$
s_i = X_c \left( \lambda \mathcal{L}_{\text{mean}}, \theta_{\mathcal{T}} \right) + \xi_i,
$$
\n<sup>(4)</sup>

where  $X_c$  can be regarded as an optimal solution to [Equation 2](#page-2-1) when the variance regularization term  $\mathcal{L}_{var}$  is not considered, *i.e.*,

$$
\left\|\nabla_{\theta}\ell\left(f_{\theta_{\mathcal{T}}}, \mathbf{X}_{c}\right)\right\|_{2} \leq \alpha_{1} \quad \text{and} \quad \mathcal{L}_{\text{mean}}\left(f_{\theta_{\mathcal{T}}}, \mathbf{X}_{c}\right) = \sum_{l} \left\|\mu_{l}\left(\mathbf{X}_{c}\right) - \mu_{l}\left(\mathcal{T}\right)\right\|_{2} \leq \alpha_{2}, \quad (5)
$$

where both  $\alpha_1, \alpha_2 > 0$  and  $\alpha_1, \alpha_2 \to 0$ .  $\xi_i$  represents a small perturbation and  $\xi_i \sim$  $\mathcal{N}\left(0, \sigma_{\xi}^2(\lambda \mathcal{L}_{var})\right)$ . Therefore, the variance of the synthetic dataset S is,

<span id="page-3-2"></span>
$$
Var(\mathcal{S}) = Var\big(\mathbf{X}_c(\lambda \mathcal{L}_{\text{mean}}, \theta_{\mathcal{T}})\big) + Var\left(\boldsymbol{\xi}\right) = \sigma_{\boldsymbol{\xi}}^2(\lambda \mathcal{L}_{var}).
$$
\n(6)

We have  $\text{Var}(\bm{X}_c(\lambda \mathcal{L}_{\text{mean}}, \theta_{\mathcal{T}})) = 0$  as  $\bm{X}_c$  is deterministic. Unlike other approaches that consider the mutual influences among synthetic data instances and optimize the dataset collectively, SRe2L [\[46\]](#page-11-8) optimizes each synthetic data instance individually. Therefore, the diversity of the synthetic dataset  $S$ is solely determined by  $\lambda \mathcal{L}_{var}$ .

However, simply increasing  $\lambda$  contributes marginally to enhancing the diversity of S. This is because a greater  $\lambda$  will also emphasize the regularization term  $\lambda\mathcal{L}_{\text{mean}}$ , which contradicts the emphasis on  $\lambda \mathcal{L}_{var}$ . We provide a detailed analysis in the Appendix [A.1.](#page-13-0) As a result, we propose using a decoupled coefficient,  $\lambda_{var}$ , to enhance the diversity of S.

Additionally, the synthetic data instances are optimized individually to approximate the representative data instance  $X_c$ . However, the gaussian initialization  $\mathcal{N}(0, 1)$  in pixel space does not distribute uniformly around  $X_c$  in latent space, making the converged synthetic data instances to cluster in a crowed area in latent space, as dedicated in [Figure 1.](#page-1-0) To address this, we propose initializing with real instances from  $\mathcal T$  inspired by MTT [\[1\]](#page-9-5), ensuring a uniform projection when synthesizing  $\mathcal S$ .

## 3.2 Random Perturbation on $\theta_{\mathcal{T}}$ Helps Improve Diversity

In the previous section, we highlighted the often overlooked aspect of the BN loss in introducing diversity to  $S$ , which was also verified through experiments in [Section 4.2.](#page-6-0) Building upon this, we propose to introduce randomness into  $\theta_{\mathcal{T}}$  to further enhance S's diversity, as it is the only remaining factor affecting  $\text{Var}(\mathcal{S})$ , as shown in [Equation 6.](#page-3-2)

Let  $x_c^* = X_c(\lambda \mathcal{L}_{\text{mean}}, \theta_\mathcal{T})$  to be the original optimal solution to [Equation 2.](#page-2-1) We aim to solve the adjusted optimal solution  $x_c=X_c(\lambda\mathcal{L}_{\rm mean},\theta_\mathcal{T}+\Delta\theta)=x_c^*+\Delta x$ , where  $\theta_\mathcal{T}$  is randomly perturbed by  $\Delta\theta$ , and  $\Delta\theta \sim \mathcal{N}(0, \sigma_\theta^2)$ . Consequently, we have:

<span id="page-3-3"></span>
$$
\left\|\nabla_{\theta}\ell\left(f_{\theta_{\mathcal{T}}+\Delta\theta},\boldsymbol{x}_{c}\right)\right\|_{2}=\left\|\nabla_{\theta}\ell\left(f_{\theta_{\mathcal{T}}+\Delta\theta},\boldsymbol{x}_{c}^{*}+\Delta\boldsymbol{x}\right)\right\|_{2}\leq\alpha_{1}.
$$
\n(7)

To solve for  $\Delta x$ , we can apply a first-order bivariate Taylor series approximation because  $\nabla_{\theta} \ell(f_{\theta_{\mathcal{T}}}, \mathbf{X}_c) \le \alpha_1$ , where  $\alpha_1 \to 0$ , and both  $\Delta \theta$  and  $\Delta \mathbf{x}$  are small. Thus,

$$
\|\nabla_{\theta}\ell\left(f_{\theta_{\mathcal{T}+\Delta\theta}}, \mathbf{x}_{c}^{*} + \Delta\mathbf{x}\right)\|_{2} = \|\nabla_{\theta}\ell\left(f_{\theta_{\mathcal{T}}}, \mathbf{x}_{c}^{*}\right) + \nabla_{\theta}^{2}\ell\left(f_{\theta_{\mathcal{T}}}, \mathbf{x}_{c}^{*}\right) \Delta\theta + \nabla_{\mathbf{x}}\left[\nabla_{\theta}\ell\left(f_{\theta_{\mathcal{T}}}, \mathbf{x}_{c}^{*}\right)\right] \Delta\mathbf{x}\right\|_{2} \leq \|\nabla_{\theta}\ell\left(f_{\theta_{\mathcal{T}}}, \mathbf{x}_{c}^{*}\right)\|_{2} + \|\nabla_{\theta}^{2}\ell\left(f_{\theta_{\mathcal{T}}}, \mathbf{x}_{c}^{*}\right) \Delta\theta + \nabla_{\mathbf{x}}\left[\nabla_{\theta}\ell\left(f_{\theta_{\mathcal{T}}}, \mathbf{x}_{c}^{*}\right)\right] \Delta\mathbf{x}\right\|_{2} \leq \alpha_{1} + \|\nabla_{\theta}^{2}\ell\left(f_{\theta_{\mathcal{T}}}, \mathbf{x}_{c}^{*}\right) \Delta\theta + \nabla_{\mathbf{x}}\left[\nabla_{\theta}\ell\left(f_{\theta_{\mathcal{T}}}, \mathbf{x}_{c}^{*}\right)\right] \Delta\mathbf{x}\right\|_{2},
$$
(8)

<span id="page-3-1"></span>We disregard the class differences in the following analysis since they are identical across all classes.

To satisfy [Equation 7,](#page-3-3) we have:

$$
\nabla_{\theta}^{2} \ell \left( f_{\theta_{\mathcal{T}}}, \boldsymbol{x}_{c}^{*} \right) \Delta \theta + \nabla_{\boldsymbol{x}} \left[ \nabla_{\theta} \ell \left( f_{\theta_{\mathcal{T}}}, \boldsymbol{x}_{c}^{*} \right) \right] \Delta \boldsymbol{x} = \mathbf{0}, \quad \text{then}
$$
  
$$
\Delta \boldsymbol{x} = - \nabla_{\boldsymbol{x}} \left[ \nabla_{\theta} \ell \left( f_{\theta_{\mathcal{T}}}, \boldsymbol{x}_{c}^{*} \right) \right]^{-1} \nabla_{\theta}^{2} \ell \left( f_{\theta_{\mathcal{T}}}, \boldsymbol{x}_{c}^{*} \right) \Delta \theta.
$$
 (9)

Intuitively,  $\Delta x$  must compensate for the  $\nabla_\theta$  incurred by introducing the random perturbation  $\Delta \theta \sim$  $\mathcal{N}(0, \sigma_\theta^2)$  on  $\theta_{\mathcal{T}}$ . By [Equation 9,](#page-4-2) Var $(\Delta x) \propto \text{Var}(\Delta \theta) = \sigma_\theta^2$ , then:

<span id="page-4-2"></span>
$$
\begin{split} \text{Var}\left(\mathcal{S}'\right) &= \text{Var}\left(\mathbf{X}_c\left(\lambda\mathcal{L}_{\text{mean}}, \theta_{\mathcal{T}} + \Delta\theta\right)\right) + \text{Var}\left(\boldsymbol{\xi}\right) \\ &= \text{Var}\left(\boldsymbol{x}_c^* + \Delta\boldsymbol{x}\right) + \text{Var}\left(\boldsymbol{\xi}\right) \\ &= \beta\sigma_\theta^2 + \sigma_\boldsymbol{\xi}^2\left(\lambda\mathcal{L}_{\text{var}}\right) \ge \sigma_\boldsymbol{\xi}^2\left(\lambda\mathcal{L}_{\text{var}}\right), \end{split} \tag{10}
$$

where  $\beta$  is determined by  $-\nabla_{x} [\nabla_{\theta} \ell(f_{\theta_{\mathcal{T}}}, x^c)]^{-1} \nabla_{\theta}^2 \ell(f_{\theta_{\mathcal{T}}}, x^c)$ , as shown in [Equation 9.](#page-4-2) Therefore, the variance of the new synthetic dataset S' is greater than that of S without perturbing  $\theta_{\mathcal{T}}$ .

<span id="page-4-4"></span>

## 3.3 Directed Weight Adjustment on $\theta_{\mathcal{T}}$

Although perturbing  $\theta_{\tau}$  could significantly increase the variance of the synthetic dataset S, undirected random perturbation  $\Delta\theta$  can also introduce noise, which in turn degrades the performance. We aim to address this limitation by directing the random perturbation  $\Delta\theta$  without introducing noise into S. We propose to obtain directed  $\Delta\theta$  by solving the following maximization problem:

<span id="page-4-0"></span>
$$
\widetilde{\Delta\theta} = \underset{\Delta\theta}{\arg\max} L_{\mathbb{B}} \left( f_{\theta\tau + \Delta\theta} \right) \quad \text{where} \quad L_{\mathbb{B}} \left( f_{\theta\tau + \Delta\theta} \right) = \sum_{\boldsymbol{x}_i \in \mathbb{B}} \ell \left( f_{\theta\tau + \Delta\theta}, \boldsymbol{x}_i \right), \tag{11}
$$

where  $\mathbb{B} \subset \mathcal{T}$  represents a randomly selected subset of  $\mathcal{T}$ , and  $|\mathbb{B}| \ll |\mathcal{T}|$ . As such,  $\widetilde{\Delta\theta}$  will not introduce unanticipated noise when synthesizing  $S$ . The randomly selected  $\mathbb B$  ensures that the randomness of  $\Delta\theta$  continues to benefit the diversity of S. Next, we will demonstrate this theoretically.

Effective dataset distillation should provide concise and critical guidance from the original dataset  $\tau$  when synthesizing the distilled dataset. Here, this guidance is introduced primarily through the converged weight parameters  $\theta_{\mathcal{T}}$ , *i.e.*,

$$
\theta_{\mathcal{T}} = \underset{\theta}{\arg\min} \, L_{\mathcal{T}} \left( f_{\theta_{\mathcal{T}}} \right) \quad \text{where} \quad L_{\mathcal{T}} \left( f_{\theta_{\mathcal{T}}} \right) = \sum_{\boldsymbol{x}_i \in \mathcal{T}} \ell \left( f_{\theta_{\mathcal{T}}} , \boldsymbol{x}_i \right), \tag{12}
$$

where  $\theta_{\mathcal{T}}$  contains informative features of  $\mathcal{T}$  because it achieves minimized training loss over  $\mathcal{T}$ . We demonstrate that  $\Delta\theta$ , obtained from [Equation 11,](#page-4-0) decreases the training loss computed over  $\mathcal{T} \setminus \mathbb{B}$ , which, in fact, highlights the features of  $\mathcal{T} \setminus \mathbb{B}$ . By applying a first-order Taylor expansion, we obtain:

<span id="page-4-3"></span>
$$
L_{\mathcal{T}\backslash \mathbb{B}}\left(f_{\theta_{\mathcal{T}}+\widetilde{\Delta\theta}}\right) \approx L_{\mathcal{T}\backslash \mathbb{B}}\left(f_{\theta_{\mathcal{T}}}\right) + \nabla_{\theta}L_{\mathcal{T}\backslash \mathbb{B}}\left(f_{\theta_{\mathcal{T}}}\right)\widetilde{\Delta\theta}.\tag{13}
$$

Since  $\theta_{\mathcal{T}}$  is optimized until reaching a local minimum with respect to the loss function computed over the training set  $\mathcal T$ , we have:

$$
\nabla_{\theta} L_{\mathcal{T}}(f_{\theta_{\mathcal{T}}}) = \nabla_{\theta} L_{\mathbb{B}}(f_{\theta_{\mathcal{T}}}) + \nabla_{\theta} L_{\mathcal{T}\setminus\mathbb{B}}(f_{\theta_{\mathcal{T}}}) = \mathbf{0} \quad \text{thus} \quad \nabla_{\theta} L_{\mathcal{T}\setminus\mathbb{B}}(f_{\theta_{\mathcal{T}}}) = -\nabla_{\theta} L_{\mathbb{B}}(f_{\theta_{\mathcal{T}}}),
$$

where 0 is the tensor of zeros with the same dimension as  $\theta_{\mathcal{T}}$ . Substitute it back into [Equation 13,](#page-4-3) we have:

$$
L_{\mathcal{T}\backslash \mathbb{B}}\left(f_{\theta_{\mathcal{T}}+\widetilde{\Delta\theta}}\right) - L_{\mathcal{T}\backslash \mathbb{B}}\left(f_{\theta_{\mathcal{T}}}\right) \approx \nabla_{\theta} L_{\mathcal{T}\backslash \mathbb{B}}\left(f_{\theta_{\mathcal{T}}}\right) \widetilde{\Delta\theta}
$$

$$
= -\nabla_{\theta} L_{\mathcal{T}\backslash \mathbb{B}}\left(f_{\theta_{\mathcal{T}}}\right) \widetilde{\Delta\theta}
$$

$$
\approx -\left(L_{\mathbb{B}}\left(f_{\theta_{\mathcal{T}}+\widetilde{\Delta\theta}}\right) - L_{\mathbb{B}}\left(f_{\theta_{\mathcal{T}}}\right)\right) \leq 0, \quad (14)
$$

 $L_{\mathbb{B}}(f_{\theta_{\mathcal{T}}+\widetilde{\Delta\theta}})$  will clearly be greater than  $L_{\mathbb{B}}(f_{\theta_{\mathcal{T}}})$ , as indicated by [Equation 11.](#page-4-0) Thus, we demonstrate that the directed  $\Delta\theta$  results in less noise and improved performance. In summary, after resolving  $\Delta\theta$ as in [Equation 11,](#page-4-0) our proposed method synthesizes data instance  $s_i$  by solving:

<span id="page-4-1"></span>
$$
\tilde{s}_i = \underset{\mathbf{s} \in \mathbb{R}^d}{\arg \min} \mathcal{L} \quad \text{where} \quad \mathcal{L} = \left[ \ell \left( f_{\theta_\mathcal{T} + \widetilde{\Delta \theta}}, \mathbf{s}_i \right) + \lambda \mathcal{L}_{\text{mean}} \left( f_{\theta_\mathcal{T}}, \mathbf{s}_i \right) + \lambda_{\text{var}} \mathcal{L}_{\text{var}} \left( f_{\theta_\mathcal{T}}, \mathbf{s}_i \right) \right]. \tag{15}
$$

# 4 Experiments

To evaluate the effectiveness of the proposed method, we have conducted extensive comparison experiments with SOTA methods on various datasets including CIFAR-10/100 ( $32 \times 32$ , 10/100 classes) [\[16\]](#page-10-8), Tiny-ImageNet (64  $\times$  64, 200 classes) [\[18\]](#page-10-11), and ImageNet-1K (224  $\times$  224, 1000 classes) [\[3\]](#page-9-9) using diverse network architectures like ResNet-(18, 50, 101) [\[11\]](#page-9-10), MobileNetV2 [\[33\]](#page-11-10), ShuffleNetV2 [\[26\]](#page-10-12), EfficientNet-B0 [\[37\]](#page-11-11), and VGGNet-16 [\[35\]](#page-11-12). We conduct our experiments on the server with one Nvidia Tesla A100 40GB GPU.

Solving  $\Delta\theta$ . Before we conduct our experiments, we propose to use a gradient descent approach to solve  $\Delta\theta$  in [Equation 11.](#page-4-0) There are two coefficients, K and  $\rho$ , used in the gradient descent approach. K represents the number of steps, and  $\rho$  normalizes the magnitude of the directed weight adjustment. The details for solving  $\Delta\theta$  can be found in Line [7](#page-2-2) of Algorithm [1.](#page-2-2)

Experiment Setting. Unless otherwise specified, we default to using ResNet-18 as the backbone for distillation. For ImageNet-1K, we use the pre-trained model provided by Torchvision while for CIFAR-10/100 and Tiny-ImageNet, we modify the original architecture under the suggestion in [\[10\]](#page-9-11). More detailed hyper-parameter settings can be found in Appendix [A.2.1.](#page-13-1)

Baselines and Metrics. We conduct comparison with seven Dataset Distillation methods including DC [\[54\]](#page-12-1), DM [\[53\]](#page-12-2), CAFE [\[42\]](#page-11-7), MTT [\[1\]](#page-9-5), TESLA [\[2\]](#page-9-6), SRe2L [\[46\]](#page-11-8), and DataDAM [\[32\]](#page-10-13). For all the considered comparison methods, we assess the quality of the distilled dataset by measuring the Top-1 classification accuracy on the original validation set using models trained on them from scratch. Blue cells in all tables highlight the highest performance.

## 4.1 Results & Discussions

CIFAR-10/100. As shown in [Table 1,](#page-5-0) our DWA exhibits superior performance compared to conventional dataset distillation methods, particularly evident on CIFAR-100 with a larger distillation budget. For instance, our DWA yields over a 10% performance enhancement compared to MTT [\[1\]](#page-9-5) with  $ipc = 50$ . Leveraging a more robust distillation backbone like ResNet-18, our approach surpasses the SOTA method SRe2L [\[46\]](#page-11-8) across all considered settings. Specifically, we achieve more than 5% and 8% accuracy improvement on CIFAR-10 and CIFAR-100, respectively.

<span id="page-5-0"></span>Table 1: Comparison with SOTA dataset distillation baselines on CIFAR-10/100. Unless otherwise specified, we use the same network architecture for distillation and validation. Following the settings in their original papers, DC [\[54\]](#page-12-1), DM [\[53\]](#page-12-2), CAFE [\[42\]](#page-11-7), MTT [\[1\]](#page-9-5), and TESLA [\[2\]](#page-9-6) use ConvNet-128 (*small model*). For SRe2L [\[46\]](#page-11-8), ResNet-18 (*large model*) is used for synthesis and validation.

| Dataset   | ipc | ConvNet     |             |             |             |             | ResNet-18   |             |             |
|-----------|-----|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|
|           |     | DC [54]     | DM [53]     | CAFE [42]   | MTT [1]     | TESLA [2]   | DWA (ours)  | SRe2L [46]  | DWA (ours)  |
| CIFAR-10  | 10  | 44.9 $±0.5$ | 48.9 $±0.6$ | 46.3 $±0.6$ | 65.4 $±0.7$ | 66.4 $±0.8$ | 45.0 $±0.4$ | 27.2 $±0.4$ | 32.6 $±0.4$ |
|           | 50  | 53.9 $±0.5$ | 63.0 $±0.4$ | 55.5 $±0.6$ | 71.6 $±0.7$ | 72.6 $±0.7$ | 63.3 $±0.7$ | 47.5 $±0.5$ | 53.1 $±0.3$ |
| CIFAR-100 | 10  | 25.2 $±0.3$ | 29.7 $±0.3$ | 27.8 $±0.3$ | 40.1 $±0.4$ | 41.7 $±0.3$ | 47.6 $±0.4$ | 31.6 $±0.5$ | 39.6 $±0.6$ |
|           | 50  |             | 43.6 $±0.4$ | 37.9 $±0.3$ | 47.7 $±0.2$ | 47.9 $±0.3$ | 59.0 $±0.1$ | 52.2 $±0.3$ | 60.9 $±0.5$ |

<span id="page-5-1"></span>Table 2: Comparison with SOTA dataset distillation baselines on Tiny-ImageNet and ImageNet-1K. Unless otherwise specified, we use the same network architecture for distillation and validation. Following the settings in their original papers, MTT [\[1\]](#page-9-5), and TESLA [\[2\]](#page-9-6) use ConvNet-128 (*small model*). For SRe2L [\[46\]](#page-11-8), ResNet-18 (*large model*) is used for synthesis, and the distilled dataset is evaluated on ResNet-18, 50, and 101. † indicates MTT is performed on a 10-class subset of the full ImageNet-1K dataset.

|               |                 | ConvNet                                         |                                                                           |                                                            | ResNet-18                                          |                                                                              |                                                                  | $ResNet-50$                                                                  |                                                  | ResNet-101                                                                                |
|---------------|-----------------|-------------------------------------------------|---------------------------------------------------------------------------|------------------------------------------------------------|----------------------------------------------------|------------------------------------------------------------------------------|------------------------------------------------------------------|------------------------------------------------------------------------------|--------------------------------------------------|-------------------------------------------------------------------------------------------|
| Dataset       | ipc             | MTT[1]                                          | DataDAM [32] TESLA [2]   SRe2L [46] DWA (ours)   SRe2L DWA (ours)   SRe2L |                                                            |                                                    |                                                                              |                                                                  |                                                                              |                                                  | DWA (ours)                                                                                |
| Tiny-ImageNet | 50<br>100       | $28.0 \pm 0.3$<br>$\sim$                        | $28.7{\scriptstyle \pm0.3}$<br>$\sim$                                     |                                                            | $41.1 \pm 0.4$<br>$49.7{\scriptstyle \pm0.3}$      | $52.8 \pm 0.2$<br>$56.0 {\scriptstyle \pm 0.2}$                              | $42.2{\scriptstyle \pm0.5}$<br>$51.2 \pm 0.4$                    | $53.7{\scriptstyle \pm0.2}$<br>$56.9{\scriptstyle \pm0.4}$                   | $142.5 \pm 0.2$<br>$51.5 \pm 0.3$                | $54.7{\scriptstyle \pm0.3}$<br>$57.4 \pm 0.3$                                             |
| ImageNet-1K   | 10<br>50<br>100 | $64.0 \pm 1.3$ <sup>T</sup><br>$\sim$<br>$\sim$ | $6.3 + 0.0$<br>$\sim$<br>۰                                                | $17.8{\scriptstyle \pm1.3}$<br>$27.9{\scriptstyle \pm1.2}$ | $21.3 \pm 0.6$<br>$46.8 \pm 0.2$<br>$52.8 \pm 0.3$ | $37.9 \pm 0.2$<br>$55.2{\scriptstyle \pm0.2}$<br>$59.2{\scriptstyle \pm0.3}$ | $28.4{\scriptstyle \pm0.1}$<br>$155.6 \pm 0.3$<br>$61.0 \pm 0.4$ | $43.0 \pm 0.5$<br>$62.3{\scriptstyle \pm0.1}$<br>$65.7{\scriptstyle \pm0.4}$ | $30.9 \pm 0.1$<br>$60.8 + 0.5$<br>$62.8 \pm 0.2$ | $46.9{\scriptstyle \pm0.4}$<br>$63.3{\scriptstyle \pm0.7}$<br>$66.7{\scriptstyle \pm0.2}$ |

Image /page/6/Figure/0 description: The image displays two grids of images, labeled (a) and (b). Grid (a) contains eight images, and grid (b) also contains eight images. All images appear to be abstract representations or stylized depictions of goldfish in water, with varying colors and compositions. Some images are blurry or fragmented, while others show more distinct shapes and colors of the fish and their surroundings.

Figure 2: Visualization of distilled images for the goldfish class. Panels (a) and (b) show the synthesized results by SRe2L [\[46\]](#page-11-8) and our DWA, respectively. The synthetic data instances generated by our DWA method exhibit significantly greater diversity compared to those produced by SRe2L, highlighting the effectiveness of our approach in capturing a broader range of features.

Image /page/6/Figure/2 description: The image contains two plots side-by-side, labeled (a) and (b). Both plots show the relationship between \lambda\_{var} (x0.01) on the x-axis and Test Acc. (%) on the y-axis. Plot (a) is titled "Backbone: SRe2L" and shows two lines: a red line labeled "decoupled var" and a blue line labeled "coupled var". The red line peaks around \lambda\_{var} = 9 with a Test Acc. of approximately 56.8%, while the blue line peaks around \lambda\_{var} = 7 with a Test Acc. of approximately 53.5%. Plot (b) is titled "Backbone: our DWA" and also shows two lines: a red line labeled "decoupled var" and a blue line labeled "coupled var". The red line peaks around \lambda\_{var} = 11 with a Test Acc. of approximately 60.5%, while the blue line peaks around \lambda\_{var} = 13 with a Test Acc. of approximately 58.2%. Both plots include shaded regions around the lines, indicating variability.

Figure 3: Analysis of decoupled  $\mathcal{L}_{var}$  coefficient. We vary  $\lambda_{var}$  across a wide range of (0.01 ~ 0.23). 'decoupled var' indicates  $\lambda_{var}$  is changing individually with a fixed mean component whose weight defaults to 0.01. 'coupled var' represents the weight of the mean and  $\lambda_{\text{var}}$  change in tandem. (a) and (b) illustrate the performance of the original SRe2L [\[46\]](#page-11-8) and our DWA in these two scenarios, respectively. This analysis is conducted on CIFAR-100 using ResNet-18. Each  $\lambda_{\text{var}}$  undergoes five independent experiments, with variance indicated by lighter color shades.

Tiny-ImageNet & ImageNet-1K. Compared with CIFAR-10/100, ImageNet datasets are more closely reflective of real-world scenarios. [Table 2](#page-5-1) lists the related results. Due to the limited scalability capacity of conventional distillation paradigm, only a few methods have conducted evaluation on ImageNet datasets. Here we provide a comprehensive comparison with SRe2L [\[46\]](#page-11-8), which has been validated as the most effective one for distilling large-scale dataset. It is obvious that our method significantly outperforms SRe2L on all ipc settings and validation models. For instance, our DWA surpasses SRe2L by 16.6% when  $\rm ipc = 10$  on ImageNet-1K using ResNet-18. [Figure 2](#page-6-1) further provides the visualization results, the enhanced diversity is the key driver behind the substantial performance improvement.

<span id="page-6-2"></span><span id="page-6-1"></span>Image /page/6/Figure/5 description: A bar chart displays the normalized feature distance for 10 different classes. The x-axis is labeled "Class index" and ranges from 1 to 10. The y-axis is labeled "Normalized feature distance" and ranges from 0.0 to 1.2. Two sets of bars are plotted for each class: pink bars represent "decoupled var" and light blue bars represent "coupled var". For class 1, decoupled var is approximately 0.85 and coupled var is approximately 0.57. For class 2, decoupled var is approximately 0.65 and coupled var is approximately 0.48. For class 3, decoupled var is approximately 1.0 and coupled var is approximately 0.67. For class 4, decoupled var is approximately 0.55 and coupled var is approximately 0.13. For class 5, decoupled var is approximately 0.53 and coupled var is approximately 0.51. For class 6, decoupled var is approximately 0.59 and coupled var is approximately 0.71. For class 7, decoupled var is approximately 0.39 and coupled var is approximately 0.39. For class 8, decoupled var is approximately 0.91 and coupled var is approximately 0.51. For class 9, decoupled var is approximately 0.39 and coupled var is approximately 0.39. For class 10, decoupled var is approximately 0.61 and coupled var is approximately 0.55.

<span id="page-6-3"></span>Figure 4: Normalized feature distance of decoupled variance component with  $\lambda_{\text{var}} = 0.11$  (the weight of mean component defaults to 0.01) and coupled variance component with  $\lambda_{\rm BN} = 0.11$ . ResNet-18's last convolutional layer outputs are used for feature distance calculation (see Appendix [A.2.2\)](#page-14-0). Ten classes are randomly chosen from CIFAR-100 distilled dataset.

<span id="page-6-0"></span>

## 4.2 Ablation Study

**Decoupled**  $\mathcal{L}_{var}$  **Coefficient.** We first test our hypothesis, as outlined in [Section 3.1,](#page-3-4) positing that strengthening  $\mathcal{L}_{\text{mean}}$  conflicts with the emphasis on  $\mathcal{L}_{\text{var}}$ , which is critical for ensuring diversity in synthetic datasets. Therefore, we compare the synthetic dataset distilled with an emphasis on  $\mathcal{L}_{\rm BN}$ (which strengthens both  $\mathcal{L}_{\text{mean}}$  and  $\mathcal{L}_{\text{var}}$ ) against one that emphasizes  $\mathcal{L}_{\text{var}}$  alone. As depicted in [Figure 3,](#page-6-2) focusing solely on  $\mathcal{L}_{var}$  outperforms the combined emphasis on  $\lambda_{BN}$  in both SRe2L [\[46\]](#page-11-8) and our proposed Directed Weight Adjustment (DWA). These experimental results verify our hypothesis in [Section 3.1,](#page-3-4) indicating the optimal value of the decoupled coefficient  $\mathcal{L}_{var}$  is 0.11. We also employ

<span id="page-7-0"></span>Table 3: An ablation study of DWA was conducted using various network architectures. The synthetic dataset was distilled by ResNet-18 from the CIFAR-100 dataset. We use *x* to denote the distilled dataset without weight adjustment,  $\bigcirc$  to denote the distilled dataset with random weight adjustment, and  $\vee$  to represent Directed Weight Adjustment (DWA).

|                   |                             | $\texttt{ipc} = 10$         |                             |                             | $\texttt{ipc} = 50$         |                             |
|-------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|
| Perturbation      | x                           |                             | V                           | x                           |                             | ✓                           |
| ResNet-18         | $30.6 + 0.7$                | $14.9{\scriptstyle \pm0.1}$ | $39.6 \pm 0.6$              | $56.1 + 0.4$                | $56.2 + 0.6$                | $60.3{\scriptstyle \pm0.5}$ |
| $ResNet-50$       | $26.5 + 1.1$                | $15.0 + 0.2$                | $35.2{\scriptstyle \pm0.7}$ | $55.7 + 0.9$                | $57.1 + 0.5$                | $60.6{\scriptstyle \pm0.8}$ |
| MobileNetV2       | $18.2{\scriptstyle \pm0.5}$ | $14.4 \pm 1.2$              | $27.8 + 0.7$                | $46.9{\scriptstyle \pm0.9}$ | $50.7{\scriptstyle \pm0.6}$ | $53.6 + 0.2$                |
| <b>ShuffleNet</b> | $10.3 + 0.7$                | $10.7 + 0.1$                | $19.4 \pm 0.9$              | $30.9 + 1.1$                | $39.1 + 0.1$                | $41.7{\scriptstyle \pm0.8}$ |
| EfficientNet      | $11.8 \pm 0.4$              | $11.1 + 0.7$                | $20.2 \pm 0.4$              | $28.6 + 1.0$                | $38.8 + 1.0$                | $40.7{\scriptstyle \pm0.3}$ |

the normalized feature distance as a metric to comprehensively evaluate our emphasis. This metric measures the mutual feature distances between instances, as defined in Appendix [A.2.2.](#page-14-0) By randomly selecting 10 classes from CIFAR-100, we calculate the normalized feature distances between synthetic datasets emphasized by the decoupled  $\mathcal{L}_{var}$  and the coupled  $\mathcal{L}_{BN}$ . The findings, illustrated in [Figure 4,](#page-6-3) validate our hypothesis from a different perspective.

Directed Weight Adjustment. We clarify the necessity of restricting the direction of weight adjustment in [Section 3.3.](#page-4-4) To test its effectiveness, we apply a random  $\Delta\theta$ , sampled from a Gaussian Distribution, to  $\theta_{\tau}$ . As shown in [Table 3,](#page-7-0) we assess synthetic datasets derived from three scenarios: no weight adjustment, random weight adjustment, and our directed weight adjustment (DWA) method, using the CIFAR-100 dataset. The results, examined across various architectures, underscore the importance of directing weight adjustments in distillation processes. Notably, we observe performance degradation in the synthetic dataset optimized with random weight adjustment at  $\rm ipc = 10$  compared to those without weight adjustment. This decline occurs because, at smaller ipc values, the noise introduced by random weight adjustment outweighs the benefits of diversity. However, as the number of synthetic instances increases, diversity becomes more effective in capturing a broader range of features, leading to improved performance, as reflected at  $\texttt{inc} = 50$ .

|             | ipc | Methods             | MobileNetv2                                        | ShuffleNet                                          | EfficientNet                                       | VGG-16                                             | ResNet-50                                          | ConvNet-128                                        |
|-------------|-----|---------------------|----------------------------------------------------|-----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|
|             | 10  | SRe2L<br>DWA (ours) | 16.1 <span>±</span> 0.5<br>27.8 <span>±</span> 0.7 | 11.8 <span>±</span> 0.7<br>19.4 <span>±</span> 0.9  | 11.1 <span>±</span> 0.3<br>20.2 <span>±</span> 0.4 | 19.2 <span>±</span> 0.2<br>30.0 <span>±</span> 0.5 | 22.4 <span>±</span> 1.3<br>35.2 <span>±</span> 0.7 | 19.4 <span>±</span> 0.2<br>27.3 <span>±</span> 0.3 |
| ResNet-18   | 50  | SRe2L<br>DWA (ours) | 43.2 <span>±</span> 0.2<br>53.6 <span>±</span> 0.2 | 27.5 <span>±</span> 1.1<br>41.7 <span>±</span> 0.8  | 24.9 <span>±</span> 1.7<br>40.7 <span>±</span> 0.3 | 40.4 <span>±</span> 1.2<br>51.6 <span>±</span> 0.4 | 52.8 <span>±</span> 0.7<br>60.6 <span>±</span> 0.8 | 19.4 <span>±</span> 0.2<br>37.0 <span>±</span> 0.3 |
|             | 10  | SRe2L<br>DWA (ours) | 28.7 <span>±</span> 1.3<br>37.3 <span>±</span> 0.1 | 25.3 <span>±</span> 0.4<br>25.3 <span>±</span> 0.4  | 18.0 <span>±</span> 0.9<br>24.5 <span>±</span> 0.4 | 21.5 <span>±</span> 1.6<br>29.6 <span>±</span> 1.3 | 41.8 <span>±</span> 0.2<br>47.1 <span>±</span> 0.3 | -<br>47.6 <span>±</span> 0.4                       |
| ConvNet-128 | 50  | SRe2L<br>DWA (ours) | 48.8 <span>±</span> 0.4<br>53.5 <span>±</span> 0.3 | 49.3 <span>±</span> 0.7<br>44.37 <span>±</span> 0.4 | 45.7 <span>±</span> 0.8<br>45.7 <span>±</span> 0.8 | 38.9 <span>±</span> 0.5<br>38.9 <span>±</span> 0.5 | 53.4 <span>±</span> 0.5<br>56.3 <span>±</span> 0.3 | -<br>59.0 <span>±</span> 0.1                       |

<span id="page-7-2"></span>Table 4: Cross-architecture performance of distilled dataset of CIFAR-100 using ResNet-18 and ConvNet-128.

**Parameters Study on** K and  $\rho$ . Apart from direction, the number of steps K and magnitude  $\rho$ of perturbation also influence the distillation process. [Figure 5](#page-7-1) illustrates the grid search for these two hyper-parameters and demonstrates the positive impact of perturbation, which is achieved effortlessly, requiring no meticulous manual parameter tuning. In our experiments, we set  $K = 12$  and  $\rho = 15e^{-3}$  for all the datasets. Readers can adjust these hyper-parameters according to their specific circumstances (different datasets and networks) to obtain better results.

Cross-Architecture Generalization. The generalizability across different architectures is a

Image /page/7/Figure/8 description: Figure 5 is a performance grid of ResNet-18 with changes in K and rho. The x-axis represents rho (x0.001) ranging from 0 to 30, and the y-axis represents K ranging from 0 to 16. The color bar on the right indicates the Test Accuracy (%) from 52.0 to 58.4. The grid shows varying levels of test accuracy across different combinations of K and rho, with higher accuracies generally observed in the upper right portion of the grid.

<span id="page-7-1"></span>in perturbation steps  $K$  and magnitude  $\rho$ .

key feature for assessing the effectiveness of the distilled dataset. In this section, we evaluate the surrogate dataset condensed by different backbones (ResNet-18 and ConvNet-128) on various archi-

tectures including MobileNetV2 [\[33\]](#page-11-10), ShuffleNetV2 [\[26\]](#page-10-12), EfficientNet-B0 [\[37\]](#page-11-11), and VGGNet-16 [\[35\]](#page-11-12). The experimental results are reported in [Table 4](#page-7-2) and [Table 5.](#page-8-1) It is evident that our DWA-synthesized dataset can effectively generalize across various architectures. Notably, for  $\text{ipc} = 50$  on CIFAR-100 with ShuffleNetV2, EfficientNet-B0, and ConvNet-128—three architectures not involved in the data synthesis phase—our method achieves impressive classification performance, with accuracies of 41.7%, 40.7%, and 37.0%, respectively, outperforming the latest SOTA method, SRe2L [\[46\]](#page-11-8), by 14.2%, 15.8%, and 17.6%. In Appendix [A.2.3,](#page-14-1) we further extend the proposed method to a vision transformer-based model, DeiT-Tiny [\[40\]](#page-11-13).

# <span id="page-8-0"></span>5 Related Works

Dataset Distillation [\[43\]](#page-11-4) emerges as a derivative of Knowledge Distillation (KD) [\[9\]](#page-9-12), emphasizing data-centric efficiency over traditional model-centric one. Previous studies have explored various strategies to condense datasets, including performance matching, gradient matching [\[54,](#page-12-1) [52,](#page-12-0) [19\]](#page-10-14) distribution matching [\[42,](#page-11-7) [53,](#page-12-2) [55,](#page-12-3) [48,](#page-11-14) [4\]](#page-9-13), and trajectory matching [\[1,](#page-9-5) [2,](#page-9-6) [5,](#page-9-7) [6,](#page-9-8) [21,](#page-10-15) [41\]](#page-11-15).

<span id="page-8-1"></span>

| Table 5: Cross-architecture performance of distilled |  |
|------------------------------------------------------|--|
| dataset of ImageNet-1K using ResNet-18.              |  |

| ipc | Methods    | MobileNetv2                 | <b>ShuffleNet</b> | EfficientNet                |
|-----|------------|-----------------------------|-------------------|-----------------------------|
| 10  | SRe2L      | $15.4 + 0.2$                | $9.0 + 0.7$       | $11.7{\scriptstyle \pm0.2}$ |
|     | DWA (ours) | $29.1 + 0.3$                | $11.4 + 0.6$      | $37.4 + 0.5$                |
| 50  | SRe2L      | $48.3{\scriptstyle \pm0.5}$ | $9.0 + 0.6$       | $53.6 + 0.4$                |
|     | DWA (ours) | $51.6 + 0.5$                | $28.5 \pm 0.5$    | $56.3 + 0.4$                |

What distinguishes DD from KD is the bi-level

optimization, which considers both model parameters and image pixels. The consequent complexity and computational burden intricate optimization significantly diminish the effectiveness of the aforementioned methods. To address this issue, SRe2L [\[46\]](#page-11-8) introduced a three-step paradigm known as *Squeeze-Recover-Relabel*. This approach relies on the highly encoded distribution prior, *i.e.*, the running mean and running variance in the BN layer, to circumvent supervision provided by model training. With this decoupled optimization, SRe2L is able to extend DD to high-resolution and large-scale datasets like ImageNet-1K.

Another critical challenge in dataset compression, not limited to distillation, is how to represent the original dataset distribution with a scarcity of synthetic data samples [\[36\]](#page-11-16). Previous research claims that the diversity of a dataset can be evaluated by spatial distribution  $[27]$ , the maximum dispersion or convex hull volume [\[47\]](#page-11-17), and coverage [\[56\]](#page-12-6). Conventional dataset distillation [\[49,](#page-11-6) [15\]](#page-10-10) treats the synthetic compact dataset as an integrated optimizable tensor without specialized guarantees for diversity and relies entirely on the matching objectives mentioned above. Recognizing this limitation, Dream [\[23\]](#page-10-6) proposed using cluster centers to induce synthesis and ensure adequate diversity. Besides, SRe2L resorts to the second-order statistics, *i.e.*, variance of representations in pre-trained weights to provide diversity.

# 6 Conclusion

In this work, we hypothesize that ensuring diversity is crucial for effective dataset distillation. Our findings indicate that the random initialization of synthetic data instances contributes minimally to ensuring that each instance captures unique knowledge from the original dataset. We validate our hypothesis through both theoretical and empirical approaches, demonstrating that enhancing diversity significantly benefits dataset distillation. To this end, we propose a novel method, Directed Weight Adjustment (DWA), which introduces diversity in synthesis by customizing weight adjustments for each mini-batch of synthetic data. This approach ensures that each mini-batch condenses a variety of knowledge. Extensive experiments, particularly on the large-scale ImageNet-1K dataset, confirm the superior performance of our proposed DWA method.

Limitations and Future work. While DWA provides a straightforward and efficient approach to introducing diversity in dataset distillation, its reliance on the sampling of a random distribution to adjust weight parameters presents limitations. Increasing the variance of the random distribution can introduce unexpected noise, thereby bottlenecking overall performance. Future investigations could explore synthesizing data instances in a sequential manner, encouraging later instances to consciously distinguish themselves from earlier ones, thereby further enhancing diversity.

# Acknowledgements

This research is supported by Jiawei Du's A\*STAR Career Development Fund (CDF) C233312004 and Joey Tianyi Zhou's A\*STAR SERC Central Research Fund (Use-inspired Basic Research). This research is also supported by National Natural Science Foundation of China under Grant 62301213.

### References

- <span id="page-9-5"></span>[1] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, pages 10708–10717, 2022.
- <span id="page-9-6"></span>[2] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *Proc. Int. Conf. Mach. Learn. (ICML)*, pages 6565–6590, 2023.
- <span id="page-9-9"></span>[3] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, pages 248–255, 2009.
- <span id="page-9-13"></span>[4] Wenxiao Deng, Wenbin Li, Tianyu Ding, Lei Wang, Hongguang Zhang, Kuihua Huang, Jing Huo, and Yang Gao. Exploiting inter-sample and inter-feature relations in dataset distillation. *arXiv preprint arXiv:2404.00563*, 2024.
- <span id="page-9-7"></span>[5] Jiawei Du, Yidi Jiang, Vincent Y. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, pages 3749–3758, 2023.
- <span id="page-9-8"></span>[6] Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. In *Adv. Neural Inf. Process. Syst. (NeurIPS)*, 2023.
- <span id="page-9-4"></span>[7] Yunzhen Feng, Shanmukha Ramakrishna Vedantam, and Julia Kempe. Embarrassingly simple dataset distillation. In *Adv. Neural Inf. Process. Syst. Workshop (NeurIPS Workshop)*, 2023.
- <span id="page-9-0"></span>[8] Leo Gao, Stella Biderman, Sid Black, Laurence Golding, Travis Hoppe, Charles Foster, Jason Phang, Horace He, Anish Thite, Noa Nabeshima, Shawn Presser, and Connor Leahy. The pile: An 800gb dataset of diverse text for language modeling. *arXiv preprint arXiv:2101.00027*, 2021.
- <span id="page-9-12"></span>[9] Jianping Gou, Baosheng Yu, Stephen J. Maybank, and Dacheng Tao. Knowledge distillation: A survey. *Int. J. Comput. Vis.*, 129(6):1789–1819, 2021.
- <span id="page-9-11"></span>[10] Kaiming He, Haoqi Fan, Yuxin Wu, Saining Xie, and Ross B. Girshick. Momentum contrast for unsupervised visual representation learning. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, pages 9726–9735, 2020.
- <span id="page-9-10"></span>[11] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, pages 770–778, 2016.
- <span id="page-9-3"></span>[12] Muyang He, Yexin Liu, Boya Wu, Jianhao Yuan, Yueze Wang, Tiejun Huang, and Bo Zhao. Efficient multimodal learning from data-centric perspective. *arXiv preprint arXiv:2402.11530*, 2024.
- <span id="page-9-2"></span>[13] Jordan Hoffmann, Sebastian Borgeaud, Arthur Mensch, Elena Buchatskaya, Trevor Cai, Eliza Rutherford, Diego de Las Casas, Lisa Anne Hendricks, Johannes Welbl, Aidan Clark, Tom Hennigan, Eric Noland, Katie Millican, George van den Driessche, Bogdan Damoc, Aurelia Guy, Simon Osindero, Karen Simonyan, Erich Elsen, Jack W. Rae, Oriol Vinyals, and Laurent Sifre. Training compute-optimal large language models. *arXiv preprint arXiv:2203.15556*, 2022.
- <span id="page-9-1"></span>[14] Jared Kaplan, Sam McCandlish, Tom Henighan, Tom B. Brown, Benjamin Chess, Rewon Child, Scott Gray, Alec Radford, Jeffrey Wu, and Dario Amodei. Scaling laws for neural language models. *arXiv preprint arXiv:2001.08361*, 2020.

- <span id="page-10-10"></span>[15] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *Proc. Int. Conf. Mach. Learn. (ICML)*, pages 11102–11118, 2022.
- <span id="page-10-8"></span>[16] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-10-0"></span>[17] Alina Kuznetsova, Hassan Rom, Neil Alldrin, Jasper R. R. Uijlings, Ivan Krasin, Jordi Pont-Tuset, Shahab Kamali, Stefan Popov, Matteo Malloci, Tom Duerig, and Vittorio Ferrari. The open images dataset V4: unified image classification, object detection, and visual relationship detection at scale. *Int. J. Comput. Vis. (IJCV)*, 128(7):1956–1981, 2020.
- <span id="page-10-11"></span>[18] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-10-14"></span>[19] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *Proc. Int. Conf. Mach. Learn. (ICML)*, pages 12352– 12364, 2022.
- <span id="page-10-3"></span>[20] Shiye Lei and Dacheng Tao. A comprehensive survey of dataset distillation. *IEEE Trans. Pattern Anal. Mach. Intell.*, 46(1):17–32, 2024.
- <span id="page-10-15"></span>[21] Dai Liu, Jindong Gu, Hu Cao, Carsten Trinitis, and Martin Schulz. Dataset distillation by automatic training trajectories. *arXiv preprint arXiv:2407.14245*, 2024.
- <span id="page-10-9"></span>[22] Songhua Liu and Xinchao Wang. MGDD: A meta generator for fast dataset distillation. In *Adv. Neural Inf. Process. Syst. (NeurIPS)*, 2023.
- <span id="page-10-6"></span>[23] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. DREAM: efficient dataset distillation by representative matching. In *Proc. IEEE/CVF Int. Conf. Comput. Vis. (ICCV)*, pages 17268–17278. IEEE, 2023.
- <span id="page-10-7"></span>[24] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. In *International Conference on Machine Learning*, pages 22649–22674. PMLR, 2023.
- <span id="page-10-5"></span>[25] Noel Loo, Ramin M. Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *Adv. Neural Inf. Process. Syst. (NeurIPS)*, 2022.
- <span id="page-10-12"></span>[26] Ningning Ma, Xiangyu Zhang, Hai-Tao Zheng, and Jian Sun. Shufflenet V2: practical guidelines for efficient CNN architecture design. In *Proc. Eur. Conf. Comput. Vis. (ECCV)*, pages 122–138, 2018.
- <span id="page-10-16"></span>[27] Adyasha Maharana, Prateek Yadav, and Mohit Bansal. D2 pruning: Message passing for balancing diversity and difficulty in data pruning. In *Proc. Int. Conf. Learn. Represent. (ICLR)*, 2024.
- <span id="page-10-4"></span>[28] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *Proc. Int. Conf. Learn. Represent. (ICLR)*, 2021.
- <span id="page-10-1"></span>[29] Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. In *Adv. Neural Inf. Process. Syst. (NeurIPS)*, pages 20596–20607, 2021.
- <span id="page-10-17"></span>[30] Ameya Prabhu, Philip HS Torr, and Puneet K Dokania. Gdumb: A simple approach that questions our progress in continual learning. In *Proc. Eur. Conf. Comput. Vis. (ECCV)*, pages 524–540. Springer, 2020.
- <span id="page-10-2"></span>[31] Noveen Sachdeva and Julian J. McAuley. Data distillation: A survey. *Trans. Mach. Learn. Res.*, 2023.
- <span id="page-10-13"></span>[32] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z. Liu, Yuri A. Lawryshyn, and Konstantinos N. Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *Proc. IEEE/CVF Int. Conf. Comput. Vis. (ICCV)*, pages 17051–17061, 2023.

- <span id="page-11-10"></span>[33] Mark Sandler, Andrew G. Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, pages 4510–4520, 2018.
- <span id="page-11-5"></span>[34] Yuzhang Shang, Zhihang Yuan, and Yan Yan. MIM4DD: mutual information maximization for dataset distillation. In *Adv. Neural Inf. Process. Syst. (NeurIPS)*, 2023.
- <span id="page-11-12"></span>[35] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. In *Proc. Int. Conf. Learn. Represent. (ICLR)*, 2015.
- <span id="page-11-16"></span>[36] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. *arXiv preprint arXiv:2312.03526*, 2023.
- <span id="page-11-11"></span>[37] Mingxing Tan and Quoc V. Le. Efficientnet: Rethinking model scaling for convolutional neural networks. In *Proc. Int. Conf. Mach. Learn. (ICML)*, pages 6105–6114, 2019.
- <span id="page-11-0"></span>[38] Kushal Tirumala, Daniel Simig, Armen Aghajanyan, and Ari Morcos. D4: improving LLM pretraining via document de-duplication and diversification. In *Adv. Neural Inf. Process. Syst. (NeurIPS)*, 2023.
- <span id="page-11-1"></span>[39] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J. Gordon. An empirical study of example forgetting during deep neural network learning. In *Proc. Int. Conf. Learn. Represent. (ICLR)*, 2019.
- <span id="page-11-13"></span>[40] Hugo Touvron, Matthieu Cord, Matthijs Douze, Francisco Massa, Alexandre Sablayrolles, and Hervé Jégou. Training data-efficient image transformers & distillation through attention. In *International conference on machine learning*, pages 10347–10357. PMLR, 2021.
- <span id="page-11-15"></span>[41] Kai Wang, Zekai Li, Zhi-Qi Cheng, Samir Khaki, Ahmad Sajedi, Ramakrishna Vedantam, Konstantinos N Plataniotis, Alexander Hauptmann, and Yang You. Emphasizing discriminative features for dataset distillation in complex scenarios. *arXiv preprint arXiv:2410.17193*, 2024.
- <span id="page-11-7"></span>[42] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. CAFE: learning to condense dataset by aligning features. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, pages 12186–12195, 2022.
- <span id="page-11-4"></span>[43] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-11-3"></span>[44] Xilie Xu, Jingfeng Zhang, Feng Liu, Masashi Sugiyama, and Mohan S. Kankanhalli. Efficient adversarial contrastive learning via robustness-aware coreset selection. 2024.
- <span id="page-11-9"></span>[45] Hongxu Yin, Pavlo Molchanov, José M. Álvarez, Zhizhong Li, Arun Mallya, Derek Hoiem, Niraj K. Jha, and Jan Kautz. Dreaming to distill: Data-free knowledge transfer via deepinversion. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, pages 8712–8721, 2020.
- <span id="page-11-8"></span>[46] Zeyuan Yin, Eric P. Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from A new perspective. In *Adv. Neural Inf. Process. Syst. (NeurIPS)*, 2023.
- <span id="page-11-17"></span>[47] Yu Yu, Shahram Khadivi, and Jia Xu. Can data diversity enhance learning generalization? In *Proc. Int. Conf. Comput. Linguistics (COLING)*, pages 4933–4945, 2022.
- <span id="page-11-14"></span>[48] Hansong Zhang, Shikun Li, Pengju Wang, Dan Zeng, and Shiming Ge. Echo: Efficient dataset condensation by higher-order distribution alignment. *arXiv preprint arXiv:2312.15927*, 2023.
- <span id="page-11-6"></span>[49] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, pages 11950–11959, 2023.
- <span id="page-11-2"></span>[50] Xin Zhang, Jiawei Du, Yunsong Li, Weiying Xie, and Joey Tianyi Zhou. Spanning training progress: Temporal dual-depth scoring (TDDS) for enhanced dataset pruning. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, 2024.

- <span id="page-12-4"></span>[51] Xin Zhang, Jiawei Du, Ping Liu, and Joey Tianyi Zhou. Breaking class barriers: Efficient dataset distillation via inter-class feature compensator. *arXiv preprint arXiv:2408.06927*, 2024.
- <span id="page-12-0"></span>[52] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *Proc. Int. Conf. Mach. Learn. (ICML)*, pages 12674–12685, 2021.
- <span id="page-12-2"></span>[53] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proc. IEEE/CVF Winter Conf. Appl. Comput. Vis. (WACV)*, pages 6503–6512, 2023.
- <span id="page-12-1"></span>[54] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *Proc. Int. Conf. Learn. Represent. (ICLR)*, 2021.
- <span id="page-12-3"></span>[55] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit. (CVPR)*, pages 7856–7865, 2023.
- <span id="page-12-6"></span>[56] Haizhong Zheng, Rui Liu, Fan Lai, and Atul Prakash. Coverage-centric coreset selection for high pruning rates. In *Proc. Int. Conf. Learn. Represent. (ICLR)*, 2023.
- <span id="page-12-5"></span>[57] Muxin Zhou, Zeyuan Yin, Shitong Shao, and Zhiqiang Shen. Self-supervised dataset distillation: A good compression is all you need. *arXiv preprint arXiv:2404.07976*, 2024.

# A Appendix

## <span id="page-13-0"></span>A.1 Minimizing $\mathcal{L}_{\text{mean}}$ and $\mathcal{L}_{\text{var}}$ can be contradictory

To prove that minimizing  $\mathcal{L}_{\text{mean}}$  and  $\mathcal{L}_{\text{var}}$  can result in contradictory objectives for some existing instances, we will demonstrate that the gradients required to minimize  $\mathcal{L}_{mean}$  and  $\mathcal{L}_{var}$ , respectively, may point in opposite directions. Specifically, for any arbitrary instance  $s_i \in S$ , our goal is to establish:

<span id="page-13-4"></span><span id="page-13-2"></span>
$$
\frac{\partial \mathcal{L}_{\text{mean}}}{\partial s_i} \cdot \frac{\partial \mathcal{L}_{\text{var}}}{\partial s_i} < 0,\tag{16}
$$

For  $\frac{\partial \mathcal{L}_{\text{mean}}}{\partial s_i}$ , we have

$$
\frac{\partial \mathcal{L}_{\text{mean}}}{\partial s_i} = \frac{\partial \left[ \mu(\mathcal{S}) - \mu(\mathcal{T}) \right]^2}{\partial s_i} = \frac{\partial \left[ \mu(\mathcal{S}) - \mu(\mathcal{T}) \right]^2}{\partial \mu(\mathcal{S})} \cdot \frac{\partial \mu(\mathcal{S})}{\partial s_i}
$$
$$
= 2 \left[ \mu(\mathcal{S}) - \mu(\mathcal{T}) \right] \cdot \frac{1}{|\mathcal{S}|},\tag{17}
$$

because  $\mu(\mathcal{S}) = \frac{1}{|\mathcal{S}|} s_i + \sum_{j \neq i} \frac{1}{|\mathcal{S}|} s_j$ , thus  $\frac{\partial \mu(\mathcal{S})}{\partial s_i} = \frac{1}{|\mathcal{S}|}$ . For  $\frac{\partial \mathcal{L}_{var}}{\partial s_i}$ , we have

$$
\frac{\partial \mathcal{L}_{var}}{\partial s_{i}} = \frac{\partial [\sigma^{2}(\mathcal{S}) - \sigma^{2}(\mathcal{T})]^{2}}{\partial s_{i}} = \frac{\partial [\sigma^{2}(\mathcal{S}) - \sigma^{2}(\mathcal{T})]^{2}}{\partial \sigma^{2}(\mathcal{S})} \cdot \frac{\partial \sigma^{2}(\mathcal{S})}{\partial s_{i}}
$$
\n
$$
= 2 [\sigma^{2}(\mathcal{S}) - \sigma^{2}(\mathcal{T})] \cdot \frac{\partial \sigma^{2}(\mathcal{S})}{\partial s_{i}}
$$
\n
$$
= 2 [\sigma^{2}(\mathcal{S}) - \sigma^{2}(\mathcal{T})] \cdot \frac{\partial [\frac{1}{|\mathcal{S}|}(s_{i} - \mu(\mathcal{S}))^{2} + \sum_{j \neq i} \frac{1}{|\mathcal{S}|}(s_{j} - \mu(\mathcal{S}))^{2}]}{\partial s_{i}}
$$
\n
$$
= 2 [\sigma^{2}(\mathcal{S}) - \sigma^{2}(\mathcal{T})] \cdot \frac{1}{|\mathcal{S}|} \frac{\partial (s_{i} - \mu(\mathcal{S}))^{2}}{\partial s_{i}}
$$
\n
$$
= 2 [\sigma^{2}(\mathcal{S}) - \sigma^{2}(\mathcal{T})] \cdot \frac{1}{|\mathcal{S}|} \cdot 2(s_{i} - \mu(\mathcal{S})) \cdot \frac{\partial (s_{i} - \mu(\mathcal{S}))}{\partial s_{i}}
$$
\n
$$
= 2 [\sigma^{2}(\mathcal{S}) - \sigma^{2}(\mathcal{T})] \cdot \frac{1}{|\mathcal{S}|} \cdot 2(s_{i} - \mu(\mathcal{S})) \cdot (1 - \frac{1}{|\mathcal{S}|}). \tag{18}
$$
\nSubstitute Equation 17 and Equation 18 back into Equation 16.

Substitute [Equation 17](#page-13-2) and [Equation 18](#page-13-3) back into [Equation 16,](#page-13-4)

<span id="page-13-3"></span>
$$
\frac{\partial \mathcal{L}_{\text{mean}}}{\partial s_i} \cdot \frac{\partial \mathcal{L}_{\text{var}}}{\partial s_i}
$$

$$
= 2 \left[ \mu(S) - \mu(\mathcal{T}) \right] \cdot \frac{1}{|S|} \cdot 2 \left[ \sigma^2(S) - \sigma^2(\mathcal{T}) \right] \cdot \frac{1}{|S|} \cdot 2(s_i - \mu(S)) \cdot (1 - \frac{1}{|S|})
$$

$$
= \left[ \frac{2}{|S|} \right]^3 \left( |S| - 1 \right) \left[ \mu(S) - \mu(\mathcal{T}) \right] \cdot \left[ \sigma^2(S) - \sigma^2(\mathcal{T}) \right] \cdot (s_i - \mu(S)), \tag{19}
$$

Let  $R = [\mu(S) - \mu(\mathcal{T})] \cdot [\sigma^2(S) - \sigma^2(\mathcal{T})]$ , where R is a constant that can be either positive or negative, depending on the values of  $\mu(S)$ ,  $\mu(\mathcal{T}), \sigma^2(S)$ , and  $\sigma^2(\mathcal{T})$ . Suppose  $R > 0$ . In this scenario, instances for which  $(s_i - \mu(S)) < 0$  will encounter contradictory objectives in optimization. Conversely, if  $R < 0$ , instances where  $(s_i - \mu(S)) > 0$  will face similar contradictions.

# A.2 Experiments

<span id="page-13-1"></span>

## A.2.2 Hyper-parameter Settings

[Table 6,](#page-14-2) [Table 7,](#page-14-3) and [Table 8](#page-14-4) list the hyper-parameter settings of our method on experimental datasets. We maintain consistency with SRe2L for a fair comparison.

<span id="page-14-2"></span>

| Distillation        |                                                 | Validation    |                                    |
|---------------------|-------------------------------------------------|---------------|------------------------------------|
| #Iteration          | 1000                                            | #Epoch        | 400                                |
| Batch Size          | 100                                             | Batch Size    | 128                                |
| Optimizer           | Adam with $\{\beta_1, \beta_2\} = \{0.5, 0.9\}$ | Optimizer     | AdamW with weight decay of 0.01    |
| Learning Rate       | 0.25 using cosine decay                         | Learning Rate | 0.001 using cosine decay           |
| Augmentation        | -                                               | Augmentation  | RandomCrop<br>RandomHorizontalFlip |
| $\lambda_{\rm var}$ | 11                                              | Tempreture    | 30                                 |
| $\rho, K$           | $15e^{-3}$ , 12                                 |               |                                    |

Table 6: Hyper-parameter settings for CIFAR-10/100.

<span id="page-14-3"></span>

|               | <b>Distillation</b>                       |               | <b>Validation</b>                         |
|---------------|-------------------------------------------|---------------|-------------------------------------------|
| #Iteration    | 2000                                      | #Epoch        | 200                                       |
| Batch Size    | 100                                       | Batch Size    | 128                                       |
| Optimizer     | Adam with ${eta_1, eta_2} = {0.5, 0.9}$ | Optimizer     | SGD with weight decay of 0.9              |
| Learning Rate | 0.1 using cosine decay                    | Learning Rate | 0.2 using cosine decay                    |
| Augmentation  | RandomResizedCrop<br>RandomHorizontalFlip | Augmentation  | RandomResizedCrop<br>RandomHorizontalFlip |
| $	ext{var}$   | 11                                        | Tempreture    | 20                                        |
| $
ho, K$      | $15e^{-3}$ , 12                           |               |                                           |

Table 8: Hyper-parameter settings for ImageNet-1K.

<span id="page-14-4"></span>

|                     | <b>Distillation</b>                             | <b>Validation</b> |                                           |
|---------------------|-------------------------------------------------|-------------------|-------------------------------------------|
| #Iteration          | 2000                                            | #Epoch            | 300                                       |
| <b>Batch Size</b>   | 100                                             | <b>Batch Size</b> | 128                                       |
| Optimizer           | Adam with $\{\beta_1, \beta_2\} = \{0.5, 0.9\}$ | Optimizer         | AdamW with weight decay of 0.01           |
| Learning Rate       | 0.25 using cosine decay                         | Learning Rate     | 0.001 using cosine decay                  |
| Augmentation        | RandomResizedCrop<br>RandomHorizontalFlip       | Augmentation      | RandomResizedCrop<br>RandomHorizontalFlip |
| $\lambda_{\rm var}$ | 2                                               | Tempreture        | 20                                        |
| $\rho, K$           | $15e^{-3}, 12$                                  |                   |                                           |

<span id="page-14-0"></span>

## A.2.3 Generalization to Vision Transformer-based Models

In [Figure 4,](#page-6-3) we use feature distance  $\mathcal{D}_{fea}$  to measure the diversity of distilled dataset. The following is how the class-wise feature distance is calculated,

$$
\mathcal{D}_{fea}^{c} = \sum_{i=1}^{\text{ipc}} \sum_{j=1}^{\text{ipc}} \|g_{\theta_{\mathcal{T}}}(\tilde{s}_{i}^{c}) - g_{\theta_{\mathcal{T}}}(\tilde{s}_{j}^{c})\|^{2},\tag{20}
$$

where  $g_{\theta_{\mathcal{T}}}(\tilde{s}_i^c)$  and  $g_{\theta_{\mathcal{T}}}(\tilde{s}_j^c)$  are the latent representations of *i*-th and *j*-th synthetic instances of class c, specifically the outputs from the last convolutional layer.

## <span id="page-14-1"></span>A.2.3 Generalization to Vision Transformer-based Models

We acknowledge that our proposed approach cannot be directly applied to models without BN layers, such as Vision Transformers (ViTs). Our baseline solution, SRe2L, involves developing a ViT-BN model that replaces all LayerNorm layers with BN layers and adds additional BN layers between the two linear layers of the feed-forward network. We followed their solution and conducted cross-architecture experiments with DeiT-Tiny [\[40\]](#page-11-13) on the ImageNet-1K dataset. The results are listed in [Table 9.](#page-15-0) The results demonstrate that our approach can be applied to ViT-BN with superior performance compared to the baseline.

<span id="page-15-0"></span>

|              | Methods    | DeiT-Tiny | ResNet-18 | ResNet-50 | ResNet-101 |
|--------------|------------|-----------|-----------|-----------|------------|
| ResNet-18    | SRe2L      | 15.41     | 46.80     | 55.60     | 60.81      |
|              | DWA (ours) | 22.72     | 55.20     | 62.30     | 63.3       |
| DeiT-Tiny-BN | SRe2L      | 25.36     | 24.69     | 31.15     | 33.16      |
|              | DWA (ours) | 37.0      | 32.64     | 40.77     | 43.15      |

Table 9: Generalization to a vision transformer-based model DeiT-Tiny.

## A.2.4 Application to Downstream Tasks

We evaluate our proposed DWA on a continual learning task, based on an effective continual learning method GDumb [\[30\]](#page-10-17). Class-incremental learning was performed under strict memory constraints on the CIFAR-100 dataset, with 20 images per class ( $\rm ipc = 20$ ). CIFAR-100 was divided into five tasks, and a ConvNet was trained on our distilled dataset, with accuracy measured as new classes were incrementally introduced. As shown in [Table 10,](#page-15-1) DWA significantly outperforms SRe2L across all class-incremental stages, demonstrating superior retention of knowledge throughout the learning process.

Table 10: Application to continual learning task.

| <b>Class</b>                                  | 20 | 40        | 60  | 80  | 100 |
|-----------------------------------------------|----|-----------|-----|-----|-----|
| SRe2L                                         |    | 15.7 10.6 | 9.0 | 7.9 | 6.9 |
| DWA (ours) $34.6$ $25.7$ $22.5$ $20.2$ $18.1$ |    |           |     |     |     |

## <span id="page-15-1"></span>A.2.5 Computational Overhead of Distillation

We compare the average time required to generate one ipc using ResNet-18 on CIFAR-100. As shown in [Table 11,](#page-15-2) our proposed DWA incurs only a 7.32% increase in computational overhead while significantly enhancing the diversity of the synthetic dataset. This additional overhead arises from the K-step directed weight perturbation applied before generating each ipc, as detailed in lines 6-7 of Algorithm [1,](#page-2-2)

For  $k = 1$  to K do

$$
\Delta \theta_k = \Delta \theta_{k-1} + \frac{\rho}{K} \nabla L_{\mathcal{S}_0^i} \left( f_{\theta_T + \Delta \theta_{k-1}} \right).
$$

Since each ipc requires 1000 iterations of forward-backward propagation for generation, the additional  $K = 12$  forward-backward propagations required by DWA are negligible in the overall distillation process.

<span id="page-15-2"></span>Table 11: Computational overhead of distillation on CIFAR-100 with ResNet-18.

| Methods    | Avg. time for generating one ipc |
|------------|----------------------------------|
| SRe2L      | 116.58 s (100%)                  |
| DWA (ours) | 125.12 s (107.32%)               |
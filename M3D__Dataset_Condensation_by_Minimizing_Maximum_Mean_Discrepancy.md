# M3D: Dataset Condensation by Minimizing Maximum Mean Discrepancy

Hansong <PERSON> $^{1,\,2*}$ , <PERSON><PERSON><PERSON> $^{1,\,2*}$ , <PERSON><PERSON><PERSON> $^{1,\,2}$ , <PERSON> $^3$ , <PERSON><PERSON> Ge $^{1,\,2*}$ 

<sup>1</sup>Institute of Information Engineering, Chinese Academy of Sciences, Beijing 100092, China

<sup>2</sup>School of Cyber Security, University of Chinese Academy of Sciences, Beijing 100049, China

<sup>3</sup>Department of Communication Engineering, Shanghai University, Shanghai 200040, China

{zhanghansong,lishikun,wangpengju,geshiming}@iie.ac.cn, <EMAIL>

## Abstract

Training state-of-the-art (SOTA) deep models often requires extensive data, resulting in substantial training and storage costs. To address these challenges, dataset condensation has been developed to learn a small synthetic set that preserves essential information from the original large-scale dataset. Nowadays, optimization-oriented methods have been the primary method in the field of dataset condensation for achieving SOTA results. However, the bi-level optimization process hinders the practical application of such methods to realistic and larger datasets. To enhance condensation efficiency, previous works proposed Distribution-Matching (DM) as an alternative, which significantly reduces the condensation cost. Nonetheless, current DM-based methods still yield less comparable results to SOTA optimization-oriented methods. In this paper, we argue that existing DM-based methods overlook the higher-order alignment of the distributions, which may lead to sub-optimal matching results. Inspired by this, we present a novel DM-based method named M3D for dataset condensation by Minimizing the Maximum Mean Discrepancy between feature representations of the synthetic and real images. By embedding their distributions in a reproducing kernel Hilbert space, we align all orders of moments of the distributions of real and synthetic images, resulting in a more generalized condensed set. Notably, our method even surpasses the SOTA optimization-oriented method IDC on the high-resolution ImageNet dataset. Extensive analysis is conducted to verify the effectiveness of the proposed method. Source codes are available at https://github.com/Hansong-Zhang/M3D.

## Introduction

In the era of deep learning, the utilization of largescale datasets comprising millions of samples has become an indispensable prerequisite for achieving state-of-the-art (SOTA) models (Zhao and Bilen 2021a; Xia et al. 2022). However, the associated storage expenses and computational costs involved in training these models present formidable challenges, often rendering them beyond the reach of startups and non-profit organizations (Wang et al. 2018; Coleman et al. 2019; Sorscher et al. 2022).

Image /page/0/Figure/13 description: The image displays three distinct patterns labeled (a), (b), and (c). Each pattern consists of a central arrangement of colored circles surrounded by a grid of gray crosses. Pattern (a) features green circles arranged in a somewhat irregular, clustered formation. Pattern (b) shows blue circles clustered more tightly in the center, with a few scattered further out. Pattern (c) presents red circles arranged in a more symmetrical, radial pattern around the center. All three patterns are set against a white background.

Figure 1: Illustration of the importance of the higher-order alignment of distributions, where circles represent the representations of synthesized examples while crosses represent the representations of original examples. (a) The misaligned distributions with different second-order moments; (b) the misaligned distributions with different third-order moments; (c) the aligned distributions.

To alleviate the challenges associated with larger datasets, Dataset Condensation (DC) (Wang et al. 2018) has emerged to reduce the training cost by synthesizing a compact set of informative images. Since its proposal, DC has attracted significant attention for addressing the challenges posed by the data burden (Cazenavette et al. 2022; Zhao and Bilen 2021b; Kim et al. 2022; Wang et al. 2022; Zhao and Bilen 2023). Typically, DC condenses the dataset by minimizing the distance between real and synthetic images via a pre-defined metric. Based on whether to perform a costly bi-level optimization (Liu et al. 2021), these methods can be generally categorized into two groups: (1) *Optimization-Oriented* methods (Zhao and Bilen 2021b; Kim et al. 2022; Zhao and Bilen 2021a; Cazenavette et al. 2022), which usually generate condensed examples by conducting performance matching or parameter matching via a bi-level optimization (Yu, Liu, and Wang 2023); (2) *Distribution-Matching(DM)-based* methods (Wang et al. 2022; Zhao and Bilen 2023), which focus on aligning the feature distributions between real and synthetic data. Optimization-oriented methods have faced criticism for their inefficiency, primarily due to the involvement of bi-level optimization modules and time-consuming network updating processes (Zhang et al. 2023; Wang et al. 2022; Zhao and Bilen 2023). In contrast, DM-based methods do not involve such nested optimization of models, which significantly reduces the computa-

<sup>\*</sup>These authors contributed equally.

<sup>†</sup>Corresponding Author

Copyright © 2024, Association for the Advancement of Artificial Intelligence (www.aaai.org). All rights reserved.

tional cost associated with dataset condensation. Nevertheless, the informativeness of the condensed examples generated by current DM-based methods may not be as comparable to those produced by optimization-oriented methods.

In this paper, we address a crucial oversight in existing DM-based methods, which is their neglect of higher-order moments of the distribution. As illustrated in Fig. 1, despite sharing the same first moment, the representation distributions of original and synthetic examples with misaligned second-order moments (Fig. 1a) or third-order moments (Fig. 1b) can exhibit much distinct characteristics. Motivated by this issue, we propose a novel DM-based method involving Minimizing the Maximum Mean Discrepancy (M3D) between the representation distributions of the real and synthetic images. Unlike previous DM-based methods that solely embed images in a feature representation space and align the first moment, our method further embeds the distribution of feature representations into a reproducing kernel Hilbert space. This transformation allows us to represent the infinite order of moments in a kernel-function form. By leveraging empirical estimation, we can readily align both first- and higher-order moments of the real and synthetic data with theoretical guarantees. Our method not only maintains the efficiency of the DM-based method but also exhibits significant improvements. Remarkably, the efficiency of our method makes it easily applicable to realistic and larger datasets like ImageNet (Deng et al. 2009).

Before delving into technical details, we clearly emphasize our contribution as:

- We reveal the importance of the alignment of higherorder moments for distribution matching, which is overlooked by previous DM-based methods.
- We propose a theoretical-guaranteed method for dataset condensation named M3D, which applies the classical kernel method to represent an infinite number of moments in a kernel-function form, enabling the improved alignment of the higher-order moments of the representation distributions.
- We conduct extensive experiments to demonstrate the effectiveness and efficiency of our proposed method, where M3D yields SOTA performance with strong generalization across various scenarios.

## Background

Problem Fromulation. Dataset Condensation (DC) (Wang et al. 2018), also called dataset distillation, targets to condense a large-scale dataset  $\mathcal{T} = \{(\boldsymbol{x}_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  into a tiny dataset  $S = \{(s_j, y_j)\}_{j=1}^{|S|}$ , so that an arbitrary model trained on  $S$  achieves comparable performance to the one trained on  $\mathcal T$ . Typically, the condensed  $\mathcal S$  is obtained by minimizing the information loss between the synthesized and the original examples, which can be formulated as:

$$
S^* = \underset{S}{\arg\min} D(\phi(\mathcal{T}), \phi(\mathcal{S})), \tag{1}
$$

where D represents a distance metric such as Mean Square Error (MSE), and  $\phi$  denotes the matching objective. As men-

tioned before, various objectives can lead to different optimization processes (Yu, Liu, and Wang 2023), and based on whether to perform a costly bi-level optimization, existing methods can be mainly divided into optimization-oriented methods and Distribution-Matching (DM)-based methods.<sup>1</sup>

Distribution Matching. Although optimization-oriented methods can achieve the SOTA performance, the inefficiency of them poses a significant obstacle to their application in realistic and larger datasets (Zhang et al. 2023). In response, DM-based methods have been developed as an alternative. In their pioneering work, DM (Zhao and Bilen 2023) introduces a surrogate matching objective that focuses on aligning the representation distributions of  $S$  and  $T$ . This objective can be formulated as:

$$
S^* = \underset{S}{\arg\min} E_{\theta \sim P_{\theta}} \left[ D(g_{\theta}(S), g_{\theta}(T)) \right], \quad (2)
$$

where  $g_{\theta}$  is the deep encoder network parameterized as  $\theta$ , which is instanced by the model  $f_{\theta}$  without the output layer. With MSE as the distance metric, the training objective of DM can be reformulated as:

$$
S^* = \underset{S}{\arg\min} E_{\theta \sim P_{\theta}} \|\frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} g_{\theta}(\boldsymbol{x}_i) - \frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} g_{\theta}(s_j) \|^2,
$$
\n(3)

which works as minimizing the gap between empirical first moment of the representation distributions between  $S$  and  $T$ . Compared to previous optimization-oriented methods, DM (Zhao and Bilen 2023) eliminates the need for network updating, relying instead on randomly initialized encoders. Furthermore, the costly bi-level optimization is avoided in DM, leading to significantly improved training efficiency.

*Remark.* Given the lower effectiveness of DM compared to optimization-oriented SOTA methods, efforts have been made to enhance DM and generate more informative examples in previous works (Zhao et al. 2023a; Sajedi et al. 2023). For instance, IDM (Zhao et al. 2023a) enhances DM through techniques such as partitioning, enriched model sampling, and class-aware regularization. Similarly, DataDAM (Sajedi et al. 2023) improves DM by incorporating attention matching. In contrast to these methods where only the first-order moment is matched, our focus is on enhancing DM through distribution embedding and higher-order moments, which are also noticed but not addressed explicitly by IDM (Zhao et al. 2023a).

Reproducing Kernel Hilbert Space. We provide a brief recap of the Reproducing Kernel Hilbert Space (RKHS) (Muandet et al. 2017; Smola et al. 2007; Borgwardt et al. 2006) here, which serves as the foundation of our method.

Definition 1 *Given a kernel* K*,* H *is a Hilbert space of functions*  $\mathcal{X} \to \mathbb{R}$  *with dot product*  $\langle \cdot, \cdot \rangle$ *, if*  $\forall \phi$ *, satisfying the reproducing property:*

$$
\langle \phi(\cdot), \mathcal{K}(x, \cdot) \rangle = \phi(x). \tag{4}
$$

<sup>&</sup>lt;sup>1</sup>Note that the introduction about more dataset condensation works can be found in the Appendix.

|                 | 1st-order (mean) | 2nd-order (variance) | 3rd-order (skewness) | Test Acc.            |
|-----------------|------------------|----------------------|----------------------|----------------------|
| DM              | 4.91             | 7.37                 | 6.76                 | 48.9                 |
| +2nd Reg.       | 4.51             | 6.96                 | 6.35                 | 52.1 (↑ 3.2)         |
| +2nd & 3rd Reg. | 3.69             | 6.14                 | 5.94                 | 53.9 (↑ 5.0)         |
| <b>M3D</b>      | <b>0.82</b>      | <b>1.23</b>          | <b>1.64</b>          | <b>63.5</b> (↑ 14.6) |

Table 1: The distance between the moments of the condensed set and the original training set. "+2nd(3rd) Reg." denotes adding the regularization of aligning the 2nd(3rd) order moment to the original loss of DM.

That is to say, with the RKHS, we can map a function  $f$  on  $X$  to its value at x as an inner product. In addition to the reproducing property mentioned above, the kernel function  $K$  must also satisfy the following two properties:

**Symmetry**: 
$$
\mathcal{K}(x, x') = \mathcal{K}(x', x)
$$
  
**Positive**:  $\mathcal{K}(\cdot, \cdot) \ge 0$ 

Commonly used kernel function include the polynomial kernel  $\mathcal{K}(x, x') = (x^{\mathsf{T}} x' + c)^d$ , the Gaussian RBF kernel  $\mathcal{K}(x, x') = \exp(-\lambda \|x - x'\|^2)$ , and the Linear kernel  $\mathcal{K}(x, x') = x^{\intercal} x'.$ 

## Methodology

In this section, we begin by analyzing the importance of the alignment of higher-order moments for distribution matching. Subsequently, we propose our method M3D by exploiting the classical kernel method (Altun and Smola 2006; Borgwardt et al. 2006) to align the higher-order moments of the representation distributions between real and synthesized data with theoretical guarantees.

### Importance of the Higher-Order Alignment

As shown in Eq. (3), it is evident that DM (Zhao and Bilen 2023) only considers aligning the first moment (mean) of the representation distributions, while neglecting higher-order moments. At a high level, it may lead to the higher-order misalignment of the representation distributions of its condensed data and original data.

To investigate this misalignment issue and highlight the importance of the higher-order alignment, we assessed the moment distances between the condensed set and the original training set on CIFAR-10 with 10 images per class. This was done by incorporating higher-order moment regularization terms into the original loss of DM (Zhao and Bilen 2023). The results, presented in Table 1, reveal that adding second-order regularization notably decreases the distance between higher-order moments of the condensed and original data, underscoring the inadequacy of aligning only the first moment. Furthermore, performing more regularization enhances the condensed dataset's performance through improved higher-order alignment. These results underscore the critical role of higher-order moment alignment in distribution matching, which is neglected in previous works.

## Minimizing Maximum Mean Discrepancy

From the preceding analysis, it becomes evident that perfecting distribution matching necessitates the consideration of higher-order moments. While incorporating higher-order regularizations directly aids in aligning these moments, it is limited to finite moments. Moreover, tuning the regularization coefficient becomes increasingly challenging with a growing number of regularization terms. In this subsection, we represent a new DM-based method that aligns the infinite order of moments in a kernel-function form. We depict the framework of the proposed M3D in Fig. 2.

Embedding Distribution in RKHS. Denoting the distribution of representations for real and synthetic examples as  $g_{\theta}(\mathcal{T}) \sim \mathbf{P}_{\mathcal{T}}$  and  $g_{\theta}(\mathcal{S}) \sim \mathbf{P}_{\mathcal{S}}$  respectively, where  $g_{\theta}$  denotes the representation extractor parameterized by  $\theta$ . As the order of moments extends infinitely, it is impractical to explicitly align an infinite number of moments. To address this, we need to first embed the distribution in an RKHS  $H$ :

$$
\mu[\boldsymbol{P}_{\mathcal{T}/\mathcal{S}}] := E_{\mathcal{T}/\mathcal{S}}[\mathcal{K}(g_{\theta}(\boldsymbol{x}/s), \cdot)],\tag{5}
$$

which has been proven to be a valid embedding for distance based on the following theorem:

Theorem 1 *(Fukumizu, Bach, and Jordan 2004) If the kernel function* K *is universal, then the mean map*  $\mu := \mathbf{P} \rightarrow$  $\mu[P]$  *is injective.* 

Maximum Mean Discrepancy. Via the reproducing property of  $H$ ,  $\forall \phi$ , we have

$$
\langle \phi, \mu[\mathbf{P}_{\mathcal{T}/\mathcal{S}}] \rangle = E_{\mathcal{T}/\mathcal{S}}[\phi(g_{\theta}(\mathbf{x}/\mathbf{s}))], \tag{6}
$$

which indicate that we can compute expectations w.r.t.  $P_{\tau/s}$ by taking the inner product with the distribution kernel embedding  $\mu[P_{\mathcal{T}/\mathcal{S}}]$ . This property is favorable because it helps us to calculate the Maximum Mean Discrepancy (MMD) between  $P_T$  and  $P_S$ :

$$
\begin{aligned} \text{MMD}(\boldsymbol{P}_{\mathcal{T}}, \boldsymbol{P}_{\mathcal{S}}) &:= \sup (E_{\mathcal{T}}[\phi(g_{\theta}(\boldsymbol{x}))] - E_{\mathcal{S}}[\phi(g_{\theta}(\boldsymbol{s}))]) \\ &= \sup \langle \phi, \mu[\boldsymbol{P}_{\mathcal{T}}] - \mu[\boldsymbol{P}_{\mathcal{S}}] \rangle, \end{aligned}
$$

where  $\phi \in \mathcal{H}$  and  $\|\phi\|_{\mathcal{H}} \leq 1$ . In addition, based on the Cauchy-Schwarz inequality, we have  $\langle \phi, \mu[P_{\mathcal{T}}] - \mu[P_{\mathcal{S}}] \rangle \leq$  $\|\phi\|_{\mathcal{H}}\|\mu[P_{\mathcal{T}}]-\mu[P_{\mathcal{S}}]\|_{\mathcal{H}}\leq \|\mu[P_{\mathcal{T}}]-\mu[P_{\mathcal{S}}]\|_{\mathcal{H}},$  hence the MMD can be further simplified as:

$$
MMD(P_T, P_S) = ||\mu[P_T] - \mu[P_S]||. \tag{7}
$$

It should be noted that  $\mu[P_T]$  and  $\mu[P_S]$  are characterized by infinite-dimensional spaces, which renders direct computation unattainable. However, we can leverage the reproducing property of the RKHS to transform them into a more tractable form using the kernel function  $K$ . This transformation can be formally expressed as:

$$
\text{MMD}^2(\boldsymbol{P}_\mathcal{T}, \boldsymbol{P}_\mathcal{S}) = \mathcal{K}_{\mathcal{T}, \mathcal{T}} + \mathcal{K}_{\mathcal{S}, \mathcal{S}} - 2\mathcal{K}_{\mathcal{T}, \mathcal{S}},\qquad(8)
$$

where  $\mathcal{K}_{X,Y} = E_{X,Y}[\mathcal{K}(g_{\theta}(x), g_{\theta}(y))]$  with  $x \sim X, y \sim$  $Y$ . Due to limited page, we provide the derivation of Eq.  $(8)$ in the Appendix. Last, note that we only have access to

Image /page/3/Figure/0 description: This diagram illustrates a method for aligning feature distributions of real and synthetic images using a Convolutional Neural Network (CNN). On the left, a grid of nine real images of cars, labeled as 'Real Images T', and a grid of four synthetic images, labeled as 'Synthetic Images S', are shown. Both sets of images are fed into a CNN, denoted as 'gθ'. The output of the CNN is a set of feature representations. The features from real images are represented by red stars, labeled as 'gθ(T)', and the features from synthetic images are represented by green pentagons, labeled as 'gθ(S)'. These feature representations are then mapped into a 'Reproducing Kernel Hilbert Space ℋ'. Within this space, the distribution of real image features is denoted by '~PT' and has a mean represented by a red star labeled 'μ[PT]'. The distribution of synthetic image features is denoted by '~PS' and has a mean represented by a green pentagon labeled 'μ[PS]'. The diagram highlights the Maximum Mean Discrepancy (MMD) loss, labeled as 'ℒMMD', which measures the distance between these two distributions, aiming to minimize it to align the synthetic and real feature spaces.

Figure 2: The framework of M3D. After extracting the representations via a encoder network, the distributions of real and synthetic representations are further embedded in the Reproducing Kernel Hilbert Space (RKHS), where the M3D loss  $\mathcal{L}_{M3D}$  is calculated to guild the update of synthetic examples for higher-order distribution alignment.

the datasets  $T$  and  $S$  rather than their underlying distributions. In order to tackle this issue, denoting the empirical approximation of  $\mu[P_T]$  and  $\mu[P_S]$  as  $\mu[T] =$  $\frac{1}{|\mathcal{T}|}\sum_{i=1}^{|\mathcal{T}|}\mathcal{K}(g_\theta(\bm{x}_i),\cdot),\,\mu[\mathcal{S}] \;=\; \frac{1}{|\mathcal{S}|}\sum_{j=1}^{|\mathcal{S}|}\mathcal{K}(g_\theta(\bm{s}_j),\cdot)$  respectively, we introduce the following theorem:

**Theorem 2** *(Altun and Smola 2006) Assume that*  $||\phi||_{\infty}$  ≤  $R$  *for all*  $\phi$  ∈  $H$  *with*  $\|\phi\|_{\mathcal{H}}$  ≤ 1*. Then with probability at least*  $1 - \delta$ ,  $\|\mu[\mathbf{P}_{\mathcal{T}/\mathcal{S}}] - \mu[\mathcal{T}/\mathcal{S}]\|$   $\leq$  $2\bar{R}(\mathcal{H},\boldsymbol{P}_{\mathcal{T}/\mathcal{S}})+R\sqrt{-|\mathcal{T}/\mathcal{S}|^{-1}\log(\delta)}$ , where  $\bar{R}(\mathcal{H},\boldsymbol{P}_{\mathcal{T}/\mathcal{S}})$ *is the Rademacher average which is ensured to yield error of*  $\mathcal{O}(\sqrt{|T/S|^{-1}})$ *.* 

Theorem 2 guarantees that the empirical approximations  $\mu$ [T/S] are good proxies for  $\mu$ [P<sub>T/S</sub>]. Therefore, we can modify Eq. (8) to the following empirical form as the M3D loss:

$$
\mathcal{L}_{\text{M3D}} = \hat{\text{MMD}}^2(\boldsymbol{P}_\mathcal{T}, \boldsymbol{P}_\mathcal{S}) = \hat{\mathcal{K}}_{\mathcal{T}, \mathcal{T}} + \hat{\mathcal{K}}_{\mathcal{S}, \mathcal{S}} - 2\hat{\mathcal{K}}_{\mathcal{T}, \mathcal{S}}, \tag{9}
$$

where  $\hat{K}_{X,Y} = \frac{1}{|X| \cdot |Y|} \sum_{i=1}^{|X|} \sum_{j=1}^{|Y|} \mathcal{K}(g_{\theta}(x_i), g_{\theta}(y_j))$  with  ${x_i}_{i=1}^{|X|} \sim X, {y_j}_{j=1}^{|Y|} \sim Y$ . Based on the analysis above, we have successfully achieved the transformation of an infinite number of moments into a finite form using RKHS. As shown in Table 1, this transformation allows us to effectively align the distributions between  $T$  and  $S$  during the condensing process.

## Training Algorithm of M3D

The pseudo-code of M3D is provided in the Appendix. In addition to the kernel method, we exploit the following two techniques to enhance the distribution matching.

**Factor**  $\&$  Up-sampling. The factor technique (Kim et al. 2022), also termed as partitioning and expansion augmentation in IDM (Zhao et al. 2023a), aims to increase the number of representations extracted from  $S$  without additional storage cost. Specifically, with the factor parameter being  $l$ , each image  $s_i \in S$  is factorized into  $l \times l$  mini-examples and then up-sampled to its original size in training:

$$
s_i \xrightarrow{\text{Factor}} \begin{bmatrix} s_i^{1,1} & \dots & s_i^{1,l} \\ \vdots & \ddots & \vdots \\ s_i^{l,1} & \dots & s_i^{l,l} \end{bmatrix} \xrightarrow{\text{Up-sample}} \{s_i^{'1}, s_i^{'2}, \dots, s_i^{'l \times l}\} \quad (10)
$$

In this way, the storage space of  $S$  can be further leveraged. Following previous works, the same factor technique is incorporated into our framework, where we further exploit its benefits in aligning distributions in higher-order moments.

Iteration per Random Model. Following DM (Zhao and Bilen 2023), we employ multiple randomly initialized models to extract representation embeddings from both  $\mathcal T$  and  $S$ . In contrast to DM, where only a single-step iteration is performed for each model, we posit that relying solely on the representation distributions of one batch of real and synthetic examples may introduce matching biases. To address this, without incurring additional memory usage, we empirically observe that conducting multiple iterations per model (IPM) enhances the performance of the condensed set.

## Experiments

In this section, we begin by comparing our proposed M3D with SOTA baselines on multiple benchmark datasets. Subsequently, we conduct an in-depth examination of M3D through ablation analysis.

Image /page/4/Figure/0 description: This image contains four line graphs, each plotting Test Accuracy (%) against Training Steps. The graphs are organized into two rows and two columns, with the top row showing results for SVHN and the bottom row for CIFAR-10. Within each dataset, there are two graphs, one for IPC=10 and one for IPC=50. Each graph displays three lines representing different methods: M3D (red), M3D w/o Fac. (blue), and DM (green). The M3D method consistently achieves the highest test accuracy across all conditions, followed by M3D w/o Fac., and then DM. The SVHN (IPC=50) graph shows the highest overall accuracy, with M3D reaching approximately 88% accuracy. The CIFAR-10 (IPC=10) graph shows the lowest overall accuracy, with M3D reaching around 62% accuracy.

Figure 3: Performance comparison between M3D and DM across varying training steps. M3D w/o Fac denotes the M3D without using the factor technique.

| Dataset        |                          | IPC Ratio $(\%)$            | <b>Coreset Selection</b> | Random Herding K-Center                                                                                                                                                                                                                                                                                                                 | DC | <b>DSA</b> | CAFE | Dataset Condensation<br>CAFE+DSA                                 | DM                                                                                                   | IDM                                                                              | M3D                                                                                                                                                                 | Whole |
|----------------|--------------------------|-----------------------------|--------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----|------------|------|------------------------------------------------------------------|------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|
| <b>MNIST</b>   | 10<br>50                 | 0.017<br>0.17<br>0.83       |                          | $(64.9_{\pm 3.5} 89.2_{\pm 1.6} 89.3_{\pm 1.5} 91.7_{\pm 0.5} 88.7_{\pm 0.6} 93.1_{\pm 0.3}$<br>95.1 <sub>±0.9</sub> 93.7 <sub>±0.3</sub> 84.4 <sub>±1.7</sub> 97.4 <sub>±0.2</sub> 97.8 <sub>±0.1</sub> 97.2 <sub>±0.2</sub><br>$97.9_{\pm 0.2}$ $94.8_{\pm 0.2}$ $97.4_{\pm 0.3}$ $98.8_{\pm 0.2}$ $99.2_{\pm 0.1}$ $98.6_{\pm 0.2}$  |    |            |      | $90.8_{\pm 0.5}$<br>$97.5_{\pm 0.1}$<br>$98.9_{\pm 0.2}$         | $89.7_{\pm 0.6}$<br>$97.5_{\pm 0.1}$<br>$98.6{\scriptstyle \pm0.1}$                                  | $\overline{\phantom{a}}$                                                         | $94.4_{+0.2}$<br>$97.6_{\pm 0.1}$ $99.6_{\pm 0.0}$<br>$98.2{\pm}0.2$                                                                                                |       |
| <b>F-MNIST</b> | 10<br>50                 | 0.017<br>0.17<br>0.83       |                          | $51.4_{\pm 3.8}$ $67.0_{\pm 1.9}$ $66.9_{\pm 1.8}$ $70.5_{\pm 0.6}$ $70.6_{\pm 0.6}$ $77.1_{\pm 0.9}$<br>$73.8_{\pm 0.7}$ $71.1_{\pm 0.7}$ $54.7_{\pm 1.5}$ $82.3_{\pm 0.4}$ $84.6_{\pm 0.3}$ $83.0_{\pm 0.4}$<br>$82.5_{\pm 0.7}$ 71.9 <sub>±0.8</sub> 68.3 <sub>±0.8</sub> 83.6 <sub>±0.4</sub> <b>88.7</b> ±0.2 84.8 <sub>±0.4</sub> |    |            |      | $73.7_{+0.7}$<br>$83.0_{\pm 0.3}$<br>$88.2{\scriptstyle \pm0.3}$ | $70.7_{\pm 0.6}$ <sup>†</sup><br>$83.5_{\pm 0.3}$ <sup>T</sup><br>$88.1_{\pm 0.6}$ <sup>T</sup>      | $\overline{\phantom{0}}$<br>$\overline{\phantom{0}}$<br>$\overline{\phantom{0}}$ | $80.7_{\pm 0.3}$<br>85.0 $_{\pm 0.1}$ 93.5 $_{\pm 0.1}$<br>$86.2{\scriptstyle \pm0.3}$                                                                              |       |
| <b>SVHN</b>    | $\mathbf{1}$<br>10<br>50 | 0.014<br>0.14<br>0.7        |                          | $14.6_{\pm 1.6}$ $20.9_{\pm 1.3}$ $21.0_{\pm 1.5}$ $31.2_{\pm 1.4}$ $27.5_{\pm 1.4}$ $42.6_{\pm 3.3}$<br>$35.1_{\pm 4.1}$ $50.5_{\pm 3.3}$ $14.0_{\pm 1.3}$ $76.1_{\pm 0.6}$ $79.2_{\pm 0.5}$ $75.9_{\pm 0.6}$<br>$70.9_{\pm 0.9}$ $72.6_{\pm 0.8}$ $20.1_{\pm 1.4}$ $82.3_{\pm 0.3}$ $84.4_{\pm 0.4}$ $81.3_{\pm 0.3}$                 |    |            |      | $42.9_{\pm 3.0}$<br>$77.9_{+0.6}$<br>$82.3{\scriptstyle \pm0.4}$ | $30.3_{\pm0.1}$ <sup>†</sup><br>$73.5_{\pm 0.5}$ <sup>T</sup><br>$82.0_{\pm 0.2}$ <sup>T</sup>       | $\overline{\phantom{0}}$<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{0}}$ | $62.8_{\pm 0.5}$<br>83.3 $\pm$ 0.7 95.4 $\pm$ 0.1<br>$89.0{\scriptstyle \pm0.2}$                                                                                    |       |
| $CIFAR-10$     | 10<br>50                 | 0.02<br>0.2<br>$\mathbf{1}$ |                          | $14.4_{\pm 2.0}$ $21.5_{\pm 1.2}$ $21.5_{\pm 1.3}$ $28.3_{\pm 0.5}$ $28.8_{\pm 0.7}$ $30.3_{\pm 1.1}$<br>$26.0_{\pm 1.2}$ 31.6 $_{\pm 0.7}$ 14.7 $_{\pm 0.9}$ 44.9 $_{\pm 0.5}$ 52.1 $_{\pm 0.5}$ 46.3 $_{\pm 0.6}$<br>$ 43.4_{\pm 1.0}$ 40.4 $_{\pm 0.6}$ 27.0 $_{\pm 1.4}$ 53.9 $_{\pm 0.5}$ 60.6 $_{\pm 0.5}$ 55.5 $_{\pm 0.6}$      |    |            |      | $31.6_{+0.8}$<br>$50.9_{\pm 0.5}$<br>$62.3{\scriptstyle \pm0.4}$ | $26.0_{\pm 0.8}$ 45.6 $_{\pm 0.7}$ 45.3 $_{\pm 0.3}$<br>63.0 $\pm$ 0.4 67.5 $\pm$ 0.1 69.9 $\pm$ 0.5 |                                                                                  | 48.9 $\pm$ 0.6 58.6 $\pm$ 0.1 63.5 $\pm$ 0.2 84.8 $\pm$ 0.1                                                                                                         |       |
| $CIFAR-100$ 10 | 50                       | 0.2<br>2<br>10              |                          | 4.2 $\pm$ 0.3 8.4 $\pm$ 0.3 8.3 $\pm$ 0.3 12.8 $\pm$ 0.3 13.9 $\pm$ 0.3 12.9 $\pm$ 0.3<br>$14.6_{\pm 0.5}$ 17.3 <sub>±0.3</sub> 7.1 <sub>±0.2</sub> 25.2 <sub>±0.3</sub> 32.3 <sub>±0.3</sub> 27.8 <sub>±0.3</sub><br>$30.0_{\pm 0.4}$ $33.7_{\pm 0.5}$ $30.5_{\pm 0.3}$ $ 42.8_{\pm 0.4}$ $37.9_{\pm 0.3}$                             |    |            |      | $14.0_{\pm 0.3}$<br>$31.5_{\pm 0.2}$<br>$42.9_{\pm 0.2}$         |                                                                                                      |                                                                                  | $11.4_{\pm 0.3}$ 20.1 $_{\pm 0.3}$ 26.2 $_{\pm 0.3}$<br>29.7 $\pm$ 0.3 45.1 $\pm$ 0.1 42.4 $\pm$ 0.2 56.2 $\pm$ 0.3<br>43.6 $\pm$ 0.4 50.0 $\pm$ 0.2 50.9 $\pm$ 0.7 |       |

Table 2: Comparison with previous coreset selection and dataset condensation methods on low-resolution datasets. All the datasets are condensed using a 3-layer ConvNet. IPC: image(s) per class. Ratio (%): the ratio of condensed examples to the whole training set. "†" denotes the result is reproduced by us. Best results are in bold. Note that some entries are marked as "-" because of scalability issues or the results are not reported.

## Experimental Setups

Datasets. We evaluate the classification performance of networks trained on synthetic images that have been condensed using various baselines as well as our proposed method M3D. Our evaluation encompasses five low-resolution datasets: MNIST (LeCun et al. 1998), Fashion-MNIST (F-MNIST) (Xiao, Rasul, and Vollgraf 2017), SVHN (Netzer et al. 2011), CIFAR-10 (Krizhevsky, Hinton et al. 2009), and CIFAR-100 (Krizhevsky, Hinton et al. 2009). In addition, we also conduct experiments on the high-resolution dataset ImageNet subsets (Deng et al. 2009). Detailed descriptions of datasets can be found in the Appendix.

Network Architectures. We use a depth-3 ConvNet (Sagun et al. 2017) for the low-resolution datasets, and a ResNetAP-10 (Kim et al. 2022) (ResNet-10 with the strided convolution replaced by average pooling) for the high-resolution ImageNet subsets.

Baselines. We employ an extensive range of methods as baselines for comparison. Regarding coreset selection methods, we consider the following: (1) Random, (2) Herding (Welling 2009), and (3) K-Center (Farahani and Hekmatfar 2009; Sener and Savarese 2017). For optimizationoriented DC methods, we evaluate (4) DC (Zhao and Bilen 2021b), (5) DSA (Zhao and Bilen 2021a), (6) IDC (Kim et al. 2022). On the other hand, for DM-based DC methods, we include (7) CAFE (Wang et al. 2022), (8) its variant CAFE+DSA (Wang et al. 2022), (9) DM (Zhao and Bilen 2023) and (10) IDM (Zhao et al. 2023a). We provide detailed descriptions of baselines in the Appendix.

Metric. Following previous works (Wang et al. 2018; Zhao and Bilen 2023; Kim et al. 2022), we employ the test accuracy of networks trained on condensed examples as the evaluation metric. All the networks are trained from scratch for multiple times — 10 times for low-resolution datasets and

Image /page/5/Picture/0 description: The image displays a grid of 8x8 images, with three such grids arranged side-by-side. Each small image within the grids contains a number, likely from a dataset of street view house numbers. The numbers range from 0 to 9, and some appear as two-digit numbers or have slight variations in font and color. The overall impression is a collection of digit samples, possibly for machine learning training or evaluation.

(a) Initialized SVHN images. (b) Condensed images by DM. (c) Condensed images by M3D.

(c) Condensed images by M3D.

Figure 4: Visualization of the condensed set of SVHN dataset with 10 images per class. The condensed set is generated by (b) DM and (c) M3D. Both DM and M3D use the same initialization as (a) shows.

| IPC     | ImageNet-10 |       |      |       | ImageNet-100 |        |      |        |
|---------|-------------|-------|------|-------|--------------|--------|------|--------|
|         | 10          | 20    | 10   | 20    | 10           | 20     | 10   | 20     |
|         | Acc.        | Time  | Acc. | Time  | Acc.         | Time   | Acc. | Time   |
| Random  | 46.9        | -     | 51.8 | -     | 20.7         | -      | 29.7 | -      |
| Herding | 50.4        | -     | 57.5 | -     | 22.6         | -      | 31.1 | -      |
| DSA     | 52.7        | 27.0h | 57.4 | 51.4h | 21.8         | 9.7h   | 30.7 | 23.9h  |
| IDC     | 72.8        | 70.1h | 76.6 | 92.8h | 46.7         | 141.0h | 53.7 | 185.0h |
| DM      | 52.3        | 1.4h  | 59.3 | 3.6h  | 22.3         | 2.8h   | 30.4 | 2.8h   |
| M3D     | 73.4        | 1.1h  | 76.8 | 3.1h  | 46.9         | 3.5h   | 55.5 | 4.2h   |

Table 3: The performance and efficiency comparison on high-resolusion ImageNet-subsets. The synthetic examples are condensed using ResNetAP-10. The minimal time required for obtaining the best performance is reported, which is measured on a single RTX-A6000 GPU with same batch size. For ImageNet-100, all methods are splitted into five sub-tasks with 20 classes each for faster optimization.

3 times for ImageNet subsets. We report the average performance and the standard deviation.

Implementation Details. We employ the Gaussian kernel for RKHS by default. The number of iterations is set to 10K for all low-resolution datasets. While for ImageNet subsets, we set 1K iterations. Additionally, the number of iterations per model is consistently set to 5 across all datasets. Regarding the learning rates for the condensed data, we assign a value of 1 for low-resolution datasets including F-MNIST, SVHN and CIFAR-10/100. For ImageNet subsets, we adopt a learning rate of 1e-1. Following IDC (Kim et al. 2022), the factor parameter  $l$  is set to 2 for low-resolution datasets and 3 for ImageNet subsets.

## Comparison to the SOTA Methods

Table 2 and Table 3 present the comparison of our method with coreset selection and dataset condensation methods.

The results show that synthetic examples are more informative than the selected ones, especially when the number of image(s) per class is small. This is attributed to the fact that synthetic examples are not confined to the set of real examples. Furthermore, our method consistently outperforms other baselines across a diverse set of scenarios. Remarkably, M3D achieves over a 5% higher accuracy than the best baseline on SVHN, CIFAR-10 (IPC=10), and CIFAR-100 (IPC=1). Notably, for high-resolution ImageNet subsets (Deng et al. 2009; Kim et al. 2022; Zhang et al. 2023), our method surpasses all baselines in test accuracy, including the current SOTA optimization-oriented IDC (Kim et al. 2022). It is worth noting that IDC (Kim et al. 2022) demands an exceptionally long time to condense ImageNet subsets, e.g., approximately 4 days on ImageNet-10 with IPC=20 (Zhang et al. 2023). In contrast, M3D achieves superior performance in a matter of hours. Additionally, our method eliminates the need for network updates, thereby circumventing the tuning of various hyper-parameters. Consequently, our method can be readily applied to realistic and larger datasets, maintaining efficiency and effectiveness simultaneously.

To further demonstrate the advantages of our method, we provide the test accuracy across varying training steps in Fig. 3. As observed, our method consistently outperforms DM at different training steps. Even without the factor technique, our method still achieves considerable improvement, highlighting the effectiveness of M3D in aligning distributions compared to previous DM-based methods.

Cross-Architecture Evaluation. We further assess the performance of our condensed examples on different architectures. In Table 4, we present the performance of our condensed examples from CIFAR-10 dataset on ConNet-3, ResNet-10 (He et al. 2016), and DenseNet-121 (Huang et al. 2017). Combining the results from Table 2, we can find that M3D outperforms the compared methods not only on the architecture used for condensation but on unseen ones.

Image /page/6/Picture/0 description: A 3x3 grid of images, each depicting a different subject. The top row shows a baby in a bonnet, a green snake, and an astronaut. The middle row features a black dog, three mushrooms, and a white dog with spots. The bottom row displays a blurry image of bottles, a window with panes, and cocktail glasses with a shaker.

Figure 5: Representative samples condensed by M3D on ImageNet. The corresponding labels, from left to right and top to bottom, are bonnet, green snake, langur, doberman, gyromitra, saluki, vacuum, window screen, and cockroach.

| IPC | Evaluation<br>Model | Method                                                |                                                       |             |
|-----|---------------------|-------------------------------------------------------|-------------------------------------------------------|-------------|
|     |                     | DSA                                                   | DM                                                    | M3D         |
| 10  | ConvNet-3           | <span style="text-decoration: underline;">52.1</span> | 48.9                                                  | <b>63.5</b> |
|     | ResNet-10           | 32.9                                                  | <span style="text-decoration: underline;">42.3</span> | <b>56.7</b> |
|     | DenseNet-121        | 34.5                                                  | <span style="text-decoration: underline;">39.0</span> | <b>54.6</b> |
| 50  | ConvNet-3           | 60.6                                                  | <span style="text-decoration: underline;">63.0</span> | <b>69.9</b> |
|     | ResNet-10           | 49.7                                                  | <span style="text-decoration: underline;">58.6</span> | <b>66.6</b> |
|     | DenseNet-121        | 49.1                                                  | <span style="text-decoration: underline;">57.4</span> | <b>66.1</b> |

Table 4: Cross-architecture generalization performance (%) on CIFAR-10. The synthetic examples is condensed using ConvNet-3 and evaluated using other architectures.

Visualizations. We visualize the condensed images of SVHN and ImageNet in Fig. 4 and Fig. 5, respectively. For SVHN, we initialize the synthetic set  $S$  using random images from the training set  $T$  and then apply the condensation process using DM and M3D. As shown, the condensed images by DM and M3D appear as if the original images have been augmented with a distinct texture. Notably, the condensed images produced by our method exhibit a more pronounced and visually appealing texture compared to DM. While the overall appearance remains similar, our condensed images demonstrate better alignment with the higher-order moments of the original training set. In the case of ImageNet, the condensed images exhibit a texture reminiscent of a sunspot. In contrast to optimization-oriented methods, the images condensed by M3D retain more natural features and are more visually recognizable to humans. More visualization results are provided in the Appendix.

Image /page/6/Figure/5 description: The image contains two figures, labeled (a) and (b). Figure (a) is a bar chart with a line graph overlaid. The x-axis is labeled with numbers from 1 to 30, and the y-axis on the left ranges from 58 to 65. The bars represent 'Accuracy' and the line graph, marked with green triangles, represents 'Time'. The bars show an increasing trend in accuracy from 1 to 15, then a decrease. The time values fluctuate, peaking at 15. Figure (b) is a bar chart comparing performance across three datasets: F-MNIST, SVHN, and CIFAR-10. For each dataset, there are three bars representing 'Gaussian', 'Linear', and 'Polinominal' methods. The y-axis ranges from 0 to 80. The bars for F-MNIST and SVHN are all above 80, while the bars for CIFAR-10 are around 62.

Figure 6: (a) Ablation of IPM, where the horizontal axis represents the number of IPM, the left and right vertical axis denote test accuracy  $(\%)$  and the corresponding time cost (mins), respectively. (b) Ablation of the kernel function, where the vertical axis denotes test accuracy  $(\%)$ .

## Ablation Study

Impact of the Iteration per Model (IPM). We conduct experiments using various number of iterations per model, and the corresponding performance is depicted in Fig. 6a. We adopt CIFAR-10 with 10 images per class to showcase the impact of IPM. In addition to the test accuracy of condensed examples, we also provide the training time required to achieve the reported accuracy. As shown, increasing the number of IPM may lead to improved performance of the condensed data, but it also increases the training time. Conversely, an excessively large IPM can compromise the generalization ability of the condensed examples.

Impact of the Kernel Function. Different kernel functions construct distinct Reproducing Kernel Hilbert Spaces (RKHS). To investigate their influence, we adopt two additional kernel functions in addition to the Gaussian kernel: the linear kernel and the polynomial kernel. Fig. 6b illustrates the test accuracy under different kernel functions with 10 images per class. As observed, the choice of  $K$  has minimal impact on the performance of the condensed dataset. This indicates that as long as the selected kernel function is valid, our M3D can effectively embed the distributions in the constructed RKHS, resulting in a robust method.

## Conclusion

In conclusion, this paper introduces a novel Distribution-Matching (DM)-based method called M3D for dataset condensation. With a theoretical guarantee, our method embeds the representation distributions of real and synthetic examples in a reproducing kernel Hilbert space, minimizing the maximum mean discrepancy between them to align their distributions in both first- and higher-order moments. Extensive experiments show the effectiveness and efficiency of our method. Notably, the efficiency of our method enables its application to more realistic and larger datasets. M3D first studies the alignment of higher-order moments of the representation distributions between real and synthetic examples, and establishes a strong baseline in DM-based methods for dataset condensation, which we believe will be valuable to the research community.

# Acknowledgments

This work was partially supported by grants from the National Key Research and Development Plan (2020AAA0140001), and the Beijing Natural Science Foundation (19L2040), and the Open Research Project of National Key Laboratory of Science and Technology on Space-Born Intelligent Information Processing (TJ-02-22-01).

## References

Altun, Y.; and Smola, A. 2006. Unifying divergence minimization and statistical inference via convex duality. In *IC-CLT*, 139–153.

Borgwardt, K. M.; Gretton, A.; Rasch, M. J.; Kriegel, H.-P.; Schölkopf, B.; and Smola, A. J. 2006. Integrating structured biological data by kernel maximum mean discrepancy. *Bioinformatics*, 22(14): e49–e57.

Cazenavette, G.; Wang, T.; Torralba, A.; Efros, A. A.; and Zhu, J.-Y. 2022. Dataset Distillation by Matching Training Trajectories. In *CVPR*, 4750–4759.

Cazenavette, G.; Wang, T.; Torralba, A.; Efros, A. A.; and Zhu, J.-Y. 2023. Generalizing Dataset Distillation via Deep Generative Prior. In *CVPR*, 3739–3748.

Chen, Z.; Geng, J.; Zhu, D.; Woisetschlaeger, H.; Li, Q.; Schimmler, S.; Mayer, R.; and Chunming, R. 2023. A Comprehensive Study on Dataset Distillation: Performance, Privacy, Robustness and Fairness. *arXiv*.

Coleman, C.; Yeh, C.; Mussmann, S.; Mirzasoleiman, B.; Bailis, P.; Liang, P.; Leskovec, J.; and Zaharia, M. 2019. Selection via proxy: Efficient data selection for deep learning. *arXiv*.

Cui, J.; Wang, R.; Si, S.; and Hsieh, C.-J. 2022. DC-BENCH: Dataset condensation benchmark. *NeurIPS*, 35: 810–822.

Deng, J.; Dong, W.; Socher, R.; Li, L.-J.; Li, K.; and Fei-Fei, L. 2009. Imagenet: A large-scale hierarchical image database. In *CVPR*, 248–255.

Deng, Z.; and Russakovsky, O. 2022. Remember the Past: Distilling Datasets into Addressable Memories for Neural Networks. In *NeurIPS*.

Du, J.; Jiang, Y.; Tan, V. T. F.; Zhou, J. T.; and Li, H. 2023. Minimizing the Accumulated Trajectory Error to Improve Dataset Distillation. In *CVPR*.

Farahani, R. Z.; and Hekmatfar, M. 2009. *Facility location: concepts, models, algorithms and case studies*. Springer Science & Business Media.

Fukumizu, K.; Bach, F. R.; and Jordan, M. I. 2004. Dimensionality reduction for supervised learning with reproducing kernel Hilbert spaces. *JMLR*, 5(Jan): 73–99.

Goetz, J.; and Tewari, A. 2020. Federated Learning via Synthetic Data. *arXiv*.

He, K.; Zhang, X.; Ren, S.; and Sun, J. 2016. Deep residual learning for image recognition. In *CVPR*, 770–778.

Huang, G.; Liu, Z.; Van Der Maaten, L.; and Weinberger, K. Q. 2017. Densely connected convolutional networks. In *CVPR*, 4700–4708.

Jacot, A.; Gabriel, F.; and Hongler, C. 2018. Neural tangent kernel: Convergence and generalization in neural networks. *NeurIPS*, 31.

Jiang, Z.; Gu, J.; Liu, M.; and Pan, D. Z. 2022. Delving into Effective Gradient Matching for Dataset Condensation. *arXiv*.

Jin, W.; Tang, X.; Jiang, H.; Li, Z.; Zhang, D.; Tang, J.; and Yin, B. 2022. Condensing graphs via one-step gradient matching. In *KDD*, 720–730.

Kim, J.-H.; Kim, J.; Oh, S. J.; Yun, S.; Song, H.; Jeong, J.; Ha, J.-W.; and Song, H. O. 2022. Dataset Condensation via Efficient Synthetic-Data Parameterization. In *ICML*, 11102– 11118.

Knoblauch, J.; Husain, H.; and Diethe, T. 2020. Optimal continual learning has perfect memory and is np-hard. In *ICML*, 5327–5337.

Krizhevsky, A.; Hinton, G.; et al. 2009. Learning multiple layers of features from tiny images.

LeCun, Y.; Bottou, L.; Bengio, Y.; and Haffner, P. 1998. Gradient-based learning applied to document recognition. *Proc. IEEE*, 86(11): 2278–2324.

Lee, S.; Chun, S.; Jung, S.; Yun, S.; and Yoon, S. 2022. Dataset condensation with contrastive signals. In *ICML*, 12352–12364.

Lei, S.; and Tao, D. 2023. A comprehensive survey to dataset distillation. *arXiv*.

Li, S.; Liu, T.; Tan, J.; Zeng, D.; and Ge, S. 2023a. Trustable Co-label Learning from Multiple Noisy Annotators. *IEEE TMM*, 25: 1045–1057.

Li, S.; Xia, X.; Deng, J.; Ge, S.; and Liu, T. 2023b. Transferring Annotator-and Instance-dependent Transition Matrix for Learning from Crowds. *arXiv*.

Li, S.; Xia, X.; Ge, S.; and Liu, T. 2022a. Selective-Supervised Contrastive Learning with Noisy Labels. In *CVPR*, 316–325.

Li, S.; Xia, X.; Zhang, H.; Zhan, Y.; Ge, S.; and Liu, T. 2022b. Estimating Noise Transition Matrix with Label Correlations for Noisy Multi-Label Learning. In *NeurIPS*.

Liu, R.; Gao, J.; Zhang, J.; Meng, D.; and Lin, Z. 2021. Investigating bi-level optimization for learning and vision from a unified perspective: A survey and beyond. *IEEE TPAMI*, 44(12): 10045–10067.

Liu, S.; Wang, K.; Yang, X.; Ye, J.; and Wang, X. 2022. Dataset distillation via factorization. *NeurIPS*, 35: 1100– 1113.

Liu, S.; and Wang, X. 2023. Few-Shot Dataset Distillation via Translative Pre-Training. In *ICCV*, 18654–18664.

Liu, S.; Ye, J.; Yu, R.; and Wang, X. 2023a. Slimmable dataset condensation. In *CVPR*, 3759–3768.

Liu, Y.; Gu, J.; Wang, K.; Zhu, Z.; Jiang, W.; and You, Y. 2023b. DREAM: Efficient Dataset Distillation by Representative Matching. In *ICCV*.

Loo, N.; Hasani, R.; Amini, A.; and Rus, D. 2022. Efficient dataset distillation using random feature approximation. *NeurIPS*, 35: 13877–13891.

Loo, N.; Hasani, R.; Lechner, M.; and Rus, D. 2023. Dataset Distillation with Convexified Implicit Gradients. In *ICML*, 22649–22674.

Masarczyk, W.; and Tautkute, I. 2020. Reducing Catastrophic Forgetting with Learning on Synthetic Data. In *CVPR Workshop*.

Medvedev, D.; and D'yakonov, A. 2021. Learning to Generate Synthetic Training Data using Gradient Matching and Implicit Differentiation. In *AIST*, 138–150.

Muandet, K.; Fukumizu, K.; Sriperumbudur, B.; Schölkopf, B.; et al. 2017. Kernel mean embedding of distributions: A review and beyond. *Found. Trends Mach. Learn.*, 10(1-2): 1–141.

Netzer, Y.; Wang, T.; Coates, A.; Bissacco, A.; Wu, B.; and Ng, A. Y. 2011. Reading digits in natural images with unsupervised feature learning.

Nguyen, T.; Chen, Z.; and Lee, J. 2020. Dataset metalearning from kernel ridge-regression. *arXiv*.

Nguyen, T.; Novak, R.; Xiao, L.; and Lee, J. 2021. Dataset Distillation with Infinitely Wide Convolutional Networks. In *NeurIPS*, 5186–5198.

Pooladzandi, O.; Davini, D.; and Mirzasoleiman, B. 2022. Adaptive second order coresets for data-efficient machine learning. In *ICML*, 17848–17869.

Sagun, L.; Evci, U.; Guney, V. U.; Dauphin, Y.; and Bottou, L. 2017. Empirical analysis of the hessian of overparametrized neural networks. *arXiv*.

Sajedi, A.; Khaki, S.; Amjadian, E.; Liu, L. Z.; Lawryshyn, Y. A.; and Plataniotis, K. N. 2023. DataDAM: Efficient Dataset Distillation with Attention Matching. In *ICCV*, 17097–17107.

Sener, O.; and Savarese, S. 2017. Active learning for convolutional neural networks: A core-set approach. *arXiv*.

Smola, A.; Gretton, A.; Song, L.; and Schölkopf, B. 2007. A Hilbert space embedding for distributions. In *ALT*, 13–31.

Sorscher, B.; Geirhos, R.; Shekhar, S.; Ganguli, S.; and Morcos, A. 2022. Beyond neural scaling laws: beating power law scaling via data pruning. *NeurIPS*, 35: 19523–19536.

Such, F. P.; Rawal, A.; Lehman, J.; Stanley, K.; and Clune, J. 2020. Generative Teaching Networks: Accelerating Neural Architecture Search by Learning to Generate Synthetic Training Data. In *ICML*, 9206–9216.

Toneva, M.; Sordoni, A.; Combes, R. T. d.; Trischler, A.; Bengio, Y.; and Gordon, G. J. 2018. An empirical study of example forgetting during deep neural network learning. *arXiv*.

Wang, C.; Sun, J.; Dong, Z.; Li, R.; and Zhang, R. 2023. Gradient Matching for Categorical Data Distillation in CTR Prediction. In *CRS*, RecSys '23, 161–170. New York, NY, USA: Association for Computing Machinery. ISBN 9798400702419.

Wang, K.; Zhao, B.; Peng, X.; Zhu, Z.; Yang, S.; Wang, S.; Huang, G.; Bilen, H.; Wang, X.; and You, Y. 2022. CAFE: Learning to Condense Dataset by Aligning Features. In *CVPR*, 12196–12205.

Wang, T.; Zhu, J.-Y.; Torralba, A.; and Efros, A. A. 2018. Dataset Distillation. *arXiv*.

Welling, M. 2009. Herding dynamical weights to learn. In *ICML*, 1121–1128.

Wiewel, F.; and Yang, B. 2021. Condensed Composite Memory Continual Learning. In *IJCNN*, 1–8.

Xia, X.; Liu, J.; Yu, J.; Shen, X.; Han, B.; and Liu, T. 2022. Moderate coreset: A universal method of data selection for real-world data-efficient deep learning. In *ICLR*.

Xiao, H.; Rasul, K.; and Vollgraf, R. 2017. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv*.

Xiong, Y.; Wang, R.; Cheng, M.; Yu, F.; and Hsieh, C.-J. 2023. Feddm: Iterative distribution matching for communication-efficient federated learning. In *CVPR*, 16323–16332.

Yang, S.; Xie, Z.; Peng, H.; Xu, M.; Sun, M.; and Li, P. 2022. Dataset pruning: Reducing training data by examining generalization influence. *arXiv*.

Ying, C.; Klein, A.; Christiansen, E.; Real, E.; Murphy, K.; and Hutter, F. 2019. Nas-bench-101: Towards reproducible neural architecture search. In *ICML*, 7105–7114.

Yu, F.; Chen, H.; Wang, X.; Xian, W.; Chen, Y.; Liu, F.; Madhavan, V.; and Darrell, T. 2020. Bdd100k: A diverse driving dataset for heterogeneous multitask learning. In *CVPR*, 2636–2645.

Yu, R.; Liu, S.; and Wang, X. 2023. Dataset distillation: A comprehensive review. *arXiv*.

Zhang, H.; Li, S.; Zeng, D.; Yan, C.; and Ge, S. 2024. Coupled Confusion Correction: Learning from Crowds with Sparse Annotations. *AAAI*.

Zhang, L.; Zhang, J.; Lei, B.; Mukherjee, S.; Pan, X.; Zhao, B.; Ding, C.; Li, Y.; and Dongkuan, X. 2023. Accelerating Dataset Distillation via Model Augmentation. In *CVPR*.

Zhao, B.; and Bilen, H. 2021a. Dataset condensation with Differentiable Siamese Augmentation. In *ICML*, 12674– 12685.

Zhao, B.; and Bilen, H. 2021b. Dataset Condensation with Gradient Matching. In *ICLR*.

Zhao, B.; and Bilen, H. 2023. Dataset Condensation with Distribution Matching. In *WACV*.

Zhao, G.; Li, G.; Qin, Y.; and Yu, Y. 2023a. Improved distribution matching for dataset condensation. In *CVPR*, 7856– 7865.

Zhao, G.; Li, G.; Qin, Y.; and Yu, Y. 2023b. Improved distribution matching for dataset condensation. In *CVPR*, 7856– 7865.

Zheng, H.; Liu, R.; Lai, F.; and Prakash, A. 2022. Coveragecentric Coreset Selection for High Pruning Rates. *arXiv*.

Zhou, D.; Wang, K.; Gu, J.; Peng, X.; Lian, D.; Zhang, Y.; You, Y.; and Feng, J. 2023. Dataset quantization. In *ICCV*, 17205–17216.

Zhou, Y.; Nezhadarya, E.; and Ba, J. 2022. Dataset Distillation using Neural Feature Regression. In *NeurIPS*.

# Pseudo-Code of M3D

The pseudo-code of M3D is presented in Algorithm 1.

Algorithm 1: M3D: Dataset Condensation by Minimizing Maximum Mean Discrepancy

Input: Original training dataset  $\tau$ **Required:** Randomly initialized synthetic set S, the learning rate of synthetic images  $\eta$ , the number of total iterations  $I_{total}$ , the number of iteration(s) per model  $I_{\text{pm}}$ . Denote the number of classes as C. Output: Condensed dataset  $S^*$ 

1: for  $i = 0, ..., I_{\text{total}} - 1$  do 2: if  $i\%I_{\text{pm}} = 0$  then 3: Randomly initialize a network  $g_{\theta_i}$ 4: end if 5: for  $c = 0, ..., C - 1$  do 6: Sample a mini-batch of both real and synthetic set in class  $c: \mathcal{B}_{c}^{\mathcal{T}} \sim \mathcal{T}$  and  $\mathcal{B}_{c}^{\mathcal{S}} \sim \mathcal{S}$ 7: Obtain the representations of the real and synthetic images  $g_{\theta}(\mathcal{B}_c^{\mathcal{T}})$  and  $g_{\theta}(\mathcal{B}_c^{\mathcal{S}})$ 8: Compute the M3D loss  $\mathcal{L}_{M3D}$  between the sampled mini-batches according to Eq. (9) 9: Update S as:  $S = S - \eta \nabla_S \mathcal{L}_{\text{M3D}}$ 10: end for 11: end for 12: **return** S as  $S^*$ 

# Related Works

*Dataset Condensation* aims to obtain a synthetic dataset that are valid to replace original one in downstream training. According to different optimization objectives, current works in *Dataset Condensation* can be roughly divided into two categories: Optimization-oriented methods and Distribution-mathcing-based methods. Next, we will elaborate these two categories in detail:

## Optimization-Oriented Methods

Optimization-oriented methods adopt a bi-level optimization fashion to learn the synthetic dataset (Cazenavette et al. 2022; Zhao and Bilen 2021b; Kim et al. 2022; Zhao and Bilen 2021a; Zhang et al. 2023; Du et al. 2023; Wang et al. 2018). The pioneering works by Wang *et al.* (Wang et al. 2018) in *Dataset Condensation* poses a strong assumption that a model trained on the condensed dataset should be identical to that trained on the original dataset. However, directly aligning the converged models proves impractical due to the vast parameter space and convergence challenges. Consequently, subsequent studies adopt a more stringent assumption that the two models should follow a similar optimization path. To realize this objective, they leverage techniques such as gradient matching or aligning training trajectories of synthetic and real images. However, though the models can be aligned along the optimization path, these methods still can not bypass the time-consuming bi-level optimization process, which hinders their application in practical scenarios. Therefore, we refer to them as optimization-oriented methods, which can be further classified into two mainstream solutions: performance matching and parameter matching.

Performance Matching. Performance matching is initially introduced by Wang *et al.* (Wang et al. 2018), wherein the synthetic dataset is trained in a manner that ensures the model trained on it achieves the lowest loss on the original dataset. In this way, the performance of models trained on synthetic and real dataset could be matched. Subsequent work by Dong *et al.* (Deng and Russakovsky 2022) proposes that substituting the model updating with the inner loop with momentum can improve the performance of synthetic images. Further, to mitigate the inefficiency of the meta-gradient backpropagation, Nguyen *et al.* (Nguyen, Chen, and Lee 2020) replace the nerural network in the inner loop with a kernel model. With kernel ridge regression (KRR), the synthetic dataset can be updated by backpropagating meta-gradient through the kernel function (Lei and Tao 2023; Nguyen, Chen, and Lee 2020; Nguyen et al. 2021; Zhou, Nezhadarya, and Ba 2022; Loo et al. 2022, 2023), which greatly reduces the training cost. Following the KRR stream, Jacot *et al.* (Jacot, Gabriel, and Hongler 2018) proposed a theory which proves the equivalance between KRR and training infinite-width neural networks. Based on this, Nguyen *et al.* (Nguyen et al. 2021) adopts infinite-width neural networks as the kernel function to dataset condensation, which establishes a close relationship between KRR and deep learning. Further, a similar method was also proposed by Zhou *et al.* (Zhou, Nezhadarya, and Ba 2022), which also focuses on the last-layer in neural networks with KRR.

Parameter Matching. The methods of parameter matching in dataset condensation is first proposed by Zhao *et al.* (Zhao and Bilen 2021b), which has been extended to various following works (Zhao and Bilen 2021a; Cazenavette et al. 2022; Kim et al. 2022). The key idea of parameter matching is that: the parameter induced by real and synthetic datasets should be consistent with each other. In DC (Zhao and Bilen 2021b), the resultant gradients w.r.t. the model on the synthetic dataset are encouraged to be close to that on the real dataset. DSA (Zhao and Bilen 2021a) further proposes to add the differentiable siamese augmentation before the images are feed to the model, which improved the informativeness of the synthetic dataset. Recognizing that singlestep gradients may accumulate errors, MTT (Cazenavette et al. 2022) introduces a multi-step parameter matching method. This approach involves iteratively updating the synthetic images to ensure that the model trained on them follows similar trajectories towards convergence. On the other hand, there are some works focusing on effective model updating during the condensing process. The original work DC (Zhao and Bilen 2021b) uses the synthetic dataset to update the network, which could cause the overfitting problem in the eraly stage of training. To tackle this, IDC (Kim et al. 2022) proposes to update the model by real dataset, in which way the overfitting problem can be alleviated due to the large size of real dataset. Moreover, IDC (Kim et al. 2022) also proposed the factor technique, which can enrich the information of synthetic dataset by factoring and up-sampling. Though effective, these parameter matching methods are computationally expensive, as thousands of differently initialized networks are required to update the synthetic dataset. To accelerate the condensing process, (Zhang et al. 2023) propose model augmentation, which adds a Gaussian perturbation on the early-stage models to save the time and storage cost to condense dataset.

### Distribution-Matching-based Methods

The goal of distribution-matching-based methods is to obtain synthetic dataset which shares similar feature distribution with real dataset (Sajedi et al. 2023; Zhao and Bilen 2023; Zhao et al. 2023a; Wang et al. 2022). Different from optimizationoriented methods, distribution-matching-based methods do not need the bi-level optimization or the meta-gradient to condense the dataset, which greatly reduces the time and memory cost of the condensing process. DM (Zhao and Bilen 2023) condenses the dataset by aligning the representation embedding of real and synthetic images. Besides, it discard the model updating process, as the trained models have little impact on the performance of the synthetic images. Similarly, CAFE (Wang et al. 2022) aligns the embedding of not only the last layer but also the frontier layers to learn the synthetic dataset. Moreover, with the discriminant loss term, CAFE (Wang et al. 2022) can enhance the discriminative property of the synthetic dataset. To further improve distribution matching, IDM (Zhao et al. 2023a) proposes the "partitioning and expansion" technique to increase the number of representations extracted from the synthetic dataset. Besides, IDM (Zhao et al. 2023a) also uses the trained model to condense the dataset, with a cross-entropy regularization loss, IDM successfully alleviates DM (Zhao and Bilen 2023)'s class misalignment issue. In addition to aligning the representation distribution, DataDAM (Sajedi et al. 2023) adds the spatial attention matching to enhance the performance of the synthetic set. Although previous distribution-matching-based methods have shown promising results in efficiency, they all only align the first-order moment of the feature distributions of synthetic and real dataset. As a result, the higher-order moment of the distributions may be misaligned by previous methods. To this end, in this paper we propose to embed the image representations to a reproducing kernel Hilbert space (RKHS), where we can simultaneously align each moment of the real and synthetic image representations, making the representation distribution of synthetic dataset more consistent with the real one.

#### Coreset Selection

Instead of synthesizing data, coreset selection methods (Welling 2009; Farahani and Hekmatfar 2009; Toneva et al. 2018; Sener and Savarese 2017; Xia et al. 2022; Yang et al. 2022) select a subset of the whole training set based on a pre-defined criterion. For instance, Herding (Welling 2009) selects samples that are close to the class centers; K-center (Farahani and Hekmatfar 2009; Sener and Savarese 2017) selects multiple center points of a class to minimize the maximum distance between the selected samples and their nearest center. However, the performance of the coreset can not be guaranteed because the criterion is heuristic. Moreover, the coreset is restricted by the quality of the original images, which further hinders their application to reduce the data burden.

#### Derivation of MMD

Recall Eq. (7) in the main manuscript that:

$$
\text{MMD}(P_{\mathcal{T}}, P_{\mathcal{S}}) = ||\mu[P_{\mathcal{T}}] - \mu[P_{\mathcal{S}}]||.
$$

Squaring both sides, we have

$$
MMD^{2}(\mathbf{P}_{\mathcal{T}}, \mathbf{P}_{\mathcal{S}}) = \|\mu[\mathbf{P}_{\mathcal{T}}] - \mu[\mathbf{P}_{\mathcal{S}}]\|^{2} = \|\mu[\mathbf{P}_{\mathcal{T}}]\|^{2} + \|\mu[\mathbf{P}_{\mathcal{S}}]\|^{2} - 2\|\mu[\mathbf{P}_{\mathcal{T}}]\|\|\mu[\mathbf{P}_{\mathcal{T}}]\|
$$

$$
= E_{\mathcal{T}}^{2}[\mathcal{K}(g_{\theta}(\mathbf{x}), \cdot)] + E_{\mathcal{S}}^{2}[\mathcal{K}(g_{\theta}(\mathbf{s}), \cdot)] - E_{\mathcal{T}}[
e K(g_{\theta}(\mathbf{x}), \cdot)]E_{\mathcal{S}}[
e K(g_{\theta}(\mathbf{s}), \cdot)] \text{ / Substituting Eq. (5)}
$$

$$
= E_{\mathcal{T}, \mathcal{T}}[\langle \mathcal{K}(g_{\theta}(\mathbf{x}), \cdot), \mathcal{K}(g_{\theta}(\mathbf{x}), \cdot) \rangle] + E_{\mathcal{S}, \mathcal{S}}[\langle \mathcal{K}(g_{\theta}(\mathbf{s}), \cdot), \mathcal{K}(g_{\theta}(\mathbf{s}), \cdot) \rangle] - 2E_{\mathcal{T}, \mathcal{S}}[\langle \mathcal{K}(g_{\theta}(\mathbf{x}), \cdot), \mathcal{K}(g_{\theta}(\mathbf{s}), \cdot) \rangle]
$$

$$
= E_{\mathcal{T}, \mathcal{T}}[
e K(g_{\theta}(\mathbf{x}), g_{\theta}(\mathbf{x}))] + E_{\mathcal{S}, \mathcal{S}}[
e K(g_{\theta}(\mathbf{s}), g_{\theta}(\mathbf{s}))] - 2E_{\mathcal{T}, \mathcal{S}}[
e K(g_{\theta}(\mathbf{x}), g_{\theta}(\mathbf{s}))] \text{ / Reproducing property of } \mathcal{K}
$$

## Dataset Description

We evaluate our method on four low-resolution datasets and one high-resolution dataset.

## Low-Resolution Datasets

- Fashion-MNIST. Fashion-MNIST (Xiao, Rasul, and Vollgraf 2017) is a popular dataset commonly used for evaluating machine learning algorithms. It contains 60,000 training images and 10,000 testing images, all in grayscale with a size of 28x28 pixels. The dataset comprises 10 different fashion categories, including items like T-shirts, dresses, and shoes.
- SVHN. SVHN (Netzer et al. 2011) is a large-scale dataset primarily designed for digit recognition in natural images. It consists color images containing house numbers captured from Google Street View. The dataset is divided into two subsets: a training set with 73,257 images, a test set with 26,032 images. SVHN is commonly used for tasks like digit localization and recognition in real-world scenarios.
- CIFAR-10/100. CIFAR-10 and CIFAR-100 (Krizhevsky, Hinton et al. 2009) are widely used benchmark datasets for object recognition and classification tasks. CIFAR-10 consists of 60,000 color images, with 50,000 images for training and 10,000 images for testing. The dataset covers 10 different object classes, including common objects like cars, birds, and cats. On the other hand, CIFAR-100 contains 100 object classes, with 600 images per class. Each image in both datasets has a size of 32x32 pixels, making them suitable for evaluating algorithms in the field of image classification and object recognition.

## ImageNet-Subsets

ImageNet (Deng et al. 2009) is designed to cover a wide range of visual concepts and is commonly used for tasks such as object recognition, image classification, and object detection. It consists of millions of high-resolution labeled images spanning over thousands of object categories. Following (Zhang et al. 2023; Kim et al. 2022), we evaluate our method on two subsets extracted from the ImageNet dataset. These subsets are specifically composed of 10 and 100 subclasses of ImageNet, referred to as ImageNet-10 and ImageNet-100, respectively.

# Baseline Description

## Coreset-Selection

- Random: selecting partial samples randomly from the original set.
- Herding (Welling 2009): selecting data points that are close to the class centres.
- K-center (Farahani and Hekmatfar 2009; Sener and Savarese 2017): selecting the subset using K-center algorithm, which iteratively selects centers and including points that are closest to these centers.

### Dataset-Condensation

### Optimization-oriented.

- DC (Zhao and Bilen 2021b): matching the gradient induced by the real and synthetic images, and update the network on the condensed set.
- DSA (Zhao and Bilen 2021a): applying a differentiable Siamese augmentation to images before input it to the network.
- IDC (Kim et al. 2022): compared to DC, IDC use a factor technique that split one image into several lower-resolution ones. Besides, IDC update the network on the original real set instead of the condensed set.

### Distribution-Matching-based.

- CAFE (Wang et al. 2022): aligning the representation embedding of the real and synthetic images in a layer-wise manner. Moreover, CAFE utilizes a discriminant loss to enhance the discriminative properties of the condensed set.
- CAFE+DSA (Wang et al. 2022): additionally applying DSA stategy to images compared to CAFE.
- DM (Zhao and Bilen 2023): aligning the representation embedding of the real and synthetic images.
- IDM (Zhao et al. 2023a): applying model sampling, distribution regularization and expansion augmentation to DM (Zhao and Bilen 2023) to improve its performance.

# More Ablation Studies

**Compatibility with Previous DM-based Methods.** The proposed kernel-form loss  $\mathcal{L}_{M3D}$  can be readily combined with previous DM-based methods. We substitute the loss function of DM (Zhao and Bilen 2023) and IDM (Zhao et al. 2023a) with our  $\mathcal{L}_{M3D}$ , as shown in Tab. 5, the  $\mathcal{L}_{M3D}$  can lead to great improvements in the performance of the examples condensed by previous DM-based methods.

# More Visualization Results

Fig. 7∼10 visualize the synthetic images condensed by our method on low-resolution datasets with and without the factor technique. We also provide the visualization of initialized and synthetic ImageNet-10 with 10 images per class in Fig. 11.

|                  |                           | DM                                         | IDM                       |                                            |
|------------------|---------------------------|--------------------------------------------|---------------------------|--------------------------------------------|
|                  | w/o \$\mathcal{L}_{M3D}\$ | w/ \$\mathcal{L}_{M3D}\$                   | w/o \$\mathcal{L}_{M3D}\$ | w/ \$\mathcal{L}_{M3D}\$                   |
| IPC=10<br>IPC=50 | 48.9<br>63.0              | <b>54.7</b> (↑ 5.8)<br><b>66.0</b> (↑ 3.0) | 58.6<br>67.5              | <b>60.5</b> (↑ 1.9)<br><b>69.6</b> (↑ 2.1) |

Table 5: Compatibility of M3D with previous DM-based methods. All experiments are conducted on CIFAR-10 (Krizhevsky, Hinton et al. 2009).

Image /page/12/Picture/2 description: The image displays two grids of small, pixelated images, labeled (a) and (b). Grid (a) contains 80 images arranged in an 8x10 layout. Grid (b) contains 160 images arranged in a 10x16 layout. Both grids appear to be visualizations of generated images, possibly from a machine learning model, with a variety of subjects including animals, vehicles, and landscapes, though the low resolution makes precise identification difficult.

Figure 7: Visualization of the synthetic set of CIFAR-10 with 10 images per class. (a) without the factor technique (b) with the factor technique.

Image /page/13/Picture/0 description: The image displays two grids of small, blurry images, labeled (a) and (b). Grid (a) contains 100 images arranged in a 10x10 formation. Grid (b) contains 140 images arranged in a 10x14 formation. Many of the images appear to be nature scenes, with some showing trees, flowers, and skies, while others have abstract patterns or textures.

Figure 8: Visualization of the synthetic set of CIFAR-100 with 1 image per class. (a) without the factor technique (b) with the factor technique.

Image /page/13/Picture/2 description: The image displays two grids of numbers, labeled (a) and (b). Grid (a) is an 8x8 grid of individual numbers, with each row displaying a different digit from 0 to 9. Grid (b) is a larger grid, approximately 10x10, also filled with individual numbers. Both grids appear to be composed of small, square tiles, each containing a single digit. The digits are presented in various colors and styles, suggesting they might be examples from a dataset, possibly for machine learning or optical character recognition.

Figure 9: Visualization of the synthetic set of SVHN with 10 images per class. (a) without the factor technique (b) with the factor technique.

Image /page/14/Picture/0 description: The image displays two grids of fashion items, labeled (a) and (b). Grid (a) shows a 6x10 arrangement of clothing items and accessories, including t-shirts, trousers, sweaters, dresses, jackets, shoes, sandals, handbags, and boots. Grid (b) presents a larger 10x10 grid with a similar variety of fashion items, but with more items and potentially variations in quality or style compared to grid (a).

Figure 10: Visualization of the synthetic set of Fashion-MNIST with 10 images per class. (a) without the factor technique (b) with the factor technique.

Image /page/14/Picture/2 description: The image displays two grids of images, labeled (a) and (b). Each grid contains 64 smaller images arranged in an 8x8 matrix. The images in grid (a) appear to be clear and distinct, showcasing a variety of subjects including babies in bonnets, lizards, dogs, monkeys, mushrooms, people, vacuum cleaners, spiders, and cocktails. Grid (b) contains images that are similar in subject matter to grid (a), but they appear to be of lower quality, with some images showing artifacts or distortions, suggesting they might be generated or lower-resolution versions.

Figure 11: Visualization of the initialized and synthetic images of ImageNet-10 with 10 images per class. (a) initialized images (b) synthetic images.
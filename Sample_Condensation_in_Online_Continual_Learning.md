<PERSON><PERSON> *Computer Science Dept. University of Pisa* Pisa, Italy <EMAIL>

<PERSON> *Computer Science Dept. University of Pisa* Pisa, Italy <EMAIL>

Andrea Cossu *Scuola Normale Superiore* Pisa, Italy <EMAIL>

<PERSON><PERSON> *Computer Science Dept. University of Pisa* Pisa, Italy <EMAIL>

*Abstract*—Online Continual learning is a challenging learning scenario where the model must learn from a non-stationary stream of data where each sample is seen only once. The main challenge is to incrementally learn while avoiding catastrophic forgetting, namely the problem of forgetting previously acquired knowledge while learning from new data. A popular solution in these scenario is to use a small memory to retain old data and rehearse them over time. Unfortunately, due to the limited memory size, the quality of the memory will deteriorate over time. In this paper we propose OLCGM, a novel replay-based continual learning strategy that uses knowledge condensation techniques to continuously compress the memory and achieve a better use of its limited size. The sample condensation step compresses old samples, instead of removing them like other replay strategies. As a result, the experiments show that, whenever the memory budget is limited compared to the complexity of the data, OLCGM improves the final accuracy compared to state-of-the-art replay strategies.

*Index Terms*—continual learning; online learning; replay; knowledge condensation.

# I. INTRODUCTION

Deep learning models have achieved state-of-the-art results in fields such as computer vision [\[9,](#page-7-0) [20,](#page-7-1) [23\]](#page-7-2) and natural language processing [\[3,](#page-7-3) [6\]](#page-7-4). However, all these results assume the presence of a static training dataset, representative of the entire data distribution. In practice, most learning environments are non-stationary, since data arrives as a stream and the underlying data distribution may change over time [\[17\]](#page-7-5). In such environments, the ability to learn incrementally over time, i.e., Continual Learning (CL) [\[5,](#page-7-6) [11,](#page-7-7) [21,](#page-7-8) [27\]](#page-7-9), is necessary. Unfortunately, most models fail to retain the knowledge about past data when they are trained on new data, a problem known as catastrophic forgetting [\[7,](#page-7-10) [17\]](#page-7-5).

In the literature, Online Continual Learning (OCL) is considered one of the most challenging continual learning scenarios. In OCL, input data arrives in small mini-batches (usually 1 to 10 samples at a time) and the model is trained in a single pass, unaware of any changes (boundaries) in the underlying data distribution [\[8\]](#page-7-11). Most strategies fail in this extreme setting, except for replay-based strategies [\[2,](#page-7-12) [18,](#page-7-13) [19\]](#page-7-14), which keep a fixed-size memory (or buffer) of previous samples to use for rehearsal. During training, old and new samples are interleaved to learn incrementally while

Image /page/0/Figure/11 description: This research was supported by TEACHING, a project funded by the EU Horizon 2020 research and innovation programme under GA n. 871385

<span id="page-0-1"></span>Image /page/0/Figure/12 description: This figure illustrates the OLCGM framework. It shows a neural network receiving input data and undergoing model training. The input data is divided into a training set (Bin) containing images of digits 2, 3, 0, and 1, further split into a training buffer (Btr) with 2 and 3, and a memory buffer (Bmem) with 0 and 1. The framework includes an LCGM Condensation module where the training buffer (Btr) is combined with a conditional buffer (Bcond) containing digits 2 and 3 to produce a condensed set (S) also showing 2 and 3. The process involves a Replay Policy and a Removal Policy that interact with a Memory buffer, which stores digits 0 and 9. The Memory buffer is updated based on the output of the LCGC Condensation.

Fig. 1: High-level illustration of OLCGM. For each set of samples received in input OLCGM replay an additional set of images from the memory. Together with the incoming sample, the replayed images are used to update the parameters of the classification model. Then, to enhance the information in the memory, the received examples are condensed together with a set of examples from the memory and saved within it.

mitigating catastrophic forgetting. Replay-based strategies use very simple operations to manipulate their memory: a policy to select which samples to add and remove, and how to sample from the memory during training. Most strategies use simple policies such as random selection or greedy policies [\[22,](#page-7-15) [2,](#page-7-12) [18\]](#page-7-13). Unfortunately, naive usage of the bounded rehearsal memory is very inefficient, and replay-based strategies are forced to remove many elements whenever the memory is updated. Over time, the memory updates will remove useful information about the previous tasks. Since the replay buffer is a component of the model, we believe that the model should learn how to manipulate its buffer and design adaptive policies, optimized for the specific learning environment.

In this paper, we address the aforementioned desiderata by proposing a novel replay-based strategy for OCL, called On-line Linear Condensation with Gradient Matching (OLCGM)<sup>[1](#page-0-0)</sup>. A high level overview of the strategy is shown in Figure [1.](#page-0-1) OLCGM updates the replay buffer with a novel operation: the *sample condensation*. Instead of removing samples, at each memory update *a subset of the examples are condensed together in a novel synthetic sample*. This approach allows to mitigate the forgetting caused by the removal of old samples

<span id="page-0-0"></span><sup>1</sup>Code available at: https://github.com/MattiaSangermano/OLCGM

since the synthetic sample will compress information about multiple samples into a single one. The experimental results in OCL scenarios show that, when the memory size is small compared to the variability in the original data, naive removal policies suffer from catastrophic forgetting since they need to discard samples from the memory very frequently to make space for new ones.

# II. RELATED WORKS

The main objective of continual learning is to learn incrementally over time without catastrophic forgetting [\[7,](#page-7-10) [17\]](#page-7-5). Most continual learning strategies in the literature can be grouped into three different families. The first family includes regularization strategies [\[10,](#page-7-16) [12,](#page-7-17) [32\]](#page-7-18), namely strategies that use penalty terms to impose constraints on the weights update. In this way, the CL algorithm is guided towards solutions that generalize over all the tasks addressed, avoiding more specialized solutions that would result in catastrophic forgetting. Secondly, architectural-based strategies [\[1,](#page-6-0) [15,](#page-7-19) [24,](#page-7-20) [26\]](#page-7-21) change the model's architecture, either statically or dynamically, as new tasks arise. These may include the addition of new units or layers, as well as freezing selected parameters to prevent changes. Finally, replay-based strategies [\[2,](#page-7-12) [14,](#page-7-22) [18,](#page-7-13) [31\]](#page-7-23) are CL algorithms that store samples or information about the past tasks in an external memory. The memory is used during the training of new tasks in order to rehearse the old knowledge and therefore to avoid forgetting. While in general there are many tradeoffs that make some of these solutions more preferable to others, it is unquestionable that in most Online Continual Learning scenarios the only strategies that consistently achieve reliable results are replay-based strategies [\[2,](#page-7-12) [18\]](#page-7-13). For example, [\[18\]](#page-7-13) keeps the memory balanced between the classes such that every time data from a new class arrives it is also added into the memory and an equal number of patterns from the other classes is removed. The selection of examples to add and remove from the memory is done using a greedy policy. The model used for the classification is trained using only the examples in memory. Notice that while this strategy achieves a good performance, it is questionable whether it can be considered an online strategy since it requires retraining the model from scratch before the evaluation. Nonetheless, it provides a simple baseline to compare against. Instead, the objective of Maximally Interfered Retrieval (MIR) [\[2\]](#page-7-12) is to augment each incoming input batch with the examples that most interfere with the input samples available from the memory. Even though these methods are able to achieve promising results, their use of greedy policies to remove and add examples from the memory is suboptimal. In general, in an OCL scenario, we expect that over time the memory will lose information about the previous tasks. Indeed, the main difference between these strategies and our work is the way in which examples are removed from the memory. In fact, the strategy we propose aims to reduce the forgetting of the knowledge present in the examples that need to be removed when new ones are added. In our case, this is done through the use of a new condensation algorithm that, in addition to compressing the information inside the memory, adaptively selects and removes all examples that contain useless information. A similar work has been proposed in [\[30,](#page-7-24) [35\]](#page-7-25), but differently from them, in our case the condensation algorithm is optimised to work in an OCL scenario. The condensation algorithms in [\[30,](#page-7-24) [35\]](#page-7-25) do not take into account the OCL constraints, therefore they are not applicable to such setting.

# III. SAMPLE CONDENSATION

In an OCL scenario, a CL algorithm processes a continuous and non-stationary stream of experiences  $\mathcal{D}_{O}$ , where each *experience* is a small set of samples, typically at most 10 samples. At each iteration t a new set of examples  $B_{tr}^t =$  $\{(x, y)^i\}_{i=1}^L \sim p_t$  becomes available, where x and y are the input images and class labels respectively, while  $p_t$  is the distribution from which  $B_{tr}^t$  is drawn and L is the number of samples received at each iteration. The distribution  $p_t$ , may be equal to the previous distribution  $p_{t-1}$  or it may be a different one. Indeed, the algorithm is unaware of any changes in the data distribution. In addition,  $L$  may change over time. For these reasons, the algorithm must be able to continuously adapt itself. These properties make the OCL scenario much more difficult to solve than other CL scenarios since the task boundaries are not defined, unlike multi-task [\[11,](#page-7-7) [34\]](#page-7-26) or classincremental [\[28,](#page-7-27) [33,](#page-7-28) [16\]](#page-7-29) scenarios. Finally, the combination of a long stream and very small batches of data makes most of the algorithms designed for CL to fail in OCL, either because of their increased computational cost or because of catastrophic forgetting.

## *A. Replay in Online Continual Learning*

Previous works applied to the OCL scenario mainly focus on the design of replay-based strategies, namely algorithms that use an external memory to store and rehearse past data in order to increase the robustness of the model to the catastrophic forgetting problem. The external memory often stores raw examples, but other alternatives are possible as well [\[4,](#page-7-30) [8,](#page-7-11) [14,](#page-7-22) [19,](#page-7-14) [25\]](#page-7-31). The high-level pseudocode for a basic replay strategy in an OCL scenario is described by Algorithm [1](#page-1-0) below:

<span id="page-1-2"></span><span id="page-1-1"></span><span id="page-1-0"></span>

| <b>Algorithm 1</b> Replay strategy in OCL scenario                          |
|-----------------------------------------------------------------------------|
| <b>Require:</b> data stream $\mathcal{D}_{\Omega}$ , classifier f, memory M |
| 1: for $B_{tr}^t \in \mathcal{D}_O$ do                                      |
| 2: Sample $B_{mem} \sim M$                                                  |
| 3: $B_{in} \leftarrow B_{tr}^t \cup B_{mem}$                                |
| 4: Update $\theta \leftarrow \text{train}(f, B_{in})$                       |
| 5: Sample $B_{in} \sim B_{tr}^t$                                            |
| 6: $M.\text{update}(B_{in})$                                                |
| 7: end for                                                                  |

<span id="page-1-3"></span>Lines [2,](#page-1-1) [5](#page-1-2) and [6](#page-1-3) describe the interactions of the training algorithm with the external memory  $M$ : sampling from the memory, choosing which examples to add, and updating the buffer. The exact implementation of the replay in Algorithm [1](#page-1-0) depends on the definition of these three operations. M can be partitioned into separate buckets (e.g. by class label) and filled using both raw examples or different kind of information useful to the model during the train phase. Each of the lines [2,](#page-1-1) [5,](#page-1-2) [6](#page-1-3) is characterized by a different policy: line [2](#page-1-1) uses a sampling policy to decide which examples to retrieve from the memory in order to supplement the mini-batch received as input, line [5](#page-1-2) choose the examples to be inserted in memory through the use of an insertion policy and line [6](#page-1-3) through a removal policy chooses which examples to remove from memory in order to free space for the new ones. The current literature focuses on the design of optimal heuristics for the three aforementioned policies [\[22,](#page-7-15) [2\]](#page-7-12). However, all of this heuristics ignore a fundamental problem: *how can we avoid losing information each time we update the external memory?* Because of computational constraints, the external memory will have a limited size. As a result, whenever we update the memory with new examples, we also need to decide which samples to keep and which to remove. Over time, the online update of the external memory will lead to a gradual forgetting of critical information included in the examples removed from the memory. This limitation will always happen in an online scenario as soon as the complexity and variability of the past data in the stream will become larger than what the memory can store. Intuitively, if the algorithm runs for a long enough time, it will always have to remove informative examples at some point. In this section, we design an algorithm to mitigate this problem. The main idea behind the algorithm is the design of a *sample condensation algorithm that allows to compress multiple examples into a single one*, avoiding the need for removing data from the buffer. While previous attempts exist for class-incremental learning scenarios [\[30,](#page-7-24) [35\]](#page-7-25), all of them are unfeasible for OCL. In particular, the large number of parameters of the synthetic images, makes them too computationally expensive. In the following, we will introduce Dataset Condensation and will show how to perform the sample condensation for replay strategies in the OCL scenario.

<span id="page-2-4"></span>

## *B. Dataset Condensation*

The objective of the Dataset Condensation [\[29\]](#page-7-32) is to compress the knowledge of an entire dataset into few synthetic training images by optimizing the synthetic images for fast adaptation. After the condensation, the synthetic data can be used for training in place of the original data. Since the synthetic dataset is much smaller than the original dataset, we can reduce the size of the training data (or an external memory) with a small drop in accuracy. Following [\[35\]](#page-7-25), let  $B = \{(x_i, y_i)\}_{i=1}^{|B|}$  be a dataset, where  $x_i$  and  $y_i$  are the input data and the label of the i-th entry of the dataset. The goal of the condensation is to generate a set of synthetic samples with their corresponding labels,  $S = \{(s_i, y_i)\}_{i=1}^{|S|}$ where  $|S| \ll |B|$ , such that the synthetic samples can be used to train a neural network  $f$  to achieve the same performance as if the same network was trained on the original dataset B. This objective can be expressed as

<span id="page-2-0"></span>
$$
\mathbb{E}_{(x,y)\sim P_B}[\ell(f_{\theta^B}(x),y)] \simeq \mathbb{E}_{(x,y)\sim P_B}[\ell(f_{\theta^S}(x),y)], \quad (1)
$$

where  $P_B$  is the data distribution,  $\ell(\cdot, \cdot)$  is a task specific loss, f is the classification model,  $\theta^B$  and  $\theta^S$  are the network parameters obtained training the network on the datasets B and S respectively. The point at which condensation comes into play in Equation [1](#page-2-0) is the way in which the synthetic images S are optimised. The optimisation of the set S and therefore  $\theta^S$ would bring the right-hand side closer to the left-hand side of Equation [1.](#page-2-0) Depending on the objective to be pursued, different condensation algorithms may be produced. In [\[29\]](#page-7-32) the authors propose to learn the syntethic images  $S$  such that the model  $f_{\theta S}$  trained on the set S minimizes the training loss over the original dataset  $D$ , namely:

<span id="page-2-2"></span><span id="page-2-1"></span>
$$
S^* = \operatorname*{argmin}_{S} \mathcal{L}^B(\theta^S) \tag{2}
$$

subject to 
$$
\theta^S = \underset{\theta}{\text{argmin}} \mathcal{L}^S(\theta),
$$
 (3)

$$
\mathcal{L}^S(\theta) = \frac{1}{|S|} \sum_{(x,y)\in S} \ell(f_{\theta}(x), y)
$$
 (4)

The optimal synthetic images  $S^*$  are produced by alternating the inner and outer meta-optimisation steps illustrated by Equations [3](#page-2-1) and [2](#page-2-2) respectively. The inner step has the objective to find the optimal weights  $\theta^S$  induced by the synthetic images S. The weights  $\theta^S$  are then used during the outer loop to quantify the quality of the synthetic images produced so far. The synthetic set  $S$  during the outer-optimization step is optimised in such a way that the weights  $\theta^S$  obtained from S during the next iteration of the inner loop increase the performance on the original dataset  $D$ . Despite the goodness of this approach in producing high quality synthetic sets, the multiple optimization step of  $\theta^S$  in Equation [3](#page-2-1) leads to unroll the recursive computation graph in order to recover the gradients required for the tuning of  $S$  in Equation [2.](#page-2-2) The unrolling is computationally expensive and therefore does not scale to large models. In order to alleviate this limitation, [\[35\]](#page-7-25) proposes the Dataset Condensation with Gradient Matching algorithm (DCGM) that learns the synthetic images by minimizing the distance in parameter space of  $\theta^B$  and  $\theta^S$ . Therefore, in addition to achieving a performance similar to  $\theta^B$ , their approach also steers the optimization of  $\theta^S$  towards  $\theta^B$ . This objective can be achieved by solving the following optimization problem:

<span id="page-2-3"></span>
$$
\min_{\mathcal{S}} \mathbb{E}\Bigg[\sum_{t=1}^{T} \mathcal{D}\big(\nabla_{\theta^{S}} \mathcal{L}^{S}(\theta_{t}^{S}), \nabla_{\theta^{S}} \mathcal{L}^{B}(\theta_{t}^{S})\Bigg],
$$
  
$$
\mathcal{D}(A, B) = \sum_{i}^{L} \left(1 - \frac{A_{i} \cdot B_{i}}{\|A_{i}\| \cdot \|B_{i}\|}\right)
$$
(5)

Where  $A_i$  and  $B_i$  are the flattened vectors of the gradients corresponding to the  $i$ -th output nodes. The idea behind Equation [5](#page-2-3) is to force the learning trajectory in the parameter space of  $\theta^S$  to be as similar as possible to that of  $\theta^B$ . Equation [5](#page-2-3) has the advantage over Equation [2](#page-2-2) of not requiring the unrolling of the computational graph for the optimization of S during each iteration  $t$ . This means that DCGM is fast and memory efficient and therefore is able to scale also with large models. The reader is referred to [\[35\]](#page-7-25) for a detailed analysis of the steps taken to achieve Equation [5](#page-2-3) from Equation [2.](#page-2-2)

## *C. Linear Combination with Gradient Matching (LCGM)*

In this section, we introduce and describe the Linear Combination with Gradient Matching (LCGM), a novel technique which makes the external memory condensation more efficient and scalable to Online Continual Learning scenarios. While DCGM drastically reduces the cost of the dataset condensation compared to a naive Dataset Distillation [\[29\]](#page-7-32), it remains too expensive to be applied directly in an OCL scenario. The number of parameters to optimize during the condensation is the total number of pixels of the synthetic images, which results in a slow convergence rate for the condensation step. In order to make the condensation step tractable in OCL, we propose to create synthetic images that are defined as a linear combination of the input images  $S = WB$ , where  $W \in \mathbb{R}^{n \times m}$ are the coefficients of the linear combination. The coefficients  $W$  are learnt through the use of gradient matching (Section [III-B\)](#page-2-4). Therefore, the coefficients are derived by:

$$
\min_{\mathcal{W}} \mathbb{E}\left[\sum_{i=1}^{T} \mathcal{D}\big(\nabla_{\theta^S} \mathcal{L}^S(\theta_i^S), \nabla_{\theta^S} \mathcal{L}^B(\theta_i^S)\big)\right]
$$
\nsubject to\n
$$
\theta^S = \operatorname*{argmin}_{\theta} \mathcal{L}^S(\theta),
$$
\n(6)

$$
\mathcal{L}^{S}(\theta) = \frac{1}{|S|} \sum_{(x,y) \in S} \ell(f_{\theta}(x), y).
$$

Algorithm [2](#page-4-0) shows the pseudocode for the external memory condensation performed by LCGM. After randomly initialising the coefficients  $W$ , the synthetic image set S is generated by a linear combination of the input images  $B$ . The coefficients are filtered through a mask  $M$ , whose objective is to define from which input images each synthetic image must be composed of. Then, after taking a mini-batch of synthetic images and one of input images, the network  $f$  is used to compute the loss over both the input samples  $(L^B)$ , the synthetic samples  $(\mathcal{L}^S)$  and their gradients w.r.t.  $\theta$ . The gradients  $\nabla_{\theta_t} \mathcal{L}^S_{c}(\theta_t)$ ,  $\nabla_{\theta_t} \mathcal{L}_c^B(\theta_t)$  are used to compute the gradient matching loss which is employed to update the coefficients  $W$  at the end of each outer loop. After generating the new set of condensed images, the parameters  $\theta$  are updated by minimizing the loss  $\mathcal{L}^S$  with learning rate  $\eta_\theta$  for I steps.

*1) Masking:* The matrix M is a bit-wise mask used to constrain the relationship between input and synthetic images:  $S = (W \odot M) \cdot B$ . So if the *i*-th row of W defines the coefficients used to generate image  $i$ , then all the input images j such that  $M_{ij} = 0$  will not be taken into account for the generation of the synthetic image  $i$ . Masking allows to control how the condensation process transfers information from the input images into the synthetic ones and allows to craft custom policies. For example, without any masking all the synthetic images are used to optimize the input ones. While this may seem optimal, this approach may reduce the diversity of the synthetic images. Since all the generated images use the same input images they end up very similar to each other, which

<span id="page-3-0"></span>Image /page/3/Picture/7 description: The image displays two grids of handwritten digits, labeled (a) and (b). Grid (a) is captioned "Using matrix mask." and grid (b) is captioned "Without matrix mask.". Both grids contain five rows and five columns of digits. The top three rows of both grids predominantly feature the digit '0', with some variations in clarity and shape. The bottom two rows of both grids primarily show the digit '1', again with some variations in rendering. Grid (a) appears to have slightly clearer and more distinct digits compared to grid (b), which shows some blurriness and distortion, particularly in the last column of the third row and the last column of the fifth row.

Fig. 2: Results of condensed MNIST images with and without the use of the matrix mask

means the buffer will be full of redundant images. A result of this choice is shown in Figure [2,](#page-3-0) which clearly shows how the images without masking tend to be optimized into a single mode.

Instead, the use of  $M$  introduces the possibility of crafting custom policies that limit the choice of which images to condense together. For example, we may want to generate each synthetic image by selecting a subset of the input images that are very similar to each other. In the subsequent experiments, we use the mask matrix to ensure that each synthetic image is composed of two images: one real and one synthetic (generated from the previous iterations).

*2) Clipping and normalization:* During the preliminary experiments, it was noticed that when the coefficients get negative or high values the resulting images reduce the learning capacity of OLCGM making it to underfit. For this reason whenever the condensed image set  $S$  has to be generated, the coefficients  $W$  are adjusted via clipping and normalization operations. Clipping forces all coefficients to be greater or equal than 0:

<span id="page-3-1"></span>
$$
\mathcal{W}_{i,j} = \max(\mathcal{W}_{i,j}, 0). \tag{7}
$$

Given that the synthetic images are generated through a linear combination of the input images, constraint [7](#page-3-1) is needed since coefficients with a negative value would cause the associated images to have reverted colors and would results in the generation of meaningless images. The values of the linear combination coefficients of OLCGM and the pixel values resulting from a condensation algorithm that optimises images pixel by pixel have a different meaning. In the case of the linear combination, the coefficient can be interpreted as a representation of the amount of information contained in an image. For this reason, it is worthwhile to have a procedure able to filter out all images that according to the condensation algorithm contain useless or redundant information, i.e. those having a coefficient equal to 0. Therefore, clipping can be seen as a learnable mechanism to remove images from the buffer.

After clipping, the coefficients are normalised to prevent them from becoming too large. Without normalisation, the coefficients could increase dramatically during the optimization process, and this would lead to the generation of images that make the training of the classifier very unstable. We avoided to use regularization techniques because they would not guarantee the generation of images with pixels on the same

scale as the real ones. Images with different scales of values have a different impact on the model update. Since we want to avoid that some images are considered a priori more influential than others, we preferred to normalize the coefficients in such a way that the sum of the coefficients of each synthetic image is equal to 1 i.e:  $W_{i,j} = \frac{W_{i,j}}{\sum_i W_i}$  $\frac{\partial v_{i,j}}{\partial u_{i,l}}.$ 

*3) Number of parameters:* If we consider that [\[35\]](#page-7-25), in order to generate the synthetic set, updates the images pixel by pixel, then the number of parameters to train is equal to  $n \cdot l$ , where n is the number of synthetic images and  $l$  the number of pixels of each figure. In LCGM, on the other hand, the number of parameters to be trained using gradient matching are those present in the W matrix, namely  $n \cdot m$  coefficients where m is the number of images to condense. Therefore LCGM has  $\frac{l}{m}$ times fewer parameters than DCGM, i.e. the ratio between the size of an image and the number of images to be condensed. Considering that in the smallest computer vision datasets, such as MNIST, each image has  $28 \times 28$  pixels, and considering that in an Online Continual Learning scenario the number of input samples available at a certain timestep is small (usually no more than 10 samples), then the number of parameters of LCGM is at nearly about two orders of magnitude less than the ones in DCGM.

<span id="page-4-0"></span>

## Algorithm 2 LCGM memory condensation

Require: B: Set of images to be condensed, network parameters initialization  $\theta$ , the number of classes C, classifier  $f$ , number of outer loops  $T$ , number of inner loops I, learning rates for updating weights  $\eta_{\theta}$  and matrix coefficients  $\eta_{\mathcal{W}}$ , mask M 1:  $\theta_0 \leftarrow \theta$ 

2:  $S \leftarrow (W \odot M) \cdot B$ 3: for  $t = 0, ..., T - 1$  do 4: **for**  $c = 0, ..., C - 1$  **do** 5: Sample mini-batches  $\mathcal{B}_c^B \sim B$  and  $\mathcal{B}_c^S \sim S$ 6:  $\mathcal{L}_{c}^{B} \leftarrow \frac{1}{|\mathcal{B}_{c}^{B}|} \sum_{(x,y) \in \mathcal{B}_{c}^{B}} \ell(f_{\theta_{t}}(x), y)$ 7:  $\mathcal{L}_{c}^{S} \leftarrow \frac{1}{|\mathcal{B}_{c}^{S}|} \sum_{(x,y) \in \mathcal{B}_{c}^{S}} \ell(f_{\theta_{t}}(x), y)$ 8:  $\mathcal{L}_{distill}^c \leftarrow D(\nabla_{\theta_t} \mathcal{L}_{c}^S(\theta_t), \nabla_{\theta_t} \mathcal{L}_{c}^B(\theta_t))$ 9: end for 10:  $\mathcal{W} \leftarrow \mathcal{W} - \eta_S \cdot \nabla_{\mathcal{W}} \sum_i \mathcal{L}_{distill}^i$ 11:  $S \leftarrow (\mathcal{W} \odot M) \cdot B$ 12:  $\theta_{t+1} \leftarrow \text{opt-alg}_{\theta}(\mathcal{L}^S(\theta_t), I, \eta_{\theta})$ 13: end for 14: return S

## *D. LCGM in Online Continual Learning (OLCGM)*

LCGM was designed with the objective of applying it within the Online Continual Learning scenario. For this reason, its computational cost has been greatly reduced compared to the cost of DCGM. Since the proposed strategy falls under replay methods, an external memory  $M$  is kept to store what is learned during the life cycle of the algorithm. The memory, as in [\[8,](#page-7-11) [18,](#page-7-13) [19\]](#page-7-14), is partitioned according to the target labels of the examples inside the memory. Algorithm [3](#page-4-1) shows how LCGM condensation is integrated in an Online Continual Learning

<span id="page-4-1"></span>

## Algorithm 3 LCGM in Online Continual Learning scenario

**Require:** train stream  $\mathcal{D}_O$ , classifier f, memory  $\mathcal{M}$  =  $\{\mathcal{M}_c\}_{c=0}^C$ 

1: for  $B_{tr}^i \in \mathcal{D}_O$  do

- 2: Sample  $B_{mem} \leftarrow$  balanced-replay(M)
- 3:  $B_{in} \leftarrow B_{tr}, B_{mem}$
- 4: Update  $\theta \leftarrow \text{train}(f, B_{in})$
- 5: if  $i \mod K$  then
- 6: Update  $\mathcal{M} \leftarrow$  memory-condensation $(\mathcal{M}, B_{tr}, i)$
- 7: end if
- 8: end for

scenario. For each set of input samples  $B_{tr}$ , a mini-batch of data is taken from the memory  $B_{mem}$ . The samples from the external memory are chosen to balance the classes of the images taken from the memory.  $B_{tr}$  and  $B_{mem}$  are then used to update the weights of the model  $f$ . At this point, with a frequency  $K$ , the input mini-batch  $B_{tr}$  is condensed in the external memory  $M$  using the LCGM condensation technique described by Algorithm [2.](#page-4-0) In order to condense the  $B_{tr}$  images, it is necessary to randomly select the same number of images from the memory so that each new image can be paired with one coming from the memory having the same class. The coupling of the images is guaranteed by the mask matrix  $M$  which must fulfill the following additional constraints:

$$
\forall i, j \in \{0, ..., |M|\}, \quad M_{i,j} \in \{0, 1\} \quad \land
$$

$$
\forall i \in \{0, ..., |M|\}, \quad \sum_{j=0}^{|M|} M_{i,j} = 2 \quad \land
$$

$$
\forall j \in \{0, ..., |M|\}, \quad \sum_{i=0}^{|M|} M_{i,j} = 1.
$$
 $(8)$ 

The first constraint ensures that the matrix is made up of only bits, the second that each synthetic image is composed of a linear combination of only two images and the third ensures that the images to be condensed are used to generate only one synthetic image.

The condensation rate  $K$  balances the computational cost of the whole OCL training phase and the degradation of the images in memory. Indeed, condensation after condensation the algorithm might reach the maximum limit of information that can be stored in a single image. Once this limit has been reached, condensation would only lead to image degradation and therefore would increase the forgetting of the previous learned knowledge.

The memory during the whole training phase contains only condensed examples. This is useful because each condensed example contains more information than a real sample and therefore allows to optimise memory usage. Moreover, because of the way condensation works, the condensed examples will have a different distribution than the real images. Having a memory composed of only condensed examples ensures that the mini-batches  $B_{in}$  used for the training of the model f are balanced with respect to both the distribution of the data and that of the memory.

During the training phase, when an image with a new class is presented, the memory is resized in order to balance it among classes. The resize is performed by downsizing the space reserved for each class (using the LCGM condensation algorithm) and by adding an equal amount of space for the new class.

# IV. EXPERIMENTS

In this section, we evaluate OLCGM in OCL scenarios and compare it against different replay-based baselines for increasing memory sizes. The development of OLCGM and the experimental phase has been implemented using the library Avalanche [\[13\]](#page-7-33).

## *A. Benchmarks*

The effectiveness of OLCGM has been validated on the benchmarks SplitMNIST, SplitFashionMNIST and SplitCI-FAR10 adapted for the Online Learning scenario. In these benchmarks the training phase is divided in experiences where within each experience only one partition of the original dataset is used to train the continual learning algorithm. The partitioning of the initial dataset is done in such a way as to respect the constraints of a Class-Incremental scenario [\[28\]](#page-7-27). The number of experiences used during all the experimental phase is equal to 5 and the task ids are not provided. In order to simulate an Online Continual Learning scenario each experience is in turn divided in a stream of smaller experiences each of size 10. Therefore, at each iteration of the training phase a new small set of images is received as input. The performance of the compared strategies are evaluated using the average accuracy (ACC) and average forgetting (AF) metrics defined as ACC =  $\frac{1}{N} \sum_{i=1}^{N}$  $i=1$  $A_i^N$ , AF =  $\frac{1}{N-1}$  $\sum_{ }^{N-1}$  $i=1$  $A_i^i - A_i^{N-1},$ where  $A_i^j$  denotes the accuracy of the model on experience i computed right after being trained on experience j.

## *B. State-of-the-art strategies*

We compare OLCGM against three replay-based state-ofthe-art strategies from the literature. The first is the random replay (RR) [\[22\]](#page-7-15), the simplest replay-based strategy. This baseline chooses randomly both the examples to be added and removed from the memory and the samples replayed from the memory that are used during the training phase. The second algorithm is MIR, a strategy that builds the mini-batches used for the training by retrieving from the memory the samples which are most interfered with the ones received in input. The last one is the same strategy as OLCGM with the only difference that the condensation algorithm is the one presented in [\[35\]](#page-7-25) and described in Section [III-B.](#page-2-4) In the following, we will refer to this strategy as ODCGM.

## *C. Model Architectures*

During the experiments conducted on the SplitMNIST and SplitFashionMNIST benchmarks we used a Multilayer Perceptron with an hidden layer of 400 neurons and RELU as activation function, while in the SplitCIFAR10 benchmark

<span id="page-5-0"></span>

| M   | <b>Benchmark</b> | <b>OLCGM</b> |    |      | <b>ODCGM</b> |    |     |
|-----|------------------|--------------|----|------|--------------|----|-----|
|     |                  | Ol           | Il | lr   | Ol           | Il | lr  |
| 10  | <b>SMNIST</b>    | 100          | 1  | 0.1  | 50           | 1  | 0.1 |
|     | SFMNIST          | 200          | 1  | 0.01 | 200          | 5  | 0.1 |
|     | SCIFAR10         | 100          | 1  | 0.01 | 50           | 1  | 0.1 |
| 20  | <b>SMNIST</b>    | 100          | 1  | 0.1  | 50           | 1  | 0.1 |
|     | SFMNIST          | 200          | 1  | 0.01 | 50           | 5  | 0.1 |
|     | SCIFAR10         | 200          | 1  | 0.01 | 100          | 1  | 0.1 |
| 50  | <b>SMNIST</b>    | 100          | 1  | 0.1  | 50           | 5  | 0.1 |
|     | SFMNIST          | 200          | 1  | 0.01 | 200          | 1  | 0.1 |
|     | SCIFAR10         | 100          | 1  | 0.01 | 50           | 1  | 0.1 |
| 100 | <b>SMNIST</b>    | 50           | 1  | 0.1  | 200          | 1  | 0.1 |
|     | SFMNIST          | 200          | 1  | 0.01 | 50           | 1  | 0.1 |
|     | SCIFAR10         | 100          | 1  | 0.01 | 200          | 1  | 0.1 |
| 200 | <b>SMNIST</b>    | 50           | 1  | 0.1  | 100          | 5  | 0.1 |
|     | SFMNIST          | 200          | 1  | 0.01 | 200          | 5  | 0.1 |
|     | SCIFAR10         | 100          | 5  | 0.01 | 200          | 1  | 0.1 |

TABLE I: Chosen hyperparameters for the experimental phase of OLCGM and ODCGM. Note that the word Split is abbreviated with the letter S in the benchmark column. The columns Ol, Il denotes the number of iterations in the outer and inner loops, while  $lr$  denotes the learning rate used for the optimization of the synthetic images.

we used a smaller version of Resnet-18 as in [\[4,](#page-7-30) [14\]](#page-7-22). In all benchmarks, Resnet-18 and MLP classifiers are optimised by using SGD with a learning rate equal to 0.1.

## *D. Model Selection*

In order to choose the best hyper-parameters of each algorithm, a grid search has been made, using as validation set a subset of the training data of each dataset. In all three benchmarks, the validation set consists of 500 examples for each experience. In the case of MIR, the hyperparameters are the same as those chosen in [\[2\]](#page-7-12). For ODCGM and OLCGM the only hyperparameters fine-tuned for the model selection are those present in the condensation module: the learning rate used for the generation of the synthetic images, the number of outer and inner loops. Random Replay is the only strategy that has no hyperparameters apart from those included in the internal model. Tables [I](#page-5-0) shows the best hyperparameters found for OLCGM and ODCGM, |M| indicates the memory capacity. The condensation rate  $K$  is set to 10 for SplitMNIST, SplitFashionMNIST and 50 for SplitCIFAR10. In addition, during the experiments the coefficient matrix  $W$ used by OLCGM for the generation of the images is randomly initialized. Initialising randomly each pixel of the ODCGM synthetic images would be unfeasible for an OCL scenario as the number of iterations required to make the synthetic images representative enough of the condensed images would be prohibitive for this scenario. Therefore, each synthetic image of ODCGM is initialized by picking randomly one of the image to be condensed.

## *E. Results*

Tables [II, III](#page-6-1) and [IV](#page-6-2) show the average accuracy (ACC) and average forgetting (AF) of the four strategies. Firstly, it can be seen that despite its simplicity RR is highly competitive. In contrast to what was presented in [\[2\]](#page-7-12), in our experiments RR is able to achieve similar performance to MIR. OLCGM

<span id="page-6-1"></span>

| м            |                 | 10             | 20             | 50             | 100            | 200            |
|--------------|-----------------|----------------|----------------|----------------|----------------|----------------|
| <b>ORR</b>   | $ACC \uparrow$  | $28.6 \pm 2.1$ | $37.8 \pm 3.0$ | $56.1 \pm 3.4$ | $69.4 \pm 2.1$ | $80.0 \pm 1.9$ |
|              | $AF \downarrow$ | $88.4 \pm 2.6$ | $77.3 \pm 3.7$ | $54.6 \pm 4.1$ | $37.8 \pm 2.6$ | $24.5 \pm 2.5$ |
| <b>MIR</b>   | $ACC \uparrow$  | $29.5 + 1.0$   | $41.0 + 1.6$   | $59.9 + 2.1$   | $71.8 \pm 1.4$ | $81.7 + 0.8$   |
|              | $AF \perp$      | $87.1 \pm 1.2$ | $72.5 \pm 2.0$ | $48.9 \pm 2.7$ | $33.9 \pm 1.7$ | $21.3 \pm 1.0$ |
| <b>OLCGM</b> | $ACC \uparrow$  | $37.3 + 2.9$   | $48.2 + 3.0$   | $63.4 + 2.6$   | $71.8 + 2.1$   | $78.2 + 2.9$   |
|              | AF J            | $78.0 \pm 3.5$ | $64.4 \pm 3.7$ | $45.4 \pm 3.2$ | $34.7 \pm 2.6$ | $26.5 \pm 3.6$ |
| <b>ODCGM</b> | $ACC \uparrow$  | $31.8 + 2.5$   | $39.2 + 3.6$   | $56.8 \pm 3.4$ | $69.7 + 2.2$   | $78.7 + 2.7$   |
|              | AF J            | $84.6 \pm 3.1$ | $75.6 \pm 4.5$ | $53.7 + 4.2$   | $37.6 \pm 2.7$ | $26.1 + 3.5$   |

TABLE II: Average accuracy and forgetting on SplitMNIST benchmark, averaged over 15 runs (in bold are highlighted the values having the highest ACC metric with respect to the memory capacity).

<span id="page-6-2"></span>

| М            |                 | 10             | 20             | 50             | 100            | 200            |
|--------------|-----------------|----------------|----------------|----------------|----------------|----------------|
| <b>ORR</b>   | $ACC \uparrow$  | $18.9 \pm 0.4$ | $18.9 \pm 0.6$ | $20.1 \pm 0.9$ | $22.0 + 1.6$   | $26.3 \pm 2.5$ |
|              | $AF \perp$      | $86.5 \pm 1.0$ | $86.9 \pm 1.2$ | $85.0 \pm 1.2$ | $82.0 \pm 2.8$ | $75.9 \pm 3.2$ |
| <b>MIR</b>   | $ACC \uparrow$  | $18.3 \pm 0.4$ | $18.8 + 0.3$   | $20.3 + 1.0$   | $22.7 + 1.1$   | $28.6 + 1.1$   |
|              | $AF \perp$      | $74.9 \pm 3.1$ | $75.4 + 2.6$   | $67.2 + 3.1$   | $59.5 + 2.4$   | $49.1 + 1.7$   |
| <b>OLCGM</b> | $ACC \uparrow$  | $19.0 \pm 0.5$ | $18.9 + 0.7$   | $20.1 + 1.2$   | $22.4 + 1.7$   | $24.1 + 1.7$   |
|              | $AF \downarrow$ | $85.8 \pm 1.2$ | $86.2 \pm 1.3$ | $84.4 + 1.9$   | $81.3 + 3.1$   | $78.8 \pm 3.4$ |
| <b>ODCGM</b> | $ACC \uparrow$  | $18.8 + 0.5$   | $19.0 + 0.7$   | $20.0 + 1.2$   | $22.8 + 2.1$   | $25.8 + 1.6$   |
|              | AF J            | $86.3 \pm 1.1$ | $86.5 \pm 1.3$ | $84.4 + 2.3$   | $81.6 + 3.0$   | $76.8 \pm 2.6$ |
| M            |                 | 10             | 20             | 50             | 100            | 200            |
| <b>ORR</b>   | $ACC \uparrow$  | $34.2 \pm 1.7$ | $40.8 \pm 3.2$ | $54.3 + 2.2$   | $64.4 + 1.4$   | $72.1 + 1.0$   |
|              | $AF \perp$      | $80.9 \pm 2.0$ | $72.6 \pm 4.0$ | $55.5 \pm 2.8$ | $42.4 + 1.8$   | $32.2 \pm 1.6$ |
| <b>MIR</b>   | $ACC \uparrow$  | $37.7 + 2.1$   | $43.9 + 1.5$   | $57.9 + 1.6$   | $65.3 + 1.7$   | $70.8 + 1.2$   |
|              | $AF \perp$      | $75.9 \pm 2.5$ | $68.0 \pm 2.1$ | $46.9 \pm 2.6$ | $33.8 \pm 2.4$ | $28.4 \pm 1.6$ |
| <b>OLCGM</b> | $ACC \uparrow$  | $41.9 + 3.4$   | $52.1 + 2.8$   | $62.3 + 1.4$   | $67.8 + 1.0$   | $71.1 + 1.2$   |
|              | $AF \downarrow$ | $71.2 \pm 4.3$ | $58.2 \pm 3.5$ | $45.1 \pm 1.8$ | $37.7 \pm 1.4$ | $33.0 \pm 1.8$ |
| <b>ODCGM</b> | $ACC \uparrow$  | $35.6 + 1.6$   | $41.4 + 2.9$   | $54.7 + 2.3$   | $63.6 + 1.9$   | $71.5 + 1.4$   |
|              | AF J            | $79.2 + 2.0$   | $71.8 \pm 3.6$ | $54.9 \pm 2.9$ | $43.2 + 2.6$   | $32.9 \pm 1.9$ |

TABLE IV: Average accuracy and forgetting on SplitCIFAR10 benchmark, averaged over 15 runs (in bold are highlighted the values having the highest ACC metric with respect to the memory capacity).

shows improved performance with respect to MIR, especially when the available memory capacity is low compared to the complexity of the data. Indeed, in this cases OLCGM is capable to improve the average accuracy of MIR up to 26%. The advantage of OLCGM is graduallly reduced when the memory capacity increases, thus suggesting that when there is limited memory it is critical to be able to condense the largest amount of information in few images. When the memory is large, instead, it is more important to manage appropriately its content since it already stores enough information to avoid forgetting. This is most likely the reason why MIR performs better when the memory increases.

<span id="page-6-3"></span>Image /page/6/Figure/5 description: The image displays a comparison between two methods, OLCGM and ODCGM, for image generation. Each method is presented with two columns: 'Image initialization' and 'Condensed image'. Both methods show three rows of generated images, representing different categories: a handwritten digit '4' for OLCGM and '3' for ODCGM, a grayscale image of a dress, and a color image of a car. For both OLCGM and ODCGM, the 'Condensed image' appears to be a refined or processed version of the 'Image initialization'.

Fig. 3: OLCGM and ODCGM synthetic images after the initialization and after their optimization

The same pattern described above arises when comparing OLCGM with ODCGM. That is, with limited memory ca-

TABLE III: Average accuracy and forgetting on SplitFashionM-NIST benchmark, averaged over 15 runs (in bold are highlighted the values having the highest ACC metric with respect to the memory capacity).

pacity, OLCGM achieves higher performance, whereas when increasing the memory size this advantage disappears. At a first glance, this would suggest that ODCGM with high memory capacities is able to produce a better representation of knowledge in memory than OLCGM. In fact, when looking at the generated images in Figure [3,](#page-6-3) we can see that the condensation method of ODCGM collapses to images that are almost the same as when they were initialised. Therefore, the condensed images are very similar to the real ones. Having images in the buffer that are very similar to the real ones makes ODCGM behave like RR. In fact their performances are very similar. Overall, we can conclude that whenever the memory size is large enough, removing random samples like done by RR is sufficient. However, whenever the data stream is much more complex than what the limited memory can represent with raw samples, we see that the sample condensation of OLCGM outperforms RR, MIR, and ODCGM.

# V. CONCLUSIONS

In this paper, we studied the problem of updating the rehearsal memory of replay-based algorithms in Online Continual Learning scenarios. OCL is still an unsolved problem, and most strategies proposed in the literature are replay-based. The majority of these strategies rely on fixed and naive random policies to add or remove examples. However, while these simple policies may be successfull in simple toy streams, like those used in the literature, they use the memory buffer inefficiently. Instead, we proposed an alternative solution that compresses different samples together instead of removing them, thus allowing a better use of the rehearsal buffer via an adaptive compression. The results show that, whenever the memory buffer is small, i.e. in those settings where naive policies fail, compression-based strategies like the proposed one allow to improve the average accuracy of the model and to reduce the forgetting. We hope these results will encourage more research in adaptive methods to manage the rehearsal buffer. In the future, we plan to extend the proposed strategy to make it scale to more difficult benchmarks and further reduce its computational cost.

#### **REFERENCES**

<span id="page-6-0"></span>[1] R. Aljundi, P. Chakravarty, and T. Tuytelaars. "Expert Gate: Lifelong Learning with a Network of Experts". In: *2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)* (2017), pp. 7120–7129.

- <span id="page-7-12"></span>[2] R. Aljundi et al. "Online continual learning with maximally interfered retrieval". In: *Advances in Neural Information Processing Systems* volume 32 (2019).
- <span id="page-7-3"></span>[3] T. Brown et al. "Language models are few-shot learners". In: *Advances in neural information processing systems* 33 (2020), pp. 1877–1901.
- <span id="page-7-30"></span>[4] A. Chaudhry et al. "Efficient Lifelong Learning with A-GEM". In: *International Conference on Learning Representations* (2019).
- <span id="page-7-6"></span>[5] Z. Chen and B. Liu. "Lifelong machine learning". In: *Synthesis Lectures on Artificial Intelligence and Machine Learning* 12.3 (2018), pp. 1–207.
- <span id="page-7-4"></span>[6] J. Devlin et al. "Bert: Pre-training of deep bidirectional transformers for language understanding". In: *arXiv preprint arXiv:1810.04805* (2018).
- <span id="page-7-10"></span>[7] R. M. French. "Catastrophic forgetting in connectionist networks". In: *Trends in cognitive sciences* 3.4 (1999).
- <span id="page-7-11"></span>[8] T. L. Hayes, N. D. Cahill, and C. Kanan. "Memory efficient experience replay for streaming learning". In: *2019 International Conference on Robotics and Automation (ICRA)*. IEEE. 2019, pp. 9769–9776.
- <span id="page-7-0"></span>[9] K. He et al. "Deep residual learning for image recognition. arXiv 2015". In: *arXiv preprint arXiv:1512.03385* (2015).
- <span id="page-7-16"></span>[10] J. Kirkpatrick et al. "Overcoming catastrophic forgetting in neural networks". In: *Proceedings of the National Academy of Sciences* 114.13 (2017), pp. 3521–3526.
- <span id="page-7-7"></span>[11] T. Lesort et al. "Continual learning for robotics: Definition, framework, learning strategies, opportunities and challenges". In: *Information Fusion* 58 (2020), pp. 52– 68.
- <span id="page-7-17"></span>[12] Z. Li and D. Hoiem. "Learning without Forgetting". In: *IEEE Transactions on Pattern Analysis and Machine Intelligence* 40.12 (2018), pp. 2935–2947.
- <span id="page-7-33"></span>[13] V. Lomonaco et al. "Avalanche: an end-to-end library for continual learning". In: *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*. 2021, pp. 3600–3610.
- <span id="page-7-22"></span>[14] D. Lopez-Paz and M. Ranzato. "Gradient Episodic Memory for Continual Learning". In: *NIPS* (2017).
- <span id="page-7-19"></span>[15] A. Mallya and S. Lazebnik. "PackNet: Adding Multiple Tasks to a Single Network by Iterative Pruning". In: *2018 IEEE/CVF Conference on Computer Vision and Pattern Recognition* (2018), pp. 7765–7773.
- <span id="page-7-29"></span>[16] M. Masana et al. "Class-incremental learning: survey and performance evaluation on image classification". In: *arXiv preprint arXiv:2010.15277* (2020).
- <span id="page-7-5"></span>[17] M. McCloskey and N. Cohen. "Catastrophic Interference in Connectionist Networks: The Sequential Learning Problem". English (US). In: *Psychology of Learning and Motivation - Advances in Research and Theory* 24.C (Jan. 1989), pp. 109–165.
- <span id="page-7-13"></span>[18] A. Prabhu, P. H. S. Torr, and P. K. Dokania. "GDumb: A Simple Approach that Questions Our Progress in Continual Learning". In: *Computer Vision – ECCV 2020* (2020). Ed. by A. Vedaldi et al., pp. 524–540.

- <span id="page-7-14"></span>[19] S.-A. Rebuffi et al. "iCaRL: Incremental Classifier and Representation Learning". In: *2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)* (2017), pp. 5533–5542.
- <span id="page-7-1"></span>[20] J. Redmon et al. "You only look once: Unified, realtime object detection". In: *Proceedings of the IEEE conference on computer vision and pattern recognition*. 2016, pp. 779–788.
- <span id="page-7-8"></span>[21] M. B. Ring et al. "Continual learning in reinforcement environments". In: (1994).
- <span id="page-7-15"></span>[22] A. Robins. "Catastrophic forgetting, rehearsal and pseudorehearsal". In: *Connection Science* 7.2 (1995), pp. 123–146.
- <span id="page-7-2"></span>[23] O. Russakovsky et al. "Imagenet large scale visual recognition challenge". In: *International journal of computer vision* 115.3 (2015), pp. 211–252.
- <span id="page-7-20"></span>[24] A. A. Rusu et al. "Progressive Neural Networks". In: *CoRR* (2016).
- <span id="page-7-31"></span>[25] G. Saha and K. Roy. "Saliency Guided Experience Packing for Replay in Continual Learning". In: *arXiv preprint arXiv:2109.04954* (2021).
- <span id="page-7-21"></span>[26] J. Serra et al. "Overcoming Catastrophic Forgetting with Hard Attention to the Task". In: *Proceedings of the 35th International Conference on Machine Learning*. Proceedings of Machine Learning Research 80 (2018). Ed. by J. Dy and A. Krause, pp. 4548–4557.
- <span id="page-7-9"></span>[27] S. Thrun and T. M. Mitchell. "Lifelong robot learning". In: *Robotics and autonomous systems* 15.1-2 (1995), pp. 25–46.
- <span id="page-7-27"></span>[28] G. M. van de Ven and A. S. Tolias. "Three scenarios for continual learning". In: *arXiv preprint arXiv:1904.07734* (Apr. 2019).
- <span id="page-7-32"></span>[29] T. Wang et al. "Dataset Distillation". In: *arXiv preprint arXiv:1811.10959* (2018).
- <span id="page-7-24"></span>[30] F. Wiewel and B. Yang. "Condensed Composite Memory Continual Learning". In: *arXiv preprint arXiv:2102.09890* (Feb. 2021).
- <span id="page-7-23"></span>[31] Y. Wu et al. "Large Scale Incremental Learning". In: *2019 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)* (2019), pp. 374–382.
- <span id="page-7-18"></span>[32] F. Zenke, B. Poole, and S. Ganguli. "Continual Learning Through Synaptic Intelligence". In: *Proceedings of the 34th International Conference on Machine Learning*. Proceedings of Machine Learning Research 70 (2017). Ed. by D. Precup and Y. W. Teh, pp. 3987–3995.
- <span id="page-7-28"></span>[33] J. Zhang et al. "Class-incremental learning via deep model consolidation". In: *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*. 2020, pp. 1131–1140.
- <span id="page-7-26"></span>[34] Y. Zhang and Q. Yang. "A survey on multi-task learning". In: *IEEE Transactions on Knowledge and Data Engineering* (2021).
- <span id="page-7-25"></span>[35] B. Zhao, K. R. Mopuri, and H. Bilen. "Dataset Condensation with Gradient Matching". In: *International Conference on Learning Representations* (2021).
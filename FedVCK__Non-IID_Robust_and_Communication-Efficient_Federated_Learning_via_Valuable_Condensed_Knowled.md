# FedVCK: Non-IID Robust and Communication-Efficient Federated Learning via Valuable Condensed Knowledge for Medical Image Analysis

Guochen Yan $^{1,3,4}$ , Luyuan Xie $^{2,3,4}$ , <PERSON><PERSON><PERSON> Gao $^5$ , <PERSON><PERSON><PERSON> $^6$ , <PERSON><PERSON><sup>2,3,4\*</sup>, <PERSON><PERSON><PERSON><sup>2,3,4</sup>, <PERSON><PERSON><PERSON><sup>2,3,4†</sup>

<sup>1</sup>School of Computer Science, Peking University, Beijing, China

<sup>2</sup>School of Software and Microelectronics, Peking University, Beijing, China

<sup>3</sup>PKU-OCTA Laboratory for Blockchain and Privacy Computing, Peking University, Beijing, China

<sup>4</sup>National Engineering Research Center for Software Engineering, Peking University, Beijing, China

<sup>5</sup>The University of Queensland, Brisbane, Australia

<sup>6</sup>Center for Machine Learning Research, Peking University, Beijing, China

Guochen <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>,

<EMAIL>, <EMAIL>, <EMAIL>

## Abstract

Federated learning has become a promising solution for collaboration among medical institutions. However, data owned by each institution would be highly heterogeneous and the distribution is always non-independent and identical distribution (non-IID), resulting in client drift and unsatisfactory performance. Despite existing federated learning methods attempting to solve the non-IID problems, they still show marginal advantages but rely on frequent communication which would incur high costs and privacy concerns. In this paper, we propose a novel federated learning method: Federated learning via Valuable Condensed Knowledge (FedVCK). We enhance the quality of condensed knowledge and select the most necessary knowledge guided by models, to tackle the non-IID problem within limited communication budgets effectively. Specifically, on the client side, we condense the knowledge of each client into a small dataset and further enhance the condensation procedure with latent distribution constraints, facilitating the effective capture of highquality knowledge. During each round, we specifically target and condense knowledge that has not been assimilated by the current model, thereby preventing unnecessary repetition of homogeneous knowledge and minimizing the frequency of communications required. On the server side, we propose relational supervised contrastive learning to provide more supervision signals to aid the global model updating. Comprehensive experiments across various medical tasks show that FedVCK can outperform state-of-the-art methods, demonstrating that it's non-IID robust and communication-efficient.

## Introduction

Federated learning has become increasingly attractive since it allows collaborative training among sensitive institutions without direct data sharing. However, in reality, each medical institution would have its specialization, and the private

\*Corresponding author: <EMAIL>

†Corresponding author: <EMAIL>

| Method      | <b>Syn. Data Quality</b>        | <b>Knowledge Selection</b>    |
|-------------|---------------------------------|-------------------------------|
| FedGen      | calibrate classifiers           | heuristic                     |
| FedMix      | distorted sample                | repeated                      |
| FedGAN      | match single sample             | repeated                      |
| <b>DFRD</b> | infidelity                      | heuristic                     |
| FedDM       | only match final feature        | repeated                      |
| <b>DESA</b> | only match final feature        | repeated                      |
| FedVCK      | <b>latent dist. constraints</b> | <b>model-guided selection</b> |

Table 1: Representative data-centric methods' problems of 1) synthesis data quality and 2) knowledge selection in the synthesis. 'heuristic' indicates they adopt heuristic diversity loss with no relation to the need of models. 'repeated' indicates they do nothing and thus select data with repeated knowledge in synthesis. In contrast, we adopt latent distribution constraints and model-guided selection respectively.

data are highly related to the regional demographic characteristics. The data owned by each client are non-independent and identical distribution (non-IID), exhibiting significant data heterogeneity and imbalance. Under this scenario, federated learning methods suffer a global model with unsatisfactory performance due to the model divergence and client drift phenomenon (Li et al. 2019, 2022a). Meanwhile, frequent communication between heterogeneous and dispersed institutions would incur high communication costs, delays, and complex administrative procedures, with increasing privacy and safety risks (Zhu, Liu, and Han 2019; Mothukuri et al. 2021). A non-IID robust and communication-efficient federated learning method is desired.

Many federated learning methods are proposed to cope with non-IID problems by modifying local training process (Li et al. 2020; Li, He, and Song 2021; Zhou, Zhang, and Tsang 2023) or global aggregation process (Chen and Chao 2020; Lin et al. 2020; Zheng et al. 2023). However, they are mainly **model-centric**. They focus on mitigating model parameter-level divergence indirectly under a typical paradigm of local training and global aggregation, leading to marginal advantages in performance and communication

Copyright © 2025, Association for the Advancement of Artificial Intelligence (www.aaai.org). All rights reserved.

Image /page/1/Figure/0 description: The figure displays a line graph comparing the performance of two methods, 'Vanilla' and 'Our method', over several rounds. The x-axis represents the 'Round' number, ranging from 0 to 16. The left y-axis, labeled 'Lcond', measures a value from 0 to 50, and the right y-axis, labeled 'Accuracy (%)', measures accuracy from 10 to 70. Two red lines represent 'Lcond': 'Lcond: Vanilla' starts at approximately 55 and decreases sharply to around 2, then fluctuates slightly between 2 and 4. 'Lcond: Our method' starts at approximately 10 and decreases to around 3, then remains relatively flat between 3 and 4. Two blue lines represent 'Acc': 'Acc: Vanilla' starts at approximately 5% and increases to around 30% with fluctuations. 'Acc: Our method' starts at approximately 10% and increases steadily to around 65%, then plateaus around 65-67%.

(a) We use the distribution matching-based method to condense knowledge on the OrganC dataset. Without our latent distribution constraints (Vanilla, dashed line),  $L_{cond}$  would be easily reduced in each round but the model's performance struggles to improve with the condensed knowledge, demonstrating the low-quality problem of vanilla methods.

Image /page/1/Figure/2 description: A bar chart displays the empirical MMD (Maximum Mean Discrepancy) for different classes, labeled from Class 0 to Class 8. The y-axis represents the Empirical MMD, ranging from 0.05 to 0.10. Two sets of bars are presented for each class: blue bars labeled 'Without Pw' and orange bars labeled 'With Pw'. For Class 0, 'Without Pw' is approximately 0.072 and 'With Pw' is approximately 0.092. For Class 1, 'Without Pw' is approximately 0.077 and 'With Pw' is approximately 0.090. For Class 2, 'Without Pw' is approximately 0.069 and 'With Pw' is approximately 0.081. For Class 3, 'Without Pw' is approximately 0.068 and 'With Pw' is approximately 0.079. For Class 4, 'Without Pw' is approximately 0.083 and 'With Pw' is approximately 0.095. For Class 5, 'Without Pw' is approximately 0.054 and 'With Pw' is approximately 0.066. For Class 6, 'Without Pw' is approximately 0.083 and 'With Pw' is approximately 0.093. For Class 7, 'Without Pw' is approximately 0.076 and 'With Pw' is approximately 0.090. For Class 8, 'Without Pw' is approximately 0.052 and 'With Pw' is approximately 0.064.

(b) We measure the average MMD of condensed knowledge classwisely between adjacent rounds on the Path dataset. Greater MMD indicates a larger distribution difference. The vanilla selection causes more knowledge repetition between rounds. Our model-guided selection  $(P_w)$  ensures that the condensed knowledge between adjacent rounds exhibits greater differences.

Figure 1: Illustration of the low synthesized data quality problem in Figure (a) and repeated knowledge problem in Figure (b).

costs under severe non-IID scenarios (Li et al. 2022a).

The model divergence originates from the data divergence (Zhao et al. 2018), thus mitigating the data-level divergence would be more essential to tackle the non-IID problems. Recently, various data-centric federated learning methods attempted to share virtual or synthesized data to mitigate data divergence. They synthesize diverse objectives including latent features (Zhu, Hong, and Zhou 2021), approximated real data (Li et al. 2022b; Zhu and Luo 2022; Yoon et al. 2021), inverted data (Zhang et al. 2022; Wang et al. 2024a), condensed data (Xiong et al. 2023; Huang et al. 2024; Wang et al. 2024b) and so on. However, under severe non-IID scenarios, these methods still face problems because of: 1) low synthesized data quality. For instance, mix-up would distort data (Verma et al. 2019). The inverted data is of infidelity with biased models. And the advanced dataset condensation cannot effectively extract subtle and meaningful knowledge which we demonstrate in Figure 1a. These low-quality data would fail to guide the model training; 2) repeated knowledge. The data are randomly selected to synthesize virtual data, and their value and importance to the current model are not considered. Thus, the knowledge contained tends to be homogeneous and unnecessarily repeated (see Figure 1b), thus cannot effectively update the model after several rounds. We summarize problems in the above two aspects of representative data-centric federated learning methods in Table 1. Additionally, some synthesis methods would incur privacy concerns, and most methods are not communication-efficient. They still face challenges to achieve a satisfactory performance under limited communication rounds in non-IID scenarios.

Motivated by the above limitations, we propose a novel data-centric Federated learning method via Valuable Condensed Knowledge (FedVCK). Our method includes two parts, valuable knowledge condensation on the client side and relational supervised learning-aided updating on the server side. Specifically, we condense each client's knowledge into a small dataset. To ensure condensing high-quality knowledge, we propose latent distribution constraints to better capture subtle and meaningful knowledge in latent spaces. To minimize redundancy in each round of knowledge condensation, we explicitly measure the missing knowledge of the current model and select the most necessary knowledge in condensation on each client. On the server side, we identify the hard negative classes for each class and propose a relational supervised contrastive learning to enhance the supervision signals during model updating. Due to the balanced, high-quality, unrepeated, and necessary condensed knowledge, the training of the global model is insulated from the effects of non-IID problems and can achieve enhanced performance within limited communication rounds (e.g. 10). Moreover, our method only condenses task-related high-level knowledge with random noise initialization, thereby facilitating privacy protection. Our main contributions are summarized as follows:

- We propose a novel data-centric federated learning method: FedVCK, for collaborative medical image analysis. FedVCK is robust to severe non-IID scenarios and communication efficient with valuable knowledge.
- On the client side, we propose model-guided selection to sample the most needed knowledge each round to avoid unnecessary repetition. We also propose latent distribution constraints to enhance the quality of knowledge.
- On the server side, we identify the hard negative classes and propose relational supervised contrastive learning to enhance supervised learning in model updating.
- We conduct comprehensive experiments and results show that our method achieves better predictive performance, especially under limited communication budgets. We also conduct experiments to verify the privacypreserving ability and generality of our method.

# Related Works

The data owned by each client is typically highly heterogeneous and does not follow an independent and identical distribution. Under severe non-IID scenarios, models trained on clients tend to be highly biased and divergent, a phenomenon known as client drift. Aggregating these biased and divergent client models at the server often results in suboptimal performance. Many model-centric methods focus on modifying local training process, such as introducing regularization or contrastive terms to reduce divergence (Li et al. 2020; Acar et al. 2021; Li, He, and Song 2021; Xie et al. 2024c,b,a) or improving aggregation process (Lin et al. 2020; Chen and Chao 2020; Zheng et al. 2023). They try to alleviate the client drift from the model parameter level.

The model divergence originates from the data divergence (Zhao et al. 2018). Directly reducing the difference in data distribution would reduce the model divergence fundamentally. Recently, data-centric federated learning methods have drawn attention since they can synthesize and then share virtual synthesized data to mitigate the non-IID problem in a data-centric manner. Besides that FedGen (Zhu, Hong, and Zhou 2021) which generates virtual representation, various format data are synthesized on the server or clients. FedMix (Yoon et al. 2021) broadcasts the mixup data to approximate the real data. FedGAN (Nguyen et al. 2021) and SDA-FL (Li et al. 2022b) train and share GANs to imitate real data to support COVID-19 detection. FedFTG (Zhang et al. 2022) and advanced version DFRD (Wang et al. 2024a) use model inversion (Yin et al. 2020) to generate data for knowledge distillation. FedDM (Xiong et al. 2023) condenses the knowledge to update the global model. DESA (Huang et al. 2024) distills anchor data and broadcasts them to enable mutual regularization and distillation among clients.

## Proposed Method

### Overview

The overview of FedVCK is shown in Figure 2. In short, it consists of two parts: valuable knowledge condensation on the client side and relational supervised learning-aided updating on the server side. On the client side, we borrow distribution matching techniques in dataset condensation and optimize the learnable dataset to condense knowledge from local data. To ensure quality, we record dynamic distribution statistics of the local data batch in each encoder layer and replace the statistics during embedding learnable knowledge as fixed constraints, which could force the latent distribution of the condensed knowledge to capture subtle and meaningful knowledge of different levels. To minimize redundancy in each round of condensation, we explicitly measure the prediction error on each sample and select the data on which the model performs poorly. We consider such data critical as it contains knowledge not yet captured by the current model. By focusing more on these important samples, the condensation process ensures that the condensed knowledge complements the global model's missing capabilities. On the server side, we collect the condensed knowledge dataset and train the global model with supervised learning and relational

contrastive learning. We first identify hard negative classes for each class where the global model tends to mispredict by uploaded logit prototypes. Then we use supervised contrastive learning in a bootstrap manner to draw the features of the same class closer to their prototypes and push the features away from their hard negative classes' prototypes. We will introduce our designs in detail in the following sections.

### Preliminary: Dataset Condensation

The objective of dataset condensation (Wang et al. 2018; Yu, Liu, and Wang 2023; Gao et al. 2024a,b) is to condense knowledge from a large dataset into a small learnable dataset, which could be used to train models to achieve comparable performance. Distribution matching is an advanced method widely used in dataset condensation.

Distribution matching. The intuition behind is to optimize a small dataset  $S$  to match the latent feature distribution of local data  $\mathcal T$  by minimizing the distance to the latent features of local data with maximum mean discrepancy (MMD) (Gretton et al. 2012; Zhao and Bilen 2023):

$$
\underset{\mathcal{S}}{\arg\min} \underset{\|\psi_{\theta}\|_{\mathcal{H}} \leq 1}{\sup} (\mathbb{E}[\psi_{\theta}(\mathcal{T})] - \mathbb{E}[\psi_{\theta}(\mathcal{S})]), \qquad (1)
$$

where H is reproducing kernel Hilbert space (RKHS),  $\psi_{\theta}$  is the shared embedding function to map the input to its latent feature, parameterized by a multi-layer encoder. In practice, We minimize the estimated empirical MMD loss by classwisely align the latent feature distributions to optimize  $S$ :

$$
L_{cond} = \sum_{c=0}^{C-1} \| \frac{1}{|B_c|} \sum_{x_i \in B_c} \psi_{\theta}(x_i) - \frac{1}{|\mathcal{S}_c|} \sum_{s_i \in \mathcal{S}_c} \psi_{\theta}(s_i) \|^2,
$$
\n(2)

where C is the number of classes,  $\mathcal{T}_c$  is the local data with class c,  $B_c$  is a batch randomly sampled from  $\mathcal{T}_c$  with a uniform distribution, and  $S_c$  is the knowledge dataset corresponding to class  $c$ . To enable the high-order estimation, we choose to align the latent feature distributions in an RKHS with kernel  $K$  (Zhang et al. 2024), and minimize the following empirical MMD as condensation loss:

$$
L_{cond} = \sum_{c=0}^{C-1} \sum_{B_c \stackrel{P_u}{\sim} \mathcal{T}_c} \hat{\mathcal{K}}_{B_c, B_c} + \hat{\mathcal{K}}_{\mathcal{S}_c, \mathcal{S}_c} - 2\hat{\mathcal{K}}_{B_c, \mathcal{S}_c}, \quad (3)
$$

where  $\hat{K}_{X,Y} = \frac{1}{|X|\cdot|Y|} \sum_{i=1}^{|X|} \sum_{j=1}^{|Y|} \mathcal{K}(\psi_{\theta}(x_i), \psi_{\theta}(y_j)),$  ${x_i}_{i=1}^{|X|} \sim X$ ,  ${y_j}_{j=1}^{|Y|} \sim Y$ . The kernel function K can be a linear kernel, inner-product kernel, or Gaussian kernel.

Knowledge initialization. There are several manners to initialize learnable knowledge dataset  $S$  whose format is the same as real data. To best prevent the privacy leak of the local data, we choose random Gaussian noise  $\mathcal{N}(0, 1)$  to initialize the knowledge dataset, making the knowledge can only be condensed by matching the latent distributions. The condensed knowledge dataset  $S$  would contain no individual and privacy information in pixel space and the adversary can hardly infer the membership from the condensed knowledge datasets (Dong, Zhao, and Lyu 2022).

Image /page/3/Figure/0 description: The figure illustrates the overview of a federated learning process with model-guided knowledge selection. On the left, labeled 'Client i', there are two branches representing condensed knowledge and real data. The condensed knowledge branch shows multiple layers of latent distributions, each represented by {μ, σ}, with some marked with snowflakes indicating frozen states. This condensed knowledge is used to generate latent features. The real data branch shows data batches, with a sampling mechanism indicated by Pw ~ wMt. Both branches contribute to 'Latent Distribution Constraints' and generate 'Latent feat.' which leads to a loss function Lr\_cond. The client also has 'Local Data' stored in a database. On the right, labeled 'Server', the client uploads 'Condensed knowledge logit prototypes'. The server collects this knowledge into S^t and uses it along with the global model M\_t to update the global model through a loss function Lce + Lrc. The server also downloads the global model M\_t to the client. A legend at the bottom defines M\_t, S\_t, S\_c, and B\_c. The overall process involves clients processing local data and condensed knowledge, and the server aggregating this information to update a global model.

Figure 2: Overview of FedVCK. On the client side, we sample local data by importance sampling guided by the current model and then impose latent distribution constraints in optimization. We upload the condensed knowledge dataset and logit prototypes to the server. On the server side, we use cross entropy loss and relational contrastive loss to update the global model.

### Latent Distribution Constraints

By optimizing S with  $L_{cond}$  in Eq. 2 or Eq. 3, the condensed knowledge dataset  $S$  can replace the real data to effectively train models. However, it's challenging to ensure the condensation quality when condensing the knowledge from local data into a small random noise-initialized dataset, because random noises have no prior about the local data. Moreover, the condensation loss  $(L_{cond})$  is not sufficient to guide the learning of subtle meaningful knowledge since the latent features of  $S$  can take shortcuts to over-fit the latent features of local data. To assess the deficiency of the vanilla optimization process, we show in Figure 1a that while  $L_{cond}$  is easily reduced at each round, it can not effectively condense meaningful knowledge to improve performance effectively. Additionally, matching the distribution of the final representation of the encoder neglects the previous intermediate latent feature distributions.

To enhance the effectiveness and ensure consistent distribution of all latent features, we transfer dynamic distribution statistics (mean and variation) from the local real data to the condensed knowledge data during the condensation procedure. Compared to (Yin et al. 2020), our approach does not require the addition of an extra loss term, enabling a more flexible and efficient condensation procedure. Specifically, we first record the distribution statistics of each layer  $\{\{\mu_1, \sigma_1\}, ..., \{\mu_L, \sigma_L\}\}\$  with a L layer encoder when embedding a batch of local data. Then the statistics during embedding condensed data are replaced and fixed with recorded statistics:

 $(1)$ 

$$
s_i^{(1)} = Norm(\psi_1(s_i), \{\mu_1, \sigma_1\}),
$$
  

$$
s_i^{(2)} = Norm(\psi_2(s_i^{(1)}), \{\mu_2, \sigma_2\}),
$$
  
...  

$$
\psi_{\theta}(s_i) = s_i^{(L)} = Norm(\psi_L(s_i^{(L-1)}), \{\mu_L, \sigma_L\})
$$
 $(4)$ 

where  $\psi_l$  is *l*-th layer of encoder  $\psi_\theta$  and  $Norm$  denotes

batch normalization (Ioffe and Szegedy 2015). The distribution statistics constraint could force the optimization process to consider intermediate latent distributions and prevent it from taking shortcuts. Thus the quality of condensed knowledge can be largely improved.

### Model-guided Knowledge Selection

If we uniformly sample real data from local data to conduct condensation, the condensed knowledge in each round will be repeated and homogeneous, dominated by simple and easy-to-learn knowledge. It would be less beneficial to further improve the performance of the global model. However, from the model perspective, we find that the importance of the knowledge contained in each sample varies. The current global model may perform well on some local data but lacks the ability to make correct predictions on others, exposing that the current model lacks some knowledge. The data containing the missing knowledge would be more important at this round and it's better to focus on condensing knowledge from these data to complement the model knowledge. Specifically, we first measure the importance of each data sample explicitly by model prediction error as  $t$ -th round:

$$
w_{\mathbf{M}_t}(x_i) = \frac{1}{1 + e^{-err_t(x_i) + b}},
$$
\n(5)

where the  $err_t(x_i)$  refers to the prediction error on  $x_i$  of current model  $M_t$  at t-th round and b is a constant to control the scale range. The higher the prediction error, the more important  $x_i$  would be. Here we adopt the cross-entropy loss as the prediction error with the current model  $M_t$ :

$$
err_t(x_i) = L_{ce}(y_i, \mathbf{M}_t(x_i)).
$$
\n(6)

The current model is usually not well-trained and may over-fit on limited uploaded condensed knowledge, the distribution of loss would be skewed and less calibrated to reflect the proper importance relation. We propose the selfensemble of the current model  $M_t$  and previous model  $M_{t-1}$  to smooth and regularize the distribution of loss. Thus the desired knowledge can be condensed progressively. we refine the prediction error of Eq. 6 as:

$$
e\tilde{r}r_t(x_i) = L_{ce}(y_i, \alpha \mathbf{M}_t(x_i) + (1 - \alpha)\mathbf{M}_{t-1}(x_i)), \quad (7)
$$

where  $\alpha$  is a hyper-parameter. With refined prediction error, we can calculate the refined importance in Eq. 5 and replace the uniform sampling  $P_u$  in Eq. 3 with importance sampling  $P_w$  conditioned on model M<sub>t</sub> and M<sub>t−1</sub>:

$$
P_w(x_i|\mathbf{M}_t, \mathbf{M}_{t-1}) = \frac{w_{\mathbf{M}_t}(x_i)}{\sum_{x_j} w_{\mathbf{M}_t}(x_j)}.
$$
 (8)

We then refine the condensation loss of Eq. 3 as :

$$
L_{r,cond} = \sum_{c=0}^{C-1} \hat{K}_{B_c^{P_w}, B_c^{P_w}} + \hat{K}_{S_c, S_c} - 2\hat{K}_{B_c^{P_w}, S_c}, \quad (9)
$$

where data in each batch  $B_c^{P_w}$  is sampled based on  $P_w$ . Thus the condensation process in the  $t$ -th round can be regarded as a biased variant of Eq. 1 where the where the expectation over  $\mathcal T$  is replaced by a weighted expectation under  $P_w$ :

$$
\underset{\mathcal{S}}{\arg\min} \underset{\|\psi_{\theta}\|_{\mathcal{H}}\leq 1}{\sup} (\mathbb{E}_{P_w}[\psi_{\theta}(\mathcal{T})] - \mathbb{E}[\psi_{\theta}(\mathcal{S})]), \qquad (10)
$$

Note that the current model  $M_t$  is dynamically updating. We measure the importance and derive the importance sampling  $P_w$  at each round. Thus, the condensed knowledge in each round can continue to transition from known knowledge to missing knowledge. The global model could complement its ability at each round, making its performance improve consistently.

### Relational Prototype-wise Contrastive Learning

On the server side, we calculate the global logit prototype for each class and identify their hard negative classes. Afterward, prototype-wise contrastive learning is deployed to facilitate the discrimination between classes.

At the t-th round, besides the condensed knowledge dataset, each client  $k$  uploads the logit prototypes  $\{p_0^{k,t},...,p_{C-1}^{k,t}\}\$  calculated by the global model as:

$$
\mathbf{p_c}^{k,t} = \frac{1}{N_{c,k}} \sum_{i}^{N_{c,k}} f_{M_{t-1}}(x_{i,c,k}), \tag{11}
$$

where the  $x_{i,c,k}$  denotes the local data with class c in client k, and  $f_{M_{t-1}}$  denotes the current model without the last softmax layer at the beginning of the  $t$ -th round. Then we aggregate these prototypes uploaded from each client into global logit prototypes  $\{ \mathbf{p_0}^t, ..., \mathbf{p_{C-1}}^t \}$ :

$$
\mathbf{p_c}^t = \frac{1}{|\mathcal{T}_c|} \sum_{k}^{N} |\mathcal{T}_{c,k}| \mathbf{p_c}^{k,t}, \tag{12}
$$

where N denotes the number of clients,  $|\mathcal{T}_c|$  denotes the total number of data of class c, and  $|\mathcal{T}_{c,k}|$  denotes the number of data of class  $c$  in client  $k$ . With global logit prototypes, we can derive the Top-K hard negative classes for class  $c$  as :

$$
HN(c) = \{j_1, j_2, ... j_K\} = \underset{j \neq c}{\arg \text{top}} K \ \mathbf{p_c}[j], \qquad (13)
$$

where  $HN(c)$  contains the class indices with Top-K values in prototype vector  $p_c$  except c. We recognize  $HN(c)$  as hard negative classes' indices set for class  $c$  since the global always predicts a higher probability on these classes and tends to mis-classify. To amplify discrimination ability of the global model, it would be more effective to push features of class c away from that of  $HN(c)$ . Note that the global logit prototypes may change across rounds with the updated global model,  $HN(c)$  would also change adaptively.

We also calculate feature prototypes  $\{\mathbf{f_0}^t, ..., \mathbf{f_{C-1}}^t\}$  with condensed knowledge datasets on the server at  $t$ -th round:

$$
\mathbf{f_c}^t = \frac{1}{|\mathcal{S}_c^{t-1}|} \sum_{s_i \in \mathcal{S}_c^{0, \dots, t-1}} \psi_{\theta}^{t-1}(s_i), \tag{14}
$$

where  $S_c^{t-1}$  is the accumulated knowledge dataset of class c uploaded before t-th round, and  $\psi_{\theta}^{t-1}$  is the encoder of the global model  $M_{t-1}$  at the very beginning of t-th round.

With hard negative classes set  $HN(c)$  and feature prototypes, inspired by SimSiam (Chen and He 2021), we propose relational supervised contrastive learning with prototypes in a bootstrap manner:

$$
L_{rc} = \sum_{(s_i, c_i) \in S: t} -\log \frac{\exp (h(\psi_\theta^t(s_i)) \cdot \mathbf{f_{c_i}}^t / \tau)}{\sum_{c_j \in H N(c_i)} \exp (h(\psi_\theta^t(s_i)) \cdot \mathbf{f_{c_j}}^t / \tau)},
$$
\n(15)

where  $h$  is a learnable projector similar with (Chen and He 2021; Grill et al. 2020) and  $\tau$  is the temperature hyperparameter. Then we update the global model along with cross-entropy loss at  $t$ -th round with:

$$
L_{update} = L_{ce}(S^{:t}) + L_{rc},\tag{16}
$$

where  $L_{ce}$  denotes the cross-entropy loss with condensed knowledge datasets and the relational supervised contrastive learning offers more supervision signals in model updating.

# Experiments

Datasets. We evaluate the performance of our proposed FedVCK on 4 medical tasks, which contain 5 datasets with different modalities from (Yang, Shi, and Ni 2021; Yang et al. 2023): 1) Colon Pathology, we adopt the Path dataset, 2) Retinal OCT scans, we adopt the OCT dataset, 3) Abdominal CT scans, we adopt the OrganS and OrganC dataset, 4) Chest X-Ray, we adopt the Pneumonia dataset. To validate the generality, we also select CI-FAR10 (Krizhevsky 2009), STL10 (Coates, Ng, and Lee 2011), and ImageNette (Howard and Team 2019) datasets. Our selected datasets enjoy a wide range of modalities and resolutions from  $28\times28$  to  $224\times224$  and detailed introductions about datasets are shown in the Appendix.

Baselines. We compare FedVCK with nine federated learning methods including both model-centric methods (FedAvg, FedProx, and MOON) and data-centric methods (FedGen, FedMix, FedGAN, DFRD, FedDM, and DESA). We summarize rationale of the baseline selection and their synthesis objectives and methods in Table 1 in Appendix.

|               | 0.05  |            |        |            |            | 0.02  |            |        |            |            |
|---------------|-------|------------|--------|------------|------------|-------|------------|--------|------------|------------|
| Model         |       | ConvNet    |        |            | ResNet18   |       | ConvNet    |        | ResNet18   |            |
| $Acc(\%)$     | Path  | <b>OCT</b> | OrganS | OrganC     | Pneumonia  | Path  | <b>OCT</b> | OrganS | OrganC     | Pneumonia  |
| FedAvg        | 46.34 | 62.00      | 66.27  | 70.38      | 69.87      | 43.72 | 26.54      | 60.70  | 65.87      | 63.14      |
| FedProx       | 61.34 | 62.50      | 69.14  | 71.80      | 69.07      | 40.15 | 32.20      | 67.76  | 60.94      | 62.50      |
| <b>MOON</b>   | 51.91 | 56.10      | 52.33  | 71.82      | 62.50      | 50.88 | 29.90      | 62.84  | 61.81      | 62.50      |
| FedGen        | 42.03 | 53.25      | 59.90  | 49.65      | 60.37      | 39.87 | 34.74      | 47.06  | 37.63      | 58.43      |
| FedGAN        | 54.40 | 56.80      | 71.76  | <b>OOM</b> | 75.32      | 54.37 | 25.20      | 70.34  | <b>OOM</b> | 62.50      |
| FedMix        | 35.78 | 48.90      | 62.10  | 60.63      | 62.50      | 31.50 | 29.30      | 54.65  | 29.22      | 62.50      |
| <b>DFRD</b>   | 37.44 | 31.50      | 39.80  | <b>OOM</b> | <b>OOM</b> | 14.01 | 34.20      | 37.93  | <b>OOM</b> | <b>OOM</b> |
| FedDM         | 73.97 | 61.70      | 71.37  | 35.60      | 75.80      | 73.64 | 62.20      | 69.46  | 18.20      | 68.75      |
| <b>DESA</b>   | 33.37 | 47.00      | 69.98  | 54.16      | 61.38      | 66.41 | 35.20      | 67.32  | 39.46      | 62.50      |
| <b>FedVCK</b> | 80.36 | 68.30      | 73.23  | 79.52      | 86.70      | 81.10 | 68.20      | 72.90  | 79.04      | 84.62      |

Table 2: Overall predictive accuracy comparison on medical datasets. We test our method and baselines under two non-IID scenarios:  $Dir(0.05)$  and  $Dir(0.02)$ . For datasets with 224×224 image sizes, we adopt the ResNet18 model. **Bold** numbers indicate the best accuracy results. 'OOM' indicates out-of-memory.

| β             | 0.05  |         |             |            |               | 0.02          |                  |             |            |               |
|---------------|-------|---------|-------------|------------|---------------|---------------|------------------|-------------|------------|---------------|
|               | Model | ConvNet |             |            | ResNet18      |               | ConvNet          |             |            | ResNet18      |
|               |       | Acc(%)  | <b>Path</b> | <b>OCT</b> | <b>OrganS</b> | <b>OrganC</b> | <b>Pneumonia</b> | <b>Path</b> | <b>OCT</b> | <b>OrganS</b> |
| FedAvg        | 18.55 | 53.70   | 36.92       | 29.31      | 62.50         | 12.84         | 25.00            | 39.32       | 25.13      | 35.74         |
| FedProx       | 56.94 | 49.70   | 48.76       | 50.60      | 63.78         | 40.15         | 28.70            | 49.60       | 46.13      | 62.50         |
| <b>MOON</b>   | 49.40 | 32.00   | 45.69       | 33.43      | 62.50         | 26.96         | 25.00            | 42.60       | 25.83      | 62.50         |
| FedGen        | 34.47 | 27.60   | 45.75       | 24.89      | 58.00         | 37.20         | 25.00            | 37.79       | 18.77      | 57.50         |
| FedGAN        | 19.89 | 48.00   | 55.65       | <b>OOM</b> | 64.74         | 32.32         | 25.00            | 44.67       | <b>OOM</b> | 62.50         |
| FedMix        | 28.97 | 31.60   | 57.35       | 32.46      | 62.50         | 28.48         | 25.00            | 42.37       | 17.73      | 62.50         |
| <b>DFRD</b>   | 22.40 | 25.00   | 39.05       | <b>OOM</b> | <b>OOM</b>    | 10.45         | 25.00            | 29.93       | <b>OOM</b> | <b>OOM</b>    |
| FedDM         | 72.76 | 61.70   | 71.32       | 31.66      | 73.27         | 70.39         | 62.20            | 69.33       | 15.12      | 62.50         |
| <b>DESA</b>   | 29.48 | 36.90   | 66.00       | 50.72      | 38.78         | 43.62         | 27.80            | 66.42       | 39.46      | 37.50         |
| <b>FedVCK</b> | 78.52 | 66.41   | 72.65       | 71.39      | 83.01         | 78.61         | 65.90            | 71.68       | 71.89      | 82.48         |

Table 3: Predictive accuracy comparison on medical datasets under limited communication budgets.

Configuration. Following the commonly used setting, we simulate non-IID scenarios with Dirichlet distribution  $Dir(\beta)$  among 10 clients where  $\beta$  is 0.05 and 0.02 to simulate severe non-IID scenarios. We adopt the ConvNet (Gidaris and Komodakis 2018) and ResNet18 (He et al. 2016). We set the size of the condensed knowledge dataset S to  $p\%$ of the original dataset size, where p is selected from  $\{1, 2, 5\}$ according to different datasets. We initialize S from  $\mathcal{N}(0, 1)$ . Hyper-parameters in each method are tuned as suggested in the original papers. We run all experiments with NVIDIA Geforce RTX 3090 GPU and report the mean results among three runs. More details are introduced in the Appendix.

## Performance Under Non-IID Scenarios

Overall performance. We evaluate all methods' overall performance under non-IID scenarios in Table 2, assuming the communication budgets are adequate (100 communication rounds). We can observe that model-centric federated learning methods struggle with mediocre performance. Some data-centric methods (e.g. FedGen and DFRD) perform worse. We find this is because the poor synthesis quality and poor global model hinder each other and cause a vicious circle. On OrganC and Pneumonia datasets, FedGAN and DRFD face the out-of-memory problem. Clients in FedGAN must train and upload huge generators and discriminators and the server in DFRD must maintain ensemble models and huge generators. Most data-centric baselines degrade hardly on the two datasets since capturing subtle and meaningful knowledge in larger sizes is harder. Our method successfully condenses knowledge with high quality and high necessity for the global model, thus showing advantages over all datasets' baselines.

Under limited communication budgets. Since communication budgets are usually limited in reality, a method that can achieve high performance within a few communication rounds is more desired. We compare the performance of our method and baselines within 10 communication rounds under non-IID scenarios. The experimental results are shown in Table 3. We can observe that all baselines cannot achieve satisfactory performance within limited rounds, while our method consistently outperforms others on all datasets. The performance is relatively close to the overall performance in Table 2 and would not be significantly affected by more severe non-IID ( $\beta = 0.02$ ), demonstrating that our method is not only communication-efficient but also robust to non-IID.

## Performance Analysis

Ablation study. We conduct ablation study to evaluate the effectiveness of our designs. The experimental results are shown in Table 4. Besides, we measure the empirical MMD of the condensed knowledge between two adjacent rounds

| Acc(%)              | Path  | <b>OrganS</b> | Path  | OrganS |
|---------------------|-------|---------------|-------|--------|
| w.o. all            | 72.76 | 71.32         | 73.97 | 71.37  |
| w.o. $L_{rc} + P_w$ | 73.04 | 71.74         | 76.48 | 72.13  |
| w.o. $L_{rc}$       | 74.52 | 71.93         | 78.56 | 72.40  |
| <b>FedVCK</b>       | 78.52 | 72.65         | 80.36 | 73.23  |

Table 4: Ablation study on medical datasets. The left part of the table is the performance under limited communication rounds and the right part is the overall performance.

| <b>Method</b>                                                  | Path           | OrganS         | Pneumonia       |
|----------------------------------------------------------------|----------------|----------------|-----------------|
| FedMix, DRFD<br>FedAvg, FedProx<br>MOON, FedGen<br><b>DESA</b> | 12.13 MB       | 12.20 MB       | 426.15 MB       |
| FedGAN                                                         | 178.85 MB      | 178.69 MB      | 2349.40 MB      |
| FedVCK, FedDM                                                  | <b>2.04 MB</b> | <b>0.52 MB</b> | <b>11.77 MB</b> |
| p%                                                             | 1%             | 5%             | 5%              |

Table 5: Per-round upload communication costs.  $p\%$  indicates the size of the condensed knowledge dataset as a proportion of the size of the original dataset.

with or without model-guided selection in Figure 1b. We can note that with model-guided selection, the condensed knowledge between adjacent rounds exhibits greater MMD values, reflecting that it can avoid repeated knowledge and force the optimization process to condense more heterogeneous and model-specific knowledge. We also study the impact of the size of learnable knowledge dataset in the Appendix. Larger size would have more capacity but increase optimization difficulty and communication overhead.

Communication analysis. Our method is communication efficient from two aspects. From the perspective of communication rounds, we have demonstrated our method can quickly achieve satisfactory performance under limited budgets in Table 3. From the perspective of upload communication costs, we quantify the actual per-round upload communication costs of all clients in Table 5. Our method's perround uploading costs are less than that of model-centric federated learning. More analysis about the communication and full experimental results are shown in the Appendix.

# Privacy Analysis

To practically test whether the condensed knowledge would leak individual privacy, we conduct the membership inference attack following an advanced method: LiRA (Carlini et al. 2022) and compare FedVCK with the model-centric federated learning method (e.g. FedAvg). We attack uploaded models or condensed knowledge from clients and record the AUC of ROC on each client with a balanced test set. Since the MIA task is a binary classification, we set the minimum AUC to 0.5 and mark it as a total defense if the AUC of a client is less than or equal to 0.5. We calculate the max and mean AUC of all clients and defense rate (the proportion of clients achieving total defense) in Table 6. The results show that our method better preserves privacy than FedAvg, and enables more total defense cases. In addition,

| Method        | Max AUC ↓    | Mean AUC ↓   | Defense Rate ↑ |
|---------------|--------------|--------------|----------------|
| FedAvg        | 0.556        | 0.529        | 10%            |
| <b>FedVCK</b> | <b>0.544</b> | <b>0.514</b> | <b>50%</b>     |

Table 6: The AUC results of MIA experiment on OrganS dataset.  $\downarrow$  means the lower, the better.  $\uparrow$  means the opposite.

| Acc(%)  | CIFAR10 |       | STL10 |       | ImageNette |       |
|---------|---------|-------|-------|-------|------------|-------|
|         | 0.05    | 0.02  | 0.05  | 0.02  | 0.05       | 0.02  |
| FedAvg  | 52.13   | 52.01 | 46.03 | 39.60 | 47.21      | 38.17 |
| FedProx | 57.60   | 53.39 | 42.93 | 42.88 | 52.66      | 32.84 |
| MOON    | 46.63   | 42.03 | 38.08 | 38.42 | 35.26      | 21.32 |
| FedGen  | 39.36   | 32.71 | 38.11 | 37.44 | 31.92      | 41.89 |
| FedGAN  | 55.79   | 53.86 | 51.84 | 50.03 | 50.80      | 40.31 |
| FedMix  | 42.28   | 43.97 | 46.56 | 42.88 | 50.80      | 39.41 |
| DFRD    | 52.07   | 37.53 | 31.60 | 21.03 | 33.20      | 16.20 |
| FedDM   | 54.75   | 50.47 | 54.90 | 51.62 | 52.25      | 43.82 |
| DESA    | 53.90   | 48.19 | 46.74 | 37.33 | 42.29      | 28.90 |
| FedVCK  | 62.96   | 60.56 | 57.04 | 56.89 | 62.76      | 61.73 |

Table 7: Overall predictive accuracy comparison on natural datasets. We adopt the ConvNet model by default.

since our method needs fewer communication rounds, privacy can be further protected from potential temporal-based MIA (Zhu et al. 2024).

### Extend to Natural Datasets

To validate the generality of our method, we also extend our evaluation on natural datasets. The natural datasets contain various colored objects with more significant inter-class differences. The overall experimental results are shown in Table 7. Our method still outperforms others consistently, which demonstrate a boarder generality of our method. Full experiments about the predictive performance, communication cost, and privacy-preserving are listed in the Appendix.

# Conclusion and Discussion

In this paper, we propose a novel data-centric federated learning method, FedVCK, for collaborative medical image analysis. FedVCK can tackle the non-IID problem in a communication-efficient manner. Specifically, FedVCK adaptively selects the most necessary knowledge with the guidance of current models, and condenses it into a small knowledge dataset with latent distribution constraints to enhance the quality. The condensed knowledge can effectively update the global model with the help of relational supervised contrastive learning. Our method generally outperforms state-of-the-art methods in non-IID scenarios, especially under limited communication budgets. Further work is to extend to more data modalities such as 3D CT and to adopt advanced techniques to improve the effectiveness and efficiency of condensation.

## Acknowledgments

This work is supported by the National Key R&D Program of China under Grant No.2022YFB2703301.

# References

Acar, D. A. E.; Zhao, Y.; Navarro, R. M.; Mattina, M.; Whatmough, P. N.; and Saligrama, V. 2021. Federated learning based on dynamic regularization. *arXiv preprint arXiv:2111.04263*.

Carlini, N.; Chien, S.; Nasr, M.; Song, S.; Terzis, A.; and Tramer, F. 2022. Membership inference attacks from first principles. In *2022 IEEE Symposium on Security and Privacy (SP)*, 1897–1914. IEEE.

Chen, H.-Y.; and Chao, W.-L. 2020. Fedbe: Making bayesian model ensemble applicable to federated learning. *arXiv preprint arXiv:2009.01974*.

Chen, X.; and He, K. 2021. Exploring simple siamese representation learning. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, 15750– 15758.

Coates, A.; Ng, A.; and Lee, H. 2011. An analysis of singlelayer networks in unsupervised feature learning. In *Proceedings of the fourteenth international conference on artificial intelligence and statistics*, 215–223. JMLR Workshop and Conference Proceedings.

Dong, T.; Zhao, B.; and Lyu, L. 2022. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, 5378–5396. PMLR.

Dwork, C.; McSherry, F.; Nissim, K.; and Smith, A. 2006. Calibrating noise to sensitivity in private data analysis. In *Theory of Cryptography: Third Theory of Cryptography Conference, TCC 2006, New York, NY, USA, March 4-7, 2006. Proceedings 3*, 265–284. Springer.

Gao, X.; Chen, T.; Zhang, W.; Li, Y.; Sun, X.; and Yin, H. 2024a. Graph condensation for open-world graph learning. In *Proceedings of the 30th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, 851–862.

Gao, X.; Yu, J.; Chen, T.; Ye, G.; Zhang, W.; and Yin, H. 2024b. Graph condensation: A survey. *arXiv preprint arXiv:2401.11720*.

Gidaris, S.; and Komodakis, N. 2018. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, 4367–4375.

Goodfellow, I.; Pouget-Abadie, J.; Mirza, M.; Xu, B.; Warde-Farley, D.; Ozair, S.; Courville, A.; and Bengio, Y. 2020. Generative adversarial networks. *Communications of the ACM*, 63(11): 139–144.

Gretton, A.; Borgwardt, K.; Rasch, M.; Schölkopf, B.; and Smola, A. 2012. A kernel two-sample test. *Journal of Machine Learning Research,Journal of Machine Learning Research*.

Grill, J.-B.; Strub, F.; Altché, F.; Tallec, C.; Richemond, P.; Buchatskaya, E.; Doersch, C.; Avila Pires, B.; Guo, Z.; Gheshlaghi Azar, M.; et al. 2020. Bootstrap your own latenta new approach to self-supervised learning. *Advances in neural information processing systems*, 33: 21271–21284.

He, K.; Zhang, X.; Ren, S.; and Sun, J. 2016. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, 770–778.

Howard, J.; and Team, F. 2019. Imagenette: A subset of 10 easily classified classes from Imagenet. Available at https://github.com/fastai/imagenette. Accessed on 2024-08- 01.

Huang, C.-Y.; Srinivas, K.; Zhang, X.; and Li, X. 2024. Overcoming Data and Model Heterogeneities in Decentralized Federated Learning via Synthetic Anchors. *arXiv preprint arXiv:2405.11525*.

Ioffe, S.; and Szegedy, C. 2015. Batch normalization: Accelerating deep network training by reducing internal covariate shift. In *International conference on machine learning*, 448– 456. pmlr.

Krizhevsky, A. 2009. Learning multiple layers of features from tiny images. Technical Report, Unifeatures from tiny images.<br>versity of Toronto. CIF CIFAR-10 dataset available at https://www.cs.toronto.edu/ kriz/cifar.html.

Li, Q.; Diao, Y.; Chen, Q.; and He, B. 2022a. Federated learning on non-iid data silos: An experimental study. In *2022 IEEE 38th international conference on data engineering (ICDE)*, 965–978. IEEE.

Li, Q.; He, B.; and Song, D. 2021. Model-contrastive federated learning. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, 10713–10722.

Li, T.; Sahu, A. K.; Zaheer, M.; Sanjabi, M.; Talwalkar, A.; and Smith, V. 2020. Federated optimization in heterogeneous networks. *Proceedings of Machine learning and systems*, 2: 429–450.

Li, X.; Huang, K.; Yang, W.; Wang, S.; and Zhang, Z. 2019. On the convergence of fedavg on non-iid data. *arXiv preprint arXiv:1907.02189*.

Li, Z.; Shao, J.; Mao, Y.; Wang, J. H.; and Zhang, J. 2022b. Federated learning with gan-based data synthesis for non-iid clients. In *International Workshop on Trustworthy Federated Learning*, 17–32. Springer.

Li, Z.; Sun, Y.; Shao, J.; Mao, Y.; Wang, J. H.; and Zhang, J. 2024. Feature matching data synthesis for non-iid federated learning. *IEEE Transactions on Mobile Computing*.

Lin, T.; Kong, L.; Stich, S. U.; and Jaggi, M. 2020. Ensemble distillation for robust model fusion in federated learning. *Advances in neural information processing systems*, 33: 2351–2363.

Mothukuri, V.; Parizi, R. M.; Pouriyeh, S.; Huang, Y.; Dehghantanha, A.; and Srivastava, G. 2021. A survey on security and privacy of federated learning. *Future Generation Computer Systems*, 115: 619–640.

Nguyen, D. C.; Ding, M.; Pathirana, P. N.; Seneviratne, A.; and Zomaya, A. Y. 2021. Federated learning for COVID-19 detection with generative adversarial networks in edge cloud computing. *IEEE Internet of Things Journal*, 9(12): 10257– 10271.

Verma, V.; Lamb, A.; Beckham, C.; Najafi, A.; Mitliagkas, I.; Lopez-Paz, D.; and Bengio, Y. 2019. Manifold mixup: Better representations by interpolating hidden states. In *International conference on machine learning*, 6438–6447. PMLR.

Wang, S.; Fu, Y.; Li, X.; Lan, Y.; Gao, M.; et al. 2024a. DFRD: Data-Free Robustness Distillation for Heterogeneous Federated Learning. *Advances in Neural Information Processing Systems*, 36.

Wang, T.; Zhu, J.-Y.; Torralba, A.; and Efros, A. A. 2018. Dataset distillation. *arXiv preprint arXiv:1811.10959*.

Wang, Y.; Fu, H.; Kanagavelu, R.; Wei, Q.; Liu, Y.; and Goh, R. S. M. 2024b. An aggregation-free federated learning for tackling data heterogeneity. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 26233–26242.

Xie, L.; Lin, M.; Liu, S.; Xu, C.; Luan, T.; Li, C.; Fang, Y.; Shen, Q.; and Wu, Z. 2024a. pFLFE: Cross-silo Personalized Federated Learning via Feature Enhancement on Medical Image Segmentation . In *proceedings of Medical Image Computing and Computer Assisted Intervention – MICCAI 2024*, volume LNCS 15010. Springer Nature Switzerland.

Xie, L.; Lin, M.; Luan, T.; Li, C.; Fang, Y.; Shen, Q.; and Wu, Z. 2024b. MH-pFLID: Model Heterogeneous personalized Federated Learning via Injection and Distillation for Medical Data Analysis. In *Forty-first International Conference on Machine Learning*.

Xie, L.; Lin, M.; Xu, C.; Luan, T.; Zeng, Z.; Qian, W.; Li, C.; Fang, Y.; Shen, Q.; and Wu, Z. 2024c. MH-pFLGB: Model Heterogeneous personalized Federated Learning via Global Bypass for Medical Image Analysis . In *proceedings of Medical Image Computing and Computer Assisted Intervention – MICCAI 2024*, volume LNCS 15010. Springer Nature Switzerland.

Xiong, Y.; Wang, R.; Cheng, M.; Yu, F.; and Hsieh, C.-J. 2023. Feddm: Iterative distribution matching for communication-efficient federated learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 16323–16332.

Yang, J.; Shi, R.; and Ni, B. 2021. MedMNIST Classification Decathlon: A Lightweight AutoML Benchmark for Medical Image Analysis. In *IEEE 18th International Symposium on Biomedical Imaging (ISBI)*, 191–195.

Yang, J.; Shi, R.; Wei, D.; Liu, Z.; Zhao, L.; Ke, B.; Pfister, H.; and Ni, B. 2023. MedMNIST v2-A large-scale lightweight benchmark for 2D and 3D biomedical image classification. *Scientific Data*, 10(1): 41.

Yin, H.; Molchanov, P.; Alvarez, J. M.; Li, Z.; Mallya, A.; Hoiem, D.; Jha, N. K.; and Kautz, J. 2020. Dreaming to distill: Data-free knowledge transfer via deepinversion. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, 8715–8724.

Yoon, T.; Shin, S.; Hwang, S. J.; and Yang, E. 2021. Fedmix: Approximation of mixup under mean augmented federated learning. *arXiv preprint arXiv:2107.00233*.

Yu, R.; Liu, S.; and Wang, X. 2023. Dataset distillation: A comprehensive review. *IEEE Transactions on Pattern Analysis and Machine Intelligence*.

Zhang, H.; Li, S.; Wang, P.; Zeng, D.; and Ge, S. 2024. M3D: Dataset Condensation by Minimizing Maximum Mean Discrepancy. In Wooldridge, M. J.; Dy, J. G.; and Natarajan, S., eds., *Thirty-Eighth AAAI Conference on Artificial Intelligence, AAAI 2024, Thirty-Sixth Conference on Innovative Applications of Artificial Intelligence, IAAI 2024, Fourteenth Symposium on Educational Advances in Artificial Intelligence, EAAI 2014, February 20-27, 2024, Vancouver, Canada*, 9314–9322. AAAI Press.

Zhang, L.; Shen, L.; Ding, L.; Tao, D.; and Duan, L.-Y. 2022. Fine-tuning global model via data-free knowledge distillation for non-iid federated learning. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, 10174–10183.

Zhao, B.; and Bilen, H. 2023. Dataset Condensation with Distribution Matching. In *2023 IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*.

Zhao, Y.; Li, M.; Lai, L.; Suda, N.; Civin, D.; and Chandra, V. 2018. Federated learning with non-iid data. *arXiv preprint arXiv:1806.00582*.

Zheng, X.; Ying, S.; Zheng, F.; Yin, J.; Zheng, L.; Chen, C.; and Dong, F. 2023. Federated Learning on Non-iid Data via Local and Global Distillation. In *2023 IEEE International Conference on Web Services (ICWS)*, 647–657. IEEE.

Zhou, T.; Zhang, J.; and Tsang, D. H. 2023. FedFA: Federated learning with feature anchors to align features and classifiers for heterogeneous data. *IEEE Transactions on Mobile Computing*.

Zhu, G.; Li, D.; Gu, H.; Han, Y.; Yao, Y.; Fan, L.; and Yang, Q. 2024. Evaluating Membership Inference Attacks and Defenses in Federated Learning. *arXiv preprint arXiv:2402.06289*.

Zhu, L.; Liu, Z.; and Han, S. 2019. Deep leakage from gradients. *Advances in neural information processing systems*, 32.

Zhu, W.; and Luo, J. 2022. Federated medical image analysis with virtual sample synthesis. In *International Conference on Medical Image Computing and Computer-Assisted Intervention*, 728–738. Springer.

Zhu, Z.; Hong, J.; and Zhou, J. 2021. Data-free knowledge distillation for heterogeneous federated learning. In *International conference on machine learning*, 12878–12889. PMLR.

# Datasets and Configurations

We evaluate methods on two categories of datasets: medical datasets and natural datasets. For medical datasets, we adopt 5 widely used datasets with different modalities and image sizes.

- Path consists of 107,180 histopathologic images of colorectal cancer, collected from hematoxylin and eosinstained histological slides. The dataset is divided into 89,996 training images, 10,004 validation images, and 7,180 test images. The images are 28×28 pixels and contain nine types of tissues: tumor epithelium, simple stroma, complex stroma, immune cells, debris, mucus, smooth muscle, normal colon mucosa, and cancerassociated stroma.
- OCT is based on optical coherence tomography images of the retina. This dataset includes 109,309 images categorized into four classes: choroidal neovascularization, diabetic macular edema, drusen, and normal. The dataset is split into 97,477 training images, 10,832 validation images, and 1,000 test images. The images are gray-scale and resized to 28×28 pixels.
- OrganS comprises 25,221 abdominal CT images, labeled into 11 classes representing different organ segments. The dataset is split into 13,940 training images, 2,452 validation images, and 8,829 test images. The images are  $28\times28$  pixels in size and are used for multi-class classification tasks.
- OrganC contains 23,660 abdominal CT images, categorized into 11 classes. The dataset is divided into 13,000 training images, 2,392 validation images, and 8,268 test images. We use the large-size version. All samples are  $224 \times 224$  pixels in size. It is utilized for multi-class classification tasks.
- Pneumonia is derived from a dataset of 5,856 chest Xray images, with labels for pneumonia and normal cases. The dataset includes 4,708 training images, 524 validation images, and 624 test images. We use the large-size version. All samples are 224×224 pixels in size.

For natural datasets, we adopt 3 widely used datasets and follow the standard splits:

- CIFAR10 consists of 60,000,  $32 \times 32$  color images in 10 classes, with 6,000 images per class. There are 50,000 training images and 10,000 test images. The classes are airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck.
- STL10 is a 10-class color image dataset. Each class contains 1,300 images. We resize each sample to  $32\times32$ , similar to CIFAR10. The classes include airplane, bird, car, cat, deer, dog, horse, monkey, ship, and truck.
- ImageNette is a subset of the larger ImageNet dataset, containing 10 classes and around 10,000 images. The classes include tench, English springer, cassette player, chain saw, church, French horn, garbage truck, gas pump, golf ball, and parachute. We resize each sample to  $64\times64$ .

We adopt the standard preprocessing and splits on all datasets and report the mean accuracy among three runs. We adopt the ConvNet (Gidaris and Komodakis 2018) model and adopt ResNet18 (He et al. 2016) model for datasets with  $224 \times 224$  image sizes. We set the size of the condensed knowledge dataset S to  $p\%$  of that of the original dataset, where we select p from  $\{1, 2, 5\}$  according to different datasets. We initialize S from  $\mathcal{N}(0, 1)$ . We set the local epochs as 10. We tune the learning rate of model training and knowledge learning from 0.001 to 0.01. Other hyperparameters in each method are tuned as suggested in the original papers. We implement all methods with Pytorch 2.1 on Ubuntu 20.04 equipped with an NVIDIA Geforce RTX 3090 GPU and Intel(R) Xeon(R) CPU E5-2680 v4.

| Method      | <b>Synthesis Objective</b> | <b>Synthesis Method</b> |
|-------------|----------------------------|-------------------------|
| FedGen      | latent feature             | generator at server     |
| FedMix      | original sample            | mix up                  |
| FedGAN      | original sample            | GAN at clients          |
| <b>DFRD</b> | original sample            | model inversion         |
| FedDM       | latent knowledge           | distribution matching   |
| <b>DESA</b> | anchor data                | distribution matching   |

Table 8: Summary of synthesis objectives and methods of representative data-centric federated learning methods.

## Summary of Data-centric Baselines

Various data-centric federated learning methods synthesize various objectives with different methods. The synthesis objectives including 1) latent features (Zhu, Hong, and Zhou 2021), synthesized by a generator on the server, 2) original data (Nguyen et al. 2021; Li et al. 2022b; Zhu and Luo 2022), synthesized by generative models such as GANs, 3) data mixture (Yoon et al. 2021), synthesized by simply mix up the samples and labels of a few data, 4) model inverted data (Zhang et al. 2022; Wang et al. 2024a), synthesized by model inversion, 5) latent knowledge (Xiong et al. 2023; Wang et al. 2024b; Huang et al. 2024) by distribution matching. Considering different synthesis objectives and methods, We select FedGen, FedMix, FedGAN, DFRD, FedDM and DESA as representative baselines. We summarize them in Table 8.

Problems. Most data-centric federated learning methods face problems in the quality and value of the synthesized data, especially under non-IID scenarios. We summarize the problems as follows:

• Low synthesized data quality. The quality of synthesized and shared virtual data would be low, leading to the model being biased or degraded. For example, the generative models (Goodfellow et al. 2020) generated data only contain information of the single sample, and the training of generative models with limited local data would be costly and unstable (Li et al. 2024). The mixup of the real data would distort the data due to the nonlinearity between input space and label space, causing incorrect data pairs (Verma et al. 2019). The quality of

Image /page/10/Figure/0 description: A bar chart displays the empirical MMD for 11 classes, labeled Class 0 through Class 10. For each class, there are two bars: one blue bar representing 'Without Pw' and an orange bar representing 'With Pw'. The y-axis, labeled 'Empirical MMD', ranges from 0.2 to 0.6. For Class 0, 'Without Pw' is approximately 0.49 and 'With Pw' is approximately 0.6. For Class 1, 'Without Pw' is approximately 0.52 and 'With Pw' is approximately 0.61. For Class 2, 'Without Pw' is approximately 0.53 and 'With Pw' is approximately 0.61. For Class 3, 'Without Pw' is approximately 0.53 and 'With Pw' is approximately 0.62. For Class 4, 'Without Pw' is approximately 0.49 and 'With Pw' is approximately 0.58. For Class 5, 'Without Pw' is approximately 0.46 and 'With Pw' is approximately 0.55. For Class 6, 'Without Pw' is approximately 0.02 and 'With Pw' is approximately 0.21. For Class 7, 'Without Pw' is approximately 0.5 and 'With Pw' is approximately 0.57. For Class 8, 'Without Pw' is approximately 0.49 and 'With Pw' is approximately 0.57. For Class 9, 'Without Pw' is approximately 0.47 and 'With Pw' is approximately 0.56. For Class 10, 'Without Pw' is approximately 0.3 and 'With Pw' is approximately 0.39.

Figure 3: We measure the average MMD of condensed knowledge class-wisely between adjacent rounds on the OrganC dataset. Greater MMD indicates a larger difference in distribution. The vanilla selection would cause more knowledge repetition between rounds. With our model-guided selection  $(P_w)$  in each round, the condensed knowledge between adjacent rounds is more different.

model inversion is highly related to the model performance. Biased models under non-IID scenarios fail to generate data of fidelity. The distribution matching would distill knowledge from local data. However, it only match the final features and it still faces problems dealing with extracting subtle and discriminative features from larger image sizes with less inter-class difference.

- Repeated knowledge. Despite one can synthesize data with high fidelity, its benefits for current models are under-explored. Without explicitly considering the current global model, the knowledge in synthesized data tends to be repeated and homogeneous in each round, which would be less effective after several rounds. The global model thus can hardly advance its performance.
- Privacy risk. Some synthesis methods would easily unveil the privacy of local data. The synthetic data should carry minimum irrelevant and individual information. However, the real data mixture and data generated by generative models are highly similar to the private data, and broadcast or peer-to-peer communication (Huang et al. 2024) increases the security and privacy concerns.

We summarize the problems faced by representative methods in Table 1. Moreover, most data-centric federated learning methods still rely on frequent communication, thus they are not communication efficient.

# Impact of p Value

In experiments we set the size of the condensed knowledge dataset to  $p\%$  of the size of the original local datasets, where the value of  $p$  is selected from  $\{1,2,5\}$ . Intuitively, larger p indicates that knowledge dataset can have larger capacity to contain more knowledge. But it also increase the difficulty of optimization and increasing costs in computation and communication. To evaluate the impact of the selection of  $p$ , we test the both overall predictive performance and performance under limited communication rounds under wider range of  $p$  values selected from 0.5 to 10. The experimental

Image /page/10/Figure/8 description: A line graph displays the accuracy (%) on the y-axis against p (%) on the x-axis, with values ranging from 0.5 to 10. The graph plots six lines representing different categories: 'Path (Limited)' (cyan, solid), 'OrganS (Limited)' (green, solid), 'OrganC (Limited)' (pink, solid), 'Path (Overall)' (cyan, dashed), 'OrganS (Overall)' (green, dashed), and 'OrganC (Overall)' (pink, dashed). At p=0.5, accuracies are approximately 79% for Path (Limited), 75% for OrganS (Limited), and 75% for OrganC (Limited), and 75% for Path (Overall), 70% for OrganS (Overall), and 71% for OrganC (Overall). At p=1, accuracies are approximately 80.5% for Path (Limited), 69.5% for OrganS (Limited), and 76% for OrganC (Limited), and 79% for Path (Overall), 70% for OrganS (Overall), and 71.5% for OrganC (Overall). At p=2, accuracies are approximately 81.5% for Path (Limited), 71% for OrganS (Limited), and 74.5% for OrganC (Limited), and 81% for Path (Overall), 71.5% for OrganS (Overall), and 75.5% for OrganC (Overall). At p=5, accuracies are approximately 81% for Path (Limited), 73% for OrganS (Limited), and 79.5% for OrganC (Limited), and 80.5% for Path (Overall), 73% for OrganS (Overall), and 78% for OrganC (Overall). At p=10, accuracies are approximately 81% for Path (Limited), 74.5% for OrganS (Limited), and 75% for OrganC (Limited), and 80.5% for Path (Overall), 75% for OrganS (Overall), and 79% for OrganC (Overall).

Figure 4: Predictive performance under various  $p$  values on medical datasets. 'Limited' indicates the performance under limited 10 communication rounds. 'Overall' indicates the overall performance with adequate communication rounds.

Image /page/10/Figure/10 description: The image is a line graph showing the accuracy (%) on the y-axis against p (%) on the x-axis. The x-axis has labels at 1, 2, 5, and 10. The y-axis has labels at 50, 55, 60, 65, and 70. There are six lines plotted, each representing a different dataset and condition: CIFAR10 (Limited) in dashed cyan, ImageNette (Limited) in dashed green, STL10 (Limited) in dashed pink, CIFAR10 (Overall) in solid cyan, ImageNette (Overall) in solid green, and STL10 (Overall) in solid pink. The CIFAR10 (Limited) line starts at approximately 59% at p=1 and increases to about 62% at p=2, then decreases slightly to about 61% at p=5 and p=10. The ImageNette (Limited) line starts at approximately 62% at p=1 and increases to about 64% at p=2, then to about 66% at p=5 and 68% at p=10. The STL10 (Limited) line starts at approximately 51% at p=1 and increases to about 54% at p=2, then to about 56% at p=5 and 57% at p=10. The CIFAR10 (Overall) line starts at approximately 59% at p=1 and increases to about 62% at p=2, then to about 63% at p=5 and 62% at p=10. The ImageNette (Overall) line starts at approximately 62% at p=1 and increases to about 64% at p=2, then to about 67% at p=5 and 69% at p=10. The STL10 (Overall) line starts at approximately 52% at p=1 and increases to about 54% at p=2, then to about 56% at p=5 and 57% at p=10. The legend is located in the top right corner of the graph.

Figure 5: Predictive performance under various  $p$  values on natural datasets. 'Limited' indicates the performance under limited communication rounds (10). 'Overall' indicates the overall performance with adequate communication rounds.

results on medical datasets and natural datasets are shown in Figure 4 and Figure 5. Generally, with larger size of knowledge size, the predictive performance would be better, but there is still a law of diminishing marginal utility with increasing overhead of optimization and communication.

## Ablation Study of $K$ Value in Eq. 13

We conduct the ablation study of the selection of  $K$  in Eq. 13. We select different  $K$  and test the performance on Path and OrganS datasets under limited communication budgets. The experimental results are shown in Table 13. We conclude that  $K$  should be chosen as a moderate value relative to the total number of classes. A small  $K$  would result in an insufficient number of negative samples, while a large K may introduce unnecessary and noisy 'easy negative' samples.

# More Communication Analysis

We analyze and categorize the message types of baseline methods and our method during the federated learning process in Table 10. Initialization indicates the preparation phase before federated learning. Our method does not need to communicate before and enjoys a small datasetlevel rather than model-level upload costs. The ConvNet model and ResNet18 model in our experiments have around  $3.2 \times 10^5$  and  $1.12 \times 10^7$  parameters with float 32 precision,

| <b>Method</b>                                           | <b>Path</b>    | <b>OCT</b>     | <b>OrganS</b>  | <b>OrganC</b>   | <b>Pneumonia</b> | <b>CIFAR10</b> | <b>STL10</b>   | <b>ImageNette</b> |
|---------------------------------------------------------|----------------|----------------|----------------|-----------------|------------------|----------------|----------------|-------------------|
| FedMix, DFRD<br>FedAvg, FedProx<br>MOON, FedGen<br>DESA | 12.13 MB       | 11.65 MB       | 12.20 MB       | 426.15 MB       | 426.15 MB        | 12.21 MB       | 12.21 MB       | 14.55 MB          |
| FedGAN                                                  | 178.85 MB      | 178.13 MB      | 178.69 MB      | 2349.40 MB      | 2349.40 MB       | 178.93 MB      | 178.93 MB      | 276.20 MB         |
| FedVCK, FedDM                                           | <b>2.04 MB</b> | <b>0.74 MB</b> | <b>0.52 MB</b> | <b>30.72 MB</b> | <b>11.77 MB</b>  | <b>2.96 MB</b> | <b>0.25 MB</b> | <b>1.48 MB</b>    |
| <b>p%</b>                                               | 1%             | 1%             | 5%             | 5%              | 5%               | 2%             | 5%             | 1%                |

Table 9: Per-round upload communication costs of all clients.  $p\%$  indicates the size of condensed knowledge dataset as a proportion of the size of the original dataset.

|         | Initialization | Upload      | Download    |
|---------|----------------|-------------|-------------|
| FedMix  | Mix up data    |             |             |
| DSEA    | Anchor data    |             |             |
| FedAvg  | None           | Model       | Model       |
| FedProx |                |             |             |
| MOON    |                |             |             |
| DRFD    |                |             |             |
| FedGen  |                |             | Model, G    |
| FedGAN  |                | Model, G, D | Model, G, D |
| FedDM   | Knowledge      | Model       | Model       |
| FedVCK  |                |             |             |

Table 10: Message types of each method uploaded and downloaded in each round. G denotes the generator and D denotes the discriminator. Initialization indicates the preparation phase before the federated learning.

| $Acc(\%)$ |  | CIFAR10 |       | STL10 |       | ImageNette |       |
|-----------|--|---------|-------|-------|-------|------------|-------|
|           |  | β       | 0.05  | 0.02  | 0.05  | 0.02       | 0.05  |
| FedAvg    |  | 38.68   | 26.82 | 33.09 | 27.18 | 34.22      | 11.75 |
| FedProx   |  | 37.58   | 33.67 | 28.58 | 27.91 | 42.42      | 22.78 |
| MOON      |  | 36.20   | 28.06 | 29.95 | 28.95 | 22.11      | 10.83 |
| FedGen    |  | 24.50   | 24.37 | 26.40 | 26.44 | 36.19      | 27.35 |
| FedGAN    |  | 36.63   | 31.20 | 25.90 | 29.28 | 44.18      | 25.86 |
| FedMix    |  | 37.97   | 31.83 | 27.25 | 27.01 | 40.82      | 24.23 |
| DFRD      |  | 35.98   | 27.65 | 25.07 | 19.26 | 27.01      | 14.47 |
| FedDM     |  | 54.62   | 47.96 | 53.70 | 49.71 | 50.39      | 39.97 |
| DESA      |  | 51.75   | 47.03 | 42.83 | 35.86 | 40.79      | 24.87 |
| FedVCK    |  | 61.89   | 60.04 | 54.91 | 55.04 | 61.20      | 57.94 |

Table 11: Predictive accuracy comparison on natural datasets under two non-IID scenarios:  $Dir(0.05)$  and  $Dir(0.02)$  under limited communication budgets. We adopt the ConvNet model by default. Bold numbers indicate the best accuracy results.

| Method        | Max AUC ↓    | Mean AUC ↓   | Defense Rate ↑ |
|---------------|--------------|--------------|----------------|
| FedAvg        | 0.667        | 0.575        | 10%            |
| <b>FedVCK</b> | <b>0.544</b> | <b>0.514</b> | <b>30%</b>     |

Table 12: The AUC results of MIA on CIFAR10 dataset under  $Dir(0.05)$ .  $\downarrow$  means the lower, the better.  $\uparrow$  means the opposite.

|                    | K     | 3            | 5     | 7     | 9 |
|--------------------|-------|--------------|-------|-------|---|
| Path (nclass=9)    | 76.25 | <b>78.52</b> | 77.58 | 75.77 |   |
| OrganS (nclass=11) | 72.24 | <b>72.65</b> | 72.09 | 71.92 |   |

Table 13: Ablation Study of  $K$  value

| $Acc(\%)$           | Path  | OrganS | CIFAR10 | ImageNette |
|---------------------|-------|--------|---------|------------|
| w.o. all            | 72.76 | 71.32  | 54.62   | 50.39      |
| w.o. $L_{rc} + P_w$ | 73.04 | 71.74  | 60.54   | 57.99      |
| w.o. $L_{rc}$       | 74.52 | 71.93  | 61.19   | 60.42      |
| FedVCK              | 78.52 | 72.65  | 61.89   | 61.20      |
| w.o. all            | 73.97 | 71.37  | 54.75   | 52.25      |
| w.o. $L_{rc} + P_w$ | 76.48 | 72.13  | 61.35   | 60.92      |
| w.o. $L_{rc}$       | 78.56 | 72.40  | 61.69   | 61.94      |
| <b>FedVCK</b>       | 80.36 | 73.23  | 62.95   | 62.76      |

Table 14: Ablation study on both medical and natural datasets. The top sub-table is the performance under limited communication budgets and the bottom sub-table is the performance under adequate communication budgets.

respectively. Thus, each parameter would take 4 bytes. For our condensed knowledge dataset, we can adopt the PIL format where one pixel takes 1 byte to store and transmit.

# More Experiments about Model-guided Selection

Besides the illustration on Path dataset in the main content, we also conduct analytic experiments on the OrganC dataset. we measure the empirical MMD of the condensed knowledge between two adjacent rounds class-wisely with or without model-guided selection in Figure 3. Empirically, the larger the MMD indicates, the more different the distributions are. We can note that with model-guided selection (with  $P_w$ ), the condensed knowledge between two adjacent rounds shows a higher MMD value. It reflects that it can force to condense more different knowledge from the previous round and thus avoid unnecessary repetition. The heterogeneous and model-specific knowledge would be more effective and valuable for model updating.

# Experiments on Natural Datasets

We compare our methods with baseline methods on natural datasets. The experimental results of the overall predictive performance are shown in Table 7. The experimental results of the predictive performance under limited communication budgets are shown in Table 11. In addition to predictive performance, we conduct an ablation study in Table 14 and analyze upload communication costs in Table 9 on natural datasets. We also conduct the MIA experiment to test the privacy-preserving ability on natural datasets in Table 12. All of these experiments demonstrate the advantages of our methods in terms of predictive performance, communication costs, and privacy preservation ability.

# Training Cost Analysis

In each client, we need to optimize  $O(\frac{pNwd}{100M})$  parameters and perform forward and backward  $O(T)$  times for condensation, where N is the size of the dataset,  $\frac{p}{100}$  is the total size of condensed knowledge, M is the number of clients.  $p\%$  is a small value.

# More Privacy Analysis

We initialize the knowledge dataset S with  $\mathcal{N}(0, 1)$ . Knowledge can be condensed by matching the latent feature distributions with constraints. (Dong, Zhao, and Lyu 2022) has demonstrated that each sample in the selected batch contributes equally in condensation and on one is dominated. Thus, it is difficult to infer individual privacy. (Dong, Zhao, and Lyu 2022) also connects to differential privacy (Dwork et al. 2006) and claims that the privacy budget  $\epsilon$  is of the order of  $\mathcal{O}(\frac{|S|}{|\mathcal{T}|})$ , which is a small value because  $|\mathcal{S}| \ll |\mathcal{T}|$ .
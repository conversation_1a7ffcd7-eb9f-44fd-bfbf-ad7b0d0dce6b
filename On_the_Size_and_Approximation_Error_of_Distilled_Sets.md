Image /page/0/Picture/0 description: The image contains the text "MIT CSAIL DRL" with a horizontal line below it. The text "MIT" is in grey, and "CSAIL" is in white letters within a brown and yellow graphic. "DRL" is in black letters.

# **On the Size and Approximation Error of Distilled Sets**

**[<PERSON><PERSON>](https://scholar.google.com/citations?user=6r72e-MAAAAJ&hl=en)** 1,§[,\\*](#page-0-0)**, [<PERSON><PERSON>](https://scholar.google.com/citations?user=721xaz0AAAAJ&hl=en)** 2,§ **, [<PERSON>](https://scholar.google.com/citations?user=vokGv-gAAAAJ&hl=en)** <sup>1</sup> **, [<PERSON><PERSON>](https://scholar.google.com/citations?user=YarJF3QAAAAJ&hl=en)** <sup>1</sup> **, [<PERSON>](https://scholar.google.com/citations?user=fIupeSAAAAAJ&hl=en)** <sup>1</sup> **, [<PERSON><PERSON>](https://scholar.google.com/citations?user=910z20QAAAAJ&hl=en)** <sup>1</sup>

<sup>1</sup>**Computer Science and Artificial Intelligence Lab (CSAIL), Massachusetts Institute of Technology (MIT)**

<sup>2</sup>**DataHeroes**

**Dataset Distillation is the task of synthesizing small datasets from large ones while still retaining comparable predictive accuracy to the original uncompressed dataset. Despite significant empirical progress in recent years, there is little understanding of the theoretical limitations/guarantees of dataset distillation, specifically, what excess risk is achieved by distillation compared to the original dataset, and how large are distilled datasets? In this work, we take a theoretical view on kernel ridge regression (KRR) based methods of dataset distillation such as Kernel Inducing Points. By transforming ridge regression in random Fourier features (RFF) space, we provide the first proof of the existence of small (size) distilled datasets and their corresponding excess risk for shift-invariant kernels. We prove that a small set of instances exists in the original input space such that its solution in the RFF space coincides with the solution of the original data. We further show that a KRR solution can be generated using this distilled set of instances which gives an approximation towards the KRR solution optimized on the full input data. The size of this set is linear in the dimension of the RFF space of the input set or alternatively near linear in the number of effective degrees of freedom, which is a function of the kernel, number of datapoints, and the regularization parameter** *λ***. The error bound of this distilled set is also a function of** *λ***. We verify our bounds analytically and empirically.**

## **1. Introduction**

Motivated by the growing data demands of modern deep learning, dataset distillation [\[ZB21,](#page-14-0) [NNXL21,](#page-13-0) [ZNB22,](#page-14-1) [WZTE18\]](#page-14-2) aims to summarize large datasets into significantly smaller synthetic *distilled* datasets, which when trained on retain high predictive accuracy, comparable to the original dataset. These distilled datasets have applications in continual learning [\[ZNB22,](#page-14-1) [SCCB22\]](#page-13-1), architecture search [\[SRL](#page-13-2)+19], and privacy preservation [\[CKF22\]](#page-11-0). Recent years have seen the development of numerous distillation algorithms, but despite this progress, the field has remained largely empirical. Specifically, there is little understanding of what makes one dataset "easier to distill" than another, or whether such small synthetic datasets even exist.

This work aims to fill this gap by providing the first bounds on the sufficient size and relative error associated with distilled datasets. Noting prior work relating neural network training to kernel ridge regression (KRR), we consider dataset distillation in the kernel ridge regression settings with shift-invariant kernels. By casting the problem into the Random Fourier Feature (RFF) space, we show that:

**The size and relative error of distilled datasets is governed by the kernel's "number of effective** degrees of freedom",  $d_k^{\lambda}$ . Specifically, in Section [4,](#page-3-0) we show that distilled sets of size  $\Omega(d_k^{\lambda} \log d_k^{\lambda})$ ,

<span id="page-0-0"></span><sup>\*</sup>Correspondence E-mail: <EMAIL>. §Equal contribution.

exist, with  $12\lambda + 2\mathcal{L}_\lambda$  predictive error on the training dataset, and only 8 $\lambda$  error with respect to the optimal solution computed on the full dataset, where *λ* is the kernel ridge regression regularization parameter and  $\mathcal{L}_{\lambda}$  the KRR training error on the original dataset; see Theorem [3](#page-4-0) and Remark [1](#page-7-0) for full details.

**These bounds hold in practice for both real and synthetic datasets**. In section [5,](#page-8-0) we validate our theorem by distilling synthetic and real datasets with varying sizes and values of  $d_k^{\lambda}$ , showing that in all scenarios our bounds accurately predict the error associated with distillation.

## **2. Related work**

**Coresets.** Coresets are weighted selections from a larger training dataset, which, when used for training, yield similar outcomes as if the whole dataset was used [\[MSSW18,](#page-12-0) [MBL20,](#page-12-1) [MJF19,](#page-12-2) [JMF19,](#page-11-1)  $MEM+22$  $MEM+22$ . The key benefit of using coresets is that they significantly speed up the training process, unlike when the full data set is used. Current methods for picking out coresets incorporate clustering techniques [\[FL11,](#page-11-2) [JTMF20,](#page-12-4) [LBK16,](#page-12-5) [BLHK16,](#page-11-3) [MJTF21\]](#page-12-6), bilevel optimization [\[BMK20\]](#page-11-4), sensitivity analysis [\[MSSW18,](#page-12-0) [HCB16,](#page-11-5) [TMF20,](#page-13-3) [MSF20,](#page-12-7) [TBFR21,](#page-13-4) [TWZ](#page-14-3)+22, [MTP](#page-13-5)+22], and surrogate models for approximation [\[TZM](#page-14-4)<sup>+</sup>23]. Newer strategies are specifically designed for neural networks, where before each training epoch, coresets are chosen such that their gradients align with the gradients of the entire dataset [\[MBL20,](#page-12-1) [PDM22,](#page-13-6) [TZM](#page-14-4)<sup>+</sup>23], followed by training the model on the chosen coreset. Although coresets are usually theoretically supported, these methods fall short when the aim is to compute a coreset once for a full training procedure.

**Dataset Distillation.** To this end, dataset distillation algorithms construct synthetic datasets (not necessarily a subset from the original input) such that gradient descent training on the synthetic datapoints results in high predictive accuracy on the real dataset. Cast as a bilevel optimization problem, early methods involve unrolling training computation graph [\[WZTE18\]](#page-14-2) for a few gradient descent steps and randomly sampled weight initializations. More sophisticated methods aim to approximate the unrolled computation using kernel methods [\[NCL21,](#page-13-7) [NNXL21,](#page-13-0) [ZNB22,](#page-14-1) [LHAR22a,](#page-12-8) [LHAR22b\]](#page-12-9), surrogate objectives such gradient matching [\[ZMB21,](#page-14-5) [ZB21\]](#page-14-0), trajectory matching [\[CWT](#page-11-6)<sup>+</sup>22] or implicit gradients [\[LHLR23\]](#page-12-10). The kernel-induced points (KIP) algorithm [\[NCL21,](#page-13-7) [NNXL21\]](#page-13-0) is a technique that employs Neural Tangent Kernel (NTK) theory[\[JGH18,](#page-11-7) [LHAR22b\]](#page-12-9) to formulate the ensuing loss:  $\mathcal{L}_{KIP} = \frac{1}{2} ||y_t - K_{TS} K_{SS}^{-1} y_S||_2^2$ . This loss signifies the predictive loss of training infinitely wide networks on distilled datapoints *X<sup>S</sup>* with corresponding labels *yS*, on the original training set and labels *XT*, *yT*, with *K<sub>ii</sub>*· being the NTK. Dataset distillation is closely related to the use of inducing points to accelerate Gaussian Processes [\[SG05,](#page-13-8) [TRB16\]](#page-14-6), for which convergence rates exist, but the existence of such inducing points is not unknown [\[BRVDW19\]](#page-11-8).

**From dataset distillation to kernel ridge regression.** Kernel ridge regression extends the linear machine learning ridge regression model by using a kernel function to map input data into higherdimensional feature spaces, allowing for more complex non-linear relationships between variables to be captured [\[Mur12\]](#page-13-9). Various methods have been proposed to improve and accelerate the training process of kernel ridge regression. Most notably, Random Fourier Features [\[RR07\]](#page-13-10) approximates shift-invariant kernel functions by mapping the input data into a lower-dimensional feature space using a randomized cosine transformation. This has been shown to work effectively in practice due to regularizing effects  $[JS<sup>+</sup>20]$ , as well as providing approximation bounds to the full kernel

ridge regression [\[SS15,](#page-13-11) [AKM](#page-11-9)<sup>+</sup>17, [LTOS19\]](#page-12-12). Training infinite-width neural networks can be cast as kernel ridge regression with the Neural Tangent Kernel (NTK) [\[JGH18\]](#page-11-7), which allows a closed-form solution of the infinite-width neural network's predictions, enabling kernel-based dataset distillation algorithms such as [\[NCL21,](#page-13-7) [NNXL21,](#page-13-0) [LHAR22a\]](#page-12-8).

**Goal.** We thus provide the first provable guarantees on the existence and approximation error of a small distilled dataset in the kernel ridge regression settings.

### **3. Background**

We first provide some notation that will be used throughout the paper.

**Notations.** In this paper we let  $\mathcal H$  be a Hilbert space with  $\|\cdot\|$  H as its norm. For a vector  $a\in\mathbb R^n$ , we use ∥*a*∥ to denote its Euclidean norm, and *a<sup>i</sup>* to denote its *i*th entry for every *i* ∈ [*n*]. For any positive integer *n*, we use the convention  $[n] = \{1, 2, \dots, n\}$ . Let  $A \in \mathbb{R}^{n \times m}$  be a matrix, then, for every  $i \in [n]$ and  $j \in [m]$ ,  $A_{i*}$  denotes the *i*th row of  $A$ ,  $A_{*j}$  denotes the *j*th column of  $A$ , and  $A_{i,j}$  is the *j*th entry of the *i*th row of *A*. Let  $B \in \mathbb{R}^{n \times n}$ , then we denote the trace of *B* by  $Tr(B)$ . We use  $I_m \in \mathbb{R}^{m \times m}$  to denote the identity matrix. Finally, vectors are addressed as column vectors unless stated otherwise.

#### **3.1. Kernel ridge regression**

Let  $X \in \mathbb{R}^{n \times d}$  be a matrix and let  $y \in \mathbb{R}^n$  be a vector. Let  $k : \mathbb{R}^d \times \mathbb{R}^d \to [0, \infty)$  be a kernel function, and let  $K \in \mathbb{R}^{n \times n}$  be its corresponding kernel matrix with respect to the rows of  $X$ ; i.e.,  $K_{i,j} = k(X_{i*}, X_{j*})$ for every  $i, j \in [n]$ . Let  $\lambda > 0$  be a regularization parameter. The goal of kernel ridge regression (KRR) involving  $X$ ,  $y$ ,  $k$ , and  $\lambda$  is to find

$$
\alpha_{[\mathbf{X},y,k]}^{\lambda} \in \underset{\alpha \in \mathbb{R}^n}{\arg \min} \frac{1}{n} \|y - \mathbf{K}\alpha\|^2 + \lambda \alpha^T \mathbf{K}\alpha. \tag{1}
$$

We use the notation  $f^{\lambda}_{[{\bf X},y,k]}\colon\mathbb{R}^d\to\mathbb{R}$  to denote the in-sample prediction by applying the KRR solution obtained on **X**, *y* and  $\lambda$  using the kernel *k*, i.e., for every  $x \in \mathbb{R}^d$ ,

$$
f_{\left[\mathbf{X},y,k\right]}^{\lambda}(x) = \sum_{i=1}^{n} \alpha_{\left[\mathbf{X},y,k\right]}^{\lambda} k\left(\mathbf{X}_{i*},x\right).
$$
 (2)

To provide our theoretical guarantees on the size and approximation error for the distilled datasets, the following assumption will be used in our theorem and proofs.

<span id="page-2-3"></span>**Assumption 1.** *We inherit the same theoretical assumptions used at [\[LTOS21\]](#page-12-13) for handling the KRR problem:*

- <span id="page-2-0"></span>*(I)* Let  $\mathcal F$  be the set of all functions mapping  $\mathbb R^d$  to  $\mathbb R$ *. Let*  $f^*\in\mathcal F$  *be the minimizer of*  $\sum^n_i f_i$ ∑  $\sum_{i=1}^{n} |y_i - f(\mathbf{X}_{i*})|^2$ , *subject to the constraint that for every*  $x \in \mathbb{R}^d$  *and*  $y \in \mathbb{R}$ ,  $y = f^*(x) + \epsilon$ , where  $\mathbb{E}(\epsilon) = 0$  and  $Var(\epsilon) = \sigma^2$ . Furthermore, we assume that y is bounded, i.e.,  $|y| \le y_0$ .
- <span id="page-2-1"></span>*(II) We assume that*  $|| f^{\lambda}_{[\mathbf{X},y,k]}$  $\big\|\mathcal{H}\leq 1.$
- <span id="page-2-2"></span>*(III)* For a kernel *k*, denote with  $\lambda_1 \geq \cdots \geq \lambda_n$  the eigenvalues of the kernel matrix **K**. We assume that the *regularization parameter satisfies*  $0 \le n\lambda \le \lambda_1$ *.*

**The logic behind our assumptions.** First, the idea behind Assumption [\(I\)](#page-2-0) is that the pair  $(X, y)$ can be linked through some function that can be from either the same family of kernels that we support (i.e., shift-invariant) or any other kernel function. In the context of neural networks, the intuition behind Assumption [\(I\)](#page-2-0) is that there exists a network from the desired architectures that gives a good approximation for the data. Assumption [\(II\)](#page-2-1) aims to simplify the bounds used throughout the paper as it is a pretty standard assumption, characteristic to the analysis of random Fourier features [\[LTOS19,](#page-12-12) [RR17\]](#page-13-12). Finally, Assumption [\(III\)](#page-2-2) is to prevent underfitting. Specifically speaking, the largest eigenvalue of  $K(K + n\lambda I_n)^{-1}$  is  $\frac{\lambda_1}{(\lambda_1+n\lambda)}$ . Thus, in the case of  $n\lambda > \lambda_1$ , the in-sample prediction is dominated by the term  $n\lambda$ . Throughout the following analysis, we will use the above assumptions. Hence, for the sake of clarity, we will not repeat them, unless problem-specific clarifications are required.

**Connection to Dataset distillation of neural networks.** Since the neural network kernel in the case of infinite width networks describes a Gaussian distribution [\[JGH18\]](#page-11-7), we aim at proving the existence of small sketches (distilled sets) for the input data with respect to the KRR problem with Gaussian kernel. However, the problem with this approach is that the feature space (in the Gaussian kernel corresponding mapping) is rather intangible or hard to map to, and sketch (distilled set) construction techniques require the representation of these points in the feature space.

To resolve this problem, we use a randomized approximated feature map, e.g., random Fourier features (RFF), and weighted random Fourier features (Weighted RFF). The dot product between every two mapped vectors in this approximated feature map aims to approximate their Gaussian kernel function [\[RR07\]](#page-13-10). We now restate a result connecting ridge regression in the RFF space (or alternatively weighted RFF), and KRR in the input space.

<span id="page-3-1"></span>**Theorem 2** (A result of the proof of Theorem 1 and Corollary 2 of [\[LTOS21\]](#page-12-13)). *Let*  $X \in \mathbb{R}^{n \times d}$  *be an*  $i$ nput matrix,  $y\in\mathbb{R}^n$  be an input label vector,  $k:\mathbb{R}^d\times\mathbb{R}^d\to[0,\infty)$  be a shift-invariant kernel function, and  $\mathbf{K} \in \mathbb{R}^{n \times n}$ , where  $\forall i,j \in [n]: \mathbf{K}_{i,j} = k(\mathbf{X}_{i*}, \mathbf{X}_{j*}).$  Let  $\lambda > 0$ , and let  $d_{\mathbf{K}}^{\lambda} = Tr\left(\mathbf{K}\left(\mathbf{K} + n\lambda\mathbf{I}_n\right)^{-1}\right)$ . Let  $s_\phi \in \Omega$   $(d_K^\lambda \log\left(d_K^\lambda\right))$  be a positive integer. Then, there exists a pair  $(\phi,\widetilde{\mathbf{X}})$  such that (i)  $\phi$  is a mapping *ϕ* : **R***<sup>d</sup>* → **R***s<sup>ϕ</sup> (which is based on either the weighted RFF function or the RFF function [\[LTOS21\]](#page-12-13)), (ii)* **<sup>X</sup>**<sup>e</sup> *is a matrix*  $\widetilde{\mathbf{X}} \in \mathbb{R}^{n \times s_{\phi}}$  *where for every*  $i \in [n]$ ,  $\widetilde{\mathbf{X}}_{i*} := \phi(\mathbf{X}_{i*})$ *, and (iii)*  $(\phi, \widetilde{\mathbf{X}})$  *satisfies* 

$$
\frac{1}{n}\sum_{i=1}^n\left|y_i-f_{\left[\widetilde{\mathbf{X}};y,\phi\right]}^{\lambda}\left(\widetilde{\mathbf{X}}_{i*}\right)\right|^2\leq \frac{1}{n}\sum_{i=1}^n\left|y_i-f_{\left[\mathbf{X};y,k\right]}^{\lambda}\left(\mathbf{X}_{i*}\right)\right|^2+4\lambda,
$$

where  $f^{\lambda}_{[\widetilde{\mathbf{X}},y,\phi]}:\mathbb{R}^{s_{\phi}} \to \mathbb{R}$  such that for every row vector  $z \in \mathbb{R}^{s_{\phi}},$   $f^{\lambda}_{[\widetilde{\mathbf{X}},y,\phi]}(z) = z\left(\widetilde{\mathbf{X}}^T\widetilde{\mathbf{X}} + \lambda ns_{\phi}\lambda\mathbf{I}_{s_{\phi}}\right)^{-1}\widetilde{\mathbf{X}}^T y$ . *Note that, Table [1](#page-10-0) gives bounds on s<sub>φ</sub> when*  $\lambda \propto \frac{1}{\sqrt{\lambda}}$ *n .*

<span id="page-3-0"></span>

## **4. Main result: on the existence of small distilled sets**

In what follows, we show that for any given matrix  $X \in \mathbb{R}^{n \times d}$  and a label vector  $y \in \mathbb{R}^n$ , there exists a matrix  $\mathbf{S}\in\mathbb{R}^{(s_\phi+1)\times d}$  and a label vector  $y_\mathbf{S}\in\mathbb{R}^{s_\phi+1}$  such that the fitting solution in the RFF space mapping of **S** is identical to that of the fitted solution on the RFF space mapping of **X**. With such **S** and *y***S**, we proceed to provide our main result showing that one can construct a solution for KRR in the original space of **S** which provably approximates the quality of the optimal KRR solution involving **X** and *y*. Thus, we obtain bounds on the minimal distilled set size required for computing a robust approximation, as well as bounds on the error for such a distilled set.

<span id="page-4-0"></span>**Theorem 3** (On the existence of some distilled data). Let  $X \in \mathbb{R}^{n \times d}$  be a matrix,  $y \in \mathbb{R}^n$  be a label vector,  $k: \mathbb{R}^d \times \mathbb{R}^d \to [0,\infty)$  *be a kernel function,*  $Y = (0,1) \cup \{2\}$ *, and let*  $s_{\phi}$  *be defined as in Theorem [2.](#page-3-1) Then,*  $t$ here exists a matrix  $\mathbf{S} \in \mathbb{R}^{\left( s_{\phi} + 1 \right) \times d}$  and a label vector  $y_{\mathbf{S}}$  such that

<span id="page-4-1"></span>*(i) the weighted RFF mapping*  $\widetilde{\mathbf{S}} \in \mathbb{R}^{(s_{\phi}+1) \times (s_{\phi})}$  *of* **S***, satisfies that* 

$$
\left(\widetilde{\mathbf{X}}^T\widetilde{\mathbf{X}}+\lambda ns_{\phi}\lambda\mathbf{I}_{s_{\phi}}\right)^{-1}\widetilde{\mathbf{X}}^T\mathbf{y}=\left(\widetilde{\mathbf{S}}^T\widetilde{\mathbf{S}}+\lambda ns_{\phi}\lambda\mathbf{I}_{s_{\phi}}\right)^{-1}\widetilde{\mathbf{S}}^T\mathbf{y}_{\mathbf{S}},
$$

*and*

<span id="page-4-2"></span>*(ii)* there exists an in-sample prediction  $f_{[S,y_S,k]}^{\lambda,\mathbf{X},y}$  (not necessarily the optimal on  $S$  and  $y_s$ ) satisfying

$$
\frac{1}{n}\sum_{i=1}^{n} \left| f_{\left[\mathbf{X},y,k\right]}^{\lambda}\left(\mathbf{X}_{i*}\right) - f_{\left[\mathbf{S},y_{\mathbf{S}},k\right]}^{\lambda,\mathbf{X},y}\left(\mathbf{X}_{i*}\right) \right|^{2} \leq \min_{\tau \in \mathcal{Y}} \left(2 \max\left\{\tau, \frac{4}{\tau^{2}}\right\} + \\ 2 \min\left\{1 + \tau, \frac{4\left(1 + \tau\right)}{3\tau}\right\}\right) \lambda,
$$
\n(3)

<span id="page-4-4"></span>*and*

$$
\frac{1}{n}\sum_{i=1}^{n}\left|y_{i}-f_{\left[\mathbf{S},y_{\mathbf{S}},k\right]}^{\lambda,\mathbf{X},y}\left(\mathbf{X}_{i\ast}\right)\right|^{2} \leq \min_{\tau\in\Upsilon} \frac{\min\left\{1+\tau,\frac{4(1+\tau)}{3\tau}\right\}}{n}\sum_{i=1}^{n}\left|y_{i}-f_{\left[\mathbf{X},y,k
ight]}^{\lambda}\left(\mathbf{X}_{i\ast}\right)\right|^{2} + \left(4\min\left\{1+\tau,\frac{4(1+\tau)}{3\tau}\right\} + 2\max\left\{\tau,\frac{4}{\tau^{2}}\right\}\right)\lambda.
$$
(4)

*Proof.* Let **S** be any matrix in  $\mathbb{R}^{(s_\phi+1)\times d}$  and let  $\widetilde{\mathbf{S}}$  be the weighted RFF mapping of **S**.

**Proof of [\(i\).](#page-4-1)** To ensure [\(i\),](#page-4-1) we need to find a corresponding proper  $y_s$ . We observe that

$$
\left(\widetilde{\mathbf{S}}^T\widetilde{\mathbf{S}} + \lambda ns_{\phi} \lambda \mathbf{I}_{s_{\phi}}\right) \left(\widetilde{\mathbf{X}}^T \widetilde{\mathbf{X}} + \lambda ns_{\phi} \lambda \mathbf{I}_{s_{\phi}}\right)^{-1} \widetilde{\mathbf{X}}^T \mathbf{y} = \widetilde{\mathbf{S}}^T \mathbf{y}_{\mathbf{S}}
$$

Let  $b=\left(\widetilde{\mathbf{S}}^T\widetilde{\mathbf{S}}+\lambda ns_\phi \lambda \mathbf{I}_{s_\phi}\right)\left(\widetilde{\mathbf{X}}^T\widetilde{\mathbf{X}}+\lambda ns_\phi \lambda \mathbf{I}_{s_\phi}\right)^{-1}\widetilde{\mathbf{X}}^T y$ , be the left-hand side term above.  $b$  is a vector of dimension  $s_{\phi}$ . Hence we need to solve  $b = \tilde{S}^T y_S$  for  $y_S$ . Since it is a linear system with  $s_{\phi} + 1$  variables and  $s_\phi$  equations, we get that the solution is  $y_\mathbf{S} = \left(\widetilde{\mathbf{S}}^T\right)^{\dagger}$   $b$ , where  $\left(\cdot\right)^{\dagger}$  denotes the pseudo-inverse of the given matrix.

<span id="page-4-3"></span>**Proof of [\(ii\).](#page-4-2)** Inspired by [\[LHAR22a\]](#page-12-8) and [\[NCL20\]](#page-13-13), the goal is to find a set of instances that their in-sample prediction with respect to the input data (**X** in our context) would lead to an approximation towards the solution that one would achieve if the KRR was used only with the input data. To that end, we introduce the following Lemm.

**Lemma 1** (Restatement of Lemma 6 [\[LTOS21\]](#page-12-13))**.** *Under Assumption [1](#page-2-3) and the definitions in Theorem [2,](#page-3-1) for every*  $f \in \mathcal{H}$  *with*  $||f|| \mathcal{H} \leq 1$ *, with constant probability, it holds that* 

$$
\inf_{\substack{\sqrt{s_{\phi}}\|\beta\|\leq \sqrt{2}\\ \beta\in \mathbb{R}^{s_{\phi}}}} \sum_{i=1}^{n} \frac{1}{n} \left| f\left(\mathbf{X}_{i*}\right)-\widetilde{\mathbf{X}}_{i*}\beta \right|^{2} \leq 2\lambda.
$$

Note that Lemma [1](#page-4-3) shows that for every in-sample prediction function with respect to **X**, there exists a query  $\beta \in \mathbb{R}^{s_{\phi}}$  in the RFF space of that input data such that the distance between the inprediction sample function in the input space and the in-sample prediction in the RFF space is at 2*λ*.

**Furthermore, at [\[LTOS21\]](#page-12-13) it was shown that** *β* **is defined as**  $β = \frac{1}{sφ} \widetilde{\mathbf{X}}^T \left( \widetilde{\mathbf{X}} \widetilde{\mathbf{X}}^T + n\lambda \mathbf{I}_{sφ} \right)^{-1} \mathbf{f}[\mathbf{X}]$ **, where**  $f[X]_i = f(X_{i*})$  for every  $i \in [n]$ .

We thus set out to find an in-sample prediction function that is defined over **S** such that by its infimum by Lemma [1](#page-4-3) would be the same solution  $β$  that the ridge regression on **X** attains with respect to the *y*. Specifically speaking, we want to find an in-sample prediction  $f_{\text{IS,}y_{\text{c}}}^{\lambda,\mathbf{X},y}$  $\begin{bmatrix} (S,y_S,k) \ (S,y_S,k) \end{bmatrix}$  ( $\cdot$ ) such that

<span id="page-5-0"></span>
$$
\beta = \frac{1}{s_{\phi}} \widetilde{\mathbf{X}}^T \left( \frac{1}{s_{\phi}} \widetilde{\mathbf{X}} \widetilde{\mathbf{X}}^T + n \lambda \mathbf{I}_{s_{\phi}} \right)^{-1} \mathbf{f}_{\mathbf{S}}[\mathbf{X}], \tag{5}
$$

.

where

(i) 
$$
f_S[X] \in \mathbb{R}^n
$$
 such that for every  $i \in [n]$ ,  $f_S[X]_i = f_{[S,y_S,k]}^{\lambda, X,y} (X_{i*})$ , and

<span id="page-5-1"></span>(ii) 
$$
f_{[\mathbf{S},y_{\mathbf{S}},k]}^{ \lambda,\mathbf{X},y} (\cdot) = \sum_{i=1}^{s_{\phi}+1} \alpha_{i} k\left(\mathbf{S}_{i\ast},\cdot\right)
$$
 such that  $\alpha \in \mathbb{R}^{s_{\phi}+1}$ 

Hence we need to find an in-sample prediction function  $f_{\text{IS,} \nu_{\text{c}}}^{\lambda, X, y}$  $[\mathbf{s}_{\mathcal{Y}\mathbf{s},k}]$  satisfying [5.](#page-5-0) Now, notice that  $\beta\in\mathbb{R}^{s_{\phi}},$   $\mathbf{f}_{\mathbf{S}}[\mathbf{X}]\in\mathbb{R}^{n}$  and  $\widetilde{\mathbf{X}}^{T}\left(\frac{1}{s_{\phi}}\widetilde{\mathbf{X}}\widetilde{\mathbf{X}}^{T}+n\lambda\mathbf{I}_{n}\right)^{-1}\in\mathbb{R}^{s_{\phi}\times n}.$  Due to the fact that we aim to find  $f_{[\mathbf{S},\mathbf{y}_{\mathbf{S}}]}^{\lambda,\mathbf{X},\mathbf{y}}$ [**S**,*y***S**,*k*] , such a task boils down to finding  $\alpha \in \mathbb{R}^{s_\phi+1}$  which defines  $f_{\text{IS},y_\phi}^{\lambda,\chi,y}$  $[\mathbf{s}_{\mathcal{Y}\mathbf{s},k}]$  as in [\(ii\).](#page-5-1) The above problem can be reduced to a system of linear equations where the number of equalities is  $s_{\phi}$ , while the number of variables is  $s_{\phi} + 1$ .

.

To do so, we denote  $\frac{1}{s_\phi}\widetilde{\mathbf{X}}^T\left(\frac{1}{s_\phi}\widetilde{\mathbf{X}}\widetilde{\mathbf{X}}^T+n\lambda\mathbf{I}_n\right)^{-1}$  by  $\hat{\mathbf{A}}$ , and observe that we aim to solve

$$
\beta = \hat{\mathbf{A}} f_{\mathbf{S}}^{\lambda}[\mathbf{X}] = \hat{\mathbf{A}} \begin{bmatrix} \sum_{i=1}^{s_{\phi}+1} \alpha_{i} k \left( \mathbf{S}_{i*}, \mathbf{X}_{1*} \right) \\ \sum_{i=1}^{s_{\phi}+1} \alpha_{i} k \left( \mathbf{S}_{i*}, \mathbf{X}_{2*} \right) \\ \vdots \\ \sum_{i=1}^{s_{\phi}+1} \alpha_{i} k \left( \mathbf{S}_{i*}, \mathbf{X}_{n*} \right) \end{bmatrix}
$$

We now show that every entry  $b_j$   $(j \in [s_\phi+1])$  in  $\beta$  can be rewritten as inner products between another pair of vectors in  $\mathbb{R}^{s_\phi+1}$  instead of the inner product between two vectors in  $\mathbb{R}^n$ . Formally, for every  $j \in [s_{\phi} + 1]$ , it holds that

$$
\beta_{j} = \hat{\mathbf{A}}_{j*} \begin{bmatrix} \sum\limits_{i=1}^{s_{\phi}+1} \alpha_{i}k\left(\mathbf{S}_{i*}, \mathbf{X}_{1*}\right) \\ \sum\limits_{i=1}^{s_{\phi}+1} \alpha_{i}k\left(\mathbf{S}_{i*}, \mathbf{X}_{2*}\right) \\ \vdots \\ \sum\limits_{i=1}^{s_{\phi}+1} \alpha_{i}k\left(\mathbf{S}_{i*}, \mathbf{X}_{n*}\right) \end{bmatrix} = \begin{bmatrix} \sum\limits_{t=1}^{n} \hat{\mathbf{A}}_{j,t}k\left(\mathbf{S}_{1*}, \mathbf{X}_{t*}\right) & \cdots & \sum\limits_{t=1}^{n} \hat{\mathbf{A}}_{j,t}k\left(\mathbf{S}_{(s_{\phi}+1)*}, \mathbf{X}_{t*}\right) \end{bmatrix} \begin{bmatrix} \alpha_{1} \\ \vdots \\ \alpha_{s_{\phi}+1} \end{bmatrix}.
$$

Thus, for every *j*  $\in$  [ $s_{\phi}$  + 1], define

$$
\mathbf{A}_{j*} = \left[ \sum_{t=1}^n \hat{\mathbf{A}}_{j,t} k\left(\mathbf{S}_{1*}, \mathbf{X}_{t*}\right), \cdots, \sum_{t=1}^n \hat{\mathbf{A}}_{j,t} k\left(\mathbf{S}_{\left(s_{\phi}+1\right)*}, \mathbf{X}_{t*}\right) \right] \in \mathbb{R}^{s_{\phi}+1}.
$$

The right-hand side of [\(5\)](#page-5-0) can reformulated as

$$
\frac{1}{s_{\phi}} \widetilde{\mathbf{X}}^T \left( \frac{1}{s_{\phi}} \widetilde{\mathbf{X}} \widetilde{\mathbf{X}}^T + n \lambda \mathbf{I}_n \right)^{-1} \mathbf{f}_{\mathbf{S}}[\mathbf{X}] = \mathbf{A} \alpha, \tag{6}
$$

where now we only need to solve  $\beta = A\alpha$ . Such a linear system of equations might have an infinite set of solutions due to the fact that we have  $s_{\phi}$  + 1 variables (the length of *α*) and exactly  $s_{\phi}$  equations. For simplicity, a solution to the above equality would be  $α := \left( {\bf A} \right)^{\dagger} \beta.$ 

To proceed in proving [\(ii\)](#page-4-2) with all of the above ingredients, we utilize the following tool.

<span id="page-6-0"></span>**Lemma 2** (Special case of Definition 6.1 from [\[BFL](#page-11-10)<sup>+</sup>16]). Let *X* be a set, and let  $(X, \| \cdot \|^{2})$  be a 2-metric  $\sup{z \to z}$  *space i.e., for every*  $x, y, z \in X$ *,*  $\|x - y\|^2 \leq 2\left(\|x - z\|^2 + \|y - z\|^2\right)$  *. Then, for every*  $\varepsilon \in (0,1)$ *, and*  $x, y, z \in X$ ,

<span id="page-6-1"></span>
$$
(1 - \varepsilon) \|y - z\|^2 - \frac{4}{\varepsilon^2} \|x - z\|^2 \le \|x - y\|^2 \le \frac{4}{\varepsilon^2} \|x - z\|^2 + (1 + \varepsilon) \|y - z\|^2. \tag{7}
$$

We note that Lemma [2](#page-6-0) implies that  $x, y, z \in \mathbb{R}^d$ 

<span id="page-6-2"></span>
$$
||x - y||^2 \le \min_{\tau \in Y} \max \left\{ \tau, \frac{4}{\tau^2} \right\} ||x - z||^2 + \min \left\{ 1 + \tau, \frac{4(1 + \tau)}{3\tau} \right\} ||y - z||^2. \tag{8}
$$

where for  $\tau = 2$  we get the inequality associated with the property of 2-metric, and for any  $\tau \in (0,1)$ , we obtain the inequality [\(7\)](#page-6-1).

We thus observe that

$$
\frac{1}{n} \sum_{i=1}^{n} \left| f_{\left[\mathbf{X},y,k\right]}^{\lambda} \left(\mathbf{X}_{i*}\right) - f_{\left[\mathbf{S},y_{\mathbf{S}},k\right]}^{\lambda,\mathbf{X},y} \left(\mathbf{X}_{i*}\right) \right|^{2}
$$

$$
= \frac{1}{n} \sum_{i=1}^{n} \left| f_{\left[\mathbf{X},y,k\right]}^{\lambda} \left(\mathbf{X}_{i*}\right) - f_{\left[\mathbf{X},y,\phi\right]}^{\lambda} \left(\mathbf{X}_{i*}\right) + f_{\left[\mathbf{X},y,\phi\right]}^{\lambda} \left(\mathbf{X}_{i*}\right) - f_{\left[\mathbf{S},y_{\mathbf{S}},k\right]}^{\lambda,\mathbf{X},y} \left(\mathbf{X}_{i*}\right) \right|^{2}
$$

$$
\leq \min_{\tau \in \mathcal{Y}} \frac{\max \left\{ \tau, \frac{4}{\tau^{2}} \right\}}{n} \sum_{i=1}^{n} \left| f_{\left[\mathbf{X},y,k\right]}^{\lambda} \left(\mathbf{X}_{i*}\right) - f_{\left[\mathbf{X},y,\phi\right]}^{\lambda} \left(\mathbf{X}_{i*}\right) \right|^{2} + \frac{\min \left\{ 1 + \tau, \frac{4(1+\tau)}{3\tau} \right\}}{n} \sum_{i=1}^{n} \left| f_{\left[\mathbf{X},y,\phi\right]}^{\lambda} \left(\mathbf{X}_{i*}\right) - f_{\left[\mathbf{S},y_{\mathbf{S}},k\right]}^{\lambda,\mathbf{X},y} \left(\mathbf{X}_{i*}\right) \right|^{2}
$$

$$
\leq \min_{\tau \in \mathcal{Y}} 2 \max \left\{ \tau, \frac{4}{\tau^{2}} \right\} \lambda + 2 \min \left\{ 1 + \tau, \frac{4(1+\tau)}{3\tau} \right\} \lambda
$$

$$
= \min_{\tau \in \mathcal{Y}} \left( 2 \max \left\{ \tau, \frac{4}{\tau^{2}} \right\} + 2 \min \left\{ 1 + \tau, \frac{4(1+\tau)}{3\tau} \right\} \right) \lambda,
$$

where the first equality holds by adding and subtracting the same term, the first inequality holds by Lemma [2,](#page-6-0) and the second inequality holds by combining the way  $f_{[S,y_S,k]}^{\lambda,X,y}$  was defined and Theorem [2.](#page-3-1)

Finally, to conclude the proof of Theorem [3,](#page-4-0) we derive [4](#page-4-4)

The following equation illustrates the Pythagorean theorem:

$$
\frac{1}{n}\sum_{i=1}^{n} \left| y_{i} - f_{\left[\mathbf{S},y_{\mathbf{S}},k\right]}^{\lambda,X,y} \left(\mathbf{X}_{i*}\right) \right|^{2} = \frac{1}{n}\sum_{i=1}^{n} \left| y_{i} - f_{\left[\tilde{\mathbf{X}}_{i}y_{i}\phi\right]}^{\lambda}\left(\tilde{\mathbf{X}}_{i*}\right) + f_{\left[\tilde{\mathbf{X}}_{i}y_{i}\phi\right]}^{\lambda}\left(\tilde{\mathbf{X}}_{i*}\right) - f_{\left[\mathbf{S},y_{\mathbf{S}},k\right]}^{\lambda,X,y} \left(\mathbf{X}_{i*}\right) \right|^{2}
$$

$$
\leq \min_{\tau \in \Upsilon} \frac{\min\left\{1 + \tau, \frac{4(1+\tau)}{3\tau}\right\}}{n} \sum_{i=1}^{n} \left| y_{i} - f_{\left[\tilde{\mathbf{X}}_{i}y_{i}\phi\right]}^{\lambda}\left(\tilde{\mathbf{X}}_{i*}\right) \right|^{2}
$$

$$
+ \frac{\max\left\{\tau, \frac{4}{\tau^{2}}\right\}}{n} \sum_{i=1}^{n} \left| f_{\left[\tilde{\mathbf{X}}_{i}y_{i}\phi\right]}^{\lambda}\left(\tilde{\mathbf{X}}_{i*}\right) - f_{\left[\mathbf{S},y_{\mathbf{S}},k\right]}^{\lambda,X,y} \left(\mathbf{X}_{i*}\right) \right|^{2}
$$

$$
\leq \min_{\tau \in \Upsilon} \frac{\min\left\{1 + \tau, \frac{4(1+\tau)}{3\tau}\right\}}{n} \sum_{i=1}^{n} \left| y_{i} - f_{\left[\tilde{\mathbf{X}}_{i}y_{i}\phi\right]}^{\lambda}\left(\tilde{\mathbf{X}}_{i*}\right) \right|^{2} + \left(4 \min\left\{1 + \tau, \frac{4(1+\tau)}{3\tau}\right\} + 2 \max\left\{\tau, \frac{4}{\tau^{2}}\right\}\right) \lambda,
$$
(9)

where the equality holds by adding and subtracting the same term, the first inequality holds by [\(8\)](#page-6-2), and the second inequality follows as a result of the way  $f_{\bf S}^\lambda$  was constructed and the fact that  $\beta$  is its infimum based on Lemma [1,](#page-4-3) and the last inequality holds by Theorem [2.](#page-3-1)

To simplify the bounds stated at Theorem [3,](#page-4-0) we provide the following remark.

<span id="page-7-0"></span>**Remark 1.** *By fixing*  $\tau := 2$ *, the bounds in Theorem [3](#page-4-0) become* 

$$
\frac{1}{n}\sum_{i=1}^n \left| f^{\lambda}_{[\mathbf{X},y,k]}(\mathbf{X}_{i*}) - f^{\lambda,\mathbf{X},y}_{[\mathbf{S},y_{\mathbf{S}},k]}(\mathbf{X}_{i*}) \right|^2 \leq 8\lambda,
$$

*and*

$$
\frac{1}{n}\sum_{i=1}^n\left|y_i-f_{\left[\mathbf{S},y_{\mathbf{S}},k\right]}^{\lambda,\mathbf{X},y}\left(\mathbf{X}_{i*}\right)\right|^2\leq\frac{2}{n}\sum_{i=1}^n\left|y_i-f_{\left[\mathbf{X},y,k\right]}^{\lambda}\left(\mathbf{X}_{i*}\right)\right|^2+12\lambda.
$$

*As for fixing*  $\tau := \varepsilon \in (0,1)$ *, we obtain that* 

$$
\frac{1}{n}\sum_{i=1}^n\left|y_i-f_{\left[\mathbf{S},y_{\mathbf{S}},k\right]}^{\lambda,\mathbf{X},y}\left(\mathbf{X}_{i*}\right)\right|^2\leq\frac{1+\varepsilon}{n}\sum_{i=1}^n\left|y_i-f_{\left[\mathbf{X},y,k\right]}^{\lambda}\left(\mathbf{X}_{i*}\right)\right|^2+\left(4\left(1+\varepsilon\right)+\frac{8}{\varepsilon^2}\right)\lambda.
$$

<span id="page-8-0"></span>

## **5. Experimental Study**

To validate our theoretical bounds, we performed distillation on three datasets: two synthetic datasets consisted of data generated from a Gaussian Random Field section [5.1,](#page-8-1) and classification of two clusters section [5.2,](#page-9-0) and one real dataset of MNIST binary 0 vs. 1 classification section [5.3.](#page-9-1) Full experimental details for all experiments are available in the appendix.

<span id="page-8-3"></span><span id="page-8-1"></span>

### **5.1. 2d Gaussian Random Fields**

Image /page/8/Figure/8 description: The image displays three plots labeled (a), (b), and (c). Plot (a) is a scatter plot titled "Two Clusters, n = 300, σx = 1.5". It shows two distinct clusters of data points, with points in the left cluster colored blue and points in the right cluster colored red. The x-axis ranges from -5 to 5, and the y-axis ranges from -4 to 4. Plot (b) is a heatmap titled "Training Data Predictive, fλx". The x-axis ranges from -20 to 20, and the y-axis ranges from -20 to 20. The heatmap displays a pattern of colors, with dark purple, teal, and yellow regions indicating varying predictive values. Plot (c) is also a heatmap titled "Distilled Data Predictive, fSλ". Its axes and range are identical to plot (b), and it shows a similar, though not identical, color pattern to plot (b).

**Fig. 1**: (a) visualizes the two clusters dataset in section [5.2](#page-9-0) with  $n = 300$  and  $\sigma_x = 1.5$ . (b) and (c) visualizing the KRR predictive functions generated by the original dataset (b) and the distilled dataset (c) for the Gaussian Random Field experiment in section [5.1](#page-8-1) for  $\sigma_x = 5.0$ . The distilled dataset is able to capture all the nuances of the original dataset with a fraction of the datapoints.

<span id="page-8-2"></span>Image /page/8/Figure/10 description: The image displays four plots related to a Gaussian Random Field. The title of the figure is "Gaussian Random Field". The x-axis for all plots is labeled "Cluster Stddev, \$\sigma\_x\$" and ranges from 0 to 5. The first plot, titled "\$d\_k^{\lambda}\$", shows a line graph with points increasing from approximately 0 at x=0 to 400 at x=5. The second plot, titled "Distilled Set Size, s", shows a line graph with points increasing from approximately 0 at x=0 to 2500 at x=5. The third plot, titled "Compression Ratio", shows a line graph with a logarithmic y-axis. The graph starts at approximately 200 at x=0, peaks at around 400 at x=0.5, and then decreases to approximately 20 at x=5. The fourth plot, titled "Upper Bound vs. Actual Loss", also has a logarithmic y-axis. It shows two lines: "Predicted Upper Bound" represented by dashed blue triangles, and "Distillation Loss" represented by solid blue circles. The "Predicted Upper Bound" line increases from approximately 10^-3 at x=0 to 10^-1 at x=5. The "Distillation Loss" line stays at approximately 10^-3 from x=0 to x=4, and then increases to approximately 2x10^-3 at x=5. A shaded region indicates uncertainty for the "Predicted Upper Bound" line. The caption below the plots reads "Fig. 2. Distillation results for synthetic data generated by Gaussian Random Field (\$\\sigma\_x=2\$)".

**Fig. 2**: Distillation results for synthetic data generated by a Gaussian Random Field (n = 3)

We first test our bounds by distilling data generated from the Gaussian Process prior induced by a kernel, *k* on 2d data. We use a squared exponential kernel with lengthscale parameter  $l = 1.5$ :

 $k(x, x') = e^{-\frac{||x - x'||_2^2}{2l^2}}$ . For **X**, we sample  $n = 10^5$  datapoints from  $\mathcal{N}(0, \sigma_x^2)$ , with  $\sigma_x \in [0.25, 5.0]$ . We then sample  $y \sim \mathcal{N}(0, K_{XX} + \sigma_y^2 I_n)$ ,  $\sigma_y = 0.01$ . We fix  $\lambda = 10^{-5}$  and distill down to  $s = d_k^{\lambda} \log d_k^{\lambda}$ . The resulting values of  $d_k^{\lambda}$ , *s*, and compression ratios are plotted in fig. [2](#page-8-2). We additionally plot the predicted upper bound given by Remark [1](#page-7-0) and the actual distillation loss. Our predicted upper bound accurately bounds the actual distillation loss. To better visualize how distillation affects the resulting KRR prediction, we show the KRR predictive function  $f_{\bf X}^{\lambda}$  and the distilled predictive  $f_{\bf S}^{\lambda}$  for  $\sigma_x = 5.0$ in fig. **1**[b](#page-8-3) and fig. **1**[c.](#page-8-3)

<span id="page-9-0"></span>

### **5.2. Two Gaussian Clusters Classification**

<span id="page-9-2"></span>Image /page/9/Figure/3 description: The image displays a figure titled "Two Clusters" with four subplots. Each subplot has the x-axis labeled "Cluster Stddev, \$\sigma\_x\$" ranging from 0 to 5. The first subplot, titled "\$d\_k^\lambda\$", shows a linear increase from approximately 0 to 400 as the cluster standard deviation increases from 0 to 5. The second subplot, titled "Distilled Set Size, s", shows a quadratic increase from approximately 0 to 2500 as the cluster standard deviation increases from 0 to 5. The third subplot, titled "Compression Ratio", shows a logarithmic decrease from approximately 1000 to 10 as the cluster standard deviation increases from 0 to 5. The fourth subplot, titled "Upper Bound vs. Actual Loss", shows two lines plotted on a logarithmic y-axis. The "Predicted Upper Bound" (dashed line with triangles) increases from approximately \$10^{-4}\$ to \$10^{-1}\$ and then plateaus. The "Distillation Loss" (solid line with circles) increases from approximately \$10^{-4}\$ to \$10^{-2}\$ and then plateaus at a lower value than the upper bound. A legend indicates that the dashed line with triangles represents the "Predicted Upper Bound" and the solid line with circles represents the "Distillation Loss".

**Fig. 3**: Distillation results for synthetic data of two Gaussian clusters  $(n = 3)$ 

Our second synthetic dataset is one consisting of two Gaussian clusters centered at  $(-2,0)$  and  $(2,0)$ , with labels  $-1$  and  $+1$ , respectively. Each cluster contains 5000 datapoints so that  $n = 10<sup>5</sup>$ . Each cluster as standard deviation  $\sigma_x \in [0.25, 5.0]$ . Additionally, two allow the dataset to be easily classified, we clip the *x* coordinates of clusters 1 and clusters 2 to not exceed/drop below −0.4 and 0.4, for the two clusters, respectively. This results in a margin between the two classes. We visualize the dataset for  $n = 300$  and  $\sigma = 1.5$  in fig. 1[a.](#page-8-3) We use the same squared exponential kernel as in section [5.1](#page-8-1) with  $l = 1.5$ , fix  $\lambda = 10^{-5}$  and distill with the same protocol as in section [5.1.](#page-8-1) We likewise plot  $d_k^{\lambda}$ , *s*, and compression ratios and distillation losses in fig. **[3](#page-9-2)**, again with our bound accurately containing the true distillation loss.

<span id="page-9-1"></span>

### **5.3. MNIST Binary Classification**

<span id="page-9-3"></span>Image /page/9/Figure/7 description: The image displays five line graphs illustrating the results of MNIST 0 vs. 1 classification. The x-axis for all graphs represents the Training Set Size, n, ranging from 2000 to 10000. The first graph, titled 'dk', shows a curve increasing from approximately 100 to 300. The second graph, titled 'Distilled Set Size, s', shows a curve increasing from about 500 to 1600, with the y-axis on a logarithmic scale from 10^0 to 6 x 10^0. The third graph, 'Compression Ratio', shows a curve increasing from roughly 1 x 10^-4 to 3 x 10^-4, with the y-axis on a logarithmic scale. The fourth graph, 'λ', shows a curve decreasing from about 3 x 10^-4 to 10^-5, with the y-axis on a logarithmic scale. The fifth graph, 'Upper Bound vs. Actual Loss', plots two lines: 'Predicted Upper Bound' (dashed green line with triangles) which stays relatively constant around 10^-1, and 'Distillation Loss' (solid green line with circles) which increases from approximately 10^-5 to 2 x 10^-4. The y-axis for this graph is also on a logarithmic scale from 10^-5 to 10^-1.

**Fig. 4**: Distillation results for MNIST binary 0 vs. 1 classification  $(n = 3)$ 

| <b>SAMPLING SCHEME</b> | <b>SPECTRUM</b>                        | NUMBER OF FEATURES                                |
|------------------------|----------------------------------------|---------------------------------------------------|
| <b>WEIGHTED RFF</b>    | finite rank                            | $s_{\phi} \in \Omega(1)$                          |
|                        | $\lambda_i \propto A^i$                | $s_{\phi} \in \Omega(\log n \cdot \log \log n)$   |
|                        | $\lambda_i \propto i^{-2t} (t \geq 1)$ | $s_{\phi} \in \Omega(n^{1/2t} \cdot \log n)$      |
|                        | $\lambda_i \propto i^{-1}$             | $s_{\phi} \in \Omega(\sqrt{n} \cdot \log n)$      |
| <b>PLAIN RFF</b>       | finite rank                            | $s_{\phi} \in \Omega(\sqrt{n})$                   |
|                        | $\lambda_i \propto A^i$                | $s_{\phi} \in \Omega(\sqrt{n} \cdot \log \log n)$ |
|                        | $\lambda_i \propto i^{-2t} (t \geq 1)$ | $s_{\phi} \in \Omega(\sqrt{n} \cdot \log n)$      |
|                        | $\lambda_i \propto i^{-1}$             | $s_{\phi} \in \Omega(\sqrt{n} \cdot \log n)$      |

<span id="page-10-0"></span>**Table 1**: Table 1 from [\[LTOS19\]](#page-12-12). The trade-off in the worst case for the squared error loss.

For our final dataset, we consider binary classification on MNIST 0 and 1 digits, with labels −1 and  $+1$ , respectively. We use the same squared-exponential kernel with  $l = 13.9$ , which was chosen to maximize the marginal-log-likelihood, treating the problem as Gaussian Process regression. We vary  $n \in [500, 10000]$ , with an equal class split, and perform the same distillation protocol as in section [5.1.](#page-8-1) Here, we additionally scale  $\lambda \propto \frac{1}{\sqrt{\lambda}}$  $\frac{1}{n}$  such that  $\lambda = 10^{-4}$  $\lambda = 10^{-4}$  $\lambda = 10^{-4}$  when  $n = 5000$ . Distilling yields fig. **4**, showing that our bounds can accurately predict distillation losses for real-world datasets.

## **6. Conclusion**

In this study, we adopt a theoretical perspective to provide bounds on the (sufficient) size and approximation error of distilled datasets. By leveraging the concept of random Fourier features (RFF), we prove the existence of small distilled datasets and we bound their corresponding excess risk when using shift-invariant kernels. Our findings indicate that the size of the guaranteed distilled data is a function of the "number of effective degrees of freedom," which relies on factors like the kernel, the number of points, and the chosen regularization parameter,  $\lambda$ , which also controls the excess risk.

In particular, we demonstrate the existence of a small subset of instances within the original input space, where the solution in the RFF space coincides with the solution found using the input data in the RFF space. Subsequently, we show that this distilled subset of instances can be utilized to generate a KRR solution that approximates the KRR solution obtained from the complete input data. To validate these findings, we conducted empirical examinations on both synthetic and real-world datasets supporting our claim.

While this study provides a vital first step in understanding the theoretical limitations of dataset distillation, the proposed bounds are not tight, as seen by the gap between the theoretical upper bound and the empirical distillation loss in section [5.](#page-8-0) Future work could look at closing this gap, as well as better understanding the tradeoff between distillation size and relative error.

## **acknowledgements**

This research has been funded in part by the Office of Naval Research Grant Number Grant N00014- 18-1-2830, DSTA Singapore, and the J. P. Morgan AI Research program.

## **References**

- <span id="page-11-9"></span>[AKM+17] Haim Avron, Michael Kapralov, Cameron Musco, Christopher Musco, Ameya Velingker, and Amir Zandieh. Random fourier features for kernel ridge regression: Approximation bounds and statistical guarantees. In *International conference on machine learning*, pages 253–262. PMLR, 2017.
  - [BFL+16] Vladimir Braverman, Dan Feldman, Harry Lang, Adiel Statman, and Samson Zhou. New frameworks for offline and streaming coreset constructions. *arXiv preprint arXiv:1612.00889*, 2016.
- <span id="page-11-10"></span><span id="page-11-3"></span>[BLHK16] Olivier Bachem, Mario Lucic, S. Hamed Hassani, and Andreas Krause. Approximate k-means++ in sublinear time. In Dale Schuurmans and Michael P. Wellman, editors, *Proceedings of the Thirtieth AAAI Conference on Artificial Intelligence, February 12-17, 2016, Phoenix, Arizona, USA*, pages 1459–1467. AAAI Press, 2016.
- <span id="page-11-4"></span>[BMK20] Zalán Borsos, Mojmir Mutny, and Andreas Krause. Coresets via bilevel optimization for continual learning and streaming. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, volume 33, pages 14879–14890, 2020.
- <span id="page-11-8"></span><span id="page-11-7"></span><span id="page-11-6"></span><span id="page-11-5"></span><span id="page-11-2"></span><span id="page-11-1"></span><span id="page-11-0"></span>[BRVDW19] David Burt, Carl Edward Rasmussen, and Mark Van Der Wilk. Rates of convergence for sparse variational Gaussian process regression. In Kamalika Chaudhuri and Ruslan Salakhutdinov, editors, *Proceedings of the 36th International Conference on Machine Learning*, volume 97 of *Proceedings of Machine Learning Research*, pages 862–871. PMLR, 09–15 Jun 2019.
  - [CKF22] Dingfan Chen, Raouf Kerkouche, and Mario Fritz. Private set generation with discriminative information. In *Neural Information Processing Systems (NeurIPS)*, 2022.
  - [CWT+22] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022.
    - [FL11] Dan Feldman and Michael Langberg. A unified framework for approximating and clustering data. In *Proceedings of the forty-third annual ACM symposium on Theory of computing*, pages 569–578, 2011.
    - [HCB16] Jonathan H. Huggins, Trevor Campbell, and Tamara Broderick. Coresets for scalable bayesian logistic regression. In Daniel D. Lee, Masashi Sugiyama, Ulrike von Luxburg, Isabelle Guyon, and Roman Garnett, editors, *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, pages 4080–4088, 2016.
    - [JGH18] Arthur Jacot, Franck Gabriel, and Clément Hongler. Neural tangent kernel: Convergence and generalization in neural networks. *Advances in neural information processing systems*, 31, 2018.
    - [JMF19] Ibrahim Jubran, Alaa Maalouf, and Dan Feldman. Introduction to coresets: Accurate coresets. *arXiv preprint arXiv:1910.08707*, 2019.

- <span id="page-12-11"></span>[JSS+20] Arthur Jacot, Berfin Simsek, Francesco Spadaro, Clément Hongler, and Franck Gabriel. Implicit regularization of random feature models. In *International Conference on Machine Learning*, pages 4631–4640. PMLR, 2020.
- <span id="page-12-4"></span>[JTMF20] Ibrahim Jubran, Murad Tukan, Alaa Maalouf, and Dan Feldman. Sets clustering. In *International Conference on Machine Learning*, pages 4994–5005. PMLR, 2020.
- <span id="page-12-5"></span>[LBK16] Mario Lucic, Olivier Bachem, and Andreas Krause. Strong coresets for hard and soft bregman clustering with applications to exponential family mixtures. In *Artificial intelligence and statistics*, pages 1–9. PMLR, 2016.
- <span id="page-12-8"></span>[LHAR22a] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022.
- <span id="page-12-12"></span><span id="page-12-10"></span><span id="page-12-9"></span>[LHAR22b] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Evolution of neural tangent kernels under benign and adversarial training. *arXiv preprint arXiv:2210.12030*, 2022.
  - [LHLR23] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients, 2023.
  - [LTOS19] Zhu Li, Jean-Francois Ton, Dino Oglic, and Dino Sejdinovic. Towards a unified analysis of random fourier features. In *International conference on machine learning*, pages 3905–3914. PMLR, 2019.
  - [LTOS21] Zhu Li, Jean-Francois Ton, Dino Oglic, and Dino Sejdinovic. Towards a unified analysis of random fourier features. *The Journal of Machine Learning Research*, 22(1):4887–4937, 2021.
  - [MBL20] Baharan Mirzasoleiman, Jeff A. Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In *Proceedings of the 37th International Conference on Machine Learning, ICML 2020, 13-18 July 2020, Virtual Event*, volume 119 of *Proceedings of Machine Learning Research*, pages 6950–6960. PMLR, 2020.
- <span id="page-12-13"></span><span id="page-12-6"></span><span id="page-12-3"></span><span id="page-12-2"></span><span id="page-12-1"></span>[MEM+22] Alaa Maalouf, Gilad Eini, Ben Mussay, Dan Feldman, and Margarita Osadchy. A unified approach to coreset learning. *IEEE Transactions on Neural Networks and Learning Systems*, 2022.
  - [MJF19] Alaa Maalouf, Ibrahim Jubran, and Dan Feldman. Fast and accurate least-mean-squares solvers. *Advances in Neural Information Processing Systems*, 32, 2019.
  - [MJTF21] Alaa Maalouf, Ibrahim Jubran, Murad Tukan, and Dan Feldman. Coresets for the average case error for finite query sets. *Sensors*, 21(19):6689, 2021.
  - [MSF20] Alaa Maalouf, Adiel Statman, and Dan Feldman. Tight sensitivity bounds for smaller coresets. In *Proceedings of the 26th ACM SIGKDD international conference on knowledge discovery & data mining*, pages 2051–2061, 2020.
- <span id="page-12-7"></span><span id="page-12-0"></span>[MSSW18] Alexander Munteanu, Chris Schwiegelshohn, Christian Sohler, and David Woodruff. On coresets for logistic regression. *Advances in Neural Information Processing Systems*, 31, 2018.

- <span id="page-13-13"></span><span id="page-13-9"></span><span id="page-13-5"></span>[MTP+22] Alaa Maalouf, Murad Tukan, Eric Price, Daniel M Kane, and Dan Feldman. Coresets for data discretization and sine wave fitting. In *International Conference on Artificial Intelligence and Statistics*, pages 10622–10639. PMLR, 2022.
  - [Mur12] Kevin P Murphy. *Machine learning: a probabilistic perspective*. MIT press, 2012.
  - [NCL20] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020.
  - [NCL21] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *International Conference on Learning Representations*, 2021.
- <span id="page-13-7"></span><span id="page-13-6"></span><span id="page-13-0"></span>[NNXL21] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *Thirty-Fifth Conference on Neural Information Processing Systems*, 2021.
  - [PDM22] Omead Pooladzandi, David Davini, and Baharan Mirzasoleiman. Adaptive second order coresets for data-efficient machine learning. In Kamalika Chaudhuri, Stefanie Jegelka, Le Song, Csaba Szepesvári, Gang Niu, and Sivan Sabato, editors, *International Conference on Machine Learning, ICML 2022, 17-23 July 2022, Baltimore, Maryland, USA*, volume 162 of *Proceedings of Machine Learning Research*, pages 17848–17869. PMLR, 2022.
    - [RR07] Ali Rahimi and Benjamin Recht. Random features for large-scale kernel machines. *Advances in neural information processing systems*, 20, 2007.
    - [RR17] Alessandro Rudi and Lorenzo Rosasco. Generalization properties of learning with random features. *Advances in neural information processing systems*, 30, 2017.
- <span id="page-13-12"></span><span id="page-13-10"></span><span id="page-13-8"></span><span id="page-13-1"></span>[SCCB22] Mattia Sangermano, Antonio Carta, Andrea Cossu, and Davide Bacciu. Sample condensation in online continual learning, 2022.
  - [SG05] Edward Snelson and Zoubin Ghahramani. Sparse gaussian processes using pseudoinputs. In Y. Weiss, B. Schölkopf, and J. Platt, editors, *Advances in Neural Information Processing Systems*, volume 18. MIT Press, 2005.
- <span id="page-13-2"></span>[SRL+19] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth O. Stanley, and Jeff Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. *CoRR*, abs/1912.07768, 2019.
  - [SS15] Danica J Sutherland and Jeff Schneider. On the error of random fourier features. *arXiv preprint arXiv:1506.02785*, 2015.
- <span id="page-13-11"></span><span id="page-13-4"></span>[TBFR21] Murad Tukan, Cenk Baykal, Dan Feldman, and Daniela Rus. On coresets for support vector machines. *Theoretical Computer Science*, 890:171–191, 2021.
- <span id="page-13-3"></span>[TMF20] Murad Tukan, Alaa Maalouf, and Dan Feldman. Coresets for near-convex functions. In Hugo Larochelle, Marc'Aurelio Ranzato, Raia Hadsell, Maria-Florina Balcan, and Hsuan-Tien Lin, editors, *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2020.

- <span id="page-14-6"></span>[TRB16] Dustin Tran, Rajesh Ranganath, and David M. Blei. The variational gaussian process, 2016.
- <span id="page-14-3"></span>[TWZ+22] Murad Tukan, Xuan Wu, Samson Zhou, Vladimir Braverman, and Dan Feldman. New coresets for projective clustering and applications. In *International Conference on Artificial Intelligence and Statistics*, pages 5391–5415. PMLR, 2022.
- <span id="page-14-4"></span>[TZM+23] Murad Tukan, Samson Zhou, Alaa Maalouf, Daniela Rus, Vladimir Braverman, and Dan Feldman. Provable data subset selection for efficient neural network training. *arXiv preprint arXiv:2303.05151*, 2023.
- <span id="page-14-5"></span><span id="page-14-2"></span><span id="page-14-1"></span><span id="page-14-0"></span>[WZTE18] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
  - [ZB21] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, 2021.
  - [ZMB21] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021.
  - [ZNB22] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022.

### **A. Experiment Details**

All experiments unless otherwise stated present the average/standard deviations of *n* = 3 runs. Each run consists of a random subset of MNIST 0/1 digits for MNIST binary classification, or random positions of sampled datapoints for synthetic data, and different samples from the GP for the Gaussian Random Field experiment. Distilled datasets are initialized as subsets of the original training data. We distill for 20000 iterations with Adam optimizer with a learning rate of 0.002 optimizing both images/data positions and labels. We use full batch gradient descent for the synthetic datasets and a maximum batch size of 2000 for the MNIST experiment. For the MNIST experiment we found that particularly for larger values of *n*, with minibatch training, we could obtain lower distillation losses by optimizing for longer, so the closing of the gap between the upper bound and experiment values in fig. **[4](#page-9-3)** may be misleading: longer optimization could bring the actual distillation loss lower.

To ensure that assumption [\(II\)](#page-2-1) is fulfilled, we scale the labels such that  $|| f_{[{\bf X},y,k]}^{\lambda}||$  $\left\| \mathcal{H} \right\| = 1$ . For example, if we are working with MNIST binary classification, with labels  $\{+1, -1\}$ , we first compute  $\left\| f_{\left[\mathbf{X},y,k\right]}^{\lambda}\right\|$  $\|\mathcal{H} = r$  using  $\{+1, -1\}$  labels, then rescale the labels by  $1/r$  so that the labels are  $\{+\frac{1}{r}, -\frac{1}{r}\}$ . Suppose this results in some upper bound  $\mathcal{L}_U$  and some real distillation loss  $\mathcal{L}_R.$  For the corresponding plots in figs. **[2](#page-8-2)** to **[4](#page-9-3)**, we plot  $r^2\mathcal{L}_U$  and  $r^2\mathcal{L}_R$ . We do this because the  $r$  values for different parameters (such as *n* or  $\sigma_x$ ) could be different, and scaling for the plots allows the values to be comparable.

In the figures for the upper bounds on the distillation loss we plot the smallest value of the upper bounds in remark [1.](#page-7-0)
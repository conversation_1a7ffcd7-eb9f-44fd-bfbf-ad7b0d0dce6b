# Graph Data Condensation via Self-expressive Graph Structure Reconstruction

Z<PERSON><PERSON><sup>∗</sup> Shanghai Jiao Tong University Shanghai, China <EMAIL>

Chaolv Zeng<sup>∗</sup> Shanghai Jiao Tong University Shanghai, China <EMAIL>

<PERSON><PERSON><PERSON><PERSON>† Shanghai Jiao Tong University Shanghai, China <EMAIL>

## Abstract

With the increasing demands of training graph neural networks (GNNs) on large-scale graphs, graph data condensation has emerged as a critical technique to relieve the storage and time costs during the training phase. It aims to condense the original large-scale graph to a much smaller synthetic graph while preserving the essential information necessary for efficiently training a downstream GNN. However, existing methods concentrate either on optimizing node features exclusively or endeavor to independently learn node features and the graph structure generator. They could not explicitly leverage the information of the original graph structure and failed to construct an interpretable graph structure for the synthetic dataset. To address these issues, we introduce a novel framework named Graph Data Condensation via Self-expressive Graph Structure Reconstruction (GCSR). Our method stands out by (1) explicitly incorporating the original graph structure into the condensing process and (2) capturing the nuanced interdependencies between the condensed nodes by reconstructing an interpretable self-expressive graph structure. Extensive experiments and comprehensive analysis validate the efficacy of the proposed method across diverse GNN models and datasets. Our code is available at [https://github.com/zclzcl0223/GCSR.](https://github.com/zclzcl0223/GCSR)

## CCS Concepts

• Information systems  $\rightarrow$  Data mining.

## Keywords

Graph Data Condensation; Graph Neural Network; Node Classification

#### ACM Reference Format:

Zhanyu Liu, Chaolv Zeng, and Guanjie Zheng. 2024. Graph Data Condensation via Self-expressive Graph Structure Reconstruction. In Proceedings of the 30th ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD '24), August 25–29, 2024, Barcelona, Spain. ACM, New York, NY, USA, [11](#page-10-0) pages.<https://doi.org/10.1145/3637528.3671710>

KDD '24, August 25–29, 2024, Barcelona, Spain

© 2024 Copyright held by the owner/author(s). Publication rights licensed to ACM. ACM ISBN 979-8-4007-0490-1/24/08

<https://doi.org/10.1145/3637528.3671710>

## <span id="page-0-0"></span>1 Introduction

Image /page/0/Figure/18 description: The image displays a figure titled "Self-expressive Graph Structure Induction". Below the title, author information is presented, including names, affiliations, and email addresses. The figure is divided into sections labeled "1 Introduction" and a series of diagrams illustrating different graph condensation methods. These methods are labeled (a) GCond, (b) SFGC, (c) SGDD, and (d) GCSR, each showing a pipeline involving "Objective Matching" and "Update" steps, with variations in components like MLP, Graphon Approximation, and SER. Additionally, section (e) illustrates an "SER block" with a diagram showing weighted connections and a regularization term. The figure is captioned as "Figure 1: Learning pipelines of different graph condensation methods. X, A denotes the original full dataset and X', A' de-".

Figure 1: Learning pipelines of different graph condensation methods. X, A denotes the original full dataset and X', A' denotes condensed dataset. SER is our proposed self-expressive graph structure reconstruction module and Reg is the regularization term for reconstruction.

In recent years, the significant increase in the volume of graph data in different fields, such as social networks [\[29,](#page-8-0) [49\]](#page-9-0), recommendation systems [\[11,](#page-8-1) [65\]](#page-9-1), traffic networks [\[34,](#page-8-2) [40–](#page-8-3)[42\]](#page-9-2), and knowledge graphs [\[62,](#page-9-3) [74\]](#page-9-4), has presented challenges for practitioners and researchers. Consequently, storing, processing, analyzing, and transmitting large-scale data has become a burden in real-world applications. Moreover, when considering deep learning tasks such as neural architecture search [\[39,](#page-8-4) [55\]](#page-9-5) and continual learning [\[20,](#page-8-5) [33\]](#page-8-6), directly utilizing the full dataset for training a graph neural network (GNN) has the potential to result in poor efficiency.

To address this issue, data-centric graph size reduction methods, such as graph coarsening [\[2,](#page-8-7) [25\]](#page-8-8), graph sparsification [\[1,](#page-8-9) [4\]](#page-8-10), coreset selection [\[33,](#page-8-6) [64\]](#page-9-6), and graph sampling [\[5,](#page-8-11) [73\]](#page-9-7), aim to reduce the redundancy of the graph data and output a small dataset. However, these methods rely on heuristics such as eigenvalues [\[1\]](#page-8-9) and the accuracy loss [\[33\]](#page-8-6), which could lead to unsatisfactory generalization between different network architectures and suboptimal performance on downstream tasks such as node classification [\[23,](#page-8-12) [71\]](#page-9-8). In recent studies [\[23,](#page-8-12) [24,](#page-8-13) [70,](#page-9-9) [71,](#page-9-8) [80\]](#page-9-10), the concept of Graph Data Condensation has emerged, intending to train a compact synthetic dataset that exhibits comparable performance to the full dataset when utilized to train the same GNN model. This approach achieves good results by matching surrogate objectives between the synthetic dataset and the original dataset.

However, existing graph data condensation methods fail to effectively and efficiently preserve the valuable information embedded within the original graph structure and capture the inter-node correlations within the condensed dataset. Fig. [1](#page-0-0) illustrates the learning pipeline of various graph data condensation methods focused on node classification including GCond [\[24\]](#page-8-13), SGDD [\[71\]](#page-9-8), SFGC [\[80\]](#page-9-10),

<sup>∗</sup>Both authors contributed equally to this work. †Corresponding Author

Permission to make digital or hard copies of all or part of this work for personal or classroom use is granted without fee provided that copies are not made or distributed for profit or commercial advantage and that copies bear this notice and the full citation on the first page. Copyrights for components of this work owned by others than the author(s) must be honored. Abstracting with credit is permitted. To copy otherwise, or republish, to post on servers or to redistribute to lists, requires prior specific permission and/or a fee. Request <NAME_EMAIL>.

and our proposed GCSR. GCond aims to model the graph structure by utilizing node features as input to a multilayer perceptron (MLP), while SFGC proposes a graph-free framework. These two methods cannot build explicit and interpretable inter-node correlations, and ignore the rich information contained in the original graph structure. SGDD employs the original graph structure to guide the generation of the synthetic graph structure through graphon approximation, but the application of bi-loop optimization may result in unstable performance and unsatisfactory efficiency.

To effectively and efficiently construct an explicit and interpretable graph structure for the synthetic condensed data, and integrate the information from the original graph structure, we propose a novel framework named Graph Data Condensation via Self-expressive Graph Structure Reconstruction (GCSR). Overall, GCSR contains three novel modules. The first module is the Initialization Module, where we utilize the  $k$ -order node feature for node initialization and the probabilistic adjacency matrix derived from the original graph structure to initialize the regularization term used in the subsequent reconstruction process. The second module is the Self-expressive Reconstruction Module. Leveraging the self-expressiveness property of graph data [\[26,](#page-8-14) [68\]](#page-9-11), which indicates that nodes within the same feature subspace can represent each other, we reconstruct an explicit and interpretable graph structure using a closed-form expression. The last module is the Update Module. In this module, the node feature is updated through multi-step gradient matching with the training trajectories of the full dataset. The regularization term of the reconstruction is updated through bootstrapping, ensuring adaptability to the training dynamics and preventing overfitting of the learned graph structure to the small condensed dataset. In general, our contributions can be summarized as follows:

- To the best of our knowledge, we are the first to attempt to construct the explicit and interpretable graph structure for the synthetic condensed data in the graph condensation task. The graph structure is constructed based on the self-expressive nature of graph data and effectively integrates the extensive information present in the original full graph.
- We propose a novel framework for the graph condensation task via self-expressive graph structure reconstruction, abbreviated as GCSR. This framework encompasses three key modules, including initialization, self-expressive reconstruction, and update. These modules work in tandem to generate condensed data that effectively captures the essence of the original graph dataset.
- We conduct extensive experiments on five real-world graph datasets. The results demonstrate that our proposed framework achieves superior performance and has many characteristics such as maintaining the inter-class similarity of the original graph.

## 2 Related Work

Graph Neural Networks. Taking both node features and their structure information as the input, graph neural networks (GNNs) [\[7,](#page-8-15) [10,](#page-8-16) [16,](#page-8-17) [19,](#page-8-18) [28,](#page-8-19) [60,](#page-9-12) [66,](#page-9-13) [69,](#page-9-14) [72\]](#page-9-15) have emerged as powerful tools in graph machine learning. The versatility of graph-structured data and the strong graph representation capabilities of GNNs have led to their

widespread adoption in a range of real-world applications, including recommender systems [\[13\]](#page-8-20), natural language processing [\[67\]](#page-9-16), and computer vision [\[50\]](#page-9-17).

Dataset Condensation. Dataset condensation (DC), also known as dataset distillation [\[3,](#page-8-21) [37,](#page-8-22) [52,](#page-9-18) [57,](#page-9-19) [63,](#page-9-20) [77–](#page-9-21)[79\]](#page-9-22), aims to generate a smaller synthetic dataset from a large training dataset, such that it can effectively substitute the original dataset for downstream training tasks. To obtain such a synthetic dataset, various works employ different optimization objectives. Some methods [\[8,](#page-8-23) [37,](#page-8-22) [43,](#page-9-23) [51,](#page-9-24) [52,](#page-9-18) [63,](#page-9-20) [81\]](#page-9-25) learn the synthetic dataset by using a meta-learning style framework to optimize the performance on the real full data. Some methods [\[3,](#page-8-21) [6,](#page-8-24) [9,](#page-8-25) [27,](#page-8-26) [31,](#page-8-27) [32,](#page-8-28) [75,](#page-9-26) [77,](#page-9-21) [79\]](#page-9-22) optimize the synthetic dataset by matching the gradients of the neural networks trained on the synthetic dataset and the original dataset respectively. Some methods [\[30,](#page-8-29) [57,](#page-9-19) [61,](#page-9-27) [78\]](#page-9-28) optimize the synthetic dataset by matching the distribution between the synthetic dataset and the original dataset. While initially applied to image data, DC has recently found wide application in graph-structured data for both node-level tasks [\[24,](#page-8-13) [71,](#page-9-8) [80\]](#page-9-10) and graph-level tasks [\[23,](#page-8-12) [70\]](#page-9-9). GCond [\[24\]](#page-8-13) first tries to condense graph data via gradient matching and generate the adjacency matrix from the synthetic node features using a trained multilayer perception. SGDD [\[71\]](#page-9-8) enhances GCond by broadcasting the original graph structure to the generation of the synthetic graph structure through laplacian energy distribution matching. SFGC [\[80\]](#page-9-10) proposes to generate structure-free graphs by matching training trajectories and selecting optimal synthetic graphs using graph neural tangent kernel (GNTK) [\[10\]](#page-8-16). However, these methods could not effectively and efficiently capture an explicit and interpretable graph structure for the condensed graph and neglect the explicit information of the original graph structure, which results in suboptimal performance.

Self-expressiveness. The self-expressiveness property indicates that each data sample can be represented by a linear combination of other data points [\[45,](#page-9-29) [48\]](#page-9-30). Such a property is first explored in subspace segmentation [\[44\]](#page-9-31), which aims to segment data drawn from multiple linear subspaces as data in the same subspace could represent each other. Later, researchers begin to apply this property to graph learning [\[54,](#page-9-32) [68\]](#page-9-11), aiming to learn a denoised and interpretable graph structure for downstream tasks such as graph clustering [\[26,](#page-8-14) [48\]](#page-9-30). They add different regularizations and constraints to ensure that the learned graph structure can fully reflect the connection relationships between nodes.

## 3 Preliminary

**Graph Condensation.** A graph dataset  $\mathcal{T}$  is denoted as  $\mathcal{T}$  =  ${A, \dot{X}, Y}$ , where  $A \in \mathbb{R}^{N \times N}$  is the adjacency matrix,  $X \in \mathbb{R}^{N \times d}$ is the node feature, and  $Y \in \mathbb{R}^{N \times 1}$  is the label. Generally, graph data condensation aims to learn a downsized synthetic graph  $\vec{S}$  =  $\{A', X', Y'\}$  with  $A' \in \mathbb{R}^{N' \times N'}$ ,  $X' \in \mathbb{R}^{N' \times d}$ , and  $Y' \in \mathbb{R}^{N' \times 1}$  from the original training graph  $\mathcal{T} (N' \ll N)$ . Both  $\mathbf{Y}_i$  and  $\mathbf{Y}'_j$  belong to the class set  $C = \{0, 1, \dots, C - 1\}$ . Formally, graph condensation is defined by solving the problem as follows:

$$
\min \mathcal{L}(\text{GNN}_{\theta_{\mathcal{S}}}(A, X), Y)
$$
  
s.t.  $\theta_{\mathcal{S}} = \underset{\theta}{\arg \min} \mathcal{L}(\text{GNN}_{\theta}(A', X'), Y')$ . (1)

Here, GNN $_{\theta}$  represents the GNN model parameterized with  $\theta$ ,  $\theta_{\mathcal{S}}$ denotes the parameters of the model trained on the synthetic graph  $\mathcal{S} = \{\mathbf{A}', \mathbf{X}', \mathbf{Y}'\}$ , and  $\mathcal{L}$  is the error of node classification. For the graph data condensation task, only the variables  $A'$  and  $X'$  are optimized, while the node labels  $Y'$  are fixed, maintaining the same class distribution as the original labels Y.

## 4 Method

In this section, we present our proposed framework GCSR, which consists of three essential modules. The diagram of the framework is depicted in Fig. [2,](#page-3-0) and the detailed pseudo code is shown in Alg. [1.](#page-3-1) The first module, termed the Initialization Module, initializes the node feature and graph regularizer for the downstream synthetic dataset. Then, the Self-expressive Reconstruction Module exploits the inherent self-expressive property of the graph data and employs a closed-form solution to derive an interpretable graph structure that captures the self-representation relationships among the nodes. Finally, the Update Module uses the derived adjacency matrix to update the graph regularizer in a bootstrapping style and the multistep gradient matching loss to update the node feature respectively.

### 4.1 Initialization

#### 4.1.1 Node Initialization.

Goal: The initial feature of the condensed dataset is crucial to the final performance. Previous methods utilize random noise [\[8,](#page-8-23) [61\]](#page-9-27) or random samples from the original dataset [\[3,](#page-8-21) [24,](#page-8-13) [71,](#page-9-8) [78\]](#page-9-28). However, these methods overlook the valuable graph structure information during the node feature initialization stage. In fact, in the field of graph learning, it has been observed that node features that incorporate message passing exhibit higher expressiveness and yield better performance in downstream tasks, such as node classification, due to the fusion of graph structure information [\[66,](#page-9-13) [72\]](#page-9-15). Building upon this insight, we propose an approach to initialize the node feature after the message passing process. By doing so, we ensure that the framework is initialized with better node features, leading to enhanced performance in subsequent tasks.

Message Passing Initialization: Given the original graph dataset  $\mathcal{T} = \{A, X, Y\}$ , we first normalize it following previous work [\[26,](#page-8-14) [28,](#page-8-19) [48\]](#page-9-30). The symmetrically normalized adjacency matrix can be defined as:

<span id="page-2-0"></span>
$$
\hat{\mathbf{A}} = \tilde{\mathbf{D}}^{-\frac{1}{2}} \tilde{\mathbf{A}} \tilde{\mathbf{D}}^{-\frac{1}{2}},\tag{2}
$$

where  $\mathbf{A} = \mathbf{A} + \mathbf{I}$  is the adjacency matrix with self-connections added,  $\tilde{\mathbf{D}}_{ii} = \sum_{j} \tilde{\mathbf{A}}_{ij}$  is the degree matrix, and **I** is the identity matrix. Under the framework of message passing mechanisms [\[17,](#page-8-30) [48\]](#page-9-30), each node of  $\mathcal T$  aggregates the information from its k-order neighborhood:

<span id="page-2-1"></span>
$$
\hat{\mathbf{X}} = \hat{\mathbf{A}}^k \mathbf{X},\tag{3}
$$

where  $\hat{\textbf{A}}^k$  indicates the *k*-order adjacency matrix.

After Eq. [2](#page-2-0) and Eq. [3,](#page-2-1) we obtain a node representation  $\hat{\mathbf{X}}$  that aggregates neighborhood features as well as graph structure information. We then initialize the synthetic nodes  $X'$  by randomly sampling the node feature from  $\hat{\mathbf{X}}$ ,

<span id="page-2-2"></span>
$$
\mathbf{X}' = Random\_Sample(\hat{\mathbf{X}}).
$$
 (4)

Moreover, to make the synthetic dataset conform to the distribution of the original dataset and avoid creating an unbalanced dataset, we keep the class distribution the same as the original graph. Formally, the following equation holds for every node class, i.e.,  $c \in \{0, 1, \cdots, C-1\}.$ 

$$
\sum_{i=1}^{N'} \frac{\mathbb{1}(Y'_i = c)}{N'} = \sum_{k=1}^{N} \frac{\mathbb{1}(Y_k = c)}{N}.
$$
 (5)

Here,  $\mathbf{Y}'_i$  and  $\mathbf{Y}_k$  are the class of node in the synthetic dataset and original dataset respectively, and  $\mathbbm{1}$  is the indicator function.

#### 4.1.2 Regularizer Initialization.

Goal: The downstream self-expressive reconstruction task needs a graph structure regularizer to avoid the solution falling into a trivial solution [\[26,](#page-8-14) [68\]](#page-9-11). So, we aim to initialize a graph structure regularizer by incorporating the information of the original graph structure. However, considering that the dimensions of adjacency matrices of the synthetic graph and the original graph are inconsistent, it is difficult to directly transfer the structure information of the original graph to the synthetic graph. Consequently, we propose to generate a probabilistic adjacency matrix [\[15,](#page-8-31) [23\]](#page-8-12) based on the information from the original adjacency matrix as the initial graph regularizer. To guarantee the stability of the training process, we replace the learnable parameter and use the frequency of edge types as the probability instead, which reflects the correlation between classes.

Graph Regularizer Initialization: Given the original adjacency matrix  $A_{ij}$ , we aim to first calculate the frequency of edge types and construct a class-wise correlation matrix as follows:

<span id="page-2-3"></span>
$$
\overline{\mathbf{A}}_{cc'} = \frac{\sum_{i,j=1}^{N} \mathbb{1}(\mathbf{Y}_{i} = c) \mathbb{1}(\mathbf{Y}_{j} = c') \mathbf{A}_{ij}}{\sum_{i,j=1}^{N} \mathbb{1}(\mathbf{Y}_{i} = c) \mathbf{A}_{ij}},
$$
\n(6)

where  $\overline{A} \in \mathbb{R}^{|C| \times |C|}$  represents the adjacency matrix generated from the original graph,  $Y_i$  is the class of node *i* of the original dataset, and 1 is the indicator function. The value of  $\overline{A}_{cc'}$  implies the frequency that an edge from a node in class  $c$  to a node in class  $c'$  exists. With  $\overline{\mathbf{A}}$ , we can generate an adjacency matrix that leverages the structure information of the original graph:

<span id="page-2-4"></span>
$$
\mathbf{P}_{ij} = \overline{\mathbf{A}}_{\mathbf{Y}_i' \mathbf{Y}_j'},\tag{7}
$$

where  $\mathbf{P} \in \mathbb{R}^{N' \times N'}$  and  $\mathbf{Y}'_i$  denotes the label of synthetic node . Consequently, P captures the inter-class correlations and the correlations offer valuable information for the downstream selfexpressive reconstruction. By utilizing P as the graph structure regularization term, the framework could avoid generating trivial solutions in the optimization process.

#### 4.2 Self-expressive Reconstruction

4.2.1 Reconstruction. In recent years, it has been witnessed the great success of graph structure learning [\[26,](#page-8-14) [48,](#page-9-30) [54\]](#page-9-32) via self-expressive graph structure reconstruction. Such a strategy exploits the selfexpressiveness property of graph nodes to reconstruct the adjacency matrix in structure-free graphs. Mathematically, it can be formulated as the following expression:

$$
\min_{\mathbf{Z}} ||\mathbf{X}'^T - \mathbf{X}'^T \mathbf{Z}||_F^2.
$$
 (8)

<span id="page-3-0"></span>Image /page/3/Figure/2 description: The figure illustrates the overview of Graph Data Condensation via Self-expressive Graph Structure Reconstruction (GCSR). It is divided into four main modules: Initialization Module, Self-expressive Reconstruction Module, and Update Module, with a Bootstrapping Update connecting the latter two. The Initialization Module includes Regularizer Initialization, showing graph structures with 2 and 5 edges and an edge weight of 2/5=0.4, and Node Initialization, which involves normalizing the adjacency matrix A to get A-hat and then computing X-hat as A-hat times X. The Self-expressive Reconstruction Module takes the initialized X and A, along with a regularization term involving Z and P, and a closed-form solution, to compute Z. The Update Module involves symmetrizing Z to get A-prime, and then defining T as (X, A, Y) and S as (X', A', Y'). It then proceeds with M-Step Training and N-Step Training, followed by Gradient Matching with a loss function L. The Bootstrapping Update and Back-propagation Update are indicated by red arrows, showing the flow of information and updates between modules. The figure also displays mathematical formulas and notations related to the process.

Figure 2: Overview of Graph Data Condensation via Self-expressive Graph Structure Reconstruction (GCSR).

Algorithm 1: GCSR for Graph Data Condensation

<span id="page-3-1"></span>**Input:**  $\mathcal{T} = \{A, X, Y\}$ : training data

Input: Y ′ : pre-defined synthetic labels

**Input:**  $\{\theta\}$ : set of expert GNN parameters trained on  $\mathcal T$ 

- **Require:** Training epochs  $K$ , synthetic steps  $N$ , expert steps  $M$ , learning rate  $\eta_1$ ,  $\eta_2$ , regularization weight  $\alpha$ ,  $\beta$ , update rate  $\tau$ ,  $\mathbf{v}$ 
  - 1: Initialize  $X'$  according to Eq. [3](#page-2-1) and Eq. [4](#page-2-2)
- 2: Initialize P according to Eq. [6](#page-2-3) and Eq. [7](#page-2-4)
- 3: Initialize  $\mathbf{Z}_h$  as identity matrix **I**
- 4: for  $k = 1 \rightarrow K$  do
- 5: Sample expert GNN parameters:  $\theta_t \sim P_\theta$ ;
- 6: Initialize synthetic GNN parameters:  $\widehat{\theta}_t = \theta_t$ ;
- 7: Compute A′ according to Eq. [13](#page-3-2) and Eq. [14;](#page-4-0)
- 8: for  $i = 1 \rightarrow N$  do
- 9: Update synthetic GNN w.r.t. classification loss:  $\widehat{\theta}_{t+i} = \widehat{\theta}_{t+i-1} - \eta_1 \nabla l(S, \widehat{\theta}_{t+i-1});$
- 10: end for
- 11: Compute loss between synthetic and expert params:  $\mathcal{L} = ||\widehat{\theta}_{t+N} - \theta_{t+M}||_F^2/||\theta_{t+M} - \theta_t||_F^2;$
- 12: Update  $\mathbf{X}' \leftarrow \mathbf{X}' \eta_2 \nabla_{\mathbf{X}'} \mathcal{L}(S, \mathcal{T});$
- 13: Update  $\mathbf{P} \leftarrow \tau \mathbf{P} + (1 \tau) \mathbf{A}'$ ;
- 14: Update  $\mathbf{Z}_h \leftarrow \gamma \mathbf{Z}_h + (1 \gamma) \mathbf{A}'$ ;
- 15: end for

// Computing the final adjacency matrix A' 16:  $\mathbf{Z} = (\mathbf{X}'\mathbf{X}'^T + \alpha \mathbf{I} + \beta \mathbf{I})^{-1} (\mathbf{X}'\mathbf{X}'^T + \alpha \mathbf{P} + \beta \mathbf{Z}_h)$ 

- 17:  $\mathbf{A}' = (|\mathbf{Z}| + |\mathbf{Z}|^T)/2$
- Output:  $\hat{S} = \{A', X', Y'\}$

Here,  $\mathbf{X}' \in \mathbb{R}^{N' \times d}$  is the node features, and  $\mathbf{Z} \in \mathbb{R}^{N' \times N'}$  is the self-expressive matrix that measures the similarity between nodes with interpretability. However, solving such an equation could lead to trivial solutions such as identity matrix I. Consequently, existing methods [\[12,](#page-8-32) [26,](#page-8-14) [35,](#page-8-33) [36,](#page-8-34) [68\]](#page-9-11) impose various regularizations

such as least-square and low rankness. For example, Least Square Regression (LSR) could be represented as:

$$
\min_{\mathbf{Z}} \left| \left| \mathbf{X}'^{T} - \mathbf{X}'^{T} \mathbf{Z} \right| \right|_{F}^{2} + \alpha \left| \left| \mathbf{Z} \right| \right|_{F}^{2},\tag{9}
$$

where  $\alpha > 0$  is a trade-off parameter and  $|| \cdot ||_F$  is Frobenius norm. Nonetheless, the structure information of the original graph is only implicitly used when initializing  $X'$  via message passing. To explicitly incorporate the original graph structure and the synthetic graph structure information to Z, we add a regularization term and propose our self-expressive reconstruction model:

$$
\min_{\mathbf{Z}} \left| \left| \mathbf{X}'^{T} - \mathbf{X}'^{T} \mathbf{Z} \right| \right|_{F}^{2} + \alpha \left| \left| \mathbf{Z} - \mathbf{P} \right| \right|_{F}^{2},\tag{10}
$$

where  $P$  is the synthetic adjacency matrix generated from the original graph. By incorporating  $P$  into the regularization term, the similarity matrix could learn from the original graph structure. However, since the synthetic node features  $X'$  is updated in each epoch and  $X'$  has a significant impact on the optimizing object, the learned similarity matrix Z could experience drastic changes, resulting in unstable performance. To address this issue and mitigate the fluctuations, we propose to introduce the historical similarity matrix  $Z_h$  into the regularization term. By including  $Z_h$ , which captures the past information of the similarity matrix, we ensure a more stable learning process and enhance the overall performance of the model.

<span id="page-3-3"></span>
$$
\min_{\mathbf{Z}} ||\mathbf{X}'^T - \mathbf{X}'^T \mathbf{Z}||_F^2 + \alpha ||\mathbf{Z} - \mathbf{P}||_F^2 + \beta ||\mathbf{Z} - \mathbf{Z}_h||_F^2. \qquad (11)
$$

Here,  $\mathbf{Z}_h$  is initialized as identity matrix **I** and is updated through the training process of the graph condensation task.

4.2.2 Closed-form Solution. Directly optimizing Eq. [11](#page-3-3) is timeconsuming. However, Eq. [11](#page-3-3) can be easily solved by setting its first-order derivative w.r.t. Z to zero:

<span id="page-3-4"></span>
$$
-2X'(X'^T - X'^T Z) + 2\alpha (Z - P) + 2\beta (Z - Z_h) = 0.
$$
 (12)

Eq. [12](#page-3-4) could be reduced to:

<span id="page-3-2"></span>
$$
\mathbf{Z} = (\mathbf{X}'\mathbf{X}'^T + \alpha \mathbf{I} + \beta \mathbf{I})^{-1} (\mathbf{X}'\mathbf{X}'^T + \alpha \mathbf{P} + \beta \mathbf{Z}_h).
$$
 (13)

Here, Z not only takes the correlations between nodes into account (i.e.,  $X'X'^{T}$ ), but also retains the structure information of the original graph (i.e., P). Furthermore, the learned similarity matrix Z possesses interpretability as it explicitly indicates the weight to which the node feature of one node is represented by the other nodes in the synthetic graph dataset. This interpretability allows for a clear understanding of dependencies among the nodes of the synthetic graph dataset, leading to enhanced transparency in the proposed framework and a comprehensive comprehension of the graph data condensation task for the first time.

Considering the symmetry and non-negativity of the real-world adjacency matrix, we symmetrize  $Z$  [\[26,](#page-8-14) [76\]](#page-9-33) and calculate the final synthetic adjacency matrix as follows:

<span id="page-4-0"></span>
$$
A' = \frac{1}{2} (|Z| + |Z|^T), \tag{14}
$$

where  $|\cdot|$  is the elementwise absolute operation.  $A' \in \mathbb{R}^{N' \times N'}$  is the synthetic adjacency matrix.

4.2.3 Complexity Analysis. For Eq. [13,](#page-3-2) the time complexity of computing  $X'X'^{\hat{T}}$  is  $O(N'^{2}d)$ . The time complexity of computing  $(X'X'^T + \alpha I + \beta I)^{-1}$  is  $O(N'^3)$  due to the matrix inverse operation. The time complexity of matrix multiplication between the two main terms is  $O(N'^3)$ . Consequently, the overall time com-plexity for calculating Eq. [13](#page-3-2) is  $O(N^3 + N^2d)$ . In practice, since the matrix multiplication could be highly parallelized, the main time-consuming part of Eq. [13](#page-3-2) is the matrix inverse operation with a complexity of  $O(N^3)$ . It is worth noting that this time complexity is acceptable since the condensed dataset is small, with  $N' \approx 100$ .

## 4.3 Update

4.3.1 Node Feature Update. In order to update and refine the synthetic data  $S$  to ensure its performance similarity to the original data  $\mathcal T$  when training downstream models, it is essential to enable the synthetic data  $S$  to learn the training dynamics observed in models trained with the real data  $\mathcal T$ . This learning process facilitates the alignment of the synthetic data with the underlying patterns and characteristics present in the original data, thereby enhancing its suitability for downstream tasks such as node classification. Here, we adopt multi-step gradient matching [\[3,](#page-8-21) [80\]](#page-9-10), which essentially matches the multi-step gradient of a model trained by the synthetic data  $S$  and the real data  $T$ . Formally, the matching objective could be formulated as follows:

$$
D(S, \mathcal{T}; \theta_t) = \frac{||(\widehat{\theta}_{t+N} - \theta_t) - (\theta_{t+M} - \theta_t)||_F^2}{||\theta_{t+M} - \theta_t||_F^2}
$$

$$
= \frac{||\widehat{\theta}_{t+N} - \theta_{t+M}||_F^2}{||\theta_{t+M} - \theta_t||_F^2}, (15)
$$

where  $\theta_t$  is the model parameter sampled from the training trajectory of  $\mathcal T$ . Based on  $\theta_t$ , we optimized it with dataset  $\mathcal S$  for  $N$ epochs to generate the trained parameter  $\hat{\theta}_{t+N}$  and optimized it with dataset  $T$  for M epochs to generate  $\theta_{t+M}$ .

Subsequently, the matching loss is the expectation of the  $D(S, \mathcal{T}; \theta_t)$ w.r.t.  $\theta_t$ , which could be formulated as follows:

$$
\mathcal{L}(\mathcal{S}, \mathcal{T}) = \mathbb{E}_{\theta_t \sim P_{\theta}}[D(\mathcal{S}, \mathcal{T}; \theta_t)], \qquad (16)
$$

Table 1: Details of dataset statistics.

<span id="page-4-1"></span>

| Datasets   | #Nodes  | #Edges     | #Classes | #Features | Sparsity | Homophily |
|------------|---------|------------|----------|-----------|----------|-----------|
| Citeseer   | 3,327   | 4,732      | 6        | 3,703     | 0.09%    | 0.74      |
| Cora       | 2,708   | 5,429      | 7        | 1,433     | 0.15%    | 0.81      |
| Ogbn-arxiv | 169,343 | 1,166,243  | 40       | 128       | 0.01%    | 0.65      |
| Flickr     | 89,250  | 899,756    | 7        | 500       | 0.02%    | 0.33      |
| Reddit     | 232,965 | 57,307,946 | 41       | 602       | 0.09%    | 0.78      |

where  $P_{\theta}$  is the distribution of parameter trajectories trained by real dataset T. By optimizing  $\mathcal{L}(S, \mathcal{T})$ , the synthetic graph dataset S would be guided by the training trajectories of the original graph  $\mathcal T$ . Consequently, the model trained by  $S$  could have similar parameters to the model trained by  $\mathcal T$  and the performance on downstream tasks is expected to be comparable.

4.3.2 Graph Regularizer Update. The graph regularizer P plays a significant role in determining the resulting graph structure A'. However, maintaining P unchanged throughout the condensing process can lead to undesirable outcomes, such as the synthetic graph A ′ overfitting to the specific characteristics of P. Furthermore, real-world graphs are commonly subject to noise and incompleteness [\[15,](#page-8-31) [38\]](#page-8-35), and these imperfections could be inherited by the graph regularizer P. To address these issues, we update P with bootstrapping algorithms [\[18,](#page-8-36) [38\]](#page-8-35) as follows:

<span id="page-4-2"></span>
$$
\mathbf{P} \leftarrow \tau \mathbf{P} + (1 - \tau) \mathbf{A}' \,. \tag{17}
$$

Similarly, we update the historical similarity matrix  $Z_h$  as follows:

<span id="page-4-3"></span>
$$
Z_h \leftarrow \gamma Z_h + (1 - \gamma) A', \qquad (18)
$$

where  $\tau \in [0, 1]$  and  $\gamma \in [0, 1]$ .  $\tau \ge 0.9$  provides a very slow update to preserve as much of the original structure information as possible.  $y \geq 0.5$  provides a faster update to ensure that the newest structure information can be retained and benefit the downstream processes.

## 5 Experiment

### 5.1 Experimental Settings

Datasets: In line with previous research [\[24,](#page-8-13) [71,](#page-9-8) [80\]](#page-9-10), we evaluate our proposed framework on node classification task. We use three transductive datasets: Citeseer [\[28\]](#page-8-19), Cora [\[28\]](#page-8-19), and Ogbn-arxiv [\[21\]](#page-8-37), as well as two inductive datasets: Flickr [\[73\]](#page-9-7) and Reddit [\[19\]](#page-8-18). The details of the original dataset statistics are shown in Table [1,](#page-4-1) and we follow the public splits for each of them.

Settings: We condense each dataset to three different condensation ratios  $(r)$ , which stands for the ratio of synthetic node number  $rN(0 < r < 1)$  to original node number N. In line with GCond [\[24\]](#page-8-13), the condensation process involves two stages: (1) a learning stage, where a 2-layer SGC with 256 hidden units is used to generate synthetic graphs, (2) a test stage, where a 2-layer GCN with 256 hidden units is trained on the obtained synthetic graph from the first stage, and then the trained model is tested on the original test set. For each setting, we generate 5 synthetic graphs, test each synthetic graph 10 times, and report the average node classification accuracy with standard deviation.

Hyper-parameters: At the learning stage, We perform a grid search to select hyper-parameters on the following searching space: synthetic training steps  $N$  is tuned amongst  $\{5, 20, 50, 100\}$ ; expert <span id="page-5-0"></span>Table 2: Overall node classification accuracy on the test split of datasets. For Citeseer, Cora, and Ogbn-arxiv, we report their transductive performance. For Flickr and Reddit, we report their inductive performance. Full indicates the performance with the original graph. The best results are highlighted in bold and grey. and the second-best results are underlined. Note that \* indicates that we re-implement these methods to guarantee the comparison is fair since they have different GNN models for condensation in the original paper and thus the results are different.

| Dataset    | ratio $(\%)$        |                                                    |                                                    |                                                    | Random Herding K-Center Coarsening                 | <b>DCG</b>                                         | GCond                                              | $SFGC^*$                                           | $SGDD^*$                                           | <b>MTT</b>                                         | <b>GCSR</b>                                        | Full           |
|------------|---------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------|
| Citeseer   | 0.9<br>1.8<br>3.6   | $54.4 \pm 4.4$<br>$64.2 \pm 1.7$<br>$69.1 \pm 0.1$ | $57.1 \pm 1.5$<br>$66.7 \pm 1.0$<br>$69.0 \pm 0.1$ | $52.4 \pm 2.8$<br>$64.3 \pm 1.0$<br>$69.1 \pm 0.1$ | $52.2 \pm 0.4$<br>$59.0 \pm 0.5$<br>$65.3 \pm 0.5$ | $66.8 \pm 1.5$<br>$66.9 \pm 0.9$<br>$66.3 \pm 1.5$ | $70.5 \pm 1.2$<br>$70.6 \pm 0.9$<br>$69.8 \pm 1.4$ | $66.3 \pm 2.4$<br>$69.0 \pm 1.1$<br>$70.8 \pm 0.4$ | $71.5 \pm 0.9$<br>$71.2 \pm 0.7$<br>$70.9 \pm 1.2$ | $66.1 \pm 3.0$<br>$69.2 \pm 1.2$<br>$71.0 \pm 0.6$ | $70.2 \pm 1.1$<br>$71.7 \pm 0.9$<br>$74.0 \pm 0.4$ | $71.7 \pm 0.4$ |
| Cora       | 1.3<br>2.6<br>5.2   | $63.6 \pm 3.7$<br>$72.8 \pm 1.1$<br>$76.8 \pm 0.1$ | $67.0 \pm 1.3$<br>$73.4 \pm 1.0$<br>$76.8 \pm 0.1$ | $64.0 \pm 2.3$<br>$73.2 \pm 1.2$<br>$76.7 \pm 0.1$ | $31.2 \pm 0.2$<br>$65.2 \pm 0.6$<br>$70.6 \pm 0.1$ | $67.3 \pm 1.9$<br>$67.6 \pm 3.5$<br>$67.7 \pm 2.2$ | $79.8 \pm 1.3$<br>$80.1 \pm 0.6$<br>$79.3 \pm 0.3$ | $77.7 \pm 1.8$<br>$79.3 \pm 0.8$<br>$79.4 \pm 0.5$ | $79.1 \pm 1.3$<br>$79.0 \pm 1.9$<br>$80.2 \pm 0.8$ | $78.4 \pm 1.4$<br>$79.7 \pm 0.9$<br>$80.5 \pm 0.6$ | $79.9 \pm 0.7$<br>$80.6 \pm 0.8$<br>$81.2 \pm 0.9$ | $81.4 \pm 0.6$ |
| Ogbn-arxiv | 0.05<br>0.25<br>0.5 | $47.1 \pm 3.9$<br>$57.3 \pm 1.1$<br>$60.0 \pm 0.9$ | $52.4 \pm 1.8$<br>$58.6 \pm 1.2$<br>$60.4 \pm 0.8$ | $47.2 \pm 3.0$<br>$56.8 \pm 0.8$<br>$60.3 \pm 0.4$ | $35.4 \pm 0.3$<br>$43.5 \pm 0.2$<br>$50.4 \pm 0.1$ | $58.6 \pm 0.4$<br>$59.9 \pm 0.3$<br>$59.5 \pm 0.3$ | $59.2 \pm 1.1$<br>$63.2 \pm 0.3$<br>$64.0 \pm 0.4$ | $59.0 \pm 1.8$<br>$64.6 \pm 0.3$<br>$65.2 \pm 0.8$ | $59.6 \pm 0.5$<br>$61.7 \pm 0.3$<br>$58.7 \pm 0.6$ | $58.7 \pm 1.7$<br>$64.2 \pm 0.5$<br>$65.1 \pm 0.7$ | $60.6 \pm 1.1$<br>$65.4 \pm 0.8$<br>$65.9 \pm 0.6$ | $71.3 \pm 0.1$ |
| Flickr     | 0.1<br>0.5<br>1     | $41.8 \pm 2.0$<br>$44.0 \pm 0.4$<br>$44.6 \pm 0.2$ | $42.5 \pm 1.8$<br>$43.9 \pm 0.9$<br>$44.4 \pm 0.6$ | $42.0 \pm 0.7$<br>$43.2 \pm 0.1$<br>$44.1 \pm 0.4$ | $41.9 \pm 0.2$<br>$44.5 \pm 0.1$<br>$44.6 \pm 0.1$ | $46.3 \pm 0.2$<br>$45.9 \pm 0.1$<br>$45.8 \pm 0.1$ | $46.5 \pm 0.4$<br>$47.1 \pm 0.1$<br>$47.1 \pm 0.1$ | $45.5 \pm 0.8$<br>$46.0 \pm 0.4$<br>$46.1 \pm 0.3$ | $46.1 \pm 0.3$<br>$45.9 \pm 0.4$<br>$46.4 \pm 0.2$ | $45.4 \pm 0.4$<br>$46.0 \pm 0.4$<br>$46.2 \pm 0.4$ | $46.6 \pm 0.3$<br>$46.6 \pm 0.2$<br>$46.8 \pm 0.2$ | $47.1 \pm 0.1$ |
| Reddit     | 0.05<br>0.1<br>0.2  | $46.1 \pm 4.4$<br>$58.0 \pm 2.2$<br>$66.3 \pm 1.9$ | $53.1 \pm 2.5$<br>$62.7 \pm 1.0$<br>$71.0 \pm 1.6$ | $46.6 \pm 2.3$<br>$53.0 \pm 3.3$<br>$58.5 \pm 2.1$ | $40.9 \pm 0.5$<br>$42.8 \pm 0.8$<br>$47.4 \pm 0.9$ | $88.2 \pm 0.2$<br>$89.5 \pm 0.1$<br>$90.5 \pm 1.2$ | $88.0 \pm 1.8$<br>$89.6 \pm 0.7$<br>$90.1 \pm 0.5$ | $80.0 \pm 3.1$<br>$84.6 \pm 1.6$<br>$87.9 \pm 1.2$ | $84.2 \pm 0.7$<br>$80.6 \pm 0.4$<br>$84.1 \pm 0.3$ | $81.6 \pm 1.8$<br>$85.2 \pm 1.3$<br>$87.7 \pm 1.1$ | $90.5 \pm 0.2$<br>$91.2 \pm 0.2$<br>$92.2 \pm 0.1$ | $94.1 \pm 0.0$ |

steps  $M$  is searched in  $\{1, 2\}$ ; the learning rate of Adam optimizer for updating synthetic node features  $\eta_2$  is selected from {0.00001, 0.0001, 0.001, 0.01}; the regularization coefficient  $\alpha$  and  $\beta$  is chosen from the log space between 0.1 to 1000; the update rate  $\tau$  is tuned from 0.9 to 1. The update rate  $\gamma$  is fixed to 0.5. The learning rate for updating synthetic networks  $\eta_1$  is fixed to 0.01. At the test stage, we follow the setting of GCond [\[24\]](#page-8-13). We fix the learning rate to 0.01, the weight decay rate to 0.0005, and train networks for 600 epochs.

Baselines: We compare our framework with the following baselines: graph coreset methods (Random, Herding [\[64\]](#page-9-6), and K-Center [\[14,](#page-8-38) [58\]](#page-9-34)), graph coarsening method [\[22\]](#page-8-39), graph-based variant of dataset condensation method (DCG [\[79\]](#page-9-22), MTT [\[3\]](#page-8-21)), and graph condensation methods (GCond [\[24\]](#page-8-13), SFGC [\[80\]](#page-9-10), and SGDD [\[71\]](#page-9-8)).

### 5.2 Overall Performance

For dataset condensation baselines, all the synthetic graphs are generated by SGC. We reuse most of the results in [\[24\]](#page-8-13) since the experiment setting is exactly the same. We re-implement SFGC and SGDD to guarantee a fair comparison since they use GCN to generate the condensed graph dataset. The comparison of all the baselines is shown in Table [2.](#page-5-0) From the table, we can observe that: (1) overall, GCSR achieves superior performance compared to the baselines. The condensed data is comparable to the original dataset for training a GNN for node classification. (2) Compared to baselines, GCSR exhibits an improvement in accuracy with an increase in the condensed graph size, which contradicts the observations reported in prior studies and adheres to the intuition that more data leads to better performance. This further validates that our method is effective and successfully solves the graph dataset condensation task. (3) Moreover, compared with baselines that solely update

<span id="page-5-1"></span>Table 3: The accuracy (%) of different GNNs used for condensation and the condensed dataset tested with GCN. The experiment is conducted on Cora with a 2.6% ratio.

| Methods      | Models         |                |                |                |                |
|--------------|----------------|----------------|----------------|----------------|----------------|
|              | <b>APPNP</b>   | Cheby          | <b>GCN</b>     | SAGE           | SGC            |
| <b>GCOND</b> | $73.5 \pm 2.4$ | $76.8 \pm 2.1$ | $70.6 \pm 3.7$ | $77.0 \pm 0.7$ | $80.1 \pm 0.6$ |
| <b>SFGC</b>  | $78.1 \pm 0.8$ | $77.1 \pm 0.8$ | $79.1 \pm 0.7$ | $78.5 \pm 0.8$ | $79.3 \pm 0.8$ |
| <b>SGDD</b>  | $74.6 \pm 1.6$ | $75.9 \pm 0.2$ | $73.3 \pm 0.2$ | $74.3 \pm 1.8$ | $79.0 \pm 1.9$ |
| <b>GCSR</b>  | $80.3 \pm 1.0$ | $80.2 \pm 0.9$ | $80.1 \pm 0.9$ | $80.2 \pm 1.1$ | $80.6 \pm 0.8$ |

synthetic node features such as DCG, SFGC, and MTT, our approach outperforms them by a large margin. We attribute the improved performance to the effectively generated adjacency matrix that captures both the self-expressiveness property of node features and the information of the original graph structure.

#### 5.3 Cross Architecture Performance

Consistent with state-of-the-art benchmarks [\[24,](#page-8-13) [71,](#page-9-8) [80\]](#page-9-10), we evaluate the generalizability of the graph condensation framework.

Different GNN for Condensation: Here, We choose APPNP [\[16\]](#page-8-17), Cheby [\[7\]](#page-8-15), GCN [\[28\]](#page-8-19), GraphSAGE [\[19\]](#page-8-18), and SGC [\[66\]](#page-9-13) to serve as the models used in the condensation phase and test the condensed dataset with GCN. The experiment is conducted on Cora with a ratio of 2.6%. The performance is reported in Table [3.](#page-5-1) The results reveal that: (1) our method is model-insensitive and performs smoothly across different GNN architectures for condensation. (2) Among all these architectures, SGC is the simplest one but surpasses others in all methods. This indicates that a more complex GNN model in condensation does not necessarily bring better optimization results since it complicates the parameter matching process.

<span id="page-6-1"></span>Image /page/6/Figure/2 description: The image displays four heatmaps labeled (a) Condensed by GCond, (b) Condensed by SGDD, (c) Condensed by GCSR, and (d) Original graph. Each heatmap is a 6x6 matrix of numerical values ranging from 0 to 1, with darker blue indicating higher values and lighter blue indicating lower values. A color bar on the right indicates the scale from 0.0 to 1.0. Heatmap (c) and (d) have red boxes highlighting certain regions. Specifically, in (c), a box encloses the top-left 5x5 submatrix, and another box encloses the bottom-right 4x4 submatrix. In (d), a box encloses the top-left 3x3 submatrix, and another box encloses the submatrix from row 1, column 1 to row 3, column 3. The numerical values in the matrices are displayed within each cell.

Figure 3: Cross-Class Neighborhood Similarity (CCNS) of Citeseer generated from the synthetic graph with a 3.6% condensation ratio from (a) GCond, (b) SGDD, and (c) GCSR, as well as (d) the original graph. The axes represent the classes.

<span id="page-6-0"></span>Table 4: The performance of SGC used for condensation and the condensed dataset tested with different GNNs. The average accuracy  $(\%)$  is reported. Avg. stands for the average accuracy of models except for MLP.

| Dataset                                                                       | Methods      | Models |       |       |      |      |      | Avg. |
|-------------------------------------------------------------------------------|--------------|--------|-------|-------|------|------|------|------|
|                                                                               |              | MLP    | APPNP | Cheby | GCN  | SAGE | SGC  |      |
| Citeseer<br><span style="padding-left: 20px;"><math>r = 1.8%</math></span>    | <b>GCOND</b> | 58.3   | 69.6  | 68.3  | 70.5 | 66.2 | 70.3 | 69.0 |
|                                                                               | <b>SFGC</b>  | 62.4   | 69.1  | 68.2  | 69.0 | 69.0 | 69.3 | 68.9 |
|                                                                               | SGDD         | 64.9   | 71.2  | 70.0  | 71.2 | 71.4 | 71.3 | 71.0 |
|                                                                               | GCSR         | 65.7   | 71.6  | 70.9  | 71.7 | 71.6 | 71.8 | 71.5 |
| Cora<br><span style="padding-left: 20px;"><math>r = 2.6%</math></span>        | <b>GCOND</b> | 61.6   | 78.5  | 76.0  | 80.1 | 78.2 | 79.3 | 78.4 |
|                                                                               | <b>SFGC</b>  | 65.4   | 79.2  | 76.4  | 79.3 | 79.4 | 79.3 | 78.7 |
|                                                                               | SGDD         | 65.5   | 77.6  | 77.8  | 79.0 | 79.2 | 77.9 | 78.3 |
|                                                                               | GCSR         | 70.1   | 80.6  | 78.3  | 80.6 | 80.6 | 80.8 | 80.2 |
| Ogbn-arxiv<br><span style="padding-left: 20px;"><math>r = 0.25%</math></span> | <b>GCOND</b> | 45.8   | 63.4  | 54.9  | 63.2 | 62.6 | 63.7 | 61.6 |
|                                                                               | <b>SFGC</b>  | 45.3   | 63.9  | 59.2  | 64.6 | 64.7 | 64.7 | 63.4 |
|                                                                               | SGDD         | 41.8   | 59.8  | 51.4  | 61.7 | 61.1 | 61.1 | 59.0 |
|                                                                               | GCSR         | 44.7   | 64.4  | 58.9  | 65.4 | 65.4 | 65.6 | 63.9 |
| Flickr<br><span style="padding-left: 20px;"><math>r = 0.5%</math></span>      | <b>GCOND</b> | 44.8   | 45.9  | 42.8  | 47.1 | 46.2 | 46.1 | 45.6 |
|                                                                               | <b>SFGC</b>  | 44.0   | 46.1  | 43.6  | 46.0 | 46.0 | 46.1 | 45.6 |
|                                                                               | SGDD         | 43.0   | 45.3  | 41.7  | 45.9 | 45.8 | 45.7 | 44.9 |
|                                                                               | GCSR         | 45.2   | 46.3  | 44.9  | 46.6 | 46.6 | 46.3 | 46.1 |
| Reddit<br><span style="padding-left: 20px;"><math>r = 0.1%</math></span>      | <b>GCOND</b> | 42.5   | 87.8  | 75.5  | 89.4 | 89.1 | 89.6 | 86.3 |
|                                                                               | <b>SFGC</b>  | 46.3   | 84.3  | 68.4  | 84.6 | 84.6 | 86.6 | 81.7 |
|                                                                               | SGDD         | 41.6   | 80.7  | 69.7  | 80.6 | 81.1 | 67.8 | 76.0 |
|                                                                               | GCSR         | 49.5   | 88.9  | 80.4  | 91.2 | 91.0 | 91.0 | 88.5 |

Different GNN for Test: We optimize synthetic graphs with SGC and test their performance under different neural architectures, i.e., MLP, APPNP, Cheby, GCN, GraphSAGE, and SGC. As shown in Table [4,](#page-6-0) our method obtains the best performance under most neural architectures and shows the best average performance. It has been well studied in [\[47,](#page-9-35) [53,](#page-9-36) [82\]](#page-9-37) that different GNN models show similar low-pass filtering behaviors and the graph topology structure is a low-pass filter that provides a means to denoise the data. Our method fully retains the original structure information which contributes to its excellent transferability.

### 5.4 Learned Graph Structure Visualization

5.4.1 Similarity of Synthetic Structure and Original Structure. As shown in Fig. [3,](#page-6-1) we report the cross-class neighborhood similarity (CCNS) [\[46\]](#page-9-38) of Citesser generated from the synthetic graph of GCond, SGDD, and GCSR, as well as the original graph. We do not involve SFGC as the synthetic graph learned by it is structure-free. The value of CCNS  $s(c, c')$  measures the neighborhood patterns similarity of nodes in class  $c$  and nodes in class  $c'$  from a neighborhood label distribution perspective. It is a good metric that reflects the connection relationships between nodes in a graph. From Fig. [3,](#page-6-1) we can find that: (1) graphs learned from GCond exhibit inter-class similarity with no clear distinction because their structure is directly generated from synthetic node features via MLP. (2) SGDD tends to emphasize the intra-class similarity while ignoring the inter-class similarity. Likewise, it also exhibits no clear distinction in inter-class similarity. (3) Owing to the ability to leverage the original graph structure and the inter-node correlations within the synthetic graph, our results mimic the node connection characteristics of the original graph excellently, which leads to better performance ultimately.

5.4.2 Interpretability of Self-expressive Reconstruction. We visualize the learned synthetic adjacency matrix of Citeseer as well as the components used to generate it (i.e.,  $X'X'^T$  and P). From Fig. [4,](#page-7-0) we can observe that the derived synthetic adjacency matrix tends to maintain the joint connection properties of  $X'X'^T$  and **P**. Connections between nodes of the same class tend to be retained, while connections between nodes of different classes tend to be eliminated. Such a phenomenon aligns with the connection characteristics of the homophilic graph like Citeseer, where nodes are prone to connect with similar others.

### 5.5 Learned Node Feature Visualization

To evaluate whether our method can capture more information from the original graph, we use t-SNE [\[59\]](#page-9-39) to visualize the real and synthetic node features of Cora condensed by GCond, SFGC, SGDD, and GCSR. For a clearer display, we only show nodes from 3 out of 7 classes. We also calculate the average silhouette coefficient [\[56\]](#page-9-40) (a metric that measures the distance between samples of different classes and a higher coefficient is better) of synthetic data generated from each method. Fig. [5](#page-7-1) shows that: (1) data learned by GCond,

KDD '24, August 25–29, 2024, Barcelona, Spain Zhanyu Liu, Chaolv Zeng, and Guanjie Zheng

<span id="page-7-0"></span>Image /page/7/Figure/1 description: The image displays three heatmaps, labeled (a), (b), and (c). Heatmap (a) is titled "X' X' T" and shows a dense pattern of light blue squares with some darker blue clusters. Heatmap (b) is titled "P" and exhibits distinct blocks of darker blue squares arranged diagonally, with lighter blue filling the spaces between them. Heatmap (c) is titled "A'" and also shows distinct blocks of darker blue squares along the diagonal, similar to heatmap (b), but with a more uniform distribution of lighter blue. A color bar on the right indicates that darker shades of blue represent higher values, ranging from 0.0 to 1.0.

Figure 4: Visualizations of (a) the inner product of the synthetic nodes, (b) the updated probabilistic graph, and (c) the synthetic topology structure. The dataset illustrated is Citeseer with the condensation ratio set to 1.8%.

<span id="page-7-1"></span>Image /page/7/Figure/3 description: This image displays four scatter plots, each representing a different method: GCond, SFGC, SGDD, and GCSR. Each plot visualizes data points categorized into three classes (2, 3, and 4), with 'origin' data points shown as circles and 'synthetic' data points as stars. The colors used are red for class 2, blue for class 3, and green for class 4. Each plot also includes a silhouette coefficient (SC) score: GCond has an SC of 0.1417, SFGC has an SC of 0.2868, SGDD has an SC of 0.1414, and GCSR has an SC of 0.4761. Two of the plots, GCond and SGDD, highlight specific clusters of synthetic data points with dashed black circles, indicating areas of interest or potential focus for analysis.

Figure 5: Distributions of the synthetic nodes condensed by four methods (GCond, SFGC, SGDD, and GCSR) on Cora under a 2.6% condensation ratio. SC represents the average silhouette coefficient of the synthetic data.

<span id="page-7-2"></span>Image /page/7/Figure/5 description: This image contains two plots, (a) Citeseer, r=1.8% and (b) Reddit, r=0.1%. Both plots show testing accuracy (%) on the y-axis against training epoch on the x-axis. Plot (a) shows three lines representing k=0 (green), k=2 (orange), and k=4 (blue), along with a dashed red line for the whole dataset. The k=0 line starts at around 62% and increases to about 68% by 6000 epochs. The k=2 and k=4 lines start at around 67% and increase to about 70.5% and 71%, respectively, by 6000 epochs. A note at epoch 0 states "Initializing synthetic nodes via message passing brings a better starting point." Plot (b) also shows three lines for k=0, k=2, and k=4, and a dashed red line for the whole dataset. The k=0 line starts at around 72% and increases to about 84% by 20000 epochs. The k=2 and k=4 lines start at around 90% and increase to about 92% and 93%, respectively, by 20000 epochs. A note at epoch 16000 states "It ultimately leads to a more stable and better convergence result."

Figure 6: Training curve of different initialization methods.  $k$ stands for the hop of neighborhood being captured.  $k = 0$  will have no message passing and have the same performance as a random selection from original nodes.

SFGC, and SGDD is not well conformed to the distribution of the original data, and some nodes are mixed up with nodes of other classes. (2) Data learned by GCSR obeys the distribution of the original data and exhibits a distinct separation between different classes. This could lead to better classification performance and evident distribution characteristics. (3) GCSR achieves the highest silhouette coefficient, indicating the learned node features of different classes are easy to separate.

<span id="page-7-3"></span>Table 5: Evaluation of the effectiveness of each module. MPI denotes message passing initialization for node featuers and SER represents self-expressive reconstruction. The condensation model is SGC. The performance is shown in test accuracy (%) of GCN.

| MPI | SER | Citeseer        | Cora            | Ogbn-arxiv      | Flickr          | Reddit          |
|-----|-----|-----------------|-----------------|-----------------|-----------------|-----------------|
|     |     | r=1.8%          | r=2.6%          | r=0.25%         | r=0.5%          | r=0.1%          |
| -   | -   | 69.2±1.2        | 79.7±0.9        | 64.2±0.5        | 46.0±0.4        | 85.2±1.3        |
| ✓   | -   | 70.1±0.8        | 80.4±0.8        | 64.2±1.1        | 46.4±0.4        | 90.4±0.7        |
| ✓   | ✓   | <b>71.7±0.9</b> | <b>80.6±0.8</b> | <b>65.4±0.8</b> | <b>46.6±0.2</b> | <b>91.2±0.2</b> |

### 5.6 Node Initialization

To evaluate the proposed message passing initialization for node features, we conduct extra experiments with one transductive dataset (Citeseer) and one inductive dataset (Reddit) under different initialization methods. To remove interference from other modules, we do not reconstruct the self-expressive adjacency matrix, i.e., we use the identity matrix as the graph structure in the synthetic dataset. Fig. [6](#page-7-2) illustrates the node classification training curve of different initialization. We can make the following observations: (1) Initializing synthetic nodes via message passing brings a better starting point. We attribute such improvement to the fusion of the original graph structure and node features that enhance the synthetic data and the low-pass filtering properties of message passing that denoise the data. (2) Message passing initialization ultimately leads to a more stable and better convergence result. Generally, the end performance of Citeseer is about 1.3% higher and that of Reddit is about 6.1% higher. Such improvements are marvelous since there is no extra cost. (3) The results are not significantly influenced by the number of hops of the neighborhood. Empirically, setting  $k$  to 2 or 3 is a more robust and general choice.

### 5.7 Ablation Study

In GCSR, two components help broadcast the original graph structure to the synthetic graph, i.e., message passing initialization and self-expressive reconstruction. We conduct an ablation study to analyze the contribution of each component. As seen in Table [5,](#page-7-3) the joint use of two components brings about state-of-the-art performance. The results support our claim that both components contribute to leveraging information from the original graph. Message passing initialization transfers the implicit information of the original graph structure to the synthetic node features. Self-expressive reconstruction generates the interpretable graph structure that combines the information of the correlation between the node embeddings and the global structure information from the original graph. It is worth mentioning that, as a novel approach to initialize synthetic nodes, message passing initialization is independent of other modules and can significantly improve the condensation results even without adjacency matrices.

## 6 Conclusion

In this paper, we present a novel framework GCSR which learns synthetic graphs via self-expressive graph structure reconstruction.

It fills the gap in existing methods by making full use of the original graph structure and the correlations between node features to generate an interpretable synthetic graph structure. Extensive experimental results demonstrate the superiority of our proposed method. In the future, we plan to explore a more comprehensive reconstruction strategy that broadcasts more information from the original graph to the synthetic graph (e.g., sparsity and heterogeneity) to enhance the generalizability and robustness of synthetic graphs.

### Acknowledgement

This work was sponsored by National Key Research and Development Program of China under Grant No.2022YFB3904204, National Natural Science Foundation of China under Grant No. 62102246, 62272301, and Provincial Key Research and Development Program of Zhejiang under Grant No. 2021C01034. Part of the work was done when the students were doing internships at Yunqi Academy of Engineering.

#### References

- <span id="page-8-9"></span>[1] Joshua Batson, Daniel A Spielman, Nikhil Srivastava, and Shang-Hua Teng. 2013. Spectral sparsification of graphs: theory and algorithms. Commun. ACM 56, 8 (2013), 87–94.
- <span id="page-8-7"></span>[2] Chen Cai, Dingkang Wang, and Yusu Wang. 2021. Graph coarsening with neural networks. arXiv preprint arXiv:2102.01350 (2021).
- <span id="page-8-21"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. 2022. Dataset distillation by matching training trajectories. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 4750–4759.
- <span id="page-8-10"></span>[4] Tianlong Chen, Yongduo Sui, Xuxi Chen, Aston Zhang, and Zhangyang Wang. 2021. A unified lottery ticket hypothesis for graph neural networks. In International conference on machine learning. PMLR, 1695–1706.
- <span id="page-8-11"></span>[5] Wei-Lin Chiang, Xuanqing Liu, Si Si, Yang Li, Samy Bengio, and Cho-Jui Hsieh. 2019. Cluster-gcn: An efficient algorithm for training deep and large graph convolutional networks. In Proceedings of the 25th ACM SIGKDD international conference on knowledge discovery & data mining. 257–266.
- <span id="page-8-24"></span>[6] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. 2022. Scaling up dataset distillation to imagenet-1k with constant memory. arXiv preprint arXiv:2211.10586 (2022).
- <span id="page-8-15"></span>[7] Michaël Defferrard, Xavier Bresson, and Pierre Vandergheynst. 2016. Convolutional neural networks on graphs with fast localized spectral filtering. Advances in neural information processing systems 29 (2016).
- <span id="page-8-23"></span>[8] Zhiwei Deng and Olga Russakovsky. 2022. Remember the past: Distilling datasets into addressable memories for neural networks. Advances in Neural Information Processing Systems 35 (2022), 34391–34404.
- <span id="page-8-25"></span>[9] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. 2023. Minimizing the accumulated trajectory error to improve dataset distillation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 3749–3758.
- <span id="page-8-16"></span>[10] Simon S Du, Kangcheng Hou, Russ R Salakhutdinov, Barnabas Poczos, Ruosong Wang, and Keyulu Xu. 2019. Graph neural tangent kernel: Fusing graph neural networks with graph kernels. Advances in neural information processing systems 32 (2019).
- <span id="page-8-1"></span>[11] Magdalini Eirinaki, Jerry Gao, Iraklis Varlamis, and Konstantinos Tserpes. 2018. Recommender systems for large-scale social networks: A review of challenges and solutions., 413-418 pages
- <span id="page-8-32"></span>[12] Ehsan Elhamifar and René Vidal. 2013. Sparse subspace clustering: Algorithm, theory, and applications. IEEE transactions on pattern analysis and machine intelligence 35, 11 (2013), 2765–2781.
- <span id="page-8-20"></span>[13] Wenqi Fan, Yao Ma, Qing Li, Yuan He, Eric Zhao, Jiliang Tang, and Dawei Yin. 2019. Graph neural networks for social recommendation. In The world wide web conference. 417–426.
- <span id="page-8-38"></span>[14] Reza Zanjirani Farahani and Masoud Hekmatfar. 2009. Facility location: concepts, models, algorithms and case studies. Springer Science & Business Media.
- <span id="page-8-31"></span>[15] Luca Franceschi, Mathias Niepert, Massimiliano Pontil, and Xiao He. 2019. Learning discrete structures for graph neural networks. In International conference on machine learning. PMLR, 1972–1982.
- <span id="page-8-17"></span>[16] Johannes Gasteiger, Aleksandar Bojchevski, and Stephan Günnemann. 2018. Predict then propagate: Graph neural networks meet personalized pagerank. arXiv preprint arXiv:1810.05997 (2018).

- <span id="page-8-30"></span>[17] Justin Gilmer, Samuel S Schoenholz, Patrick F Riley, Oriol Vinyals, and George E Dahl. 2017. Neural message passing for quantum chemistry. In International conference on machine learning. PMLR, 1263–1272.
- <span id="page-8-36"></span>Jean-Bastien Grill, Florian Strub, Florent Altché, Corentin Tallec, Pierre Richemond, Elena Buchatskaya, Carl Doersch, Bernardo Avila Pires, Zhaohan Guo, Mohammad Gheshlaghi Azar, et al. 2020. Bootstrap your own latent-a new approach to self-supervised learning. Advances in neural information processing systems 33 (2020), 21271–21284.
- <span id="page-8-18"></span>[19] Will Hamilton, Zhitao Ying, and Jure Leskovec. 2017. Inductive representation learning on large graphs. Advances in neural information processing systems 30 (2017).
- <span id="page-8-5"></span>[20] Yi Han, Shanika Karunasekera, and Christopher Leckie. 2020. Graph neural networks with continual learning for fake news detection from social media. arXiv preprint arXiv:2007.03316 (2020).
- <span id="page-8-37"></span>[21] Weihua Hu, Matthias Fey, Marinka Zitnik, Yuxiao Dong, Hongyu Ren, Bowen Liu, Michele Catasta, and Jure Leskovec. 2020. Open graph benchmark: Datasets for machine learning on graphs. Advances in neural information processing systems 33 (2020), 22118–22133.
- <span id="page-8-39"></span>[22] Zengfeng Huang, Shengzhong Zhang, Chong Xi, Tang Liu, and Min Zhou. 2021. Scaling up graph neural networks via graph coarsening. In Proceedings of the 27th ACM SIGKDD conference on knowledge discovery & data mining. 675–684.
- <span id="page-8-12"></span>[23] Wei Jin, Xianfeng Tang, Haoming Jiang, Zheng Li, Danqing Zhang, Jiliang Tang, and Bing Yin. 2022. Condensing graphs via one-step gradient matching. In Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining. 720–730.
- <span id="page-8-13"></span>[24] Wei Jin, Lingxiao Zhao, Shichang Zhang, Yozen Liu, Jiliang Tang, and Neil Shah. 2021. Graph condensation for graph neural networks. arXiv preprint arXiv:2110.07580 (2021).
- <span id="page-8-8"></span>[25] Yu Jin, Andreas Loukas, and Joseph JaJa. 2020. Graph coarsening with preserved spectral properties. In International Conference on Artificial Intelligence and Statistics. PMLR, 4452–4462.
- <span id="page-8-14"></span>[26] Zhao Kang, Zhanyu Liu, Shirui Pan, and Ling Tian. 2022. Fine-grained attributed graph clustering. In Proceedings of the 2022 SIAM International Conference on Data Mining (SDM). SIAM, 370–378.
- <span id="page-8-26"></span>[27] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. 2022. Dataset condensation via efficient synthetic-data parameterization. In International Conference on Machine Learning. PMLR, 11102–11118.
- <span id="page-8-19"></span>[28] Thomas N Kipf and Max Welling. 2016. Semi-supervised classification with graph convolutional networks. arXiv preprint arXiv:1609.02907 (2016).
- <span id="page-8-0"></span>[29] Rabindra Lamsal. 2021. Design and analysis of a large-scale COVID-19 tweets dataset. applied intelligence 51 (2021), 2790–2804.
- <span id="page-8-29"></span>[30] Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. 2022. Dataset condensation with latent space knowledge factorization and sharing. arXiv preprint arXiv:2208.10494 (2022).
- <span id="page-8-27"></span>[31] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. 2022. Dataset condensation with contrastive signals. In International Conference on Machine Learning. PMLR, 12352–12364.
- <span id="page-8-28"></span>[32] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. 2022. Dataset distillation using parameter pruning. arXiv preprint arXiv:2209.14609 (2022).
- <span id="page-8-6"></span>[33] Zhizhong Li and Derek Hoiem. 2017. Learning without forgetting. IEEE transactions on pattern analysis and machine intelligence 40, 12 (2017), 2935–2947.
- <span id="page-8-2"></span>[34] Chumeng Liang, Zherui Huang, Yicheng Liu, Zhanyu Liu, Guanjie Zheng, Hanyuan Shi, Kan Wu, Yuhao Du, Fuliang Li, and Zhenhui Jessie Li. 2023. CBLab: Supporting the Training of Large-scale Traffic Control Policies with Scalable Traffic Simulation. In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining. 4449–4460.
- <span id="page-8-33"></span>[35] Guangcan Liu, Zhouchen Lin, Shuicheng Yan, Ju Sun, Yong Yu, and Yi Ma. 2012. Robust recovery of subspace structures by low-rank representation. IEEE transactions on pattern analysis and machine intelligence 35, 1 (2012), 171–184.
- <span id="page-8-34"></span>[36] Guangcan Liu, Zhouchen Lin, and Yong Yu. 2010. Robust subspace segmentation by low-rank representation. In Proceedings of the 27th international conference on machine learning (ICML-10). 663–670.
- <span id="page-8-22"></span>[37] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. 2022. Dataset distillation via factorization. Advances in Neural Information Processing Systems 35 (2022), 1100–1113.
- <span id="page-8-35"></span>[38] Yixin Liu, Yu Zheng, Daokun Zhang, Hongxu Chen, Hao Peng, and Shirui Pan. 2022. Towards unsupervised deep graph structure learning. In Proceedings of the ACM Web Conference 2022. 1392–1403.
- <span id="page-8-4"></span>[39] Zhanyu Liu, Ke Hao, Guanjie Zheng, and Yanwei Yu. 2024. Dataset Condensation for Time Series Classification via Dual Domain Matching. arXiv preprint arXiv:2403.07245 (2024).
- <span id="page-8-3"></span>[40] Zhanyu Liu, Chumeng Liang, Guanjie Zheng, and Hua Wei. 2023. FDTI: Finegrained Deep Traffic Inference with Roadnet-enriched Graph. arXiv preprint arXiv:2306.10945 (2023).
- [41] Zhanyu Liu, Guanjie Zheng, and Yanwei Yu. 2023. Cross-city Few-Shot Traffic Forecasting via Traffic Pattern Bank. In Proceedings of the 32nd ACM International Conference on Information and Knowledge Management. 1451–1460.

KDD '24, August 25-29, 2024, Barcelona, Spain

- <span id="page-9-2"></span>[42] Zhanyu Liu, Guanjie Zheng, and Yanwei Yu. 2024. Multi-scale Traffic Pattern Bank for Cross-city Few-shot Traffic Forecasting. arXiv preprint arXiv:2402.00397 (2024).
- <span id="page-9-23"></span>[43] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. 2023. Dataset Distillation with Convexified Implicit Gradients. arXiv preprint arXiv:2302.06755 (2023).
- <span id="page-9-31"></span>[44] Can-Yi Lu, Hai Min, Zhong-Qiu Zhao, Lin Zhu, De-Shuang Huang, and Shuicheng Yan. 2012. Robust and efficient subspace segmentation via least squares regression. In Computer Vision–ECCV 2012: 12th European Conference on Computer Vision, Florence, Italy, October 7-13, 2012, Proceedings, Part VII 12. Springer, 347–360.
- <span id="page-9-29"></span>[45] Juncheng Lv, Zhao Kang, Xiao Lu, and Zenglin Xu. 2021. Pseudo-supervised deep subspace clustering. IEEE Transactions on Image Processing 30 (2021), 5252–5263.
- <span id="page-9-38"></span>[46] Yao Ma, Xiaorui Liu, Neil Shah, and Jiliang Tang. 2021. Is homophily a necessity for graph neural networks? arXiv preprint arXiv:2106.06134 (2021).
- <span id="page-9-35"></span>[47] Yao Ma, Xiaorui Liu, Tong Zhao, Yozen Liu, Jiliang Tang, and Neil Shah. 2021. A unified view on graph neural networks as graph signal denoising. In Proceedings of the 30th ACM International Conference on Information & Knowledge Management. 1202–1211.
- <span id="page-9-30"></span>[48] Zhengrui Ma, Zhao Kang, Guangchun Luo, Ling Tian, and Wenyu Chen. 2020. Towards clustering-friendly representations: Subspace clustering via graph filtering. In Proceedings of the 28th ACM international conference on multimedia. 3081–3089.
- <span id="page-9-0"></span>[49] Usman Naseem, Imran Razzak, Matloob Khushi, Peter W Eklund, and Jinman Kim. 2021. COVIDSenti: A large-scale benchmark Twitter data set for COVID-19 sentiment analysis. IEEE transactions on computational social systems 8, 4 (2021), 1003–1015.
- <span id="page-9-17"></span>[50] Usman Nazir, He Wang, and Murtaza Taj. 2021. Survey of image based graph neural networks. arXiv preprint arXiv:2106.06307 (2021).
- <span id="page-9-24"></span>[51] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. 2020. Dataset meta-learning from kernel ridge-regression. arXiv preprint arXiv:2011.00050 (2020).
- <span id="page-9-18"></span>[52] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. 2021. Dataset distillation with infinitely wide convolutional networks. Advances in Neural Information Processing Systems 34 (2021), 5186–5198.
- <span id="page-9-36"></span>[53] Hoang Nt and Takanori Maehara. 2019. Revisiting graph neural networks: All we have is low-pass filters. arXiv preprint arXiv:1905.09550 (2019).
- <span id="page-9-32"></span>[54] Erlin Pan and Zhao Kang. 2021. Multi-view contrastive graph clustering. Advances in neural information processing systems 34 (2021), 2148–2159.
- <span id="page-9-5"></span>[55] Pengzhen Ren, Yun Xiao, Xiaojun Chang, Po-Yao Huang, Zhihui Li, Xiaojiang Chen, and Xin Wang. 2021. A comprehensive survey of neural architecture search: Challenges and solutions. ACM Computing Surveys (CSUR) 54, 4 (2021), 1–34.
- <span id="page-9-40"></span>[56] Peter J Rousseeuw. 1987. Silhouettes: a graphical aid to the interpretation and validation of cluster analysis. Journal of computational and applied mathematics 20 (1987), 53–65.
- <span id="page-9-19"></span>[57] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. 2023. Datadam: Efficient dataset distillation with attention matching. In Proceedings of the IEEE/CVF International Conference on Computer Vision. 17097–17107.
- <span id="page-9-34"></span>[58] Ozan Sener and Silvio Savarese. 2017. Active learning for convolutional neural networks: A core-set approach. arXiv preprint arXiv:1708.00489 (2017).
- <span id="page-9-39"></span>[59] Laurens Van der Maaten and Geoffrey Hinton. 2008. Visualizing data using t-SNE. Journal of machine learning research 9, 11 (2008).
- <span id="page-9-12"></span>[60] Petar Velickovic, Guillem Cucurull, Arantxa Casanova, Adriana Romero, Pietro Lio, Yoshua Bengio, et al. 2017. Graph attention networks. stat 1050, 20 (2017), 10–48550.
- <span id="page-9-27"></span>[61] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. 2022. Cafe: Learning to condense dataset by aligning features. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 12196–12205.
- <span id="page-9-3"></span>[62] Ruijie Wang, Yuchen Yan, Jialu Wang, Yuting Jia, Ye Zhang, Weinan Zhang, and Xinbing Wang. 2018. Acekg: A large-scale knowledge graph for academic data

mining. In Proceedings of the 27th ACM international conference on information and knowledge management. 1487–1490.

- <span id="page-9-20"></span>[63] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. 2018. Dataset distillation. arXiv preprint arXiv:1811.10959 (2018).
- <span id="page-9-6"></span>[64] Max Welling. 2009. Herding dynamical weights to learn. In Proceedings of the 26th Annual International Conference on Machine Learning. 1121–1128.
- <span id="page-9-1"></span>[65] Fangzhao Wu, Ying Qiao, Jiun-Hung Chen, Chuhan Wu, Tao Qi, Jianxun Lian, Danyang Liu, Xing Xie, Jianfeng Gao, Winnie Wu, et al. 2020. Mind: A large-scale dataset for news recommendation. In Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics. 3597–3606.
- <span id="page-9-13"></span>[66] Felix Wu, Amauri Souza, Tianyi Zhang, Christopher Fifty, Tao Yu, and Kilian Weinberger. 2019. Simplifying graph convolutional networks. In International conference on machine learning. PMLR, 6861–6871.
- <span id="page-9-16"></span>[67] Lingfei Wu, Yu Chen, Kai Shen, Xiaojie Guo, Hanning Gao, Shucheng Li, Jian Pei, Bo Long, et al. 2023. Graph neural networks for natural language processing: A survey. Foundations and Trends® in Machine Learning 16, 2 (2023), 119-328.
- <span id="page-9-11"></span>[68] Jun Xu, Mengyang Yu, Ling Shao, Wangmeng Zuo, Deyu Meng, Lei Zhang, and David Zhang. 2019. Scaled simplex representation for subspace clustering. IEEE Transactions on Cybernetics 51, 3 (2019), 1493–1505.
- <span id="page-9-14"></span>[69] Keyulu Xu, Weihua Hu, Jure Leskovec, and Stefanie Jegelka. 2018. How powerful are graph neural networks? arXiv preprint arXiv:1810.00826 (2018).
- <span id="page-9-9"></span>[70] Zhe Xu, Yuzhong Chen, Menghai Pan, Huiyuan Chen, Mahashweta Das, Hao Yang, and Hanghang Tong. 2023. Kernel Ridge Regression-Based Graph Dataset Distillation. In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining. 2850–2861.
- <span id="page-9-8"></span>[71] Beining Yang, Kai Wang, Qingyun Sun, Cheng Ji, Xingcheng Fu, Hao Tang, Yang You, and Jianxin Li. 2023. Does Graph Distillation See Like Vision Dataset Counterpart? arXiv preprint arXiv:2310.09192 (2023).
- <span id="page-9-15"></span>[72] Chenxiao Yang, Qitian Wu, Jiahua Wang, and Junchi Yan. 2022. Graph neural networks are inherently good generalizers: Insights by bridging gnns and mlps. arXiv preprint arXiv:2212.09034 (2022).
- <span id="page-9-7"></span>[73] Hanqing Zeng, Hongkuan Zhou, Ajitesh Srivastava, Rajgopal Kannan, and Viktor Prasanna. 2019. Graphsaint: Graph sampling based inductive learning method. arXiv preprint arXiv:1907.04931 (2019).
- <span id="page-9-4"></span>[74] Hongming Zhang, Xin Liu, Haojie Pan, Yangqiu Song, and Cane Wing-Ki Leung. 2020. ASER: A large-scale eventuality knowledge graph. In Proceedings of the web conference 2020. 201–211.
- <span id="page-9-26"></span>[75] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. 2023. Accelerating dataset distillation via model augmentation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 11950–11959.
- <span id="page-9-33"></span>[76] Xiaotong Zhang, Han Liu, Qimai Li, and Xiao-Ming Wu. 2019. Attributed graph clustering via adaptive graph convolution. arXiv preprint arXiv:1906.01210 (2019).
- <span id="page-9-21"></span>[77] Bo Zhao and Hakan Bilen. 2021. Dataset condensation with differentiable siamese augmentation. In International Conference on Machine Learning. PMLR, 12674– 12685.
- <span id="page-9-28"></span>[78] Bo Zhao and Hakan Bilen. 2023. Dataset condensation with distribution matching. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision. 6514–6523.
- <span id="page-9-22"></span>[79] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. 2020. Dataset condensation with gradient matching. arXiv preprint arXiv:2006.05929 (2020).
- <span id="page-9-10"></span>[80] Xin Zheng, Miao Zhang, Chunyang Chen, Quoc Viet Hung Nguyen, Xingquan Zhu, and Shirui Pan. 2023. Structure-free Graph Condensation: From Large-scale Graphs to Condensed Graph-free Data. arXiv preprint arXiv:2306.02664 (2023).
- <span id="page-9-25"></span>[81] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. 2022. Dataset distillation using neural feature regression. Advances in Neural Information Processing Systems 35 (2022), 9813–9827.
- <span id="page-9-37"></span>[82] Meiqi Zhu, Xiao Wang, Chuan Shi, Houye Ji, and Peng Cui. 2021. Interpreting and unifying graph neural networks with an optimization framework. In Proceedings of the Web Conference 2021. 1215–1226.

Table 6: The effectiveness of MPI.

<span id="page-10-1"></span><span id="page-10-0"></span>

| Dataset           | GCSR                             | SFGC           | SFGC+MPI                  | GCond          | GCond+MPI                 |
|-------------------|----------------------------------|----------------|---------------------------|----------------|---------------------------|
| Citeseer (r=1.8%) | <b><math>74.0 \pm 0.4</math></b> | $70.8 \pm 0.4$ | $71.8 \pm 0.4$ $\uparrow$ | $69.8 \pm 1.4$ | $71.3 \pm 0.9$ $\uparrow$ |
| Reddit (r=0.1%)   | <b><math>91.2 \pm 0.2</math></b> | $84.6 \pm 1.6$ | $90.5 \pm 0.2$ $\uparrow$ | $89.6 \pm 0.7$ | $90.2 \pm 0.2$ $\uparrow$ |

Table 7: The sensitivity analysis of  $k$  in MPI.

<span id="page-10-2"></span>

| Dataset                                                                                                      |  |  |  |
|--------------------------------------------------------------------------------------------------------------|--|--|--|
| Citeseer(r=1.8%)   $69.2 \pm 1.2$ $70.2 \pm 0.8$ $70.2 \pm 0.8$ $70.3 \pm 0.7$ $70.4 \pm 0.7$ $70.3 \pm 0.7$ |  |  |  |

<span id="page-10-3"></span>Table 8: The effectiveness of graph regularizer initialization of GCSR.

| Method             | Citeseer $(r=1.8%)$ | Reddit $(r=0.1%)$ |
|--------------------|---------------------|-------------------|
| <b>GCSR</b>        | $71.8 \pm 0.9$      | $91.2 \pm 0.2$    |
| Sub-structure Init | $69.5 \pm 2.0$      | $91.0 \pm 0.3$    |
| Random Init        | $64.8 \pm 2.6$      | $72.2 \pm 2.5$    |

<span id="page-10-4"></span>Table 9: The sensitive analysis of regularizer update parameter  $\tau$ .

| Dataset          | 0.5        | 0.9        | 0.99       | 0.999      | 1          |
|------------------|------------|------------|------------|------------|------------|
| Citeseer(r=1.8%) | 70.2 ± 1.0 | 71.8 ± 0.9 | 70.2 ± 1.2 | 68.7 ± 1.7 | 68.1 ± 1.8 |

<span id="page-10-5"></span>Table 10: The sensitive analysis of regularizer update parameter y.

| Dataset          | 0.1        | 0.5        | 0.9        | 1          |
|------------------|------------|------------|------------|------------|
| Citeseer(r=1.8%) | 71.6 ± 0.9 | 71.8 ± 0.9 | 71.6 ± 0.9 | 70.6 ± 0.8 |

Table 11: The sensitivity analysis of  $\alpha$ .

<span id="page-10-6"></span>

| Dataset                                                                                       | 0.1 | 0.5 | 10 |
|-----------------------------------------------------------------------------------------------|-----|-----|----|
| Citeseer(r=1.8%)   $70.1 \pm 0.8$ $70.2 \pm 1.0$ $71.1 \pm 0.8$ $71.8 \pm 0.9$ $70.1 \pm 1.0$ |     |     |    |
| Ogbn-arxiv (r=0.05%)   59.9 ± 1.1  60.6 ± 1.1  59.2 ± 1.3  58.5 ± 1.8  58.8 ± 0.7             |     |     |    |

Table 12: The sensitivity analysis of  $\beta$ .

<span id="page-10-7"></span>

| Dataset              | 0          | 0.1               | 0.5        | 1                 | 10         |
|----------------------|------------|-------------------|------------|-------------------|------------|
| Citeseer(r=1.8%)     | 71.6 ± 0.9 | <b>71.8 ± 0.9</b> | 71.6 ± 1.0 | 71.6 ± 0.9        | 71.3 ± 1.0 |
| Ogbn-arxiv (r=0.05%) | 59.8 ± 1.3 | 60.3 ± 1.3        | 60.2 ± 1.3 | <b>60.6 ± 1.1</b> | 60.5 ± 1.0 |

## A The Effectiveness of Message Passing Initialization

GCSR introduces message-passing initialization (MPI), which is a novel approach in the field of graph condensation. To further verify the effectiveness of MPI, we implement it on two other graph condensation frameworks and check whether MPI brings performance enhancement. The result is shown in Table [6.](#page-10-1) We could observe that with MPI, the performance of SFGC and GCond both improve significantly, which validates the effectiveness of our proposed MPI module. Nevertheless, our method still performs better, which illustrates the necessity of SER.

Furthermore, we evaluate the sensitivity of parameter  $k$  in the MPI module. We solely implement MPI on GCSR and get the result of different  $k$  on Table [7.](#page-10-2) Consistent with our paper, MPI could significantly improve condensation performance. However, the results are not significantly influenced by  $k$ . As illustrated in the table above, even if we set  $k$  to 8 or 10, the improvement compared to  $k=2$  is very small.

## B Graph Regularizer Update Analysis

We initialize the graph regularizer with a probabilistic adjacency matrix to avoid the inconsistency between the dimensions of adjacency matrices of the synthetic graph and the original graph. To further verify the effectiveness of our initialization, we compare our initialization with sub-structure initialization of DosCond [\[23\]](#page-8-12) and random initialization. The result is shown in Table [8.](#page-10-3) We can observe that the probabilistic adjacency matrix initialization achieves the best performance.

Furthermore, update momentum ratio  $\tau$  and  $\gamma$  play important roles in updating the graph regularizer in Eqs. [17](#page-4-2) and [18.](#page-4-3) We evaluate their impact by setting different  $\tau$  and  $\gamma$  as shown in Table [9](#page-10-4) and Table [10.](#page-10-5) Here  $\tau = 1$  means not update **P** and  $\gamma = 1$  means not update  $Z_h$ . We could observe that these two regularizer updates contribute to the final performance. Moreover, the performance is more sensitive to  $\tau$ , so it is recommended to search for more  $\tau$  in the real application.

## C Graph Reconstruction Analysis

In this section, we analyze the impact of hyper-parameter  $\alpha$  and  $\beta$  in reconstructing the graph structure Z in Eq. [13](#page-3-2) on Citeseer and Ogbn-arxiv as shown in Table [11](#page-10-6) and Table [12.](#page-10-7) Here,  $\alpha = 0$  means we only apply regularizer  $\mathbb{Z}_h$  and  $\beta = 0$  indicates we only apply regularizer P. We could observe that adding each regularizer could enhance the performance and the joint use of two regularizers brings the best performance. Moreover, the hyperparameter is recommended to search in (0,1].
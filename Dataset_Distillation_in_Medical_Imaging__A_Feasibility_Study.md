# Dataset Distillation in Medical Imaging: A Feasibility Study

Muyang <PERSON><sup>a</sup>, <PERSON><sup>a</sup>, <PERSON><PERSON><sup>a</sup>, <PERSON><PERSON><PERSON><sup>a</sup>, <PERSON><PERSON><PERSON><sup>a</sup>, <PERSON><sup>a</sup>, and <PERSON><PERSON><sup>a</sup>

<sup>a</sup>Vanderbilt University, Nashville TN 37235, USA

# ABSTRACT

Data sharing in the medical image analysis field has potential yet remains underappreciated. The aim is often to share datasets efficiently with other sites to train models effectively. One possible solution is to avoid transferring the entire dataset while still achieving similar model performance. Recent progress in data distillation within computer science offers promising prospects for sharing medical data efficiently without significantly compromising model effectiveness. However, it remains uncertain whether these methods would be applicable to medical imaging, since medical and natural images are distinct fields. Moreover, it is intriguing to consider what level of performance could be achieved with these methods. To answer these questions, we conduct investigations on a variety of leading data distillation methods, in different contexts of medical imaging. We evaluate the feasibility of these methods with extensive experiments in two aspects: 1) Assess the impact of data distillation across multiple datasets characterized by minor or great variations. 2) Explore the indicator to predict the distillation performance. Our extensive experiments across multiple medical datasets reveal that data distillation can significantly reduce dataset size while maintaining comparable model performance to that achieved with the full dataset, suggesting that a small, representative sample of images can serve as a reliable indicator of distillation success. This study demonstrates that data distillation is a viable method for efficient and secure medical data sharing, with the potential to facilitate enhanced collaborative research and clinical applications.

Keywords: Medical Data Sharing, Dataset Distillation, Pattern Recognition

# 1. INTRODUCTION

Data plays a crucial role in machine learning and data analysis, with the importance of big data being demon-strated in many large-scale models today.<sup>[1](#page-7-0)</sup> In medical environments, it is common to share image data between different hospitals for medical research, enhancing patient care, and facilitating the development of innovative treatments.[2](#page-7-1) Therefore, the topic of efficient data sharing between different sites is becoming increasingly important. Furthermore, given the sensitive nature of personal health information, ensuring the privacy and security of this data is crucial to maintaining patient trust and complying with legal and ethical standards.<sup>[3,](#page-7-2)[4](#page-7-3)</sup>

Data distillation  $^5$  $^5$  emerges as a practical solution, offering a means to share the crucial essence of the full dataset without the need to transfer the entire bulk. This approach aims to compress the original dataset into a much smaller dataset to increase the efficiency of model training and deployment, without sacrificing the model performance. Also, it avoids sharing the original data and is suitable for sharing-restricted scenarios.

Among current dataset distillation methods, dataset condensation  $(DC)^6$  $(DC)^6$  is a fundamental work, that firstly proposes to match the gradient between the original dataset and synthetic small dataset, which achieved good performance on natural image datasets. As a method developed from DC, MTT(Matching Training Trajectories) shows great performance on dataset distillation, speeding up the process and improving the general distillation work.[7,](#page-7-6) [8](#page-7-7)

However, it remains uncertain whether the methods that work successfully on natural images would apply to medical imaging, which often features smaller class variability.<sup>[9](#page-7-8)</sup> Natural image data distillation methods were evaluated on ImageNet, CIFAR-[10,](#page-7-9) CIFAR-100, and MNIST.<sup>10, [11](#page-7-10)</sup> These datasets, annotated by humans, feature images with relatively clear visual attributes that facilitate image classification. In contrast, medical images

Further author information: (Send correspondence to Yuankai Huo)

E-mail: <EMAIL>

focus on preserving essential diagnostic details crucial for disease detection, often presenting subtler distinctions between classes.[12,](#page-7-11) [13](#page-7-12) Medical images typically involve similar biological tissues with subtle differences crucial for diagnosis, such as variations in tissue texture, density, or the presence of minute abnormalities. When examining small image patches from pathology or radiology images, distinguishing between benign and malignant tumors or different stages of a disease often requires expert knowledge to interpret subtle visual cues accurately. This necessitates a specialized approach that maintains critical medical information, distinct from the broader aims of natural image distillation.<sup>[14](#page-7-13)</sup> Furthermore, the wider applicability of dataset distillation to various types of medical datasets has yet to be explored. A soft-label anonymous gastric X-ray image distillation method has been proposed, $15, 16$  $15, 16$  $15, 16$  but their work was limited to a single modality. This significant difference in inter-class variation poses unique challenges for dataset distillation methods when applied to medical images.

Image /page/1/Figure/1 description: This image compares natural images and medical images, highlighting differences in inter-class variation. On the left, under 'Natural Images', the CIFAR-100 dataset is shown with examples of 'Chair', 'Clock', 'Cup', and 'Bike', characterized by large inter-class variation. Below these are 'Distilled' versions of these images. In the center, under 'Medical Images', the BloodMNIST dataset is presented with examples of 'Basophil' and 'Platelet', also noted for large inter-class variation. Below these are 'Distilled' versions with question marks. On the right, the PathMNIST dataset is shown with examples of 'Cancer-associated Stroma' and 'Debris', characterized by small inter-class variation. Below these are 'Distilled' versions with question marks. A blue arrow indicates a transition from the natural images to the medical images.

Figure 1: Difference between natural and medical datasets

Therefore, this work focuses on exploring the data distillation of medical images in a wide range of data modalities, including colon pathology, microscope, dermatoscope images, retinal OCT and abdominal CT, for multi-class classification tasks. Initially, we aimed to address these questions as a starting point for our research: 1) How does the distillation process for medical datasets compare to established methods used for natural images in specific contexts or applications? 2) What is the performance across the 9 different medical databases (one integrated dataset and eight individual datasets)? 3)Is there any indicator to predict the distillation performance to some degree?

In this paper, we propose a universal medical dataset distillation pipeline for effective data sharing in healthcare, and we provide comprehensive experimental analysis methods to address the above three key questions. This paper will contribute to the following parts:

- Assess the impact of data distillation across multiple datasets characterized by minor or great variations.
- Explore the indicator to predict the distillation performance.

# 2. METHODS

The universal medical distillation-based method is presented in Fig. [2,](#page-2-0) where we try to answer 3 questions to evaluate the performance. Specifically, we evaluate two benchmark distillation methods for classifying multimodal

<span id="page-2-0"></span>Image /page/2/Figure/0 description: This is a flowchart illustrating a research study on medical image distillation. The study poses three questions: Q1 asks how medical image distillation compares to natural image distillation, with examples of grapefruit and medical cell images. Q2 investigates the performance across nine different medical datasets, showing the distillation process and its effectiveness (high/low) measured by neural networks. Q3 explores indicators to predict distillation performance, presenting results for different 'ipc' values (1, 10, 50) and a link to clinical/research data sharing. The flowchart uses arrows to connect different stages and questions, indicating the flow of the research.

Figure 2: General working pipeline for this paper. We investigate the effectiveness of distillation on medical datasets by designing multiple experiments to answer these questions.

and multiple unimodal medical datasets. Also, we compare the performance of data distillation with random selection baselines to propose an indicator to predict the distillation performance.

# 2.1 Distillation simulation on medical datasets with different data modality

Firstly, we extract 10% of images from each medical dataset and relabeled them with their respective dataset names rather than inter-class names to build a new dataset. The diverse data modalities of these medical datasets ensure a similarly large inter-class variation of natural images, which allows us to test the feasibility of distillation on medical datasets after modification. Secondly, we also design multiply comparison distillation experiments on different medical datasets, which allows us to monitor their possibility in distillation.

## 2.2 Dataset distillation on individual medical datasets

For distillation on individual dataset distillation, we also selected two main dataset distillation methods, DC and MTT to evaluate the distillation performance of different medical datasets.

### 2.2.1 Dataset condensation (DC)

Dataset condensation aims to create a small, synthetic dataset  $S = \{(s_i, y_i)\}\$  that maintains the generalization performance of models trained on it, closely mirroring those trained on the original, larger dataset  $T = \{(x_i, y_i)\}.$ The process is guided by minimizing an empirical loss function, with the primary objective formulated as

$$
\theta_T = \arg\min_{\theta} \frac{1}{|T|} \sum_{(x,y)\in T} l(\phi_{\theta}(x), y)
$$
\n(1)

where l denotes a task-specific loss, and  $\phi_{\theta}$  represents the model parameterized by  $\theta$ . The goal is to ensure that the model  $\phi_{\theta_S}$  trained on the condensed set S approximates the performance of  $\phi_{\theta_T}$  trained on T, facilitated by the equation

$$
\min_{S} \mathbb{E}_{\theta_0 \sim P_{\theta_0}} [D(\theta_S(\theta_0), \theta_T(\theta_0))]
$$
\n(2)

subject to

$$
\theta_S(S) = \arg\min_{\theta} L_S(\theta(\theta_0))
$$
\n(3)

This approach remarkably reduces the computational resources required for training without compromising the model's generalization capability.

#### 2.2.2 Distillation by matching training trajectories (MTT)

The methodology strives to align the parameters of a student network, trained on a distilled dataset, with those of teacher networks, which were trained on the original medical dataset D. Initially, each teacher network T is trained on D, resulting in parameters  $\{\theta_i\}_0^I$ , referred to as teacher parameters. Correspondingly, parameters from training on the distilled dataset  $D_c$  at each iteration i are termed student parameters  $\tilde{\theta}_i$ . The process aims to distill original images that encourage the student network's parameters to resemble those obtained from the actual medical dataset, starting from identical initial values. In the distillation phase, student parameters  $\theta_i$  are initialized with  $\theta_i$  which is randomly selected from teacher parameters at step i. We then apply gradient descent updates to  $\theta$  against the cross-entropy loss  $\ell$  of  $D_c$ , as per the following equation:

$$
\tilde{\theta}_{i+j+1} = \tilde{\theta}_{i+j} - \alpha \nabla \ell(A(D_c); \tilde{\theta}_{i+j}),\tag{4}
$$

where j and  $\alpha$  represent the number of gradient descent updates and the modifiable learning rate, respectively. A signifies a differentiable data augmentation module that enhances distillation efficacy.<sup>[17](#page-7-16)</sup> After the distillation process, we compare the updated student parameters  $\theta_{i+J}$  with teacher parameters  $\theta_{i+K}$ , obtained after K gradient updates, to calculate the final loss  $L$ , expressed as the normalized  $L_2$  loss between these parameter sets:

$$
L = \frac{\|\tilde{\theta}_{i+J} - \theta_{i+K}\|_2^2}{\|\theta_i - \theta_{i+K}\|_2^2},\tag{5}
$$

Minimizing  $L$  and performing backpropagation through all  $J$  updates refines the student network to produce an optimized distilled dataset  $D_c^*$ .

## 3. DATA AND EXPERIMENTAL SETTING

In our investigation, we employed the dataset distillation approach across a diverse array of medical images, leveraging eight meticulously preprocessed datasets from the MedMNIST collection.<sup>[18](#page-7-17)</sup> These datasets encapsulate a wide range of medical imaging types, including colon pathology (PathMNIST), dermatoscopy images (DermaMNIST), retinal OCT scans (OCTMNIST), blood cell microscopy (BloodMNIST), kidney cortex microscopy (TissueMNIST), and various abdominal CT scans (OrganAMNIST, OrganCMNIST, OrganSMNIST). Each dataset was selected for its unique data modality, encompassing a total of six different modalities, and varying significantly in scale, ranging from as few as about 10,000 images to over 165,000 images for training, which resembles the diversity of dataset sizes which resembles the diversity of datasets within medical imaging research. To accommodate our analysis framework—a simple ConvNet architecture designed by Gidaris and Komodak<sup>[19](#page-7-18)</sup>—all images were resized to a uniform dimension of  $32x32$  pixels, slightly adjusted from their original 28x28 format. This standardization was critical for maintaining the integrity of the images while ensuring compatibility with our computational model. To further refine our experimental setup and enhance the reliability of our findings, we applied ZCA whitening across all datasets. This preprocessing step was critical for normalizing the images, which reduced redundancy and emphasized critical features, and effectively improved the model's ability to generalize across diverse medical imaging scenarios. Through these methodological enhancements, our study sought to advance the understanding and application of dataset distillation techniques within the nuanced field of medical imaging, offering insights into the complexities and requirements specific to this domain. For experiment settings, two distillation methods, DC and MTT, were tested on the 8 datasets from MedMNIST when IPC (images per class)=1, 10, and 50. The batch size for training and for real data evaluation was 256 for IPC=1, and 128 for IPC=10 or more, with a training rate of  $5 \times 10^{-6}$ . All the experiments were run on a 16GB NVIDIA RTX5000 GPU.

### 4. RESULTS

Fig [3](#page-4-0) demonstrates that the DC method consistently achieves higher accuracy rates than MTT and random selection in medical dataset distillation, suggesting its superior capability to handle the large inter-class variations characteristic of medical datasets. Table 1 depicts a set of results comparing different methods of dataset distillation on medical images, illustrating the effectiveness of the distillation process. It shows several subsets of the MedMNIST dataset, with varying medical imaging modalities such as blood samples, skin conditions, and different organ scans. The right side of the image presents a table with accuracy percentages across random selection and two distillation methods: DC and MTT, at different numbers of images per class (1, 10, 50). From the accuracy metrics, it is evident that both DC and MTT methods greatly outperform the random selection baseline. This implies a successful distillation that encapsulates large inter-class variation into a smaller, more manageable dataset size. For instance, with only 10 images per class, DC achieves over 90% accuracy in some cases, which suggests that the method is particularly efficient in retaining critical features from a vast, variable dataset. These results highlight the potential of dataset distillation techniques to simulate large inter-class variations effectively, allowing for the creation of condensed datasets that still carry the essential information needed for high-performance medical image analysis.

<span id="page-4-0"></span>Image /page/4/Figure/2 description: The image displays a comparison of accuracy between Random Selection, DC, and MTT methods across different numbers of images per class (1, 10, and 50). The bar chart shows that for 1 image per class, DC achieves approximately 75% accuracy, MTT achieves approximately 30% accuracy, and Random Selection achieves approximately 25% accuracy. For 10 images per class, DC achieves approximately 90% accuracy, MTT achieves approximately 75% accuracy, and Random Selection achieves approximately 70% accuracy. For 50 images per class, DC and MTT both achieve approximately 90% accuracy, while Random Selection achieves approximately 85% accuracy. To the left of the bar chart, there are two rows of images labeled 'Original datasets' and 'Distilled'. The 'Original datasets' row shows various medical images, including histology slides and CT scans. The 'Distilled' row shows a smaller set of processed or generated images.

Figure 3: Distillation simulation on a large inter-class variance integrated medical dataset. The original datasets, from left to right, are TissueMNIST, PathMNIST, OrganSMNIST, OrganCMNIST, OrganAMNIST, OCTM-NIST, DermaMNIST, and BloodMNIST, each treated as a single class in this integrated dataset. The right figure shows that higher distillation accuracy highlights the potential for increasing inter-class variance in specific medical datasets for improved distillation.

All the accuracies on the randomly selected images from the full dataset and on the distilled images with different distillation methods are listed in Table 1. The synthetic datasets obtained from BloodMNIST, TissueM-NIST, OrganAMNIST, OrganCMNIST and OrganSMNIST show performance comparable to the state-of-the-art accuracy obtained from the corresponding full datasets as expected. For PathMNIST, DermaMNIST and OCTM-NIST, however, random selection method shows better performance than the other two distillation methods when IPC is 50. This indicates that the distillation method may not work well on these datasets. Fig [5](#page-5-0) shows a strong linear correlation between the accuracy of the distilled dataset and the randomly selected dataset. For most of the datasets, the best accuracy is realized on 50 IPC. These experiments allow us to decide the data sharing strategy: we can first distill 50 images and then compare with 50 randomly selected medical images from full datasets. If the performance of the small synthetic dataset is better than the randomly selected, then it is likely that further distillation with a different IPC may yield both more meaningful accuracy insurance and smaller distilled dataset size.

In addition to these findings, our correlation study (Figure [5\)](#page-5-0) revealed a strong linear relationship between the accuracies of distilled datasets and randomly selected subsets. This suggests that the performance on a small, randomly selected sample can serve as a heuristic for predicting the effectiveness of dataset distillation. The variance in the correlation across different runs was minimal, supporting the robustness of this heuristic.

This relationship likely exists because random selection acts as a proxy for task simplicity: simpler tasks with distinct feature spaces are more likely to benefit from data distillation.

Image /page/5/Figure/1 description: The image displays two distinct sections, labeled a) BloodMNIST and b) OCTMNIST. Section a) shows a grid of 64 images of blood cells, with a text box indicating 'Use MTT for distillation with IPC = 50' and an accuracy score of 'ACC = 89.18±0.29'. To the right of the blood cell images, there are 8 smaller images, each paired with a label: basophil, eosinophil, erythroblast, immature granulocytes, lymphocyte, monocyte, neutrophil, and platelet. Section b) shows a grid of 36 images of retinal scans, with a text box indicating 'Use MTT for distillation with IPC = 50' and an accuracy score of 'ACC = 46.12±1.90'. To the right of the retinal scan images, there are 4 smaller images, each paired with a label: choroidal neovascularization, diabetic macular edema, drusen, and normal.

<span id="page-5-0"></span>Figure 4: We test distillation methods on 9 different medical datasets, and most of large variation medical dataset shows relatively ideal distillation performance as is shown like a), and most of small variation datasets, such as b), shows lower effectiveness in distillation.

Image /page/5/Figure/3 description: The image contains two scatter plots side-by-side, both titled "Random Selection vs MTT Accuracy" and "Random Selection vs DC Accuracy" respectively. The left plot shows MTT Accuracy on the y-axis and Random Selection Accuracy on the x-axis, with a correlation of 0.87 and R^2 of 0.753. The right plot shows DC Accuracy on the y-axis and Random Selection Accuracy on the x-axis, with a correlation of 0.65 and R^2 of 0.422. Both plots display data points colored according to a legend that lists seven datasets: PathMNIST, DermaMNIST, OCTMNIST, BloodMNIST, TissueMNIST, OrganAMNIST, OrganCMNIST, and OrganSMNIST. A blue line of best fit is drawn on each plot. The x-axis for both plots ranges from 40 to 80, and the y-axis for the left plot ranges from 40 to 90, while the y-axis for the right plot ranges from 40 to 80.

Figure 5: This figure shows the correlation between MTT/DC and Random Selected method. Comparing MTT and DC's performance on IPC=50 referred to randomly selected 50 images from original datasets, MTT shows higher correlation than DC, which indicates its possible better distillation performance on higher IPC. This can answer the third question in our main pipeline.

# 5. CONCLUSION

Our study presents a comprehensive evaluation of dataset distillation techniques within the realm of medical imaging, showcasing their potential to streamline data sharing and enhance model training efficiency across diverse medical datasets. We've demonstrated that dataset distillation can effectively condense critical diagnostic information into greatly smaller datasets without compromising accuracy. Notably, our findings reveal that

| Dataset     | Img/Cls       | Random Selection                                      | DC                                                  | MTT                                                 | Full Dataset |
|-------------|---------------|-------------------------------------------------------|-----------------------------------------------------|-----------------------------------------------------|--------------|
| PathMNIST   | 1<br>10<br>50 | 26.02 ± 13.41<br>57.69 ± 5.68<br><b>73.47 ± 4.06</b>  | 24.98 ± 2.74<br>42.24 ± 0.89<br>38.26 ± 1.21        | 13.40 ± 0.56<br>32.35 ± 2.07<br>68.61 ± 1.24        | 90.70 ± 0.10 |
| DermaMNIST  | 1<br>10<br>50 | 18.45 ± 11.97<br>27.97 ± 13.71<br><b>60.50 ± 4.20</b> | 28.22 ± 2.12<br>44.07 ± 1.36<br>44.03 ± 2.48        | 25.52 ± 1.75<br>59.67 ± 2.00<br>58.73 ± 0.54        | 73.50 ± 0.10 |
| OCTMNIST    | 1<br>10<br>50 | 26.09 ± 2.91<br>39.71 ± 4.36<br><b>58.41 ± 3.30</b>   | 29.92 ± 0.99<br>46.78 ± 1.05<br>45.21 ± 1.33        | 25.40 ± 1.57<br>35.62 ± 2.38<br>46.12 ± 1.90        | 74.30 ± 0.10 |
| BloodMNIST  | 1<br>10<br>50 | 34.78 ± 6.16<br>64.19 ± 4.83<br>79.14 ± 3.02          | 62.46 ± 2.03<br><b>74.81 ± 0.70</b><br>72.84 ± 0.93 | 60.50 ± 3.01<br><b>89.38 ± 0.33</b><br>89.18 ± 0.29 | 95.80 ± 0.10 |
| TissueMNIST | 1<br>10<br>50 | 23.29 ± 6.74<br>28.69 ± 2.72<br>34.30 ± 6.07          | 33.83 ± 1.91<br>36.25 ± 0.77<br><b>41.04 ± 0.86</b> | 13.6 ± 1.01<br>35.00 ± 1.86<br><b>46.49 ± 0.95</b>  | 67.6 ± 0.10  |
| OrganAMNIST | 1<br>10<br>50 | 19.05 ± 9.44<br>53.03 ± 4.30<br><b>76.65 ± 1.90</b>   | 48.44 ± 0.61<br>75.73 ± 0.30<br><b>75.19 ± 0.38</b> | 44.04 ± 0.53<br>84.52 ± 0.47<br><b>86.33 ± 0.47</b> | 93.50 ± 0.10 |
| OrganCMNIST | 1<br>10<br>50 | 25.38 ± 6.79<br>57.08 ± 4.96<br><b>80.91 ± 0.99</b>   | 50.04 ± 1.54<br>79.03 ± 0.22<br><b>79.69 ± 0.50</b> | 67.29 ± 1.10<br>84.51 ± 0.44<br><b>85.39 ± 0.10</b> | 90.00 ± 0.10 |
| OrganSMNIST | 1<br>10<br>50 | 19.67 ± 4.67<br>40.56 ± 3.18<br><b>65.99 ± 1.16</b>   | 32.89 ± 1.83<br>59.80 ± 0.26<br><b>74.45 ± 0.46</b> | 31.17 ± 0.68<br>66.87 ± 0.52<br>69.17 ± 0.42        | 78.20 ± 0.10 |

Table 1: Comparing distillation and random selection methods in 8 different medical datasets sourced from MedMNIST. As in previous work, we distill the given number of images per class using the training set, train a neural network on the synthetic set, and evaluate on the test set. To get  $\bar{x} \pm s$ , we train 5 networks from scratch on the distilled dataset. Note that the random selected method and state-of-the-art use  $\text{ResNet-18}^{20}$  $\text{ResNet-18}^{20}$  $\text{ResNet-18}^{20}$ for all the datasets. All others use a 128-width ConvNet. The best performances of distillation or random selection method on each dataset are highlighted on bold. This table exhibits different distillation performances on medical datasets with diverse inter-class variations.

distillation methods excel in specific datasets, while challenges remain in others, emphasizing the need for tailored approaches. This work lays a foundational step towards optimizing data sharing strategies in healthcare, fostering advancements in medical research and patient care through more efficient and secure data utilization. Our research not only advances the understanding of dataset distillation's applicability but also opens avenues for future exploration in optimizing distillation processes for medical imaging data.

# ACKNOWLEDGMENTS

This research was supported by NIH R01DK135597(Huo), DoD HT9425-23-1-0003(HCY), NIH NIDDK DK56942(ABF). This work was also supported by Vanderbilt Seed Success Grant, Vanderbilt Discovery Grant, and VISE Seed Grant. This project was supported by The Leona M. and Harry B. Helmsley Charitable Trust grant G-1903-03793 and G-2103-05128. This research was also supported by NIH grants R01EB033385, R01DK132338, REB017230, R01MH125931, and NSF 2040462. We extend gratitude to NVIDIA for their support by means of the NVIDIA hardware grant. This works was also supported by NSF NAIRR Pilot Award NAIRR240055.

# REFERENCES

- <span id="page-7-0"></span>[1] L'heureux, A., Grolinger, K., Elyamany, H. F., and Capretz, M. A., "Machine learning with big data: Challenges and approaches," Ieee Access 5, 7776–7797 (2017).
- <span id="page-7-1"></span>[2] Hopfield, J. J., "Neural networks and physical systems with emergent collective computational abilities.," Proceedings of the national academy of sciences  $79(8)$ ,  $2554-2558$  (1982).
- <span id="page-7-2"></span>[3] Hulsen, T., "Sharing is caring—data sharing initiatives in healthcare," International journal of environmental research and public health  $17(9)$ , 3046 (2020).
- <span id="page-7-3"></span>[4] Kaissis, G. A., Makowski, M. R., Rückert, D., and Braren, R. F., "Secure, privacy-preserving and federated machine learning in medical imaging," Nature Machine Intelligence  $2(6)$ , 305–311 (2020).
- <span id="page-7-4"></span>[5] Wang, T., Zhu, J.-Y., Torralba, A., and Efros, A. A., "Dataset distillation," (2020).
- <span id="page-7-5"></span>[6] Zhao, B., Mopuri, K. R., and Bilen, H., "Dataset condensation with gradient matching," (2021).
- <span id="page-7-6"></span>[7] Chen, Z., Geng, J., Zhu, D., Woisetschlaeger, H., Li, Q., Schimmler, S., Mayer, R., and Rong, C., "A comprehensive study on dataset distillation: Performance, privacy, robustness and fairness," (2023).
- <span id="page-7-7"></span>[8] Cui, J., Wang, R., Si, S., and Hsieh, C.-J., "Dc-bench: Dataset condensation benchmark," (2022).
- <span id="page-7-8"></span>[9] Wang, Y., Thakur, A., Dong, M., Ma, P., Petridis, S., Shang, L., Zhu, T., and Clifton, D. A., "Medical records condensation: a roadmap towards healthcare data democratisation," arXiv e-prints , arXiv:2305.03711 (May 2023).
- <span id="page-7-9"></span>[10] Beyer, L., Hénaff, O. J., Kolesnikov, A., Zhai, X., and Oord, A. v. d., "Are we done with imagenet?,"  $arXiv$ preprint arXiv:2006.07159 (2020).
- <span id="page-7-10"></span>[11] Byerly, A., Kalganova, T., and Dear, I., "No routing needed between capsules," (2021).
- <span id="page-7-11"></span>[12] Kumar, R., Wang, W., Kumar, J., Yang, T., Khan, A., Ali, W., and Ali, I., "An integration of blockchain and ai for secure data sharing and detection of ct images for the hospitals," Computerized Medical Imaging and Graphics 87, 101812 (2021).
- <span id="page-7-12"></span>[13] Vu, H. A., "Integrating preprocessing methods and convolutional neural networks for effective tumor detection in medical imaging,"  $arXiv$  preprint  $arXiv:2402.16221$  (2024).
- <span id="page-7-13"></span>[14] Morra, L., Piano, L., Lamberti, F., and Tommasi, T., "Bridging the gap between natural and medical images through deep colorization," in [2020 25th International Conference on Pattern Recognition (ICPR)], 835–842, IEEE (2021).
- <span id="page-7-14"></span>[15] Li, G., Togo, R., Ogawa, T., and Haseyama, M., "Soft-label anonymous gastric x-ray image distillation," in [2020 IEEE International Conference on Image Processing (ICIP)], IEEE (Oct. 2020).
- <span id="page-7-15"></span>[16] Li, G., Togo, R., Ogawa, T., and Haseyama, M., "Compressed gastric image generation based on soft-label dataset distillation for medical data sharing," Computer Methods and Programs in Biomedicine 227, 107189 (2022).
- <span id="page-7-16"></span>[17] Zhao, B. and Bilen, H., "Dataset condensation with differentiable siamese augmentation," (2021).
- <span id="page-7-17"></span>[18] Yang, J., Shi, R., Wei, D., Liu, Z., Zhao, L., Ke, B., Pfister, H., and Ni, B., "Medmnist v2 - a large-scale lightweight benchmark for 2d and 3d biomedical image classification," Scientific Data 10 (Jan. 2023).
- <span id="page-7-18"></span>[19] Gidaris, S. and Komodakis, N., "Dynamic few-shot visual learning without forgetting," (2018).
- <span id="page-7-19"></span>[20] He, K., Zhang, X., Ren, S., and Sun, J., "Deep residual learning for image recognition," (2015).
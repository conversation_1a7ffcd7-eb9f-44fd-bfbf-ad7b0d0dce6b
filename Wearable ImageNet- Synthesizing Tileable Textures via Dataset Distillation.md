Image /page/0/Picture/0 description: The image contains a light blue logo with the letters C, V, and F. The letters are stylized and appear to be interconnected.

This CVPR workshop paper is the Open Access version, provided by the Computer Vision Foundation. Except for this watermark, it is identical to the accepted version; the final published version of the proceedings is available on IEEE Xplore.

# <span id="page-0-1"></span>Wearable ImageNet: Synthesizing Tileable Textures via Dataset Distillation

George <PERSON><sup>1</sup> Tongzhou Wang<sup>2</sup> <PERSON><sup>2</sup> <PERSON><sup>3</sup> Jun-Yan  $Zhu<sup>1</sup>$ 

<sup>1</sup>Carnegie Mellon University <sup>2</sup>Massachusetts Institute of Technology <sup>3</sup>UC Berkeley

[georgecazenavette.github.io/mtt-distillation](http://georgecazenavette.github.io/mtt-distillation)

Image /page/0/Picture/6 description: The image shows a sequence of four images. The first image is a collage of six close-up photographs of scarlet macaws. The second image is a pattern of scarlet macaws. The third image is a gray arrow pointing to the right. The fourth image is a man wearing a suit jacket with a scarlet macaw pattern.

Figure 1. Our new method builds upon recent dataset distillation work [\[2\]](#page-4-0) and lets us distill classes into *tilable textures*. These textures can then be used for downstream tasks, such as pattern swatches for clothing. (Visualizations made with FAB3D: [https://fabric.tri3d.in/\)](https://fabric.tri3d.in/)

<span id="page-0-0"></span>

## Abstract

*Recent methods for Dataset Distillation are able to take in a large set of images of a specific class (e.g., from ImageNet) and synthesize a single image, such that a classifier trained on that image could perform similarly to one trained on the original dataset. It was noticed that the resulting "distilled images" are often quite visually pleasing. In this paper, we describe a simple method for generating tileable distilled textures by sampling random crops from a toroidal canvas of synthetic pixels while enforcing that all such crops serve as effective distilled training data. Such distilled textures not only summarize a given image category in a visually interesting way, but also allow for generation of infinite texture patterns suitable for printing on fabric, clothing, etc. This paper might be just the first step in making the ImageNet dataset into a fashion statement.*

## 1. Introduction

The task of dataset distillation [\[2,](#page-4-0) [2,](#page-4-0) [8,](#page-4-1) [9,](#page-4-2) [10,](#page-4-3) [11,](#page-4-4) [12,](#page-4-5) [13,](#page-4-6) [14\]](#page-4-7) involves creating a small synthetic dataset (as little as one image per class) such that a model trained on this synthetic dataset will have similar test-time performance as a model trained on the full original training set. This problem is particularly challenging, as a good solution must strike a delicate balance between compressing the visual information into just a few images, while still preserving the learnable discriminative features of each class. Unexpectedly, the results of the most recent method [\[2\]](#page-4-0) have shown an additional

property of being visually intriguing (see Figure [4B](#page-2-0)). In this short paper, we further explore dataset distillation as a tool for synthesizing visually interesting textures.

Until now, all existing methods of dataset distillation have focused on the synthesis of standard-sized distilled image(s) corresponding to each class. Here, we propose an alternative method that yields a more artistic yet still functional result. We consider a "canvas" of pixels for each class, which is twice as large as the input training images. At distillation time, we apply circular padding and take random crops from this canvas. By optimizing all such random crops to serve as effective training data, we see the emergence of categorybased textures that preserve continuity across their borders. While being aesthetically pleasing in their own right, the resulting images can also be applied to practical tasks, such as pattern swatches used to create clothing or wallpaper. We call our method Tileable Texture Distillation.

Our method can be thought of as a type of texture synthesis, but while classic patch-based synthesis methods, e.g. [\[5\]](#page-4-8), create a texture from a single source image, our method produces a result that captures the "essence" of an entire dataset class. Our way of synthesizing tileable textures is inspired by Wang tiles work of Cohen et al. [\[3\]](#page-4-9) (and their algorithm can easily be used to extend our work to non-periodic tiled textures). Our other source of inspiration is the classic Image Epitomes work [\[7\]](#page-4-10), but while epitomes capture the statistics necessary for *reconstruction* of an image, our method instead focuses on data needed for discrimination.

<span id="page-1-2"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: This image illustrates a machine learning training process comparing two methods. The top row shows "Real Training Data" consisting of various images of penguins. These images are processed and fed into a model labeled "For All Classes" for training, leading to a neural network. The bottom row depicts "Random Crops of Toroidal Canvas," which are processed with "Pad+Crop" to generate multiple cropped images of penguins. These are also fed into a model labeled "For All Classes" for training, resulting in another neural network. An arrow indicates "Similar Test Performance" between the two trained models.

Figure 2. Our method takes random crops of a padded distilled *canvas* instead of discrete images. Note: Our method distills all classes simultaneously, although only one (penguin) is shown in detail in this figure for simplicity.

<span id="page-1-1"></span>Image /page/1/Figure/2 description: The image displays a grid of ten different tiled patterns. The top row features five patterns: the first is a blue and green floral pattern, the second is a pink floral pattern, the third is a colorful parrot pattern, the fourth is a pattern of ducks on water, and the fifth is a pattern of penguins. The bottom row also has five patterns: the first is a blue pattern with dark shapes, the second is a yellow and purple floral pattern, the third is a brown and black spotted pattern, the fourth is a green pattern with dark blue and pink elements, and the fifth is a green and white snowy pattern.

Figure 3. ImageSquawk Distilled Textures Tiled 3x3

## 2. Preliminaries

Here, we briefly review our underlying distillation method: Dataset Distillation by Matching Training Trajectories (MTT) [\[2\]](#page-4-0). Similar to previous methods, MTT seeks to distill synthetic data that can still train a well-performing model.

While other methods attempt to optimize the synthetic data with respect to either a single gradient step [\[12,](#page-4-5) [14\]](#page-4-7) or the entire training process [\[8,](#page-4-1) [9,](#page-4-2) [11\]](#page-4-4), MTT considers many-step trajectories starting from varying points along pre-computed *expert trajectories* (those obtained by training on the full, real training set). On a high-level, MTT encourages models trained on synthetic data to mimic the behavior of the expert trajectory.

Specifically, MTT initialize initializes a student network at some point along an expert trajectory. The student network is then trained for many ( $N \approx 50$ ) iterations on the synthetic

data, and the "distillation loss" is calculated as the relative error between the student network's current parameters and those of a future timestep of the expert trajectory. The motivation behind this method is an effort to *directly* optimize the synthetic data such that it induces a similar training trajectory as the real training set, resulting in a final model that lies at a similar point in parameter space. For further details on the distillation method from which ours was adapted, we refer the reader to [\[2\]](#page-4-0).

## 3. Tilable Texture Distillation

Our method, Tilable Texture Distillation, builds upon MTT and offers a new mode of distillation. Instead of distilling individual, disjoint training images, we instead optimize a toroidal canvas of pixels such that any random crop of the canvas is a good training sample. As illustrated in Figure [2,](#page-1-0) our optimization objective is to ensure that a model trained

<span id="page-2-2"></span><span id="page-2-0"></span>Image /page/2/Picture/0 description: The image is a collage of 12 different pictures, arranged in two rows of six. The top row features various birds: a peacock displaying its tail, a pink flamingo, a red and blue macaw, a pelican standing on rocks in the water, a king penguin in the water, a bald eagle in flight, a toucan perched on a branch, an ostrich sitting on grass, a black swan swimming, and a white cockatoo. The bottom row displays an assortment of fruits and vegetables: a pineapple, bananas, a strawberry, oranges, lemons, a cut-open pomegranate, figs, red and orange bell peppers, cucumbers, and green apples.

(A) Real Training Data (showing 1 image per class)

Image /page/2/Figure/2 description: The image displays a grid of abstract, colorful images, possibly generated by a neural network. The top row contains 10 images, and the bottom row contains 8 images. The images are varied in color and texture, with some appearing to depict natural scenes like flowers, penguins, or landscapes, while others are more abstract. Below the grid, the text "(B) Cazenavette et al. [2]" is visible, indicating the source or context of these images.

Image /page/2/Picture/3 description: The image displays a grid of 12 abstract, colorful, and textured patterns. The top row features six distinct patterns: the first shows blue and white abstract shapes resembling flowers or plants; the second has pink and green floral patterns; the third is a busy pattern of small, dark figures on a blue water-like background; the fourth shows a pattern of dark, vertical shapes with yellow accents on a blue background; the fifth is a pattern of yellow and blue abstract shapes; and the sixth depicts a pattern of dark, vertical shapes with red accents on a green background. The bottom row also contains six patterns: the first is a blue and brown textured pattern; the second shows yellow and blue abstract shapes; the third is a dense pattern of red and green floral elements; the fourth is a bright orange pattern with some green; the fifth is a vibrant yellow pattern with green accents; and the sixth is a pattern of small green and white elements. The overall impression is a collection of diverse, visually stimulating abstract designs.

(C) Tilable Texture Distillation (Ours)

Figure 4. Traditional distillation methods take the real training data (A) and create disjoint synthetic training images (B). Our new method builds upon recent dataset distillation work [\[2\]](#page-4-0) and allows for the synthesis of class-based tileable textures (C).

<span id="page-2-1"></span>Image /page/2/Figure/6 description: The image displays a progression of four square images, each labeled with an iteration count. The first image, labeled 'Iteration: 0', is a solid gray square with a subtle grainy texture. The subsequent images show the development of a colorful, abstract pattern. 'Iteration: 1k' reveals a chaotic mix of colors including purple, green, yellow, and blue, with no discernible shapes. 'Iteration: 5k' shows a similar pattern but with a slightly more organized, repeating arrangement of colors. The final image, 'Iteration: 50k', presents a more defined pattern resembling foliage or flowers, with distinct blue elements scattered across a background of green and brown textures, suggesting a generative process evolving over time.

Figure 5. We initialize our synthetic canvases from Gaussian noise. Structure emerges after several hundred iterations, but it takes tens of thousands until convergence.

on random crops of our distilled canvases will perform similarly to a model trained on the real training set.

To achieve this effect, we first apply *circular padding* in both the  $x$  and  $y$  directions. By always considering the image to be circular padded, we effectively induce a toroidal topology on our synthetic canvas. Where MTT would use the disjoint synthetic samples to train the student trajectory, we instead train our student trajectory on *random crops* of our padded synthetic canvases, including such crops that would span the seams of the torus.

While the random cropping alone induces a texturized appearance in our synthetic canvas, the circular padding ensures that they will also be *tilable*. Our loss function naturally encourages each "image" fed through the student network to be continuous. By using circular padding, we enforce that the patches that span the seams of the torus (i.e. the edges of the image) to also be continuous. This has the corollary effect of making our images tileable, as seen in Figure [3.](#page-1-1) Note that both padding and cropping are differentiable operations.

Please refer to our Appendix (Alg. 1) for more details.

## 4. Experiments

Since we are focusing on visual (qualitative) results in this work instead of classification performance, we chose to distill higher-resolution imagery than in the MTT paper [\[2\]](#page-4-0). Specifically, for each class, we distilled a  $512 \times 512$  toroidal canvas wherein each  $256 \times 256$  patch is optimized to be a good training sample. For comparison, the largest images distilled in MTT were  $128 \times 128$ .

We stay with the precedent set by other dataset distillation work and use ConvNet models [\[6\]](#page-4-11). Since our patches are  $256 \times 256$ , we use a depth-6 ConvNet for our distillation, following the pattern seen in  $DM$  [\[13\]](#page-4-6) and MTT [\[2\]](#page-4-0).

We distill the ImageSquawk subset of ImageNet [\[4\]](#page-4-12) as

<span id="page-3-1"></span><span id="page-3-0"></span>Image /page/3/Picture/0 description: The image displays four individuals modeling different outfits against a plain white background. From left to right, the first person is a woman wearing a long-sleeved, V-neck top with a floral pattern in shades of green, pink, and red, paired with blue jeans. The second person is a man in a patterned blue blazer with a black vest, black shirt, and black pants. The third person is a woman wearing a short-sleeved, knee-length dress with a colorful floral print, holding a red clutch. The fourth person is a man wearing a short-sleeved collared shirt with a geometric pattern in blue, white, and yellow, paired with light blue jeans.

Figure 6. By distilling classes into tileable textures, we can apply our synthetic data to cases where such tileability is required, such as fabric pattern swatches. From left to right, we see our flamingo, eagle, macaw, and penguin textures applied to clothing using FAB3D [\[1\]](#page-4-13)

introduced in [\[2\]](#page-4-0). We chose this dataset with the thought that the various colors and patterns of the birds' plumage would make for visually appealing textures. Specifically, ImageSquawk is composed of the peacock, flamingo, macaw, pelican, penguin, eagle, toucan, ostrich, black swan, and cockatoo classes. We also distill ImageFruit, which contains pineapple, banana, strawberry, orange, lemon, pomegranate, fig, bell pepper, cucumber, and granny smith.

### 4.1. Results

We present the result of our main experiment in Figure [1.](#page-0-0) Our method (b) can synthesize texture images with no centering bias while still resembling the distinct features of their respective classes (a), which makes them potentially useful in designing new clothes (c).

In Figure [3,](#page-1-1) we see that our distilled textures seamlessly tile together into a larger, continuous image. It is possible to create larger tiles without increasing memory cost simply by distilling a larger canvas while keeping the underlying patch size the same. However, this method would significantly increase training time since each patch would be sampled less frequently, requiring more total iterations for convergence.

As far as training (distillation) dynamics, we see coarse structure emerge relatively early in the optimization, within the first several hundred iterations. However, it takes many more iterations, on the order of tens of thousands, to synthesize a more defined structure in the texture and remove the high-frequency noise components, as seen in Figure [5.](#page-2-1)

Once we have our converged toroidal textures, we can apply them to domain-relevant tasks, such as pattern swatches for fabrics. Using FAB3D [\[1\]](#page-4-13), we visualize our textures as applied to clothing. FAB3D has knowledge of the clothing article's underlying topology, so it makes full use of our textures' tileability, as seen in Figure [6.](#page-3-0)

## 5. Discussion

In this work, we introduced an extension to a recent dataset distillation algorithm that allows us to distill tileable, class-based textures. While all distillation methods to date have solely focused on classification performance [\[8,](#page-4-1) [9,](#page-4-2) [10,](#page-4-3) [11,](#page-4-4) [12,](#page-4-5) [13,](#page-4-6) [14\]](#page-4-7), our new method is the first to change the overall objective to synthesize something other than disjoint training samples.

Tilable Texture Distillation takes random crops across an induced toroidal canvas and enforces them to all be good training examples for discriminating the respective class. It gives us a unique way of synthesizing tileable class-based textures for down-stream use. We also include code allowing users to easily distill textures for their own classes of choice.

By presenting a dataset distillation method with an end goal other than training discriminative models, we hope to encourage the creative use of dataset distillation methods for other novel tasks.

Limitations. As we continue to distill higher-resolution images, the process becomes more computationally costly, both spatially and temporally. For MTT specifically, training larger models to obtain the expert trajectories (i.e., depth-6 ConvNet versus depth-3 ConvNet) takes much longer, and the checkpoints take up significantly more space when stored on disk. Furthermore, distilling quadratically more pixels (i.e., doubling the resolution) while keeping the patch size the same requires *many* more iterations before convergence, especially when performing our new random crop method.

Acknowledgements. We are grateful to the artist Danielle Baskin for her suggestion to that we apply our distillation method to creating fabric patterns. This work is supported, in part, by the NSF Graduate Research Fellowship under Grant No. DGE1745016 and grants from J.P. Morgan Chase, IBM, and SAP.

## References

- <span id="page-4-13"></span>[1] Tri3d fabric visualiser. <https://fabric.tri3d.in/>. [4](#page-3-1)
- <span id="page-4-0"></span>[2] George Cazenavette, Tongzhou Wang, Alexei A. Efros, Antonio Torralba, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-2) [4](#page-3-1)
- <span id="page-4-9"></span>[3] Michael F Cohen, Jonathan Shade, Stefan Hiller, and Oliver Deussen. Wang tiles for image and texture generation. *ACM TOG*, 22(3):287–294, 2003. [1](#page-0-1)
- <span id="page-4-12"></span>[4] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009. [3](#page-2-2)
- <span id="page-4-8"></span>[5] Alexei A Efros and William T Freeman. Image quilting for texture synthesis and transfer. In *SIGGRAPH*, pages 341–346, 2001. [1](#page-0-1)
- <span id="page-4-11"></span>[6] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *CVPR*, 2018. [3](#page-2-2)
- <span id="page-4-10"></span>[7] Nebojsa Jojic, Brendan J Frey, and Anitha Kannan. Epitomic analysis of appearance and shape. In *ICCV*, volume 2, pages 34–34. IEEE Computer Society, 2003. [1](#page-0-1)
- <span id="page-4-1"></span>[8] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *ICLR*, 2020. [1,](#page-0-1) [2,](#page-1-2) [4](#page-3-1)
- <span id="page-4-2"></span>[9] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *NeurIPS*, 2021. [1,](#page-0-1) [2,](#page-1-2) [4](#page-3-1)
- <span id="page-4-3"></span>[10] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022. [1,](#page-0-1) [4](#page-3-1)
- <span id="page-4-4"></span>[11] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2,](#page-1-2) [4](#page-3-1)
- <span id="page-4-5"></span>[12] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*. PMLR, 2021. [1,](#page-0-1) [2,](#page-1-2) [4](#page-3-1)
- <span id="page-4-6"></span>[13] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021. [1,](#page-0-1) [3,](#page-2-2) [4](#page-3-1)
- <span id="page-4-7"></span>[14] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2020. [1,](#page-0-1) [2,](#page-1-2) [4](#page-3-1)
<span id="page-0-0"></span>

# POINT CLOUD DATASET DISTILLATION

Anonymous authors

Paper under double-blind review

# ABSTRACT

This study introduces dataset distillation (DD) tailored for 3D data, particularly point clouds. DD aims to substitute large-scale real datasets with a small set of synthetic samples while preserving model performance. Existing methods mainly focus on structured data such as images. However, adapting DD for unstructured point clouds poses challenges due to their diverse orientations and resolutions in 3D space. To address these challenges, we theoretically demonstrate the importance of matching rotation-invariant features between real and synthetic data for 3D distillation. We further propose a plug-and-play point cloud rotator to align the point cloud to a canonical orientation, facilitating the learning of rotation-invariant features by all point cloud models. Furthermore, instead of optimizing fixedsize synthetic data directly, we devise a point-wise generator to produce point clouds at various resolutions based on the sampled noise amount. Compared to conventional DD methods, the proposed approach, termed DD3D, enables efficient training on low-resolution point clouds while generating high-resolution data for evaluation, thereby significantly reducing memory requirements and enhancing model scalability. Extensive experiments validate the effectiveness of DD3D in shape classification and part segmentation tasks across diverse scenarios, such as cross-architecture and cross-resolution settings.

# 1 INTRODUCTION

**029 030 031**

**032 033 034 035 036 037 038** Dataset distillation (DD) [\(<PERSON> et al.,](#page-12-0) [2018\)](#page-12-0) aims to distill the knowledge of a large-scale dataset into a few synthetic samples, where the models trained on the real and synthetic data will have comparable performance. By doing so, DD significantly reduces the computational cost of training neural networks from scratch. Due to its remarkable efficiency and effectiveness, DD has been used in a variety of domains, such as image [\(Zhao et al.,](#page-12-1) [2021;](#page-12-1) [Zhao & Bilen,](#page-12-2) [2023;](#page-12-2) [Cazenavette et al.,](#page-10-0) [2022\)](#page-10-0), video [\(Wang et al.,](#page-12-3) [2024\)](#page-12-3), text [\(Maekawa et al.,](#page-11-0) [2023\)](#page-11-0) etc. Despite great progress, existing DD methods only succeed on structured 1D and 2D data, while the distillation of unstructured 3D data, *e.g*., point cloud, is still under-explored.

**039 040 041 042 043 044** Point cloud data exists in large quantities in various fields. For example, MVPNet [\(Yu et al.,](#page-12-4) [2023\)](#page-12-4) scans more than 87K point clouds from real-world videos for machine vision, Objaverse-XL [\(Deitke](#page-10-1) [et al.,](#page-10-1) [2023\)](#page-10-1) provides more than 10M high-quality 3D assets, and [Qu et al.](#page-11-1) [\(2022\)](#page-11-1) constructs a 100M dataset for high-energy physics, where particles are modeled as point clouds. Training on these datasets from scratch is time-consuming and resource-intensive, requiring more efficient approaches. However, several reasons prevent existing DD frameworks from generalizing to 3D point clouds.

**045 046 047 048 049 050 051 052 053** First, point clouds with different orientations represent the same semantic information, *e.g*., shapes. However, existing DD methods do not take the symmetry of data into account, which cannot handle the randomly rotated data and result in sub-optimal performance. As shown in Figure [1a,](#page-1-0) directly applying DD to the point clouds with different orientations cannot obtain meaningful synthetic data. Second, point clouds have flexible resolutions, *i.e*., the number of points, depending on specific models and applications. Generally, a larger resolution encodes more fine-grain information but also increases the computational costs [\(Huang et al.,](#page-10-2) [2024;](#page-10-2) [Qiu et al.,](#page-11-2) [2021\)](#page-11-2). However, existing DD methods initialize the synthetic data as a fixed-size tensor, which cannot be customized for different point cloud models. Moreover, the memory budget for fixed-size tensors will increase rapidly when dealing with dense-resolution scenes, *e.g*., segmentation [\(Chang et al.,](#page-10-3) [2015;](#page-10-3) [Ren et al.,](#page-11-3) [2022\)](#page-11-3).

<span id="page-1-0"></span>Image /page/1/Figure/1 description: The image displays two diagrams, (a) DD for point clouds and (b) DD3D for point clouds. Diagram (a) shows three point clouds of airplanes, each undergoing a 'Distill' process resulting in a dense, less structured point cloud. A 'Match' arrow connects this distilled point cloud back to the original airplane point clouds. Diagram (b) starts with three rotated point clouds of airplanes. These are then subjected to a 'Distill' process, followed by a 'Generate' process mediated by a 'Generator' (represented by stacked rectangles). The 'Generate' process results in three output point clouds with varying densities, labeled as (256 Points), (512 Points), and (1024 Points). A 'Match' arrow connects the output of the 'Distill' process to the input rotated airplane point clouds.

Figure 1: Differences between vanilla DD and DD3D when distilling 3D point clouds.

Once the weaknesses of existing methods are identified, it is natural to ask: *How can we build a distillation framework that overcomes the orientation and resolution issues of 3D point clouds?* To answer this question, we first theoretically prove that random rotations weaken the principle components of real data, thereby degenerating the distillation performance. Based on this discovery, we propose DD3D, the first DD framework for 3D point clouds, illustrated in Figure [1b.](#page-1-0) Specifically, DD3D first uses a rotator to convert the point cloud into a canonical orientation by learning a rotationequivariant projection matrix to offset random rotation. Then, the knowledge of rotation-invariant data is distilled into a point-wise generator to predict the point coordinates from noise, where the resolution is based on the number of sampled noises. Finally, the rotator and generator are jointly optimized by minimizing the gradient differences between the real and synthetic data.

**077 078 079 080 081 082 083** The contributions are summarized as follows. (1) We propose the first 3D distillation framework, DD3D, which can eliminate the influence of random rotations and synthesize point clouds at arbitrary resolutions. (2) We theoretically prove that matching the rotation-invariant features can preserve the principal components of real data and prevent data degeneration. (3) DD3D can be trained with low-resolution point clouds and generates high-resolution data for evaluation, significantly reducing memory usage and enhancing model scalability. (4) Extensive experiments on shape classification and part segmentation tasks validate the effectiveness of DD3D over baselines.

**084 085**

**086**

# 2 RELATED WORK

**087 088 089 090 091 092 093 094 095 096 097 098 099 100 101** Dataset Distillation. Research on DD can be roughly divided into two directions. The first is to explore advanced matching objectives to improve the distillation performance. For example, performance matching [\(Wang et al.,](#page-12-0) [2018\)](#page-12-0), gradient matching [\(Zhao et al.,](#page-12-1) [2021;](#page-12-1) [Zhao & Bilen,](#page-12-5) [2021\)](#page-12-5), distribution matching [\(Zhao & Bilen,](#page-12-2) [2023;](#page-12-2) [Wang et al.,](#page-12-6) [2022\)](#page-12-6), trajectory matching [\(Cazenavette et al.,](#page-10-0) [2022;](#page-10-0) [Guo et al.,](#page-10-4) [2024;](#page-10-4) [Du et al.,](#page-10-5) [2023\)](#page-10-5) and feature regression [\(Zhou et al.,](#page-12-7) [2022;](#page-12-7) [Loo et al.,](#page-11-4) [2022;](#page-11-4) [Nguyen et al.,](#page-11-5) [2021\)](#page-11-5). On the other hand, some methods innovate efficient data parameterizations to avoid directly optimizing the synthetic data. For example, neural networks [\(Liu et al.,](#page-11-6) [2022\)](#page-11-6), spectral representation [\(Shin et al.,](#page-11-7) [2023\)](#page-11-7), linear transformation [\(Deng & Russakovsky,](#page-10-6) [2022\)](#page-10-6), and up-sampling [\(Kim et al.,](#page-10-7) [2022\)](#page-10-7). Among them, a special parameterization technique is to distill the knowledge into a generative model [\(Zhao & Bilen,](#page-12-8) [2022;](#page-12-8) [Wang et al.,](#page-12-9) [2023;](#page-12-9) [Zhang et al.,](#page-12-10) [2023;](#page-12-10) [Cazenavette et al.,](#page-10-8) [2023;](#page-10-8) [Zhang et al.,](#page-12-11) [2024\)](#page-12-11), which can generate diverse synthetic data with unlimited samples. Although valid, these methods rely on the prior knowledge of generative models pre-trained on large-scale datasets, which is not feasible for point clouds. A recent work<sup>[1](#page-0-0)</sup> also applies GM to point cloud data. However, neither of them considers the orientation and resolution issues. For a more comprehensive introduction to DD, please refer to the recent surveys [\(Yu et al.,](#page-12-12) [2024;](#page-12-12) Lei  $\&$ [Tao,](#page-10-9) [2024;](#page-10-9) [Geng et al.,](#page-10-10) [2023;](#page-10-10) [Sachdeva & McAuley,](#page-11-8) [2023\)](#page-11-8).

**102 103 104 105 106 Point Cloud Analysis.** Deep learning on point clouds plays a vital role in 3D data analysis [\(Guo](#page-10-11) [et al.,](#page-10-11) [2021b\)](#page-10-11). Traditional methods can be classified into three categories: Point-based methods, *e.g*., PointNet [\(Qi et al.,](#page-11-9) [2017a\)](#page-11-9) and PointNet++ [\(Qi et al.,](#page-11-10) [2017b\)](#page-11-10), convolution-based methods, *e.g*., PointCNN [\(Li et al.,](#page-10-12) [2018\)](#page-10-12) and PointConv [\(Wu et al.,](#page-12-13) [2019\)](#page-12-13), and relation-based methods, *e.g*., DGCNN [\(Wang et al.,](#page-12-14) [2019\)](#page-12-14) and Point Transformer [\(Guo et al.,](#page-10-13) [2021a\)](#page-10-13). However, these methods

<sup>1</sup><https://github.com/kghandour/dd3d>

**108 109 110 111 112 113 114 115 116** are rotation-sensitive and cannot handle point clouds with different orientations. Some advanced methods are designed to learn rotation-equivariant or invariant features, such as vector neuron [\(Deng](#page-10-14) [et al.,](#page-10-14) [2021\)](#page-10-14), spherical harmonic [\(Poulenard et al.,](#page-11-11) [2019\)](#page-11-11), tensor field [\(Thomas et al.,](#page-11-12) [2018\)](#page-11-12), and graph features [\(Kim et al.,](#page-10-15) [2020;](#page-10-15) [Zhao et al.,](#page-12-15) [2019\)](#page-12-15). However, these methods introduce additional operators and cannot preserve the original geometric information, *i.e*., coordinates. Another way is to project point clouds into the same orientation. For example, principal component analysis (PCA) leverages the eigenvectors of the covariance matrix to transform point clouds into the direction with maximum variance. But this approach suffers from the sign-ambiguity issue [\(Xiao et al.,](#page-12-16) [2020;](#page-12-16) [Yu](#page-12-17) [et al.,](#page-12-17) [2020;](#page-12-17) [Li et al.,](#page-10-16) [2021\)](#page-10-16).

**117 118**

**119 120 121**

**131 132 133**

**137**

- 3 BACKGROUND
- 3.1 PRELIMINARY

**122 123 124 125 126 127 128 Task Formulation.** Suppose that  $\mathcal{T} = \{(\mathcal{C}_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  is a large-scale training dataset, where  $\mathcal{C}_i$  is a point cloud with label  $y_i$  for the shape classification task. Each point cloud has n points, represented as  $C = \{P, V\}$ , where  $P \in \mathbb{R}^{n \times 3}$  represents the 3D coordinates of points and  $V \in \mathbb{R}^{n \times v}$  indicates the part to which the point belongs in segmentation task and  $v$  is the number of parts. The goal of DD3D is to synthesize a much smaller point cloud dataset  $\mathcal{S} = \{(\mathcal{C}_j, y_j)\}_{j=1}^{|\mathcal{S}|}$ , where  $|\mathcal{S}| \ll |\mathcal{T}|$ , such that a classification or segmentation model  $f_\theta$  trained on  $\mathcal T$  and  $\mathcal S$  will have comparable performance. Other tasks, such as detection, are left for future studies.

**129 130** Dataset Distillation. In order to effectively optimize the synthetic data, existing DD methods adopt a bi-level optimization paradigm, which can be formulated as:

$$
\min_{\mathcal{S}} \mathcal{L}_{DD}\left(f_{\theta*}(\mathcal{S}), f_{\theta*}(\mathcal{T})\right) \quad \text{s.t.} \quad \theta* = \underset{\theta}{\arg\min} \mathcal{L}_{cls}\left(f_{\theta}(\mathcal{S}), Y^{\mathcal{S}}\right),\tag{1}
$$

**134 135 136** where the inner loop updates the model  $f_\theta$  on the synthetic data, and the outer loop optimizes the synthetic data. In particular,  $\mathcal{L}_{DD}$  is a metric that measures the distance between real and synthetic data. For example, gradient matching [\(Zhao et al.,](#page-12-1) [2021\)](#page-12-1) minimizes the gradient differences.

### 3.2 DATASET DISTILLATION WITH ROTATIONS

**139 140 141 142** Before detailing the proposed method, we first give a general analysis of how rotations affect the performance of DD. Let  $X_{\mathcal{S}} \in \mathbb{R}^{|\mathcal{S}| \times d}$ ,  $X_{\mathcal{T}} \in \mathbb{R}^{|\mathcal{T}| \times d}$  denote the representations learned by  $f_{\theta}$  on the synthetic data and real training data, respectively, and  $d$  is the hidden dimension.

Theorem 1. *Assume the classifier is a linear layer* W *and* Lcls *can be simplified to the mean-squared error*  $||XW - Y||_F^2$ . The objective of gradient matching is equal to variance preserving:

<span id="page-2-0"></span>
$$
\min_{\mathcal{S}} \mathcal{L}_{GM} = \min_{\mathcal{S}} \mathcal{D} \left( \nabla_W \mathcal{L}_{cls}^{\mathcal{S}}, \nabla_W \mathcal{L}_{cls}^{\mathcal{T}} \right) \quad \Rightarrow \quad \min_{\mathcal{S}} \left\| X_{\mathcal{S}}^{\top} X_{\mathcal{S}} - X_{\mathcal{T}}^{\top} X_{\mathcal{T}} \right\|_F^2, \tag{2}
$$

*where*  $\mathcal{D}$  *is a distance metric and*  $\nabla_W$  *is the gradient with respect to*  $W$ *.* 

**149 150 151 152** Theorem [1](#page-2-0) reveals that synthetic data preserves the variance information of real data. We then analyze how random rotations affect the variance of real data. Without loss of generality, we assume that  $f_\theta$ is rotation-equivariant, *i.e.*,  $f_{\theta}$  ( $PR$ ) =  $f_{\theta}$  ( $P$ ) R, where  $R \in SO(d)$  is a random rotation matrix.

**Theorem 2.** Assume  $X_{\mathcal{T}}$  follows a d-dimensional multivariate Gaussian distribution  $\mathcal{N}(\mu, \Sigma)$ . Let  $X'_{\mathcal{T}}$  be the rotated representations of  $X_{\mathcal{T}}$  such that:

<span id="page-2-1"></span>
$$
\lambda_{max}\left(\mathbb{E}\left[X_{\mathcal{T}}^{\prime\top}X_{\mathcal{T}}^{\prime}\right]\right) \leq \lambda_{max}\left(\mathbb{E}\left[X_{\mathcal{T}}^{\top}X_{\mathcal{T}}\right]\right) \quad \Rightarrow \quad \sigma_{max}\left(\mathbb{E}\left[X_{\mathcal{T}}^{\prime}\right]\right) \leq \sigma_{max}\left(\mathbb{E}\left[X_{\mathcal{T}}\right]\right), \quad (3)
$$

**157 158** *where*  $\lambda_{max}$  *and*  $\sigma_{max}$  *are the maximum eigenvalues and singular values, respectively.* 

**159 160 161** Theorem [2](#page-2-1) states that random rotations reduce the maximum singular value of the data representations, implying that the principle component of  $X<sub>T</sub>$  is weakened. In this case, the synthetic data cannot effectively capture the distribution of the real data, degenerating model performance. All proofs can be seen in Appendix [A.](#page-13-0)

# 4 THE PROPOSED METHOD

**162 163**

**173 174**

**186**

**191 192**

## 4.1 PLUG-AND-PLAY POINT CLOUD ROTATOR

**166 167 168 169** The above analysis demonstrates that learning rotation-invariant representations is crucial for point cloud distillation. However, a considerable part of point cloud models do not have this property. To solve this problem, we propose a plug-and-play point cloud rotator to transform the point clouds into their canonical view, enabling all methods to learn rotation-invariant representations.

**170 171 172 Rotation-equivariant.** We can leverage the orthogonality of the rotation matrix to eliminate its influence, *i.e.*,  $RR^{\top} = I$ , where PCA is a typical method:

$$
\frac{1}{n} \sum (PR - \overline{P}R)^{\top} (PR - \overline{P}R) = R^{\top} U \Lambda U^{\top} R \Rightarrow (PR)(R^{\top} U) = PU,
$$
\n(4)

**175 176 177 178 179 180** where  $\overline{P}$  is the center of P and U represents the eigenvectors of the covariance matrix. Notably, the projection  $R<sup>†</sup>U$  is equivariant to the rotation of coordinates, and therefore  $(PR)(R<sup>†</sup>U) = PU$ is rotation-invariant. However, the eigenvectors have the *sign ambiguity* issue, *i.e.*,  $-u_i$  is also a valid eigenvector. As a result, the canonical view  $PU$  is not unique and has 8 ambigui-ties in 3D space [\(Xiao et al.,](#page-12-16) [2020;](#page-12-16) [Yu et al.,](#page-12-17) [2020\)](#page-12-17), *i.e.*,  $PUQ = P[\pm u_1, \pm u_2, \pm u_3]$ , where  $\{Q \in \mathbb{R}^{3 \times 3} | Q_{ii} = \{1, -1\}$ ,  $Q_{ij} = 0, \forall i \neq j\}$  is a random reflection matrix.

**181 182 183 184 185 Sign-invariant.** Our rotator  $r : \mathbb{R}^{n \times 3} \to \mathbb{R}^{n \times 3}$  is mainly designed to improve the performance of PCA by solving the sign ambiguity problem. Specifically, the rotator aims to learn a sign-equivariant reflection matrix  $\overline{Q}$  for each point cloud such that  $PUQ \cdot \overline{Q} = PU$  is sign-invariant. Specifically, the rotator first lifts the scalar coordinates to the vector representations:

$$
H = \left[\sin(\pm PU), \sin(\pm 2PU) \cdots \sin(\pm tPU)\right]^\top = \left[\sin(PU), \sin(2PU) \cdots \sin(tPU)\right]^\top Q, \quad (5)
$$

**187 188 189 190** where  $HQ \in \mathbb{R}^{n \times t \times 3}$  is the sign-equivariant representations,  $sin(\cdot)$  is the sine function and t is the period of Fourier features. An average pooling is then applied on  $H$  to learn the representations of the whole point cloud. Finally, a learnable vector  $w \in \mathbb{R}^t$  is used to decode the reflection matrix. The overall architecture of the rotation is formulated as follows:

$$
r(P) = PUQ \cdot \overline{Q} = PUQ \cdot \text{Sign}(w \cdot \text{Pool}(HQ)),\tag{6}
$$

**193 194 195 196** where "Sign" means the signs of a matrix. The reflection matrix  $\overline{Q}$  has the same signs as  $Q$  because the sinusoidal features, pooling function, and linear decoder preserve the sign information of  $HQ$ , which can solve the sign ambiguity and learn sign-invariant representations.

**197 198 199 200 201** Other methods. There are different approaches to learning rotation-invariant representations, such as vector neruon [\(Deng et al.,](#page-10-14) [2021\)](#page-10-14) and graph features [\(Kim et al.,](#page-10-15) [2020\)](#page-10-15). However, these methods break the original point coordinates, which are not easy to incorporate with other models. On the other hand, some methods try to solve the sign ambiguity by using pooling [\(Yu et al.,](#page-12-17) [2020\)](#page-12-17) and attention [\(Xiao et al.,](#page-12-16) [2020;](#page-12-16) [Li et al.,](#page-10-16) [2021\)](#page-10-16) mechanisms, which is inefficient as they need to calculate the representations for all ambiguous views.

**202 203 204**

## 4.2 POINT-WISE GENERATOR

**205 206 207 208 209 210 211** In addition to the orientation, point clouds often have different resolutions, which also need to be considered in the distillation process. Traditional DD methods update the synthetic data in an *explicit* way, *i.e*., directly optimizing the fixed-size tensors, which is unsuitable for point cloud synthesizing. On the other hand, the implicit neural representation (INR) methods [\(Sitzmann et al.,](#page-11-13) [2020;](#page-11-13) [Park](#page-11-14) [et al.,](#page-11-14) [2019\)](#page-11-14) show great potential in generating data with arbitrary resolutions [\(Chen et al.,](#page-10-17) [2021;](#page-10-17) [Singh et al.,](#page-11-15) [2023\)](#page-11-15). Generally, INR predicts the signals of given coordinates, but the coordinates of the synthetic point clouds are unknown.

**212 213 214 215 Point Denoising.** Our solution is to use INR as a generator  $g : \mathbb{R} \to \mathbb{R}^3$ , whose input is random noise and output is the coordinates of a point. This means that we treat the noise as a special continuous coordinate, and the generator is used to obtain "3D signals" by denoising the noise. It is also worth noting that the generator adopts a point-wise paradigm rather than an instance-wise generation. Therefore, the amount of points is the same as the sampled noise, which allows us to generate point

<span id="page-4-0"></span>Image /page/4/Figure/1 description: The image depicts a process flow diagram. It starts with a column of black dots labeled "Noise." An arrow labeled "Partition" points to a column of dots colored with a gradient from yellow at the top to purple at the bottom, labeled "Labels." Another arrow points from "Labels" to a stylized representation of a neural network labeled "(Generator)." From the generator, two arrows diverge. The top arrow leads to a point cloud shaped like an airplane, with colors corresponding to the "Labels" column, and is labeled "Global Match." An arrow then points from this to a more refined airplane shape, also colored, labeled "Global Match." The bottom arrow from the generator leads to a point cloud shaped like a wing, colored blue, and is labeled "Local Match." An arrow then points from this to a split wing shape, also colored blue, labeled "Local Match."

Figure 2: Illustration of DD3D for part segmentation task. Each noise is first pre-partitioned into different parts according to its value, *e.g*., the noise within (0, 0.45) is marked as fuselage. Then the generator maps the noise into coordinates to match the global (shape) and local (part) information.

clouds with infinite resolution. We choose SIREN [\(Sitzmann et al.,](#page-11-13) [2020\)](#page-11-13) as the generator, which can be formulated as:

 $g = [\Phi_1 \circ \Phi_2 \circ \cdots \circ \Phi_L] W_P, \quad \Phi_i = \sin(z_i w_i + b_i),$  (7)

**234 235 236 237** where L is the number of layers,  $\circ$  denotes the cascade of neural networks,  $\Phi_i$  is a multi-layer perceptron (MLP) with sine activation function in the *i*-th layer,  $z_1 \sim \mathcal{U}(0, 1)$  is the input noise, and  $\hat{W}_P \in \mathbb{R}^{d \times 3}$  is the decoder to generate 3D coordinates of points. Notably, we use uniform distribution instead of Gaussian distribution, as INR needs the input to be normalized within [0, 1].

**238 239 240 241 242** Conditional Modulating. The implicit generator can synthesize point clouds with arbitrary resolution but lacks label information, which is crucial for DD because it concentrates on classification task [\(Yu](#page-12-12) [et al.,](#page-12-12) [2024;](#page-12-12) [Lei & Tao,](#page-10-9) [2024\)](#page-10-9). Therefore, we use a modulator  $c : \mathbb{R}^d \to \mathbb{R}^d$ , which is implemented as another cascaded MLP  $\Psi$ , to encode the label information and generate conditions for the point cloud generation:

$$
c = \Psi_1 \circ \Psi_2 \circ \cdots \circ \Psi_L, \quad \Psi_i = \text{ReLU}(m_i w'_i + b'_i), \tag{8}
$$

**244 245 246 247** where  $ReLU(\cdot) = max(0, \cdot), m_i \in \mathbb{R}^d$  denotes the conditional representations and  $m_1$  is a one-hot matrix, representing the label information. Assume that there are  $K$  classes in total, and each class has N synthetic point clouds, then  $m_1 \in \mathbb{R}^{KN}$  and  $w'_1 \in \mathbb{R}^{KN \times d}$ . The conditional representations are then used to modulate each layer of the generator. The overall architecture is formulated as:

$$
g \odot c = [(\Psi_1 \odot \Phi_1) \circ (\Psi_2 \odot \Phi_2) \circ \cdots \circ (\Psi_L \odot \Phi_L)] W, \tag{9}
$$

where ⊙ is the element-wise multiplication to modulate the frequency and phase of the features. For clarity, in the following sections, we use  $g(z, i)$  to denote  $g \odot c$  with the *i*-th condition.

## 4.3 DISTILLATION TASKS

Traditional DD methods mainly focus on the fundamental image classification task. To better evaluate the performance of 3D distillation, we not only conduct experiments on the basic shape classification task but also explore the challenging part segmentation task. Shape classification aims to assign each point cloud a label, emphasizing global information, while part segmentation predicts the label of each point, which is more fine-grained.

Shape Classification. The distillation objective of the shape classification task is defined as:

**243**

$$
\mathcal{L}_{shape} = \sum_{k=1}^{K} \mathcal{D} \left( \nabla \mathcal{L}_{cls} \left( f_{\theta} \circ r(B_k^{\mathcal{S}}), Y_k^{\mathcal{S}} \right), \nabla \mathcal{L}_{cls} \left( f_{\theta} \circ r(B_k^{\mathcal{T}}), Y_k^{\mathcal{T}} \right) \right), \tag{10}
$$

**263 264 265 266** where K is the total classes of shapes,  $B_k^{\mathcal{T}}$  and  $Y_k^{\mathcal{T}}$  are a batch of real training data and labels,  $B_k^S = g(z, k)$  denotes a batch of synthetic point clouds belonging to the k-th class, and  $Y_k^S$  represents the predefined synthetic labels.

**267 268 269** Part Segmentation. In the segmentation task, each shape is divided into several parts, *e.g*., an airplane can be divided into fuselage, wings, engine, and rear. Such fine-grained labels need to be predefined before distillation. Therefore, DD3D first partitions the noise into different parts based on its value and then feeds the noise into the generator and rotator sequentially to obtain the synthetic

**270 271 272 273** data. Intuitively, the synthetic data should encode both the global shapes and local geometry of the real data. Therefore, we propose global and local matching to match the gradients of the entire shape and individual parts, respectively. A toy example is shown in Figure [2.](#page-4-0)

**274** The distillation objective of global matching in the part segmentation task is defined as:

<span id="page-5-2"></span>
$$
\mathcal{L}_{part} = \sum_{k=1}^{K} \mathcal{D} \left( \nabla \mathcal{L}_{seg} \left( f_{\theta} \circ r(B_k^{\mathcal{S}}), V_k^{\mathcal{S}} \right), \nabla \mathcal{L}_{seg} \left( f_{\theta} \circ r(B_k^{\mathcal{T}}), V_k^{\mathcal{T}} \right) \right), \tag{11}
$$

**278 279 280 281 282** where  $V^{\mathcal{T}}$  and  $V^{\mathcal{S}}$  represents the real and synthetic part labels, respectively. To match the gradient of a specific part p, we apply an element-wise mask  $M_p$  on the segmentation labels, *i.e.*,  $V^{\mathcal{T}} = V^{\mathcal{T}} \odot M_p$ and  $V^{\mathcal{S}} = V^{\mathcal{S}} \odot M_p$ , to avoid interference from the gradients of other parts. In practice, we calculate local and global gradient matching alternately to preserve information of shapes and parts. See Algorithms [1,](#page-5-0) [2,](#page-5-1) [3,](#page-5-2) and Appendix [B](#page-13-1) for the algorithm diagrams and detailed descriptions.

<span id="page-5-0"></span>

| 284<br>285 |     | <b>Algorithm 1 DD3D</b> for classification                                                    |     | <b>Algorithm 3 DD3D</b> for part segmentation                                                                         |                                |
|------------|-----|-----------------------------------------------------------------------------------------------|-----|-----------------------------------------------------------------------------------------------------------------------|--------------------------------|
| 286        |     | <b>Input:</b> Training dataset $\mathcal{T}$                                                  |     | <b>Input:</b> Training dataset $T$                                                                                    |                                |
| 287        |     | <b>Output:</b> Model <i>f</i> , Rotator <i>r</i> , Generator <i>q</i>                         |     | <b>Output:</b> Model $f$ , Rotator $r$ , Generator $g$                                                                |                                |
| 288        |     | 1: for $k = 1, \dots, K$ do                                                                   |     | 1: for $k = 1, \dots, K$ do                                                                                           | $\triangleright$ Shape Classes |
| 289        | 2:  | Initialize $f, r, g \sim \theta_f, \theta_r, \theta_a$                                        | 2:  | Initialize $f, r, g \sim \theta_f, \theta_r, \theta_q$                                                                |                                |
|            | 3:  | repeat                                                                                        | 3:  | repeat                                                                                                                |                                |
| 290        | 4:  | Sample a batch $B_k^{\mathcal{T}}, Y_k^{\mathcal{T}} \sim \mathcal{T}$                        | 4:  | Sample a batch $B_k^{\mathcal{T}}, V_k^{\mathcal{T}} \sim \mathcal{T}$                                                |                                |
| 291        | 5:  | Sample noise $z_1 \sim \mathcal{U}(0, 1)$                                                     | 5:  | Sample noise $z_1 \sim \mathcal{U}(0, 1)$                                                                             |                                |
| 292        | 6:  | Generate $B_k^{\mathcal{S}} = g(z_1, k)W$                                                     | 6:  | Generate $V_k^{\mathcal{S}}$ by partitioning noise                                                                    |                                |
| 293        | 7:  | Compute $\nabla \mathcal{L}_{cls}^{\mathcal{S}}$ and $\nabla \mathcal{L}_{cls}^{\mathcal{T}}$ | 7:  | Generate $B_k^{\mathcal{S}} = g(z_1, k)W$                                                                             |                                |
| 294        | 8:  | Update $\theta_g$ with $\mathcal{L}_{shape}$                                                  | 8:  | Compute $\nabla \mathcal{L}_{seq}^{\mathcal{S}}$ and $\nabla \mathcal{L}_{seq}^{\mathcal{T}} \Rightarrow$ Shape Info. |                                |
| 295        | 9:  | repeat                                                                                        | 9:  | for $p \in k$ do $\Rightarrow$ Part belongs to a shape                                                                |                                |
| 296        | 10: | Update $\theta_f$ , $\theta_r$ with $\mathcal{L}_{cls}^S$                                     | 10: | Apply mask $M_p$ on $V_k^S$                                                                                           |                                |
| 297        | 11: | until inner-loop end                                                                          | 11: | Compute $\nabla \mathcal{L}_{seq}^S$ and $\nabla \mathcal{L}_{seq}^T \triangleright$ Part Info.                       |                                |
| 298        | 12: | <b>until</b> outer-loop end                                                                   | 12: | end for                                                                                                               |                                |
| 299        |     | $13:$ end for                                                                                 | 13: | Update $\theta_q$ with $\mathcal{L}_{part}$                                                                           |                                |
| 300        |     | Algorithm 2 DD3D for evaluation                                                               | 14: | repeat                                                                                                                |                                |
| 301        |     |                                                                                               | 15: | Update $\theta_f$ , $\theta_r$ with $\mathcal{L}_{sea}^S$                                                             |                                |
| 302        |     | 1: Generate $B^S, Y^S$ or $V^S$ via g                                                         | 16: | until inner-loop end                                                                                                  |                                |
| 303        |     | 2: Optimize $\theta_f$ and $\theta_r$ until convergence                                       | 17: | until outer-loop end                                                                                                  |                                |
|            |     | 3: Evaluate $f \circ r$ on the test dataset                                                   |     | $18:$ end for                                                                                                         |                                |
| 304        |     |                                                                                               |     |                                                                                                                       |                                |

<span id="page-5-3"></span><span id="page-5-1"></span>

## 4.4 DISCUSSION

**307 308 309 310 311** Complexity. The complexity of DD contains three parts: data parameterization, model forward, and data alignment. For a point cloud with n points, DD3D has an additional time complexity  $O(Lnd)$  to generate synthetic data, which makes the time overhead higher than that of vanilla DD methods. But if we consider down-sampling the points, the time complexity of all three parts can be reduced. See Section [5.5](#page-8-0) for a comprehensive comparison of the time and space overhead between DD and DD3D.

**312 313 314 315 316 317 318** Limitations. Unlike parameterizing data as an explicit matrix, DD3D leverages a generator to synthesize data, significantly reducing the computational costs and memory budget. However, a major drawback is that the generator cannot take original data as initialization, which may affect the convergence of the model. A comparison can be found in Section [5.4.](#page-8-1) Moreover, there are still some issues that remain unsolved. For example, existing methods cannot applied to tasks with continuous labels, such as detection, which limits their applications. Besides, how to make the synthetic datasets learn fine-grained details beyond shapes remains a challenge.

**319**

**305 306**

**275 276 277**

**283**

# 5 EXPERIMENTS

**320 321**

**322 323** We benchmark our method on two fundamental tasks of point cloud analysis: shape classification (Section [5.1\)](#page-6-0) and part segmentation (Section [5.2\)](#page-6-1), followed by a series of analyses, including generalization (Section [5.3\)](#page-7-0), ablation (Section [5.4\)](#page-8-1), and visualization (Section [5.6\)](#page-9-0).

**324 325 326 327 328 329 330 331 332 Datasets.** We employ three datasets of different scales for the shape classification task:  $(i)$  ScanObjectNN (*OBJ BG*) [\(Uy et al.,](#page-11-16) [2019\)](#page-11-16) is the smallest dataset but consists of real-world data, which is challenging to distillate. (ii) ModelNet40 [\(Wu et al.,](#page-12-18) [2015\)](#page-12-18) is a larger synthetic dataset generated from CAD models.  $(iii)$  MVPNet [\(Yu et al.,](#page-12-4) [2023\)](#page-12-4) is the largest dataset, containing 87K point clouds scanned from real-world videos. We use its subset MVPNet100, which includes data from the 100 most populous categories, to alleviate the influence of long-tail distribution, similar to the CAFIR-100 dataset<sup>[2](#page-0-0)</sup>. For the part segmentation task, we follow [Qi et al.](#page-11-9) [\(2017a\)](#page-11-9) and choose ShapeNet-part [\(Yi](#page-12-19) [et al.,](#page-12-19) [2016\)](#page-12-19) dataset for evaluation. All the datasets use the standard data splits, and their detailed statistic information can be found in Appendix [C.](#page-14-0)

**333 334 335 336 337 338 339 340** Data Preparation and Metrics. Each cloud contains 1,024 points and is normalized into a unit sphere. We consider two settings: *Aligned* and *Rotated*. In the *Aligned* setting, both training and test point clouds have the same orientation, while in the *Rotated* setting, both training and test data are rotated randomly. For the rotated data, we project them along the direction of maximum variance during pre-processing. Note that the point clouds in MVPNet only have 180◦ views, so we do not randomly rotate them. The details of pre-processing can be found in Appendix [C.](#page-14-0) We report the Overall Accuracy (OA, %) of each method in the shape classification task and the average class intersection of union (IoU, %) in the part segmentation task.

**341 342 343 344 345 346** Baselines. To demonstrate the effectiveness of our method, we choose two types of baselines: (1) Coreset-based methods, including Random, Herding [\(Welling,](#page-12-20) [2009\)](#page-12-20) and K-Center [\(Sener & Savarese,](#page-11-17) [2018\)](#page-11-17). (2) Distillation-based methods, including Gradient Matching (GM) [\(Zhao et al.,](#page-12-1) [2021\)](#page-12-1), Distribution Matching (DM) [\(Zhao & Bilen,](#page-12-2) [2023\)](#page-12-2), and Trajectory Matching (TM) [\(Cazenavette](#page-10-0) [et al.,](#page-10-0) [2022\)](#page-10-0). We choose GM as the distillation objection for DD3D as it makes a trade-off between time and memory consumption. See Appendix  **for the detailed hyperparameters.** 

**347 348 349 350 351 352 353** Backbones. We provide a lightweight PointNet as the distillation backbone, which abandons the transformation network because previous literature [\(Yu et al.,](#page-12-12) [2024\)](#page-12-12) pointed out that complex network architecture may lead to degraded distillation performance. See Appendix [E](#page-15-0) for more details. In the evaluation stage, we adopt various advanced backbones to evaluate the generalization ability of distilled datasets, including PointNet++ [\(Qi et al.,](#page-11-10) [2017b\)](#page-11-10), DGCNN [\(Wang et al.,](#page-12-14) [2019\)](#page-12-14), Point Transformer [\(Guo et al.,](#page-10-13) [2021a\)](#page-10-13), PointMLP [\(Ma et al.,](#page-11-18) [2022\)](#page-11-18), and PointNext [\(Qian et al.,](#page-11-19) [2022\)](#page-11-19). Results can be found in Table [3.](#page-8-2)

**354 355 356 357 358 359** Experimental Setup. For each method, we perform the distillation process twice, evaluate each synthetic point cloud dataset five times (10 results in total), and report the mean and standard deviation. Baselines are all initialized with original data, while DD3D is trained from scratch. For the shape classification task, we consider three different distillation ratios with 1, 10, and 50 synthetic point clouds per class (CPC). For the part segmentation task, we only choose CPC=1 due to the limitation of GPU memory.

- **360**
- **361**

<span id="page-6-0"></span>5.1 SHAPE CLASSIFICATION

**362 363 364 365 366 367 368 369 370 371 372** The results of different methods on the shape classification task are shown in Table [1,](#page-7-1) from which we have the following observations. Firstly, the results of distillation-based methods consistently outperform coreset-based methods, demonstrating the effectiveness of DD. However, as the amount of synthetic data increases, the performance of the coreset increases rapidly. Secondly, DD3D achieves state-of-the-art performance on all five datasets, demonstrating its superiority over traditional DD methods. Notably, DD3D obtains more improvements over baselines as the number of CPCs increases, possibly because the generator provides more diverse data. Thirdly, the results on the rotated data are weaker than those on the aligned data. Although we project the rotated data to the canonical orientation, *i.e*., direction with maximum variance, these point clouds still have slightly different orientations, while the aligned data is manually registered, which is strictly towards the direction of gravity and therefore has better performance.

**373 374**

**377**

<span id="page-6-1"></span>

## 5.2 PART SEGMENTATION

**375 376** We illustrate the results of the part segmentation task in Table [2.](#page-7-2) As the segmentation task is different from the basic classification task, some coreset and distillation methods cannot adapt to it. Therefore,

<sup>2</sup>[https://www.cs.toronto.edu/˜kriz/cifar.html](https://www.cs.toronto.edu/~kriz/cifar.html)

| Dataset      | CPC | Coreset-based    |                  |                  | Distillation-based |                  |                  |                  | Full Dataset |
|--------------|-----|------------------|------------------|------------------|--------------------|------------------|------------------|------------------|--------------|
|              |     | Random           | Herding          | K-Center         | GM                 | DM               | TM               | DD3D             |              |
| ScanObjectNN | 1   | $22.00 pm 2.56$ | $16.29 pm 1.37$ | $18.18 pm 1.04$ | $26.34 pm 2.07$   | $25.90 pm 1.34$ | $26.42 pm 2.08$ | $30.62 pm 1.75$ |              |
| (Aligned)    | 10  | $32.63 pm 1.51$ | $31.94 pm 3.31$ | $33.46 pm 1.46$ | $39.87 pm 3.00$   | $37.61 pm 2.78$ | $36.44 pm 2.74$ | $43.77 pm 2.63$ | 66.96        |
|              | 50  | $54.15 pm 1.77$ | $51.70 pm 1.87$ | $54.22 pm 1.30$ | $57.52 pm 2.03$   | $56.91 pm 1.17$ |                  | $61.96 pm 1.44$ |              |
| ScanObjectNN | 1   | $14.90 pm 2.10$ | $18.10 pm 1.55$ | $19.91 pm 2.16$ | $14.64 pm 3.04$   | $18.74 pm 2.44$ | $19.29 pm 3.90$ | $23.59 pm 2.17$ |              |
| (Rotated)    | 10  | $20.50 pm 1.26$ | $20.20 pm 2.19$ | $22.05 pm 1.76$ | $20.55 pm 3.99$   | $20.26 pm 4.31$ | $19.20 pm 4.52$ | $25.84 pm 3.11$ | 54.84        |
|              | 50  | $42.98 pm 1.84$ | $43.39 pm 1.34$ | $44.29 pm 2.07$ | $47.74 pm 1.82$   | $48.11 pm 2.30$ |                  | $50.26 pm 1.42$ |              |
| ModelNet40   | 1   | $40.53 pm 0.36$ | $43.41 pm 0.81$ | $43.90 pm 1.51$ | $53.38 pm 0.86$   | $53.21 pm 0.58$ | $52.37 pm 0.99$ | $53.82 pm 0.28$ |              |
| (Aligned)    | 10  | $71.89 pm 0.29$ | $74.63 pm 0.48$ | $73.13 pm 0.78$ | $75.45 pm 0.82$   | $74.45 pm 0.47$ | $75.39 pm 1.32$ | $76.31 pm 0.49$ | 88.05        |
|              | 50  | $82.37 pm 0.45$ | $82.75 pm 0.49$ | $82.73 pm 0.28$ | $81.74 pm 0.55$   | $83.02 pm 1.16$ |                  | $83.91 pm 0.23$ |              |
| ModelNet40   | 1   | $34.65 pm 0.71$ | $30.03 pm 1.42$ | $30.05 pm 0.50$ | $41.32 pm 1.96$   | $41.71 pm 1.65$ | $37.36 pm 2.98$ | $42.36 pm 0.83$ |              |
| (Rotated)    | 10  | $58.87 pm 0.65$ | $56.03 pm 0.62$ | $57.69 pm 0.97$ | $55.69 pm 1.63$   | $55.45 pm 1.80$ | $56.21 pm 1.14$ | $58.14 pm 1.36$ | 80.45        |
|              | 50  | $70.13 pm 0.64$ | $70.02 pm 0.71$ | $69.68 pm 0.59$ | $68.92 pm 0.73$   | $69.31 pm 0.79$ |                  | $71.27 pm 0.32$ |              |
| MVPNet100    | 1   | $5.21 pm 0.27$  | $8.14 pm 0.22$  | $8.41 pm 0.35$  | $10.52 pm 0.83$   | $11.73 pm 0.49$ | $10.74 pm 0.57$ | $13.68 pm 0.48$ |              |
|              | 10  | $15.99 pm 0.30$ | $22.11 pm 0.21$ | $20.54 pm 0.21$ | $25.68 pm 0.77$   | $25.71 pm 0.69$ |                  | $31.14 pm 1.31$ | 55.63        |
|              | 50  | $30.14 pm 0.27$ | $35.87 pm 0.24$ | $35.48 pm 0.44$ | $37.41 pm 0.57$   | $36.83 pm 0.20$ |                  | $40.61 pm 0.38$ |              |

<span id="page-7-1"></span>**378 380** Table 1: Shape classification results of different methods, mean accuracy  $(\%) \pm$  standard deviation. Bold indicates the best performance, and "-" means out-of-memory during distillation. CPC: Number of Clouds Per Class.

<span id="page-7-2"></span>*Note*: All methods with rotated data are trained with the point cloud rotator. Ablations can be seen in Table [5.](#page-9-1)

Table 2: Part Segmentation results on the ShapeNet dataset, mean IoU (%).

|         | mIoU         | air-<br>plane | bag   | cap   | car   | chair | ear-<br>phone |       | guitar | knife | lamp  | laptop | motor | mug   | pistol | rocket | skate-<br>board | table |       |
|---------|--------------|---------------|-------|-------|-------|-------|---------------|-------|--------|-------|-------|--------|-------|-------|--------|--------|-----------------|-------|-------|
| Full    | 74.43        | 77.06         | 74.88 | 69.26 | 75.27 | 76.16 | 69.89         |       | 78.22  | 76.66 | 74.72 | 77.03  | 73.49 | 73.84 | 78.03  | 74.03  | 66.54           | 75.73 |       |
| Coreset | 48.83        | 47.03         | 24.68 | 58.89 | 39.57 | 70.13 | 30.78         |       | 74.15  | 58.46 | 42.24 | 89.34  | 26.78 | 37.93 | 56.01  | 20.18  | 41.59           | 63.41 |       |
| GM      | 47.94        | 48.93         | 20.34 | 42.12 | 29.98 | 73.06 | 24.22         |       | 73.41  | 69.24 | 32.38 | 83.80  | 20.96 | 61.58 | 44.17  |        | 38.62           | 43.32 | 60.84 |
| DD3D    | <b>50.99</b> |               | 42.39 | 34.37 | 54.00 | 29.20 | 70.52         | 27.87 |        | 77.16 | 74.83 | 34.09  | 86.52 | 28.46 | 64.93  | 53.04  | 34.89           | 43.62 | 59.94 |

we only compare DD3D with the random coreset selection and gradient matching methods. It can be observed that the performance of GM is not as good as the random coreset method, although it is initialized by the real data. On the other hand, DD3D outperforms both methods, validating its advantages in learning the coordinates and labels of point clouds. However, the performance of DD3D is not as good as the full dataset because part segmentation needs to learn both global information, *i.e*., shapes, and local information, *i.e*., parts, which is a challenging task in 3D distillation. The visualizations of DD3D with different matching objectives can be seen in Appendix [F.](#page-16-0)

<span id="page-7-0"></span>**412** 5.3 GENERALIZATION STUDIES

**413 414** We conduct two generalization experiments to verify the effectiveness of DD3D further.

**415 416 417 418 419 420 421 422 423** Cross-architecture Generalization. We first evaluate whether DD3D can benefit different point cloud models. Specifically, we use PointNet as the distillation method and utilize five advanced point cloud models as evaluation methods, trained on the synthetic data from scratch. Notably, we use synthetic data with CPC=50 to alleviate the randomness. The results are shown in Table [3,](#page-8-2) from which we can see that DD3D consistently outperforms DM and GM across different datasets and evaluation methods, proving that the synthetic data distilled by DD3D has better generalizability. This may be attributed to the generator that provides various point clouds in each epoch by sampling different noises, which plays a role like data augmentation. However, we can also observe that the results of evaluation methods are not as good as PointNet, emphasizing that the synthetic data is still biased by the distillation model.

**424 425 426 427 428 429 430 431** Cross-resolution Generalization. We next explore the performance of DD3D under different resolutions. Typically, the shape classification task needs 1,024 points for training and evaluation. In this experiment, we randomly sample 256 and 512 points from real data to supervise the distillation of DD3D. Once trained, we leverage DD3D to generate 1,024 points for evaluation. It is visible from Figure [3](#page-8-3) that training on high-resolution point clouds can accelerate the convergence of DD3D but the final matching losses are similar. Moreover, Table [4](#page-8-3) shows that different resolutions have similar performance. In some cases, low-resolution data also outperforms high-resolution point clouds, *e.g*., ScanObjectNN. This discovery shows that DD3D can not only achieve stable results but also significantly reduce computational costs and GPU occupancy.

<span id="page-8-2"></span>

| <b>Dataset</b> | Ratio | Method | PointNet++   | <b>DGCNN</b> | <b>PCT</b>   | PointMLP     | PointNeXt    | Average      |
|----------------|-------|--------|--------------|--------------|--------------|--------------|--------------|--------------|
| ScanObjectNN   | 32.3% | DM     | 56.02        | 51.47        | 52.72        | 51.33        | 51.82        | 52.67        |
|                |       | GM     | 55.38        | 52.98        | 53.28        | 51.33        | 52.81        | 53.16        |
|                |       | DD3D   | <b>57.14</b> | <b>53.36</b> | <b>54.04</b> | <b>52.50</b> | <b>53.36</b> | <b>54.08</b> |
| ModelNet40     | 20.3% | DM     | 74.35        | 74.84        | 76.92        | 72.49        | 71.48        | 74.02        |
|                |       | GM     | 76.54        | 73.38        | 77.31        | 74.11        | 72.00        | 74.67        |
|                |       | DD3D   | <b>77.71</b> | <b>75.36</b> | <b>79.21</b> | <b>75.36</b> | <b>73.99</b> | <b>76.33</b> |
| MVPNet100      | 8.0%  | DM     | 33.20        | 31.26        | 33.92        | 32.58        | 31.17        | 32.43        |
|                |       | GM     | 31.35        | 29.88        | 31.43        | 31.79        | 30.82        | 31.09        |
|                |       | DD3D   | <b>34.19</b> | <b>32.94</b> | <b>35.82</b> | <b>33.08</b> | <b>32.75</b> | <b>33.76</b> |

Table 3: Cross-architecture results (%) on different datasets with CPC=50.

<span id="page-8-3"></span>Image /page/8/Figure/3 description: This figure contains two line graphs side-by-side, both titled "ModelNet40". The x-axis for both graphs is labeled "Iteration", and the y-axis for both graphs is labeled "Matching Loss". The left graph shows data for iterations from 0 to 200, with matching loss values ranging from approximately 105 to 145. Three lines are plotted: a red line with circles representing a resolution of 256, a green line with circles representing a resolution of 512, and a blue line with circles representing a resolution of 1024. The right graph shows data for iterations from 0 to 400, with matching loss values ranging from approximately 55 to 95. It also displays three lines with the same color and marker conventions as the left graph. The caption below the graphs reads "Figure 3: Matching loss of different resolutions."

Table 4: DD3D under different resolutions.

| CPC=50       | Resolution |       |              |       |
|--------------|------------|-------|--------------|-------|
|              | 256        | 512   | 1024         | Avg.  |
| ScanObjectNN | 61.27      | 60.59 | <b>61.96</b> | 61.27 |
| ModelNet40   | 83.03      | 83.59 | <b>83.91</b> | 83.51 |
| MVPNet100    | 39.88      | 40.13 | <b>40.61</b> | 40.21 |

<span id="page-8-1"></span>

## 5.4 ABLATION STUDIES

**457 458 459 460 461 462 463 464 465 466 Point Cloud Rotator.** We first verify the effectiveness of the proposed point cloud rotator on the rotated ModelNet40 dataset. Specifically, we consider three different models: (1) PointNet, which is rotation-sensitive; (2) PointNet + PCA, which is rotation-invariant but sign-variant; (3) PointNet + Rotator, which is rotation- and sign-invariant. It can be observed from Table [5](#page-9-1) that the performance of all methods drops rapidly when the data is randomly rotated. On the other hand, leveraging PCA to transform the point clouds into a canonical orientation can significantly improve the distillation performance. However, the results are still far from the model with the point cloud rotator, which reflects that sign ambiguity will seriously prevent the distillation model from learning meaningful synthetic data. Finally, it can be observed that the proposed rotator can help point cloud models to rotation-invariant representations, thus benefiting the learning of synthetic data.

**467 468 469 470 471 472 473 474 475 476** Point-wise Generator. In Section [4.4,](#page-5-3) we have discussed the pros and cons of DD and DD3D. Here, we make a further attempt to show the advantages of the proposed point-wise generator. Firstly, in Table [6,](#page-9-1) we report the results of DD with and without initialization. It is noticeable that initializing DD with real data is important for the distillation performance. However, its performance is still not as good as DD3D, which does not rely on any initialization. Moreover, the performance of DD3D can still be improved if we use sampling during the evaluation, *i.e*., generating different point clouds at each epoch, because the generated data serves as data augmentation to improve the model performance. This strategy is more useful when the value of CPC is small. Additionally, in Figure [4,](#page-9-2) we visualize the matching loss of DD and DD3D. It is observable that DD without initialization has a higher loss value and converges more slowly than DD3D, reflecting the advantages of the proposed point-wise generator in synthetic point clouds.

**477 478**

<span id="page-8-0"></span>

## 5.5 TIME AND SPACE OVERHEAD

**479 480 481 482 483 484 485** We compare the overhead between DD and DD3D from multiple views. Firstly, Figure [8a](#page-16-1) shows that the time overhead of DD3D is slightly higher than DD due to the generation of synthetic data. Then, we can observe from Figure [8b](#page-16-1) that the memory budget of DD grows faster than DD3D as the value of CPC increases. DD3D can save the budget of synthetic data by sharing the generator between different classes, and its memory is nearly  $4x$  smaller than DD when CPC=10. Figure  $4c$ illustrates the changes in time and space overhead of DD3D at different resolutions. We can see that training with low-resolution point clouds significantly reduces overhead, which is important for resource-constrained scenarios, such as edge computing.

<span id="page-9-2"></span><span id="page-9-1"></span>Image /page/9/Figure/1 description: The image contains two tables and three bar charts, along with a line graph. The first table compares different models (PointNet, PointNet + PCA, PointNet + Rotator, and Full Dataset) against various metrics (Random, GM, DM, DD3D). The second table compares different configurations of DD3D (DD w/o Initialization, DD w/ Initialization, DD3D w/o Sampling, DD3D w/ Sampling) across different values (1, 10, 50). The line graph shows Matching Loss over Iterations for DD3D, DD (Random), and DD (Initialized). The first bar chart, labeled '(a) Time', compares the 'Times (s) Per Iteration' for DD3D and DD across different numbers of clouds per class (1, 5, 10). The second bar chart, labeled '(b) Budget', compares the 'Budget (GB)' for DD3D and DD across different numbers of clouds per class (1, 5, 10). The third bar chart, labeled '(c) Resolution', compares 'Time (s)' and 'Space (GB)' for DD3D and DD across different numbers of points (256, 512, 1024).

**486 487** Table 5: Ablation studies of the point cloud rotator on the ModelNet40 dataset with CPC=50.

Table 6: Comparison between DD and DD3D with different training strategies.

Image /page/9/Figure/4 description: Figure 4: Matching loss of methods with different settings.

<span id="page-9-3"></span><span id="page-9-0"></span>5.6 VISUALIZATION

Figure 5: Time and space overhead between DD and DD3D.

Image /page/9/Figure/6 description: The image displays a comparison of three different methods (Raw Images, DD3D, and GM) for representing 3D objects as point clouds. Each method is presented in a column, and there are three rows of objects. The first row shows representations of airplanes, the second row shows representations of lamps, and the third row shows representations of a guitar, a laptop, and a handgun. The 'Raw Images' column shows the original point cloud data. The 'DD3D' and 'GM' columns show processed versions of the point clouds, likely generated by different algorithms. The numbers 508 through 521 are displayed on the left side of the image, possibly indicating image coordinates or labels.

Figure 6: Visualization of the real and synthetic datasets. Top row: ModelNet40 (Airplane). Middle row: ModelNet40 (Lamp). Bottom row: ShapeNet (Guitar, Laptop, and Pistol).

**526 529** We visualize the real and synthetic point clouds in Figure [6](#page-9-3) for a more intuitive comparison. The results of DD3D and GM are placed in the last two columns. It can be observed that the point clouds generated by GM tend to condense to some clusters, while some isolated points are left as noise. On the contrary, the point clouds generated by DD3D are coherent and encode the global geometric shapes. Moreover, in ShapeNet, the point clouds of GM are squeezed, making its shape inconsistent with the real dataset, while the results of DD3D are more realistic and encode the spatial relationship between parts, validating the effectiveness of DD3D for 3d data. See Appendix [F](#page-16-0) for more visualizations.

# 6 CONCLUSION

**535 536 537 538 539** This paper introduces DD3D for 3D point cloud distillation, which matches the rotation-invariant data distribution between real and synthetic data by transforming point clouds into a canonical orientation. Once trained, DD3D can synthesize point clouds at arbitrary resolutions, reducing memory budget and improving scalability. Extensive experiments on both classification and segmentation tasks validate the superiority of DD3D over traditional DD methods. A promising direction is to initialize DD3D with real data to improve its performance.

**527 528**

# REFERENCES

<span id="page-10-17"></span><span id="page-10-14"></span><span id="page-10-10"></span><span id="page-10-8"></span><span id="page-10-6"></span><span id="page-10-5"></span><span id="page-10-3"></span><span id="page-10-1"></span><span id="page-10-0"></span>

| 541 | George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In CVPR, pp. 10708-10717. IEEE, 2022.                                                                                                                                                       |
|-----|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 542 | George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In CVPR, pp. 3739–3748. IEEE, 2023.                                                                                                                                                    |
| 543 | Angel X. Chang, Thomas A. Funkhouser, Leonidas J. Guibas, Pat Hanrahan, Qi-Xing Huang, Zimo Li, Silvio Savarese, Manolis Savva, Shuran Song, Hao Su, Jianxiong Xiao, Li Yi, and Fisher Yu. Shapenet: An information-rich 3d model repository. CoRR, abs/1512.03012, 2015.                                                                  |
| 544 | Yinbo Chen, Sifei Liu, and Xiaolong Wang. Learning continuous image representation with local implicit image function. In CVPR, pp. 8628–8638. Computer Vision Foundation / IEEE, 2021.                                                                                                                                                    |
| 545 | Matt Deitke, Ruoshi Liu, Matthew Wallingford, Huong Ngo, Oscar Michel, Aditya Kusupati, Alan Fan, Christian Laforte, Vikram Voleti, Samir Yitzhak Gadre, Eli VanderBilt, Aniruddha Kembhavi, Carl Vondrick, Georgia Gkioxari, Kiana Ehsani, Ludwig Schmidt, and Ali Farhadi. Objaverse-xl: A universe of 10m+3d objects. In NeurIPS, 2023. |
| 546 | Congyue Deng, Or Litany, Yueqi Duan, Adrien Poulenard, Andrea Tagliasacchi, and Leonidas J. Guibas. Vector neurons: A general framework for so(3)-equivariant networks. In ICCV, pp. 12180-12189. IEEE, 2021.                                                                                                                              |
| 547 | Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In NeurIPS, 2022.                                                                                                                                                                                                  |
| 548 | Jiawei Du, Yidi Jiang, Vincent Y. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In CVPR, pp. 3749–3758. IEEE, 2023.                                                                                                                                               |
| 549 | Jiahui Geng, Zongxiong Chen, Yuandou Wang, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, Zhiming Zhao, and Chunming Rong. A survey on dataset distillation: Approaches, applications and future directions. In <i>IJCAI</i> , pp. 6610–6618. ijcai.org, 2023.                                                                     |
| 550 | Meng-Hao Guo, Junxiong Cai, Zheng-Ning Liu, Tai-Jiang Mu, Ralph R. Martin, and Shi-Min Hu. PCT: point cloud transformer. Comput. Vis. Media, 7(2):187-199, 2021a.                                                                                                                                                                          |
| 551 | Yulan Guo, Hanyun Wang, Qingyong Hu, Hao Liu, Li Liu, and Mohammed Bennamoun. Deep learning for 3d point clouds: A survey. IEEE Trans. Pattern Anal. Mach. Intell., 43(12):4338-4364, 2021b.                                                                                                                                               |
| 552 | Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In ICLR. OpenReview.net, 2024.                                                                                                                                             |
| 553 | Zixuan Huang, Justin Johnson, Shoubhik Debnath, James M. Rehg, and Chao-Yuan Wu. Pointinfinity: Resolution-invariant point diffusion models. In CVPR, 2024.                                                                                                                                                                                |
| 554 | Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In ICML, volume 162 of Proceedings of Machine Learning Research, pp. 11102-11118. PMLR, 2022.                                                       |
| 555 | Seohyun Kim, Jaeyoo Park, and Bohyung Han. Rotation-invariant local-to-global representation learning for 3d point cloud. In NeurIPS, 2020.                                                                                                                                                                                                |
| 556 | Shiye Lei and Dacheng Tao. A comprehensive survey of dataset distillation. IEEE Trans. Pattern Anal. Mach. Intell., 46(1):17-32, 2024.                                                                                                                                                                                                     |
| 557 | Feiran Li, Kent Fujiwara, Fumio Okura, and Yasuyuki Matsushita. A closer look at rotation-invariant deep point cloud analysis. In ICCV, pp. 16198-16207. IEEE, 2021.                                                                                                                                                                       |
| 558 | Yangyan Li, Rui Bu, Mingchao Sun, Wei Wu, Xinhan Di, and Baoquan Chen. Pointcnn: Convolution                                                                                                                                                                                                                                               |

<span id="page-10-16"></span><span id="page-10-15"></span><span id="page-10-13"></span><span id="page-10-12"></span><span id="page-10-11"></span><span id="page-10-9"></span><span id="page-10-7"></span><span id="page-10-4"></span><span id="page-10-2"></span>**593** Yangyan Li, Rui Bu, Mingchao Sun, Wei Wu, Xinhan Di, and Baoquan Chen. Pointcnn: Convolution on x-transformed points. In *NeurIPS*, pp. 828–838, 2018.

<span id="page-11-19"></span><span id="page-11-18"></span><span id="page-11-17"></span><span id="page-11-16"></span><span id="page-11-15"></span><span id="page-11-14"></span><span id="page-11-13"></span><span id="page-11-12"></span><span id="page-11-11"></span><span id="page-11-10"></span><span id="page-11-9"></span><span id="page-11-8"></span><span id="page-11-7"></span><span id="page-11-6"></span><span id="page-11-5"></span><span id="page-11-4"></span><span id="page-11-3"></span><span id="page-11-2"></span><span id="page-11-1"></span><span id="page-11-0"></span>**594 595 596 597 598 599 600 601 602 603 604 605 606 607 608 609 610 611 612 613 614 615 616 617 618 619 620 621 622 623 624 625 626 627 628 629 630 631 632 633 634 635 636 637 638 639 640 641 642 643 644 645 646 647** Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, 2022. Noel Loo, Ramin M. Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022. Xu Ma, Can Qin, Haoxuan You, Haoxi Ran, and Yun Fu. Rethinking network design and local geometry in point cloud: A simple residual MLP framework. In *ICLR*. OpenReview.net, 2022. Aru Maekawa, Naoki Kobayashi, Kotaro Funakoshi, and Manabu Okumura. Dataset distillation with attention labels for fine-tuning BERT. In *ACL (2)*, pp. 119–127. Association for Computational Linguistics, 2023. Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. In *ICLR*. OpenReview.net, 2021. Jeong Joon Park, Peter R. Florence, Julian Straub, Richard A. Newcombe, and Steven Lovegrove. Deepsdf: Learning continuous signed distance functions for shape representation. In *CVPR*, pp. 165–174. Computer Vision Foundation / IEEE, 2019. Adrien Poulenard, Marie-Julie Rakotosaona, Yann Ponty, and Maks Ovsjanikov. Effective rotationinvariant point CNN with spherical harmonics kernels. In *3DV*, pp. 47–56. IEEE, 2019. Charles Ruizhongtai Qi, Hao Su, Kaichun Mo, and Leonidas J. Guibas. Pointnet: Deep learning on point sets for 3d classification and segmentation. In *CVPR*, pp. 77–85. IEEE Computer Society, 2017a. Charles Ruizhongtai Qi, Li Yi, Hao Su, and Leonidas J. Guibas. Pointnet++: Deep hierarchical feature learning on point sets in a metric space. In *NIPS*, pp. 5099–5108, 2017b. Guocheng Qian, Yuchen Li, Houwen Peng, Jinjie Mai, Hasan Hammoud, Mohamed Elhoseiny, and Bernard Ghanem. Pointnext: Revisiting pointnet++ with improved training and scaling strategies. In *NeurIPS*, 2022. Shi Qiu, Saeed Anwar, and Nick Barnes. Dense-resolution network for point cloud classification and segmentation. In *WACV*, pp. 3812–3821. IEEE, 2021. Huilin Qu, Congqiao Li, and Sitian Qian. Particle transformer for jet tagging. In *ICML*, volume 162 of *Proceedings of Machine Learning Research*, pp. 18281–18292. PMLR, 2022. Jiawei Ren, Liang Pan, and Ziwei Liu. Benchmarking and analyzing point cloud classification under corruptions. In *ICML*, volume 162 of *Proceedings of Machine Learning Research*, pp. 18559–18575. PMLR, 2022. Noveen Sachdeva and Julian J. McAuley. Data distillation: A survey. *CoRR*, abs/2301.04272, 2023. Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018. DongHyeok Shin, Seungjae Shin, and Il-Chul Moon. Frequency domain-based dataset distillation. In *NeurIPS*, 2023. Rajhans Singh, Ankita Shukla, and Pavan K. Turaga. Polynomial implicit neural representations for large diverse datasets. In *CVPR*, pp. 2041–2051. IEEE, 2023. Vincent Sitzmann, Julien N. P. Martel, Alexander W. Bergman, David B. Lindell, and Gordon Wetzstein. Implicit neural representations with periodic activation functions. In *NeurIPS*, 2020. Nathaniel Thomas, Tess E. Smidt, Steven Kearnes, Lusann Yang, Li Li, Kai Kohlhoff, and Patrick Riley. Tensor field networks: Rotation- and translation-equivariant neural networks for 3d point clouds. *CoRR*, abs/1802.08219, 2018. Mikaela Angelina Uy, Quang-Hieu Pham, Binh-Son Hua, Duc Thanh Nguyen, and Sai-Kit Yeung. Revisiting point cloud classification: A new benchmark dataset and classification model on real-world data. In *ICCV*, pp. 1588–1597. IEEE, 2019.

<span id="page-12-20"></span><span id="page-12-19"></span><span id="page-12-18"></span><span id="page-12-17"></span><span id="page-12-16"></span><span id="page-12-15"></span><span id="page-12-14"></span><span id="page-12-13"></span><span id="page-12-12"></span><span id="page-12-11"></span><span id="page-12-10"></span><span id="page-12-9"></span><span id="page-12-8"></span><span id="page-12-7"></span><span id="page-12-6"></span><span id="page-12-5"></span><span id="page-12-4"></span><span id="page-12-3"></span><span id="page-12-2"></span><span id="page-12-1"></span><span id="page-12-0"></span>

| 648 | Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. CAFE: learning to condense dataset by aligning features. In <i>CVPR</i> , pp. 12186–12195. IEEE, 2022.                                                            |
|-----|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 649 | Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. <i>CoRR</i> , abs/2303.04707, 2023.                                                                                                                               |
| 650 | Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. <i>CoRR</i> , abs/1811.10959, 2018.                                                                                                                                                             |
| 651 | Yue Wang, Yongbin Sun, Ziwei Liu, Sanjay E. Sarma, Michael M. Bronstein, and Justin M. Solomon. Dynamic graph CNN for learning on point clouds. <i>ACM Trans. Graph.</i> , 38(5):146:1–146:12, 2019.                                                                                     |
| 652 | Ziyu Wang, Yue Xu, Cewu Lu, and Yong-Lu Li. Dancing with images: Video distillation via static-dynamic disentanglement. In <i>CVPR</i> , 2024.                                                                                                                                           |
| 653 | Max Welling. Herding dynamical weights to learn. In <i>ICML</i> , volume 382, pp. 1121–1128, 2009.                                                                                                                                                                                       |
| 654 | Wenxuan Wu, Zhongang Qi, and Fuxin Li. Pointconv: Deep convolutional networks on 3d point clouds. In <i>CVPR</i> , pp. 9621–9630. Computer Vision Foundation / IEEE, 2019.                                                                                                               |
| 655 | Zhirong Wu, Shuran Song, Aditya Khosla, Fisher Yu, Linguang Zhang, Xiaoou Tang, and Jianxiong Xiao. 3d shapenets: A deep representation for volumetric shapes. In <i>CVPR</i> , pp. 1912–1920, 2015.                                                                                     |
| 656 | Zelin Xiao, Hongxin Lin, Renjie Li, Lishuai Geng, Hongyang Chao, and Shengyong Ding. Endowing deep 3d models with rotation invariance based on principal component analysis. In <i>ICME</i> , pp. 1–6. IEEE, 2020.                                                                       |
| 657 | Li Yi, Vladimir G. Kim, Duygu Ceylan, I-Chao Shen, Mengyan Yan, Hao Su, Cewu Lu, Qixing Huang, Alla Sheffer, and Leonidas J. Guibas. A scalable active framework for region annotation in 3d shape collections. <i>ACM Trans. Graph.</i> , 35(6):210:1–210:12, 2016.                     |
| 658 | Ruixuan Yu, Xin Wei, Federico Tombari, and Jian Sun. Deep positional and relational feature learning for rotation-invariant point cloud analysis. In <i>ECCV</i> , volume 12355, pp. 217–233. Springer, 2020.                                                                            |
| 659 | Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. <i>IEEE Trans. Pattern Anal. Mach. Intell.</i> , 46(1):150-170, 2024.                                                                                                                            |
| 660 | Xianggang Yu, Mutian Xu, Yidan Zhang, Haolin Liu, Chongjie Ye, Yushuang Wu, Zizheng Yan, Chenming Zhu, Zhangyang Xiong, Tianyou Liang, Guanying Chen, Shuguang Cui, and Xiaoguang Han. Mvimgnet: A large-scale dataset of multi-view images. In <i>CVPR</i> , pp. 9150–9161. IEEE, 2023. |
| 661 | David Junhao Zhang, Heng Wang, Chuhui Xue, Rui Yan, Wenqing Zhang, Song Bai, and Mike Zheng Shou. Dataset condensation via generative model. <i>CoRR</i> , abs/2309.07698, 2023.                                                                                                         |
| 662 | Haiyu Zhang, Shaolin Su, Yu Zhu, Jinqiu Sun, and Yanning Zhang. GSDD: generative space dataset distillation for image super-resolution. In <i>AAAI</i> , pp. 7069-7077. AAAI Press, 2024.                                                                                                |
| 663 | Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In <i>ICML</i> , volume 139 of <i>Proceedings of Machine Learning Research</i> , pp. 12674–12685. PMLR, 2021.                                                                                    |
| 664 | Bo Zhao and Hakan Bilen. Synthesizing informative training samples with GAN. <i>CoRR</i> , abs/2204.07513, 2022.                                                                                                                                                                         |
| 665 | Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In <i>WACV</i> , pp. 6503-6512. IEEE, 2023.                                                                                                                                                                    |
| 666 | Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In <i>ICLR. OpenReview.net</i> , 2021.                                                                                                                                                        |
| 667 | Chen Zhao, Jiaqi Yang, Xin Xiong, Angfan Zhu, Zhiguo Cao, and Xin Li. Rotation invariant point cloud classification: Where local geometry meets global topology. <i>CoRR</i> , abs/1911.00195, 2019.                                                                                     |
| 668 | Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In <i>NeurIPS</i> , 2022.                                                                                                                                                           |

<span id="page-13-0"></span>

# A PROOF OF THEOREMS

Theorem 1. *Assume the classifier is a linear layer* W *and* Lcls *can be simplified to the mean-squared error*  $||XW - Y||_F^2$ . The objective of gradient matching is equal to variance preserving:

$$
\min_{\mathcal{S}} \mathcal{L}_{GM} = \min_{\mathcal{S}} \mathcal{D} \left( \nabla_W \mathcal{L}_{cls}^{\mathcal{S}}, \nabla_W \mathcal{L}_{cls}^{\mathcal{T}} \right) \quad \Rightarrow \quad \min_{\mathcal{S}} \left\| X_{\mathcal{S}}^{\top} X_{\mathcal{S}} - X_{\mathcal{T}}^{\top} X_{\mathcal{T}} \right\|_F^2, \tag{12}
$$

*where*  $D$  *is a distance metric and*  $\nabla_W$  *is the gradient with respect to*  $W$ *.* 

 $\leq$   $||W||$ 

*Proof.* The gradient of  $||XW - Y||_F^2$  is denoted as  $\nabla = X^\top (XW - Y)$ . We can then match the gradients between the real and synthetic data:

$$
||\nabla_S - \nabla_T||_F^2 = ||X_S^\top (X_S W - Y_S) - X_T^\top (X_T W - Y_T)||_F^2
$$
\n(13)

$$
\frac{2}{F} \underbrace{\left\| X_S^\top X_S - X_T^\top X_T \right\|_F^2}_{\text{Variance}} + \underbrace{\left\| X_S^\top Y_S - X_T^\top Y_T \right\|_F^2}_{\text{Mean}}.
$$
 (14)

 $(16)$ 

**716** We can see that the first term is to preserve the variance of real data, and the second term aligns the **717** average representations of samples belonging to the same class. These two terms can be combined if **718** we set  $\tilde{X}_{S} = X_{S} - X_{S}^{\top} Y_{S}$  and  $\tilde{X}_{T} = X_{T} - X_{T}^{\top} Y_{T}$  for each class. Then we only need to match the **719** variance between  $\tilde{X}_{\mathcal{S}}$  and  $\tilde{X}_{\mathcal{T}}$ .  $\Box$ **720**

**Theorem 2.** Assume  $X_{\mathcal{T}}$  follows a d-dimensional multivariate Gaussian distribution  $\mathcal{N}(\mu, \Sigma)$ . Let  $X'_{\mathcal{T}}$  be the rotated representations of  $X_{\mathcal{T}}$  such that:

$$
\lambda_{max}\left(\mathbb{E}\left[X_{\mathcal{T}}^{\top}X_{\mathcal{T}}^{\top}\right]\right) \leq \lambda_{max}\left(\mathbb{E}\left[X_{\mathcal{T}}^{\top}X_{\mathcal{T}}\right]\right) \quad \Rightarrow \quad \sigma_{max}\left(\mathbb{E}\left[X_{\mathcal{T}}^{\prime}\right]\right) \leq \sigma_{max}\left(\mathbb{E}\left[X_{\mathcal{T}}\right]\right), \tag{15}
$$

*where*  $\lambda_{max}$  *and*  $\sigma_{max}$  *are the maximum eigenvalues and singular values, respectively.* 

*Proof.* Firstly, the largest eigenvalue of the covariance matrix  $X_{\mathcal{T}}$ <sup>T</sup> $X_{\mathcal{T}}$  is equal to the largest singular value of  $X_{\mathcal{T}}$ . Therefore, we only prove the first inequality.

Secondly, as  $X^{\top} X = \sum_{i=1}^{n} x_i^{\top} x_i$ , for  $X_{\mathcal{T}} \sim \mathcal{N}(\boldsymbol{\mu}, \Sigma)$ , we have:

$$
\mathbb{E}\left[X_{\mathcal{T}}^{\top} X_{\mathcal{T}}\right] = \mathbb{E}\left[\sum_{i=1}^{n} x_i^{\top} x_i\right] = \sum_{i=1}^{n} \mathbb{E}\left[x_i^{\top} x_i\right] = n\left(\boldsymbol{\mu}^{\top} \boldsymbol{\mu} + \Sigma\right),
$$

**732 733 734**

$$
\mathbb{E}\left[X_{\mathcal{T}}^{\top}X_{\mathcal{T}}^{\prime}\right] = \mathbb{E}\left[\sum_{i=1}^{n} R_{i}^{\top}x_{i}^{\top}x_{i}R_{i}\right] = \sum_{i=1}^{n} \mathbb{E}\left[R_{i}^{\top}x_{i}^{\top}x_{i}R_{i}\right] = \sum_{i=1}^{n} R_{i}^{\top} \mathbb{E}\left[x_{i}^{\top}x_{i}\right]R_{i}.
$$
 (17)

Thirdly, we have:

$$
\lambda_{max}\left(\mathbb{E}\left[X_{\mathcal{T}}^{\top}X_{\mathcal{T}}\right]\right)=n\lambda_{max}\left(\boldsymbol{\mu}^{\top}\boldsymbol{\mu}+\boldsymbol{\Sigma}\right),\tag{18}
$$

$$
\lambda_{max}\left(\mathbb{E}\left[X_{\mathcal{T}}^{\prime}\right]X_{\mathcal{T}}^{\dagger}\right) = \lambda_{max}\left(\sum_{i=1}^{n} R_i^{\top} \mathbb{E}\left[x_i^{\top} x_i\right] R_i\right) \le \sum_{i=1}^{n} \lambda_{max}\left(R_i^{\top} \mathbb{E}\left[x_i^{\top} x_i\right] R_i\right) \tag{19}
$$

$$
= \sum_{i=1}^{n} \lambda_{max} \left( R_i^{\top} \boldsymbol{\mu}^{\top} \boldsymbol{\mu} R_i + R_i^{\top} \Sigma R_i \right) \leq \lambda_{max} \left( \mathbb{E} \left[ X_T^{\top} X_{\mathcal{T}} \right] \right). \tag{20}
$$

The above inequality shows that the largest eigenvalue of  $\mathbb{E}\left[X_{\mathcal{T}}^{\top}X_{\mathcal{T}}\right]$  is the upper bound of  $\mathbb{E}\left[X_{\mathcal{T}}^{\prime\top}X_{\mathcal{T}}^{\prime}\right]$ . The equality holds if and only if the random rotation matrices are commutative, which is infeasible in practice.  $\Box$ 

**749 750 751**

<span id="page-13-1"></span>

# B IMPLEMENTATION DETAILS OF DD3D

**752 753 754 755** Here, we explain some details of DD3D, consisting of two important components: a point cloud rotator and a point-wise generator. Both components are built based on the SIREN [\(Sitzmann et al.,](#page-11-13) [2020\)](#page-11-13) model, which stacks multiple fully connected layers with  $sin(\cdot)$  activation to capture the high-frequency information. The PyTorch code is shown in Algorithm [4,](#page-14-2) where some details are highlighted.

9 # w0 is to adjust the frequency of sine function 10 self.sign\_encoder = SIREN(1, hidden\_dim, w0=w0) 11 self.sign\_decoder = SIREN(hidden\_dim, 1, w0=1.)

 $x = x.\text{unsqueeze}(-1) # x: [B, N, 3, 1]$ 

 $sign = torch.sizeon(feat)$  #  $sign-equivalent$ 

 $x = x * sign # [B, N, 3, 1] * [B, 1, 3, 1] \rightarrow [B, N, 3, 1]$ 

### Algorithm 4 PyTorch code of DD3D

6 def \_\_init\_\_(self, hidden\_dim, w0): 7 super().\_\_init\_\_()

import torch.nn as nn

class Rotator(nn.Module):

def forward(self, x):

```
759
760
761
762
763
764
```

**757 758**

4

8

12

15

1 import torch

import SIREN

```
765
```

```
766
767
768
```

 $769^{+19}_{-20}$ **770 771**

<span id="page-14-3"></span><span id="page-14-0"></span>

# C DETAILS OF DATASETS

<span id="page-14-2"></span>return x.squeeze(-1)

16 feat = self.sign\_encoder(x).mean(dim=1, keepdim=True) # [B, N, 3, 1] -> [B, 1, 3, d]<br>17 feat = self.sign\_decoder(feat) # [B, 1, 3, d] -> [B, 1, 3, 1]

|                      | ScanObjectNN | ModelNet40 | MVPNet | ShapeNet |
|----------------------|--------------|------------|--------|----------|
| # Shape Classes      | 15           | 40         | 100    | 16       |
| # Part Classes       | -            | -          | -      | 50       |
| # Training Samples   | 2,322        | 9,843      | 62,494 | 14,007   |
| # Validation Samples | 580          | 2,468      | 15,670 | 2,874    |
| Resolution           | 1,024        | 1,024      | 1,024  | 2,048    |

The detailed statistical information of the datasets used in this paper is shown in Table [7.](#page-14-3) We list the sources of the datasets and their licenses in the following.

- ScanObjectNN: <https://github.com/feiran-l/rotation-invariant-pointcloud-analysis> (MIT license)
- ModelNet40: <http://modelnet.cs.princeton.edu/ModelNet40.zip>
- MVPNet: <https://github.com/GAP-LAB-CUHK-SZ/MVImgNet>
- ShapeNet: <https://github.com/feiran-l/rotation-invariant-pointcloud-analysis> (MIT license)

<span id="page-14-1"></span>

# D HYPERPARAMETERS

<span id="page-14-4"></span>The hyperparameters of baselines and DD3D are listed in Tables [8](#page-14-4) and [9,](#page-15-1) respectively.

|                   | ScanObjectNN           | ModelNet40             | MVPNet100              | ShapeNet              |
|-------------------|------------------------|------------------------|------------------------|-----------------------|
| Optimizer         | Adam                   | Adam                   | Adam                   | Adam                  |
| Initial LR        | 0.001                  | 0.001                  | 0.001                  | 0.001                 |
| <b>Batch Size</b> | 32                     | 32                     | 64                     | 32                    |
| <b>Iterations</b> | 200                    | 400                    | 600                    | 200                   |
| Weight Decay      | 0.0005                 | 0.0005                 | 0.0005                 | 0.0005                |
| Augmentation      | Scale, Jitter, Rotate  | Scale, Jitter, Rotate  | Scale, Jitter, Rotate  | Scale, Jitter, Rotate |
|                   | StepLR                 | StepLR                 | StepLR                 |                       |
| Scheduler         | (Decay 0.1 / 100 iter) | (Decay 0.1 / 100 iter) | (Decay 0.5 / 200 iter) |                       |

Table 8: Hyperparameters used for Data Synthesis.

<span id="page-15-1"></span>

|              | ScanObjectNN                        | ModelNet40                          | MVPNet100             | ShapeNet              |
|--------------|-------------------------------------|-------------------------------------|-----------------------|-----------------------|
| Optimizer    | Adam                                | Adam                                | Adam                  | Adam                  |
| Initial LR   | 0.001                               | 0.001                               | 0.001                 | 0.001                 |
| Batch Size   | 8                                   | 8                                   | 32                    | 8                     |
| Epochs       | 200                                 | 200                                 | 200                   | 200                   |
| Weight Decay | 0.0005                              | 0.0005                              | 0.0005                | 0.0005                |
| Augmentation | Scale, Jitter, Rotate               | Scale, Jitter, Rotate               | Scale, Jitter, Rotate | Scale, Jitter, Rotate |
| Scheduler    | StepLR<br>(Decay $0.1 / 100$ epoch) | StepLR<br>(Decay $0.1 / 100$ epoch) | CosineAnnealingLR     | -                     |

### Table 9: Hyperparameters used for Validation.

<span id="page-15-0"></span>

# E DETAILS OF BACKBONES

Previous work [\(Yu et al.,](#page-12-12) [2024\)](#page-12-12) pointed out that complexity architecture may degenerate the distillation performance. Therefore, we re-implement some traditional point cloud models, described as follows.

• PointNet: 3 layers with dimension [64, 128, 1024]. Each layer consists of a Conv1d layer, an InstanceNorm1d or BatchNorm1d layer (for classification and segmentation, respectively), and a ReLU activation. We use max-pooling to learn the global representation of point clouds.

For other methods, we use the codes provided by openpoints<sup>[3](#page-0-0)</sup> library. The YAML configuration files are listed below.

Image /page/15/Figure/7 description: This image is a table comparing different point cloud processing models: PointNet++, DGCNN, PCT, PointMLP, and PointNext. Each column details the configuration parameters for its respective model. For PointNet++, parameters include encoder arguments like PointNet2Encoder with specific channels, width, layers, strides, and MLPs, as well as aggregation and grouping arguments. DGCNN's configuration includes channels, number of classes, embedding dimensions, blocks, convolution type, k value, dropout, normalization, and activation arguments. PCT specifies the number of points, classes, input dimensions, and transformer-related parameters. PointMLP lists in-channels, points, classes, embedding dimensions, groups, expansion, activation, bias, normalization, and dimension expansion. PointNext includes encoder arguments like PointNextEncoder with blocks, strides, width, channels, radius, sampling layers, and aggregation details. All models have class arguments specifying the head name, number of classes, MLPs, and normalization.

F ADDITIONAL EXPERIMENTS AND DISCUSSIONS

Performance of Synthetic Datasets. The performance of DD is positively correlated with the memory overhead, *i.e*., CPC, of the synthetic datasets. When applied to applications requiring high accuracy, we can increase the value of CPC to improve the performance of synthetic datasets. To validate this, we conduct experiments on ScanObjectNN and ModelNet40 with CPC=100 and evaluate the performance of DD3D and GM. The results are shown below.

| Table 10. Results on ScanObjectNN |       |       |       |       | Table 11. Results on ModelNet40 |       |       |       |       |
|-----------------------------------|-------|-------|-------|-------|---------------------------------|-------|-------|-------|-------|
| CPC                               | 1     | 10    | 50    | 100   | CPC                             | 1     | 10    | 50    | 100   |
| GM                                | 26.34 | 39.87 | 57.52 | 62.82 | GM                              | 53.38 | 65.45 | 81.74 | 84.71 |
| DD3D                              | 30.62 | 43.77 | 61.96 | 65.51 | DD3D                            | 53.82 | 76.31 | 83.91 | 86.68 |
| Full                              | 66.96 |       |       |       | Full                            | 88.05 |       |       |       |

Table 10: Results on ScanObjectNN

### Table 11: Results on ModelNet40

<sup>3</sup><https://github.com/guochengqian/openpoints>

<span id="page-16-0"></span> Local-matching and Global-matching. The segmentation task is more challenging than the classification task as it needs both global information (shape) and local information (part). Therefore, we propose local and global matching in the segmentation task. To verify the role of each objective, we visualize the point clouds generated by DD3D with local and global matching, respectively. The results are shown below. We can see that global matching cannot learn the shapes of parts, such as the chassis of a laptop (Row 3, Column 2). Local matching cannot capture the spatial relationship between parts, such as the handle and body of a bag (Row 4, Column 4). Therefore, the combination of local and global matching is essential for the distillation of the segmentation task.

Image /page/16/Figure/2 description: This image displays a grid of images categorized by object type and processing method. The object types, listed across the top, are Guitar, Laptop, Pistol, Bag, and Mug. The processing methods are listed down the left side: Raw Images, Local & Global, Global, and Local. Each cell in the grid shows a representation of the corresponding object type and processing method, rendered as a collection of colored dots. The 'Raw Images' row shows the original object shapes in yellow. The subsequent rows show progressively more abstract representations using combinations of yellow, green, and purple dots, illustrating the effects of different processing techniques on the object data.

Figure 7: Synthetic images of DD3D with different matching objectives.

<span id="page-16-1"></span>Delving into Aligned and Misaligned Orientations. To further analyze the influence of rotated and aligned point clouds, we use Grad-CAM to visualize the importance distribution of PointNet trained on these two datasets. We denote the two models as PointNet-aligned and PointNet-rotated. It can be observed that the importance of PointNet-aligned is more concentrated than PointNet-rotated, validating our analysis that rotation-invariant features can preserve the principle components.

Image /page/16/Figure/5 description: The image displays two scatter plots side-by-side, both visualizing point cloud data colored by importance. The left plot is labeled "modelnet\_no\_pca" and titled "(a) PointNet-rotated". The right plot is labeled "modelnet\_pca" and titled "(b) PointNet-aligned". Both plots show a shape resembling an airplane or a cross, with points distributed along its structure. A color bar on the right of each plot indicates that the color represents "importance", ranging from 0.0 (dark blue) to 1.0 (red). The x and y axes are labeled with numerical values ranging from approximately -0.8 to 1.0. The points in both plots are predominantly dark blue, indicating low importance, with some areas of green, yellow, and red, signifying higher importance, particularly around the center and along the wings of the depicted shape.

Image /page/16/Figure/6 description: The image contains two numbers, 916 and 917, displayed in a light gray color against a white background. The numbers are stacked vertically, with 916 positioned above 917.

Figure 8: Grad-CAM importance of PointNet trained on aligned and rotated datasets.

 How to Learn Geometric Details? DD typically focuses on capturing coarse-grained (lowfrequency) features that encode the principle information of real datasets. When applied to tasks requiring fine-grained information, we may be concerned about its ability to learn high-frequency details. We visualize DD3D with different periods, *i.e*., t=10/50/100, in the following. A larger value of  $t$  indicates a higher frequency components. We can find that increasing high-frequency input can significantly help DD3D learn geometric details. This inspires us to enhance high-frequency information for fine-grained tasks.

Image /page/17/Figure/2 description: This image displays a grid of scatter plots showing the evolution of different objects over time. The objects are labeled as Guitar, Laptop, Pistol, Bag, and Mug. The rows represent different time steps: 'Raw Images', 't=10', 't=50', and 't=100'. Each cell in the grid contains a scatter plot of points, with colors varying between yellow, purple, and teal, illustrating the object's shape at that specific time step. The 'Raw Images' row shows the initial forms of the objects, while subsequent rows show how these forms are represented or transformed at later time steps, with the points becoming more abstract and less defined as time progresses.

Figure 9: Synthetic images of DD3D with different periods.
# Curriculum Dataset Distillation

Zhiheng Ma1,3,4<sup>∗</sup> Anjia Cao2<sup>∗</sup> <PERSON><PERSON><sup>2</sup> <PERSON>ng Wei<PERSON>†

<sup>1</sup>Shenzhen University of Advanced Technology <sup>2</sup>School of Software Engineering, Xi'an Jiaotong University <sup>3</sup>Guangdong Provincial Key Laboratory of Computility Microelectronics <sup>4</sup>Shenzhen Institute of Advanced Technology, Chinese Academy of Sciences {caoanjia7, moolink}@stu.xjtu.edu.cn <EMAIL> <EMAIL>

<span id="page-0-0"></span>Image /page/0/Figure/3 description: This image is a figure comparing the results of a method labeled "Ours" against a method labeled "SRe2L" for generating images of goldfish, flamingos, lions, and pineapples. Each category (a, b, c, d) shows four images generated by SRe2L in the top row and four images generated by <PERSON><PERSON> in the bottom row. The goldfish images show schools of fish in water. The flamingo images show groups of pink flamingos in a natural setting. The lion images are stylized portraits of lions. The pineapple images are abstract representations of pineapples.

Figure 1: **ImageNet-1K Distillation Comparison: our method vs.**  $SRe^{2}L$  **[\[1\]](#page-9-0).** In contrast to  $SRe^{2}L$ , which often results in images with repetitive patterns, our approach creates synthetic images with a much richer diversity of patterns.

# Abstract

Most dataset distillation methods struggle to accommodate large-scale datasets due to their substantial computational and memory requirements. In this paper, we present a curriculum-based dataset distillation framework designed to harmonize scalability with efficiency. This framework strategically distills synthetic images, adhering to a curriculum that transitions from simple to complex. By incorporating curriculum evaluation, we address the issue of previous methods generating images that tend to be homogeneous and simplistic, doing so at a manageable computational cost. Furthermore, we introduce adversarial optimization towards synthetic images to further improve their representativeness and safeguard against their overfitting to the neural network involved in distilling. This enhances the generalization capability of the distilled images across various neural network architectures and also increases their robustness to noise. Extensive experiments demonstrate that our framework sets new benchmarks in large-scale dataset distillation, achieving substantial improvements of 11.1% on Tiny-ImageNet, 9.0% on ImageNet-1K, and 7.3% on ImageNet-21K. The source code will be released to the community.

<sup>\*</sup>Equal contribution

<sup>†</sup>Corresponding author

# 1 Introduction

Dataset distillation, as elucidated by [\[2\]](#page-9-1), entails compressing the original dataset into a significantly smaller synthetic dataset. This streamlined synthetic dataset confers pronounced advantages, particularly in improving data storage efficacy, fortifying privacy safeguards, and expediting training processes  $[3-6, 1, 7]$  $[3-6, 1, 7]$  $[3-6, 1, 7]$  $[3-6, 1, 7]$  $[3-6, 1, 7]$ . Furthermore, the utility of this approach has been effectively demonstrated across various downstream applications, as evidenced in domains such as continual learning  $[8, 9]$  $[8, 9]$  $[8, 9]$ and neural architecture search [\[10\]](#page-9-7).

A considerable number of algorithms for dataset distillation have approached the problem as a bi-level optimization task  $[2, 9]$  $[2, 9]$  $[2, 9]$ . This methodology involves an inner loop that focuses on updating the model and an outer loop for refining synthetic data. These strategies have shown significant advances in small-scale datasets such as MNIST and CIFAR [\[11\]](#page-9-8), which are known for their relatively low image resolutions and data volume. The outer loop, which evaluates the original data on the network trained with the synthetic data, ensures alignment between these two datasets. However, a major challenge arises from the high computational and memory costs associated with performing multiple unrolled iterations within the bi-level optimization framework. This limitation greatly hinders the application of bi-level-based methods to more complex real-world datasets like ImageNet [\[12\]](#page-9-9).

Recently, Yin *et al.* unveiled SRe<sup>2</sup>L [\[1\]](#page-9-0), an innovative approach that decouples bi-level optimization into discrete processes, achieving commendable dataset distillation efficacy on ImageNet. Initially, the method involves training a neural network on the original dataset, succeeded by applying the model inversion technique  $[13–16]$  $[13–16]$  to generate a synthetic dataset from the trained model. The last step involves the adoption of data augmentation and relabeling strategies [\[17\]](#page-9-12) to substantially enhance dataset diversity. Remarkably,  $SRe<sup>2</sup>L$  obviates the necessity for evaluation on the original dataset by leveraging the network, trained on the original data, as an effective surrogate. This strategy significantly decreases computational demands, thereby facilitating scalability to large-scale datasets.

However, our analysis highlights a significant concern related to the data diversity generated by SRe<sup>2</sup>L, as depicted in Figure [1.](#page-0-0) The recurrence of repetitive patterns in synthetic images leads to a decrease in the efficiency of  $SRe<sup>2</sup>L$ , limiting its ability to comprehensively represent the original data distribution. Two key factors collectively contribute to this problem. First,  $SRe<sup>2</sup>L$  lacks explicit guidance from evaluations conducted on the original dataset. Therefore, it cannot identify which parts of the samples should be further distilled. Second, the model inversion technique tends to generate patterns that are most representative – or, in other terms, simpler – from the perspective of the trained "teacher" models. This leads to the synthetic dataset's insufficient exploration of complex patterns and the issue of image homogenization.

Building on this analysis, we propose a method called Curriculum Dataset Distillation (CUDD) that harmonizes scalability with representational diversity. This approach segments the creation of the synthetic dataset into a series of curricular stages, systematically synthesizing images in a progression from simple to complex to ensure comprehensive coverage of the original patterns. At the beginning of each curriculum, we first evaluate the original dataset through a "student" neural network trained on all synthetic samples of prior curriculum to identify data instances that cannot be accurately classified, indicating areas where representational diversity is lacking.

However, the volume of the misclassified subset significantly exceeds the capacity of the synthetic dataset, necessitating further compression and representation by fewer synthetic samples. To maximize data efficiency in curriculum learning, we aim to increase the difficulty of the synthetic data as much as possible without compromising its alignment with the misclassified subset and its semantic correctness. The objective function is tripartite, encapsulating the essence of our method's innovation. The first component ensures that synthetic images are classified accurately by a "teacher" network trained on the original dataset, thus ensuring the semantic correctness of the synthetic samples. The second component, an explicit regularization term, aligns the synthetic images with the intricacies of the misclassified subset, maintaining fidelity to the most challenging data aspects. Lastly, the adversarial loss from the "student" network is strategically applied to further increase the difficulty and differentiate newly generated synthetic samples from all prior ones. As curriculum learning progresses, the capability of the "student" network gradually approaches that of the "teacher" network, providing increasingly effective adversarial feedback to generate more complex patterns.

Comprehensive experiments validate CUDD's superior performance, outstripping prior state-ofthe-art methods across all real-world benchmark datasets. Specifically, CUDD achieves average

improvements of  $11.1\%$  on Tiny-ImageNet [\[18\]](#page-9-13), 9.0% on ImageNet-1K [\[12\]](#page-9-9), and 7.3% on ImageNet-21K [\[19\]](#page-9-14), and notably doubles the performance on heterogeneous architectures such as DeiT-Tiny [\[20\]](#page-9-15) and MLP-Mixer [\[21\]](#page-9-16).

## 2 Method

#### 2.1 Advantages and Limitations of Prior Methods

Utilizing an extensive labeled dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$ , dataset distillation [\[2,](#page-9-1) [22\]](#page-9-17) aims to generate a significantly smaller synthetic dataset  $S = \{(\tilde{x}_i, y_i)\}_{i=1}^{|S|}$ , where  $|S| \ll |\mathcal{T}|$ . In this context, x denotes the original image, whereas  $\tilde{x}$  signifies the synthetic image of identical resolution. The objective is to ensure that in the process of training any neural networks, the employment of synthetic datasets as substitutes for original datasets maintains equivalent performance while significantly diminishing the training costs. The dataset distillation can be formulated as a bi-level optimization task:

$$
S^* := \underset{S}{\arg\min} \mathcal{L}_{ce}(\phi^*, \mathcal{T})
$$
  
s.t.  $\phi^* := \underset{\phi}{\arg\min} \mathcal{L}_{ce}(\phi, \mathcal{S}).$  (1)

In this framework, the inner optimization pertains to refining the proxy neural network  $\phi$  using the synthetic dataset  $S$ , whereas the outer optimization involves assessing the performance of this optimized network  $\phi^*$  on the original dataset T. However, achieving the optimal solution for this problem presents considerable challenges. This difficulty arises primarily because the inner optimization does not constitute a convex problem, and the sheer number of parameters in both the neural network and the synthetic dataset is substantial. Various methodologies, as referenced in the literature  $\left[2, 9\right]$  $\left[2, 9\right]$  $\left[2, 9\right]$ , employ implicit gradient techniques, calculating these gradients through back-propagation across the unrolled computational graph. Nonetheless, these methods frequently encounter significant computational and memory demands  $[3, 23]$  $[3, 23]$  $[3, 23]$ , which pose substantial barriers when attempting to scale to extensive datasets like ImageNet [\[12\]](#page-9-9).

Conversely,  $SRe<sup>2</sup>L$  [\[1\]](#page-9-0) introduces a disentangled framework that directly generates the synthetic dataset via the model inversion technique  $[13–16]$  $[13–16]$ . This process is distinct in that it does not require feedback evaluation on the original dataset:

<span id="page-2-1"></span><span id="page-2-0"></span>
$$
\theta^* := \arg\min_{\theta} \mathcal{L}_{ce}(\theta, \mathcal{T}),\tag{2}
$$

$$
\mathcal{S}^* := \underset{\mathcal{S}}{\arg\min} \mathcal{L}_{\text{ce+bn}}(\theta^*, \mathcal{S}). \tag{3}
$$

The above formulations succinctly elucidate the  $SRe<sup>2</sup>L$  approach. Initially, in the phase described by Equation [\(2\)](#page-2-0), the methodology begins with training a teacher network using the original dataset. Subsequently, the phase specified in Equation [\(3\)](#page-2-1) is focused on generating the synthetic dataset from the teacher network. This generation process is accomplished by directly optimizing synthetic images from random noise. The overarching aim is to reconstruct the predictions and the batch statistics of the original dataset, employing both a standard cross-entropy loss  $\mathcal{L}_{ce}$  and a batch statistic loss  $\mathcal{L}_{bn}$ :

$$
\mathcal{L}_{bn} = \sum_{l} \|\mu_l(X_{\mathcal{S}}) - BN_l^{\mu}\|_2 + \|\sigma_l(X_{\mathcal{S}}) - BN_l^{\sigma}\|_2, \tag{4}
$$

where  $\mu_l(X_S)$  and  $\sigma_l(X_S)$  denote the batch statistics of the synthetic images preceding each batch normalization layer, with l indicating the layer index. Furthermore,  $BN_l^{\mu}$  and  $BN_l^{\sigma}$  represent the respective trained parameters for each batch normalization layer.

Initially developed for white-box inverse attacks on neural networks [\[24,](#page-10-0) [25\]](#page-10-1), the model inversion technique has since found widespread application in various fields, such as knowledge distillation [\[26,](#page-10-2) [16,](#page-9-11) [27\]](#page-10-3), network compression [\[28–](#page-10-4)[30\]](#page-10-5), and continual learning [\[31,](#page-10-6) [32\]](#page-10-7), noted for its ability to replace the need for the original dataset. However, its application to dataset distillation encounters specific challenges: first, the absence of feedback evaluation on the original dataset implies that the synthetic dataset might not comprehensively represent the entire original data distribution; second, while model inversion is adept at producing highly representative patterns, it often falls short in exploring more complex and rare patterns. This limitation is evident in the comparison presented in Figure [1.](#page-0-0) Building

Image /page/3/Figure/0 description: This diagram illustrates a curriculum learning framework for training neural networks. It begins with an initial curriculum (Curriculum 1: j-1) and an original dataset. The process involves curriculum evaluation, creating a sampled subset (Tj), and adversarial optimization to generate a synthetic subset (S\*j). This synthetic subset is then used to train a student network (φj), which is part of the next curriculum (Curriculum j+1: J). The diagram also shows a feedback loop where a previous student network (φ\*j-1) and a teacher network (θ\*) are used with the sampled subset (Tj) to generate 'unclassifiable' and 'classifiable' data, contributing to adversarial loss (ℒadv), batch normalization + cross-entropy loss (ℒbn+ce), and regularization loss (ℒreg). The diagram uses icons of books to represent curricula, images of birds for datasets, and neural network diagrams to represent the models. Fire icons indicate trainable parameters, and snowflake icons indicate frozen parameters.

Figure 2: Curriculum Dataset Distillation Overview. A single curriculum comprises three key phases: 1) Initial selection of samples from the original dataset that are misclassified by the previous curriculum's student network but correctly identified by the teacher network, serving as seeds for synthetic sample generation. 2) Optimization of synthetic samples using the objective function detailed in Equation [\(6\)](#page-4-0). 3) Integration of both existing and newly synthesized samples to train an updated student network.

on this analysis, we propose a curriculum dataset distillation (CUDD) that integrates the strengths of previous methods. This strategy is founded on key principles: 1) reintroducing feedback evaluation of the original dataset while maintaining a controlled increment in computational expense, and 2) motivating the model to generate a diverse array of samples, facilitating exploration from simpler to more complex patterns.

#### 2.2 Curriculum Dataset Distillation

We commence by segregating the synthetic dataset into  $J$  distinct, non-overlapping subsets, expressed as  $\bigcup_{j=1}^{J} S_j = S$ , with J signifying the aggregate number of curricula. The strategy is structured to sequentially generate synthetic subsets, progressing through each curriculum in turn.

Curriculum Feedback Evaluation. Initially, a teacher neural network is trained, with its corresponding weights denoted as  $\theta^*$ , employing the complete original dataset  $\mathcal T$ , as explicated in Equation [\(2\)](#page-2-0). In the bi-level optimization method, each iteration necessitates an evaluation of the original dataset, a process that presents significant scalability challenges. In contrast,  $SRe<sup>2</sup>L$  omits explicit evaluation of the original dataset. We introduce a mediating strategy between these two methods, implementing feedback evaluation at the onset of each curriculum:

<span id="page-3-0"></span>
$$
\text{Init } \mathcal{S}_j \text{ with } \mathcal{T}_j \sim R(\theta^*, \mathcal{T} \setminus \mathcal{T}_{1:j-1}) \cap W(\phi^*_{j-1}, \mathcal{T} \setminus \mathcal{T}_{1:j-1}), \tag{5}
$$

where  $\phi_{j-1}^*$  symbolizes the weight of the preceding student neural network trained on  $\mathcal{S}_{1:j-1}^*$ . The function  $R(\theta^*, \mathcal{T} \setminus \mathcal{T}_{1:j-1})$  selects correctly classified samples from  $\mathcal{T} \setminus \mathcal{T}_{1:j-1}$  using the teacher network  $\theta^*$ . Conversely,  $W(\phi_{j-1}^*, \mathcal{T} \setminus \mathcal{T}_{1:j-1})$  identifies misclassified samples, acting as the erroneous subset selector.

As delineated in Equation [\(5\)](#page-3-0), we perform a random sampling without replacement of a subset  $\mathcal{T}_i$ from instances that are correctly classified by the teacher model yet misclassified by the preceding student model. Importantly, the subset  $\mathcal{T}_j$  is meticulously curated to mirror the size of  $\mathcal{S}_j$ , thereby ensuring that  $|\mathcal{T}_j| = |\mathcal{S}_j|$ . This facilitates the initialization of  $\mathcal{S}_j$  with the elements of  $\mathcal{T}_j$ . This strategy, grounded in the easy-to-hard training paradigm common in curriculum learning [\[33,](#page-10-8) [34\]](#page-10-9), presents multiple advantages: it ensures synthetic image diversity by progressively increasing difficulty, facilitates foundational feature learning with simpler images initially using a limited set, addresses more complex situations with a larger image pool in later stages, and permits reusing previously generated synthetic images, eliminating the need for their regeneration.

Adversarial Data Optimization. Typically, given the significantly lower quantity of synthetic images compared to the original images, the subset  $\mathcal{T}_i$  can cover only a minimal fraction of the misclassified images. Solely employing the previously mentioned initialization method is markedly insufficient.

Algorithm 1 Curriculum Dataset Distillation

**Input:** original dataset  $\mathcal{T}$ , pre-trained teacher network  $\theta^*$ , number of curricula  $J$ for  $j = 1$  to  $J$  do if  $j = 1$  then  $\triangleright$  Omit the adversarial loss in Equation. [\(6\)](#page-4-0)  $\triangleright$  Initialize  $S_j$  via correct subset selection:  $\mathcal{T}_j \sim R(\theta^*, \mathcal{T}), \mathcal{S}_j \leftarrow \mathcal{T}_j$ else ⊳ Initialize  $S_j$  via feedback evaluation on  $\phi_{j-1}^*$ :  $\mathcal{T}_j \sim R(\theta^*, \mathcal{T} \setminus \mathcal{T}_{1:j-1}) \cap W(\phi_{j-1}^*, \mathcal{T} \setminus \mathcal{T}_{1:j-1})$  $\mathcal{S}_j \leftarrow \mathcal{T}_j$ end if repeat  $\triangleright$  Compute distillation loss  $\mathcal{L}(\theta^*, \phi_{j-1}^*, \mathcal{T}_j, \mathcal{S}_j)$  according to Equation. [\(6\)](#page-4-0)  $\triangleright$  Update  $S_j$  with respect to the loss:  $\mathcal{\tilde{S_j}} \leftarrow \mathcal{S_j} - \nabla_{\mathcal{S_j}} \mathcal{L} \tilde(\theta^*, \phi_{j-1}^*, \mathcal{T}_j, \mathcal{S}_j)$ **until**  $\check{\mathcal{S}}_j$  converged,  $\mathcal{S}_j^* \leftarrow \mathcal{S}_j$ ▷ Unify the synthetic dataset:  $S_{1:j}^* \leftarrow S_{1:j-1}^* \cup S_j^*$ <br>  $\triangleright$  Train the student network on  $S_{1:j}^*$  to get  $\phi_j^*$ end for **Output:** The final synthetic dataset  $S \leftarrow S^*_{1:J}$ 

<span id="page-4-1"></span>Consequently, we introduce our synthetic data optimization objective, which integrates an adversarial loss to further increase the difficulty and data efficiency of each synthetic image:

<span id="page-4-0"></span>
$$
\mathcal{S}_j^* := \underset{\mathcal{S}_j}{\arg\min} \mathcal{L}_{\text{ce+bn}}(\theta^*, \mathcal{S}_j) + \alpha_{\text{reg}} \mathcal{L}_{\text{reg}}(\mathcal{T}_j, \mathcal{S}_j) + \alpha_{\text{adv}} \mathcal{L}_{\text{adv}}(\phi_{j-1}^*, R(\theta^*, \mathcal{S}_j)).
$$
 (6)

Our objective encompasses three components; the first component aligns with  $SRe^{2}L$  (Equation [\(3\)](#page-2-1)), focusing on distilling synthetic images through model inversion from the teacher network, which is trained on the original dataset. From the curriculum learning viewpoint, this goal further suggests that the synthetic images are expected to be accurately classified by the teacher network. The second component is a regularization loss, ensuring that  $S_i$  remains aligned with its initial state  $\mathcal{T}_i$ . This is realized through the adoption of a Mean Squared Error (MSE) loss, applicable within either the pixel or the teacher network's feature space. Our experimental results reveal that the regularization loss  $\mathcal{L}_{reg}$  significantly enhances diversity and acts as a complement to  $\mathcal{L}_{ce+bn}$ . While  $\mathcal{L}_{ce+bn}$  ensures that  $S$  is derived from a proxy distribution akin to the original data distribution, it does not ensure the uniqueness of each sample. Conversely,  $\mathcal{L}_{reg}$ , by sampling  $\mathcal{T}_j$  from  $\mathcal{T}$  without replacement, ensures each synthetic sample's distinctiveness.

The third component of our approach is the adversarial loss. Instead of employing the cross-entropy loss, we utilize the non-saturating loss, which has been demonstrated to be more effective in adversar-ial training contexts, as evidenced in the literature [\[35,](#page-10-10) [36\]](#page-10-11):

$$
\mathcal{L}_{adv} = -\frac{1}{N} \sum_{i=1}^{N} \sum_{c=1}^{C} \mathbb{1}[y_i = c] \log(1 - F(\phi_{j-1}^*, \tilde{x}_i)^{[c]}), \quad \text{s.t.} \quad (\tilde{x}_i, y_i) \in R(\theta^*, \mathcal{S}_j), \tag{7}
$$

where N signifies the batch size, C stands for the overall class count,  $(\tilde{x}_i, y_i)$  denotes the pair of a synthetic image and its associated label, and  $F(\phi_{j-1}^*, \tilde{x}_i)$  signifies the softmax output of the student network. This objective is to further push the synthetic images towards the decision boundary to improve the informativeness of the synthetic images. It should be emphasized that the adversarial loss is selectively applied to images accurately identified by the teacher network, denoted as  $R(\theta^*, \mathcal{S}_j)$ . This approach ensures the prevention of synthetic image collapse.

**Student Network Training.** Following the generation of synthetic images for the current curriculum, we integrate these with all synthetic images produced in previous curricula to train the new student network for the subsequent curriculum. To further augment diversity, we also implement the relabeling technique  $[17]$  utilized in SRe<sup>2</sup>L:

$$
\phi_j^* := \arg\min_{\phi} \mathcal{L}_{ce}(\phi, \mathcal{S}_{1:j}^*). \tag{8}
$$

<span id="page-5-0"></span>

|              | Dataset                                              |                             | Tiny-ImageNet    | $ImageNet-1K$               |                             |                             |                  | ImageNet-21K       |                  |
|--------------|------------------------------------------------------|-----------------------------|------------------|-----------------------------|-----------------------------|-----------------------------|------------------|--------------------|------------------|
| Architecture | <b>IPC</b>                                           | 50                          | 100              | 10                          | 50                          | 100                         | 200              | 10                 | 20               |
| ResNet-18    | SRe <sup>2</sup> L[1]                                | $41.1 + 0.4$                | $49.7_{\pm 0.3}$ | $21.3{\scriptstyle \pm0.6}$ | $46.8 + 0.2$                | $52.8 + 0.3$                | $57.0 + 0.4$     | $19.3_{\pm 0.3}^*$ | $21.6 + 0.3*$    |
|              | $\text{C} \text{U} \text{D} \text{D}$ (Ours)         | $55.6 + 0.2$                | $56.8 + 0.2$     | $39.0_{\pm 0.4}$            | $57.4_{\pm 0.2}$            | $61.3{\scriptstyle \pm0.1}$ | $65.0_{\pm 0.1}$ | $28.0_{\pm 0.2}$   | $34.9_{\pm0.1}$  |
| ResNet-50    | SRe <sup>2</sup> L[1]                                | $42.2 + 0.5$                | $51.2 + 0.4$     | $28.4_{\pm0.1}$             | $55.6 + 0.3$                | $61.0 + 0.4$                | $64.6 + 0.3$     | $28.6 + 0.4*$      | $30.5 + 0.2*$    |
|              | $\text{C} \text{U} \text{D} \text{D}$ (Ours)         | $57.0_{\pm0.2}$             | $58.8 + 0.4$     | $46.2{\scriptstyle \pm0.6}$ | $63.6{\scriptstyle \pm0.2}$ | $66.7{\scriptstyle \pm0.3}$ | $68.6 + 0.2$     | $34.1_{\pm 0.4}$   | $36.1_{\pm0.1}$  |
| ResNet-101   | SRe <sup>2</sup> L[1]                                | $42.5{\scriptstyle \pm0.2}$ | $51.5 + 0.3$     | $30.9_{\pm 0.1}$            | $60.8 + 0.5$                | $62.8 + 0.2$                | $65.9_{\pm 0.3}$ | $29.0_{\pm 0.3}$ * | $32.5 + 0.1*$    |
|              | $\textbf{C} \textbf{U} \textbf{D} \textbf{D}$ (Ours) | $57.4_{\pm 0.3}$            | $59.4_{\pm 0.5}$ | $46.8{\scriptstyle \pm0.3}$ | $64.9{\scriptstyle \pm0.3}$ | $67.2_{\pm0.1}$             | $69.0_{\pm 0.2}$ | $35.4 + 0.4$       | $36.9_{\pm 0.3}$ |

Table 1: Comparisons on large-scale datasets. CUDD achieves state-of-the-art performance across all evaluated neural network architectures, under various images per class (IPC) configurations, and across all evaluated datasets. <sup>∗</sup> denotes results obtained using the official code, while other results are directly taken from the original paper [\[1\]](#page-9-0).

<span id="page-5-1"></span>Image /page/5/Figure/2 description: A bar chart compares the test accuracy (%) of two methods, 'Ours' (red bars) and 'SRe2L' (blue bars), across various unseen architectures. The architectures listed on the x-axis are DeiT-Tiny, Swin-Tiny, ConvNeXt-Tiny, DenseNet-121, EfficientNet-V2-S, MobileNet-V2, RegNet-Y-8GF, ShuffleNet-V2-2.0X, ResMLP-S24, and MLP-Mixer-S/16. The y-axis ranges from 20% to 70%. For DeiT-Tiny, 'Ours' achieves 37.1% and 'SRe2L' achieves 15.4%. For Swin-Tiny, 'Ours' achieves 57.9% and 'SRe2L' achieves 39.2%. For ConvNeXt-Tiny, 'Ours' achieves 64.9% and 'SRe2L' achieves 53.5%. For DenseNet-121, 'Ours' achieves 61.0% and 'SRe2L' achieves 49.7%. For EfficientNet-V2-S, 'Ours' achieves 63.8% and 'SRe2L' achieves 54.1%. For MobileNet-V2, 'Ours' achieves 54.2% and 'SRe2L' achieves 36.6%. For RegNet-Y-8GF, 'Ours' achieves 65.9% and 'SRe2L' achieves 60.3%. For ShuffleNet-V2-2.0X, 'Ours' achieves 58.9% and 'SRe2L' achieves 46.1%. For ResMLP-S24, 'Ours' achieves 56.9% and 'SRe2L' achieves 36.6%. For MLP-Mixer-S/16, 'Ours' achieves 43.1% and 'SRe2L' achieves 21.2%.

Figure 3: Comparisons on heterogeneous architectures. CUDD achieves state-of-the-art performance on all 10 heterogeneous architectures with substantial leads. Experiments are conducted on ImageNet-1K using 50 images per class.

It is noteworthy that once the complete synthetic dataset is generated, both the teacher and student networks can be discarded, retaining solely the synthetic dataset for downstream tasks. The entirety of our algorithm is delineated in Algorithm [1.](#page-4-1) Given that the initial curriculum lacks a trained student network, we omit adversarial loss and select  $\mathcal{T}_1$  from  $\mathcal{T}$  only via the correct subset selection function.

## 3 Experiments

#### 3.1 Dataset and Implementation Details

We evaluate CUDD on various datasets, including : Tiny-ImageNet  $[18]$ , ImageNet-1K  $[12]$ , ImageNet-21K-P [\[19\]](#page-9-14), CIFAR-10 and CIFAR-100 [\[11\]](#page-9-8).

In line with previous research practices  $[3, 4]$  $[3, 4]$  $[3, 4]$ , we employ the "Images Per Class" (IPC) metric to indicate the capacity of the synthetic dataset. To uphold the diversity of the synthetic data, the number of curriculum stages, denoted as J, should grow in tandem with the increase in IPC. To strike a balance between scalability and effectiveness, we configure the growth of  $J$  to follow a logarithmic increase with respect to IPC. More precisely, we define J as follows:  $J = \max(0, \lfloor log_2(\frac{IPC}{5}) \rfloor) + 1$ . For the hyperparameters specified in Equation [\(6\)](#page-4-0), we set  $\alpha_{reg} = \alpha_{adv} = 1$ . Regarding the proxy neural network architecture for dataset distillation, we generally employ ResNet-18 unless otherwise specified, consistent with the architecture used in  $SRe^{2}L$ . For Tiny-ImageNet, the first 7×7 Conv layer of ResNet-18 is replaced by a  $3\times3$  Conv layer and the maxpool layer is discarded, following [\[1,](#page-9-0) [37\]](#page-10-12). We train 3 randomly initialized evaluation networks on the synthetic dataset to obtain the average accuracy and the error bar.

#### 3.2 Quantitative Comparisons

As  $SRe^{2}L$  [\[1\]](#page-9-0) is the first method that achieves satisfactory performance on large-scale datasets such as ImageNet-1K and ImageNet-21K, we strictly follow its experimental setup and conduct comprehensive comparisons with it. The experimental results are detailed in Table [1](#page-5-0) and can be summarized as follows: 1) Our method **consistently** outperforms  $SRe<sup>2</sup>L$  across all evaluated models, IPC settings, and evaluated datasets, achieving average improvements of 11.1% on Tiny-ImageNet, 9.0% on ImageNet-1K, and 7.3% on ImageNet-21K. 2) Both  $SRe<sup>2</sup>L$  and our approach demonstrate robust generalization ability beyond the specific network architecture used in the distillation process.

<span id="page-6-0"></span>

| Method              | ConvNet-4            | ResNet-18            | DenseNet-121         | RegNet-Y-800MF       | MobileNet-V2         | EfficientNet-B0      | Avg. |
|---------------------|----------------------|----------------------|----------------------|----------------------|----------------------|----------------------|------|
| <b>DM</b> [23]      | $24.1 	ext{ ± } 0.3$ | $29.9 	ext{ ± } 0.4$ | $24.9 	ext{ ± } 0.3$ | $16.5 	ext{ ± } 0.2$ | $18.4 	ext{ ± } 0.4$ | $21.8 	ext{ ± } 0.3$ | 22.6 |
| <b>MTT [4]</b>      | $28.0 	ext{ ± } 0.3$ | $30.9 	ext{ ± } 0.2$ | $29.0 	ext{ ± } 0.4$ | $18.3 	ext{ ± } 0.5$ | $19.8 	ext{ ± } 0.1$ | $26.9 	ext{ ± } 0.2$ | 25.5 |
| IDC $[5]$           | $25.2 	ext{ ± } 0.2$ | $32.4 	ext{ ± } 0.7$ | $29.1 	ext{ ± } 0.2$ | $24.3 	ext{ ± } 0.3$ | $25.6 	ext{ ± } 0.5$ | $27.0 	ext{ ± } 0.4$ | 27.3 |
| <b>DREAM</b> [38]   | $25.6 	ext{ ± } 0.4$ | $32.9 	ext{ ± } 0.2$ | $29.6 	ext{ ± } 0.5$ | $25.1 	ext{ ± } 0.4$ | $26.2 	ext{ ± } 0.4$ | $27.0 	ext{ ± } 0.3$ | 27.7 |
| DATM [7]            | $39.7 	ext{ ± } 0.3$ | $43.6 	ext{ ± } 0.2$ | $40.1 	ext{ ± } 0.4$ | $36.3 	ext{ ± } 0.3$ | $35.4 	ext{ ± } 0.3$ | $37.8 	ext{ ± } 0.2$ | 38.8 |
| $	ext{CUDD}$ (Ours) | $45.2 	ext{ ± } 0.2$ | $46.2 	ext{ ± } 0.1$ | $42.8 	ext{ ± } 0.2$ | $41.2 	ext{ ± } 0.3$ | $35.4 	ext{ ± } 0.2$ | $38.2 	ext{ ± } 0.4$ | 41.5 |

Table 2: Cross-architecture performance on Tiny-ImageNet with 50 images per class. All the methods adopt 4-depth ConvNet for the distillation stage for fair comparisons.

<span id="page-6-2"></span>Image /page/6/Figure/2 description: The image displays a grid of six real images in the top row and six synthetic images in the bottom row. The real images show a bird on a branch, people riding horses on a road, birds at a feeder, a bullfinch, a puppy, and a soccer game. The synthetic images are artistic interpretations of the real images, with a painterly style. The first synthetic image shows two birds on a branch with a blue background. The second shows people riding horses in a mountainous landscape. The third shows birds at a feeder with a colorful, abstract background. The fourth shows a bullfinch in a grassy area. The fifth shows a puppy. The sixth shows people playing soccer. The text 'Real' is to the left of the top row, and 'Syn.' is to the left of the bottom row.

Figure 5: Comparisons between real images employed as initial seeds and the corresponding optimized synthetic images.

Our method demonstrates more significant generalization abilities compared to  $SRe<sup>2</sup>L$  on heterogeneous architectures. We conduct experiments across 10 heterogeneous architectures on ImageNet-1K, including DeiT-Tiny [\[20\]](#page-9-15), Swin-Tiny [\[39\]](#page-10-14), ConvNeXt-Tiny [\[40\]](#page-10-15), DenseNet-121 [\[41\]](#page-10-16), EfficientNet-V2 [\[42\]](#page-10-17), MobileNet-V2 [\[43\]](#page-10-18), RegNet [\[44\]](#page-10-19), ShuffleNet-V2 [\[45\]](#page-10-20), ResMLP [\[46\]](#page-10-21), and MLP-Mixer [\[21\]](#page-9-16). The experimental outcomes are illustrated in Figure [3](#page-5-1) and can be encapsulated as follows: 1) Our method realizes a **doubling** of performance on DeiT-Tiny and MLP-Mixer, marking an average improvement of  $15.1\%$  over  $SRe^{2}L$ . 2) In the majority of network architectures, our performance remains competitive with that of ResNet-18, which has an accuracy of 57.4%. Since previous mainstream dataset distillation methods [\[23,](#page-9-18) [4,](#page-9-19) [5,](#page-9-20) [38,](#page-10-13) [7\]](#page-9-4) primarily focus on small datasets, and can hardly handle large-scale datasets, we compare our method with them on Tiny-ImageNet [\[18\]](#page-9-13). For fairness, we adopt the 4-depth ConvNet architecture during the distillation stage for all the compared methods and set the multi-formation factors  $[5]$  to 1 for both IDC  $[5]$  and DREAM  $[38]$ . As shown in Table [2,](#page-6-0) our method achieves state-of-the-art performance across a variety of architectures [\[47,](#page-11-0) [41,](#page-10-16) [48,](#page-11-1) [43,](#page-10-18) [44\]](#page-10-19) among these approaches.

Our adversarial training not only enhances the primary objectives but also offers supplementary advantages. We evaluate the effectiveness of models trained on synthetic datasets in out-ofdomain scenarios  $[49, 50, 6]$  $[49, 50, 6]$  $[49, 50, 6]$  $[49, 50, 6]$  $[49, 50, 6]$ . To assess the robustness of our models, we specifically employ ImageNet-C [\[51\]](#page-11-4), which encompasses a variety of datasets each corrupted by 19 distinct types of perturbations. Figure [4](#page-6-1) presents the mean accuracy across all 19 corruption types at two distinct corruption levels. In comparison to  $SRe<sup>2</sup>L$ , our method demonstrates superior performance under all kinds of corruptions.

<span id="page-6-1"></span>Image /page/6/Figure/6 description: The image displays two radar charts side-by-side, labeled (a) ResNet-18 and (b) DenseNet-121. Both charts compare the performance of 'Ours' (red line) and 'SRe2L' (blue line) across various datasets, indicated by labels around the perimeter such as GN, SN, IN, DB, MB, GB, ZB, SW, FT, FOG, BS, CT, ET, PIX, JPEG, SPN, GSB, SPT, and SAT. Numerical values are shown for each data point on both lines. For ResNet-18, 'Ours' ranges from 22.1 to 54.5, while 'SRe2L' ranges from 13.2 to 42.6. For DenseNet-121, 'Ours' ranges from 24.1 to 58.0, and 'SRe2L' ranges from 14.5 to 45.1. The charts are used to visualize performance metrics, with higher values generally indicating better performance.

Figure 4: Robustness against corruptions on ImageNet-C. Our method demonstrates enhanced robustness in two models trained on the synthetic dataset distilled from ImageNet-1K with IPC 50.

#### 3.3 Ablation Studies

Our ablation study, focusing on the objective function detailed in Equation [\(6\)](#page-4-0), is presented in Table [3.](#page-7-0) The results clearly demonstrate that incorporating both regularization loss and adversarial loss individually enhances performance. Moreover, the synergistic integration of these two losses yields an even more significant performance improvement.

<span id="page-7-0"></span>

| adv. | reg. | ResNet-18                                   | ResNet-50                                   | ResNet-101                                  |       | space   $ResNet-18$         | $ResNet-50$    | $ResNet-101$   | feature                                         | $38.7{	extstyle 	extpm}0.2$ | $45.7{	extstyle 	extpm}0.4$ | $47.5 	extpm 0.4$ |
|------|------|---------------------------------------------|---------------------------------------------|---------------------------------------------|-------|-----------------------------|----------------|----------------|-------------------------------------------------|-----------------------------|-----------------------------|-------------------|
| -    | -    | $34.4 	ext{ 	extpm } 0.2$                   | $39.1 	ext{ 	extpm } 0.3$                   | $42.9 	ext{ 	extpm } 0.3$                   | pixel | $39.0{\scriptstyle \pm0.4}$ | $46.2 \pm 0.6$ | $46.8 \pm 0.3$ | Table 4: Comparative analysis of regularization |                             |                             |                   |
| ✓    | -    | $36.0 	ext{ 	extpm } 0.3$                   | $41.6 	ext{ 	extpm } 0.5$                   | $45.3 	ext{ 	extpm } 0.4$                   |       |                             |                |                |                                                 |                             |                             |                   |
| -    | ✓    | $38.3 	ext{ 	extpm } 0.2$                   | $44.7 	ext{ 	extpm } 0.3$                   | $44.9 	ext{ 	extpm } 0.2$                   |       |                             |                |                |                                                 |                             |                             |                   |
| ✓    | ✓    | <b><math>39.0 	ext{ 	extpm } 0.4</math></b> | <b><math>46.2 	ext{ 	extpm } 0.6</math></b> | <b><math>46.8 	ext{ 	extpm } 0.3</math></b> |       |                             |                |                |                                                 |                             |                             |                   |

Table 3: Ablations on the objective function.

<span id="page-7-2"></span>Image /page/7/Figure/2 description: The image contains two plots. The left plot is a line graph showing test accuracy (%) versus images per class. The x-axis ranges from 5 to 40, and the y-axis ranges from 30 to 90. There are two lines: a red line labeled 'Logarithmic' and a blue line labeled 'Uniform'. Both lines show an increasing trend in test accuracy as the number of images per class increases. The right plot is a bar chart showing cumulative training time (mins) versus images per class. The x-axis ranges from 5 to 40, and the y-axis ranges from 0 to 600. There are two sets of bars for each value on the x-axis: red bars labeled 'Logarithmic' and blue bars labeled 'Uniform'. The blue bars (Uniform) are consistently higher than the red bars (Logarithmic), indicating that the uniform method takes longer to train.

loss applied in pixel space versus feature space.

Image /page/7/Figure/6 description: This image contains two line graphs side-by-side, labeled (a) IPC-20 and (b) IPC-50. Both graphs plot Test Accuracy (%) on the y-axis against Epochs on the x-axis, ranging from 100 to 1000. Each graph shows two lines: a red line labeled "Curriculum" and a blue line labeled "Scratch". In graph (a), the "Curriculum" line starts at approximately 59% accuracy at 100 epochs and rises to about 79% at 1000 epochs. The "Scratch" line starts at approximately 33% accuracy at 100 epochs and rises to about 74% at 1000 epochs. In graph (b), the "Curriculum" line starts at approximately 76% accuracy at 100 epochs and rises to about 85% at 1000 epochs. The "Scratch" line starts at approximately 47% accuracy at 100 epochs and rises to about 85% at 1000 epochs. The "Curriculum" line consistently shows higher accuracy than the "Scratch" line in both graphs, especially in the earlier epochs.

Image /page/7/Figure/7 description: The image contains two figures, Figure 6 and Figure 7. Figure 6 is a comparative analysis of logarithmic curriculum scheduling versus uniform curriculum scheduling. The experiments were conducted on CIFAR-10 using a single RTX 3090 GPU. Figure 6 has two parts: (a) Test Accuracy and (b) Cumulative Training Time. Figure 7 compares student network training iterations, specifically curriculum training (initializing from the previous curriculum) versus training from scratch. Figure 7 also has two parts: (a) IPC-20 and (b) IPC-50. The student network continuously improves.

scratch. The student network continuously improves through the curricula.

We also undertake a detailed comparative study on the application of regularization loss, examining its effectiveness in the pixel space as opposed to the feature space. The experimental results suggest minimal disparities between these two methodologies. Further ablation studies are provided in the supplementary material for comprehensive insight.

Furthermore, we perform ablation of real-image initialization on ImageNet-1K, including three scenarios: 1)  $SRe<sup>2</sup>L$  with real-image initialization, 2) our method without any involvement of real images, and 3) our method with the regularization term  $\mathcal{L}_{reg}$  disabled. As shown in Table [5,](#page-7-1) initialization from real images can bring improvements to  $SRe<sup>2</sup>L$ , but there is still a gap between it and our method. In addition, as shown in Figure [5,](#page-6-2) despite utilizing real images as a

<span id="page-7-1"></span>

| Method             | init.        | $\mathcal{L}_{\text{reg}}$ | IPC 10                           | IPC 50                           |
|--------------------|--------------|----------------------------|----------------------------------|----------------------------------|
| SRe2L              | -            | -                          | $21.3 \pm 0.6$<br>$22.8 \pm 0.3$ | $46.8 \pm 0.2$<br>$48.0 \pm 0.2$ |
| SRe2L              | $\checkmark$ | -                          |                                  |                                  |
| <b>CUDD</b> (Ours) | -            | -                          | $33.5 \pm 0.3$                   | $54.8 \pm 0.1$                   |
| <b>CUDD</b> (Ours) | $\checkmark$ | -                          | $36.0 \pm 0.3$                   | $55.9 \pm 0.2$                   |
| <b>CUDD</b> (Ours) | $\checkmark$ | $\checkmark$               | $39.0 \pm 0.4$                   | $57.4 \pm 0.2$                   |

Table 5: Ablation on real-image initialization.

form of regularization in Equation  $(6)$ , the synthetic images exhibit marked differences from their real-image counterparts, effectively preserving the privacy of the original images, similar to previous dataset distillation methods [\[23,](#page-9-18) [5,](#page-9-20) [22,](#page-9-17) [38,](#page-10-13) [7\]](#page-9-4), which also adopt real image initialization. Moreover, our findings indicate that CUDD tends to produce objects of diverse scales within a single image, thereby enhancing the information density.

### 3.4 Effectiveness and Efficiency of Curriculum Learning

In comparison to  $SRe<sup>2</sup>L$ , the training time overhead in our approach arises from the requirement to train a student network at the end of each curriculum, utilizing all synthetic images that have been generated up to that juncture. We introduce two strategies aimed at reducing the training cost, thereby enhancing the scalability of our method. The first strategy, termed logarithmic curriculum scheduling, aims to decrease the overall number of curricula. As illustrated in Figure  $6$  (a), uniform curriculum scheduling, characterized by a dense series of curricula (IPC-{5, 10, 15, 20, 25, 30, 35, 40}), yields only a marginal improvement (1.6% at IPC-40) compared to the logarithmic curriculum scheduling, which employs a sparser sequence of curricula (IPC-{5, 10, 20, 40}). However, this slight enhancement comes at the cost of doubling the training time (Figure  $6$  (b)). Therefore, we adopt the more cost-effective logarithmic curriculum scheduling.

The second strategy, termed curriculum training, is designed to reduce the number of training iterations required by the student network. Rather than initiating the student network's training from scratch for each curriculum, we employ the optimized parameters of the student network from the preceding curriculum as the initialization point, *i.e.*,  $\phi_j \leftarrow \phi_{j-1}^*$ . Figure [7](#page-7-2) depicts the results on CIFAR-10 under IPC-20 and IPC-50 configurations. Experimental results demonstrate that curriculum training is capable of converging to an accuracy comparable to that achieved by training from scratch, but

requiring only **half** the iterations. Furthermore, as the curriculum progresses, the student network's capabilities steadily improve, enabling it to provide more valuable feedback during the subsequent stages of the curriculum.

# 4 Related Works

**Dataset Distillation.** Dataset distillation  $[2, 22]$  $[2, 22]$  $[2, 22]$  aims to learn a compact synthetic dataset that captures crucial information from the original dataset. [\[2\]](#page-9-1) first proposed a bi-level optimization framework to handle this task, further extended by [\[9\]](#page-9-6). To alleviate the computationally intensive optimization procedure of this formulation, various surrogate objectives have been proposed, including KRR-based approaches  $[52–55]$  $[52–55]$ , parameter-based methods  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$  $[3, 56, 4, 57, 5, 50, 58, 59, 6, 60, 7]$ , and distribution-based techniques [\[23,](#page-9-18) [61–](#page-11-12)[65\]](#page-11-13).

Recent studies have begun to explore fully disentangled methods for large-scale dataset distillation [\[1,](#page-9-0) [66–](#page-11-14)[68\]](#page-11-15), leveraging a trained teacher network to produce synthetic datasets. [\[66\]](#page-11-14) proposes a method for more effective optimization of individual images. [\[67\]](#page-11-16) utilizes multiple teacher architectures for better generalization ability. [\[68\]](#page-11-15) composes each synthetic image by cropping multiple real image patches without further optimization. Compared to these concurrent works, CUDD: 1) performs feedback evaluation on the original dataset to ensure better coverage of its distribution, and 2) considers inter-image diversity, utilizing a novel optimization method to further enhance it.

Coreset Selection and Data Augmentation. Coreset selection [\[69–](#page-11-17)[72\]](#page-12-0) identifies a subset of the most representative samples from the original dataset. Methods like  $[69, 73]$  $[69, 73]$  $[69, 73]$  eliminate redundant samples based on their similarity to the remaining ones. Other approaches [\[74,](#page-12-2) [75\]](#page-12-3) select samples based on their learning difficulty. Data augmentation [\[76](#page-12-4)[–80\]](#page-12-5) applies deterministic or random transformations to increase the diversity of the original data while preserving most of the original information. In contrast, CUDD directly optimizes the synthetic data, resulting in a high distortion rate of the original data and the ability to prevent the leakage of private information. Moreover, data augmentation and our method are complementary to each other.

**Curriculum Learning.** Curriculum learning  $\begin{bmatrix} 33 \end{bmatrix}$  is originally defined as a way to train networks by organizing the order in which data are fed to the network. Data sorting can be based on priori rules [\[33\]](#page-10-8), model performance [\[34,](#page-10-9) [81\]](#page-12-6), and data diversity [\[82,](#page-12-7) [83\]](#page-12-8). Leveraging the curriculum concept, [\[84\]](#page-12-9) decreases the probability of dropout during training. [\[85\]](#page-12-10) proposes to gradually deblur convolutional activation maps. CUDD orchestrates curricula for data synthesis as well as student network training, thereby enriching data diversity while also enhancing the distillation efficiency.

# 5 Conclusion and Limitations

Conclusion. In this paper, we propose CUDD, a simple and neat framework for curriculum dataset distillation. We decompose the synthesis of data into multiple curricula and utilize student networks for knowledge transmission between these curricula. First, we perform feedback evaluation on the previously trained student network, sampling an original subset to serve as initialization points of the synthetic subset. This ensures better coverage of the original data distribution. Second, we utilize both the teacher and student networks for adversarial optimization of the current synthetic subset. This approach enhances data representativeness and fosters better generalization across various network architectures. Finally, we train the student network to gain knowledge of existing synthetic data. We further introduce logarithmic curriculum scheduling and curriculum student training to accelerate the distilling process. Extensive experiments prove that our framework achieves state-of-the-art on various benchmarks.

Limitations. We acknowledge certain limitations in CUDD: 1) At present, our method does not achieve lossless dataset distillation, indicating a potential loss of information during the distillation process on large-scale datasets. 2) There is a possibility that biases present in the original dataset may be preserved or even amplified in the synthetic dataset, potentially leading to biased outcomes.

## References

- <span id="page-9-0"></span>[1] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *NeurIPS*, 2023.
- <span id="page-9-1"></span>[2] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-9-2"></span>[3] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021.
- <span id="page-9-19"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022.
- <span id="page-9-20"></span>[5] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *ICML*, 2022.
- <span id="page-9-3"></span>[6] Xing Wei, Anjia Cao, Funing Yang, and Zhiheng Ma. Sparse parameterization for epitomic dataset distillation. In *NeurIPS*, 2023.
- <span id="page-9-4"></span>[7] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023.
- <span id="page-9-5"></span>[8] Felix Wiewel and Bin Yang. Condensed composite memory continual learning. In *IJCNN*, 2021.
- <span id="page-9-6"></span>[9] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *NeurIPS*, 2022.
- <span id="page-9-7"></span>[10] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *ICML*, 2020.
- <span id="page-9-8"></span>[11] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. Technical report, 2009.
- <span id="page-9-9"></span>[12] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009.
- <span id="page-9-10"></span>[13] Alexander Mordvintsev, Christopher Olah, and Mike Tyka. Inceptionism: Going deeper into neural networks. 2015.
- [14] Aravindh Mahendran and Andrea Vedaldi. Understanding deep image representations by inverting them. In *CVPR*, 2015.
- [15] Alexey Dosovitskiy and Thomas Brox. Inverting visual representations with convolutional networks. In *CVPR*, 2016.
- <span id="page-9-11"></span>[16] Hongxu Yin, Pavlo Molchanov, Jose M Alvarez, Zhizhong Li, Arun Mallya, Derek Hoiem, Niraj K Jha, and Jan Kautz. Dreaming to distill: Data-free knowledge transfer via deepinversion. In *CVPR*, 2020.
- <span id="page-9-12"></span>[17] Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In *ECCV*, 2022.
- <span id="page-9-13"></span>[18] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-9-14"></span>[19] Tal Ridnik, Emanuel Ben-Baruch, Asaf Noy, and Lihi Zelnik-Manor. Imagenet-21k pretraining for the masses. *arXiv preprint arXiv:2104.10972*, 2021.
- <span id="page-9-15"></span>[20] Hugo Touvron, Matthieu Cord, Matthijs Douze, Francisco Massa, Alexandre Sablayrolles, and Hervé Jégou. Training data-efficient image transformers & distillation through attention. In *ICML*, 2021.
- <span id="page-9-16"></span>[21] Ilya O Tolstikhin, Neil Houlsby, Alexander Kolesnikov, Lucas Beyer, Xiaohua Zhai, Thomas Unterthiner, Jessica Yung, Andreas Steiner, Daniel Keysers, Jakob Uszkoreit, et al. Mlp-mixer: An all-mlp architecture for vision. In *NeurIPS*, 2021.
- <span id="page-9-17"></span>[22] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. In *NeurIPS*, 2022.
- <span id="page-9-18"></span>[23] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023.

- <span id="page-10-0"></span>[24] Matt Fredrikson, Somesh Jha, and Thomas Ristenpart. Model inversion attacks that exploit confidence information and basic countermeasures. In *SIGSAC*, 2015.
- <span id="page-10-1"></span>[25] Zecheng He, Tianwei Zhang, and Ruby B Lee. Model inversion attacks against collaborative inference. In *ACSAC*, 2019.
- <span id="page-10-2"></span>[26] Matan Haroush, Itay Hubara, Elad Hoffer, and Daniel Soudry. The knowledge within: Methods for data-free model compression. In *CVPR*, 2020.
- <span id="page-10-3"></span>[27] Gongfan Fang, Jie Song, Xinchao Wang, Chengchao Shen, Xingen Wang, and Mingli Song. Contrastive model invertion for data-free knolwedge distillation. In *IJCAI*, 2021.
- <span id="page-10-4"></span>[28] Yaohui Cai, Zhewei Yao, Zhen Dong, Amir Gholami, Michael W Mahoney, and Kurt Keutzer. Zeroq: A novel zero shot quantization framework. In *CVPR*, 2020.
- [29] Xiangguo Zhang, Haotong Qin, Yifu Ding, Ruihao Gong, Qinghua Yan, Renshuai Tao, Yuhang Li, Fengwei Yu, and Xianglong Liu. Diversifying sample generation for accurate data-free quantization. In *CVPR*, 2021.
- <span id="page-10-5"></span>[30] Yunshan Zhong, Mingbao Lin, Gongrui Nan, Jianzhuang Liu, Baochang Zhang, Yonghong Tian, and Rongrong Ji. Intraq: Learning synthetic images with intra-class heterogeneity for zero-shot network quantization. In *CVPR*, 2022.
- <span id="page-10-6"></span>[31] James Smith, Yen-Chang Hsu, Jonathan Balloch, Yilin Shen, Hongxia Jin, and Zsolt Kira. Always be dreaming: A new approach for data-free class-incremental learning. In *ICCV*, 2021.
- <span id="page-10-7"></span>[32] Huan Liu, Li Gu, Zhixiang Chi, Yang Wang, Yuanhao Yu, Jun Chen, and Jin Tang. Few-shot classincremental learning via entropy-regularized data-free replay. In *ECCV*, 2022.
- <span id="page-10-8"></span>[33] Yoshua Bengio, Jérôme Louradour, Ronan Collobert, and Jason Weston. Curriculum learning. In *ICML*, 2009.
- <span id="page-10-9"></span>[34] M Kumar, Benjamin Packer, and Daphne Koller. Self-paced learning for latent variable models. In *NeurIPS*, 2010.
- <span id="page-10-10"></span>[35] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. In *NeurIPS*, 2014.
- <span id="page-10-11"></span>[36] Teppei Suzuki. Teachaugment: Data augmentation optimization using teacher knowledge. In *CVPR*, 2022.
- <span id="page-10-12"></span>[37] Kaiming He, Haoqi Fan, Yuxin Wu, Saining Xie, and Ross Girshick. Momentum contrast for unsupervised visual representation learning. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 9729–9738, 2020.
- <span id="page-10-13"></span>[38] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. In *ICCV*, 2023.
- <span id="page-10-14"></span>[39] Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei, Zheng Zhang, Stephen Lin, and Baining Guo. Swin transformer: Hierarchical vision transformer using shifted windows. In *ICCV*, 2021.
- <span id="page-10-15"></span>[40] Zhuang Liu, Hanzi Mao, Chao-Yuan Wu, Christoph Feichtenhofer, Trevor Darrell, and Saining Xie. A convnet for the 2020s. In *CVPR*, 2022.
- <span id="page-10-16"></span>[41] Gao Huang, Zhuang Liu, Laurens Van Der Maaten, and Kilian Q Weinberger. Densely connected convolutional networks. In *CVPR*, 2017.
- <span id="page-10-17"></span>[42] Mingxing Tan and Quoc Le. Efficientnetv2: Smaller models and faster training. In *ICML*, 2021.
- <span id="page-10-18"></span>[43] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *CVPR*, 2018.
- <span id="page-10-19"></span>[44] Ilija Radosavovic, Raj Prateek Kosaraju, Ross Girshick, Kaiming He, and Piotr Dollár. Designing network design spaces. In *CVPR*, 2020.
- <span id="page-10-20"></span>[45] Ningning Ma, Xiangyu Zhang, Hai-Tao Zheng, and Jian Sun. Shufflenet v2: Practical guidelines for efficient cnn architecture design. In *ECCV*, 2018.
- <span id="page-10-21"></span>[46] Hugo Touvron, Piotr Bojanowski, Mathilde Caron, Matthieu Cord, Alaaeldin El-Nouby, Edouard Grave, Gautier Izacard, Armand Joulin, Gabriel Synnaeve, Jakob Verbeek, et al. Resmlp: Feedforward networks for image classification with data-efficient training. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 45(4):5314–5321, 2022.

- <span id="page-11-0"></span>[47] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, 2016.
- <span id="page-11-1"></span>[48] Mingxing Tan and Quoc Le. Efficientnet: Rethinking model scaling for convolutional neural networks. In *ICML*, 2019.
- <span id="page-11-2"></span>[49] Balhae Kim, Jungwon Choi, Seanie Lee, Yoonho Lee, Jung-Woo Ha, and Juho Lee. On divergence measures for bayesian pseudocoresets. In *NeurIPS*, 2022.
- <span id="page-11-3"></span>[50] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, 2022.
- <span id="page-11-4"></span>[51] Dan Hendrycks and Thomas Dietterich. Benchmarking neural network robustness to common corruptions and perturbations. In *ICLR*, 2019.
- <span id="page-11-5"></span>[52] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021.
- <span id="page-11-18"></span>[53] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, 2022.
- [54] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022.
- <span id="page-11-6"></span>[55] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. In *ICML*, 2023.
- <span id="page-11-7"></span>[56] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021.
- <span id="page-11-8"></span>[57] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *ICML*, 2022.
- <span id="page-11-9"></span>[58] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *CVPR*, 2023.
- <span id="page-11-10"></span>[59] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, 2023.
- <span id="page-11-11"></span>[60] Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. In *NeurIPS*, 2023.
- <span id="page-11-12"></span>[61] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022.
- [62] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. In *NeurIPS Workshop*, 2022.
- [63] Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*, 2022.
- [64] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *CVPR*, 2023.
- <span id="page-11-13"></span>[65] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *ICCV*, 2023.
- <span id="page-11-14"></span>[66] Zeyuan Yin and Zhiqiang Shen. Dataset distillation in large data era. *arXiv preprint arXiv:2311.18838*, 2023.
- <span id="page-11-16"></span>[67] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. *arXiv preprint arXiv:2311.17950*, 2023.
- <span id="page-11-15"></span>[68] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. *arXiv preprint arXiv:2312.03526*, 2023.
- <span id="page-11-17"></span>[69] Max Welling. Herding dynamical weights to learn. In *ICML*, 2009.
- [70] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. In *UAI*, 2010.

- [71] Dan Feldman, Matthew Faulkner, and Andreas Krause. Scalable training of mixture models via coresets. In *NeurIPS*, 2011.
- <span id="page-12-0"></span>[72] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. icarl: Incremental classifier and representation learning. In *CVPR*, 2017.
- <span id="page-12-1"></span>[73] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018.
- <span id="page-12-2"></span>[74] Katerina Margatina, Giorgos Vernikos, Loïc Barrault, and Nikolaos Aletras. Active learning by acquiring contrastive examples. In *EMNLP*, 2021.
- <span id="page-12-3"></span>[75] Melanie Ducoffe and Frederic Precioso. Adversarial active learning for deep networks: a margin based approach. *arXiv preprint arXiv:1802.09841*, 2018.
- <span id="page-12-4"></span>[76] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. 2012.
- [77] Hongyi Zhang, Moustapha Cisse, Yann N Dauphin, and David Lopez-Paz. mixup: Beyond empirical risk minimization. In *ICLR*, 2018.
- [78] Sangdoo Yun, Dongyoon Han, Seong Joon Oh, Sanghyuk Chun, Junsuk Choe, and Youngjoon Yoo. Cutmix: Regularization strategy to train strong classifiers with localizable features. In *ICCV*, 2019.
- [79] Shengyu Zhao, Zhijian Liu, Ji Lin, Jun-Yan Zhu, and Song Han. Differentiable augmentation for dataefficient gan training. *NeurIPS*, 2020.
- <span id="page-12-5"></span>[80] Tero Karras, Miika Aittala, Janne Hellsten, Samuli Laine, Jaakko Lehtinen, and Timo Aila. Training generative adversarial networks with limited data. *NeurIPS*, 2020.
- <span id="page-12-6"></span>[81] Yong Jae Lee and Kristen Grauman. Learning the easy things first: Self-paced visual category discovery. In *CVPR*, 2011.
- <span id="page-12-7"></span>[82] Dingwen Zhang, Deyu Meng, Chao Li, Lu Jiang, Qian Zhao, and Junwei Han. A self-paced multipleinstance learning framework for co-saliency detection. In *ICCV*, 2015.
- <span id="page-12-8"></span>[83] Petru Soviany. Curriculum learning with diversity for supervised computer vision tasks. In *ICML Workshop*, 2020.
- <span id="page-12-9"></span>[84] Pietro Morerio, Jacopo Cavazza, Riccardo Volpi, René Vidal, and Vittorio Murino. Curriculum dropout. In *ICCV*, 2017.
- <span id="page-12-10"></span>[85] Samarth Sinha, Animesh Garg, and Hugo Larochelle. Curriculum by smoothing. In *NeurIPS*, 2020.

## Appendix

This appendix is structured as follows:

- In Sec. [A,](#page-13-0) we provide details of the dataset.
- In Sec. [B,](#page-13-1) we present additional qualitative results accompanied by in-depth analyses.
- In Sec. [C,](#page-14-0) we conclude our detailed hyper-parameters and discuss the computational costs.
- In Sec. [D,](#page-15-0) we provide more visualizations.

## <span id="page-13-0"></span>A Dataset Details

We evaluate our methods on the following datasets:

- TinyImageNet  $[18]$  is a 64×64 dataset with 200 classes. Each class has 500 images for training and 50 images for validation.
- ImageNet-1K [\[12\]](#page-9-9) consists of 1,000 classes. The training and validation set contains 1,281,167 images and 50,000 images, respectively. We resize all images to the standard resolution 224×224.
- ImageNet-21K-P [\[19\]](#page-9-14) removes infrequent classes from the original ImageNet-21K, resulting in 10,450 classes in total. There are 11,060,223 images for training and 522,500 images for validation. All images are resized to 224×224 resolution.
- CIFAR-10 [\[11\]](#page-9-8) is a standard small-scale dataset consists of 60,000  $32 \times 32$  resolution images in 10 different classes. For each class, 5,000 images are used for training and 1,000 images are used for validation.
- CIFAR-100 [\[11\]](#page-9-8) contains 100 classes. It has a training set with 50,000 images and a validation set with 10,000 images.

## <span id="page-13-1"></span>B Additional Results and Analyses

Application in Continual Learning. As a widespread application scenario for dataset distillation  $[23, 50, 9, 6]$  $[23, 50, 9, 6]$  $[23, 50, 9, 6]$  $[23, 50, 9, 6]$  $[23, 50, 9, 6]$  $[23, 50, 9, 6]$  $[23, 50, 9, 6]$ , class-continual learning can reflect the average information content and diversity of each class in the synthetic dataset. We conduct continual learning experiments on ImageNet-1K with IPC-10 and use ResNet-18 and DenseNet-121 for evaluation. We randomly divide the 1000 classes into 5 learning steps, *i.e.*, 200 classes per step. As illustrated in Figure [8,](#page-13-2) CUDD consistently achieves the highest test accuracy at every stage for both evaluation models, highlighting its advantage over  $SRe<sup>2</sup>L$ .

<span id="page-13-2"></span>Image /page/13/Figure/15 description: The image contains two line graphs side-by-side, both plotting "Test Accuracy (%)" on the y-axis against "Number of Classes" on the x-axis. The x-axis ranges from 200 to 1000 in increments of 200. The left graph, labeled (a) ResNet-18, shows two lines: "Ours" (red) and "SRe2L" (blue). The "Ours" line starts at approximately 18% accuracy at 200 classes and increases to about 38% at 1000 classes. The "SRe2L" line starts at approximately 6% accuracy at 200 classes and increases to about 21% at 1000 classes. The right graph, labeled (b) DenseNet-121, also shows two lines: "Ours" (red) and "SRe2L" (blue). The "Ours" line starts at approximately 27% accuracy at 200 classes and increases to about 51% at 1000 classes. The "SRe2L" line starts at approximately 7% accuracy at 200 classes and increases to about 23% at 1000 classes.

(a) ResNet-18 (b) DenseNet-121 Figure 8: Application in continual learning. On two different architectures, CUDD consistently maintains the highest test accuracy across all learning steps.

<span id="page-13-3"></span>

| $\alpha$ adv                                   | ResNet-18                        | ResNet-50                        | ResNet-101                       |  | $\alpha$ reg                                   | ResNet-18                        | ResNet-50                        | ResNet-101                       |
|------------------------------------------------|----------------------------------|----------------------------------|----------------------------------|--|------------------------------------------------|----------------------------------|----------------------------------|----------------------------------|
| 0.3                                            | $38.9 \pm 0.2$                   | $45.0 \pm 0.4$                   | $46.7 \pm 0.3$                   |  | 0.3                                            | $38.3 \pm 0.2$                   | $45.3 \pm 0.5$                   | $46.1 \pm 0.4$                   |
| 1.0                                            | <b><math>39.0 \pm 0.4</math></b> | <b><math>46.2 \pm 0.6</math></b> | <b><math>46.8 \pm 0.3</math></b> |  | 1.0                                            | <b><math>39.0 \pm 0.4</math></b> | <b><math>46.2 \pm 0.6</math></b> | <b><math>46.8 \pm 0.3</math></b> |
| 3.0                                            | $38.8 \pm 0.4$                   | $44.0 \pm 0.5$                   | $47.3 \pm 0.4$                   |  | 3.0                                            | $37.9 \pm 0.2$                   | $45.0 \pm 0.4$                   | $45.7 \pm 0.4$                   |
| Table 6: Sensitivity analysis of $\alpha$ adv. |                                  |                                  |                                  |  | Table 7: Sensitivity analysis of $\alpha$ reg. |                                  |                                  |                                  |

Hyper-parameter Sensitivity Analysis. We conduct experiments on the ImageNet-1K dataset using the IPC-10 configuration to assess the sensitivity of the hyper-parameters  $\alpha_{\text{adv}}$  and  $\alpha_{\text{reg}}$ , as indicated in Table [6](#page-13-3) and Table [7,](#page-13-3) respectively. It is evident that these two hyper-parameters exhibit robust performance across a wide range and are not overly sensitive. Consequently, we opt for moderate values in our subsequent experiments.

Additional Results on Small Datasets. Table [8](#page-14-1) lists the evaluation results of different methods on networks involved in their distillation. The significance of these results is constrained, because

<span id="page-14-1"></span>

|                         |                             |                              | $CIFAR-10$                     | $CIFAR-100$                 |                              |  |
|-------------------------|-----------------------------|------------------------------|--------------------------------|-----------------------------|------------------------------|--|
|                         | Method $\setminus$ IPC      | 10                           | 50                             | 10                          | 50                           |  |
|                         | $DC$ [3] (ICLR'21)          | $51.0{\scriptstyle \pm 0.6}$ | $56.8{\scriptstyle \pm 0.4}$   | $28.4_{\pm 0.3}$            | $30.6 + 0.6$                 |  |
|                         | DSA [56] (ICML'21)          | $53.0_{\pm 0.4}$             | $60.3{\scriptstyle \pm0.4}$    | $32.2_{\pm 0.4}$            | $43.1 + 0.3$                 |  |
|                         | KIP $[52]$ (NeurIPS'21)     | $47.2_{\pm 0.4}$             | $57.0_{\pm 0.4}$               | $29.0_{\pm 0.3}$            |                              |  |
|                         | DM [23] (WACV'23)           | $47.6 + 0.6$                 | $62.0 + 0.3$                   | $29.2 + 0.3$                | $42.3 + 0.4$                 |  |
|                         | <b>MTT</b> [4] (CVPR'22)    | $63.7{\scriptstyle \pm0.4}$  | $70.3 + 0.6$                   | $38.2 + 0.4$                | $46.3{\scriptstyle \pm0.3}$  |  |
|                         | $FRePo [53]$ (NeurIPS'22)   | $65.5 + 0.4$                 | $71.7_{+0.2}$                  | $42.5 + 0.2$                | $44.3 + 0.2$                 |  |
| $C3-W128$               | FTD [58] (CVPR'23)          | $66.6{\scriptstyle \pm 0.3}$ | $73.8{\scriptstyle \pm0.2}$    | $43.4_{\pm 0.3}$            | $50.7{\scriptstyle \pm 0.3}$ |  |
|                         | <b>TESLA</b> [59] (ICML'23) | $66.4{\scriptstyle \pm 0.8}$ | $72.6 \pm 0.7$                 | $41.7{\scriptstyle \pm0.3}$ | $47.9_{+0.3}$                |  |
|                         | RCIG [55] (ICML'23)         | $69.1{\scriptstyle \pm0.4}$  | $73.5{\scriptstyle \pm 0.3}$   | $44.1_{\pm 0.4}$            | $46.7{\scriptstyle \pm0.3}$  |  |
|                         | DataDAM $[65]$ (ICCV'23)    | $54.2_{\pm 0.8}$             | $67.0_{\pm 0.4}$               | $34.8 + 0.5$                | $49.4 + 0.3$                 |  |
|                         | SeqMatch [60] (NeurIPS'23)  | $66.2 + 0.6$                 | $74.4 + 0.5$                   | $41.9 + 0.5$                | $51.2 + 0.3$                 |  |
|                         | DATM [7] (ICLR'24)          | $66.8{\scriptstyle \pm0.2}$  | $76.1_{\pm 0.3}$               | $47.2_{\pm 0.4}$            | $55.0_{\pm 0.2}$             |  |
|                         | <b>CUDD</b> (Ours)          | $56.9_{\pm 0.3}$             | $72.7{\scriptstyle \pm0.2}$    | 49.0 $\pm$ 0.4              | $57.5 + 0.2$                 |  |
| $\infty$                | $SRe2L$ [1] (NeurlPS'23)    | $27.8\t\pm\t0.5*$            | $48.9{\scriptstyle \pm 0.6^*}$ | $23.5 + 0.8$                | $51.4_{\pm 0.8}$             |  |
| $\overline{\mathbf{R}}$ | <b>CUDD</b> (Ours)          | $56.2{\scriptstyle \pm 0.4}$ | $84.5{\scriptstyle \pm0.3}$    | $60.3{\scriptstyle \pm0.2}$ | $65.7{\scriptstyle \pm0.2}$  |  |

Table 8: Comparisons with previous dataset distillation methods on small-scale datasets. C3-W128 denotes ConvNet-3-Width-128 [\[3,](#page-9-2) [4\]](#page-9-19). R18 denotes ResNet-18. Evaluation is conducted on the same network structure that is involved in distilling. <sup>∗</sup> denotes results obtained using the official code, and results of  $[3, 56, 52, 23, 4]$  $[3, 56, 52, 23, 4]$  $[3, 56, 52, 23, 4]$  $[3, 56, 52, 23, 4]$  $[3, 56, 52, 23, 4]$  $[3, 56, 52, 23, 4]$  $[3, 56, 52, 23, 4]$  $[3, 56, 52, 23, 4]$  $[3, 56, 52, 23, 4]$  are taken from DC-BENCH  $[22]$ , while other results are directly taken from their original papers.

dataset distillation methods tend to overfit on networks they used in distillation, leading to inflated performance metrics. It's crucial to acknowledge that the results of different network architectures can not be directly compared.

<span id="page-14-2"></span>

|                                                                                                      |    | $CIFAR-10$ |                                                                                           | CIFAR-100 |      |    |  |
|------------------------------------------------------------------------------------------------------|----|------------|-------------------------------------------------------------------------------------------|-----------|------|----|--|
| IPC.                                                                                                 | 10 | -20        | 50                                                                                        | -10       | - 20 | 50 |  |
| none                                                                                                 |    |            | $53.2 \pm 0.4$ $73.6 \pm 0.4$ $84.0 \pm 0.3$ $59.7 \pm 0.4$ $63.0 \pm 0.2$ $65.4 \pm 0.3$ |           |      |    |  |
| constraint 56.2 $\pm$ 0.4 74.3 $\pm$ 0.2 84.5 $\pm$ 0.3 60.3 $\pm$ 0.2 63.5 $\pm$ 0.3 65.7 $\pm$ 0.2 |    |            |                                                                                           |           |      |    |  |
| Table 9: Effect of adversarial constraint.                                                           |    |            |                                                                                           |           |      |    |  |

**Effect of Adversarial Constraint.** We try to allow the existence of the adversarial term regardless of whether the teacher can correctly recognize the current data, as shown in Table [9.](#page-14-2) We find that applying adversarial constraints can demonstrate greater advantages under small storage budgets, as synthetic data patterns are relatively easy to classify.

Adaptation to Other Distillation Objectives. Our method primarily provides a framework to generate synthetic images in separate sequential curricula to improve diversity without introducing significant computational overhead. Theoretically, any dataset distillation objective can be applied within each curriculum. However, a major objective of this paper is to scale up to large-scale datasets. Therefore, we adopt  $SRe^{2}L$  as our primary baseline, as it is the first to achieve satisfactory performance on large-scale datasets. To support this claim, we conduct an experiment where we apply our framework to MTT [\[4\]](#page-9-19) on Tiny-ImageNet with IPC 50. The results of these experiments are presented in Table. [10.](#page-15-1) In this particular setup, synthetic images in each curriculum are optimized by MTT within our proposed framework, and the images are generated sequentially through multiple curricula. As evidence, this combination results in improved cross-architecture performance compared to applying MTT alone.

# <span id="page-14-0"></span>C Hyper-Parameters and Computational Costs

We detail the hyper-parameters for data synthesis and their subsequent evaluation in downstream model training within Table [11](#page-15-2) and Table [12,](#page-15-3) respectively. All of our experiments can be conducted on a single 24GB RTX 3090 GPU. The wall clock distilling time of IPC-1 and peak GPU memory costs are concluded as follows: {35 seconds, 0.8 GB} for CIFAR-10, and {40 seconds, 2.1 GB} for CIFAR-100, {17 minutes, 6.3 GB} for Tiny-ImageNet, {86 minutes, 8.3 GB} for ImageNet-1K, {7.5 hours, 8.3 GB} for ImageNet-21K. It is important to note that the distillation time is primarily

<span id="page-15-2"></span><span id="page-15-1"></span>

| Method                                                                              | ConvNet-4                                                                | ResNet-18      | DenseNet-121                                                    | RegNet-Y-800MF      |     | MobileNet-V2 | EfficientNet-B0 | Avg. |  |
|-------------------------------------------------------------------------------------|--------------------------------------------------------------------------|----------------|-----------------------------------------------------------------|---------------------|-----|--------------|-----------------|------|--|
| MTT[4]                                                                              | $28.0 + 0.3$                                                             | $30.9 + 0.2$   | $29.0 + 0.4$                                                    | $18.3 + 0.5$        |     | $19.8 + 0.1$ | $26.9 + 0.2$    | 25.5 |  |
| CUDD (Ours)                                                                         | $45.2 \pm 0.2$                                                           | $46.2{\pm}0.1$ | $42.8 + 0.2$                                                    | $41.2 + 0.3$        |     | $35.4 + 0.2$ | $38.2 \pm 0.4$  | 41.5 |  |
| MTT [4] w/ our framework                                                            | $45.4 + 0.4$                                                             | $46.5 + 0.3$   | $43.5 + 0.2$                                                    | $43.2 + 0.2$        |     | $37.6 + 0.3$ | $38.7 + 0.5$    | 42.5 |  |
| Table 10: Adapting our curriculum framework to other distillation objectives.       |                                                                          |                |                                                                 |                     |     |              |                 |      |  |
| config<br>CIFAR-10<br>CIFAR-100<br>Tiny-ImageNet<br>$ImageNet-1K$<br>$ImageNet-21K$ |                                                                          |                |                                                                 |                     |     |              |                 |      |  |
| weight decay                                                                        | Adam<br>optimizer<br>$\beta_1, \beta_2 = 0.5, 0.9$<br>momentum<br>$1e-4$ |                |                                                                 |                     |     |              |                 |      |  |
| Ir schedule                                                                         |                                                                          |                |                                                                 | cosine              |     |              |                 |      |  |
| augmentation                                                                        |                                                                          |                |                                                                 | Random Resized Crop |     |              |                 |      |  |
| $\alpha_{\text{adv}}$                                                               |                                                                          |                |                                                                 | 1.0                 |     |              |                 |      |  |
| $\alpha_{\text{reg}}$                                                               |                                                                          | 1.0            |                                                                 |                     |     |              |                 |      |  |
| learning rate                                                                       |                                                                          | 0.25<br>0.25   |                                                                 | 0.25                |     | 0.25<br>0.05 |                 |      |  |
| batch size                                                                          | 10                                                                       | 100            |                                                                 | 100<br>100          |     |              | 100             |      |  |
| iteration                                                                           | 1000                                                                     | 1000           |                                                                 | 4000                |     | 4000<br>2000 |                 |      |  |
|                                                                                     |                                                                          |                |                                                                 |                     |     |              |                 |      |  |
|                                                                                     |                                                                          |                | Table 11: Specific hyper-parameters employed in data synthesis. |                     |     |              |                 |      |  |
| CIFAR-10<br>config<br>CIFAR-100<br>Tiny-ImageNet<br>$ImageNet-1K$<br>$ImageNet-21K$ |                                                                          |                |                                                                 |                     |     |              |                 |      |  |
| optimizer                                                                           |                                                                          |                |                                                                 | AdamW               |     |              |                 |      |  |
|                                                                                     | $\beta_1, \beta_2 = 0.9, 0.999$<br>momentum                              |                |                                                                 |                     |     |              |                 |      |  |
|                                                                                     | $1e-3$<br>learning rate                                                  |                |                                                                 |                     |     |              |                 |      |  |
|                                                                                     | weight decay<br>$1e-2$                                                   |                |                                                                 |                     |     |              |                 |      |  |
| Ir schedule<br>cosine                                                               |                                                                          |                |                                                                 |                     |     |              |                 |      |  |
| augmentation                                                                        |                                                                          |                |                                                                 | Random Resized Crop |     |              |                 |      |  |
| batch size                                                                          | 16                                                                       | 64             |                                                                 | 64                  | 128 |              | 32              |      |  |
| epoch                                                                               | 1000                                                                     | 1000           |                                                                 | 500                 | 300 |              | 300             |      |  |

<span id="page-15-3"></span>Table 12: Comprehensive hyper-parameter configuration for evaluation in downstream model training.

influenced by factors such as the total number of classes, image resolution, and the number of iterations.

# <span id="page-15-0"></span>D Additional Visualizations

Additional visualizations are provided in the subsequent section. Figure [9](#page-16-0) (a)-(e) depict comparisons between our synthetic data and that generated by SRe<sup>2</sup>L. Additionally, extensive collections of our synthetic data on ImageNet-1K and ImageNet-21K are displayed in Figure [10](#page-17-0) and Figure [11,](#page-18-0) respectively.

<span id="page-16-0"></span>Image /page/16/Picture/0 description: The image displays a grid of images comparing two methods, labeled "SRe
2L" and "Ours", across five different categories: (a) Water Ouzel, (b) Peacock, (c) Anemone Fish, (d) Jigsaw Puzzle, and (e) Pomegranate. Each category shows a row of images generated by the "SRe
2L" method above a row of images generated by the "Ours" method. The "SRe
2L" images in each category appear to be stylized or abstract representations, while the "Ours" images are more detailed and realistic, showcasing the subjects of each category more clearly. For example, in (a) Water Ouzel, the "Ours" images show birds in natural settings, whereas the "SRe
2L" images are more impressionistic. Similarly, (b) Peacock shows detailed peacock feathers and eyes in the "Ours" row, contrasted with abstract green patterns in the "SRe
2L" row. (c) Anemone Fish features vibrant clownfish in the "Ours" row and abstract underwater scenes in the "SRe
2L" row. (d) Jigsaw Puzzle displays colorful puzzle pieces in the "Ours" row and abstract, wavy patterns in the "SRe
2L" row. Finally, (e) Pomegranate shows realistic pomegranates in the "Ours" row and abstract red and white patterns in the "SRe
2L" row. The overall presentation is a visual comparison of image generation quality between the two methods.

Figure 9: Additional comparisons between synthetic data distilled by our method and SRe<sup>2</sup>L.

<span id="page-17-0"></span>Image /page/17/Picture/0 description: A close-up, blurry shot of many small, orange and white fish swimming in dark water. Some of the fish are in focus, while others are blurred, giving the impression of movement. The lighting is dim, with some bright spots reflecting off the fish and the water.

Image /page/17/Picture/1 description: A surreal, abstract image features two chickens with human-like legs and feet. The chickens are positioned in the foreground, with their bodies appearing to merge with a rocky, watery landscape. One chicken is closer to the viewer, while the other is slightly behind it. The overall impression is dreamlike and unsettling, with a focus on the unusual combination of animal and human elements.

Image /page/17/Picture/2 description: A colorful macaw parrot is perched on a wooden chair in front of a desk. The parrot has red, blue, and yellow feathers. The background is blurred, but appears to be an indoor setting with furniture.

Image /page/17/Picture/3 description: A surreal, abstract image depicts a creature resembling a snake or dragon coiled around a human hand. The creature has a patterned, scaly texture with shades of brown, orange, and green. The hand appears soft and fleshy, with warm, peachy tones. The overall impression is one of gentle entanglement or protection, with the creature cradling the hand in a soft, organic embrace.

Image /page/17/Picture/4 description: This is an abstract image with a central cluster of golden, brown, and blue shapes that appear to be intertwined. The shapes are smooth and organic, with some appearing to have a metallic sheen. The background is a soft, blurred beige color, with hints of orange and yellow at the top right.

Image /page/17/Picture/5 description: A close-up, abstract image shows a swirling mixture of creamy, light brown and tan colors with streaks of blue and gray. The textures appear smooth and viscous, suggesting a liquid or semi-liquid substance like paint or melted chocolate.

Image /page/17/Picture/6 description: The image shows a close-up of a zebra's mane, with the stripes rendered in a stylized, almost abstract manner. The colors are a mix of blues, purples, and oranges, with a warm glow emanating from the center of the mane.

Image /page/17/Picture/7 description: A close-up, abstract image of a flower with swirling petals in shades of orange, pink, and purple. The center of the flower is dark and textured. To the right, a blurred image of a brown object, possibly a chestnut, rests on a textured surface.

Image /page/17/Picture/8 description: A close-up, stylized image of a lion's face, with a focus on its eyes and ears. The image has a painterly quality, with visible brushstrokes and a warm color palette dominated by browns and oranges. The lion's mane is suggested by textured lines around its face.

Image /page/17/Picture/9 description: This is a surreal, abstract image that appears to be a composite of multiple lion faces blended together. The colors are predominantly warm tones of gold, orange, and brown, with some darker shadows. The lions' features, such as eyes, noses, and mouths, are distorted and overlapping, creating a dreamlike or unsettling effect. The overall impression is one of fragmented identity or a merging of consciousness.

Image /page/17/Picture/10 description: The image contains two blurry pictures. The top picture shows a deer jumping over a fence. The bottom picture shows a person lying down with their head in their hands.

Image /page/17/Picture/11 description: A close-up, artistic rendering of a snail's head and shell against a blurred, warm-toned background. The snail's body is a reddish-brown, with visible eye stalks and a textured shell that transitions from reddish-brown to yellow and brown hues. The overall impression is one of intricate detail and a soft, painterly quality.

Image /page/17/Picture/12 description: A close-up, slightly blurry shot shows a small bird with black and white plumage perched on the edge of a shiny, dark gray funnel. The bird appears to be drinking or eating something yellow that is visible inside the funnel. The background is out of focus, with hints of a wooden structure and a sandy or earthy ground.

Image /page/17/Picture/13 description: This is a surreal and abstract image that appears to depict two figures with pig-like faces and white, feathery bodies, possibly resembling turkeys. The figures are positioned centrally, with one slightly behind and to the right of the other. The background is indistinct, with swirling patterns of light and dark colors, suggesting a dreamlike or distorted reality. The overall impression is unsettling and bizarre, blending animalistic features with an unnatural and fragmented aesthetic.

Image /page/17/Picture/14 description: A close-up, artistic rendering of a small, fluffy dog with bright green eyes peeking out of a carrier. The dog has dark fur around its eyes and nose, with lighter brown and white fur on its face and ears. The carrier appears to be made of a soft, light-colored material, possibly fabric or plush, and has a structured opening. The overall impression is one of warmth and comfort.

Image /page/17/Picture/15 description: A close-up, abstract image shows a yellow butterfly with its wings spread, resting on a stack of books. The butterfly's wings are detailed with intricate patterns and textures, while the books appear to be old and worn. The background is blurred, creating a soft focus effect that draws attention to the butterfly and books.

Image /page/17/Picture/16 description: A close-up, abstract image shows a glowing, translucent object with a textured, brown, conical top. The object is illuminated from within, casting a bright yellow light that shines through its lower, diamond-shaped section. The background is a blurry, textured brown.

Image /page/17/Picture/17 description: A close-up, abstract painting depicts a lighter with a bright blue and white flame erupting from its top. The flame is surrounded by swirling orange and yellow light, suggesting intense heat and energy. The background is dark and textured, with hints of brown and black, creating a dramatic contrast with the vibrant flame. The overall impression is one of power and intensity.

Image /page/17/Picture/18 description: A close-up, slightly blurry image shows two desserts. The dessert on the left appears to be a scoop of pink ice cream on top of a waffle cone, with some red fruit underneath. The dessert on the right is less clear but seems to be a small, round dessert with colorful, reflective elements.

Image /page/17/Picture/19 description: A close-up, slightly blurry image shows a snake coiled around a yellow object. The snake has brown and black patterns. Part of the snake's body is white with red and black spots. The yellow object appears to be fabric or plastic.

Image /page/17/Picture/20 description: The image is split into two halves. The left half shows a close-up of a plate with two dark chocolates and a white napkin. There is also a glimpse of a newspaper or magazine and some netting. The right half of the image features a large, iridescent bubble with swirling colors and textures inside, set against a blurred background that appears to be an urban or industrial setting with metallic structures.

Figure 10: Synthetic data on ImageNet-1K.

Image /page/17/Picture/22 description: The image contains the number 18 in black text on a white background.

<span id="page-18-0"></span>Image /page/18/Picture/0 description: This is a grid of 30 images, each with a distinct artistic style, resembling oil paintings. The images depict a variety of subjects including animals (dogs, horses, camels, chickens, roosters, kiwis, parrots, birds, ducks, cows), people (children, adults), objects (cars, motorcycles, swimming pools, food items like fish and fruits, flowers), and abstract patterns. The overall presentation is a collage of these diverse, stylized images.

Figure 11: Synthetic data on ImageNet-21K.
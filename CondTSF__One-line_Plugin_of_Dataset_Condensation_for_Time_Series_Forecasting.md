# CondTSF: One-line Plugin of Dataset Condensation for Time Series Forecasting

Jianrong Din<PERSON><sup>1,2</sup>\*, <PERSON><PERSON><PERSON><sup>1</sup>\*, <PERSON><PERSON><PERSON><PERSON><sup>1†</sup>, <PERSON><PERSON><sup>1</sup>, Linghe Kong<sup>1</sup>

<sup>1</sup> School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University <sup>2</sup> Zhiyuan College, Shanghai Jiao Tong University

{rafaelding,zhyliu00,gjzheng,jinhaiming,linghe.kong}@sjtu.edu.cn

# Abstract

*Dataset condensation* is a newborn technique that generates a small dataset that can be used in training deep neural networks (DNNs) to lower storage and training costs. The objective of dataset condensation is to ensure that the model trained with the synthetic dataset can perform comparably to the model trained with full datasets. However, existing methods predominantly concentrate on classification tasks, posing challenges in their adaptation to time series forecasting (TS-forecasting). This challenge arises from disparities in the evaluation of synthetic data. In classification, the synthetic data is considered well-distilled if the model trained with the full dataset and the model trained with the synthetic dataset yield identical labels for the same input, regardless of variations in output logits distribution. Conversely, in TS-forecasting, the effectiveness of synthetic data distillation is determined by the distance between predictions of the two models. The synthetic data is deemed welldistilled only when all data points within the predictions are similar. Consequently, TS-forecasting has a more rigorous evaluation methodology compared to classification. To mitigate this gap, we theoretically analyze the optimization objective of dataset condensation for TS-forecasting and propose a new one-line plugin of dataset condensation for TS-forecasting designated as Dataset Condensation for Time Series Forecasting (CondTSF) based on our analysis. Plugging CondTSF into previous dataset condensation methods facilitates a reduction in the distance between the predictions of the model trained with the full dataset and the model trained with the synthetic dataset, thereby enhancing performance. We conduct extensive experiments on eight commonly used time series datasets. CondTSF consistently improves the performance of all previous dataset condensation methods across all datasets, particularly at low condensing ratios.

# <span id="page-0-0"></span>1 Introduction

Dataset condensation is a strategy for mitigating the computational demands of training large models on extensive datasets. It is pointed out by previous works[\[15,](#page-10-0) [32\]](#page-11-0) that building foundation models[\[14,](#page-10-1) [10,](#page-9-0) [6,](#page-9-1) [35,](#page-11-1) [2,](#page-9-2) [44\]](#page-11-2) on time series forecasting (TS-forecasting) have become a hot topic. However, fine-tuning these large models using full time series datasets can entail considerable computational overhead. Hence, the employment of dataset condensation techniques becomes imperative. In recent years, various methods have been proposed in the field of dataset condensation, such as matchingbased methods[\[51,](#page-12-0) [49,](#page-11-3) [3,](#page-9-3) [20,](#page-10-2) [38,](#page-11-4) [7,](#page-9-4) [5,](#page-9-5) [41,](#page-11-5) [50,](#page-12-1) [52,](#page-12-2) [36\]](#page-11-6) and kernel methods[\[33,](#page-11-7) [55\]](#page-12-3). To date, dataset condensation methods have achieved success in classification tasks, including image classification[\[8,](#page-9-6) [11,](#page-9-7) [22\]](#page-10-3), graph classification[\[17,](#page-10-4) [16,](#page-10-5) [23,](#page-10-6) [43,](#page-11-8) [25,](#page-10-7) [9,](#page-9-8) [27\]](#page-10-8) and time series classification[\[26\]](#page-10-9).

#### 38th Conference on Neural Information Processing Systems (NeurIPS 2024).

Co-primary author. † Corresponding author.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays a comparative analysis of synthetic data for image classification and time series forecasting, alongside a comparison of different performance methods. Part (a) illustrates the evaluation of synthetic data for image classification, showing a test image of a horse. Two models are trained: one with a synthetic dataset (Ms) and another with a full dataset (Mf). The model trained with the synthetic dataset predicts the label 'Horse' with a probability of 0.5, 'Cat' with 0.05, 'Rabbit' with 0.25, and 'Bird' with 0.2. The model trained with the full dataset predicts 'Horse' with 0.8, 'Cat' with 0.15, 'Rabbit' with 0.02, and 'Bird' with 0.03. This leads to a predicted label with no error, similar prediction, and good synthetic data. Part (b) shows the evaluation of synthetic data for time series forecasting, with a test data sample. Again, two models are trained: Ms and Mf. The results show pointwise distance, large error, dissimilar prediction, and bad synthetic data. Part (c) compares the performance of different methods, including MTT, MTT + CondTSF, KIP, KIP + CondTSF, FTD, FTD + CondTSF, FRePo, and FRePo + CondTSF. These comparisons are presented as line graphs plotting Test Error against Condensation Epochs, showing how the test error decreases over epochs for each method, with and without CondTSF.

Figure 1: Left: Difference between evaluation of dataset condensation for classification tasks and time series forecasting tasks. Right: Comparison in performance of previous methods with and without CondTSF.

However, directly applying these dataset condensation methods designed for classification to the domain of time series forecasting (TS-forecasting) results in performance degradation. The objective of dataset condensation is to generate a synthetic dataset so that when the model  $\mathcal{M}_s$  trained with the synthetic dataset and the model  $\mathcal{M}_f$  trained with the full dataset are given identical input, the two models output **similar predictions**. However, the concept of **similar prediction** differs between classification and TS-forecasting. In classification, as shown in Fig[.1\(](#page-1-0)a), predictions are considered similar if  $M_s$  and  $M_f$  assign the same class label, irrespective of differences in the distribution of output logits. Conversely, in TS-forecasting, as illustrated in Fig[.1\(](#page-1-0)b), the similarity of predictions from  $\mathcal{M}_s$  and  $\mathcal{M}_f$  is indicated by the mean squared distance of the predictions. The predictions are deemed similar only when all data points within the predictions are similar. This distinction in evaluation indicates TS-forecasting imposes more stringent criteria in discerning similar predictions compared to classification. It poses a challenge that previous dataset condensation methods based on classification fail to provide adequate assurance for the similarity between predictions of  $\mathcal{M}_s$  and  $\mathcal{M}_f$  within the realm of TS-forecasting.

To mitigate the gap, we propose a novel one-line dataset condensation plugin designed specifically for TS-forecasting called Condensation for Time Series Forecasting (CondTSF) based on our theoretical analysis. We first formulate the optimization objective of dataset condensation for TSforecasting. Then we transform the original optimization objective into minimizing the distance between predictions of  $\mathcal{M}_s$  and  $\mathcal{M}_f$ . Furthermore, to minimize the distance between predictions of  $\mathcal{M}_s$  and  $\mathcal{M}_f$ , we decompose the task into minimizing two terms, namely gradient term and value term. We theoretically prove that plugging CondTSF into previous methods can minimize the value term and gradient term synchronously. Therefore, CondTSF serves as an effective plugin to boost the performance of dataset condensation for TS-forecasting. As depicted in Fig[.1\(](#page-1-0)c), plugging CondTSF into previous methods yields a significant enhancement in performance.

In short, our contributions can be summarized as follows.

- To the best of our knowledge, we are the first to explore dataset condensation for TS-forecasting. We conduct a theoretical analysis of the optimization objective of dataset condensation for TS-forecasting, breaking it down into two optimizable terms to facilitate improved optimization.
- Leveraging insights from our theoretical analysis of TS-forecasting, we propose a simple yet effective dataset condensation plugin CondTSF. Plugging CondTSF into existing methods enables synchronous optimization of the two terms, leading to performance enhancement.
- We conduct extensive experiments on eight widely used time series datasets to prove the effectiveness of CondTSF. CondTSF notably improves the performance of all previous dataset condensation methods across all datasets, particularly under low condensing ratios.

# 2 Related Works

Time Series Forecasting: Time series forecasting (TS-forecating) is the task of using historical, time-stamped data to predict future values. Previous works utilize different methods to achieve better

performance. These models can be mainly categorized into 3 types. (1) Transformer-based Models: Transformer[\[40\]](#page-11-9) have shown great success in natural language processing, and models based on transformers[\[53,](#page-12-4) [42,](#page-11-10) [24,](#page-10-10) [54\]](#page-12-5) emerged in TS-forecasting fields. (2) MLP-based Models: Efforts to use MLP-based models have been put into TS-forecasting in recent years[\[47\]](#page-11-11) since DLinear[\[45\]](#page-11-12) triumph transformer-based models with a simple MLP structure. (3) Patch-based Models: These models[\[34,](#page-11-13) [48,](#page-11-14) [28,](#page-10-11) [29\]](#page-10-12) focused on learning representation cross patches instead of learning attention at each time point. Therefore they used a patching strategy before feeding the data to transfomers.

Dataset Condensation: Dataset condensation is a task that aims at distilling a large dataset into a smaller one so that when a model is trained on the small synthetic dataset and the full dataset separately, the testing performances of the trained models are similar. Previous works related to dataset condensation can be divided into 3 classes below. (1) Coreset Selecting Methods: These methods aim at selecting data with representative features from source dataset to construct a synthetic dataset[\[1,](#page-9-9) [4,](#page-9-10) [12,](#page-10-13) [37,](#page-11-15) [39\]](#page-11-16). (2) Matching-based Methods: These methods aim at minimizing a specific metric surrogate model learned from source dataset and synthetic dataset. The defined metrics are different, including gradient[\[51,](#page-12-0) [18,](#page-10-14) [46\]](#page-11-17), features from the same class[\[41\]](#page-11-5), distribution of synthetic data[\[50,](#page-12-1) [52\]](#page-12-2) and training trajectories[\[3,](#page-9-3) [5,](#page-9-5) [7,](#page-9-4) [11,](#page-9-7) [8\]](#page-9-6). (3) Kernel-based Methods: These methods aim at obtaining a closed-form solution for the optimization problem utilizing kernel ridge-regression[\[20,](#page-10-2) [33\]](#page-11-7). In this way, the bi-level optimization problem of dataset condensation is reduced to a single-level optimization problem. Based on these results, the following works have made significant progress in different areas, including decreasing training cost and time[\[55\]](#page-12-3), improving performance[\[30,](#page-10-15) [31\]](#page-10-16).

# 3 Preliminaries

Dataset Condensation for TS-forecasting Given a time series dataset, we split the dataset into a train set and a test set. In this paper, we denote the train set as  $f$  and the test set as  $x$ . We denote the synthetic dataset as s. The synthetic dataset s is a small dataset distilled from the full train set  $f$ . Train set f, test set x, and synthetic dataset s are all vectors. We employ  $\mathcal{M}_{\theta}$  as a neural network parameterized by  $\theta$ . Without losing generality, we suppose the model  $\mathcal{M}_{\theta}$  is using historical sequence  $x_{t:t+m}$  with length m to predict future sequence  $x_{t+m:t+m+n}$  with length n. Given the test set x, we formulate the test error of  $\mathcal{M}_{\theta}$  as the error between the prediction of  $\mathcal{M}_{\theta}$  on test input  $x_{t:t+m}$ and the test label  $x_{t+m:t+m+n}$ , as shown in Eq[.1.](#page-2-0)

<span id="page-2-0"></span>
$$
\mathcal{L}_{test}(\mathcal{M}_{\theta}, \mathbf{x}) \triangleq \sum_{t} ||\mathcal{M}_{\theta}(\mathbf{x}_{t:t+m}) - \mathbf{x}_{t+m:t+m+n}||^2 \tag{1}
$$

During **dataset condensation process**, a distribution of initial model parameters  $P_{\theta}$  is available for training model parameter sampling, and the full train set  $f$  is available for condensation. Subsequently, a synthetic dataset s is distilled from the full train set  $f$  using dataset condensation methods. During testing process, initial testing model parameter  $\theta_{0. test}$  is sampled from  $P_{\theta}$ . Since  $\theta_{0. test}$  is sampled in the testing process, it's unavailable during the previous dataset condensation process. Then model parameters  $\theta_{s, test}$  and  $\theta_{f, test}$  are obtained by training initial testing parameter  $\theta_{0, test}$  on synthetic dataset  $s$  and the full train set  $f$  respectively. The objective of dataset condensation is to ensure model  $\mathcal{M}_{\theta_{s, test}}$  and  $\mathcal{M}_{\theta_{f, test}}$  have comparable performance on test set x. Therefore the practical optimization objective is to ensure that model  $\mathcal{M}_{\theta_{s, test}}$  trained with synthetic dataset s minimizes the test error  $\mathcal{L}_{test}$  on test set x. The optimization objective is formulated as Eq[.2.](#page-2-1)

<span id="page-2-1"></span>
$$
\min_{\mathbf{s}} \mathcal{L}_{test}(\mathcal{M}_{\theta_{s, test}}, \mathbf{x}) \tag{2}
$$

# <span id="page-2-2"></span>4 Method

Since test set  $x$  is not available during the dataset condensation process, the original optimization objective for dataset condensation in Eq[.2](#page-2-1) is non-optimizable. To mitigate this gap, in the following sections, we transform the non-optimizable objective into two distinct optimizable terms. Then we develop methods to optimize the two terms, thereby indirectly optimizing the original objective.

## 4.1 Decomposition

In this section, we decompose the optimization objective of dataset condensation in Eq[.2](#page-2-1) into two optimizable terms for better optimization. In the testing process, the initial testing model parameter

<span id="page-3-4"></span>Image /page/3/Figure/0 description: The image depicts a flowchart illustrating a process for distilling synthetic data. The process begins with a 'Full train set' labeled 'f', which undergoes a 'Random Sample' to produce 'Initial synthetic data' labeled 's'. This initial data is then fed into a 'Parameter Matching' block, which involves minimizing the squared difference between parameters, referred to as the 'Gradient Term Optimization'. This block is linked to a 'Non-optimizable objective' labeled min L\_test(M\_theta\_s, x). The output of the 'Parameter Matching' block is then processed by a 'CondTSF' block, which focuses on 'Value Term Optimization' by minimizing L\_label. Both the 'Parameter Matching' and 'CondTSF' blocks are described as having a 'Break down to optimizable term'. The 'CondTSF' block outputs 'Distilled synthetic data' labeled 's\*'. Arrows indicate the flow of data and the iterative nature of the process, with feedback loops connecting the optimization steps.

Figure 2: Complete process of dataset condensation using CondTSF.

 $\theta_{0, test}$  is sampled from a distribution of initial model parameters  $P_{\theta}$ . Then we train  $\theta_{0, test}$  on the synthetic dataset s to get model parameter  $\theta_{s, test}$ , and train  $\theta_{0, test}$  on the full train set f to get model parameter  $\theta_{f, test}$ . Given test dataset x, the optimization objective is formulated as Eq[.3.](#page-3-0)

<span id="page-3-0"></span>
$$
\min_{\mathbf{s}} \mathcal{L}_{test}(\mathcal{M}_{\theta_{s, test}}, \mathbf{x})
$$
\nwhere 
$$
\mathcal{L}_{test}(\mathcal{M}_{\theta_{s, test}}, \mathbf{x}) = \sum_{t} ||\mathcal{M}_{\theta_{s, test}}(\mathbf{x}_{t:t+m}) - \mathbf{x}_{t+m:t+m+n}||^2
$$
\n(3)

Meanwhile, there is a non-optimizable error  $\epsilon$  between the prediction of model  $\mathcal{M}_{\theta_{f, test}}$  and the true label from the test dataset, which is formulated in Eq[.4.](#page-3-1)

<span id="page-3-1"></span>
$$
x_{t+m:t+m+n} = \mathcal{M}_{\theta_{f, test}}(x_{t:t+m}) + \epsilon \tag{4}
$$

Then we decompose the upper bound of  $\mathcal{L}_{test}(\mathcal{M}_{\theta_{s, test}}, x)$  into two terms, as shown in Thm[.1.](#page-3-2) We utilize Taylor Expansion in the proof of Thm[.1.](#page-3-2) For each real test data  $x_{t:t+m}$ , we can arbitrarily choose position  $t'$  and get synthetic data  $s_{t':t'+m}$ . Then we can perform Taylor Expansion with  $s_{t':t'+m}$  to obtain the value of  $\mathcal{M}_{\theta_{s, test}}(x_{t:t+m})$  and  $\mathcal{M}_{\theta_{f, test}}(x_{t:t+m})$ .

<span id="page-3-2"></span>**Theorem 1.** Given arbitrary synthetic data  $s_{t':t'+m}$ , the upper bound of the optimization objective *of dataset condensation*  $\mathcal{L}_{test}(\mathcal{M}_{\theta_{s, test}}, \boldsymbol{x})$  *can be formulated as such* 

$$
\mathcal{L}_{test}(\mathcal{M}_{{\theta}_{s, test}}, \boldsymbol{x}) \leq \sum_{t} ||\boldsymbol{\epsilon}||^{2} + \underbrace{||\mathcal{M}_{{\theta}_{s, test}}({s}_{t':t'+m}) - \mathcal{M}_{{\theta}_{f, test}}({s}_{t':t'+m})||^{2}}_{Value \text{ Term}} + \underbrace{||(\nabla \mathcal{M}_{{\theta}_{s, test}}({s}_{t':t'+m}) - \nabla \mathcal{M}_{{\theta}_{f, test}}({s}_{t':t'+m}))^{\top}(\boldsymbol{x}_{t:t+m} - {s}_{t':t'+m})||^{2}}_{Gradient \text{ Term}} \tag{5}
$$

To prove Thm[.1,](#page-3-2) we use linear models for further analysis since linear models can be both effective and efficient in TS-forecasting[\[45\]](#page-11-12). Given a linear model  $\mathcal{M}_{\theta}(x) = \theta x$ , its second and higher order gradient is zero. Therefore first-order Taylor Expansion is sufficient to obtain the accurate prediction of the model. Meanwhile, if  $\mathcal{M}_{\theta}$  is a non-linear model, we ignore the higher-order terms of Taylor Expansion. We prove Thm[.1](#page-3-2) by applying the property of the first-order Taylor Expansion and triangular inequality of norm functions. The complete proof is in App[.A.1.](#page-13-0) Hence we decompose the optimization objective of dataset condensation for TS-forecasting into two optimizable terms, namely value term and gradient term. For value term, it ensures  $\mathcal{M}_{\theta_{s, test}}$  and  $\mathcal{M}_{\theta_{f, test}}$  are similar in prediction values. For **gradient term**, it ensures the predictions of  $\mathcal{M}_{\theta_{s, test}}$  and  $\mathcal{M}_{\theta_{t, test}}$  are similar in gradient. Optimizing these two terms can optimize the upper bound of the original optimization objective, and therefore indirectly optimize the original optimization objective in Eq[.3.](#page-3-0)

## 4.2 Gradient Term Optimization

We develop a method to optimize gradient term in this section. Given a linear model  $\mathcal{M}_{\theta}(x) = \theta x$ , its gradient on input is  $\nabla \mathcal{M}_{\theta}(\bm{x}) = \theta^{\top}$ . It indicates that the gradient of a linear model on input is the parameter of the model. We apply Cauchy-Schwarz Inequality to the gradient term and get its upper bound. We reformulate the gradient term and get its upper bound as shown in Eq[.6.](#page-3-3)

<span id="page-3-3"></span>
$$
\begin{aligned}\n&\|(\nabla \mathcal{M}_{\theta_{s, test}}(s_{t':t'+m}) - \nabla \mathcal{M}_{\theta_{f, test}}(s_{t':t'+m}))^\top (\boldsymbol{x}_{t:t+m} - s_{t':t'+m})\|^2 \text{ (Gradient Term)} \\
&= \|(\theta_{s, test} - \theta_{f, test})(\boldsymbol{x}_{t:t+m} - s_{t':t'+m})\|^2 \\
&\leq \|\theta_{s, test} - \theta_{f, test}\|^2 \cdot \|\boldsymbol{x}_{t:t+m} - s_{t':t'+m}\|^2\n\end{aligned} \tag{6}
$$

<span id="page-4-3"></span>Image /page/4/Figure/0 description: The image displays a diagram illustrating a machine learning concept. The main part of the diagram shows a scatter plot with red dots representing 'Input Synthetic Data', yellow dots representing 'Train Expert Model Prediction', and blue dots representing 'Test Expert Model Prediction'. Lines connect some of the red dots to corresponding yellow and blue dots, indicating relationships or predictions. A magnified view on the right shows a single red dot labeled 's\_t':t'+m' and a box containing several yellow dots and one blue dot labeled 'Similar'. Arrows point from the yellow dots to the red dot and from the blue dot to the red dot, with text indicating 'M\_theta\_f(s\_t':t'+m)' and 'M\_theta\_f,test(s\_t':t'+m)'. Below this, a section titled 'Visualization of Original Data' shows a line graph comparing 'Input Synthetic Data' (red line) with 'Expert Model Prediction' (green and light blue lines), demonstrating the model's performance.

Empirically illustrate:  $\mathcal{M}_{\theta_f,test}( \bm{s}_{t':t'+m}) \approx \mathcal{M}_{\theta^0_t}( \bm{s}_{t':t'+m}) \approx \mathcal{M}_{\theta^1_t}( \bm{s}_{t':t'+m}) \approx \cdots \approx \mathcal{M}_{\theta^k_t}( \bm{s}_{t':t'+m})$ 

Figure 3: Given the same synthetic data as input, all expert models trained on the full train set  $f$  provide similar predictions. The initial parameters of the models are sampled from the same distribution  $P_\theta$ . The visualization of this figure utilized MDS[\[19\]](#page-10-17) algorithm for dimension reduction.

Since test data  $x_{t:t+m}$  is not available during the dataset condensation process, the distance between synthetic data and test data  $||x_{t:t+m} - s_{t':t'+m}||^2$  is not optimizable. Therefore we only need to optimize the distance between parameters  $||\theta_{s, test} - \theta_{f, test}||^2$ . All previous dataset condensation methods based on parameter matching can minimize this distance. Here we utilize MTT[\[3\]](#page-9-3) as an example to clarify the optimization process. The optimization objective of trajectory matching is

<span id="page-4-0"></span>
$$
\min_{\mathbf{s}} \frac{||\theta_{f, test} - \theta_{s, test}||^2}{||\theta_{f, test} - \theta_{0, test}||^2}
$$
\n<sup>(7)</sup>

However, since  $\theta_{s, test}$  and  $\theta_{f, test}$  are trained from testing initial parameter  $\theta_{0, test} \sim P_{\theta}$ , they are not available during dataset condensation process. Therefore, in practice, we sample  $\theta_0^0, \dots, \theta_0^k \sim P_\theta$  as initial parameters during dataset condensation process. The initial parameters are trained on synthetic dataset s and full train set f respectively to get  $\theta_s^0, \ldots, \theta_s^k$  and  $\theta_f^0, \ldots, \theta_f^k$ . Then we substitute  $\theta_{s, test}$ ,  $\theta_{f, test}$  and  $\theta_{0, test}$  in Eq[.7](#page-4-0) with parameters sampled in dataset condensation, making the optimization objective optimizable. The practical optimization objective is shown in Eq[.8.](#page-4-1)

<span id="page-4-1"></span>
$$
\min_{s} \sum_{i=0}^{k} \frac{||\theta_f^i - \theta_s^i||^2}{||\theta_f^i - \theta_0^i||^2}
$$
\n(8)

In practice,  $\theta_0^0,\ldots,\theta_0^k$  and  $\theta_f^0,\ldots,\theta_f^k$  are sampled, trained, and stored in a parameter buffer before dataset condensation process. It can be concluded that using trajectory matching methods is intuitively minimizing the distance between  $\theta_s^i$  and  $\theta_f^i$  for all initial parameters  $\theta_0^i \sim \tilde{P}_{\theta}$ . By minimizing the upper bound of the gradient term, trajectory matching methods indirectly optimize the gradient term.

## 4.3 Value Term Optimization

We develop an optimization objective to optimize the **value term** in this section. Since  $\theta_{f, test}$  is trained from  $\theta_{0, test}$ , it's unavailable in dataset condensation process. To mitigate this gap, we prove that although  $\theta_{f, test}$  is unavailable in dataset condensation process, its prediction  $\mathcal{M}_{\theta_{f, test}}(s_{t':t'+m})$ is still available. To prove this statement, we sample initial model parameters  $\theta_0^0, \dots, \theta_0^k$  from  $P_\theta$ . Then  $\theta_0^0, \ldots, \theta_0^k$  are all trained with the same full train set  $f$ . After training, we get parameters  $\theta_f^0, \ldots, \theta_f^k$ . It is observed that models  $\mathcal{M}_{\theta_f^0}, \ldots, \mathcal{M}_{\theta_f^k}$  predict similarly given arbitrary synthetic data  $s_{t':t'+m}$  as input.

Since initial testing parameter  $\theta_{0, test}$  is also sampled from the same distribution  $P_{\theta}$  and  $\theta_{f, test}$  is trained from  $\theta_{0, test}$  using the same full train set f, the prediction of  $\mathcal{M}_{\theta_f, test}$  is similar to predictions of an arbitrary expert model  $\mathcal{M}_{\theta_f^i}$ . The conclusion is formulated in Eq[.9.](#page-4-2)

<span id="page-4-2"></span>
$$
\mathcal{M}_{\theta_f, test}(s_{t':t'+m}) \approx \mathcal{M}_{\theta_f^0}(s_{t':t'+m}) \approx \mathcal{M}_{\theta_f^1}(s_{t':t'+m}) \approx \cdots \approx \mathcal{M}_{\theta_f^k}(s_{t':t'+m})
$$
(9)

<span id="page-5-3"></span>Algorithm 1 Dataset Condensation with CondTSF (MTT[\[3\]](#page-9-3) as backbone)

| <b>Input:</b> Synthetic data s; Parameter buffer $\{(0, \theta_f)\}^k$ ; Synthetic learning rate $\alpha$ ; Trajectory matching<br>epochs N; Total condensation epochs E; Additive update ratio $\beta$ ; Gap of epochs G between<br>using CondTSF |
|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>Output:</b> Optimized synthetic data s                                                                                                                                                                                                          |
| 1: Split <i>s</i> into training sets $\{(s_{t:t+m}, s_{t+m:t+m+n})\}$                                                                                                                                                                              |
| 2: for each condensation epoch $e$ in range $E$ do                                                                                                                                                                                                 |
| <i>3:</i> <b>if</b> e mod $G \neq 0$ then                                                                                                                                                                                                          |
| <i><i>4:</i> Sample <math>(\theta_0^i, \theta_f^i)</math> from <math>\{(\theta_0, \theta_f)\}^k</math></i>                                                                                                                                         |
| <i><i>5:</i> Initialize student parameter <math>\hat{\theta}_0 \leftarrow \theta_0^i</math></i>                                                                                                                                                    |
| <i><i>6:</i> <b>for</b> each trajectory matching epoch j in range N <b>do</b> /*MTT[3] trajectory matching*/</i>                                                                                                                                   |
| <i><i><i>7:</i> Train <math>\mathcal{M}_{{\hat{\theta}_i}}</math> with synthetic data</i></i>                                                                                                                                                      |
| <i><i><i>8:</i> <math>\hat{\theta}_{j+1} \leftarrow \hat{\theta}_j - \alpha \nabla \mathcal{L}(\mathcal{M}_{{\hat{\theta}_j}}(s_{t:t+m}), s_{t+m:t+m+n})</math> for all synthetic data</i></i>                                                     |
| <i><i><i>9:</i> <math>\mathcal{L}_{param} \leftarrow   \theta_f^i - \hat{\theta}_N  ^2/  \theta_f^i - \theta_0^i  ^2</math></i></i>                                                                                                                |
| <i><i><i>10:</i> Update synthetic data s with respect to <math>\mathcal{L}_{param}</math> /*Optimize gradient term*/</i></i>                                                                                                                       |
| <i>11:</i> else                                                                                                                                                                                                                                    |
| <i><i>12:</i> for each train sample <math>(s_{t:t+m}, s_{t+m:t+m+n})</math> in training sets do</i>                                                                                                                                                |
| <i><i><i>13:</i> Choose an arbitrary expert model with parameter <math>\theta_f^i</math> from <math>\{(\theta_0, \theta_f)\}^k</math></i></i>                                                                                                      |
| <i><i><i>14:</i> <math>\boldsymbol{y} \leftarrow \mathcal{M}_{{\theta}^i_{f}}(\boldsymbol{s}_{t:t+m})</math></i></i>                                                                                                                               |
| <i><i><i>15:</i> <math>s_{t+m:t+m+n} \leftarrow (1-\beta) \cdot s_{t+m:t+m+n} + \beta \cdot y</math> /*Optimize value term*/</i></i>                                                                                                               |
| 16: return $s$                                                                                                                                                                                                                                     |

Experiments have proved Eq[.9](#page-4-2) in Fig[.3.](#page-4-3) As shown in Fig[.3,](#page-4-3) for each synthetic data input  $s_{t':t'+m}$ (orange points), the predictions of corresponding expert models (yellow and blue points) are similar. Therefore, although  $\theta_{f, test}$  is unavailable in the dataset condensation process, its prediction  $\mathcal{M}_{\theta_{f, test}}(s_{t':t'+m})$  can be obtained using the prediction of an arbitrary expert model  $\mathcal{M}_{\theta_f^i}(s_{t':t'+m})$ . Now we reformulate the value term and transform it into a practical optimization objective. Firstly, We formulate the upper bound of the value term as shown in Thm[.2.](#page-5-0)

<span id="page-5-0"></span>Theorem 2. *The upper bound of the value term can be formulated as such*

$$
||\mathcal{M}_{\theta_{s, test}}(s_{t':t'+m}) - \mathcal{M}_{\theta_{f, test}}(s_{t':t'+m})||^2 \leq 2 \cdot \sum_{t'} ||\mathcal{M}_{\theta_{f, test}}(s_{t':t'+m}) - s_{t'+m:t'+m+n}||^2
$$
 (10)

We prove Thm[.2](#page-5-0) by utilizing the triangular inequality and the prediction optimality of  $\theta_{s, test}$  on synthetic data s. The complete proof is in App[.A.2.](#page-14-0) Accodring to Thm[.2,](#page-5-0) we obtain an optimizable upper bound of the value term. Therefore the optimization objective for the value term can be naturally defined as minimizing the upper bound of the value term, as shown in Eq[.11.](#page-5-1)

<span id="page-5-1"></span>
$$
\min_{\mathbf{s}} \mathcal{L}_{label} \text{ where } \mathcal{L}_{label} = \sum_{t'} ||\mathcal{M}_{\theta_{f, test}}(s_{t':t'+m}) - s_{t'+m:t'+m+n}||^2 \tag{11}
$$

According to Thm[.2,](#page-5-0) label error  $\mathcal{L}_{label}$  is the upper bound of the value term. Therefore, by minimizing the upper bound of the value term, the value term is indirectly minimized.

## 4.4 CondTSF

In this section, we develop a one-line plugin called CondTSF to minimize the label error  $\mathcal{L}_{label}$ in Eq[.11](#page-5-1) so that the value term can be optimized. CondTSF is a lightweight one-line plugin, no backpropagation or gradient is required during the update. CondTSF utilizes a simple yet effective additive method to iteratively update the synthetic data s and minimize the label error  $\mathcal{L}_{label}$ . In TS-forecasting, when generating training data, the data is usually sampled overlap from the dataset. Inspired by the overlap property, we utilize an additive method in CondTSF to gradually update the synthetic data to avoid vibrations. In the  $i_{\text{th}}$  update iteration, CondTSF uses the prediction of expert model  $\mathcal{M}_{f, test}(s_{t':t'+m})$  to update synthetic label  $s_{t'+m:t'+m+n}$ . The update process is shown in Eq[.12.](#page-5-2)

<span id="page-5-2"></span>
$$
\mathbf{s}_{t'+m:t'+m+n}^{(i+1)} = (1-\beta) \cdot \mathbf{s}_{t'+m:t'+m+n}^{(i)} + \beta \cdot \mathcal{M}_{\theta_{f, test}}(\mathbf{s}_{t':t'+m}^{(i)})
$$
(12)

|              | CondTSF        |                            | <b>ExchangeRate</b> |                                                | Weather                    |                            | Electricity       |                                                | Traffic                    |
|--------------|----------------|----------------------------|---------------------|------------------------------------------------|----------------------------|----------------------------|-------------------|------------------------------------------------|----------------------------|
|              |                | MAE                        | <b>MSE</b>          | <b>MAE</b>                                     | <b>MSE</b>                 | <b>MAE</b>                 | <b>MSE</b>        | <b>MAE</b>                                     | <b>MSE</b>                 |
| Random       |                | $0.783 \pm 0.090$          | $1.070 \pm 0.246$   | $0.530 \pm 0.084$                              | $0.647 \pm 0.159$          | $0.840 \pm 0.017$          | $1.102 \pm 0.031$ | $0.854 \pm 0.018$                              | $1.350 \pm 0.043$          |
|              | x              | $0.716 \pm 0.090$          | $0.875 \pm 0.217$   | $0.483 \pm 0.053$                              | $0.530 \pm 0.087$          | $0.808 \pm 0.017$          | $1.017 \pm 0.046$ | $0.823 \pm 0.007$                              | $1.296 \pm 0.021$          |
| DC           | ✓              | $0.602 \pm 0.115$          | $0.632 \pm 0.215$   | $0.449 \pm 0.055$                              | $0.467 \pm 0.084$          | $0.794 \pm 0.014$          | $0.987 + 0.035$   | $0.818 \pm 0.012$                              | $1.265 \pm 0.032$          |
|              |                | 15.8%                      | 27.8%               | 6.9%                                           | 11.7%                      | 1.7%                       | 2.9%              | 0.7%                                           | 2.4%                       |
|              | Х              | $0.778 \pm 0.084$          | $0.964 \pm 0.136$   | $0.509 \pm 0.065$                              | $0.538 + 0.085$            | $0.747 + 0.012$            | $0.840 \pm 0.019$ | $0.742 \pm 0.010$                              | $1.052 \pm 0.024$          |
| MTT          | ✓              | $0.195 \pm 0.007$          | $0.061 \pm 0.004$   | $0.326 \pm 0.009$                              | $0.284 \pm 0.007$          | $0.391 \pm 0.003$          | $0.284 \pm 0.004$ | $0.494 \pm 0.022$                              | $0.579 \pm 0.037$          |
|              |                | 75.0%                      | 93.7%               | 36.0%                                          | 47.2%                      | 47.6%                      | 66.1%             | 33.4%                                          | 45.0%                      |
|              | Х              | $0.683 \pm 0.128$          | $0.806 \pm 0.248$   | $0.474 \pm 0.049$                              | $0.492 \pm 0.067$          | $0.733 \pm 0.011$          | $0.820 \pm 0.018$ | $0.741 \pm 0.013$                              | $1.037 \pm 0.035$          |
| PP           | ✓              | $0.191 \pm 0.006$          | $0.058 \pm 0.003$   | $0.324 \pm 0.006$                              | $0.283 \pm 0.005$          | $0.390 \pm 0.006$          | $0.285 \pm 0.006$ | $0.490 \pm 0.013$                              | $0.570 \pm 0.020$          |
|              |                | 72.0%                      | 92.8%               | 31.7%                                          | 42.5%                      | 46.8%                      | 65.3%             | 33.8%                                          | 45.1%                      |
|              | Х              | $0.730 \pm 0.124$          | $0.801 \pm 0.211$   | $0.522 \pm 0.011$                              | $0.557 + 0.020$            | $0.719 \pm 0.029$          | $0.790 \pm 0.052$ | $0.741 \pm 0.020$                              | $1.063 \pm 0.051$          |
| <b>TESLA</b> | ✓              | $0.188 \pm 0.014$          | $0.059 \pm 0.008$   | $0.295 \pm 0.010$                              | $0.276 \pm 0.013$          | $0.389 \pm 0.005$          | $0.293 \pm 0.006$ | $0.576 \pm 0.016$                              | $0.730 \pm 0.025$          |
|              |                | 74.3%                      | 92.7%               | 43.6%                                          | 50.3%                      | 46.0%                      | 62.9%             | 22.2%                                          | 31.3%                      |
|              | х              | $0.690 \pm 0.153$          | $0.818 \pm 0.278$   | $0.511 \pm 0.037$                              | $0.535 \pm 0.048$          | $0.748 \pm 0.012$          | $0.844 \pm 0.019$ | $0.745 \pm 0.007$                              | $1.054 \pm 0.014$          |
| <b>FTD</b>   | ✓              | $0.184 \pm 0.005$          | $0.055 \pm 0.003$   | $0.320 \pm 0.005$                              | $0.280 \pm 0.004$          | $0.396 \pm 0.003$          | $0.290 \pm 0.002$ | $0.501 \pm 0.021$                              | $0.587 \pm 0.032$          |
|              |                | 73.3%                      | 93.3%               | 37.3%                                          | 47.6%                      | 47.1%                      | 65.6%             | 32.7%                                          | 44.3%                      |
|              | x              | $0.646 \pm 0.137$          | $0.702 \pm 0.243$   | $0.515 \pm 0.035$                              | $0.554 \pm 0.038$          | $0.752 \pm 0.016$          | $0.850 \pm 0.027$ | $0.740 \pm 0.013$                              | $1.043 \pm 0.026$          |
| <b>DATM</b>  | ✓              | $0.190 \pm 0.010$          | $0.058 + 0.006$     | $0.320 \pm 0.015$                              | $0.290 \pm 0.014$          | $0.381 \pm 0.005$          | $0.276 \pm 0.005$ | $0.496 \pm 0.016$                              | $0.582 \pm 0.025$          |
|              |                | 70.6%                      | 91.8%               | 37.9%                                          | 47.6%                      | 49.4%                      | 67.6%             | 33.0%                                          | 44.2%                      |
| Full         | $\overline{a}$ | $0.110 \pm 0.001$          | $0.023 \pm 0.000$   | $0.197 \pm 0.001$                              | $0.131 \pm 0.001$          | $0.312 \pm 0.002$          | $0.223 \pm 0.002$ | $0.406 \pm 0.003$                              | $0.492 \pm 0.004$          |
|              |                |                            |                     |                                                |                            |                            |                   |                                                |                            |
|              |                |                            | ETTm1               |                                                | ETTm2                      |                            | ETTh1             |                                                | ETTh <sub>2</sub>          |
|              | CondTSF        | MAE                        | <b>MSE</b>          | <b>MAE</b>                                     | <b>MSE</b>                 | <b>MAE</b>                 | <b>MSE</b>        | <b>MAE</b>                                     | <b>MSE</b>                 |
| Random       | L,             | $0.728 \pm 0.033$          | $0.993 \pm 0.082$   | $0.695 \pm 0.011$                              | $0.889 \pm 0.030$          | $0.756 \pm 0.035$          |                   | $1.059 \pm 0.083$   0.749 $\pm$ 0.037          | $1.013 \pm 0.089$          |
|              | Х              | $0.672 \pm 0.020$          | $0.859 \pm 0.038$   | $0.631 \pm 0.023$                              | $0.708 \pm 0.063$          | $0.704 \pm 0.053$          | $0.933 \pm 0.118$ | $0.627 \pm 0.081$                              | $0.694 \pm 0.158$          |
| DC           | ✓              | $0.661 \pm 0.012$          | $0.833 \pm 0.018$   | $0.591 \pm 0.026$                              | $0.603 \pm 0.044$          | $0.678 \pm 0.034$          | $0.873 \pm 0.070$ | $0.601 \pm 0.027$                              | $0.631 \pm 0.060$          |
|              |                | 1.8%                       | 3.1%                | 6.4%                                           | 14.9%                      | 3.6%                       | 6.4%              | 4.1%                                           | 9.1%                       |
|              | Х              | $0.653 \pm 0.019$          | $0.771 \pm 0.040$   | $0.685 \pm 0.022$                              | $0.754 \pm 0.051$          | $0.693 \pm 0.009$          | $0.845 \pm 0.023$ | $0.719 \pm 0.006$                              | $0.827 \pm 0.016$          |
| <b>MTT</b>   | ✓              | $0.491 \pm 0.004$          | $0.502 \pm 0.008$   | $0.347 \pm 0.028$                              | $0.202 \pm 0.028$          | $0.532 \pm 0.014$          | $0.580 \pm 0.029$ | $0.329 \pm 0.003$                              | $0.205 \pm 0.002$          |
|              |                | 24.8%                      | 34.9%               | 49.3%                                          | 73.3%                      | 23.3%                      | 31.3%             | 54.2%                                          | 75.2%                      |
|              | Х              | $0.660 \pm 0.014$          | $0.788 \pm 0.032$   | $0.615 \pm 0.093$                              | $0.620 \pm 0.168$          | $0.694 \pm 0.008$          | $0.851 \pm 0.018$ | $0.673 \pm 0.052$                              | $0.757 \pm 0.086$          |
| PP           | ✓              | $0.489 + 0.005$            | $0.491 \pm 0.013$   | $0.336 \pm 0.024$                              | $0.190 \pm 0.023$          | $0.527 \pm 0.011$          | $0.574 \pm 0.029$ | $0.336 \pm 0.004$                              | $0.211 \pm 0.005$          |
|              |                | 26.0%                      | 37.7%               | 45.4%                                          | 69.4%                      | 24.1%                      | 32.6%             | 50.1%                                          | 72.1%                      |
|              | Х              | $0.641 \pm 0.009$          | $0.751 \pm 0.018$   | $0.577 \pm 0.142$                              | $0.570 \pm 0.210$          | $0.674 \pm 0.013$          | $0.813 \pm 0.030$ | $0.616 \pm 0.095$                              | $0.630 \pm 0.154$          |
| <b>TESLA</b> | ✓              | $0.542 \pm 0.037$          | $0.622 \pm 0.058$   | $0.292 \pm 0.001$                              | $0.155 \pm 0.001$          | $0.533 \pm 0.020$          | $0.588 \pm 0.048$ | $0.332 \pm 0.007$                              | $0.208 \pm 0.006$          |
|              |                | 15.4%                      | 17.2%               | 49.4%                                          | 72.8%                      | 21.0%                      | 27.7%             | 46.2%                                          | 67.0%                      |
|              | Х              | $0.663 \pm 0.009$          | $0.790 \pm 0.020$   | $0.563 \pm 0.147$                              | $0.571 \pm 0.221$          | $0.693 \pm 0.016$          | $0.857 + 0.044$   | $0.625 \pm 0.148$                              | $0.686 \pm 0.240$          |
| <b>FTD</b>   | ✓              | $0.494 \pm 0.007$          | $0.502 \pm 0.010$   | $0.347 \pm 0.012$                              | $0.200 \pm 0.012$          | $0.529 \pm 0.014$          | $0.570 \pm 0.030$ | $0.335 \pm 0.009$                              | $0.210 \pm 0.008$          |
|              |                | 25.5%                      | 36.5%               | 38.4%                                          | 65.0%                      | 23.7%                      | 33.4%             | 46.5%                                          | 69.4%                      |
|              | Х              | $0.642 \pm 0.031$          | $0.768 \pm 0.050$   | $0.644 \pm 0.047$                              | $0.679 \pm 0.090$          | $0.689 \pm 0.036$          | $0.870 \pm 0.057$ | $0.611 \pm 0.150$                              | $0.650 \pm 0.245$          |
| <b>DATM</b>  | ✓              | $0.531 \pm 0.032$          | $0.569 \pm 0.045$   | $0.305 \pm 0.006$                              | $0.167 \pm 0.005$          | $0.532 \pm 0.028$          | $0.582 \pm 0.068$ | $0.330 \pm 0.004$                              | $0.209 \pm 0.003$          |
| Full         |                | 17.2%<br>$0.432 \pm 0.001$ | 25.9%               | 52.6%<br>$0.473 \pm 0.001$   $0.230 \pm 0.001$ | 75.4%<br>$0.113 \pm 0.001$ | 22.7%<br>$0.389 \pm 0.003$ | 33.1%             | 45.9%<br>$0.339 \pm 0.004$   $0.276 \pm 0.002$ | 67.8%<br>$0.166 \pm 0.002$ |

<span id="page-6-0"></span>Table 1: Distill performance of different dataset condensation methods. For each method, *X*means CondTSF is not used, ✓means CondTSF is used, and ↓ means the decreased percentage of test error after CondTSF is applied. Five synthetic datasets are distilled and the average and standard deviation are reported.

where  $0 < \beta < 1$  is the update ratio of this additive update method. This additive update process lowers the label error  $\mathcal{L}_{label}$  of s in each iteration exponentially, which can be formulated as

$$
Llabel^{(i+1)} = \sum_{t'} ||\mathbf{s}_{t'+m:t'+m+n}^{(i+1)} - \mathcal{M}_{\theta_{f,test}}(\mathbf{s}_{t':t'+m}^{(i+1)})||^2
$$

$$
= (1 - \beta)^2 
Llabel^{(i)} = (1 - \beta)^2 Llabel^{(i)}
$$
(13)

Since the update ratio has a value of  $0 < \beta < 1$ , CondTSF lowers the label error  $\mathcal{L}_{label}$  exponentially in each update iteration and solves the optimization problem for the value term. As a plugin module, CondTSF is used to update once for every  $G$  iteration of parameter matching methods. In this way, the gradient term and the value term can be optimized synchronously. The algorithm is shown in Alg[.1.](#page-5-3) We also formulate the complete condensation process using CondTSF, as shown in Fig[.2.](#page-3-4)

# <span id="page-6-1"></span>5 Experiment

## 5.1 Experiment Settings

Dataset Settings: The efficacy of dataset condensation methods is assessed across eight time series datasets. For all datasets, the model is set to be using 24 steps of data to forecast 24 steps of data. We

Table 2: Information and condensation settings of time series datasets.

<span id="page-7-0"></span>

|                  | ETTm1&ETTm2 | ETTh1&ETTh2 | ExchangeRate | Weather | Electricity | Traffic |
|------------------|-------------|-------------|--------------|---------|-------------|---------|
| Dataset length   | 57600       | 14400       | 7588         | 52696   | 26304       | 17544   |
| Distill ratio    | 0.83‰       | 3.33‰       | 6.33‰        | 0.91‰   | 1.82‰       | 2.74‰   |
| Distilled length | 48          | 48          | 48           | 48      | 48          | 48      |

<span id="page-7-1"></span>Table 3: Generalization ability of different dataset condensation methods. For each dataset and each method, MLP, LSTM, CNN are trained with the synthetic data distilled from DLinear expert models. For each architecture, five test models are trained, the average and standard deviation of MAE, MSE are summarized. The result of CondTSF is using MTT as the backbone.

|                        |                                        |                                        |                                        | <b>ExchangeRate</b>                    |                                        |                                            |                                        |                                        | Weather                                |                                        |                                        |                                        |
|------------------------|----------------------------------------|----------------------------------------|----------------------------------------|----------------------------------------|----------------------------------------|--------------------------------------------|----------------------------------------|----------------------------------------|----------------------------------------|----------------------------------------|----------------------------------------|----------------------------------------|
|                        |                                        | <b>MLP</b>                             |                                        | <b>LSTM</b>                            |                                        | <b>CNN</b>                                 | <b>MLP</b>                             |                                        | <b>LSTM</b>                            |                                        |                                        | <b>CNN</b>                             |
|                        | <b>MAE</b>                             | <b>MSE</b>                             | <b>MAE</b>                             | <b>MSE</b>                             | <b>MAE</b>                             | <b>MSE</b>                                 | <b>MAE</b>                             | <b>MSE</b>                             | MAE                                    | <b>MSE</b>                             | <b>MAE</b>                             | <b>MSE</b>                             |
| Random                 | $0.931 \pm 0.024$                      | $1.246 \pm 0.057$                      | $0.840 \pm 0.047$                      | $1.035 \pm 0.102$                      | $0.910 + 0.038$                        | $1.217 \pm 0.106$                          | $0.554 \pm 0.010$                      | $0.632 \pm 0.016$                      | $0.531 \pm 0.020$                      | $0.598 + 0.033$                        | $0.570 + 0.006$                        | $0.655 \pm 0.017$                      |
| DC                     | $0.713 \pm 0.059$                      | $0.740 \pm 0.114$                      | $0.511 \pm 0.034$                      | $0.390 \pm 0.048$                      | $0.588 \pm 0.049$                      | $0.519 \pm 0.072$                          | $0.503 \pm 0.014$                      | $0.540 \pm 0.022$                      | $0.446 \pm 0.011$                      | $0.430 \pm 0.017$                      | $0.517 + 0.016$                        | $0.533 + 0.028$                        |
| <b>KIP</b>             | $0.483 \pm 0.012$                      | $0.397 \pm 0.013$                      | $0.512 \pm 0.024$                      | $0.422 \pm 0.026$                      | $0.494 \pm 0.022$                      | $0.414 \pm 0.027$                          | $0.293 \pm 0.008$                      | $0.276 \pm 0.011$                      | $0.262 \pm 0.004$                      | $0.253 \pm 0.004$                      | $0.331 \pm 0.005$                      | $0.292 \pm 0.003$                      |
| FRePo                  | $0.564 \pm 0.033$                      | $0.537 \pm 0.041$                      | $0.583 \pm 0.048$                      | $0.569 \pm 0.077$                      | $0.599 \pm 0.025$                      | $0.578 + 0.044$                            | $0.393 \pm 0.013$                      | $0.401 \pm 0.011$                      | $0.419 \pm 0.044$                      | $0.424 \pm 0.043$                      | $0.434 \pm 0.011$                      | $0.428 + 0.016$                        |
| MTT                    | $0.421 \pm 0.007$                      | $0.301 \pm 0.009$                      | $0.431 \pm 0.010$                      | $0.313 \pm 0.009$                      | $0.419 + 0.007$                        | $0.300 + 0.010$                            | $0.286 \pm 0.006$                      | $0.256 \pm 0.004$                      | $0.279 \pm 0.007$                      | $0.249 \pm 0.004$                      | $0.328 \pm 0.018$                      | $0.276 \pm 0.014$                      |
| PP<br><b>TESLA</b>     | $0.383 \pm 0.009$<br>$0.316 \pm 0.008$ | $0.249 \pm 0.008$<br>$0.172 \pm 0.007$ | $0.388 \pm 0.013$<br>$0.323 \pm 0.010$ | $0.252 \pm 0.011$<br>$0.175 \pm 0.007$ | $0.465 \pm 0.021$<br>$0.439 \pm 0.034$ | $0.343 \pm 0.024$<br>$0.302 \pm 0.044$     | $0.279 \pm 0.019$<br>$0.298 \pm 0.012$ | $0.253 \pm 0.010$<br>$0.266 \pm 0.007$ | $0.309 \pm 0.009$<br>$0.292 \pm 0.012$ | $0.271 \pm 0.008$<br>$0.253 \pm 0.010$ | $0.344 \pm 0.023$<br>$0.331 \pm 0.004$ | $0.315 \pm 0.031$<br>$0.283 + 0.006$   |
| <b>FTD</b>             |                                        |                                        |                                        |                                        |                                        | $0.329 \pm 0.031$                          |                                        |                                        |                                        | $0.264 \pm 0.017$                      |                                        |                                        |
| <b>DATM</b>            | $0.425 \pm 0.007$<br>$0.452 \pm 0.013$ | $0.306 \pm 0.005$<br>$0.349 \pm 0.016$ | $0.433 \pm 0.012$<br>$0.229 \pm 0.045$ | $0.310 \pm 0.011$<br>$0.095 \pm 0.032$ | $0.445 \pm 0.025$<br>$0.351 \pm 0.053$ | $0.209 \pm 0.049$                          | $0.286 \pm 0.010$<br>$0.270 \pm 0.004$ | $0.251 \pm 0.005$<br>$0.258 \pm 0.004$ | $0.303 \pm 0.028$<br>$0.275 \pm 0.012$ | $0.253 \pm 0.010$                      | $0.347 \pm 0.015$<br>$0.323 \pm 0.022$ | $0.305 \pm 0.014$<br>$0.282 \pm 0.021$ |
| CondTSF                | $0.135 \pm 0.005$                      | $0.032 \pm 0.002$                      | $0.135 \pm 0.004$                      | $0.032 \pm 0.002$                      | $0.248 \pm 0.031$                      | $0.101 \pm 0.022$                          | $0.242 \pm 0.009$                      | $0.229 \pm 0.006$                      | $0.248 \pm 0.004$                      | $0.231 \pm 0.004$                      | $0.283 \pm 0.007$                      | $0.256 \pm 0.004$                      |
|                        |                                        |                                        |                                        |                                        |                                        |                                            |                                        |                                        |                                        |                                        |                                        |                                        |
|                        |                                        | <b>MLP</b>                             |                                        | Electricity<br><b>LSTM</b>             |                                        | <b>CNN</b>                                 | <b>MLP</b>                             |                                        | <b>Traffic</b><br><b>LSTM</b>          |                                        |                                        | <b>CNN</b>                             |
|                        | <b>MAE</b>                             | <b>MSE</b>                             | MAE                                    | <b>MSE</b>                             | MAE                                    | <b>MSE</b>                                 | MAE                                    | MSE                                    | <b>MAE</b>                             | <b>MSE</b>                             | <b>MAE</b>                             | <b>MSE</b>                             |
| Random                 | $0.790 \pm 0.016$                      | $0.931 \pm 0.039$                      | $0.758 \pm 0.007$                      | $0.866 \pm 0.016$                      | $0.782 \pm 0.012$                      | $0.919 \pm 0.034$                          | $0.743 \pm 0.015$                      | $1.102 \pm 0.042$                      | $0.742 \pm 0.007$                      | $1.088 \pm 0.015$                      | $0.753 \pm 0.016$                      | $1.100 \pm 0.031$                      |
| $_{\mathrm{DC}}$       | $0.778 + 0.007$                        | $0.912 \pm 0.016$                      | $0.770 \pm 0.004$                      | $0.884 \pm 0.009$                      | $0.769 \pm 0.010$                      | $0.897 + 0.022$                            | $0.730 \pm 0.011$                      | $1.035 \pm 0.031$                      | $0.709 \pm 0.012$                      | $0.989 + 0.030$                        | $0.747 + 0.009$                        | $1.068 \pm 0.020$                      |
| <b>KIP</b>             | $0.769 \pm 0.014$                      | $0.881 \pm 0.029$                      | $0.700 \pm 0.018$                      | $0.741 \pm 0.036$                      | $0.761 \pm 0.016$                      | $0.864 \pm 0.035$                          | $0.738 + 0.018$                        | $1.056 \pm 0.045$                      | $0.714 \pm 0.017$                      | $1.008 \pm 0.023$                      | $0.753 \pm 0.018$                      | $1.074 \pm 0.023$                      |
| FRePo                  | $0.620 \pm 0.009$                      | $0.626 \pm 0.016$                      | $0.633 \pm 0.016$                      | $0.625 \pm 0.029$                      | $0.642 \pm 0.011$                      | $0.665 \pm 0.022$                          | $0.645 \pm 0.007$                      | $0.802 \pm 0.014$                      | $0.650 + 0.005$                        | $0.811 \pm 0.012$                      | $0.656 \pm 0.003$                      | $0.817 + 0.006$                        |
| MTT                    | $0.465 \pm 0.009$                      | $0.374 \pm 0.010$                      | $0.467 \pm 0.013$                      | $0.378 \pm 0.015$                      | $0.491 \pm 0.008$                      | $0.405 \pm 0.010$                          | $0.635 \pm 0.004$                      | $0.797 + 0.009$                        | $0.634 + 0.008$                        | $0.788 \pm 0.011$                      | $0.655 \pm 0.007$                      | $0.817 + 0.007$                        |
| $\rm PP$               | $0.483 + 0.008$                        | $0.388 \pm 0.009$                      | $0.481 \pm 0.008$                      | $0.388 \pm 0.009$                      | $0.521 \pm 0.015$                      | $0.444 \pm 0.021$                          | $0.617 \pm 0.006$                      | $0.751 \pm 0.006$                      | $0.610 + 0.008$                        | $0.740 \pm 0.010$                      | $0.593 \pm 0.004$                      | $0.745 \pm 0.010$                      |
| <b>TESLA</b>           | $0.515 \pm 0.006$                      | $0.441 \pm 0.007$                      | $0.515 \pm 0.012$                      | $0.439 \pm 0.011$                      | $0.530 \pm 0.006$                      | $0.462 \pm 0.009$                          | $0.623 \pm 0.009$                      | $0.800 \pm 0.014$                      | $0.603 \pm 0.004$                      | $0.778 \pm 0.011$                      | $0.631 \pm 0.003$                      | $0.809 + 0.009$                        |
| <b>FTD</b>             | $0.505 \pm 0.009$                      | $0.418 \pm 0.010$                      | $0.500 \pm 0.015$                      | $0.414 \pm 0.016$                      | $0.539 \pm 0.006$                      | $0.470 + 0.008$                            | $0.635 \pm 0.013$                      | $0.787 + 0.018$                        | $0.644 \pm 0.016$                      | $0.796 \pm 0.024$                      | $0.632 \pm 0.005$                      | $0.783 \pm 0.011$                      |
| <b>DATM</b>            | $0.501 \pm 0.011$                      | $0.416 \pm 0.012$                      | $0.509 \pm 0.018$                      | $0.428 + 0.023$                        | $0.511 \pm 0.005$                      | $0.431 \pm 0.007$                          | $0.583 \pm 0.008$                      | $0.707 + 0.015$                        | $0.592 \pm 0.004$                      | $0.709 \pm 0.009$                      | $0.598 \pm 0.005$                      | $0.726 \pm 0.012$                      |
| CondTSF                | $0.326 \pm 0.002$                      | $0.231 \pm 0.002$                      | $0.324 \pm 0.012$                      | $0.230 + 0.007$                        | $0.373 \pm 0.008$                      | $0.272 \pm 0.008$                          | $0.423 \pm 0.004$                      | $0.498 \pm 0.003$                      | $0.419 \pm 0.006$                      | $0.488 + 0.007$                        | $0.454 \pm 0.005$                      | $0.522 + 0.006$                        |
|                        |                                        |                                        |                                        |                                        |                                        |                                            |                                        |                                        |                                        |                                        |                                        |                                        |
|                        |                                        |                                        |                                        |                                        |                                        |                                            |                                        |                                        |                                        |                                        |                                        |                                        |
|                        |                                        | <b>MLP</b>                             | <b>LSTM</b>                            | ETTm1                                  |                                        | CNN                                        | <b>MLP</b>                             |                                        | ETTm2<br><b>LSTM</b>                   |                                        |                                        | <b>CNN</b>                             |
|                        | MAE                                    | <b>MSE</b>                             | MAE                                    | <b>MSE</b>                             | MAE                                    | <b>MSE</b>                                 | <b>MAE</b>                             | <b>MSE</b>                             | MAE                                    | <b>MSE</b>                             | MAE                                    | <b>MSE</b>                             |
| Random                 | $0.697 + 0.009$                        | $0.859 \pm 0.020$                      | $0.677 + 0.017$                        | $0.801 \pm 0.033$                      | $0.713 \pm 0.015$                      |                                            | $0.891 \pm 0.027$   $0.732 \pm 0.017$  | $0.880 \pm 0.041$                      | $0.754 \pm 0.020$                      | $0.927 \pm 0.054$                      | $0.760 \pm 0.021$                      | $0.934 \pm 0.056$                      |
| DC                     | $0.662 \pm 0.006$                      | $0.786 \pm 0.003$                      | $0.636 \pm 0.007$                      | $0.741 \pm 0.011$                      | $0.676 \pm 0.013$                      | $0.808 + 0.028$                            | $0.623 \pm 0.036$                      | $0.629 \pm 0.069$                      | $0.532 \pm 0.028$                      | $0.459 \pm 0.048$                      | $0.682 \pm 0.023$                      | $0.745 \pm 0.051$                      |
| <b>KIP</b>             | $0.566 \pm 0.005$                      | $0.697 \pm 0.019$                      | $0.555 \pm 0.008$                      | $0.690 \pm 0.018$                      | $0.571 \pm 0.007$                      | $0.694 \pm 0.015$                          | $0.285 \pm 0.012$                      | $0.144 \pm 0.009$                      | $0.290 \pm 0.021$                      | $0.149 + 0.015$                        | $0.347 \pm 0.031$                      | $0.201 \pm 0.028$                      |
| FRePo                  | $0.599 \pm 0.007$                      | $0.718 + 0.013$                        | $0.611 \pm 0.028$                      | $0.738 \pm 0.048$                      | $0.630 \pm 0.031$                      | $0.749 \pm 0.086$                          | $0.476 \pm 0.032$                      | $0.412 \pm 0.043$                      | $0.472 \pm 0.057$                      | $0.395 \pm 0.089$                      | $0.579 + 0.075$                        | $0.574 \pm 0.142$                      |
| MTT                    | $0.484 \pm 0.003$                      | $0.484 \pm 0.005$                      | $0.515 \pm 0.033$                      | $0.530 \pm 0.048$                      | $0.563 \pm 0.006$                      | $0.608 \pm 0.019$                          | $0.258 \pm 0.007$                      | $0.129 \pm 0.005$                      | $0.246 \pm 0.005$                      | $0.124 \pm 0.004$                      | $0.340 \pm 0.016$                      | $0.193 \pm 0.017$                      |
| PP                     | $0.486 \pm 0.007$                      | $0.474 \pm 0.008$                      | $0.527 \pm 0.031$                      | $0.539 \pm 0.040$                      | $0.581 \pm 0.019$                      | $0.644 \pm 0.036$                          | $0.272 \pm 0.004$                      | $0.136 \pm 0.003$                      | $0.269 \pm 0.002$                      | $0.135 \pm 0.002$                      | $0.308 \pm 0.013$                      | $0.167 + 0.010$                        |
| <b>TESLA</b>           | $0.519 \pm 0.003$                      | $0.523 \pm 0.003$                      | $0.513 \pm 0.007$                      | $0.516 \pm 0.007$                      | $0.579 \pm 0.013$                      | $0.620 \pm 0.020$                          | $0.272 \pm 0.004$                      | $0.135 \pm 0.003$                      | $0.272 \pm 0.007$                      | $0.135 \pm 0.006$                      | $0.365 \pm 0.041$                      | $0.221 \pm 0.043$                      |
| <b>FTD</b>             | $0.528 \pm 0.015$                      | $0.579 \pm 0.032$                      | $0.631 \pm 0.015$                      | $0.790 \pm 0.037$                      | $0.576 \pm 0.024$                      | $0.626 \pm 0.041$                          | $0.279 \pm 0.005$                      | $0.142 \pm 0.004$                      | $0.290 \pm 0.011$                      | $0.147 + 0.010$                        | $0.403 \pm 0.025$                      | $0.254 \pm 0.024$                      |
| <b>DATM</b>            | $0.499 + 0.007$                        | $0.516 \pm 0.006$                      | $0.513 \pm 0.015$                      | $0.524 \pm 0.016$                      | $0.577 + 0.030$                        | $0.616 \pm 0.046$                          | $0.293 \pm 0.004$                      | $0.153 \pm 0.004$                      | $0.290 \pm 0.009$                      | $0.149 \pm 0.007$                      | $0.377 + 0.030$                        | $0.232 \pm 0.029$                      |
| CondTSF                | $0.452 \pm 0.004$                      | $0.455 + 0.001$                        | $0.459 \pm 0.013$                      | $0.461 \pm 0.011$                      | $0.520 \pm 0.018$                      | $0.543 \pm 0.025$                          | $0.231 \pm 0.002$                      | $0.107 + 0.001$                        | $0.240 \pm 0.009$                      | $0.111 \pm 0.005$                      | $0.273 \pm 0.021$                      | $0.133 + 0.014$                        |
|                        |                                        |                                        |                                        | ETTh1                                  |                                        |                                            |                                        |                                        | ETTh <sub>2</sub>                      |                                        |                                        |                                        |
|                        |                                        | MLP                                    | <b>LSTM</b>                            |                                        |                                        | <b>CNN</b>                                 | <b>MLP</b>                             |                                        | <b>LSTM</b>                            |                                        |                                        | <b>CNN</b>                             |
|                        | <b>MAE</b>                             | <b>MSE</b>                             | <b>MAE</b>                             | <b>MSE</b>                             | <b>MAE</b>                             | <b>MSE</b>                                 | <b>MAE</b>                             | <b>MSE</b>                             | MAE                                    | <b>MSE</b>                             | <b>MAE</b>                             | <b>MSE</b>                             |
| Random                 | $0.670 \pm 0.011$                      | $0.796 \pm 0.023$                      | $0.658 \pm 0.012$                      | $0.773 \pm 0.022$                      | $0.703 \pm 0.014$                      | $0.874 \pm 0.039$                          | $0.732 \pm 0.012$                      | $0.874 \pm 0.031$                      | $0.702 \pm 0.014$                      | $0.799 \pm 0.031$                      | $0.755 + 0.027$                        | $0.908 + 0.062$                        |
| DC                     | $0.643 \pm 0.019$                      | $0.745 \pm 0.038$                      | $0.626 \pm 0.014$                      | $0.718 \pm 0.013$                      | $0.672 \pm 0.023$                      | $0.802 \pm 0.041$                          | $0.619 \pm 0.019$                      | $0.650 \pm 0.043$                      | $0.534 \pm 0.019$                      | $0.481 \pm 0.036$                      | $0.680 \pm 0.028$                      | $0.746 \pm 0.053$                      |
| <b>KIP</b>             | $0.636 \pm 0.017$                      | $0.732 \pm 0.029$                      | $0.608 \pm 0.016$                      | $0.696 \pm 0.027$                      | $0.650 \pm 0.016$                      | $0.758 + 0.021$                            | $0.494 \pm 0.009$                      | $0.419 \pm 0.011$                      | $0.431 \pm 0.012$                      | $0.329 \pm 0.016$                      | $0.551 \pm 0.032$                      | $0.492 \pm 0.051$                      |
| FRePo                  | $0.653 \pm 0.004$                      | $0.770 \pm 0.007$                      | $0.640 \pm 0.009$                      | $0.754 \pm 0.019$                      | $0.659 \pm 0.010$                      | $0.783 \pm 0.025$                          | $0.570 \pm 0.033$                      | $0.552 \pm 0.060$                      | $0.485 \pm 0.036$                      | $0.405 \pm 0.054$                      | $0.672 \pm 0.023$                      | $0.728 \pm 0.054$                      |
| MTT                    | $0.606 \pm 0.003$                      | $0.673 \pm 0.009$                      | $0.613 \pm 0.005$                      | $0.680 + 0.010$                        | $0.612 \pm 0.007$                      | $0.692 \pm 0.011$                          | $0.307 + 0.005$                        | $0.182 \pm 0.004$                      | $0.305 \pm 0.014$                      | $0.180 + 0.010$                        | $0.374 \pm 0.033$                      | $0.246 \pm 0.036$                      |
| $\rm PP$               | $0.633 \pm 0.006$                      | $0.719 \pm 0.006$                      | $0.630 \pm 0.006$                      | $0.710 \pm 0.009$                      | $0.635 \pm 0.007$                      | $0.730 \pm 0.007$                          | $0.354 \pm 0.019$                      | $0.229 \pm 0.020$                      | $0.292 \pm 0.012$                      | $0.173 \pm 0.007$                      | $0.450 \pm 0.060$                      | $0.346 \pm 0.085$                      |
| <b>TESLA</b>           | $0.602 \pm 0.005$                      | $0.671 \pm 0.010$                      | $0.590 + 0.005$                        | $0.651 \pm 0.013$                      | $0.612 \pm 0.005$                      | $0.691 \pm 0.015$                          | $0.308 + 0.007$                        | $0.181 \pm 0.006$                      | $0.292 \pm 0.004$                      | $0.170 \pm 0.003$                      | $0.390 \pm 0.016$                      | $0.257 + 0.014$                        |
| <b>FTD</b>             | $0.616 \pm 0.008$                      | $0.710 \pm 0.014$                      | $0.618 \pm 0.008$                      | $0.716 \pm 0.011$                      | $0.626 \pm 0.008$                      | $0.725 \pm 0.014$                          | $0.329 \pm 0.003$                      | $0.197 \pm 0.004$                      | $0.312 \pm 0.007$                      | $0.187 + 0.012$                        | $0.386 \pm 0.014$                      | $0.249 + 0.023$                        |
| <b>DATM</b><br>CondTSF | $0.617 \pm 0.004$<br>$0.434 \pm 0.001$ | $0.681 \pm 0.010$<br>$0.397 + 0.001$   | $0.612 \pm 0.007$<br>$0.429 \pm 0.002$ | $0.672 \pm 0.015$<br>$0.388 \pm 0.005$ | $0.637 \pm 0.004$<br>$0.473 \pm 0.006$ | $0.723 \pm 0.010$<br>$0.456 \!\pm\! 0.008$ | $0.337 \pm 0.005$<br>$0.290 \pm 0.005$ | $0.208 \pm 0.006$<br>$0.168 \pm 0.004$ | $0.329 \pm 0.006$<br>$0.287 + 0.006$   | $0.200 \pm 0.006$<br>$0.166 \pm 0.004$ | $0.398 \pm 0.015$<br>$0.342 \pm 0.008$ | $0.268 \pm 0.013$<br>$0.211 \pm 0.006$ |

set the length of the synthetic dataset as 48, as shown in Table[.2.](#page-7-0) Each synthetic dataset can only generate one training pair. We conduct experiments with two larger distill ratios as shown in App[.B.](#page-15-0)

Model Settings: We plug CondTSF into existing dataset condensation models based on parameter matching, including DC[\[51\]](#page-12-0), MTT[\[3\]](#page-9-3), PP[\[21\]](#page-10-18), TESLA[\[5\]](#page-9-5), FTD[\[7\]](#page-9-4) and DATM[\[11\]](#page-9-7) to prove the effectiveness of CondTSF. We also conduct experiments on non-parameter-matching based methods, including DM[\[50\]](#page-12-1), IDM[\[52\]](#page-12-2), KIP[\[33\]](#page-11-7), FRePo[\[55\]](#page-12-3) to prove that optimizing value term only also helps boost the performance. The experiment setting and results are shown in App[.E.](#page-19-0) We use DLinear[\[45\]](#page-11-12) as the expert model to perform dataset condensation since DLinear is a linear model.

Metric Settings: The source dataset is first divided into a train set and a test set. All synthetic data is initialized by randomly sampling data from the train set. After a synthetic dataset is finished distilling, it is used to train another five models. After the five models are trained, they are tested on the test set. Their average mean absolute error (MAE) and mean square error (MSE) are recorded. We repeat the process above five times and report the average and standard deviation. While testing the generalization ability of the dataset condensation methods, DLinear[\[45\]](#page-11-12) is used as the expert model to perform dataset condensation. Meanwhile, MLP, LSTM[\[13\]](#page-10-19), and CNN are used as test models when testing the generalization ability of the dataset condensation methods.

<span id="page-8-0"></span>Image /page/8/Figure/0 description: This figure contains three plots comparing the performance of MTT and MTT + CondTSF over condensation epochs. The first plot, titled "Gradient Term", shows the parameter error on the y-axis and condensation epochs on the x-axis. Both MTT and MTT + CondTSF start with a parameter error of 1.0 and decrease over epochs, with MTT + CondTSF generally showing a lower parameter error after epoch 40. The second plot, titled "L\_label Value Term", shows the label error on the y-axis and condensation epochs on the x-axis. MTT shows a label error that increases from 0 to about 80 by epoch 80 and then plateaus, while MTT + CondTSF shows a label error that increases to a peak of about 20 by epoch 60 and then decreases. The third plot, titled "L\_test", shows the test error on the y-axis and condensation epochs on the x-axis. Both MTT and MTT + CondTSF start with a test error of approximately 0.8 and decrease over epochs. MTT + CondTSF shows a significantly lower test error than MTT, especially after epoch 40, reaching a test error of around 0.2 by epoch 200, while MTT plateaus around 0.5.

Figure 4: Changing trajectory of Left: parameter error which refer to gradient term, Middle: label error which refer to value term and **Right:** test error during dataset condensation process.

Implementation Details: As a plugin module, we test CondTSF with all previous methods. Each synthetic dataset is optimized using a standard training process according to the chosen backbone model. CondTSF is set to update every 3 epochs and the additive update ratio  $\beta$  is set to be 0.01. All the experiments are carried out on an NVIDIA RTX 3080Ti.

## 5.2 Results

Single Architecture Performance: The results are summarized in Table[.1.](#page-6-0) For each backbone method, the first line shows the performance of the backbone model, the second line shows the performance of a backbone model with CondTSF, and the third line shows the percentage of reduction in MAE and MSE after CondTSF is applied. There's a considerable reduction in error for all backbone models. The results suggest that CondTSF is effective in optimizing the value term and enhancing the performance in dataset condensation for TS-forecasting. However, using CondTSF on DC[\[51\]](#page-12-0) is not as effective as other methods. The reason is that instead of directly matching parameters, DC matches the gradient of parameters on loss in each iteration. Indirectly matching gradient leads to accumulating errors in parameters, making DC unable to lower parameter error as effectively as directly matching parameters. Therefore CondTSF is not effective enough when applied to DC[\[51\]](#page-12-0).

Cross Architecture Performance: We also conduct experiments to evaluate the cross-architecture performance of dataset condensation methods. The results are summarized in Table[.3.](#page-7-1) We test all models on all datasets with MLP, LSTM[\[13\]](#page-10-19), and CNN as test models. All synthetic data is distilled using DLinear[\[45\]](#page-11-12) model as experts. We use MTT[\[3\]](#page-9-3) as the backbone for CondTSF. We observe that CondTSF based on MTT outperformed all other previous models.

## 5.3 Discussion

Test Performance and Errors: We conduct experiments on ExchangeRate dataset with MTT[\[3\]](#page-9-3) and MTT+CondTSF. As shown in Fig[.4,](#page-8-0) trajectory of parameter error  $\frac{||\theta_f - \theta_s||^2}{||\theta_f - \theta_0||^2}$ , label error  $\mathcal{L}_{label}$ and test error  $\mathcal{L}_{test}$  through the distillation process are presented. Regarding the parameter error corresponding to the gradient term, both MTT and MTT+CondTSF converge quickly, suggesting that the incorporation of CondTSF doesn't impact parameter alignment. As for the label error corresponding to the value term, since the initial synthetic data  $s$  is randomly sampled from the train set  $f$  and the expert model is trained by the train set  $f$ , the label error of  $s$  is small at the beginning. However, the utilization of MTT results in an elevation of label error, whereas employing CondTSF effectively mitigates this increase in label error. During the test, MTT+CondTSF notably outperforms MTT by concurrently optimizing both the value term and the gradient term.

# <span id="page-8-1"></span>6 Limitations

The limitation of this work is that we use linear models in our analysis so that the gradient of a model on input is the parameter of the model. Therefore, only linear models like DLinear[\[45\]](#page-11-12) are solid enough to be an expert model for dataset condensation. The analysis no longer holds when it comes to more complicated models. However, experiments in App[.D](#page-18-0) and App[.E](#page-19-0) show that CondTSF is

also effective with non-parameter-matching methods and non-linear models, which merits further exploration.

# 7 Conclusion

In this study, we provide abundant proof that previous dataset condensation methods based on classification are not suitable for dataset condensation for TS-forecasting. We elucidate that these earlier methods, predominantly focused on classification tasks, only address a portion of the optimization objective pertinent to TS-forecasting. To address this issue, we propose a plugin module called CondTSF that can collaborate with parameter matching based dataset condensation methods. CondTSF optimizes the optimization objective that previous methods have neglected and boosts the performance of dataset condensation methods on TS-forecasting. We conduct experiments on eight widely used time series datasets and prove the effectiveness of our proof and method. CondTSF consistently enhances the performance of all previous techniques across all datasets, substantiating its effectiveness in improving dataset condensation outcomes for TS-forecasting applications.

### Acknowledgements

This work was sponsored by National Natural Science Foundation of China under Grant No. 62102246, 62272301, and Provincial Key Research and Development Program of Zhejiang under Grant No. 2021C01034. Part of the work was done when the students were doing internships at Yunqi Academy of Engineering.

# References

- <span id="page-9-9"></span>[1] Olivier Bachem, Mario Lucic, and Andreas Krause. Practical coreset constructions for machine learning. *arXiv preprint arXiv:1703.06476*, 2017.
- <span id="page-9-2"></span>[2] Defu Cao, Furong Jia, Sercan O Arik, Tomas Pfister, Yixiang Zheng, Wen Ye, and Yan Liu. Tempo: Prompt-based generative pre-trained transformer for time series forecasting. *arXiv preprint arXiv:2310.04948*, 2023.
- <span id="page-9-3"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-9-10"></span>[4] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. *arXiv preprint arXiv:1203.3472*, 2012.
- <span id="page-9-5"></span>[5] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023.
- <span id="page-9-1"></span>[6] Abhimanyu Das, Weihao Kong, Rajat Sen, and Yichen Zhou. A decoder-only foundation model for time-series forecasting. *arXiv preprint arXiv:2310.10688*, 2023.
- <span id="page-9-4"></span>[7] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3749–3758, 2023.
- <span id="page-9-6"></span>[8] Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-9-8"></span>[9] Qizhang Feng, Zhimeng Jiang, Ruiquan Li, Yicheng Wang, Na Zou, Jiang Bian, and Xia Hu. Fair graph distillation. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-9-0"></span>[10] Azul Garza and Max Mergenthaler-Canseco. Timegpt-1. *arXiv preprint arXiv:2310.03589*, 2023.
- <span id="page-9-7"></span>[11] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023.

- <span id="page-10-13"></span>[12] Sariel Har-Peled and Akash Kushal. Smaller coresets for k-median and k-means clustering. In *Proceedings of the twenty-first annual symposium on Computational geometry*, pages 126–134, 2005.
- <span id="page-10-19"></span>[13] Sepp Hochreiter and Jürgen Schmidhuber. Long short-term memory. *Neural computation*, 9(8):1735–1780, 1997.
- <span id="page-10-1"></span>[14] Ming Jin, Shiyu Wang, Lintao Ma, Zhixuan Chu, James Y Zhang, Xiaoming Shi, Pin-Yu Chen, Yuxuan Liang, Yuan-Fang Li, Shirui Pan, et al. Time-llm: Time series forecasting by reprogramming large language models. *arXiv preprint arXiv:2310.01728*, 2023.
- <span id="page-10-0"></span>[15] Ming Jin, Qingsong Wen, Yuxuan Liang, Chaoli Zhang, Siqiao Xue, Xue Wang, James Zhang, Yi Wang, Haifeng Chen, Xiaoli Li, et al. Large models for time series and spatio-temporal data: A survey and outlook. *arXiv preprint arXiv:2310.10196*, 2023.
- <span id="page-10-5"></span>[16] Wei Jin, Xianfeng Tang, Haoming Jiang, Zheng Li, Danqing Zhang, Jiliang Tang, and Bin Ying. Condensing graphs via one-step gradient matching. In *Proceedings of the ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD)*, 2022.
- <span id="page-10-4"></span>[17] Wei Jin, Lingxiao Zhao, Shichang Zhang, Yozen Liu, Jiliang Tang, and Neil Shah. Graph condensation for graph neural networks. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2022.
- <span id="page-10-14"></span>[18] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022.
- <span id="page-10-17"></span>[19] Joseph B Kruskal. Multidimensional scaling by optimizing goodness of fit to a nonmetric hypothesis. *Psychometrika*, 29(1):1–27, 1964.
- <span id="page-10-2"></span>[20] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022.
- <span id="page-10-18"></span>[21] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Dataset distillation using parameter pruning. *IEICE Transactions on Fundamentals of Electronics, Communications and Computer Sciences*, 2023.
- <span id="page-10-3"></span>[22] Haoyang Liu, Tiancheng Xing, Luwei Li, Vibhu Dalal, Jingrui He, and Haohan Wang. Dataset distillation via the wasserstein metric. *arXiv preprint arXiv:2311.18531*, 2023.
- <span id="page-10-6"></span>[23] Mengyang Liu, Shanchuan Li, Xinshi Chen, and Le Song. Graph condensation via receptive field distribution matching. *arXiv preprint arXiv:2206.13697*, 2022.
- <span id="page-10-10"></span>[24] Shizhan Liu, Hang Yu, Cong Liao, Jianguo Li, Weiyao Lin, Alex X Liu, and Schahram Dustdar. Pyraformer: Low-complexity pyramidal attention for long-range time series modeling and forecasting. In *International conference on learning representations*, 2021.
- <span id="page-10-7"></span>[25] Yilun Liu, Ruihong Qiu, and Zi Huang. Cat: Balanced continual graph learning with graph condensation. In *Proceedings of the IEEE International Conference on Data Mining (ICDM)*, 2023.
- <span id="page-10-9"></span>[26] Zhanyu Liu, Ke Hao, Guanjie Zheng, and Yanwei Yu. Dataset condensation for time series classification via dual domain matching. *arXiv preprint arXiv:2403.07245*, 2024.
- <span id="page-10-8"></span>[27] Zhanyu Liu, Chaolv Zeng, and Guanjie Zheng. Graph data condensation via self-expressive graph structure reconstruction. *arXiv preprint arXiv:2403.07294*, 2024.
- <span id="page-10-11"></span>[28] Zhanyu Liu, Guanjie Zheng, and Yanwei Yu. Cross-city few-shot traffic forecasting via traffic pattern bank. In *Proceedings of the 32nd ACM International Conference on Information and Knowledge Management*, pages 1451–1460, 2023.
- <span id="page-10-12"></span>[29] Zhanyu Liu, Guanjie Zheng, and Yanwei Yu. Multi-scale traffic pattern bank for cross-city few-shot traffic forecasting. *arXiv preprint arXiv:2402.00397*, 2024.
- <span id="page-10-15"></span>[30] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *Advances in Neural Information Processing Systems*, 35:13877–13891, 2022.
- <span id="page-10-16"></span>[31] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. *arXiv preprint arXiv:2302.06755*, 2023.

- <span id="page-11-0"></span>[32] John A Miller, Mohammed Aldosari, Farah Saeed, Nasid Habib Barna, Subas Rana, I Budak Arpinar, and Ninghao Liu. A survey of deep learning and foundation models for time series forecasting. *arXiv preprint arXiv:2401.13912*, 2024.
- <span id="page-11-7"></span>[33] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *International Conference on Learning Representations*, 2020.
- <span id="page-11-13"></span>[34] Yuqi Nie, Nam H Nguyen, Phanwadee Sinthong, and Jayant Kalagnanam. A time series is worth 64 words: Long-term forecasting with transformers. *arXiv preprint arXiv:2211.14730*, 2022.
- <span id="page-11-1"></span>[35] Kashif Rasul, Arjun Ashok, Andrew Robert Williams, Arian Khorasani, George Adamopoulos, Rishika Bhagwatkar, Marin Biloš, Hena Ghonia, Nadhir Vincent Hassen, Anderson Schneider, et al. Lag-llama: Towards foundation models for time series forecasting. *arXiv preprint arXiv:2310.08278*, 2023.
- <span id="page-11-6"></span>[36] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 17097–17107, 2023.
- <span id="page-11-15"></span>[37] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. *arXiv preprint arXiv:1708.00489*, 2017.
- <span id="page-11-4"></span>[38] Seungjae Shin, Heesun Bae, Donghyeok Shin, Weonyoung Joo, and Il-Chul Moon. Losscurvature matching for dataset selection and condensation. In *International Conference on Artificial Intelligence and Statistics*, pages 8606–8628. PMLR, 2023.
- <span id="page-11-16"></span>[39] Ivor W Tsang, James T Kwok, Pak-Ming Cheung, and Nello Cristianini. Core vector machines: Fast svm training on very large data sets. *Journal of Machine Learning Research*, 6(4), 2005.
- <span id="page-11-9"></span>[40] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez, Łukasz Kaiser, and Illia Polosukhin. Attention is all you need. *Advances in neural information processing systems*, 30, 2017.
- <span id="page-11-5"></span>[41] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196–12205, 2022.
- <span id="page-11-10"></span>[42] Haixu Wu, Jiehui Xu, Jianmin Wang, and Mingsheng Long. Autoformer: Decomposition transformers with auto-correlation for long-term series forecasting. *Advances in neural information processing systems*, 34:22419–22430, 2021.
- <span id="page-11-8"></span>[43] Zhe Xu, Yuzhong Chen, Menghai Pan, Huiyuan Chen, Mahashweta Das, Hao Yang, and Tong Hanghang. Kernel ridge regression-based graph dataset distillation. In *Proceedings of the ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD)*, 2023.
- <span id="page-11-2"></span>[44] Hao Xue and Flora D Salim. Promptcast: A new prompt-based learning paradigm for time series forecasting. *IEEE Transactions on Knowledge and Data Engineering*, 2023.
- <span id="page-11-12"></span>[45] Ailing Zeng, Muxi Chen, Lei Zhang, and Qiang Xu. Are transformers effective for time series forecasting? In *Proceedings of the AAAI conference on artificial intelligence*, volume 37, pages 11121–11128, 2023.
- <span id="page-11-17"></span>[46] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 11950–11959, 2023.
- <span id="page-11-11"></span>[47] Tianping Zhang, Yizhuo Zhang, Wei Cao, Jiang Bian, Xiaohan Yi, Shun Zheng, and Jian Li. Less is more: Fast multivariate time series forecasting with light sampling-oriented mlp structures. *arXiv preprint arXiv:2207.01186*, 2022.
- <span id="page-11-14"></span>[48] Yunhao Zhang and Junchi Yan. Crossformer: Transformer utilizing cross-dimension dependency for multivariate time series forecasting. In *The eleventh international conference on learning representations*, 2022.
- <span id="page-11-3"></span>[49] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021.

- <span id="page-12-1"></span>[50] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023.
- <span id="page-12-0"></span>[51] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021.
- <span id="page-12-2"></span>[52] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023.
- <span id="page-12-4"></span>[53] Haoyi Zhou, Shanghang Zhang, Jieqi Peng, Shuai Zhang, Jianxin Li, Hui Xiong, and Wancai Zhang. Informer: Beyond efficient transformer for long sequence time-series forecasting. In *Proceedings of the AAAI conference on artificial intelligence*, volume 35, pages 11106–11115, 2021.
- <span id="page-12-5"></span>[54] Tian Zhou, Ziqing Ma, Qingsong Wen, Xue Wang, Liang Sun, and Rong Jin. Fedformer: Frequency enhanced decomposed transformer for long-term series forecasting. In *International conference on machine learning*, pages 27268–27286. PMLR, 2022.
- <span id="page-12-3"></span>[55] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022.

# A Complete Proof

### <span id="page-13-0"></span>A.1 Complete Proof for Theorem [1](#page-3-2)

**Theorem [1.](#page-3-2)** Given arbitrary synthetic data  $s_{t':t'+m}$ , the upper bound of the optimization objective *of dataset condensation*  $\mathcal{L}_{test}(\mathcal{M}_{\theta_{stest}}, x)$  *can be formulated as such* 

$$
\mathcal{L}_{test}(\mathcal{M}_{{\theta}_{s, test}}, \mathbf{x}) \leq \sum_{t} ||\boldsymbol{\epsilon}||^{2} + \underbrace{||\mathcal{M}_{{\theta}_{s, test}}(\boldsymbol{s}_{t':t'+m}) - \mathcal{M}_{{\theta}_{f, test}}(\boldsymbol{s}_{t':t'+m})||^{2}}_{\text{Value Term}} + \underbrace{||(\nabla \mathcal{M}_{{\theta}_{s, test}}(\boldsymbol{s}_{t':t'+m}) - \nabla \mathcal{M}_{{\theta}_{f, test}}(\boldsymbol{s}_{t':t'+m}))^{\top}(\boldsymbol{x}_{t:t+m} - \boldsymbol{s}_{t':t'+m})||^{2}}_{\text{Gradient Term}} \tag{14}
$$

*Proof.* Replacing the true label  $x_{t+m:t+m+n}$  in  $\mathcal{L}_{test}(\mathcal{M}_{\theta_{s, test}}, x)$  with Eq[.4,](#page-3-1) the optimization objective of dataset condensation for TS-forecasting is reformulated as the distance between the predictions of  $\mathcal{M}_{\theta_{s, test}}$  and  $\mathcal{M}_{\theta_{f, test}}$  given the same test input. Then the triangular inequality of norm functions is used and the original optimization objective can be transformed to its upper bound, as shown in Eq[.15.](#page-13-1)

<span id="page-13-1"></span>
$$
\mathcal{L}_{test}(\mathcal{M}_{\theta_{s, test}}, \boldsymbol{x}) = \sum_{t} ||\mathcal{M}_{\theta_{s, test}}(\boldsymbol{x}_{t:t+m}) - \boldsymbol{x}_{t+m:t+m+n}||^2
$$
\n
$$
= \sum_{t} ||\mathcal{M}_{\theta_{s, test}}(\boldsymbol{x}_{t:t+m}) - \mathcal{M}_{\theta_{f, test}}(\boldsymbol{x}_{t:t+m}) - \epsilon||^2
$$
\n
$$
\leq \sum_{t} ||\mathcal{M}_{\theta_{s, test}}(\boldsymbol{x}_{t:t+m}) - \mathcal{M}_{\theta_{f, test}}(\boldsymbol{x}_{t:t+m})||^2 + ||\boldsymbol{\epsilon}||^2
$$
\n(15)

In Eq[.15,](#page-13-1) we prove that minimizing the distance between  $\mathcal{M}_{\theta_{s, test}}(\boldsymbol{x}_{t:t+m})$  and  $\mathcal{M}_{\theta_{f, test}}(\boldsymbol{x}_{t:t+m})$  is equivalent to minimizing the upper bound of the original optimization objective. Then we decompose the distance between predictions of  $\mathcal{M}_{\theta_{s, test}}$  and  $\mathcal{M}_{\theta_{f, test}}$  into two optimizable terms for better optimization. We use linear models for further analysis since linear models can be both effective and efficient in TS-forecasting tasks[\[45\]](#page-11-12). Given a linear model  $\mathcal{M}_{\theta}(x) = \theta x$ , its second and higher order gradient is zero, i.e.  $\nabla^k \mathcal{M}_{\theta}(\boldsymbol{x}) = \mathbf{0}, \forall k \geq 2$ . Therefore, first-order Taylor Expansion can be utilized to get the prediction of the model  $\mathcal{M}_{\theta}$  on test data  $x_{t:t+m}$  using the prediction and gradient of the model  $\mathcal{M}_{\theta}$  on arbitrary synthetic data  $s_{t':t'+m}$ . The process is formulated in Eq[.16.](#page-13-2) Meanwhile, it is worth mentioning that if  $\mathcal{M}_{\theta}$  is a non-linear model, then the second and higher-order terms of the Taylor Expansion are ignored in Eq[.16.](#page-13-2)

<span id="page-13-2"></span>
$$
\mathcal{M}_{\theta}(\boldsymbol{x}_{t:t+m}) = \mathcal{M}_{\theta}(\boldsymbol{s}_{t':t'+m}) + \nabla \mathcal{M}_{\theta}(\boldsymbol{s}_{t':t'+m})^{\top}(\boldsymbol{x}_{t:t+m} - \boldsymbol{s}_{t':t'+m})
$$
(16)

Then we expand Eq[.15](#page-13-1) with Taylor expansion. After that, the triangular inequality of norm functions is used to get its upper bound. In the meantime, by applying the triangular inequality, the optimization objective can be decomposed into two optimizable terms.

$$
\mathcal{L}_{test}(\mathcal{M}_{\theta_{s, test}}, \boldsymbol{x}) \leq \sum_{t} ||\mathcal{M}_{\theta_{s, test}}(\boldsymbol{x}_{t:t+m}) - \mathcal{M}_{\theta_{f, test}}(\boldsymbol{x}_{t:t+m})||^{2} + ||\boldsymbol{\epsilon}||^{2}
$$
\n
$$
= \sum_{t} ||\boldsymbol{\epsilon}||^{2} + ||\mathcal{M}_{\theta_{s, test}}(\boldsymbol{s}_{t':t'+m}) + \nabla \mathcal{M}_{\theta_{s, test}}(\boldsymbol{s}_{t':t'+m})^{\top}(\boldsymbol{x}_{t:t+m} - \boldsymbol{s}_{t':t'+m})
$$
\n
$$
- \mathcal{M}_{\theta_{f, test}}(\boldsymbol{s}_{t':t'+m}) - \nabla \mathcal{M}_{\theta_{f, test}}(\boldsymbol{s}_{t':t'+m})^{\top}(\boldsymbol{x}_{t:t+m} - \boldsymbol{s}_{t':t'+m})||^{2}
$$
\n
$$
= \sum_{t} ||\boldsymbol{\epsilon}||^{2} + ||\mathcal{M}_{\theta_{s, test}}(\boldsymbol{s}_{t':t'+m}) - \mathcal{M}_{\theta_{f, test}}(\boldsymbol{s}_{t':t'+m})
$$
\n
$$
+ (\nabla \mathcal{M}_{\theta_{s, test}}(\boldsymbol{s}_{t':t'+m}) - \nabla \mathcal{M}_{\theta_{f, test}}(\boldsymbol{s}_{t':t'+m}))^{\top}(\boldsymbol{x}_{t:t+m} - \boldsymbol{s}_{t':t'+m})||^{2}
$$
\n
$$
\leq \sum_{t} ||\boldsymbol{\epsilon}||^{2} + ||\mathcal{M}_{\theta_{s, test}}(\boldsymbol{s}_{t':t'+m}) - \mathcal{M}_{\theta_{f, test}}(\boldsymbol{s}_{t':t'+m})||^{2}
$$
\n
$$
\frac{\text{Value Term}}{\text{Value Term}}
$$
\n
$$
+ ||(\nabla \mathcal{M}_{\theta_{s, test}}(\boldsymbol{s}_{t':t'+m}) - \nabla \mathcal{M}_{\theta_{f, test}}(\boldsymbol{s}_{t':t'+m}))^{\top}(\boldsymbol{x}_{t:t+m} - \boldsymbol{s}_{t':t'+m})||^{2}}_{\text{Gradient Term}} \tag{17}
$$

Therefore Thm[.1](#page-3-2) is proved.

 $\Box$ 

#### <span id="page-14-0"></span>A.2 Complete Proof for Theorem [2](#page-5-0)

Theorem [2.](#page-5-0) *The upper bound of the value term can be formulated as such*

$$
||\mathcal{M}_{\theta_{s, test}}(s_{t':t'+m}) - \mathcal{M}_{\theta_{f, test}}(s_{t':t'+m})||^2 \leq 2 \cdot \sum_{t'} ||\mathcal{M}_{\theta_{f, test}}(s_{t':t'+m}) - s_{t'+m:t'+m+n}||^2
$$
 (18)

*Proof.* We first use triangular inequality and the non-negativity of norm functions to get the upper bound of the value term. The process is shown in Eq[.19.](#page-14-1)

<span id="page-14-1"></span>
$$
||M_{\theta_{s, test}}(s_{t':t'+m}) - M_{\theta_{f, test}}(s_{t':t'+m})||^{2} \text{ (Value Term)}
$$
\n
$$
= ||M_{\theta_{s, test}}(s_{t':t'+m}) - s_{t'+m:t'+m+n} + s_{t'+m:t'+m+n} - M_{\theta_{f, test}}(s_{t':t'+m})||^{2}
$$
\n
$$
\leq ||M_{\theta_{s, test}}(s_{t':t'+m}) - s_{t'+m:t'+m+n}||^{2} + ||s_{t'+m:t'+m+n} - M_{\theta_{f, test}}(s_{t':t'+m})||^{2}
$$
\n
$$
\leq \sum_{t'} ||M_{\theta_{s, test}}(s_{t':t'+m}) - s_{t'+m:t'+m+n}||^{2} + \sum_{t'} ||s_{t'+m:t'+m+n} - M_{\theta_{f, test}}(s_{t':t'+m})||^{2}
$$
\n
$$
(19)
$$

For further analysis, we need to step back and formulate the training process of  $\theta_s$  to derive an inequality. By doing dataset condensation, a synthetic dataset  $s$  is obtained. Then we formulate the training process of  $\theta_{s, test}$  on synthetic data s as minimizing the prediction error on s. The training process is formulated in Eq[.20.](#page-14-2)

<span id="page-14-2"></span>
$$
\theta_{s, test} = \arg \min_{\theta} \sum_{t'} ||\mathcal{M}_{\theta}(s_{t':t'+m}) - s_{t'+m:t'+m+n}||^2
$$
 (20)

 $\overline{\square}$ 

Now we can derive an inequality. We denote s as the synthetic dataset obtained by dataset condensation. We denote  $\mathcal{M}_{\theta_{s, test}}$  as the model that is trained on s as shown Eq[.20.](#page-14-2) According to Eq[.20,](#page-14-2)  $\mathcal{M}_{\theta_{s, test}}$  has the lowest prediction error on synthetic data s under the given model architecture. Since  $\mathcal{M}_{\theta_{s, test}}$  and  $\mathcal{M}_{\theta_{f, test}}$  share the same model architecture, the prediction error of  $\mathcal{M}_{\theta_{s, test}}$  on synthetic data s is no larger than the prediction error of  $\mathcal{M}_{f, test}$  on synthetic data s. This inequality can be formulated as such

<span id="page-14-3"></span>
$$
\sum_{t'} ||\mathcal{M}_{\theta_{s, test}}(s_{t':t'+m}) - s_{t'+m:t'+m+n}||^2 \leq \sum_{t'} ||s_{t'+m:t'+m+n} - \mathcal{M}_{\theta_{f, test}}(s_{t':t'+m})||^2
$$
 (21)

By applying Eq[.21](#page-14-3) to Eq[.19,](#page-14-1) we obtain the upper bound of the value term, as shown in Eq[.22.](#page-14-4)

<span id="page-14-4"></span>
$$
||\mathcal{M}_{{\theta}_{s, test}}(s_{t':t'+m}) - \mathcal{M}_{{\theta}_{f, test}}(s_{t':t'+m})||^2 \text{ (Value Term)}
$$

$$
\leq \sum_{t'} ||\mathcal{M}_{{\theta}_{s, test}}(s_{t':t'+m}) - s_{t'+m:t'+m+n}||^2 + \sum_{t'} ||s_{t'+m:t'+m+n} - \mathcal{M}_{{\theta}_{f, test}}(s_{t':t'+m})||^2
$$

$$
\leq 2 \cdot \sum_{t'} ||\mathcal{M}_{{\theta}_{f, test}}(s_{t':t'+m}) - s_{t'+m:t'+m+n}||^2
$$
(22)

Therefore Thm[.2](#page-5-0) is proved.

# <span id="page-15-0"></span>B Performance with Different Distill Ratio

We further explore the performance of CondTSF with different distill ratios and compare the results with previous matching-based methods.

### B.1 Standard Ratio Condensation

<span id="page-15-1"></span>We distill the dataset into a synthetic dataset with a flexible length for each dataset. The information on condensation in Table[.4.](#page-15-1) The performance is shown in Table[.5.](#page-15-2)

Table 4: Information and condensation settings of time series datasets.

|                  | ETTm1&ETTm2 | ETTh1&ETTh2 | ExchangeRate | Weather | Electricity | Traffic |
|------------------|-------------|-------------|--------------|---------|-------------|---------|
| Dataset length   | 57600       | 14400       | 7588         | 52696   | 26304       | 17544   |
| Distill ratio    | 0.2%        | 0.4%        | 1%           | 0.2%    | 0.3%        | 0.4%    |
| Distilled length | 115         | 57          | 75           | 105     | 78          | 70      |

<span id="page-15-2"></span>Table 5: Distill performance of different dataset condensation methods. For each method, **X**means CondTSF is not used, ✓means CondTSF is used, and ↓ means the decreased percentage of test error after CondTSF is applied. Five synthetic datasets are distilled and the average and standard deviation are reported.

|              | CondTSF |                                          | <b>ExchangeRate</b>                      |                                          | Weather                                  |                                          | Electricity                              |                                          | Traffic                                  |     |
|--------------|---------|------------------------------------------|------------------------------------------|------------------------------------------|------------------------------------------|------------------------------------------|------------------------------------------|------------------------------------------|------------------------------------------|-----|
|              |         |                                          | MAE                                      | MSE                                      | <b>MAE</b>                               | MSE                                      | MAE                                      | MSE                                      | MAE                                      | MSE |
| Random       |         | $0.730 	 ±	 0.168$                       | $0.957 	 ±	 0.397$                       | $0.566 	 ±	 0.056$                       | $0.708 	 ±	 0.135$                       | $0.832 	 ±	 0.024$                       | $1.080 	 ±	 0.058$                       | $0.845 	 ±	 0.007$                       | $1.343 	 ±	 0.017$                       |     |
|              | X       | $0.657 	 ±	 0.025$                       | $0.729 	 ±	 0.062$                       | $0.488 	 ±	 0.006$                       | $0.523 	 ±	 0.007$                       | $0.797 	 ±	 0.014$                       | $0.973 	 ±	 0.031$                       | $0.816 	 ±	 0.007$                       | $1.257 	 ±	 0.023$                       |     |
| DC           | ✓       | $0.645 	 ±	 0.014$<br>$1.9%$             | $0.710 	 ±	 0.039$<br>$2.6%$             | $0.450 	 ±	 0.041$<br>$7.7%$             | $0.469 	 ±	 0.069$<br>$10.4%$            | $0.769 	 ±	 0.041$<br>$3.6%$             | $0.920 	 ±	 0.104$<br>$5.5%$             | $0.810 	 ±	 0.017$<br>$0.7%$             | $1.250 	 ±	 0.039$<br>$0.5%$             |     |
|              |         |                                          |                                          |                                          |                                          |                                          |                                          |                                          |                                          |     |
| MTT          | X<br>✓  | $0.467 	 ±	 0.018$<br>$0.180 	 ±	 0.008$ | $0.361 	 ±	 0.024$<br>$0.053 	 ±	 0.004$ | $0.330 	 ±	 0.020$<br>$0.285 	 ±	 0.010$ | $0.291 	 ±	 0.020$<br>$0.253 	 ±	 0.005$ | $0.473 	 ±	 0.014$<br>$0.335 	 ±	 0.002$ | $0.379 	 ±	 0.017$<br>$0.238 	 ±	 0.001$ | $0.575 	 ±	 0.022$<br>$0.429 	 ±	 0.006$ | $0.726 	 ±	 0.016$<br>$0.500 	 ±	 0.007$ |     |
|              |         | 61.6%                                    | 85.3%                                    | 13.4%                                    | 13.1%                                    | 29.1%                                    | 37.4%                                    | 25.5%                                    | 31.2%                                    |     |
|              | X       | $0.463 	 ±	 0.032$                       | $0.352 	 ±	 0.042$                       | $0.340 	 ±	 0.022$                       | $0.301 	 ±	 0.022$                       | $0.471 	 ±	 0.008$                       | $0.375 	 ±	 0.009$                       | $0.582 	 ±	 0.012$                       | $0.714 	 ±	 0.014$                       |     |
| PP           | ✓       | $0.179 	 ±	 0.006$                       | $0.053 	 ±	 0.004$                       | $0.279 	 ±	 0.005$                       | $0.248 	 ±	 0.005$                       | $0.336 	 ±	 0.003$                       | $0.240 	 ±	 0.001$                       | $0.423 	 ±	 0.005$                       | $0.490 	 ±	 0.007$                       |     |
|              |         | 61.2%                                    | 84.9%                                    | 17.7%                                    | 17.6%                                    | 28.5%                                    | 35.9%                                    | 27.4%                                    | 31.3%                                    |     |
|              | X       | $0.406 	 ±	 0.026$                       | $0.275 	 ±	 0.038$                       | $0.334 	 ±	 0.009$                       | $0.292 	 ±	 0.008$                       | $0.530 	 ±	 0.007$                       | $0.463 	 ±	 0.008$                       | $0.650 	 ±	 0.018$                       | $0.855 	 ±	 0.044$                       |     |
| <b>TESLA</b> | ✓       | $0.185 	 ±	 0.014$                       | $0.056 	 ±	 0.008$                       | $0.292 	 ±	 0.009$                       | $0.262 	 ±	 0.005$                       | $0.369 	 ±	 0.002$                       | $0.273 	 ±	 0.002$                       | $0.511 	 ±	 0.012$                       | $0.614 	 ±	 0.020$                       |     |
|              |         | 54.5%                                    | 79.6%                                    | 12.4%                                    | 10.2%                                    | 30.5%                                    | 41.1%                                    | 21.4%                                    | 28.2%                                    |     |
|              | X       | $0.445 	 ±	 0.038$                       | $0.332 	 ±	 0.050$                       | $0.324 	 ±	 0.010$                       | $0.284 	 ±	 0.014$                       | $0.470 	 ±	 0.003$                       | $0.374 	 ±	 0.004$                       | $0.557 	 ±	 0.016$                       | $0.680 	 ±	 0.008$                       |     |
| <b>FTD</b>   | ✓       | $0.173 	 ±	 0.003$                       | $0.049 	 ±	 0.002$                       | $0.274 	 ±	 0.006$                       | $0.246 	 ±	 0.004$                       | $0.329 	 ±	 0.004$                       | $0.232 	 ±	 0.003$                       | $0.410 	 ±	 0.005$                       | $0.476 	 ±	 0.004$                       |     |
|              |         | 61.2%                                    | 85.1%                                    | 15.3%                                    | 13.4%                                    | 30.0%                                    | 38.1%                                    | 26.4%                                    | 30.0%                                    |     |
|              | X       | $0.454 	 ±	 0.030$                       | $0.345 	 ±	 0.047$                       | $0.315 	 ±	 0.002$                       | $0.279 	 ±	 0.001$                       | $0.495 	 ±	 0.005$                       | $0.410 	 ±	 0.006$                       | $0.583 	 ±	 0.017$                       | $0.722 	 ±	 0.033$                       |     |
| <b>DATM</b>  | ✓       | $0.182 	 ±	 0.003$                       | $0.054 	 ±	 0.002$                       | $0.296 	 ±	 0.011$                       | $0.264 	 ±	 0.007$                       | $0.325 	 ±	 0.003$                       | $0.228 	 ±	 0.002$                       | $0.410 	 ±	 0.006$                       | $0.473 	 ±	 0.005$                       |     |
|              |         | 59.9%                                    | 84.4%                                    | 5.8%                                     | 5.5%                                     | 34.3%                                    | 44.3%                                    | 29.7%                                    | 34.5%                                    |     |
| Full         |         | $0.110 	 ±	 0.001$                       | $0.023 	 ±	 0.000$                       | $0.197 	 ±	 0.001$                       | $0.131 	 ±	 0.001$                       | $0.312 	 ±	 0.002$                       | $0.223 	 ±	 0.002$                       | $0.406 	 ±	 0.003$                       | $0.492 	 ±	 0.004$                       |     |
|              | CondTSF |                                          | ETTm1                                    |                                          | ETTm2                                    |                                          | ETTh1                                    |                                          | ETTh2                                    |     |
|              |         |                                          | MAE                                      | MSE                                      | MAE                                      | MSE                                      | MAE                                      | MSE                                      | MAE                                      | MSE |
| Random       |         | $0.697 	 ±	 0.054$                       | $0.934 	 ±	 0.105$                       | $0.629 	 ±	 0.129$                       | $0.747 	 ±	 0.285$                       | $0.725 	 ±	 0.067$                       | $0.995 	 ±	 0.152$                       | $0.645 	 ±	 0.118$                       | $0.763 	 ±	 0.251$                       |     |
|              | X       | $0.665 	 ±	 0.012$                       | $0.837 	 ±	 0.024$                       | $0.575 	 ±	 0.015$                       | $0.574 	 ±	 0.030$                       | $0.713 	 ±	 0.024$                       | $0.933 	 ±	 0.049$                       | $0.591 	 ±	 0.069$                       | $0.619 	 ±	 0.132$                       |     |
| DC           | ✓       | $0.659 	 ±	 0.010$                       | $0.828 	 ±	 0.021$                       | $0.542 	 ±	 0.078$                       | $0.516 	 ±	 0.138$                       | $0.695 	 ±	 0.019$                       | $0.901 	 ±	 0.039$                       | $0.488 	 ±	 0.092$                       | $0.429 	 ±	 0.148$                       |     |
|              |         | 0.8%                                     | 1.1%                                     | 5.7%                                     | 10.0%                                    | 2.5%                                     | 3.5%                                     | 17.5%                                    | 30.8%                                    |     |
|              | X       | $0.486 	 ±	 0.016$                       | $0.478 	 ±	 0.021$                       | $0.326 	 ±	 0.013$                       | $0.183 	 ±	 0.013$                       | $0.639 	 ±	 0.020$                       | $0.748 	 ±	 0.040$                       | $0.564 	 ±	 0.116$                       | $0.551 	 ±	 0.172$                       |     |
| MTT          | ✓       | $0.470 	 ±	 0.003$                       | $0.470 	 ±	 0.005$                       | $0.273 	 ±	 0.010$                       | $0.133 	 ±	 0.007$                       | $0.453 	 ±	 0.009$                       | $0.422 	 ±	 0.016$                       | $0.324 	 ±	 0.003$                       | $0.197 	 ±	 0.003$                       |     |
|              | ↓       | 3.4%                                     | 1.6%                                     | 16.3%                                    | 27.2%                                    | 29.1%                                    | 43.6%                                    | 42.6%                                    | 64.2%                                    |     |
| PP           | X<br>✓  | $0.492 	 ±	 0.014$<br>$0.466 	 ±	 0.003$ | $0.485 	 ±	 0.023$<br>$0.470 	 ±	 0.005$ | $0.327 	 ±	 0.017$<br>$0.263 	 ±	 0.006$ | $0.185 	 ±	 0.016$<br>$0.127 	 ±	 0.004$ | $0.654 	 ±	 0.011$<br>$0.454 	 ±	 0.003$ | $0.765 	 ±	 0.024$<br>$0.421 	 ±	 0.006$ | $0.543 	 ±	 0.123$<br>$0.335 	 ±	 0.002$ | $0.517 	 ±	 0.193$<br>$0.209 	 ±	 0.003$ |     |
|              | ↓       | 5.3%                                     | 3.1%                                     | 19.5%                                    | 31.0%                                    | 30.5%                                    | 45.0%                                    | 38.4%                                    | 59.5%                                    |     |
|              | X       | $0.530 	 ±	 0.007$                       | $0.555 	 ±	 0.002$                       | $0.315 	 ±	 0.005$                       | $0.172 	 ±	 0.004$                       | $0.641 	 ±	 0.009$                       | $0.748 	 ±	 0.020$                       | $0.548 	 ±	 0.106$                       | $0.519 	 ±	 0.158$                       |     |
| <b>TESLA</b> | ✓       | $0.514 	 ±	 0.010$                       | $0.554 	 ±	 0.021$                       | $0.289 	 ±	 0.005$                       | $0.152 	 ±	 0.003$                       | $0.507 	 ±	 0.008$                       | $0.524 	 ±	 0.019$                       | $0.334 	 ±	 0.009$                       | $0.209 	 ±	 0.010$                       |     |
|              |         | 3.1%                                     | 0.3%                                     | 8.4%                                     | 11.8%                                    | 20.9%                                    | 30.0%                                    | 39.1%                                    | 59.7%                                    |     |
|              | X       | $0.490 	 ±	 0.006$                       | $0.476 	 ±	 0.010$                       | $0.330 	 ±	 0.017$                       | $0.186 	 ±	 0.017$                       | $0.633 	 ±	 0.011$                       | $0.730 	 ±	 0.021$                       | $0.611 	 ±	 0.038$                       | $0.622 	 ±	 0.064$                       |     |
| <b>FTD</b>   | ✓       | $0.463 	 ±	 0.005$                       | $0.466 	 ±	 0.003$                       | $0.264 	 ±	 0.007$                       | $0.128 	 ±	 0.005$                       | $0.427 	 ±	 0.003$                       | $0.379 	 ±	 0.006$                       | $0.313 	 ±	 0.004$                       | $0.186 	 ±	 0.003$                       |     |
|              |         | 5.4%                                     | 2.1%                                     | 19.9%                                    | 31.3%                                    | 32.6%                                    | 48.0%                                    | 48.8%                                    | 70.1%                                    |     |
|              | X       | $0.514 	 ±	 0.012$                       | $0.520 	 ±	 0.015$                       | $0.323 	 ±	 0.005$                       | $0.179 	 ±	 0.004$                       | $0.623 	 ±	 0.029$                       | $0.722 	 ±	 0.054$                       | $0.537 	 ±	 0.111$                       | $0.501 	 ±	 0.175$                       |     |
| <b>DATM</b>  | ✓       | $0.498 	 ±	 0.007$<br>$3.2%$             | $0.497 	 ±	 0.009$<br>$4.4%$             | $0.281 	 ±	 0.007$<br>$13.0%$            | $0.141 	 ±	 0.006$<br>$21.5%$            | $0.423 	 ±	 0.004$<br>$32.0%$            | $0.372 	 ±	 0.005$<br>$48.4%$            | $0.303 	 ±	 0.003$<br>$43.6%$            | $0.175 	 ±	 0.003$<br>$65.2%$            |     |
| Full         |         | $0.432 	 ±	 0.001$                       | $0.473 	 ±	 0.001$                       | $0.230 	 ±	 0.001$                       | $0.113 	 ±	 0.001$                       | $0.389 	 ±	 0.003$                       | $0.339 	 ±	 0.004$                       | $0.276 	 ±	 0.002$                       | $0.166 	 ±	 0.002$                       |     |

### B.2 3-times Standard Ratio Condensation

<span id="page-16-0"></span>We distill the dataset into a synthetic dataset with a flexible length for each dataset. Each synthetic dataset is 3 times larger than the synthetic data in Table[.4.](#page-15-1) The information on condensation is shown in Table[.6.](#page-16-0) The performance is shown in Table[.7.](#page-16-1)

Table 6: Information and condensation settings of time series datasets.

|                  | ETTm1&ETTm2 | ETTh1&ETTh2 | ExchangeRate | Weather | Electricity | Traffic |
|------------------|-------------|-------------|--------------|---------|-------------|---------|
| Dataset length   | 57600       | 14400       | 7588         | 52696   | 26304       | 17544   |
| Distill ratio    | 0.6%        | 1.2%        | 3%           | 0.6%    | 0.9%        | 1.2%    |
| Distilled length | 345         | 172         | 227          | 316     | 236         | 210     |

<span id="page-16-1"></span>Table 7: Distill performance of different dataset condensation methods. For each method, Xmeans CondTSF is not used, ✓means CondTSF is used, and ↓ means the decreased percentage of test error after CondTSF is applied. Five synthetic datasets are distilled and the average and standard deviation are reported.

|              | CondTSF |                   | <b>ExchangeRate</b> |                                       | Weather           |                                   | Electricity       |                                       | Traffic           |
|--------------|---------|-------------------|---------------------|---------------------------------------|-------------------|-----------------------------------|-------------------|---------------------------------------|-------------------|
|              |         | MAE               | <b>MSE</b>          | <b>MAE</b>                            | <b>MSE</b>        | <b>MAE</b>                        | MSE               | MAE                                   | <b>MSE</b>        |
| Random       |         | $0.852 \pm 0.081$ | $1.253 \pm 0.223$   | $0.447 + 0.067$                       | $0.471 \pm 0.105$ | $0.832 \pm 0.016$<br>$\mathbf{I}$ | $1.079 \pm 0.041$ | $0.840 \pm 0.021$<br>$\mathbb{I}$     | $1.320 \pm 0.052$ |
|              | Х       | $0.711 \pm 0.028$ | $0.864 \pm 0.063$   | $0.439 \pm 0.027$                     | $0.444 \pm 0.035$ | $0.827 \pm 0.008$                 | $1.068 \pm 0.018$ | $0.833 \pm 0.006$                     | $1.304 \pm 0.037$ |
| DC           | ✓       | $0.614 \pm 0.117$ | $0.658 \pm 0.224$   | $0.396 \pm 0.013$                     | $0.372 \pm 0.019$ | $0.804 \pm 0.003$                 | $1.012 \pm 0.009$ | $0.816 \pm 0.005$                     | $1.271 \pm 0.003$ |
|              |         | 13.6%             | 23.8%               | 9.8%                                  | 16.3%             | 2.8%                              | 5.2%              | 2.0%                                  | 2.6%              |
|              | Х       | $0.201 \pm 0.012$ | $0.066 \pm 0.008$   | $0.324 \pm 0.023$                     | $0.293 \pm 0.027$ | $0.332 \pm 0.004$                 | $0.242 \pm 0.003$ | $0.432 \pm 0.007$                     | $0.520 \pm 0.008$ |
| MTT          | ✓       | $0.175 \pm 0.006$ | $0.050 \pm 0.002$   | $0.274 \pm 0.013$                     | $0.255 \pm 0.007$ | $0.331 \pm 0.003$                 | $0.241 \pm 0.003$ | $0.422 \pm 0.006$                     | $0.505 \pm 0.003$ |
|              |         | 12.8%             | 23.7%               | 15.6%                                 | 12.7%             | 0.3%                              | 0.1%              | 2.3%                                  | 2.9%              |
|              | x       | $0.198 + 0.008$   | $0.064 \pm 0.005$   | $0.308 + 0.015$                       | $0.277 + 0.015$   | $0.333 \pm 0.004$                 | $0.242 \pm 0.003$ | $0.435 \pm 0.005$                     | $0.522 \pm 0.008$ |
| PP           |         | $0.176 \pm 0.003$ | $0.051 \pm 0.002$   | $0.274 \pm 0.006$                     | $0.259 + 0.002$   | $0.330 \pm 0.001$                 | $0.239 \pm 0.002$ | $0.429 \pm 0.004$                     | $0.512 \pm 0.005$ |
|              |         | 11.0%             | 19.9%               | 11.0%                                 | 6.5%              | 1.0%                              | 1.2%              | 1.4%                                  | 1.8%              |
|              | Х       | $0.209 \pm 0.016$ | $0.071 \pm 0.011$   | $0.297 \pm 0.005$                     | $0.265 \pm 0.003$ | $0.446 \pm 0.011$                 | $0.371 \pm 0.014$ | $0.593 \pm 0.011$                     | $0.734 \pm 0.023$ |
| <b>TESLA</b> | ✓       | $0.176 \pm 0.009$ | $0.051 \pm 0.005$   | $0.287 + 0.005$                       | $0.262 \pm 0.004$ | $0.413 \pm 0.007$                 | $0.336 \pm 0.009$ | $0.551 \pm 0.018$                     | $0.664 \pm 0.044$ |
|              |         | 15.6%             | 27.9%               | 3.6%                                  | 1.1%              | 7.3%                              | 9.3%              | 7.1%                                  | 9.5%              |
|              | Х       | $0.198 + 0.008$   | $0.064 \pm 0.005$   | $0.328 \pm 0.015$                     | $0.298 \pm 0.017$ | $0.333 \pm 0.006$                 | $0.243 \pm 0.004$ | $0.435 \pm 0.003$                     | $0.523 \pm 0.005$ |
| <b>FTD</b>   | ✓       | $0.172 \pm 0.004$ | $0.049 \pm 0.002$   | $0.281 \pm 0.007$                     | $0.258 \pm 0.004$ | $0.331 \pm 0.005$                 | $0.243 \pm 0.004$ | $0.421 \pm 0.003$                     | $0.501 \pm 0.005$ |
|              |         | 13.3%             | 23.0%               | 14.3%                                 | 13.4%             | 0.7%                              | 0.0%              | 3.3%                                  | 4.3%              |
|              | х       | $0.196 \pm 0.010$ | $0.062 \pm 0.005$   | $0.284 \pm 0.009$                     | $0.264 \pm 0.008$ | $0.335 \pm 0.006$                 | $0.244 \pm 0.005$ | $0.437 \pm 0.005$                     | $0.523 \pm 0.007$ |
| <b>DATM</b>  | ✓       | $0.173 \pm 0.007$ | $0.049 \pm 0.003$   | $0.275 \pm 0.005$                     | $0.251 \pm 0.001$ | $0.326 \pm 0.003$                 | $0.238 \pm 0.003$ | $0.416 \pm 0.005$                     | $0.497 \pm 0.003$ |
|              |         | 12.0%             | 21.3%               | 3.0%                                  | 4.8%              | 2.8%                              | 2.2%              | 4.6%                                  | 5.0%              |
| Full         | ÷       | $0.110 \pm 0.001$ |                     | $0.023 \pm 0.000$   $0.197 \pm 0.001$ | $0.131 \pm 0.001$ | $0.312 \pm 0.002$                 |                   | $0.223 \pm 0.002$   $0.406 \pm 0.003$ | $0.492 \pm 0.004$ |
|              |         |                   |                     |                                       |                   |                                   |                   |                                       |                   |
|              |         |                   | ETTm1               |                                       | ETTm2             |                                   | ETTh1             |                                       | ETTh <sub>2</sub> |
|              | CondTSF | <b>MAE</b>        | <b>MSE</b>          | <b>MAE</b>                            | <b>MSE</b>        | <b>MAE</b>                        | <b>MSE</b>        | <b>MAE</b>                            | <b>MSE</b>        |
| Random       | ٠       | $0.693 \pm 0.041$ | $0.913 \pm 0.095$   | $0.629 \pm 0.065$                     | $0.724 \pm 0.155$ | H<br>$0.742 \pm 0.055$            | $1.027 \pm 0.129$ | $\parallel 0.691 \pm 0.140$           | $0.887 \pm 0.294$ |
|              | х       | $0.603 \pm 0.045$ | $0.730 \pm 0.075$   | $0.490 \pm 0.018$                     | $0.410 \pm 0.032$ | $0.724 \pm 0.007$                 | $0.977 + 0.022$   | $0.634 \pm 0.054$                     | $0.711 \pm 0.115$ |
| DC           | ✓       | $0.590 \pm 0.009$ | $0.713 \pm 0.025$   | $0.417 + 0.093$                       | $0.312 \pm 0.116$ | $0.704 \pm 0.002$                 | $0.915 \pm 0.006$ | $0.566 \pm 0.008$                     | $0.562 \pm 0.018$ |
|              |         | 2.2%              | 2.4%                | 14.8%                                 | 24.0%             | 2.8%                              | 6.3%              | 10.7%                                 | 21.0%             |
|              | Х       | $0.520 \pm 0.022$ | $0.522 \pm 0.035$   | $0.285 \pm 0.010$                     | $0.143 \pm 0.008$ | $0.480 \pm 0.009$                 | $0.467 \pm 0.017$ | $0.329 \pm 0.009$                     | $0.199 \pm 0.008$ |
| <b>MTT</b>   | ✓       | $0.462 \pm 0.006$ | $0.476 \pm 0.012$   | $0.265 \pm 0.009$                     | $0.130 \pm 0.007$ | $0.428 + 0.009$                   | $0.383 \pm 0.012$ | $0.303 \pm 0.007$                     | $0.177 + 0.008$   |
|              |         | 11.1%             | 8.8%                | 6.9%                                  | 9.1%              | 10.9%                             | 18.0%             | 7.8%                                  | 11.0%             |
|              | Х       | $0.538 \pm 0.041$ | $0.558 \pm 0.062$   | $0.285 \pm 0.008$                     | $0.144 \pm 0.007$ | $0.477 + 0.006$                   | $0.462 \pm 0.012$ | $0.330 \pm 0.004$                     | $0.201 \pm 0.004$ |
| PP           | ✓       | $0.466 \pm 0.006$ | $0.485 \pm 0.022$   | $0.271 \pm 0.009$                     | $0.135 \pm 0.008$ | $0.442 \pm 0.016$                 | $0.405 \pm 0.029$ | $0.323 \pm 0.004$                     | $0.198 \pm 0.005$ |
|              |         | 13.3%             | 13.2%               | 5.2%                                  | 5.7%              | 7.3%                              | 12.3%             | 2.4%                                  | 1.1%              |
|              | Х       | $0.519 \pm 0.014$ | $0.558 \pm 0.050$   | $0.295 \pm 0.005$                     | $0.155 \pm 0.004$ | $0.542 \pm 0.015$                 | $0.603 \pm 0.037$ | $0.339 \pm 0.006$                     | $0.213 \pm 0.005$ |
| <b>TESLA</b> | ✓       | $0.480 \pm 0.023$ | $0.507 \pm 0.061$   | $0.288 \pm 0.004$                     | $0.152 \pm 0.004$ | $0.480 \pm 0.010$                 | $0.471 \pm 0.020$ | $0.327 \pm 0.005$                     | $0.205 \pm 0.004$ |
|              |         | 7.5%              | 8.2%                | 2.5%                                  | 1.7%              | 11.5%                             | 21.9%             | 3.3%                                  | 3.8%              |
|              | Х       | $0.531 \pm 0.023$ | $0.539 \pm 0.036$   | $0.293 \pm 0.016$                     | $0.150 \pm 0.014$ | $0.480 \pm 0.009$                 | $0.464 \pm 0.018$ | $0.327 \pm 0.010$                     | $0.197 \pm 0.009$ |
| <b>FTD</b>   | ✓       | $0.469 + 0.005$   | $0.493 \pm 0.018$   | $0.264 \pm 0.003$                     | $0.130 \pm 0.002$ | $0.440 \pm 0.006$                 | $0.400 \pm 0.012$ | $0.308 + 0.008$                       | $0.181 \pm 0.009$ |
|              |         | 11.7%             | 8.5%                | 9.7%                                  | 13.3%             | 8.3%                              | 13.7%             | 5.8%                                  | 7.8%              |
|              | Х       | $0.497 \pm 0.013$ | $0.513 \pm 0.011$   | $0.285 \pm 0.006$                     | $0.144 \pm 0.005$ | $0.480 \pm 0.012$                 | $0.464 \pm 0.026$ | $0.327 \pm 0.005$                     | $0.196 \pm 0.005$ |
| <b>DATM</b>  | ✓       | $0.493 \pm 0.006$ | $0.495 \pm 0.009$   | $0.268 \pm 0.008$                     | $0.131 \pm 0.007$ | $0.429 \pm 0.033$                 | $0.385 \pm 0.053$ | $0.299 \pm 0.007$                     | $0.172 \pm 0.007$ |
|              |         | 0.9%              | 3.4%                | 5.7%                                  | 8.6%              | 10.6%                             | 17.1%             | 8.7%                                  | 12.1%             |

We observe that CondTSF consistently improves the performance of backbone models with all condensing ratios, suggesting the effectiveness of CondTSF with different condensing ratios.

# C Performance of CondTSF with Non-parameter-matching Based Methods

We distill the dataset using the standard condensing ratio. The information on condensation is shown in Table[.4.](#page-15-1) We conduct experiments on CondTSF with non-parameter-matching based methods. We use DM[\[50\]](#page-12-1), IDM[\[52\]](#page-12-2), KIP[\[33\]](#page-11-7), FRePo[\[55\]](#page-12-3) as backbone methods. The performance is shown in Table[.8.](#page-17-0)

Results show that using CondTSF to optimize only one of the two optimizable terms can also boost the performance.

<span id="page-17-0"></span>Table 8: Distill performance of different dataset condensation methods. For each method, Xmeans CondTSF is not used, ✓means CondTSF is used, and ↓ means the decreased percentage of test error after CondTSF is applied. Five synthetic datasets are distilled and the average and standard deviation are reported.

|            | CondTSF |                                                   | <b>ExchangeRate</b>                             |                                                 | Weather                                         |                                                 | Electricity                                     |                                                 | <b>Traffic</b>                                  |
|------------|---------|---------------------------------------------------|-------------------------------------------------|-------------------------------------------------|-------------------------------------------------|-------------------------------------------------|-------------------------------------------------|-------------------------------------------------|-------------------------------------------------|
|            |         | <b>MAE</b>                                        | <b>MSE</b>                                      | <b>MAE</b>                                      | <b>MSE</b>                                      | <b>MAE</b>                                      | <b>MSE</b>                                      | <b>MAE</b>                                      | <b>MSE</b>                                      |
| Random     | ٠       | $0.730 \pm 0.168$                                 | $0.957 \pm 0.397$                               | $0.566 \pm 0.056$                               | $0.708 \pm 0.135$                               | $0.832 \pm 0.024$                               | $1.080 \pm 0.058$                               | $\mathbb{I}$<br>$0.845 \pm 0.007$               | $1.343 \pm 0.017$                               |
| DM         | Х<br>✓  | $0.772 \pm 0.016$<br>$0.697 \pm 0.030$<br>9.6%    | $0.990 \pm 0.061$<br>$0.832 \pm 0.072$<br>16.0% | $0.483 \pm 0.063$<br>$0.477 + 0.047$<br>1.2%    | $0.540 \pm 0.128$<br>$0.513 \pm 0.082$<br>5.0%  | $0.818 \pm 0.011$<br>$0.817 \pm 0.011$<br>0.1%  | $1.048 \pm 0.034$<br>$1.043 \pm 0.030$<br>0.4%  | $0.830 \pm 0.016$<br>$0.812 \pm 0.013$<br>2.3%  | $1.299 \pm 0.048$<br>$1.253 \pm 0.035$<br>3.5%  |
| <b>IDM</b> | х       | $0.708 \pm 0.107$<br>$0.683 \pm 0.120$<br>3.5%    | $0.871 \pm 0.257$<br>$0.805 \pm 0.247$<br>7.5%  | $0.517 + 0.052$<br>$0.504 \pm 0.055$<br>2.4%    | $0.594 \pm 0.105$<br>$0.570 \pm 0.116$<br>4.0%  | $0.836 \pm 0.012$<br>$0.819 \pm 0.023$<br>2.0%  | $1.087 + 0.032$<br>$1.050 \pm 0.055$<br>3.4%    | $0.823 \pm 0.022$<br>$0.804 \pm 0.020$<br>2.4%  | $1.287 \pm 0.055$<br>$1.231 \pm 0.055$<br>4.4%  |
| <b>KIP</b> | х<br>✓  | $0.538 + 0.026$<br>$0.217 + 0.009$<br>59.6%       | $0.467 \pm 0.032$<br>$0.079 \pm 0.007$<br>83.2% | $0.316 \pm 0.016$<br>$0.313 \pm 0.007$<br>1.0%  | $0.297 \pm 0.008$<br>$0.297 \pm 0.003$<br>0.0%  | $0.817 \pm 0.010$<br>$0.812 \pm 0.021$<br>0.6%  | $1.040 \pm 0.032$<br>$1.037 \pm 0.044$<br>0.3%  | $0.834 \pm 0.006$<br>$0.830 \pm 0.011$<br>0.4%  | $1.314 \pm 0.027$<br>$1.278 \pm 0.034$<br>2.8%  |
| FRePo      | Х       | $0.518 \pm 0.030$<br>$0.270 \pm 0.021$<br>47.8%   | $0.471 \pm 0.045$<br>$0.122 \pm 0.021$<br>74.1% | $0.424 \pm 0.023$<br>$0.330 \pm 0.031$<br>22.1% | $0.403 \pm 0.033$<br>$0.288 \pm 0.031$<br>28.4% | $0.590 \pm 0.023$<br>$0.464 \pm 0.011$<br>21.2% | $0.554 \pm 0.037$<br>$0.373 \pm 0.010$<br>32.6% | $0.615 \pm 0.015$<br>$0.518 \pm 0.011$<br>15.8% | $0.789 \pm 0.037$<br>$0.601 \pm 0.021$<br>23.8% |
| Full       |         | $0.110 \pm 0.001$                                 | $0.023 \pm 0.000$   $0.197 \pm 0.001$           |                                                 | $0.131 \pm 0.001$                               | $0.312 \pm 0.002$                               |                                                 | $0.223 \pm 0.002$   $0.406 \pm 0.003$           | $0.492 \pm 0.004$                               |
|            | CondTSF |                                                   | ETTm1                                           |                                                 | ETTm2                                           |                                                 | ETTh1                                           |                                                 | ETTh <sub>2</sub>                               |
|            |         | <b>MAE</b>                                        | <b>MSE</b>                                      | <b>MAE</b>                                      | <b>MSE</b>                                      | <b>MAE</b>                                      | <b>MSE</b>                                      | <b>MAE</b>                                      | <b>MSE</b>                                      |
| Random     | ٠       | $0.697 \pm 0.054$                                 | $0.934 \pm 0.105$                               | $0.629 \pm 0.129$                               | $0.747 \pm 0.285$                               | $0.725 \pm 0.067$                               | $0.995 \pm 0.152$                               | $0.645 \pm 0.118$                               | $0.763 \pm 0.251$                               |
| DM         | Х<br>✓  | $0.684 \pm 0.063$<br>$0.651 \pm 0.041$<br>4.8%    | $0.903 \pm 0.129$<br>$0.826 \pm 0.089$<br>8.5%  | $0.641 \pm 0.129$<br>$0.614 \pm 0.130$<br>4.3%  | $0.782 \pm 0.254$<br>$0.706 \pm 0.322$<br>9.7%  | $0.722 \pm 0.040$<br>$0.713 \pm 0.035$<br>1.2%  | $0.977 \pm 0.094$<br>$0.950 \pm 0.077$<br>2.8%  | $0.703 \pm 0.079$<br>$0.615 \pm 0.133$<br>12.5% | $0.895 \pm 0.189$<br>$0.706 \pm 0.267$<br>21.1% |
| <b>IDM</b> | Х       | $0.657 \pm 0.047$<br>$0.648 \pm 0.025$<br>1.4%    | $0.841 \pm 0.094$<br>$0.816 \pm 0.040$<br>3.0%  | $0.648 \pm 0.155$<br>$0.610 \pm 0.131$<br>5.8%  | $0.811 \pm 0.297$<br>$0.698 \pm 0.255$<br>13.9% | $0.713 \pm 0.055$<br>$0.694 \pm 0.039$<br>2.6%  | $0.956 \pm 0.124$<br>$0.912 \pm 0.080$<br>4.7%  | $0.667 \pm 0.121$<br>$0.573 + 0.161$<br>14.1%   | $0.823 \pm 0.252$<br>$0.632 \pm 0.314$<br>23.2% |
| <b>KIP</b> | х<br>✓  | $0.581 \pm 0.002$<br>$0.581 \pm 0.001$<br>$0.0\%$ | $0.736 \pm 0.012$<br>$0.723 \pm 0.014$<br>1.8%  | $0.316 \pm 0.002$<br>$0.290 \pm 0.002$<br>8.0%  | $0.171 \pm 0.002$<br>$0.151 \pm 0.002$<br>11.7% | $0.685 \pm 0.021$<br>$0.602 \pm 0.036$<br>12.1% | $0.861 \pm 0.028$<br>$0.709 \pm 0.082$<br>17.6% | $0.576 \pm 0.114$<br>$0.400 \pm 0.054$<br>30.6% | $0.575 \pm 0.198$<br>$0.282 \pm 0.061$<br>51.0% |
|            |         |                                                   |                                                 |                                                 |                                                 |                                                 |                                                 |                                                 |                                                 |
| FRePo      | х<br>✓  | $0.596 \pm 0.015$<br>$0.551 \pm 0.011$<br>7.6%    | $0.670 \pm 0.040$<br>$0.581 \pm 0.016$<br>13.2% | $0.572 \pm 0.023$<br>$0.424 \pm 0.024$<br>25.8% | $0.556 \pm 0.053$<br>$0.303 \pm 0.039$<br>45.5% | $0.640 \pm 0.014$<br>$0.566 \pm 0.005$<br>11.6% | $0.759 \pm 0.024$<br>$0.617 \pm 0.008$<br>18.7% | $0.549 \pm 0.077$<br>$0.430 \pm 0.064$<br>21.6% | $0.528 \pm 0.131$<br>$0.325 \pm 0.079$<br>38.4% |

# <span id="page-18-0"></span>D Performance of CondTSF with Non-Linear Expert Models

We distill the dataset using the standard condensing ratio. The information on condensation is shown in Table[.4.](#page-15-1) We conduct experiments on distilling dataset with non-linear expert models. We use MTT[\[3\]](#page-9-3), TESLA[\[5\]](#page-9-5), and DATM[\[11\]](#page-9-7) as backbone methods. The performance of using a CNN as the expert model is shown in Table[.9,](#page-18-1) and the performance of using a 3-layer-MLP as the expert model is shown in Table[.10.](#page-18-2)

Results show that CondTSF is also effective with non-linear expert models.

<span id="page-18-1"></span>Table 9: Distill performance of different dataset condensation methods with CNN as the expert model. For each method, **X**means CondTSF is not used, **V** means CondTSF is used, and  $\downarrow$  means the decreased percentage of test error after CondTSF is applied. Five synthetic datasets are distilled and the average and standard deviation are reported.

|              | CondTSF |                                                | <b>ExchangeRate</b>                             |                                                 | Weather                                         |                                                 | Electricity                                   |                                               | <b>Traffic</b>                                  |
|--------------|---------|------------------------------------------------|-------------------------------------------------|-------------------------------------------------|-------------------------------------------------|-------------------------------------------------|-----------------------------------------------|-----------------------------------------------|-------------------------------------------------|
|              |         | <b>MAE</b>                                     | <b>MSE</b>                                      | <b>MAE</b>                                      | <b>MSE</b>                                      | <b>MAE</b>                                      | <b>MSE</b>                                    | <b>MAE</b>                                    | <b>MSE</b>                                      |
| Random       |         | $0.830 \pm 0.059$                              |                                                 | $1.002 \pm 0.127$   0.504 $\pm$ 0.018           | $0.526 \pm 0.030$                               | $0.762 \pm 0.016$                               | $0.882 \pm 0.034$                             | $0.732 \pm 0.023$                             | $1.041 \pm 0.053$                               |
| <b>MTT</b>   | х       | $0.372 \pm 0.028$<br>$0.140 + 0.011$           | $0.237 + 0.031$<br>$0.063 + 0.003$              | $0.314 + 0.013$<br>$0.246 + 0.008$              | $0.278 \pm 0.007$<br>$0.120 + 0.004$            | $0.482 \pm 0.009$<br>$0.357 + 0.005$            | $0.393 \pm 0.012$<br>$0.267 + 0.005$          | $0.662 + 0.011$<br>$0.451 + 0.013$            | $0.906 + 0.026$<br>$0.519 + 0.009$              |
|              |         | 62.4%                                          | 73.4%                                           | 21.7%                                           | 56.8%                                           | 25.9%                                           | 32.1%                                         | 31.9%                                         | 42.7%                                           |
| <b>TESLA</b> | х       | $0.378 + 0.007$<br>$0.134 + 0.012$<br>64.6%    | $0.242 \pm 0.007$<br>$0.058 + 0.002$<br>76.0%   | $0.310 + 0.015$<br>$0.253 \pm 0.007$<br>18.4%   | $0.292 + 0.012$<br>$0.137 + 0.002$<br>53.1%     | $0.516 + 0.005$<br>$0.374 \pm 0.008$<br>27.5%   | $0.430 + 0.009$<br>$0.267 + 0.007$<br>37.9%   | $0.655 + 0.020$<br>$0.528 + 0.011$<br>19.4%   | $0.900 + 0.045$<br>$0.632 + 0.020$<br>29.8%     |
| <b>DATM</b>  | Х       | $0.331 \pm 0.011$<br>$0.137 + 0.017$<br>58.6%  | $0.179 \pm 0.013$<br>$0.059 + 0.004$<br>67.0%   | $0.335 \pm 0.009$<br>$0.291 + 0.005$<br>13.1%   | $0.294 \pm 0.008$<br>$0.261 + 0.004$<br>11.2%   | $0.504 \pm 0.008$<br>$0.355 + 0.006$<br>29.6%   | $0.432 \pm 0.009$<br>$0.252 + 0.006$<br>41.7% | $0.587 \pm 0.009$<br>$0.452 + 0.009$<br>23.0% | $0.742 \pm 0.014$<br>$0.526 + 0.011$<br>29.1%   |
|              | CondTSF |                                                | ETTm1                                           |                                                 | ETTm2                                           |                                                 | ETTh1                                         | ETTh <sub>2</sub>                             |                                                 |
|              |         | <b>MAE</b>                                     | <b>MSE</b>                                      | <b>MAE</b>                                      | <b>MSE</b>                                      | <b>MAE</b>                                      | <b>MSE</b>                                    | <b>MAE</b>                                    | <b>MSE</b>                                      |
| Random       |         | $0.691 \pm 0.012$                              | $0.856 \pm 0.030$    $0.723 \pm 0.027$          |                                                 | $0.846 \pm 0.052$                               | $0.727 \pm 0.015$                               | $0.931 \pm 0.030$                             | $0.722 \pm 0.015$                             | $0.847 \pm 0.033$                               |
| <b>MTT</b>   | х       | $0.550 \pm 0.006$<br>$0.482 + 0.007$<br>12.4%  | $0.585 \pm 0.011$<br>$0.507 + 0.007$<br>13.3%   | $0.347 \pm 0.008$<br>$0.236 \pm 0.009$<br>32.0% | $0.205 \pm 0.009$<br>$0.111 \pm 0.005$<br>45.9% | $0.644 \pm 0.013$<br>$0.460 \pm 0.008$<br>28.6% | $0.788 \pm 0.036$<br>$0.437 + 0.014$<br>44.5% | $0.371 + 0.016$<br>$0.297 + 0.011$<br>19.9%   | $0.245 \pm 0.020$<br>$0.173 \pm 0.009$<br>29.4% |
| <b>TESLA</b> | х       | $0.544 + 0.003$<br>$0.499 + 0.003$<br>8.3%     | $0.583 \pm 0.007$<br>$0.492 \pm 0.007$<br>15.6% | $0.359 + 0.013$<br>$0.251 \pm 0.009$<br>30.1%   | $0.222 \pm 0.016$<br>$0.119 + 0.005$<br>46.4%   | $0.634 + 0.010$<br>$0.473 + 0.009$<br>25.4%     | $0.757 + 0.037$<br>$0.458 \pm 0.018$<br>39.5% | $0.365 + 0.008$<br>$0.293 + 0.004$<br>19.7%   | $0.240 + 0.009$<br>$0.170 + 0.002$<br>29.2%     |
| <b>DATM</b>  | х       | $0.566 \pm 0.008$<br>$0.518 \pm 0.007$<br>8.5% | $0.598 + 0.011$<br>$0.521 \pm 0.012$<br>12.9%   | $0.318 + 0.013$<br>$0.231 \pm 0.002$<br>27.4%   | $0.174 + 0.014$<br>$0.107 \pm 0.001$<br>38.5%   | $0.633 + 0.009$<br>$0.451 \pm 0.005$<br>28.8%   | $0.773 + 0.025$<br>$0.418 \pm 0.007$<br>45.9% | $0.349 + 0.012$<br>$0.290 \pm 0.007$<br>16.9% | $0.221 + 0.012$<br>$0.165 \pm 0.005$<br>25.3%   |

<span id="page-18-2"></span>Table 10: Distill performance of different dataset condensation methods with 3-layer-MLP as the expert model. For each method, ✗means CondTSF is not used, ✓means CondTSF is used, and ↓ means the decreased percentage of test error after CondTSF is applied. Five synthetic datasets are distilled and the average and standard deviation are reported.

|              | CondTSF |                                      | <b>ExchangeRate</b>                  |                                      | Weather                              |                                      | Electricity                          |                                        | <b>Traffic</b>                       |
|--------------|---------|--------------------------------------|--------------------------------------|--------------------------------------|--------------------------------------|--------------------------------------|--------------------------------------|----------------------------------------|--------------------------------------|
|              |         | <b>MAE</b>                           | <b>MSE</b>                           | <b>MAE</b>                           | <b>MSE</b>                           | <b>MAE</b>                           | <b>MSE</b>                           | <b>MAE</b>                             | <b>MSE</b>                           |
| Random       |         | $0.932 \pm 0.058$                    | $1.243 \pm 0.144$                    | $0.562 \pm 0.013$                    | $0.642 \pm 0.030$                    | $0.796 \pm 0.013$                    |                                      | $0.955 \pm 0.036$    $0.751 \pm 0.012$ | $1.112 \pm 0.025$                    |
|              | х       | $0.364 \pm 0.027$                    | $0.211 \pm 0.030$                    | $0.311 \pm 0.007$                    | $0.297 \pm 0.009$                    | $0.475 \pm 0.011$                    | $0.380 \pm 0.016$                    | $0.633 \pm 0.011$                      | $0.841 \pm 0.021$                    |
| <b>MTT</b>   |         | $0.139 \pm 0.004$<br>61.8%           | $0.034 \pm 0.002$<br>83.9%           | $0.248 \pm 0.015$<br>20.3%           | $0.135 \pm 0.006$<br>54.5%           | $0.375 \pm 0.007$<br>21.1%           | $0.268 + 0.008$<br>29.5%             | $0.501 \pm 0.008$<br>20.9%             | $0.589 \pm 0.016$<br>30.0%           |
| <b>TESLA</b> | х       | $0.352 + 0.012$<br>$0.128 \pm 0.017$ | $0.209 + 0.010$<br>$0.027 \pm 0.007$ | $0.297 + 0.001$<br>$0.252 \pm 0.006$ | $0.272 + 0.003$<br>$0.135 \pm 0.001$ | $0.525 + 0.004$<br>$0.397 \pm 0.012$ | $0.462 + 0.008$<br>$0.297 \pm 0.014$ | $0.594 + 0.009$<br>$0.488 \pm 0.006$   | $0.751 + 0.022$<br>$0.577 \pm 0.009$ |
|              |         | 63.6%                                | 87.1%                                | 15.2%                                | 50.4%                                | 24.4%                                | 35.7%                                | 17.8%                                  | 23.2%                                |
|              | х       | $0.326 \pm 0.016$                    | $0.177 \pm 0.016$                    | $0.349 \pm 0.002$                    | $0.301 \pm 0.001$                    | $0.517 \pm 0.008$                    | $0.441 \pm 0.011$                    | $0.622 + 0.007$                        | $0.785 \pm 0.005$                    |
| <b>DATM</b>  |         | $0.141 \pm 0.005$<br>56.7%           | $0.042 \pm 0.001$<br>76.3%           | $0.254 \pm 0.010$<br>27.2%           | $0.126 \pm 0.004$<br>58.1%           | $0.385 \pm 0.009$<br>25.5%           | $0.280 \pm 0.008$<br>36.5%           | $0.496 \pm 0.006$<br>20.3%             | $0.582 \pm 0.011$<br>25.9%           |
|              |         |                                      |                                      |                                      |                                      |                                      |                                      |                                        |                                      |
|              |         | ETTm1                                |                                      |                                      | ETTm2                                |                                      | ETTh1                                |                                        | ETTh <sub>2</sub>                    |
|              | CondTSF | <b>MAE</b>                           | MSE                                  | <b>MAE</b>                           | MSE                                  | <b>MAE</b>                           | <b>MSE</b>                           | <b>MAE</b>                             | MSE                                  |
| Random       | ٠       | $0.692 \pm 0.013$                    | $0.855 \pm 0.027$                    | $\parallel 0.762 \pm 0.012$          | $0.955 \pm 0.037$                    | $0.687 \pm 0.010$                    |                                      | $0.838 \pm 0.022$   0.763 $\pm 0.019$  | $0.945 \pm 0.050$                    |
|              | х       | $0.564 \pm 0.012$                    | $0.655 \pm 0.022$                    | $0.362 \pm 0.011$                    | $0.219 \pm 0.012$                    | $0.615 \pm 0.002$                    | $0.732 \pm 0.015$                    | $0.354 + 0.011$                        | $0.228 \pm 0.012$                    |
| <b>MTT</b>   |         | $0.493 \pm 0.007$<br>12.6%           | $0.511 \pm 0.007$<br>22.0%           | $0.245 \pm 0.022$<br>32.3%           | $0.098 \pm 0.015$<br>55.3%           | $0.423 \pm 0.006$<br>31.2%           | $0.369 \pm 0.008$<br>49.6%           | $0.285 \pm 0.009$<br>19.5%             | $0.145 \pm 0.005$<br>36.4%           |
|              | х       | $0.541 \pm 0.002$                    | $0.570 \pm 0.004$                    | $0.341 \pm 0.014$                    | $0.197 \pm 0.013$                    | $0.606 + 0.007$                      | $0.745 \pm 0.022$                    | $0.337 + 0.008$                        | $0.210 + 0.006$                      |
| <b>TESLA</b> |         | $0.487 + 0.003$<br>10.0%             | $0.493 + 0.004$<br>13.5%             | $0.250 + 0.009$<br>26.7%             | $0.119 + 0.005$<br>39.6%             | $0.438 + 0.011$<br>27.7%             | $0.398 + 0.013$<br>46.6%             | $0.283 + 0.007$<br>16.0%               | $0.145 + 0.006$<br>31.0%             |
|              | x       | $0.558 \pm 0.009$                    | $0.611 \pm 0.021$                    | $0.329 \pm 0.006$                    | $0.186 \pm 0.005$                    | $0.593 + 0.022$                      | $0.741 \pm 0.063$                    | $0.350 + 0.009$                        | $0.222 \pm 0.008$                    |
| <b>DATM</b>  |         | $0.498 \pm 0.006$<br>10.8%           | $0.482 \pm 0.006$<br>21.1%           | $0.234 \pm 0.005$<br>28.9%           | $0.107 \pm 0.001$<br>42.5%           | $0.437 \pm 0.006$<br>26.3%           | $0.397 \pm 0.005$<br>46.4%           | $0.286 \pm 0.005$<br>18.3%             | $0.167 \pm 0.004$<br>24.8%           |

# <span id="page-19-0"></span>E Performance Comparison of CondTSF and Smoothing

We distill the dataset using the one-shot condensing ratio. The information on condensation is shown in Table[.2.](#page-7-0) We conduct experiments on distilling dataset with CondTSF and a low pass filter respectively. We use DLinear[\[45\]](#page-11-12) as the expert model. The result is shown in Table[.11.](#page-19-1)

Results indicate that using CondTSF is significantly more effective than using a low pass filter to smooth the distilled data.

|               |                             | <b>ExchangeRate</b>                   |                                       | Weather                               |                   | Electricity                           |                   | <b>Traffic</b>    |
|---------------|-----------------------------|---------------------------------------|---------------------------------------|---------------------------------------|-------------------|---------------------------------------|-------------------|-------------------|
|               | MAE                         | <b>MSE</b>                            | MAE                                   | <b>MSE</b>                            | MAE               | <b>MSE</b>                            | MAE               | <b>MSE</b>        |
| Random        | $\parallel 0.783 \pm 0.090$ |                                       | $1.070 \pm 0.246$   $0.530 \pm 0.084$ | $0.647 \pm 0.159$   $0.840 \pm 0.017$ |                   | $1.102 \pm 0.031$   0.854 $\pm$ 0.018 |                   | $1.350 \pm 0.043$ |
| <b>MTT</b>    | $0.778 \pm 0.084$           | $0.964 \pm 0.136$                     | $0.509 \pm 0.065$                     | $0.538 \pm 0.085$                     | $0.747 \pm 0.012$ | $0.840 + 0.019$                       | $0.742 \pm 0.010$ | $1.052 + 0.024$   |
| <b>Smooth</b> | $0.867 + 0.106$             | $1.358 \pm 0.321$                     | $0.602 + 0.018$                       | $0.772 \pm 0.032$                     | $0.831 \pm 0.042$ | $1.068 + 0.109$                       | $0.786 \pm 0.037$ | $1.208 + 0.101$   |
| MTT+Smooth    | $0.620 + 0.024$             | $0.636 \pm 0.062$                     | $0.501 \pm 0.012$                     | $0.527 \pm 0.028$                     | $0.788 \pm 0.027$ | $0.963 \pm 0.061$                     | $0.836 \pm 0.022$ | $1.291 \pm 0.057$ |
| MTT+CondTSF   | $0.195 \!\pm\! 0.007$       | $0.061 \pm 0.004$                     | $0.326 \pm 0.009$                     | $0.284 \pm 0.007$                     | $0.391 \pm 0.003$ | $0.284 \pm 0.004$                     | $0.494 \pm 0.022$ | $0.579 \pm 0.037$ |
|               |                             |                                       |                                       |                                       |                   |                                       |                   |                   |
|               |                             | ETTm1                                 |                                       | ETT <sub>m2</sub>                     | ETTh1             |                                       |                   | ETTh <sub>2</sub> |
|               | <b>MAE</b>                  | <b>MSE</b>                            | <b>MAE</b>                            | <b>MSE</b>                            | <b>MAE</b>        | <b>MSE</b>                            | <b>MAE</b>        | <b>MSE</b>        |
| Random        | $\parallel 0.728 \pm 0.033$ | $0.993 \pm 0.082$   $0.695 \pm 0.011$ |                                       | $0.889 \pm 0.030$   $0.756 \pm 0.035$ |                   | $1.059 \pm 0.083$   0.749 $\pm$ 0.037 |                   | $1.013 \pm 0.089$ |
| <b>MTT</b>    | $0.653 \pm 0.019$           | $0.771 \pm 0.040$                     | $0.685 \pm 0.022$                     | $0.754 \pm 0.051$                     | $0.693 \pm 0.009$ | $0.845 \pm 0.023$                     | $0.719 \pm 0.006$ | $0.827 + 0.016$   |
| Smooth        | $0.728 \pm 0.013$           | $0.991 + 0.038$                       | $0.706 \pm 0.053$                     | $0.910 + 0.136$                       | $0.701 \pm 0.007$ | $0.879 + 0.026$                       | $0.744 \pm 0.067$ | $1.023 + 0.145$   |
| MTT+Smooth    | $0.656 + 0.014$             | $0.919 + 0.040$                       | $0.619 \pm 0.029$                     | $0.684 + 0.062$                       | $0.680 \pm 0.012$ | $0.874 + 0.044$                       | $0.704 + 0.053$   | $0.910 + 0.136$   |

<span id="page-19-1"></span>Table 11: Distill performance of different dataset condensation methods. Five synthetic datasets are distilled and the average and standard deviation are reported.

# F Ablation Study of CondTSF

We compare the changing trajectories of test error during the dataset condensation process. Since MTT has been proven to be a suitable backbone for CondTSF, we conduct experiments on different methods of plugging CondTSF into MTT. We utilize the standard condensing ratio as shown in Table[.4.](#page-15-1)

Test error is calculated as such. After the synthetic data has been distilled, it is used to train 5 randomly initialized testing models. After training with the synthetic dataset, the models are tested on the test set sampled from the source dataset. MAE error is reported in the figures below.

# F.1 Performance of CondTSF with Different Gap

Image /page/20/Figure/4 description: This image displays eight line graphs, arranged in a 2x4 grid, each illustrating the test error over condensation epochs for different datasets. The datasets are labeled (a) ExchangeRate, (b) Weather, (c) Electricity, (d) Traffic, (e) ETTm1, (f) ETTm2, (g) ETTh1, and (h) ETTh2. Each graph has 'Condensation Epochs' on the x-axis, ranging from 0 to 200, and 'Test Error' on the y-axis, with scales varying slightly between graphs but generally ranging from 0.2 to 0.9. Two lines are plotted in each graph: one labeled 'MTT' (in yellow) and another labeled 'MTT 1 epoch + CondTSF 1 epoch' (in red). In most graphs, the red line shows a significantly lower test error than the yellow line, especially after around 40-80 epochs, indicating better performance with the 'MTT 1 epoch + CondTSF 1 epoch' method.

Figure 5: Yellow: Use MTT to distill for 200 epochs. Orange: Use MTT to distill for 200 epochs and use CondTSF to update in every epoch.

Image /page/20/Figure/6 description: This image displays a grid of eight line graphs, each comparing the test error of two methods, MTT and MTT 3 epochs + CondTSF 1 epoch, across condensation epochs. The graphs are labeled (a) ExchangeRate, (b) Weather, (c) Electricity, (d) Traffic, (e) ETTm1, (f) ETTm2, (g) ETTh1, and (h) ETTh2. The x-axis for all graphs represents Condensation Epochs ranging from 0 to 200. The y-axis represents Test Error, with scales varying slightly between graphs but generally ranging from 0.2 to 1.0. In most graphs, the MTT 3 epochs + CondTSF 1 epoch line shows a steeper initial decrease in test error compared to the MTT line, which tends to plateau at a higher error rate. Specifically, for ExchangeRate, Weather, Electricity, Traffic, ETTm2, and ETTh2, the MTT 3 epochs + CondTSF 1 epoch method achieves significantly lower test errors by the end of the condensation epochs. For ETTm1 and ETTh1, both methods show a decrease in test error, but MTT 3 epochs + CondTSF 1 epoch still performs better, reaching lower error values.

Figure 6: Yellow: Use MTT to distill for 200 epochs. Orange: Use MTT to distill for 200 epochs and use CondTSF to update every 3 epochs.

Image /page/21/Figure/0 description: This image displays a grid of eight line graphs, each comparing the test error of two methods, 'MTT' and 'MTT 5 epochs + CondTSF 1 epoch', against 'Condensation Epochs'. The graphs are arranged in a 2x4 layout and are labeled (a) ExchangeRate, (b) Weather, (c) Electricity, (d) Traffic, (e) ETTm1, (f) ETTm2, (g) ETTh1, and (h) ETTh2. The x-axis for all graphs represents 'Condensation Epochs' ranging from 0 to 200. The y-axis represents 'Test Error', with scales varying slightly across the graphs but generally ranging from 0.2 to 1.0. In most graphs, the 'MTT 5 epochs + CondTSF 1 epoch' line shows a steeper initial decrease in test error and achieves a lower final test error compared to the 'MTT' line, indicating better performance.

Figure 7: Yellow: Use MTT to distill for 200 epochs. Orange: Use MTT to distill for 200 epochs and use CondTSF to update every 5 epochs.

We observe that CondTSF consistently reduces the testing error with different utilization gaps.

# F.2 Relationship of Performance and Label Error

We also conduct experiments on label error and test error. We visualize the trajectory of label error  $\mathcal{L}_{label}$  and test error through the distillation process. The results are shown in Fig[.8.](#page-22-0)

- Model 1: Use MTT to distill for 200 epochs.
- Model 2: Use MTT to distill for 160 epochs and then use CondTSF to update for 40 epochs.

As shown in Fig[.8,](#page-22-0) it can be observed that using MTT[\[3\]](#page-9-3) leads to an increase in label error  $\mathcal{L}_{label}$ . While applying CondTSF effectively lowers the label error in the last 40 epochs, and therefore enhancing the performance.

<span id="page-22-0"></span>Image /page/22/Figure/0 description: This figure displays eight subplots, each illustrating the training curves of label error and test error during a distillation process for different datasets: ExchangeRate (a), Weather (b), Electricity (c), Traffic (d), ETTm1 (e), ETTm2 (f), ETTh1 (g), and ETTh2 (h). Each subplot features two y-axes: the left y-axis represents 'Label Error' and ranges from 0 to 150 (or 0 to 125 for (b)), while the right y-axis represents 'Test Error' and ranges from 0.2 to 0.9 (or 0.4 to 1.0 for (d) and (h)). The x-axis for all subplots is 'Condensation Epochs', ranging from 0 to 200. In each subplot, there are four lines: 'Test Error of Model 1' (yellow), 'Test Error of Model 2' (red), 'Label Error of Model 1' (dark blue), and 'Label Error of Model 2' (teal). A vertical dashed blue line at epoch 160 indicates when 'CondTSF is used' is applied, marked by an arrow above the line. Before epoch 160, label errors generally increase, while test errors fluctuate or decrease slightly. After epoch 160, both label errors and test errors for Model 2 show a sharp decrease, reaching low values by epoch 200, while Model 1's errors remain relatively high or increase.

Figure 8: Visulization of the training curve of label error and test error during the distillation process.

# G Parameter Sensitivity of CondTSF

We test CondTSF with different update gaps G and additive update ratios  $\beta$ . We utilize the standard distill ratio as shown in Table[.4.](#page-15-1) Our observations indicate that CondTSF displays a notable degree of robustness concerning these parameters. Specifically, the effectiveness of CondTSF persists when the update gap G is moderately sized and additive update ratio  $\beta$  is not excessively small.

Image /page/23/Figure/2 description: This image displays a collection of eight 3D bar charts, each representing the performance of CondTSF across different datasets and parameter settings. The charts are arranged in a 2x4 grid. Each chart has the x-axis labeled "Gaps of Using CondTSF" with values 1, 3, 5, 10, and 20, and the y-axis labeled "Additive Update Ratio" with values 0.010, 0.050, 0.100, 0.200, and 0.300. The z-axis represents "Test Error", with a color bar indicating the range of error values for each chart. The datasets represented are (a) ExchangeRate, (b) Weather, (c) Electricity, (d) Traffic, (e) ETTm1, (f) ETTm2, (g) ETTh1, and (h) ETTh2. The bars show varying test error levels based on the combinations of update gaps and update ratios.

Figure 9: Performance of CondTSF with different update gaps and update ratios.

# H Visualization of Synthetic Data

We provide some visualization of synthetic data distilled by MTT[\[3\]](#page-9-3) and MTT+CondTSF on all datasets. It is observed that the synthetic dataset distilled with CondTSF is smoother than the ones without CondTSF. Smoother data indicates more generalized features and therefore helps boost the performance.

Image /page/24/Figure/2 description: The image displays a grid of time series plots, organized into two main sections. The top section contains four columns labeled ETTm1, ETTm2, ETTh1, and ETTh2. Each column has three rows labeled Random Init, MTT, and CondTSF, with corresponding x-axis labels indicating time points (e.g., 0, 50, 100 or 0, 20, 40). The bottom section contains four columns labeled ExchangeRate, Weather, Electricity, and Traffic. Similar to the top section, each column has three rows labeled Random Init, MTT, and CondTSF, with x-axis labels indicating time points (e.g., 0, 20, 40, 60 or 0, 25, 50, 75, 100). Each plot shows a distinct time series line graph, with different colors representing different data series (dark blue for Random Init, teal for MTT, and yellow for CondTSF).

Figure 10: Visualization of synthetic data.

Image /page/24/Figure/4 description: The image displays a grid of time series plots. The top section contains four columns labeled ETTm1, ETTm2, ETTh1, and ETTh2. Each column has three rows labeled Random Init, MTT, and CondTSF, with x-axis values ranging from 0 to 100 or 0 to 40. The bottom section contains four columns labeled ExchangeRate, Weather, Electricity, and Traffic. Each of these columns also has three rows labeled Random Init, MTT, and CondTSF, with x-axis values ranging from 0 to 60, 0 to 100, 0 to 80, or 0 to 60 respectively. The plots show various time series data, with different colors and patterns representing different datasets and initialization methods.

Figure 11: Visualization of synthetic data.

Image /page/25/Figure/0 description: The image displays a grid of time series plots. The top section contains four columns labeled ETTm1, ETTm2, ETTh1, and ETTh2. Each column has three rows labeled Random Init, MTT, and CondTSF, with corresponding plots. The bottom section contains four columns labeled ExchangeRate, Weather, Electricity, and Traffic. Each of these columns also has three rows labeled Random Init, MTT, and CondTSF, with corresponding plots. The x-axis for ETTm1, ETTm2, ExchangeRate, Weather, Electricity, and Traffic plots ranges from 0 to 100 or 80, with tick marks at intervals like 0, 20, 40, 60, 80, 100. The x-axis for ETTh1 and ETTh2 plots ranges from 0 to 40 or 60, with tick marks at intervals like 0, 20, 40. The plots show different time series patterns, with varying colors including dark blue, teal, and yellow.

Figure 12: Visualization of synthetic data.

Image /page/25/Figure/2 description: The image displays a grid of time series plots, divided into two main sections. The top section contains four columns labeled ETTm1, ETTm2, ETTh1, and ETTh2. Each column has three rows labeled Random Init, MTT, and CondTSF, showing different time series data. The bottom section contains four columns labeled ExchangeRate, Weather, Electricity, and Traffic. Similar to the top section, each column has three rows labeled Random Init, MTT, and CondTSF, displaying corresponding time series data. The x-axis for ETTm1, ETTm2, MTT, and CondTSF plots ranges from 0 to 100 or 0 to 50. The x-axis for ExchangeRate, Weather, Electricity, and Traffic plots ranges from 0 to 100, 0 to 80, or 0 to 60. The y-axis scales vary across the plots. The overall arrangement suggests a comparison of different time series datasets under different initialization methods (Random Init, MTT, CondTSF).

Figure 13: Visualization of synthetic data.

# NeurIPS Paper Checklist

## 1. Claims

Question: Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope?

Answer: [Yes]

Justification: As shown in Sec[.1,](#page-0-0) we explicitly claim the contributions of this work.

Guidelines:

- The answer NA means that the abstract and introduction do not include the claims made in the paper.
- The abstract and/or introduction should clearly state the claims made, including the contributions made in the paper and important assumptions and limitations. A No or NA answer to this question will not be perceived well by the reviewers.
- The claims made should match theoretical and experimental results, and reflect how much the results can be expected to generalize to other settings.
- It is fine to include aspirational goals as motivation as long as it is clear that these goals are not attained by the paper.

## 2. Limitations

Question: Does the paper discuss the limitations of the work performed by the authors?

#### Answer: [Yes]

Justification: As shown in Sec[.6,](#page-8-1) we discuss the limitations of this work.

Guidelines:

- The answer NA means that the paper has no limitation while the answer No means that the paper has limitations, but those are not discussed in the paper.
- The authors are encouraged to create a separate "Limitations" section in their paper.
- The paper should point out any strong assumptions and how robust the results are to violations of these assumptions (e.g., independence assumptions, noiseless settings, model well-specification, asymptotic approximations only holding locally). The authors should reflect on how these assumptions might be violated in practice and what the implications would be.
- The authors should reflect on the scope of the claims made, e.g., if the approach was only tested on a few datasets or with a few runs. In general, empirical results often depend on implicit assumptions, which should be articulated.
- The authors should reflect on the factors that influence the performance of the approach. For example, a facial recognition algorithm may perform poorly when image resolution is low or images are taken in low lighting. Or a speech-to-text system might not be used reliably to provide closed captions for online lectures because it fails to handle technical jargon.
- The authors should discuss the computational efficiency of the proposed algorithms and how they scale with dataset size.
- If applicable, the authors should discuss possible limitations of their approach to address problems of privacy and fairness.
- While the authors might fear that complete honesty about limitations might be used by reviewers as grounds for rejection, a worse outcome might be that reviewers discover limitations that aren't acknowledged in the paper. The authors should use their best judgment and recognize that individual actions in favor of transparency play an important role in developing norms that preserve the integrity of the community. Reviewers will be specifically instructed to not penalize honesty concerning limitations.

## 3. Theory Assumptions and Proofs

Question: For each theoretical result, does the paper provide the full set of assumptions and a complete (and correct) proof?

Answer: [Yes]

Justification: We propose two theorems in Sec[.4.](#page-2-2) We provide complete proof of the two theorems in App[.A.1](#page-13-0) and App[.A.2.](#page-14-0)

### Guidelines:

- The answer NA means that the paper does not include theoretical results.
- All the theorems, formulas, and proofs in the paper should be numbered and crossreferenced.
- All assumptions should be clearly stated or referenced in the statement of any theorems.
- The proofs can either appear in the main paper or the supplemental material, but if they appear in the supplemental material, the authors are encouraged to provide a short proof sketch to provide intuition.
- Inversely, any informal proof provided in the core of the paper should be complemented by formal proofs provided in appendix or supplemental material.
- Theorems and Lemmas that the proof relies upon should be properly referenced.

## 4. Experimental Result Reproducibility

Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions of the paper (regardless of whether the code and data are provided or not)?

### Answer: [Yes]

Justification: We provide the detailed algorithm in Sec[.4.](#page-2-2) We provide detailed parameter settings to reproduce the results in Sec[.5.](#page-6-1)

- The answer NA means that the paper does not include experiments.
- If the paper includes experiments, a No answer to this question will not be perceived well by the reviewers: Making the paper reproducible is important, regardless of whether the code and data are provided or not.
- If the contribution is a dataset and/or model, the authors should describe the steps taken to make their results reproducible or verifiable.
- Depending on the contribution, reproducibility can be accomplished in various ways. For example, if the contribution is a novel architecture, describing the architecture fully might suffice, or if the contribution is a specific model and empirical evaluation, it may be necessary to either make it possible for others to replicate the model with the same dataset, or provide access to the model. In general. releasing code and data is often one good way to accomplish this, but reproducibility can also be provided via detailed instructions for how to replicate the results, access to a hosted model (e.g., in the case of a large language model), releasing of a model checkpoint, or other means that are appropriate to the research performed.
- While NeurIPS does not require releasing code, the conference does require all submissions to provide some reasonable avenue for reproducibility, which may depend on the nature of the contribution. For example
  - (a) If the contribution is primarily a new algorithm, the paper should make it clear how to reproduce that algorithm.
- (b) If the contribution is primarily a new model architecture, the paper should describe the architecture clearly and fully.
- (c) If the contribution is a new model (e.g., a large language model), then there should either be a way to access this model for reproducing the results or a way to reproduce the model (e.g., with an open-source dataset or instructions for how to construct the dataset).
- (d) We recognize that reproducibility may be tricky in some cases, in which case authors are welcome to describe the particular way they provide for reproducibility. In the case of closed-source models, it may be that access to the model is limited in some way (e.g., to registered users), but it should be possible for other researchers to have some path to reproducing or verifying the results.
- 5. Open access to data and code

Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental material?

### Answer: [Yes]

Justification: We attach the code needed to reproduce the results with the paper. Guidelines:

- The answer NA means that paper does not include experiments requiring code.
- Please see the NeurIPS code and data submission guidelines ([https://nips.cc/](https://nips.cc/public/guides/CodeSubmissionPolicy) [public/guides/CodeSubmissionPolicy](https://nips.cc/public/guides/CodeSubmissionPolicy)) for more details.
- While we encourage the release of code and data, we understand that this might not be possible, so "No" is an acceptable answer. Papers cannot be rejected simply for not including code, unless this is central to the contribution (e.g., for a new open-source benchmark).
- The instructions should contain the exact command and environment needed to run to reproduce the results. See the NeurIPS code and data submission guidelines ([https:](https://nips.cc/public/guides/CodeSubmissionPolicy) [//nips.cc/public/guides/CodeSubmissionPolicy](https://nips.cc/public/guides/CodeSubmissionPolicy)) for more details.
- The authors should provide instructions on data access and preparation, including how to access the raw data, preprocessed data, intermediate data, and generated data, etc.
- The authors should provide scripts to reproduce all experimental results for the new proposed method and baselines. If only a subset of experiments are reproducible, they should state which ones are omitted from the script and why.
- At submission time, to preserve anonymity, the authors should release anonymized versions (if applicable).
- Providing as much information as possible in supplemental material (appended to the paper) is recommended, but including URLs to data and code is permitted.

## 6. Experimental Setting/Details

Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the results?

### Answer: [Yes]

Justification: We provide detailed hyperparameter settings in Sec[.5](#page-6-1) that are necessary to reproduce the results.

Guidelines:

- The answer NA means that the paper does not include experiments.
- The experimental setting should be presented in the core of the paper to a level of detail that is necessary to appreciate the results and make sense of them.
- The full details can be provided either with the code, in appendix, or as supplemental material.

## 7. Experiment Statistical Significance

Question: Does the paper report error bars suitably and correctly defined or other appropriate information about the statistical significance of the experiments?

### Answer: [Yes]

Justification: We repeat each experiment five times and the average and standard deviation of errors are reported in Sec[.5.](#page-6-1)

- The answer NA means that the paper does not include experiments.
- The authors should answer "Yes" if the results are accompanied by error bars, confidence intervals, or statistical significance tests, at least for the experiments that support the main claims of the paper.
- The factors of variability that the error bars are capturing should be clearly stated (for example, train/test split, initialization, random drawing of some parameter, or overall run with given experimental conditions).

- The method for calculating the error bars should be explained (closed form formula, call to a library function, bootstrap, etc.)
- The assumptions made should be given (e.g., Normally distributed errors).
- It should be clear whether the error bar is the standard deviation or the standard error of the mean.
- It is OK to report 1-sigma error bars, but one should state it. The authors should preferably report a 2-sigma error bar than state that they have a 96% CI, if the hypothesis of Normality of errors is not verified.
- For asymmetric distributions, the authors should be careful not to show in tables or figures symmetric error bars that would yield results that are out of range (e.g. negative error rates).
- If error bars are reported in tables or plots, The authors should explain in the text how they were calculated and reference the corresponding figures or tables in the text.

## 8. Experiments Compute Resources

Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce the experiments?

### Answer: [Yes]

Justification: As shown in Sec[.5,](#page-6-1) we provide the model of the GPU we used for experiments. Guidelines:

- The answer NA means that the paper does not include experiments.
- The paper should indicate the type of compute workers CPU or GPU, internal cluster, or cloud provider, including relevant memory and storage.
- The paper should provide the amount of compute required for each of the individual experimental runs as well as estimate the total compute.
- The paper should disclose whether the full research project required more compute than the experiments reported in the paper (e.g., preliminary or failed experiments that didn't make it into the paper).

## 9. Code Of Ethics

Question: Does the research conducted in the paper conform, in every respect, with the NeurIPS Code of Ethics <https://neurips.cc/public/EthicsGuidelines>?

### Answer: [Yes]

Justification: This work does not involve human subjects. As shown in Sec[.5,](#page-6-1) the datasets used in this work are all widely used open-source datasets.

### Guidelines:

- The answer NA means that the authors have not reviewed the NeurIPS Code of Ethics.
- If the authors answer No, they should explain the special circumstances that require a deviation from the Code of Ethics.
- The authors should make sure to preserve anonymity (e.g., if there is a special consideration due to laws or regulations in their jurisdiction).

## 10. Broader Impacts

Question: Does the paper discuss both potential positive societal impacts and negative societal impacts of the work performed?

Answer: [NA]

Justification: This work does not involve human subjects. As shown in Sec[.5,](#page-6-1) the datasets used in this work are widely used and open-source. There's no harm to humans or leak of privacy in this work.

- The answer NA means that there is no societal impact of the work performed.
- If the authors answer NA or No, they should explain why their work has no societal impact or why the paper does not address societal impact.

- Examples of negative societal impacts include potential malicious or unintended uses (e.g., disinformation, generating fake profiles, surveillance), fairness considerations (e.g., deployment of technologies that could make decisions that unfairly impact specific groups), privacy considerations, and security considerations.
- The conference expects that many papers will be foundational research and not tied to particular applications, let alone deployments. However, if there is a direct path to any negative applications, the authors should point it out. For example, it is legitimate to point out that an improvement in the quality of generative models could be used to generate deepfakes for disinformation. On the other hand, it is not needed to point out that a generic algorithm for optimizing neural networks could enable people to train models that generate Deepfakes faster.
- The authors should consider possible harms that could arise when the technology is being used as intended and functioning correctly, harms that could arise when the technology is being used as intended but gives incorrect results, and harms following from (intentional or unintentional) misuse of the technology.
- If there are negative societal impacts, the authors could also discuss possible mitigation strategies (e.g., gated release of models, providing defenses in addition to attacks, mechanisms for monitoring misuse, mechanisms to monitor how a system learns from feedback over time, improving the efficiency and accessibility of ML).

## 11. Safeguards

Question: Does the paper describe safeguards that have been put in place for responsible release of data or models that have a high risk for misuse (e.g., pretrained language models, image generators, or scraped datasets)?

Answer: [NA]

Justification: As shown in Sec[.5,](#page-6-1) the datasets used in this work are all widely used and open-source. There's no risk of releasing unsafe data.

Guidelines:

- The answer NA means that the paper poses no such risks.
- Released models that have a high risk for misuse or dual-use should be released with necessary safeguards to allow for controlled use of the model, for example by requiring that users adhere to usage guidelines or restrictions to access the model or implementing safety filters.
- Datasets that have been scraped from the Internet could pose safety risks. The authors should describe how they avoided releasing unsafe images.
- We recognize that providing effective safeguards is challenging, and many papers do not require this, but we encourage authors to take this into account and make a best faith effort.

## 12. Licenses for existing assets

Question: Are the creators or original owners of assets (e.g., code, data, models), used in the paper, properly credited and are the license and terms of use explicitly mentioned and properly respected?

### Answer: [Yes]

Justification: As shown in Sec[.5,](#page-6-1) we cite the papers that produced the code. The datasets used in this work are all widely used and open-source.

- The answer NA means that the paper does not use existing assets.
- The authors should cite the original paper that produced the code package or dataset.
- The authors should state which version of the asset is used and, if possible, include a URL.
- The name of the license (e.g., CC-BY 4.0) should be included for each asset.
- For scraped data from a particular source (e.g., website), the copyright and terms of service of that source should be provided.

- If assets are released, the license, copyright information, and terms of use in the package should be provided. For popular datasets, <paperswithcode.com/datasets> has curated licenses for some datasets. Their licensing guide can help determine the license of a dataset.
- For existing datasets that are re-packaged, both the original license and the license of the derived asset (if it has changed) should be provided.
- If this information is not available online, the authors are encouraged to reach out to the asset's creators.

## 13. New Assets

Question: Are new assets introduced in the paper well documented and is the documentation provided alongside the assets?

Answer: [Yes]

Justification: We provide documents with the code.

Guidelines:

- The answer NA means that the paper does not release new assets.
- Researchers should communicate the details of the dataset/code/model as part of their submissions via structured templates. This includes details about training, license, limitations, etc.
- The paper should discuss whether and how consent was obtained from people whose asset is used.
- At submission time, remember to anonymize your assets (if applicable). You can either create an anonymized URL or include an anonymized zip file.

## 14. Crowdsourcing and Research with Human Subjects

Question: For crowdsourcing experiments and research with human subjects, does the paper include the full text of instructions given to participants and screenshots, if applicable, as well as details about compensation (if any)?

Answer: [NA]

## 15. Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects

- The answer NA means that the paper does not involve crowdsourcing nor research with human subjects.
- Including this information in the supplemental material is fine, but if the main contribution of the paper involves human subjects, then as much detail as possible should be included in the main paper.
- According to the NeurIPS Code of Ethics, workers involved in data collection, curation, or other labor should be paid at least the minimum wage in the country of the data collector.

### 15. Institutional Review Board (IRB) Approvals or Equivalent for Research with Human Subjects

Question: Does the paper describe potential risks incurred by study participants, whether such risks were disclosed to the subjects, and whether Institutional Review Board (IRB) approvals (or an equivalent approval/review based on the requirements of your country or institution) were obtained?

Answer: [NA]

Justification: This work does not involve crowdsourcing nor research with human subjects.

- The answer NA means that the paper does not involve crowdsourcing nor research with human subjects.
- Depending on the country in which research is conducted, IRB approval (or equivalent) may be required for any human subjects research. If you obtained IRB approval, you should clearly state this in the paper.

- We recognize that the procedures for this may vary significantly between institutions and locations, and we expect authors to adhere to the NeurIPS Code of Ethics and the guidelines for their institution.
- For initial submissions, do not include any information that would break anonymity (if applicable), such as the institution conducting the review.
# <span id="page-0-1"></span>Rethinking Data Distillation: Do Not Overlook Calibration

<PERSON>ya<PERSON><sup>1</sup>, <PERSON><sup>2</sup>, <PERSON><PERSON><sup>3</sup>, <PERSON><PERSON><sup>4</sup>, <PERSON><PERSON><PERSON><sup>5</sup>, <PERSON><PERSON><PERSON><sup>6</sup>, <PERSON><PERSON><PERSON>^*$ 

<sup>1</sup>Unaffiliated, <sup>2</sup>Texas A&M University, <sup>3</sup>Zhejiang University, <sup>4</sup>Certik, <sup>5</sup>University of Maryland, College Park, <sup>6</sup>Purdue University, <sup>\*</sup>North Carolina State University

## Abstract

*Neural networks trained on distilled data often produce over-confident output and require correction by calibration methods. Existing calibration methods such as temperature scaling and mixup work well for networks trained on original large-scale data. However, we find that these methods fail to calibrate networks trained on data distilled from large source datasets. In this paper, we show that distilled data lead to networks that are not calibratable due to (i) a more concentrated distribution of the maximum logits and (ii) the loss of information that is semantically meaningful but unrelated to classification tasks. To address this problem, we propose Masked Temperature Scaling (MTS) and Masked Distillation Training (MDT) which mitigate the limitations of distilled data and achieve better calibration results while maintaining the efficiency of dataset distillation. Our code is available upon request.*

## 1. Introduction

Dataset distillation (DD) has recently gained growing attention because of its ability to reduce the need for large amounts of data during deep neural network (DNN) training, thereby reducing training time and storage burden [\[43\]](#page-10-0). Despite the efficiency of training, studies have pointed out that DD still has multiple limitations. On the one hand, the distillation process is found to be time-consuming, computationally expensive, and storage intensive [\[43,](#page-10-0) [56,](#page-10-1) [55,](#page-10-2) [7,](#page-9-0) [28,](#page-9-1) [29,](#page-9-2) [14,](#page-9-3) [52\]](#page-10-3). On the other hand, DNNs trained on DD data are said to be poorly generalizable to different models or downstream tasks [\[43,](#page-10-0) [56,](#page-10-1) [55\]](#page-10-2). Efforts have been conducted to address these issues [\[3,](#page-9-4) [53,](#page-10-4) [24\]](#page-9-5). However, the calibration of DD has been overlooked, which is important for deploying DD safely in real-world applications.

An increasing number of studies are investigating calibration as an important property of DNNs, which means that a DNN should know when it is likely to be wrong  $[10, 10]$  $[10, 10]$ 

Image /page/0/Figure/8 description: This figure displays reliability diagrams for different calibration methods on CIFAR10 and CIFAR100 datasets. The top row shows results for CIFAR10, with columns representing 'Uncalibrated', 'Focal', 'Mixup', 'LS', and 'Ours'. The bottom row shows results for CIFAR100, with the same column categories. Each subplot contains stacked bar charts showing 'ECE' (orange) and 'Confidence' (blue) against 'Accuracy' (also blue, stacked on confidence). The x-axis for all plots is 'Confidence' ranging from 0.0 to 1.0, and the y-axis is 'Accuracy' ranging from 0.0 to 1.0. Each subplot also indicates the ECE value: CIFAR10 Uncalibrated (5.81), Focal (7.02), Mixup (21.52), LS (25.00), Ours (1.05); CIFAR100 Uncalibrated (6.27), Focal (7.79), Mixup (14.09), LS (26.18), Ours (1.87).

<span id="page-0-0"></span>Figure 1. ECE (red area, smaller is better) of different calibrations on an over-confident ConvNet trained on MTT [\[3\]](#page-9-4) distilled CI-FAR10 and CIFAR100. Our proposed techniques achieve the best calibration results compared to the over-calibration of other methods. Focal: Focal loss. LS: Label Smoothing.

[26,](#page-9-7) [1\]](#page-9-8). In other words, the confidence (probability related to the predicted category label) of a model should reflect its ground truth correctness likelihood (accuracy). Previous work has found that DNNs are often too confident to realize when they are making mistakes [\[10,](#page-9-6) [31\]](#page-10-5), which leads to safety issues, especially in safety-critical tasks, e.g., automated healthcare and self-driving cars [\[6,](#page-9-9) [33\]](#page-10-6).

We for the *first* time identify and study the calibration problem of DNNs trained on distilled data (DDNNs).

*Problem 1. We find that DDNNs still suffer from overconfidence problem.*

We evaluate the calibration quality of DDNNs by Expected Calibration Error (ECE) [\[10\]](#page-9-6), which is a common metric to quantitatively measure the difference between confidence and accuracy. Specifically, to calculate the ECE, we categorize the output probability and accuracy into different levels and calculate the average absolute difference. The lower the ECE, the better the calibration. As shown in Figure [1,](#page-0-0) the ECE (red area) of DDNNs is quite visible in the figures of the first column, which means that the probability of DDNNs' output is usually higher than the actual accuracy of its prediction. Thus, it is desirable to calibrate DDNNs for reliable prediction and decision-making.

*Problem 2. We find that DDNNs are not calibratable*

<span id="page-1-0"></span>*when using existing calibration methods.*

There are calibration methods designed to align the confidence and accuracy of DNNs trained on full datasets (FDNNs). They either modify loss term during network training  $[21]$ , use soft labels  $[50, 38]$  $[50, 38]$  $[50, 38]$ , or scale down the logits after training [\[10\]](#page-9-6). However, when training on distilled data, we find that most of the existing methods tend to over-calibrate DDNNs. As shown in Figure [1,](#page-0-0) a DDNN trained on distilled CIFAR10 (the first column) has an initial ECE of 6.17% (red area). After calibrating with focal loss (the second column), mixup (the third column), or label smoothing (the fourth column), the DDNN becomes under-confident with increased ECE of 7.79%, 14.09%, and 26.18% respectively, as shown by the inverted and enlarged red bars. This over-calibration problem also occurs for various distillation methods on common datasets (Table [1\)](#page-4-0).

In order to address the issues mentioned above, we raise the following questions:

*Question 1. Why are DDNNs not calibratable when using existing calibration methods?*

We first dive deep into the differences between the source full data and the distilled data. We find that the distilled data tend to retain information relevant to the classification task while discarding other distributional information in the full data, which may result in limiting DDNNs to pursuing higher accuracy in the classification task while losing more abilities in latent representation learning of FDNNs [\[40,](#page-10-9) [30\]](#page-9-11). By decomposing distilled and full data into smaller components and studying their corresponding significance to model training accuracy, we show that distilled data contains very condensed information, implying a loss of information and leading to harder during-training calibration. Then, we also investigate the differences between DDNNs and FDNNs. We observe that DDNNs have a more concentrated distribution of logit values, leading to less room for after-training calibration methods such as temperature scaling.

*Question 2. How to calibrate DDNNs efficiently?*

To enable DDNNs to be calibratable, we propose (i) Mask Temperature Scaling and (ii) Masked Distillation Training that can be applied both during and after the training of DDNNs. We design a binary masking method for synthetic input when training for distillation objection, which effectively forces the distillation model to extract richer information from the source dataset into distilled datasets, leading to better encoding abilities and thus better calibration of DDNNs. We also show that our proposed masked temperature scaling better improves after-training calibration results on DDNNs by introducing more dynamics to network outputs. Our proposed techniques thus allow for more powerful and more calibratable DDNNs. We summarize contributions as follows:

• We for the *first* time study the calibration of DDNNs

and find that DDNNs are not calibratable.

- We find that DD discards semantically meaningful information and that DDNNs produce a concentrated logit distribution, which explains the difficulty of calibrating DDNNs.
- We propose two masking techniques that can improve the calibration of DDNNs better than existing calibration methods, i.e., masked distillation training and masked temperature scaling. In addition, our proposed techniques can be readily deployed in existing dataset distillation methods with minimal extra cost.
- We perform extensive experiments on multiple benchmark datasets, model architectures, and data distillation methods. Our techniques reduce ECE values by up to 91.05% with comparable accuracy.

## 2. Related Work

Dataset Distillation. First introduced by [\[43\]](#page-10-0), dataset distillation is the task of synthesizing a smaller dataset from a large-scale dataset such as CIFAR100 [\[17\]](#page-9-12), so that the network trained on the distilled data has a performance comparable to that of the network trained on the source large-scale data. Recent work has significantly improved the performance of networks trained on distilled data and reduced the computational and time overhead of the distillation process while compressing the dataset size to one image per class [\[3,](#page-9-4) [7,](#page-9-0) [24,](#page-9-5) [28,](#page-9-1) [29,](#page-9-2) [56,](#page-10-1) [55,](#page-10-2) [42,](#page-10-10) [51\]](#page-10-11). Dataset distillation problem is treated as a gradient-based hyperparameter optimization [\[43\]](#page-10-0). DC performs distillation by matching the gradients generated from distilled data and full data [\[56\]](#page-10-1). DSA further improves the results by differentiable Siamese augmentations [\[55\]](#page-10-2). Other SOTA methods include matching trajectories of each parameter between the training on distilled data and full data [\[3\]](#page-9-4), optimizing soft labels [\[37\]](#page-10-12), minimizing reconstruction errors [\[48\]](#page-10-13), and using neural networks to regress features from synthetic samples to real ones  $[57]$ . The current focus of DD is on computational expense and training performance, and to the best of our knowledge, the difficulties in calibrating over-confident DDNNs remain untouched.

Neural Network Calibration. The importance of neural network calibration has been emphasized and received increasing attention  $[10]$ , with the aim of matching the output probability of a neural network (also known as the network output confidence) with the actual accuracy. [\[10\]](#page-9-6) also introduces the concept of Expected Calibration Error (ECE), which has now become a standard metric for quantitatively measuring calibration quality. A higher ECE implies a poorer calibration of the neural network, while a 0 implies a perfect calibration. Recent calibration methods that have

<span id="page-2-5"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: The image contains two plots. The left plot is a histogram showing the percentage distribution of the maximum value of network logits for FDNN and DDNN. The x-axis ranges from 0 to 25 and is labeled "Maximum Value of Network Logits". The y-axis ranges from 0.00 to 0.35 and is labeled "Percentage". The FDNN distribution is a bell curve centered around 10, while the DDNN distribution is a sharper peak centered around 12. The right side of the image contains two bar charts. The top bar chart shows the probability distribution for "ID (CIFAR10)" and "OOD (SVHN)" for class labels 0 through 9. The "ID (CIFAR10)" bar at class label 0 is close to 1.0, while the "OOD (SVHN)" bar at class label 1 is close to 1.0. The bottom bar chart also shows the probability distribution for "ID (CIFAR10)" and "OOD (SVHN)" for class labels 0 through 9. Similar to the top chart, the "ID (CIFAR10)" bar at class label 0 is close to 1.0, and the "OOD (SVHN)" bar at class label 1 is close to 1.0. The y-axis for both bar charts is labeled "Probability" and ranges from 0 to 1.0. The x-axis for both bar charts is labeled "Class Label" and ranges from 0 to 9.

<span id="page-2-1"></span>(a) Maximum logits produced by (b) Prob. of DDNN (top) vs. Ours DDNNs and FDNNs. (bottom) on ID / OOD samples.

Figure 2. Left: The more calibratable FDNN outputs more evenly distributed logits, while the less calibratable DDNN outputs a more concentrated logit distribution. Top-Right: The less calibratable DDNN struggles to distinguish between an in-distribution (ID) and an out-of-distribution (OOD) sample using its max logits.

been proposed for networks trained on large-scale datasets include Label Smoothing (LS) [\[49\]](#page-10-15), which smooths a onehot class label with uniform noise during training, forcing the model to learn loose predictions. Mixup is similar to label smoothing, where different data-label pairs are mixed to form new data points [\[38,](#page-10-8) [50\]](#page-10-7). Focal loss (FL), originally designed to address the class imbalance, modifies the traditional cross-entropy loss in classification problems by adding a moderation term, thus allowing the model to focus more on difficult examples that are easily misclassified but difficult to learn [\[21,](#page-9-10) [25\]](#page-9-13). Temperature scaling (TS) is an after-training calibration method applied to fully trained and fixed-weight networks [\[10\]](#page-9-6). As an extension of Platt scaling [\[32\]](#page-10-16), the temperature scaling method scales the output, denoted by z, of the last layer of the network with a scaler T before converting it into a probability:

<span id="page-2-4"></span>
$$
\hat{q}_i = \max_k \sigma_{softmax} (z_i/T)^{(k)} z_i \in \mathbb{R}^D.
$$
 (1)

Other work has discussed the necessity [\[45\]](#page-10-17) and hardness of network calibration  $[5, 9, 54]$  $[5, 9, 54]$  $[5, 9, 54]$  $[5, 9, 54]$  $[5, 9, 54]$ , as well as the degradation of calibration with distribution shift or model size [\[16,](#page-9-16) [20\]](#page-9-17).

## 3. Limitation Analysis of DDNNs' Calibration

We focus on the difficulties of calibrating over-confident DDNNs. As shown in the first column of Figure [1](#page-0-0) and the raw ECE reported in Table [1,](#page-4-0) DDNNs show the common over-confidence problem of neural networks, giving higher probabilities than actual accuracy; however, when applied with existing calibration methods, DDNNs are often overcalibrated and become under-confident. In this section, we analyze the reasons that may account for the DDNNs that are not calibratable from 2 aspects: (i) the after-training prediction behaviors and (ii) the during-training network capacity in terms of feature encoding ability. We also discuss the decomposed significance of full data and distilled data on the training accuracy of the network.

Image /page/2/Figure/8 description: This image contains two plots. The left plot is a bar chart showing the relative accuracy drop (%) for MTT, DSA, and FD methods at different image per class values (10, 30, 50) and for FD. The y-axis ranges from 0.0 to 0.4. The bars for 10 image per class are approximately 0.2 for MTT and 0.35 for DSA. The bars for 30 image per class are approximately 0.16 for MTT and 0.45 for DSA. The bars for 50 image per class are approximately 0.14 for MTT and 0.38 for DSA. The FD bar is approximately 0.14. The right plot is a line graph showing the accuracy (%) versus the percentage of singular values dropped. The x-axis ranges from 0 to 20, and the y-axis ranges from 50 to 75. The FDNN line starts at approximately 73% accuracy at 0% singular values dropped and decreases to approximately 72% at 20% singular values dropped. The DDNN line starts at approximately 63% accuracy at 0% singular values dropped and decreases to approximately 50% at 20% singular values dropped. Both lines show a downward trend as the percentage of singular values dropped increases. The legend indicates that the blue line represents FDNN and the red line represents DDNN.

<span id="page-2-2"></span>(a) DDNNs lose more accuracy (b) Trend of accuracy loss vs. than FDNNs across DD settings. dropping singular values (MTT).

Figure 3. Effects on model accuracy of discarding major singular values during SVD reconstruction of distilled and full CIFAR10. DDNNs suffer from more accuracy drop as we discard more singular values during reconstruction, indicating that distilled data contains more condensed information that can be easily grouped by a simple SVD decomposition. Right: IPC = 10.

## <span id="page-2-3"></span>3.1. DDNNs are Less Calibratable

We find that the logit distribution of the DDNNs' output is more concentrated, making it difficult to calibrate. In general, a neural network can be considered as a mapping function from the source data domain to the target label distribution, and in the classification task, we use the softmax function to convert logits into label probabilities. The higher the maximum logit value compared to other values, the higher the argmax probability will be and thus the more likely such prediction is over-confident. Therefore we study the distribution of maximum logit values for fully trained DDNNs and FDNNs. As shown in Figure  $2(a)$ , the more calibratable FDNNs (blue) output a more dispersed logit distribution, while the less calibratable DDNNs (red) output a concentrated logit distribution with a larger mean.

This mismatched behavior causes problems for aftertraining calibration methods such as TS and Mixup that operate on scaling output logits, because DDNNs with tight distributions of max logits struggle to distinguish between hard (e.g., out-of-distribution, OOD) and easy (e.g., indistribution, ID) samples (top of Figure  $2(b)$ ) using the corresponding max logits [\[44\]](#page-10-19). Similar theoretical assumptions appear in recent work [\[41\]](#page-10-20), where they show that the small range of logits due to regularization during training, and a large mean of logits due to the network trying to fit on hard examples may lose information about the different hardness of data points, causing the networks of aftertraining calibration methods to fail to calibrate.

Therefore, we infer that DDNNs are less calibratable using distilled data due to their more concentrated output distribution and larger mean values. Thus, in order to make DDNNs more calibratable in after-training calibration without modifying network weights, we aim to utilize data that force DDNNs to produce more diverse and smaller outputs.

<span id="page-3-5"></span><span id="page-3-2"></span>

## 3.2. DD Contains Limited Semantic Information

By reconstructing distilled and full data with SVD, we find that distilled data contains only condensed information about the classification task, resulting in the limited ability of DDNNs in latent representation learning. Intuitively, distilled images should be more informative, or more representative than source full images, in order to keep the number of images small. But do distilled images discard too much source information that is not so much useful for the classification tasks they are optimized for? We hypothesize that distilled data is "simpler" than source full data, such that dropping the same amount of information from distilled datasets should hurt the training performance worse than it does on full data. We start by breaking down full datasets into smaller components of different significance. Singular value decomposition (SVD)  $[15]$  is a powerful algorithm in linear algebra for matrix approximation:

<span id="page-3-3"></span>
$$
U, \Sigma, V = \text{SVD}(X),\tag{2}
$$

where higher singular values in  $\Sigma$  correspond to more significant components of  $X$ . Source data can then be approximately reconstructed by

<span id="page-3-4"></span>
$$
X' \approx U \cdot \Sigma' \cdot V^T \tag{3}
$$

SVD has been widely used in DNN research for model reconstruction  $[46, 47]$  $[46, 47]$  $[46, 47]$ , knowledge distillation  $[19]$ , and analyzing data [\[11\]](#page-9-20). For our purposes of analyzing data information diversity and significance of data components, we gradually throw away the highest singular values during SVD reconstruction and check for accuracy drop when trained on the approximately reconstructed data.

Our assumption is that distilled data contains dense information that can be easily grouped, such that SVD decomposes distilled data into several important components and other very small components, compared to full data components whose importance can be more evenly distributed, such that dropping the same number of important components from distilled data would lose more accuracy than in full data. We drop from 0% to 20% of the highest singular values from full CIFAR10 and CIFAR10 distilled by [\[3\]](#page-9-4) with IPC =  $10$ ,  $30$  and  $50$ , then train a ConvNet for  $300$ epochs on the resulting data. As shown in Figure [3,](#page-2-2) DDNNs suffer much more severely from the loss of principle components than FDNNs.

We thus conclude that the distilled data discards too much other semantically meaningful information from the original full data due to over-optimization of the classification task, resulting in condensed information that can be easily decomposed by SVD.

Image /page/3/Figure/8 description: The image displays a grid of scatter plots, comparing the feature representations of two neural network architectures, FDNN and DDNN, across different layers: conv0, conv1, conv2, and linear. Each scatter plot visualizes data points colored by class, with distinct clusters indicating separability. The FDNN row shows progressively more separated clusters from conv0 to conv2, with the linear layer showing clear separation. The DDNN row also shows increasing separation, with conv0 and conv1 having dispersed points, conv2 showing distinct clusters, and the linear layer exhibiting very clear, isolated clusters for each class.

<span id="page-3-0"></span>Figure 4. T-SNE projections of feature vectors from each layer of a 4-block ConvNet trained with Mixup on distilled and full data. FDNNs better encode source information as visualized by the rich features not separated until the last layer. DDNN poorly encodes source information, as shown by the feature projections already separated in layer conv2.

## <span id="page-3-1"></span>3.3. Limited Semantic Information Weakens Encoding Capacity

We further infer that DDNNs may be less capable of tasks other than classification due to the likely loss of nonclassification information. Usually, outputs of intermediate layers of DNNs could be used as feature vectors for other interesting non-classification tasks such as style transfer  $[8, 13]$  $[8, 13]$  $[8, 13]$  due to their unique encodings of source information. To see this, we visualize outputs of layers at different depths in the ConvNet using t-SNE that projects feature vectors down to 2 dimensions. We can see that in Figure [4,](#page-3-0) features from FDNNs cluster slowly, and become visually separable only in the last layer, thus retaining most of the original information in its latent vectors; features from DDNNS, however, form visible cluster already in layer conv2, making more compact final clusters that are more valuable for classifications than other tasks such as feature extraction. Moreover, clusters of DDNNs from each class are closer to each other than those from FDNNs. Clearly, outputs of middle layers from DDNNs are already alike label distributions, discarding too much non-classification information. Similar observations on other distillation backbones are reported in [\[42\]](#page-10-10), in which they also account for long-tailed gradients as possible reasons.

Therefore, DDNNs do not exhibit good encoding capability due to being trained on distilled data optimized specifically for the classification task and may be susceptible to being over-calibrated by calibration methods. We provide more details in the supplementary material.

## 4. Our Proposed Techniques

We respond to the analyses in Sections [3.1-](#page-2-3)[3.3](#page-3-1) so that our method can be applied during and after training, providing calibration options at different times and computational budget levels.

<span id="page-4-2"></span><span id="page-4-0"></span>Table 1. ECE (%) of different calibration methods on DDNNs with different DD backbones. Our proposed method yields the best or comparable ECE results in all distillation settings, reducing ECE of DSA by 91.05%. More importantly, our method does not overcalibrate DDNNs as other calibration methods do (italic), as counted in the last row. Although MX and LS outperform our method in distillation backbones of inferior accuracy (RTP, DC), we surpass them by fine-tuning a more aggressive  $r$ , as described in Section [6.](#page-7-0)

| <b>DD</b> Backbone |               | Raw                        | TS                         | MX                         | LS                         | FL                         | Ours                       |
|--------------------|---------------|----------------------------|----------------------------|----------------------------|----------------------------|----------------------------|----------------------------|
| <b>MTT</b>         | CIFAR10       | $4.93 	ext{ 	extpm } 0.2$  | $7.45 	ext{ 	extpm } 2.1$  | $21.06 	ext{ 	extpm } 0.8$ | $25.01 	ext{ 	extpm } 0.2$ | $6.62 	ext{ 	extpm } 0.2$  | $1.20 	ext{ 	extpm } 0.3$  |
|                    | CIFAR100      | $5.95 	ext{ 	extpm } 0.4$  | $7.76 	ext{ 	extpm } 0.4$  | $14.19 	ext{ 	extpm } 0.4$ | $26.36 	ext{ 	extpm } 0.4$ | $8.30 	ext{ 	extpm } 0.5$  | $2.18 	ext{ 	extpm } 0.2$  |
|                    | Tiny ImageNet | $15.78 	ext{ 	extpm } 0.3$ | $2.44 	ext{ 	extpm } 0.3$  | $2.42 	ext{ 	extpm } 0.3$  | $12.14 	ext{ 	extpm } 0.3$ | $3.61 	ext{ 	extpm } 0.3$  | $2.26 	ext{ 	extpm } 0.3$  |
|                    | ImageNette    | $8.68 	ext{ 	extpm } 1.9$  | $4.85 	ext{ 	extpm } 0.6$  | $5.19 	ext{ 	extpm } 0.6$  | $23.45 	ext{ 	extpm } 1.4$ | $6.87 	ext{ 	extpm } 1.3$  | $4.78 	ext{ 	extpm } 0.5$  |
| <b>RTP</b>         | CIFAR10       | $2.96 	ext{ 	extpm } 0.5$  | $3.28 	ext{ 	extpm } 0.7$  | $13.35 	ext{ 	extpm } 1.5$ | $9.58 	ext{ 	extpm } 0.5$  | $8.35 	ext{ 	extpm } 1.4$  | $2.22 	ext{ 	extpm } 0.5$  |
|                    | CIFAR100      | $29.71 	ext{ 	extpm } 0.6$ | $23.72 	ext{ 	extpm } 0.6$ | $3.55 	ext{ 	extpm } 0.6$  | $7.94 	ext{ 	extpm } 0.2$  | $18.51 	ext{ 	extpm } 0.5$ | $10.14 	ext{ 	extpm } 0.4$ |
| DC                 | CIFAR10       | $23.60 	ext{ 	extpm } 0.7$ | $5.00 	ext{ 	extpm } 0.7$  | $1.83 	ext{ 	extpm } 0.3$  | $1.28 	ext{ 	extpm } 0.1$  | $13.31 	ext{ 	extpm } 0.9$ | $10.39 	ext{ 	extpm } 0.8$ |
| <b>DSA</b>         | CIFAR10       | $19.91 	ext{ 	extpm } 0.3$ | $1.95 	ext{ 	extpm } 0.4$  | $6.44 	ext{ 	extpm } 0.8$  | $2.32 	ext{ 	extpm } 0.5$  | $7.95 	ext{ 	extpm } 0.7$  | $1.70 	ext{ 	extpm } 0.4$  |
| # over-calibration |               | -                          | 3                          | 3                          | 4                          | 3                          | 0                          |

### 4.1. Masked Temperature Scaling

As discussed in Section [3.1,](#page-2-3) compared to FDNNs, DDNNs produce a more concentrated distribution of logit values with larger values, and these large and condensed logit values lead to networks that are not calibratable. Since after-training calibration methods such as temperature scaling [\[10\]](#page-9-6) make use of these large and concentrated logit values from a forward pass of validation data, we seek to overcome this source of difficulty in calibration by perturbing the validation data such that the model could output more various and smaller logit values. Inspired by dropout [\[36\]](#page-10-23), we apply a simple zero-masking on the validation data of temperature scaling. Our proposed method, which we refer to as Masked Temperature Scaling (MTS), thus modifies Eq  $(1)$  as follows:

$$
\hat{q}_i = \max_k \sigma_{softmax} (z_i * mask/T)^{(k)}, \qquad (4)
$$

where q, z, mask  $\in R^D$  and the number of zeros in the mask is controlled by a hyperparameter masking ratio  $r$ . Note that masking is only applied when updating  $T$ , such that MTS does not change model accuracy. We use a sampled portion of the training data we have to update the temperature parameter T, instead of using separate validation data as in traditional temperature scaling.

This is particularly necessary in the dataset refinement setting, as we may simply not have any extra data, for example, when each class of images is set to 1 (see Section [6](#page-7-0) for more details).

### 4.2. Masked Distillation Training

In response to the analyses in Sections [3.1](#page-2-3)[-3.3,](#page-3-1) we avoid over-concentration of distillation data on easily identifiable information in the source complete data by perturbing the binary mask during distillation, so that the distillation data also contain more semantically complete information.

## Algorithm 1: Masked Distillation Training

<span id="page-4-1"></span>

| <b>Input:</b> Source training data $\mathcal{T}$ , number of                  |
|-------------------------------------------------------------------------------|
| classes $N_c$ , deep neural network $\psi_{\theta}$ parameterized             |
| with $\theta$ , criterion C, loss function l, total number of                 |
| training steps $T$ , masking ratio $r$                                        |
| <b>Output:</b> Distilled dataset $S$                                          |
| 1 for $t \leftarrow 0$ to $T$ do                                              |
| 2 custom pre-processing                                                       |
| 3 for $c \leftarrow 0$ to $N_c$ do                                            |
| 4 Sample $T_c \sim \mathcal{T}, S_c \sim \mathcal{S}$                         |
| 5 Update synthetic data $S_c$ :                                               |
| 6 $S_c \leftarrow S_c - \lambda \nabla_{S_c} C(S_c, Mask(T_c, r), l, \theta)$ |
| 7 end                                                                         |
| 8 custom post-processing                                                      |
| 9 Update $\theta$ of network $\psi_{\theta}$ using $T \sim \mathcal{T}$       |
| 10 end                                                                        |

A typical DD training paradigm tries to minimize the differences of certain characteristics, as measured by some criterion  $C$ , between data batch  $B'$  from synthetic data and data batch  $B$  from source full data. The loss function  $l(\theta; X)$  used in C is usually the cross entropy loss for training  $\theta$  on x in classification tasks. We thus put the binary mask on synthetic data before feeding it into C. We give the details of our method, Masked Distillation Training (MDT), in Algorithm [1.](#page-4-1) MDT is applicable to various distillation backbones. For instance, in Efficient Dataset Distillation  $[53]$ , they set the criterion C as the differences between gradients back-propagated from  $l$  given source data  $B$  and distilled data  $B'$ :

$$
C(B, B'; l, \theta) = \|\nabla_{\theta}\ell(\theta; B) - \nabla_{\theta}\ell(\theta; B')\| \qquad (5)
$$

<span id="page-5-4"></span>Image /page/5/Figure/0 description: The image is a line graph showing the accuracy of two models, DDNN and Ours, as a function of the percentage of singular values dropped. The x-axis is labeled "Percentage of Singular Values Dropped" and ranges from 0 to 40. The y-axis is labeled "Accuracy (%)" and ranges from 40 to 65. The DDNN model is represented by a red line with circular markers, and the Ours model is represented by a blue line with circular markers. Both lines show a decreasing trend in accuracy as the percentage of singular values dropped increases. At 0% dropped, DDNN has an accuracy of approximately 64% and Ours has an accuracy of approximately 62%. At 10% dropped, DDNN has an accuracy of approximately 58% and Ours has an accuracy of approximately 57%. At 20% dropped, DDNN has an accuracy of approximately 50% and Ours has an accuracy of approximately 53%. At 30% dropped, DDNN has an accuracy of approximately 45.5% and Ours has an accuracy of approximately 47%. At 40% dropped, DDNN has an accuracy of approximately 41% and Ours has an accuracy of approximately 42%.

<span id="page-5-2"></span>Figure 5. Model trained on MDT (Ours) distilled data suffers from less accuracy drop than the model (DDNN) trained on MTT distilled data when dropping major singular values during SVD reconstruction of DD, showing that ours alleviates the issue of condensed information of distilled data.

Image /page/5/Figure/2 description: This image displays a grid of scatter plots comparing two methods, 'Ours' and 'DDNN', across four different configurations: 'conv0', 'conv1', 'conv2', and 'linear'. Each scatter plot visualizes data points colored in red, blue, green, purple, and orange, representing different clusters. The 'Ours' method shows a more dispersed distribution of points in 'conv0' and 'conv1', with increasing separation into distinct clusters in 'conv2' and a clear separation of five clusters in 'linear'. The 'DDNN' method also shows dispersed points in 'conv0' and 'conv1', with better clustering in 'conv2' and a clear separation of five clusters in 'linear', similar to the 'Ours' method but with slightly different spatial arrangements of the clusters.

<span id="page-5-3"></span>Figure 6. T-SNE projections of feature vectors from each layer of a 4-block ConvNet trained on original distilled data and Ours. The model trained on Ours better encodes source information than the original DDNN, as visualized by the features that are hardly separated in layer conv2.

When applied with MDT, this now becomes:

$$
C(B, B'; l, \theta) = \|\nabla_{\theta}\ell(\theta; B) - \nabla_{\theta}\ell(\theta; Mask(B', r))\|
$$
\n(6)

In another distillation backbone MTT  $[3]$ , the criterion C measures parameter trajectory differences between DDNNs and FDNNs:

$$
\hat{\theta}_{t+N} \leftarrow \hat{\theta}_{t+N-1} - \lambda \nabla l \left( B', \theta_{t+N-1} \right) C \left( B', \hat{\theta}; l, \theta \right) = \left\| \hat{\theta}_{t+N} - \theta_{t+M}^* \right\|_2^2 / \left\| \theta_t^* - \theta_{t+M}^* \right\|_2^2
$$
(7)

and when applied with MDT, this becomes:

$$
\hat{\theta}_{t+N} \leftarrow \hat{\theta}_{t+N-1} - \lambda \nabla l \left( Mask\left(B', r\right), \theta_{t+N-1} \right). \tag{8}
$$

We find that a masking ratio of 10% works well for our purposes while losing minimal test accuracy, and we provide more details in Sections [5.3](#page-6-0) and [5.5.](#page-7-1)

## 4.3. Connection to Dropouts

Dropout [\[36\]](#page-10-23) is a common practice to prevent overfitting of neural networks. Two popular types are unit dropout (U-DP) and weight dropout (W-DP), which randomly discard

Image /page/5/Figure/13 description: This image contains two histograms side-by-side, both plotting "Percentage" on the y-axis against "Maximum Value of Network Logits" on the x-axis. The left histogram shows data for CIFAR10, and the right histogram shows data for CIFAR100. Both histograms display four distributions labeled "FDNN", "DDNN", "Ours (r=0.3)", and "Ours (r=0.5)". In the left histogram (CIFAR10), the "FDNN" distribution peaks around 12, "DDNN" peaks around 14, "Ours (r=0.3)" peaks around 8, and "Ours (r=0.5)" peaks around 6. In the right histogram (CIFAR100), the "FDNN" distribution peaks around 14, "DDNN" peaks around 16, "Ours (r=0.3)" peaks around 9, and "Ours (r=0.5)" peaks around 7. The y-axis ranges from 0.00 to 0.35, and the x-axis ranges from 5 to 25 in the left histogram and 5 to 30 in the right histogram.

<span id="page-5-1"></span>(a) Maximum Logits on CIFAR10 (b) Maximum Logits on CIFAR100 Figure 7. Histogram of maximum logits of DDNNs (Ours) on CI-FAR10 and CIFAR100. As we increase the ratio of masking in our method, DDNNs produce logits that cover more values, thus becoming more calibratable by after-training calibration methods.

units (neurons) and individual weights at each training step, respectively. The formulas are shown in Eq [\(9\)](#page-5-0).

<span id="page-5-0"></span>U-DP: 
$$
Y = (X \odot M)W
$$
; W-DP:  $Y = X(W \odot M)$ , (9)

where  $M$  denotes dropout mask and  $W$  refers to weights.

Our proposed Masked Distillation Training can be viewed as a new version of dropout on the input, i.e.,  $X = S_c \odot M$ . There are practices using dropout on inputs as data augmentation [\[2\]](#page-9-23). In contrast to existing efforts, we apply masking in distillation backbones on synthetic data during their forward passes. Masking some of the synthetic data makes it harder to collect easily reachable information from the source dataset, and thus forces the distillation to focus on other structurally and semantically meaningful information that has not received sufficient attention in previous data distillation.

## 5. Experiments

### 5.1. Experiment Setup

We thoroughly evaluate our proposed MTS and MDT on different dataset distillation setups and compare them with existing calibration methods. Dataset Distillation Backbones: We follow the exact settings in MTT [\[3\]](#page-9-4), RTP [\[7\]](#page-9-0), DC [\[56\]](#page-10-1) and DSA [\[55\]](#page-10-2). Our experiments are based on 4 benchmark datasets: CIFAR10 & CIFAR100 [\[17\]](#page-9-12), Tiny ImageNet [\[18\]](#page-9-24), and ImageNette (a subset of ImageNet) [\[12\]](#page-9-25). We mainly set image-per-class to larger values, e.g. 50 in MTT, and results on different IPCs are provided in supplement materials. Calibration Methods: We compare our method with existing calibration methods including Temperature Scaling (TS) [\[10\]](#page-9-6), mixup (MX) [\[50\]](#page-10-7), Label Smoothing (LS) [\[49\]](#page-10-15), and Focal Loss (FL) [\[21\]](#page-9-10). Implementations: For TS, we use an initial temperature of 1.5 and LBFGS [\[22\]](#page-9-26) optimizer with a learning rate of 0.02. For MX, we use a  $\beta$  distribution with  $\alpha = 1.0$  for the mixup ratio. For LS, we set  $\epsilon = 0.1$ . For FL, we set  $\gamma = 1$ ., which calibrates better on DDNNs than the best value 2 reported in

<span id="page-6-4"></span><span id="page-6-1"></span>Table 2. ECE (%) of different calibration methods on distilled datasets trained with our methods. Tiny: Tiny ImageNet. Nette: Nette subset of ImageNet. Our results are in shadow .

| Dataset  | Best of Others   | MDT        | MTS               | MDT + MTS         |
|----------|------------------|------------|-------------------|-------------------|
| CIFAR10  | 3.64 ± 0.2 (TS)  | 3.66 ± 0.3 | <b>1.20 ± 0.3</b> | 2.50 ± 0.5        |
| CIFAR100 | 5.95 ± 0.4 (Raw) | 4.65 ± 0.3 | <b>2.18 ± 0.2</b> | <b>2.00 ± 0.5</b> |
| Tiny     | 2.42 ± 0.3 (MX)  | 7.44 ± 1.4 | <b>2.26 ± 0.3</b> | 5.91 ± 1.4        |
| Nette    | 4.85 ± 0.6 (TS)  | 7.32 ± 1.7 | <b>4.78 ± 0.5</b> | 5.14 ± 1.2        |

Table 3. ECE  $(\%)$  of MDT with dynamically sampled r and MTS on CIFAR10, MTT with different IPCs.

<span id="page-6-2"></span>

| $IPC$ $MDTds$ | <b>MTS</b> | $MDTds + MTS$                                                                                                                          |
|---------------|------------|----------------------------------------------------------------------------------------------------------------------------------------|
|               |            | $\begin{array}{c cc}\n10 & 1.79 \pm 0.9 & 1.36 \pm 0.4 & 1.13 \pm 0.2 \\ 50 & 5.10 \pm 0.4 & 1.20 \pm 0.3 & 1.26 \pm 0.2\n\end{array}$ |
|               |            |                                                                                                                                        |

the paper. For our proposed MTS, on distillation backbone MTT, we use a fixed masking ratio of 0.3, 0.3, 0.5, and 0.1 for each of the 4 datasets respectively. On backbones RTP, DSA, and DC, due to their inferior performance in accuracy, we use a more aggressive masking ratio of 0.8.

Since the number of examples of distillation data is usually limited, we draw 10% of all distillation data as the validation set for the after-training method, as in other existing work. The experiments are repeated five times, and the mean and standard deviation are reported. More experimental setups are available in supplementary materials.

### 5.2. Empirical Analysis of MTS

We show in Figure [1](#page-0-0) that our proposed method is able to reduce the ECE (red bars) to almost zero for each confidence bin when using MTT as the distillation backbone on CIFAR10 and CIFAR100. Although traditional calibration methods such as mixup *can* perform well, they could also over-calibrate and result in under-confident networks. We visualize the under-confidence in Figure [1,](#page-0-0) in which the red bars are enlarged and switched from left to right in each bin. Additional calibration results are reported in Table [1,](#page-4-0) and our proposed MTS gives the best numerical ECE results in almost all settings. In real-world settings where no mistakes are allowed, traditional methods are regarded as unsafe due to their potential over-calibration.

As a contrast, we propose masked temperature scaling, which not only has better performance but does not show any lack of confidence in the results at all and is therefore considered a safer choice.

## <span id="page-6-0"></span>5.3. Empirical Analysis of MDT

We show that MDT improves the calibration results. As reported in Tables [2](#page-6-1)[-3,](#page-6-2) applying MDT alone or combining it with MTS yields comparable or better calibration performance. WE note that naively combining MDT + MTS may increase ECE due to DDNNs overfitting to the fixed masking ratio in MDT, then being over-calibrated by MTS. Thus

Image /page/6/Figure/11 description: This image contains two plots side-by-side, labeled (a) CIFAR10 and (b) CIFAR100. Both plots show the relationship between Mask Ratio r (%) on the x-axis and ECE (%) on the y-axis. There are four lines in each plot, representing MTT, DSA, DC, and RTP. The lines are colored red, olive green, teal, and blue, respectively. Shaded regions around each line indicate uncertainty. In plot (a), the ECE values range from approximately 0% to 22%. In plot (b), the ECE values range from approximately 0% to 28%. The legend in plot (a) shows MTT, DSA, DC, and RTP. The legend in plot (b) shows MTT, DSA (IPC=10), DC (IPC=10), and RTP.

<span id="page-6-3"></span>Figure 8. Effects on ECE  $(\%)$  of different masking ratios r in our method. For DD methods with better performance reported (MTT, RTP on CIFAR10), our method is robust to  $r$  and saves efforts in fine-tuning. For DD methods with inferior performance (DC, DSA, RTP on CIFAR100), a more aggressive masking ratio  $(r >$ 0.7) could still calibrate reasonably well.

we further improve (Table  $3$ ) MDT + MTS by dynamically sampling the r in MDT from 0 to 0.1 (denoted MDT<sup>ds</sup>) so the resulting DDNNs are more calibratable. We fix  $r$  in MTS due to the limited amount of validation data in DD. This indicates that our proposed MDT produces more robust and calibratable DDNNs than the original backbone when sufficient computational resources are available to train the distillation process from the beginning. We use MTT as the distillation backbone.

We find that MDT gives comparable model accuracy albeit altering the distillation process. With a 10% zero masking during the distillation process, MDT only leads to a loss of as large as 1.26% in DDNNs' accuracy on CIFAR100 and as low as 0.14% on Tiny ImageNet. As reported in Table [4,](#page-7-2) this is even better than traditional during-training calibration methods such as mixup, label smoothing, and focal loss that lead to different model results.

This suggests that MDT yields better calibration potential at a negligible performance cost, which is desirable in an environment where security is a major concern [\[6,](#page-9-9) [33\]](#page-10-6).

## 5.4. Enabling Calibratable DDNNs

In response to the discussion of the after-training behavior of DDNNs in Section [3.1,](#page-2-3) we examined improvements in DDNN calibrability. On the validation data for Temperature Scaling, we apply zero-masking with ratio  $r = 10\%$ , 20%, and 30% to see its effects on resulting logit distributions of DDNNs. We show in the bottom-right of Figure [2\(b\)](#page-2-1) that our MDT produces lower probabilities on OOD samples, leading to more distinguishable logits and more calibratable DDNNs than before. We also show in Figure [7](#page-5-1) that DDNNs given these mask-perturbed data will produce similarly diverse logits as if they are processing normal full data, allowing masked temperature scaling to better calibrate DDNNs with similar good performance on FDNNs.

| <b>Dataset</b> | <b>Raw</b>      | <b>MX</b>       | <b>LS</b>       | <b>FL</b>       | <b>Ours</b>                       |
|----------------|-----------------|-----------------|-----------------|-----------------|-----------------------------------|
| CIFAR10        | $70.48 pm 0.2$ | $65.50 pm 0.5$ | $67.42 pm 0.5$ | $68.79 pm 0.5$ | <b><math>69.98 pm 0.4</math></b> |
| CIFAR100       | $47.47 pm 0.2$ | $39.65 pm 0.3$ | $47.02 pm 0.2$ | $46.79 pm 0.4$ | $46.21 pm 0.4$                   |
| Tiny ImageNet  | $27.76 pm 0.2$ | $21.48 pm 0.4$ | $25.76 pm 0.3$ | $27.42 pm 0.3$ | <b><math>27.62 pm 0.4</math></b> |
| ImageNette     | $63.04 pm 1.3$ | $55.60 pm 1.0$ | $63.40 pm 0.9$ | $61.32 pm 0.9$ | $62.80 pm 1.2$                   |

<span id="page-7-5"></span><span id="page-7-2"></span>Table 4. Accuracy (%) of different during-training calibration methods on MTT distilled datasets. While all during-training calibration methods lead to a loss in accuracy, ours loses only as small as 0.14% at a masking ratio of 10%. Our results are in shadow .

## <span id="page-7-1"></span>5.5. Enhancing Semantic Information of DDNNs

We investigate whether the semantic information of DD is enhanced according to the discussion in Section [3.2.](#page-3-2) As shown in Figure [5,](#page-5-2) when trained with MDT, our DDNNs start with a little lower accuracy than the normal MTT model. However, as we gradually drop more singular values following Eqs  $(2)-(3)$  $(2)-(3)$  $(2)-(3)$ , the accuracy of the MDT model drops slower and even stays higher than the accuracy of the MTT models. This indicates that MDT distillation effectively retains more semantically meaningful information than normal distillation does, making MDT distilled data more difficult to be decomposed by SVD.

## 5.6. Improving Encoding Capacity of DDNNs

We study the improvement in the feature encoding capability of the DDNNs, responding to the discussion of DDNN behavior during training in Section [3.3.](#page-3-1) We experiment on the distillation backbone MTT, in which they collect network parameter trajectories from training on synthetic data in each iteration. We apply masked distillation training with masking ratio  $= 10\%$  on the synthetic data before the forward pass in each iteration.

We show in Figure [6](#page-5-3) that the hidden layers in the MDT model form larger clusters than the original MTT model, and that the clusters in each category are more intertwined with each other, retaining more information from the complete dataset and forming better feature vectors as desired.

<span id="page-7-0"></span>

## 6. Ablation Studies

Analysis of Mask Ratio  $r$  in MTS. We analyze the effects of mask ratio  $r$  on the calibration results of MTS. We set  $r$ from 0.1 to 0.9, increasing by 0.1. As shown in Figure [8,](#page-6-3) on CIFAR10 and CIFAR100, MTS works well for most of the possible  $r$  ranging from 0.1 to 0.5, indicating that MTS can be tuned with minimal effort. On variants of the ImageNet dataset, however, we find that 0.3 works best for Tiny ImageNet, and 0.5 for ImageNet Subset. This is probably due to the large number of classes in these more complex datasets, as well as the relatively low accuracy of their corresponding distilled datasets.

Analysis of Validation Set Size for MTS. We study the impact of how much data is drawn from all the distilled data as validation data for MTS. We denote  $N$  as the propor-

Image /page/7/Figure/10 description: This figure contains two plots, (a) CIFAR10 and (b) CIFAR100, showing the ECE (%) on the y-axis against the Size of Validation Data (as in % of Training Data) on the x-axis, ranging from 10 to 90. Both plots display four lines representing different methods: MTT (red), DSA (green), DC (teal), and RTP (blue). In plot (a), MTT is around 1.5%, DSA is around 2.5%, DC is around 9.8%, and RTP is around 2.5%. In plot (b), MTT is around 1.5%, DSA is around 4.5%, DC is around 6.2%, and RTP is around 10.5%. Shaded regions around each line indicate uncertainty or variance.

<span id="page-7-3"></span>Figure 9. Effects on ECE (%) of different sizes of the validation data  $(N, as in \%)$  of total training data) in our method. On different distillation backbones, a small  $N$  gives identical calibration performance to a larger  $N$ , indicating that our method is also applicable in scenarios with extremely scarce validation data.

<span id="page-7-4"></span>Table 5. ECE (%) of different calibration methods with IPC=1. Under this extreme compression rate, our method still outperforms other calibration methods. Our results are in shadow .

| Dataset  | Raw         | MX         | LS          | FL         | Ours              |
|----------|-------------|------------|-------------|------------|-------------------|
| CIFAR10  | 10.15 ± 1.2 | 8.40 ± 1.1 | 12.79 ± 0.6 | 2.05 ± 0.9 | <b>1.81 ± 0.7</b> |
| CIFAR100 | 2.46 ± 0.6  | 4.45 ± 0.5 | 8.89 ± 0.6  | 3.24 ± 0.9 | <b>2.19 ± 0.5</b> |

tion we sample, and we set  $N$  from 10% to 50%, increased by 10%. We can see in Figure [9](#page-7-3) that the number of samples has little effect on calibration results. We hypothesize that this is due to we only update the temperature parameter  $T$  for only one step, thus not being affected by the number of examples in this step. This also indicates that MTS can be applied when we have only a small amount of data available, such as distillation with IPC=1 or medical image analysis scenarios [\[23,](#page-9-27) [4,](#page-9-28) [35\]](#page-10-24).

Calibration in Lower Accuracy Settings. Our method outperforms other calibration methods at DD settings with extreme compression ratio, i.e. only 1 synthetic image for each label. This means traditional temperature scaling no longer applies because it requires additional validation data. As reported in Table [5,](#page-7-4) while other calibration methods over-calibrate or don't work at all, ours still produces better results, indicating its generality to various DD settings.

Comparison of Effects of Different During-Training Calibration Methods on DDNNs' Encoding Capacity. We provide more visualizations of projections of intermediate feature vectors obtained from DDNNs trained with difFigure 10. Ours (MTS) better calibrates DDNNs across different IPC in MTT. Left: CIFAR10. Right: CIFAR100.

Image /page/8/Figure/1 description: Two line graphs are shown side-by-side, both plotting ECE (%) on the y-axis against Image Per Class on the x-axis. The x-axis ranges from 1 to 50, with tick marks at 1, 10, 30, and 50. The y-axis ranges from 0 to 25, with tick marks every 5 units. Both graphs display five lines representing different methods: FL (pink), MX (red), LS (teal), TS (light blue), and Ours (green). The left graph shows that the 'Ours' method starts at approximately 1% ECE at 1 Image Per Class, drops to near 0% at 10 Images Per Class, rises to about 5.5% at 30 Images Per Class, and drops back to near 0% at 50 Images Per Class. The other methods generally show increasing ECE with more images per class, with LS having the highest ECE. The right graph shows a similar trend for the 'Ours' method, staying consistently low between 2% and 4% ECE across all tested Image Per Class values. The other methods in the right graph also show generally increasing ECE with more images per class, with LS again having the highest ECE.

<span id="page-8-2"></span>Figure 11. The more calibratable FDNN outputs more evenly distributed logits, while the less calibratable DDNN outputs a more concentrated logit distribution.

<span id="page-8-1"></span>Image /page/8/Figure/3 description: This is a histogram showing the distribution of the maximum value of network logits for two different neural network architectures, FDNN and DDNN, on the CIFAR 100 dataset. The x-axis represents the maximum value of network logits, ranging from 5 to 30. The y-axis represents the percentage, ranging from 0.00 to 0.35. The FDNN distribution is shown in blue and is centered around a logit value of approximately 14, with a wider spread. The DDNN distribution is shown in red and is more sharply peaked around a logit value of approximately 15, indicating a more concentrated distribution of maximum logit values.

ferent during-training calibration methods. The methods we use are mixup, focal loss, and label smoothing, in addition to the original training with cross-entropy loss. We can see in Figure [12](#page-8-0) that our proposed during-training calibration MDT alleviates the issue of concentrate features for all the traditional methods used, giving better encoding potentials of DDNNs for transfer learning tasks, which leads to more calibratable DDNNs.

More Results on CIFAR100: ECE on different IPCs, max logits. We show in Figure [10](#page-8-1) that our MTS outperforms others in ECE on different IPCs. In the main paper, we mainly present IPC = 10 on Tiny-ImageNet  $&$  Subsets with MTT, 10 on CIFAR100 with DC/DSA (released), and 50 on others. These DD settings have higher accuracy and would better represent real-world settings.

We also provide visualization of maximum logits of DDNN on original MTT in Figure [11,](#page-8-2) in addition to the results on CIFAR10 in our main paper.

## 7. Conclusion

In this paper, we find for the first time that networks trained on distillation data are not calibratable and have poor encoding ability because the distillation process focuses on the classification task while discarding other semantically meaningful information. Our proposed methods, namely Masked Distillation Training during training and Masked Temperature Scaling after training, effectively alleviate these limitations and make the DDNNs recalibrated.

In future work, we will look for better distillation methods that retain most of the source information and lead directly to calibratable networks. In addition, beyond calibrat-

Image /page/8/Figure/10 description: This image displays a series of scatter plots organized into four main sections, each representing a different training strategy: (b) Mixup, (c) Label Smoothing, (d) Focal Loss, and (e) No Calibration (Original Cross-Entropy). Within each section, there are two rows labeled 'Ours' and 'DDNN', and four columns labeled 'conv0', 'conv1', 'conv2', and 'linear'. Each scatter plot visualizes data points colored according to their class, with points clustered together to represent learned representations. The plots show how different training methods affect the separability of classes at various stages of a neural network (conv0, conv1, conv2) and in the final linear layer. Generally, the 'linear' column shows well-separated clusters of points for all methods, indicating successful classification. The earlier layers (conv0, conv1) show more dispersed points, with some methods like Focal Loss and No Calibration showing slightly better initial separation compared to Mixup and Label Smoothing.

<span id="page-8-0"></span>(e) No Calibration (Original Cross-Entropy)

Figure 12. T-SNE projections of feature vectors from each layer of a 4-block ConvNet trained with mixup, label smoothing, focal loss, and the original cross-entropy on distilled CIFAR10. In each training method, applying our proposed MDT (Ours) helps the network encode more source information in intermediate layers, as visualized by the rich features not separated until the last layer. The original DDNN poorly encodes source information, as shown by the feature projections already separated in layer conv2.

ing DDNNs on in-distribution data, we will rethink DDNNs in terms of more general reliability, i.e., out-of-distribution detection, robust generalization, and adaptation, which are important properties for the safety of DDNN applications.

## References

- <span id="page-9-8"></span>[1] Moloud Abdar, Farhad Pourpanah, Sadiq Hussain, Dana Rezazadegan, Li Liu, Mohammad Ghavamzadeh, Paul Fieguth, Xiaochun Cao, Abbas Khosravi, U Rajendra Acharya, et al. A review of uncertainty quantification in deep learning: Techniques, applications and challenges. *Information Fusion*, 76:243–297, 2021. [1](#page-0-1)
- <span id="page-9-23"></span>[2] Xavier Bouthillier, Kishore Konda, Pascal Vincent, and Roland Memisevic. Dropout as data augmentation. *arXiv preprint arXiv:1506.08700*, 2015. [6](#page-5-4)
- <span id="page-9-4"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-1) [2,](#page-1-0) [4,](#page-3-5) [6,](#page-5-4) [12](#page-11-0)
- <span id="page-9-28"></span>[4] Xuxin Chen, Ximin Wang, Ke Zhang, Kar-Ming Fung, Theresa C Thai, Kathleen Moore, Robert S Mannel, Hong Liu, Bin Zheng, and Yuchen Qiu. Recent advances and clinical applications of deep learning in medical image analysis. *Medical Image Analysis*, page 102444, 2022. [8](#page-7-5)
- <span id="page-9-14"></span>[5] Jiacheng Cheng and Nuno Vasconcelos. Calibrating deep neural networks by pairwise constraints. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 13709–13718, 2022. [3](#page-2-5)
- <span id="page-9-9"></span>[6] Lavindra De Silva and Alan Mycroft. Toward trustworthy programming for autonomous concurrent systems. *AI & SO-CIETY*, pages 1–3, 2022. [1,](#page-0-1) [7](#page-6-4)
- <span id="page-9-0"></span>[7] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *Neural Information Processing Systems (NeurIPS)*, 2022. [1,](#page-0-1) [2,](#page-1-0) [6,](#page-5-4) [12](#page-11-0)
- <span id="page-9-21"></span>[8] Leon A Gatys, Alexander S Ecker, and Matthias Bethge. Image style transfer using convolutional neural networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 2414–2423, 2016. [4](#page-3-5)
- <span id="page-9-15"></span>[9] Biraja Ghoshal and Allan Tucker. On calibrated model uncertainty in deep learning. *arXiv preprint arXiv:2206.07795*, 2022. [3](#page-2-5)
- <span id="page-9-6"></span>[10] Chuan Guo, Geoff Pleiss, Yu Sun, and Kilian Q Weinberger. On calibration of modern neural networks. In *International conference on machine learning*, pages 1321–1330. PMLR, 2017. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-5) [5,](#page-4-2) [6,](#page-5-4) [12](#page-11-0)
- <span id="page-9-20"></span>[11] ER Henry and J Hofrichter. [8] singular value decomposition: Application to analysis of experimental data. In *Methods in enzymology*, volume 210, pages 129–192. Elsevier, 1992. [4](#page-3-5)
- <span id="page-9-25"></span>[12] Jeremy Howard. Imagenette: A smaller subset of 10 easily classified classes from imagenet, March 2019. [6,](#page-5-4) [12](#page-11-0)
- <span id="page-9-22"></span>[13] Justin Johnson, Alexandre Alahi, and Li Fei-Fei. Perceptual losses for real-time style transfer and super-resolution. In *Computer Vision–ECCV 2016: 14th European Conference, Amsterdam, The Netherlands, October 11-14, 2016, Proceedings, Part II 14*, pages 694–711. Springer, 2016. [4](#page-3-5)
- <span id="page-9-3"></span>[14] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-

data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022. [1](#page-0-1)

- <span id="page-9-18"></span>[15] Virginia Klema and Alan Laub. The singular value decomposition: Its computation and some applications. *IEEE Transactions on automatic control*, 25(2):164–176, 1980. [4](#page-3-5)
- <span id="page-9-16"></span>[16] Ranganath Krishnan and Omesh Tickoo. Improving model calibration with accuracy versus uncertainty optimization. *Advances in Neural Information Processing Systems*, 33:18237–18248, 2020. [3](#page-2-5)
- <span id="page-9-12"></span>[17] Alex Krizhevsky and Geoffrey Hinton. Learning multiple layers of features from tiny images. Technical report, Citeseer, 2009. [2,](#page-1-0) [6,](#page-5-4) [12](#page-11-0)
- <span id="page-9-24"></span>[18] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [6,](#page-5-4) [12](#page-11-0)
- <span id="page-9-19"></span>[19] Seung Hyun Lee, Dae Ha Kim, and Byung Cheol Song. Selfsupervised knowledge distillation using singular value decomposition. In *Proceedings of the European Conference on Computer Vision (ECCV)*, pages 335–350, 2018. [4](#page-3-5)
- <span id="page-9-17"></span>[20] Bowen Lei, Ruqi Zhang, Dongkuan Xu, and Bani Mallick. Calibrating the rigged lottery: Making all tickets reliable. *arXiv preprint arXiv:2302.09369*, 2023. [3](#page-2-5)
- <span id="page-9-10"></span>[21] Tsung-Yi Lin, Priya Goyal, Ross Girshick, Kaiming He, and Piotr Dollár. Focal loss for dense object detection. In *Proceedings of the IEEE international conference on computer vision*, pages 2980–2988, 2017. [2,](#page-1-0) [3,](#page-2-5) [6](#page-5-4)
- <span id="page-9-26"></span>[22] Dong C Liu and Jorge Nocedal. On the limited memory bfgs method for large scale optimization. *Mathematical programming*, 45(1-3):503–528, 1989. [6](#page-5-4)
- <span id="page-9-27"></span>[23] Tianming Liu, Eliot Siegel, and Dinggang Shen. Deep learning and medical image analysis for covid-19 diagnosis and prediction. *Annual Review of Biomedical Engineering*, 24:179–201, 2022. [8](#page-7-5)
- <span id="page-9-5"></span>[24] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-9-13"></span>[25] Jishnu Mukhoti, Viveka Kulharia, Amartya Sanyal, Stuart Golodetz, Philip Torr, and Puneet Dokania. Calibrating deep neural networks using focal loss. *Advances in Neural Information Processing Systems*, 33:15288–15299, 2020. [3](#page-2-5)
- <span id="page-9-7"></span>[26] Rafael Müller, Simon Kornblith, and Geoffrey E Hinton. When does label smoothing help? *Advances in neural information processing systems*, 32, 2019. [1](#page-0-1)
- <span id="page-9-29"></span>[27] Vinod Nair and Geoffrey E Hinton. Rectified linear units improve restricted boltzmann machines. In *Proceedings of the 27th international conference on machine learning (ICML-10)*, pages 807–814, 2010. [12](#page-11-0)
- <span id="page-9-1"></span>[28] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-9-2"></span>[29] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-9-11"></span>[30] Aaron van den Oord, Yazhe Li, and Oriol Vinyals. Representation learning with contrastive predictive coding. *arXiv preprint arXiv:1807.03748*, 2018. [2](#page-1-0)

- <span id="page-10-5"></span>[31] Yaniv Ovadia, Emily Fertig, Jie Ren, Zachary Nado, David Sculley, Sebastian Nowozin, Joshua Dillon, Balaji Lakshminarayanan, and Jasper Snoek. Can you trust your model's uncertainty? evaluating predictive uncertainty under dataset shift. *Advances in neural information processing systems*, 32, 2019. [1](#page-0-1)
- <span id="page-10-16"></span>[32] John Platt et al. Probabilistic outputs for support vector machines and comparisons to regularized likelihood methods. *Advances in large margin classifiers*, 10(3):61–74, 1999. [3](#page-2-5)
- <span id="page-10-6"></span>[33] Khansa Rasheed, Adnan Qayyum, Mohammed Ghaly, Ala Al-Fuqaha, Adeel Razi, and Junaid Qadir. Explainable, trustworthy, and ethical machine learning for healthcare: A survey. *Computers in Biology and Medicine*, page 106043, 2022. [1,](#page-0-1) [7](#page-6-4)
- <span id="page-10-26"></span>[34] Edgar Riba, Dmytro Mishkin, Daniel Ponsa, Ethan Rublee, and Gary Bradski. Kornia: an open source differentiable computer vision library for pytorch. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 3674–3683, 2020. [12](#page-11-0)
- <span id="page-10-24"></span>[35] Zohaib Salahuddin, Henry C Woodruff, Avishek Chatterjee, and Philippe Lambin. Transparency of deep neural networks for medical image analysis: A review of interpretability methods. *Computers in biology and medicine*, 140:105111, 2022. [8](#page-7-5)
- <span id="page-10-23"></span>[36] Nitish Srivastava, Geoffrey Hinton, Alex Krizhevsky, Ilya Sutskever, and Ruslan Salakhutdinov. Dropout: a simple way to prevent neural networks from overfitting. *The journal of machine learning research*, 15(1):1929–1958, 2014. [5,](#page-4-2) [6](#page-5-4)
- <span id="page-10-12"></span>[37] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pages 1–8. IEEE, 2021. [2](#page-1-0)
- <span id="page-10-8"></span>[38] Sunil Thulasidasan, Gopinath Chennupati, Jeff A Bilmes, Tanmoy Bhattacharya, and Sarah Michalak. On mixup training: Improved calibration and predictive uncertainty for deep neural networks. *Advances in Neural Information Processing Systems*, 32, 2019. [2,](#page-1-0) [3](#page-2-5)
- <span id="page-10-25"></span>[39] Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*, 2016. [12](#page-11-0)
- <span id="page-10-9"></span>[40] Aaron Van Den Oord, Oriol Vinyals, et al. Neural discrete representation learning. *Advances in neural information processing systems*, 30, 2017. [2](#page-1-0)
- <span id="page-10-20"></span>[41] Deng-Bao Wang, Lei Feng, and Min-Ling Zhang. Rethinking calibration of deep neural networks: Do not be afraid of overconfidence. *Advances in Neural Information Processing Systems*, 34:11809–11820, 2021. [3](#page-2-5)
- <span id="page-10-10"></span>[42] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 12196–12205, June 2022. [2,](#page-1-0) [4](#page-3-5)
- <span id="page-10-0"></span>[43] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-10-19"></span>[44] Hongxin Wei, Renchunzi Xie, Hao Cheng, Lei Feng, Bo An, and Yixuan Li. Mitigating neural network overconfidence

with logit normalization. In *International Conference on Machine Learning*, pages 23631–23644. PMLR, 2022. [3](#page-2-5)

- <span id="page-10-17"></span>[45] Longfeng Wu, Bowen Lei, Dongkuan Xu, and Dawei Zhou. Towards reliable rare category analysis on graphs via individual calibration. *arXiv preprint arXiv:2307.09858*, 2023. [3](#page-2-5)
- <span id="page-10-21"></span>[46] Jian Xue, Jinyu Li, and Yifan Gong. Restructuring of deep neural network acoustic models with singular value decomposition. In *Interspeech*, pages 2365–2369, 2013. [4](#page-3-5)
- <span id="page-10-22"></span>[47] Jian Xue, Jinyu Li, Dong Yu, Mike Seltzer, and Yifan Gong. Singular value decomposition based low-footprint speaker adaptation and personalization for deep neural network. In *2014 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)*, pages 6359–6363. IEEE, 2014. [4](#page-3-5)
- <span id="page-10-13"></span>[48] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023. [2](#page-1-0)
- <span id="page-10-15"></span>[49] Li Yuan, Francis EH Tay, Guilin Li, Tao Wang, and Jiashi Feng. Revisiting knowledge distillation via label smoothing regularization. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3903– 3911, 2020. [3,](#page-2-5) [6](#page-5-4)
- <span id="page-10-7"></span>[50] Hongyi Zhang, Moustapha Cisse, Yann N Dauphin, and David Lopez-Paz. mixup: Beyond empirical risk minimization. *arXiv preprint arXiv:1710.09412*, 2017. [2,](#page-1-0) [3,](#page-2-5) [6](#page-5-4)
- <span id="page-10-11"></span>[51] Jie Zhang, Chen Chen, and Lingjuan Lyu. Ideal: Queryefficient data-free learning from black-box models. In *The Eleventh International Conference on Learning Representations*, 2022. [2](#page-1-0)
- <span id="page-10-3"></span>[52] Jie Zhang, Chen Chen, Weiming Zhuang, and Lingjuan Lv. Addressing catastrophic forgetting in federated classcontinual learning. *arXiv preprint arXiv:2303.06937*, 2023. [1](#page-0-1)
- <span id="page-10-4"></span>[53] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. *arXiv preprint arXiv:2212.06152*, 2022. [1,](#page-0-1) [5](#page-4-2)
- <span id="page-10-18"></span>[54] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 11950–11959, 2023. [3](#page-2-5)
- <span id="page-10-2"></span>[55] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [1,](#page-0-1) [2,](#page-1-0) [6,](#page-5-4) [12](#page-11-0)
- <span id="page-10-1"></span>[56] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [1,](#page-0-1) [2,](#page-1-0) [6,](#page-5-4) [12](#page-11-0)
- <span id="page-10-14"></span>[57] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022. [2](#page-1-0)

<span id="page-11-1"></span><span id="page-11-0"></span>Table 6. Accuracy (%) drops by as much as 1.68% when training with 90% of distilled Nette (a subset of ImageNet). The rest 10% is used in temperature scaling (TS). Our proposed aftertraining MTS ( shadow ) keeps the original accuracy. Our proposed during-training MDT ( shadow ) keeps a higher accuracy than that of dropping 10% of training data for TS. We use MTT [\[3\]](#page-9-4) as the distillation backbone.

| Dataset       | Full, MTS (Ours) | $TS(10\%)$      | MDT (Ours)      |
|---------------|------------------|-----------------|-----------------|
| CIFAR10       | $70.48 \pm 0.2$  | $69.78 \pm 0.5$ | $69.98 \pm 0.4$ |
| CIFAR100      | $47.47 + 0.2$    | $47.10 \pm 0.2$ | $46.21 + 0.4$   |
| Tiny ImageNet | $27.76 + 0.2$    | $27.35 + 0.2$   | $27.62 + 0.4$   |
| ImageNette    | $63.04 \pm 1.3$  | $61.36 \pm 1.6$ | $62.80 + 1.2$   |

## A. Distillation Backbones

### A.1. Datasets and Networks

Following  $[56, 55, 3, 7]$  $[56, 55, 3, 7]$  $[56, 55, 3, 7]$  $[56, 55, 3, 7]$  $[56, 55, 3, 7]$  $[56, 55, 3, 7]$  $[56, 55, 3, 7]$ , we use a ConvNet with 3 blocks for CIFAR10 and CIFAR100 [\[17\]](#page-9-12), a ConvNet with 4 blocks for Tiny-ImageNet [\[18\]](#page-9-24), and a ConvNet with 5 blocks for Nette (a subset of ImageNet) [\[12\]](#page-9-25). Each block in the ConvNets contains a  $3 \times 3$  convolutional layer with 128 channels, followed by instance normalization [\[39\]](#page-10-25), ReLU [\[27\]](#page-9-29) and a  $2 \times 2$  average pooling layer with stride 2. We apply Kornia ZCA [\[34\]](#page-10-26) on CIFAR10 and CIFAR100 for distillation backbones [\[56,](#page-10-1) [55,](#page-10-2) [3\]](#page-9-4). We pick the ConvNet in each distillation backbone because it gives the best distillation performance while keeping the distillation process under an acceptable time and computational budget.

## B. Additional Experiments

### B.1. Details in Masked Temperature Scaling

We sample from all the distilled data we have as the validation set to update the temperature parameter  $T$  in our proposed Masked Temperature Scaling. Instead of sampling from all the shuffled data at once, we perform a perclass sampling such that there is no missing class or oversampled class, which is especially important for distillation settings that aim for aggressive compression rates such as image-per-class  $\leq 10$ . The traditional temperature scaling [\[10\]](#page-9-6) separates all the data available into a training set and a validation set and uses the validation set only for updating  $T$ . This separated use of the distilled data is not applicable when image-per-class  $= 1$ . Moreover, a data split of 10% can hurt training accuracy by as much as 1.68% on the Nette subset of ImageNet, while our proposed duringtraining calibration method (MDT) only hurts accuracy by 0.24%, as reported in Table [6.](#page-11-1) In addition, our proposed after-training method Masked Temperature Scaling keeps original training accuracy and achieves better calibration results than temperature scaling as reported in our main text.

<span id="page-11-3"></span>Table 7. ECE (%) of different calibration methods on FDNNs. With a low masking ratio  $r$ , our results (shadow) are comparable to temperature scaling and most of the time beats other methods. As our method is specifically designed for DDNNs, in the case of FDNNs where traditional methods are suitable, we can simply convert our method to temperature scaling by setting  $r$  to 0.

| Dataset       | Raw   | TS   | MX    | LS    | FL    | MTS  |
|---------------|-------|------|-------|-------|-------|------|
| CIFAR10       | 4.50  | 0.99 | 14.80 | 11.85 | 1.78  | 2.67 |
| CIFAR100      | 13.05 | 1.41 | 10.69 | 7.17  | 3.49  | 1.84 |
| Tiny ImageNet | 22.26 | 4.95 | 6.34  | 3.29  | 12.55 | 4.93 |
| ImageNette    | 10.90 | 2.81 | 11.22 | 22.24 | 5.21  | 2.87 |

## B.2. More Results on SVD of Distilled Data and Full Data

As we discussed in our main text, distilled data contain more concentrated information that easily gets grouped by algorithms such as SVD. We here illustrate the cumulative explained ratio of top singular values of data distilled by different backbones. We expect that concentrated information leads to a curve skewed to the top left and evenly distributed information leads to a smooth curve close to the diagonal. This will show how much each component corresponding to the singular values in  $\Sigma$  contributes to the data reconstruction. As shown in Figure [13,](#page-11-2) the cumulative explained ratio given by ours grows at the most steady rate, showing that our method produces more evenly distributed information in distilled data compared to the overly condensed information in other distillation backbones. As we concluded in our main text, this serves as a regularization to the distillation process such that it cannot discard too much information that is unrelated to the classification task but semantically meaningful for other tasks, leading to more calibratable networks trained on the resulting distilled data.

Image /page/11/Figure/12 description: The figure displays a line graph illustrating the cumulative explained ratio (%) against the number of top singular value decomposition (SVD) values included (%). The x-axis ranges from 0 to 100, representing the percentage of top SVD values. The y-axis ranges from 0 to 100, representing the cumulative explained ratio. Four lines are plotted, each representing a different method: MTT (purple), DC (olive green), DSA (teal), and Ours (salmon pink). All lines start at (0,0) and rise towards 100%. The DSA line shows the highest cumulative explained ratio for a given percentage of SVD values, followed by DC, then MTT, and finally Ours, which shows the lowest cumulative explained ratio.

<span id="page-11-2"></span>Figure 13. Cumulative *explained ratio*, i.e., percentage of top singular values to  $\sum$  diag ( $\Sigma$ ) in SVD decomposition of distilled CI-FAR10 from different distillation backbones. Ours (red) grows at the most steady rate, indicating its evenly distributed information, compared to others with condensed information.

## B.3. Performance Analysis of FDNNs

We further test MTS on the more calibratable FDNNs. We calibrate networks trained on the full CIFAR10, CI-FAR100, TinyImageNet, and Nette subset of ImageNet. We report the mean of 2 runs due to limited computational resources. As reported in Table [7,](#page-11-3) our method performs comparably with existing well-developed methods. In realistic settings with a large amount of training data, we can set the masking ratio  $r$  to 0, which converts the MTS back to normal temperature scaling.
# <span id="page-0-1"></span>DREAM: Efficient Dataset Distillation by Representative Match<PERSON> <sup>1,2\*</sup> <PERSON><PERSON><PERSON><sup>1,2\*</sup> <PERSON><sup>1†</sup> <PERSON><sup>3</sup> <PERSON><sup>2</sup> <PERSON><sup>1‡</sup><br><sup>1</sup>National University of Singapore <sup>2</sup>Zhejiang University <sup>3</sup>Tsinghua University <sup>1</sup>National University of Singapore  $\frac{2\pi}{2}$  Zhejiang University  $\frac{3\pi}{2}$  Tsinghua University

{yanqing liu, gu jianyang, jiangwei zju}@zju.edu.cn {kai.wang, youy}@comp.nus.edu.sg <EMAIL> Code: <https://github.com/lyq312318224/DREAM>

#### Abstract

*Dataset distillation aims to synthesize small datasets with little information loss from original large-scale ones for reducing storage and training costs. Recent state-ofthe-art methods mainly constrain the sample synthesis process by matching synthetic images and the original ones regarding gradients, embedding distributions, or training trajectories. Although there are various matching objectives, currently the strategy for selecting original images is limited to naive random sampling. We argue that random sampling overlooks the evenness of the selected sample distribution, which may result in noisy or biased matching targets. Besides, the sample diversity is also not constrained by random sampling. These factors together lead to optimization instability in the distilling process and degrade the training efficiency. Accordingly, we propose a novel matching strategy named as Dataset distillation by REpresentAtive Matching (DREAM), where only representative original images are selected for matching. DREAM is able to be easily plugged into popular dataset distillation frameworks and reduce the distilling iterations by more than 8 times without performance drop. Given sufficient training time, DREAM further provides significant improvements and achieves state-of-the-art performances.*

## 1. Introduction

Deep learning has made remarkable achievements in the computer vision society [\[21,](#page-9-0) [12,](#page-8-0) [39,](#page-9-1) [30,](#page-9-2) [46,](#page-9-3) [17,](#page-8-1) [8,](#page-8-2) [59\]](#page-10-0), and the success is closely related to a large amount of efforts in data collection and annotation. But along with the progress of these efforts, the huge amount of data, in turn, becomes a barrier to both storage and training [\[56,](#page-10-1) [20\]](#page-9-4). Many methods are introduced to reduce the scale of datasets [\[51,](#page-10-2) [43,](#page-9-5) [37,](#page-9-6)

<span id="page-0-0"></span>Image /page/0/Figure/10 description: This is a histogram showing the distribution of gradient norms (L2) for different sample sets. The x-axis represents the gradient norm, ranging from 0 to 40, and the y-axis represents the sample number, ranging from 0 to 250. The histogram displays three distributions: 'All samples' (grey), 'Random samples' (blue), and 'DREAM samples' (orange). The 'DREAM samples' distribution has a mean of 6.4 and a standard deviation of 7.1, indicated by a dashed orange line. The 'Random samples' distribution has a mean of 10.0 and a standard deviation of 8.8, indicated by a dashed blue line. The 'DREAM samples' are concentrated at lower gradient norms, while 'Random samples' and 'All samples' show a broader distribution extending to higher gradient norms.

(a) The gradient norm distribution of the plane class in CIFAR10.

Image /page/0/Figure/12 description: The image displays a comparison between two methods, "Random Sampling" and "DREAM", for generating synthetic images. Both methods are visualized on a scatter plot of gray dots, representing a dataset. The "Random Sampling" side shows three red stars, labeled as "Synthetic images", connected by red arrows, indicating a sequence or relationship. Two blue circles, labeled as "Matched original images", are connected to the synthetic images by dashed blue lines. The "DREAM" side also shows three red stars connected by red arrows, with two yellow circles, also labeled as "Matched original images", connected by dashed yellow lines. The "DREAM" method appears to have its synthetic images clustered more closely together and connected to original images that are also closer to the synthetic cluster.

(b) The migration of synthetic samples during training.

Figure 1: Samples on the decision boundaries usually provide larger gradients, which biases the gradient matching optimization. Random sampling (left) overlooks the evenness of of the selected sample distribution, resulting in unstable optimization process of the synthesized samples. By only matching with proper gradients from representative original samples, our proposed DREAM (right) greatly improves the training efficiency of dataset distillation tasks. Best viewed in color.

[9\]](#page-8-3). Among these, dataset distillation, aiming at condensing large-scale datasets into smaller ones with little information loss, has become a hot topic to tackle the problem of data burden [\[4,](#page-8-4) [50,](#page-9-7) [25,](#page-9-8) [6,](#page-8-5) [13\]](#page-8-6).

Dataset distillation methods are roughly divided into two categories: coreset-based and optimization-based. Coresetbased method employ certain metrics to heuristically select samples for representing the original dataset [\[27,](#page-9-9) [44\]](#page-9-10). How-

<sup>\*</sup>Equal contribution.

<sup>†</sup>Project lead.

<sup>‡</sup>Corresponding author.

<span id="page-1-0"></span>ever, it is difficult to rely on a small proportion of original samples to contain the information of the whole dataset, resulting in low compression rate. Optimization-based methods alleviate the defect by incorporating image synthesis to introduce more information into single images [\[51\]](#page-10-2). Specifically, these methods initialize a small amount of learnable image tensors and update them through matching the training gradients [\[56,](#page-10-1) [25\]](#page-9-8), embedding distributions [\[55,](#page-10-3) [50\]](#page-9-7) or training trajectories [\[4,](#page-8-4) [13\]](#page-8-6) with the original images.

Although the optimization-based methods achieve considerable performance as well as compression ratio, the distillation process itself still requires a large amount of time. We analyze the problem from the strategy of selecting original images for matching, which is mostly set as random sampling in previous works [\[56\]](#page-10-1). We argue that random sampling overlooks the evenness of the selected sample distribution. On the one hand, the matching optimization may be overly prone to certain samples with dominant matching targets, such as boundary samples with larger training gradients [\[50\]](#page-9-7). On the other hand, the sample diversity inside a mini-batch is also not constrained, leading to potential information insufficiency. These factors together result in optimization instability of the dataset distillation process, and degrade the training efficiency.

Accordingly, we propose a novel matching strategy named as Dataset distillation by REpresentAtive Matching (DREAM) to address the aforementioned training efficiency issue. Specifically, a clustering process inside each class is conducted at intervals to generate sub-clusters reflecting the sample distribution. The sub-cluster centers, which not only are representative for surrounding samples, but also evenly cover the whole class distribution, are selected for matching. As shown in Fig. [1a,](#page-0-0) the gradient distribution of the selected samples contains less variation. By only matching with representative samples, DREAM largely reduces the instability during training, and provides a smoother and more robust distillation process. For the synthetic image initialization, we adopt a similar clustering-based strategy, where the center sample is selected from each sub-cluster, which further accelerates the training process.

DREAM can be easily plugged into popular dataset distillation frameworks. Compared with commonly adopted random matching, DREAM significantly improves the training efficiency in the distilling process. We conduct extensive experiments to validate that it only takes less than one eighth of the iterations for DREAM to obtain comparable performance with the baseline methods. In addition, given sufficient training iterations, DREAM further boosts the performance to surpass other state-of-the-art methods.

Our main contributions are summarized as:

• We analyze the training efficiency of optimizationbased dataset distillation from the strategy of selecting original samples for matching.

- We propose a Dataset distillation by REpresentAtive Matching (DREAM) strategy. By only matching representative images, DREAM accelerates the training process by more than  $8 \times$  without performance drop.
- DREAM is able to be easily plugged into a variety of dataset distillation frameworks. Extensive experiments prove that DREAM consistently improves the performance of the distilled dataset.

# 2. Related Works

#### 2.1. Dataset Distillation

Dataset distillation can be roughly divided into 2 categories: coreset-based and optimization-based.

Coreset-based methods select a certain proportion of data based on certain metrics [\[18,](#page-9-11) [5\]](#page-8-7). Lapedriza *et al*. measure the importance of the sample by the benefits obtained from training the model on the sample [\[27\]](#page-9-9). Toneva *et al*. find that samples have different forgetting characteristics and the easily forgotten samples have larger information amount [\[44\]](#page-9-10). Coresets are also utilized to solve continual learning [\[38,](#page-9-12) [1,](#page-8-8) [52\]](#page-10-4) and active learning tasks [\[41\]](#page-9-13). Besides, Shleifer *et al*. accelerate the search of neural network architecture by selecting a group of "easier" samples [\[42\]](#page-9-14). Although coreset-based methods are practical to apply, it is hard to obtain rich information from a small amount of original samples. Therefore, coreset-based methods are restricted from further reducing the compression ratio.

Optimization-based methods implement dataset distillation by synthesizing image samples constrained by various optimization targets. Wang *et al*. raise the dataset distillation concept from the optimization aspect, and update the synthetic images in a meta-learning style [\[51\]](#page-10-2). Multiple works are then proposed to constrain the image generation by matching training gradients [\[56,](#page-10-1) [54,](#page-10-5) [23\]](#page-9-15), embedding distributions [\[55,](#page-10-3) [50\]](#page-9-7) and training trajectories [\[4\]](#page-8-4) with original images. IDC injects more information into synthetic samples under the limit of fixed storage size [\[25\]](#page-9-8). Nguyen *et al*. build up a distributed meta-learning framework and incorporate the kernel approximation methods [\[35\]](#page-9-16). RFAD speeds up the computation by introducing a random feature approximation [\[31\]](#page-9-17). HaBa employs data hallucination networks to construct base images and improves the representation capability of distilled datasets [\[29\]](#page-9-18). FRePo introduces an efficient meta-gradient computation method and a "model pool" to alleviate the overfitting [\[60\]](#page-10-6). DiM [\[49\]](#page-9-19) transfers knowledge by distilling datasets into generative models. Optimization-based methods largely improve the compression ratio via fusing more information into synthetic images. However, recent state-of-the-art methods require a large number of iterations to obtain desired validation accuracy, indicating low training efficiency. In this

<span id="page-2-1"></span>work, we focus on designing a novel matching strategy for more efficient dataset distillation training.

## 2.2. Clustering

Clustering divides samples into groups in an unsuper-vised manner [\[40\]](#page-9-20). K-means [\[15,](#page-8-9) [2\]](#page-8-10) specifies the number of target clusters, and optimizes the partition to obtain clusters with similar sizes [\[19\]](#page-9-21). DBSCAN, based on density, does not require the number of target clusters in advance. The clusters are formed by gradually adding data points within the tolerance range.  $[14]$ . It is applicable to dataset of any shape, yet the size of the generated clusters is unstable, outliers are excluded from clusters, and close clusters may be merged. Hierarchical clustering methods include Agglomerative and Divisive. The former fuses multiple clusters until a certain condition is met, and the latter divides a cluster through segmentation [\[11\]](#page-8-12).

## 3. Method

Aiming at addressing the training efficiency problem for dataset distillation tasks, we propose a novel Dataset distillation by REpresentAtive Matching (DREAM) strategy. By only matching the representative original images, DREAM reduces the optimization instability, and achieves a smoother and more robust training process. In this section, we orderly introduce the basic training schemes of dataset distillation, our observations on the training efficiency and the detailed design of DREAM.

## 3.1. Preliminaries

Given a large-scale dataset  $\mathcal{T} = \{(\mathbf{x}_t^i, y_t^i)\}_{i=1}^{|\mathcal{T}|}$ , the target of dataset distillation is generating a small surrogate dataset  $S = \{(\mathbf{x}_s^i, y_s^i)\}_{i=1}^{|\mathcal{S}|}$  with as little information loss as possible, where  $|\mathcal{S}| \ll |\mathcal{T}|$ . The information loss is usually measured by the performance drop between training a model with the original images  $\mathcal T$  and the surrogate set  $\mathcal S$ . The commonly adopted optimization-based methods follow a synthetic pipeline. The surrogate set  $S$  is first initialized with random original images from  $T$ . Under the constraints of matching objectives  $\phi(\cdot)$ , the synthetic images are updated to mimicking the distribution of the original images, which is formulated as:

<span id="page-2-0"></span>
$$
S^* = \arg\min_{\phi(S), \phi(\mathcal{T})}, \qquad (1)
$$

where  **is the matching metric. Typically, we select the** training gradients as the matching objective  $\phi(\cdot)$ . Given a random model  $\mathcal{M}_{\theta}$  with training parameters  $\theta$ , S is supposed to give similar gradients to  $T$  throughout the training process of  $\mathcal{M}_{\theta}$ , that is:

$$
S^* = \arg\min_{S} \mathbf{D} \left( \nabla_{\theta} \mathcal{L}(\mathcal{M}_{\theta}(\mathcal{A}(S))), \nabla_{\theta} \mathcal{L}(\mathcal{M}_{\theta}(\mathcal{A}(T)))) \right), \tag{2}
$$

where  $\mathcal{L}(\cdot, \cdot)$  is the training loss, and A is the differentiable augmentation  $[24, 57, 45, 58]$  $[24, 57, 45, 58]$  $[24, 57, 45, 58]$  $[24, 57, 45, 58]$  $[24, 57, 45, 58]$  $[24, 57, 45, 58]$  $[24, 57, 45, 58]$ . Practically, the matching objectives are calculated on the synthetic images and a mini-batch of original images  $\{(\mathbf{x}_t^i, y_t^i)\}_{i=1}^N$  sampled from  $T$  with the same class labels. The objective matching and  $\mathcal{M}_{\theta}$  training is conducted alternatively, such that gradients at different training stages are matched, which forms the inner optimization loop. The inner loop is iterated with different random  $\mathcal{M}_{\theta}$  for more varied matching gradients.

Recent literature offers various matching objectives and achieves significant testing accuracy via training in the small synthetic dataset [\[50,](#page-9-7) [4,](#page-8-4) [25\]](#page-9-8). However, the distillation process itself still requires a large amount of training time, indicating low training efficiency. We analyze the relationship between the training efficiency and the sampled original images for matching, and accordingly propose a novel matching strategy.

#### 3.2. Observations on Training Efficiency

In the dataset distillation process, the knowledge is distilled from sampled original images by matching certain objectives. The selection of original images thereby has large influences on the training efficiency. Recent literature usually adopts random sampling for selecting the original images [\[56,](#page-10-1) [25\]](#page-9-8). We set gradient matching as an example and carefully illustrate that random sampling disturbs efficient training of the dataset distillation.

Firstly, we analyze the matching effects of samples in different regions. Among all the samples in a class, those near the distribution center have higher prediction accuracy, indicating smaller backward gradients, while those on the decision boundaries have the contrary condition. For gradient matching, the central samples provide less effective supervision, while the gradients of the boundary ones largely dominate the optimization direction. We show the training accuracy curve of matching the synthetic images with only the central or boundary samples in Fig. [2b.](#page-3-0) The small gradients provided by central samples soon fail to provide effective supervision. On the other hand, although the boundary samples are essential for building decision boundaries, only matching with them brings chaotic matching targets, which degrades the distillation performance.

Secondly, we demonstrate that random sampling cannot guarantee an evenly distributed mini-batch along the training process. We record the Maximum Mean Discrepancy (MMD) between the selected mini-batch and the whole class distribution during training in Fig. [2c.](#page-3-0) It can be observed that the MMD is kept at a relatively high level, with large fluctuations during the training process. For the gradient matching, as the mini-batch cannot effectively and consistently cover the original class sample distribution, the gradient difference of different samples are not balanced. The matching target of a mini-batch may be biased by

<span id="page-3-1"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: The image displays a research paper figure with three parts. Part (a) illustrates the training pipeline of the proposed DREAM strategy, showing a flow from 'Clustering' and 'Original Images' through a model 'M\_theta' to 'pt' and 'gt', and from 'Synthetic Images' through 'M\_theta' to 'ps' and 'gs'. These then go through a gradient calculation 'nabla\_theta L(.)' to 'gt' and 'gs', which are then matched in a 'Match' module, with 'forward' and 'backward' arrows indicating the process. Part (b) contains three plots. The first plot, 'Accuracy-Iteration', shows the accuracy over iterations for 'Ours', 'Random', 'Central', and 'Boundary' methods, with 'Ours' and 'Random' reaching around 68% accuracy, 'Central' around 57%, and 'Boundary' around 42%. The second plot, 'MMD-Iteration', shows the MMD values over iterations for 'Ours' and 'Random' methods, with 'Ours' fluctuating around 1.5 and 'Random' fluctuating around 4. The third plot, 'Clustering t-SNE', is a scatter plot showing data points colored by cluster, with star markers indicating cluster centers. The x-axis is labeled 'comp-1' and ranges from -40 to 40, and the y-axis is labeled 'comp-2' and ranges from -40 to 40.

(b) The accuracy curve with different strategies for selecting original images.

(c) The MMD curve between the sampled (d) Example clustering and sub-cluster cenmini-batch and the corresponding class data. ter results of DREAM.

Figure 2: The original images obtained by random sampling have uneven distributions, which may result in noisy or biased matching targets. Besides, the coverage of random sampling on the whole sample space is low and has large fluctuations during training. Comparatively, the centers selected by DREAM (stars) are representative for corresponding sub-clusters, and are evenly distributed over the whole class feature space. Experiments for (b) and (c) are conducted under 10 imagesper-class setting on CIFAR-10. Best viewed in color.

boundary samples with larger training gradients, which results in unstable supervision.

Besides, an unevenly distributed mini-batch also indicates relatively poor sample diversity. Information redundancy at dense regions and information lack at sparse regions make the mini-batch insufficient to represent the original data. The above factors result in optimization instability of the distillation process, and hence degrade the training efficiency. Since randomly sampling original images disturbs the training efficiency for dataset distillation training, we propose to design a novel strategy to construct minibatches with even and diverse distribution for matching.

#### 3.3. Representative Matching

Based on the purpose of achieving stable and fast optimization, only representative original images are selected for gradient matching. The selection of representative images are supposed to obey the following two principles. On the one hand, the selected images should be evenly distributed to avoid biased matching targets. On the other hand, while ensuring diversity, the selected samples should reflect the overall sample distribution of the class as accurately as possible.

Therefore, we employ a clustering process for selecting representative original images. Out of the considerations of uniform sub-cluster sizes and distribution, without loss of generality, we adopt K-Means  $[15, 2, 36]$  $[15, 2, 36]$  $[15, 2, 36]$  $[15, 2, 36]$  $[15, 2, 36]$  for dividing sub-clusters. As shown in Fig. [2d,](#page-3-0) the clustering is conducted inside each class to generate  $N$  sub-clusters that reflect the sample density.  $N$  is a pre-defined hyper-parameter for the mini-batch size of real images. The sub-cluster centers evenly cover the sample space of the whole class, and simultaneously provide sufficient diversity, which perfectly meets the above principles.

The complete training pipeline is illustrated in Fig. [2a.](#page-3-0) The clustering-selected original mini-batch and the synthetic images with the same class label are passed through the random model  $\mathcal{M}_{\theta}$  to obtain prediction scores  $p_t$  and  $p_s$ . Subsequently calculate the classification losses and their corresponding gradients. The gradient differences are backwarded to update synthetic images according to Eq. [2.](#page-2-0) Considering the brought extra time cost, the clustering process is conducted every  $I_{int}$  iterations.

Additionally, at the beginning of the training process, we cluster the data of each class into sub-clusters corresponding to the pre-defined images-per-class number. We select the center samples of each sub-cluster as the initialization of the synthetic images. A more balanced clustering-based initialization better reflects the data distribution, and accelerates the convergence from the very beginning of the training process.

## <span id="page-4-0"></span>4. Experiments

## 4.1. Datasets and Implementation Details

We verify the effectiveness of our method on multiple popular dataset distillation benchmarks, including CI-FAR10 [\[26\]](#page-9-25), CIFAR100 [\[26\]](#page-9-25), SVHN [\[33\]](#page-9-26), MNIST [\[28\]](#page-9-27), FashionMNIST [\[53\]](#page-10-9) and TinyImageNet [\[10\]](#page-8-13). For evaluation, we train a model on the distilled synthetic images and test it on the original testing images. Top-1 accuracy is reported to show the performance.

Without specific designation, the experiment is conducted on 3-layer convolutional networks (ConvNet-3) [\[16\]](#page-8-14) with 128 filters and instance normalization [\[47\]](#page-9-28). The matching mini-batch size for original images is set as 128. By default we set IDC  $[25]$  as the baseline method. The gradient matching metric  $D$  in Eq. [2](#page-2-0) is empirically set as the mean squared error for CIFAR-10, CIFAR-100, TinyImageNet and SVHN. For MNIST and FashionMNIST, D is set as the mean absolute error  $[25]$ . We conduct 1,200 matching iterations in total, inside each of which 100 inner loops are conducted. SGD is set as the optimizer, with a learning rate of 0.005. For clustering, we employ the matching model for feature extraction. The clustering interval  $I_{int}$  is set as 10 iterations, whose sensitiveness is analyzed in Sec. [4.3.](#page-7-0) We also analyze the influence of different sampling strategy from the sub-clusters in Sec. [4.3.](#page-7-0) For evaluation, we train a network for 1,000 epochs on the distilled images with a learning rate of 0.01. We perform 5 experiments and report the mean and standard deviation of the results.

#### 4.2. Comparison with State-of-the-art Methods

We compare the distilled synthetic dataset performance of DREAM and other state-of-the-art (SOTA) coreset-based and optimization-based methods on multiple datasets with different images-per-class (IPC) settings in Tab. [1.](#page-5-0) Besides, on TinyImageNet, we compare DREAM with DM [\[55\]](#page-10-3) and MTT [\[4\]](#page-8-4) in Tab. [2.](#page-5-1) Under all experiment circumstances, the proposed DREAM consistently surpasses other SOTA methods. With a small IPC setting especially, under the guidance of proper gradients, DREAM is more robust than other methods, which proves the effectiveness of the representative matching strategy. Further narrowing the performance gap between small-scale distilled datasets and the original ones indicates that the information loss of dataset distillation is reduced. More detailed comparisons are included in the supplementary material.

### 4.3. Ablation Study and Analysis

Extended experiments are designed to verify the effectiveness of our proposed DREAM strategy. Without specific designation, the experiment is conducted under the 10 IPC setting on CIFAR-10 dataset.

Component Combination Evaluation. Firstly, we verify the isolated effects of each component in our proposed DREAM strategy in Tab. [3.](#page-5-2) Under the same initialization, our proposed representative matching strategy largely improves the final dataset performance. Comparatively, the clustering-based initialization offers a large performance lead before the training begins, yet eventually brings limited improvements. Nevertheless, it still provides stable boosts and accelerates the training convergence added on the representative matching to form the whole DREAM method. Combining all the components, the full DREAM method cuts the required iterations to achieve the baseline performance by more than 8 times.

Additionally, in Fig. [2](#page-3-0) we further illustrate the effectiveness of DREAM. Fig. [2b](#page-3-0) shows that by simply assigning samples from the sub-clusters as initialization of synthetic images, the validation performance surpasses random initialization by a large margin. Under the joint effect of representative matching and clustering-based initialization, DREAM achieves the final performance of random sampling with less than eighth of training iterations, demonstrating a significant training efficiency improvement. Continuing increasing the training iterations, DREAM further improves the dataset performance by applying proper gradient as supervision.

From the sample distribution perspective, Fig. [2c](#page-3-0) demonstrates that the original images selected by DREAM consistently show lower MMD scores with the original distribution with less fluctuations, compared with random sampling. The smaller fluctuations validates that sub-cluster centers effectively and stably cover the feature distribution, and reduces the noise at the sample level during the training process. With sufficient sample diversity, distribution evenness and appropriate gradient supervision, DREAM ensures a smoother and more robust optimization process for dataset distillation training.

For better illustration of the universality of DREAM, we apply the representative matching and clustering-based initialization to some other baseline methods and receive similar effects in Tab. [3.](#page-5-2) The accuracy curve comparisons are presented in the supplementary material. It proves that DREAM is able to be easily plugged into dataset distillation frameworks and help improve the training efficiency.

Cross Architecture Generalization Analysis. It has been a problem for previous optimization-based dataset distillation works to generalize across architectures as the synthetic images would over-fit to the model utilized for gradient matching [\[56,](#page-10-1) [25\]](#page-9-8). In Tab. [4](#page-5-3) we demonstrate the cross architecture performance of our proposed DREAM strategy. We distill the dataset with ConvNet-3 and ResNet-10 [\[21\]](#page-9-0), and validate the performance on ConvNet-3, ResNet-10 and DenseNet-121 [\[22\]](#page-9-29).

DREAM surpasses the compared methods on both the

|              |              | IPC Ratio %    | Random           | <b>Coreset Selection</b><br>Herding | DC $[56]$                   | DSA [54]                    | DM [55]                  | Training Set Synthesis<br>CAFE $[50]$ | MTT[4]                   |                               | <b>DREAM</b>              | Whole<br>Dataset |
|--------------|--------------|----------------|------------------|-------------------------------------|-----------------------------|-----------------------------|--------------------------|---------------------------------------|--------------------------|-------------------------------|---------------------------|------------------|
|              |              |                |                  |                                     |                             |                             |                          |                                       |                          | IDC $[25]$                    |                           |                  |
|              |              | 0.017          | $64.9_{\pm 3.5}$ | $89.2_{\pm 1.6}$                    | $91.7_{\pm 0.5}$            | $88.7_{\pm 0.6}$            | $89.7_{\pm 0.6}$         | $93.1_{\pm 0.3}$                      | $\overline{\phantom{0}}$ | $94.2_{\pm 0.2}$ <sup>†</sup> | $95.7_{\pm 0.3}$          |                  |
| <b>MNIST</b> | 10           | 0.17           | $95.1_{\pm 0.9}$ | $93.7_{\pm 0.3}$                    | $97.4_{\pm 0.2}$            | $97.8_{\pm 0.1}$            | $97.5{\pm}0.1$           | $97.2_{\pm 0.3}$                      | -                        | $98.4 \pm 0.1^{\dagger}$      | $98.6_{\pm 0.1}$          | $99.6{\pm}0.0$   |
|              | 50           | 0.83           | $97.9_{\pm 0.2}$ | $94.8 \pm 0.2$                      | $98.8_{\pm 0.2}$            | $99.2_{\pm 0.1}$            | $98.6{\pm}0.1$           | $98.6_{\pm0.2}$                       | -                        | 99.1 $\pm$ 0.1 <sup>†</sup>   | $99.2_{\pm 0.1}$          |                  |
|              |              | 0.017          | $51.4_{\pm 3.8}$ | $67.0_{\pm 1.9}$                    | $70.5 \pm 0.6$              | $70.6_{\pm 0.6}$            | $\overline{\phantom{a}}$ | $77.1_{\pm 0.9}$                      | $\overline{\phantom{a}}$ | $81.0_{\pm 0.2}$ <sup>†</sup> | $81.3_{+0.2}$             |                  |
| FashionMNIST | 10           | 0.17           | $73.8_{\pm0.7}$  | $71.1_{\pm0.7}$                     | $82.3 \pm 0.4$              | $84.6{\scriptstyle \pm0.3}$ |                          | $83.0_{\pm0.4}$                       |                          | $86.0_{\pm 0.3}$ †            | $86.4_{\pm 0.3}$          | $93.5{\pm}0.1$   |
|              | 50           | 0.83           | $82.5{\pm}0.7$   | $71.9 \pm 0.8$                      | $83.6{\scriptstyle \pm0.4}$ | $\textbf{88.7}_{\pm0.2}$    | ٠                        | $84.8_{\pm0.4}$                       | -                        | $86.2_{\pm 0.2}$ <sup>†</sup> | $86.8_{\pm 0.3}$          |                  |
|              |              | 0.014          | $14.6_{\pm 1.6}$ | $20.9_{\pm 1.3}$                    | $31.2_{\pm 1.4}$            | $27.5 \pm 1.4$              | ٠                        | $42.6_{\pm 3.3}$                      | $\overline{\phantom{a}}$ | $68.5_{\pm 0.9}$ <sup>†</sup> | $69.8_{\pm 0.8}$          |                  |
| <b>SVHN</b>  | 10           | 0.14           | $35.1_{\pm 4.1}$ | $50.5_{\pm3.3}$                     | $76.1_{\pm 0.6}$            | $79.2_{\pm 0.5}$            |                          | $75.9_{\pm0.6}$                       | -                        | $87.5_{\pm 0.3}$ †            | $\textbf{87.9}_{\pm 0.4}$ | $95.4_{\pm 0.1}$ |
|              | 50           | 0.7            | $70.9_{\pm 0.9}$ | $72.6_{\pm 0.8}$                    | $82.3{\pm}0.3$              | $84.4_{\pm 0.4}$            | ٠                        | $81.3_{\pm0.3}$                       | -                        | $90.1_{\pm 0.1}$ <sup>†</sup> | $90.5_{\pm 0.1}$          |                  |
|              |              | 0.02           | $14.4_{\pm 2.0}$ | $21.5_{\pm 1.2}$                    | $28.3_{\pm 0.5}$            | $28.8_{\pm0.7}$             | $26.0_{\pm 0.8}$         | $30.3_{\pm1.1}$                       | $46.3{\pm}0.8$           | $50.6_{\pm 0.4}$ <sup>T</sup> | $51.1_{+0.3}$             |                  |
| CIFAR10      | 10           | 0.2            | $26.0_{+1.2}$    | $31.6{\scriptstyle \pm 0.7}$        | $44.9_{\pm0.5}$             | $52.1_{\pm 0.5}$            | $48.9{\pm}0.6$           | $46.3{\scriptstyle \pm 0.6}$          | $65.3_{\pm 0.7}$         | $67.5{\scriptstyle \pm 0.5}$  | $69.4_{\pm 0.4}$          | $84.8 + 0.1$     |
|              | 50           | 1.0            | $43.4_{\pm 1.0}$ | $40.4_{\pm0.6}$                     | $53.9 \pm 0.5$              | $60.6 \scriptstyle \pm 0.5$ | $63.0_{\pm0.4}$          | $55.5_{\pm0.6}$                       | $71.6_{\pm 0.2}$         | $74.5{\pm}0.1$                | $74.8_{\pm0.1}$           |                  |
|              | $\mathbf{1}$ | 0.2            | $4.2_{\pm 0.3}$  | $8.4_{\pm 0.3}$                     | $12.8{\pm}0.3$              | $13.9_{\pm 0.3}$            | $11.4_{\pm 0.3}$         | $12.9 \pm 0.3$                        | $24.3_{\pm 0.3}$         | $\overline{\phantom{0}}$      | $29.5_{\pm0.3}$           |                  |
| CIFAR100     | 10           | $\overline{2}$ | $14.6{\pm}0.5$   | $17.3_{\pm 0.3}$                    | $25.2_{\pm 0.3}$            | $32.3{\pm}0.3$              | $29.7{\pm}0.3$           | $27.8_{\pm 0.3}$                      | $40.1_{\pm 0.4}$         | $45.1_{\pm 0.4}$ <sup>T</sup> | $46.8_{\pm 0.7}$          | $56.2_{\pm 0.3}$ |
|              | 50           | 10             | $30.0_{\pm 0.4}$ | $33.7_{\pm 0.5}$                    | $\sim$                      | $42.8_{\pm0.4}$             | $43.6{\pm}0.4$           | $37.9_{\pm 0.3}$                      | $47.7_{\pm 0.2}$         | $\overline{\phantom{a}}$      | $52.6_{\pm 0.4}$          |                  |

<span id="page-5-5"></span><span id="page-5-0"></span>Table 1: Top-1 accuracy of test models trained on distilled synthetic images on multiple datasets. The distillation training is conducted with ConvNet-3.  $^{\dagger}$  denotes the reported error range is reproduced by us.

<span id="page-5-1"></span>Table 2: Top-1 accuracy of test models trained on distilled synthetic images on TinyImageNet. The distillation training is conducted with ConvNet-3.

|  |  | IPC Ratio %   DM [55] MTT [4] DREAM   Whole                                                                                                                                                                 |  |
|--|--|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|
|  |  | $\begin{array}{c cccccc} 1 & 0.017 & 3.9_{\pm 0.2} & 8.8_{\pm 0.3} & \textbf{10.0}_{\pm 0.4} \\ 50 & 0.83 & 24.1_{\pm 0.3} & 28.0_{\pm 0.3} & \textbf{29.5}_{\pm 0.3} \end{array} \bigg  \; 37.6_{\pm 0.4}$ |  |

<span id="page-5-2"></span>Table 3: Ablation study on the components of the proposed DREAM. RM indicates Representative Matching, and Init stands for clustering-based initialization. "Iter" stands for the required iterations to achieve the baseline performance.

|     | Comp | RM Init | $Top-1$                                                                                                   | Iter |     | Comp<br>RM Init | $Top-1$                     |                               |
|-----|------|---------|-----------------------------------------------------------------------------------------------------------|------|-----|-----------------|-----------------------------|-------------------------------|
|     |      |         | - 67.5 $\pm$ 0.5 1000 DC<br>- 68.9 $\pm$ 0.5 350 DC<br>✓ 68.1 $\pm$ 0.3 750 DS<br>✓ 69.4 $\pm$ 0.4 150 DS |      |     |                 | $-44.9_{\pm 0.5}$           |                               |
| IDC |      |         |                                                                                                           |      |     |                 | $\checkmark$ 45.9 $\pm$ 0.3 |                               |
|     |      |         |                                                                                                           |      | DSA |                 | $-52.1_{\pm 0.5}$           |                               |
|     |      |         |                                                                                                           |      |     |                 |                             | $\sqrt{53.1}$ <sub>±0.4</sub> |

absolute performance and the performance drop when applying the distilled dataset on an unseen architecture. The strong cross architecture generalization capability verifies that DREAM helps build a more reasonable distilled dataset compared to random sampling.

Sampling Strategy Analysis. Representative matching conducts clustering for each class and samples original images from the sub-clusters to form a mini-batch. We analyze the influence of different sampling strategy on the training results in Tab. [5](#page-5-4) and Fig. [3.](#page-6-0) Among each sub-cluster, the top- $n$  samples closest to the center are selected. By

<span id="page-5-3"></span>Table 4: Ablation study on cross architecture distilled dataset performance of the proposed DREAM strategy. The dataset is first distilled on a model D and then validated on another model T. † denotes the result is reproduced by us.

|              | $D\backslash T$ | $Conv-3$           | $Res-10$           | Dense-121          |
|--------------|-----------------|--------------------|--------------------|--------------------|
| MTT $[4]$    | $Conv-3$        | $64.3_{\pm 0.7}$   | $34.5_{\pm 0.6}$ † | $41.5_{\pm 0.5}$ † |
|              | $Res-10$        | $44.2_{\pm 0.3}$ † | $20.4_{\pm 0.9}$ † | $24.2_{\pm 1.3}$ † |
| IDC $[25]$   | $Conv-3$        | $67.5_{\pm 0.5}$   | $63.5_{\pm 0.1}$   | $61.6_{\pm 0.6}$   |
|              | $Res-10$        | $53.6_{\pm 0.6}$ † | $50.6_{\pm 0.9}$ † | $51.7_{\pm 0.6}$ † |
| <b>DREAM</b> | $Conv-3$        | $69.4_{\pm 0.4}$   | $66.3_{\pm 0.8}$   | $65.9_{\pm 0.5}$   |
|              | $Res-10$        | 53.7 $_{\pm 0.6}$  | $51.0_{\pm 0.9}$   | 52.8 $\pm$ 0.6     |

<span id="page-5-4"></span>Table 5: Ablation study on different sampling strategy to form a mini-batch from sub-clusters.

|                                                                                                                                                                                                                                                                                 | Sub-cluster number $N$ |    |       |     |  |  |  |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------|----|-------|-----|--|--|--|
|                                                                                                                                                                                                                                                                                 | 32                     | 64 | - 128 | 256 |  |  |  |
| $\begin{array}{c cccccc}\n & 1 & 67.2_{\pm 0.3} & 68.5_{\pm 0.1} & \textbf{69.4}_{\pm 0.4} & 68.9_{\pm 0.2} \\ \text{Samples per} & 2 & 67.7_{\pm 0.3} & 68.6_{\pm 0.3} & 69.2_{\pm 0.7} & - \\ \text{sub-cluster } n & 4 & 67.7_{\pm 0.4} & 68.7_{\pm 0.4} & - \\ \end{array}$ |                        |    |       |     |  |  |  |
|                                                                                                                                                                                                                                                                                 |                        |    |       |     |  |  |  |
|                                                                                                                                                                                                                                                                                 |                        |    |       |     |  |  |  |
|                                                                                                                                                                                                                                                                                 | 8 67.5 ± 0.3           |    |       |     |  |  |  |

grouping different sub-cluster number and samples per subcluster, we are able to obtain original image mini-batches different in scale and diversity. As observed in the results, by representative matching the dataset performance is generally stable, and receives improvements to certain extent over the baseline (67.5).

Compared in more detail, with a small sub-cluster num-

<span id="page-6-2"></span><span id="page-6-0"></span>Image /page/6/Figure/0 description: This image displays three histograms, each representing the distribution of gradient norms for 'Random samples' and 'DREAM samples' across different sub-cluster numbers. The top histogram, for N=32 sub-clusters, shows 'DREAM samples' with a mean of 4.4 and a standard deviation of 5.1, while 'Random samples' have a mean of 10.0 and a standard deviation of 8.8. The middle histogram, for N=128 sub-clusters, indicates 'DREAM samples' with a mean of 6.4 and a standard deviation of 7.1, and 'Random samples' with a mean of 10.0 and a standard deviation of 8.8. The bottom histogram, for N=256 sub-clusters, shows 'DREAM samples' with a mean of 8.2 and a standard deviation of 8.2, and 'Random samples' with a mean of 10.0 and a standard deviation of 8.8. All histograms have 'Sample Number' on the y-axis and 'Gradient Norm (L2)' on the x-axis, ranging from 0 to 40. Dashed lines indicate the mean values for both sample types in each plot.

Figure 3: The gradient distribution comparison between random sampling and our proposed DREAM strategy under different sub-cluster sample number N. Best viewed in color.

ber  $N = 32$ , the sub-cluster centers are more likely to be distributed in areas with smaller gradients, as shown in the first row of Fig. [3.](#page-6-0) As the random model  $\mathcal{M}_{\theta}$  is trained, these samples gradually fail to provide effective gradients for supervision, resulting in a sub-optimal performance. Oppositely, a larger sub-cluster number  $N = 256$  involves a distribution closer to random sampling, which brings a small performance drop, as shown in the last row of Fig. [3.](#page-6-0) Due to memory limitations, it is not applicable to further increase N, but it is conceivable that the extreme condition should yield similar results to random sampling. On the other hand, the sample number per sub-cluster  $n$  has only a slight effect on the results. The group of 1 center sample per sub-cluster and 128 sub-clusters in total is proved to obtain the optimal gradient supervision as in the second row of Fig. [3,](#page-6-0) and is chosen for mini-batch composition.

Training Stability Analysis. In order to more intuitively demonstrate the effects of the proposed DREAM strategy on the training process, we visualize the feature migration of DREAM and random sampling in Fig. [4a.](#page-7-0) Specifically, we randomly select a synthetic image as initialization and record its updated version every 10 iterations. We employ a random network to extract the features of all images, and calculate the Euclidean distance between adjacent versions of images. For DREAM, at the beginning of the training process, under the direction of proper gradients, the synthetic image goes through a larger migration. Within 100 iterations, the synthetic image has reached a relatively optimal position, and makes subsequent fine-tuning. On the contrary, there are still large fluctuations for the synthetic image matched with randomly sampled original images in the late training period, partly due to the noisy matching targets generated by uneven mini-batches.

Clustering Interval Sensitivity Analysis. We evaluate the influence of different clustering interval  $I_{int}$  on the final dataset performance in Fig. [4b.](#page-7-0) Conducting clustering at every iteration leads to the best performance, while adding the clustering interval until 10 brings mild influences. As there is an obvious top-1 accuracy degradation when the interval is further increased to 20, we select an interval of 10 to balance the distilled dataset performance and the extra calculation cost. More analysis on the computational cost of clustering is included in the supplementary material.

Experimental results on ImageNet-1K. We compare DREAM with the current state-of-the-art method TESLA [\[7\]](#page-8-15) on ImageNet-1K in Tab. [6.](#page-6-1) The experimental setting is the same as in TESLA. DREAM shows excellent performance on large datasets.

<span id="page-6-1"></span>Table 6: Results on ImageNet.

| Method       | TESLA | DREAM |
|--------------|-------|-------|
| Acc (IPC=10) | 17.8  | 18.5  |

Differences from DC-BENCH[\[6\]](#page-8-5). We provide a detailed comparison between DREAM and DC-BENCH to clarify their distinctions. DC-BENCH solely concentrates on a better initialization and lacks specific designs for the subsequent matching-based optimization, while DREAM selects representative samples for matching and enables the realization of a fully efficient training process for distillation. Furthermore, DREAM conducts extensive experiments to analyze the impact of cluster number, sample number per cluster and clustering interval. DC-BENCH achieves comparable performance using 30% of iterations, whereas DREAM achieves similar results with only 10- 20% iterations. Additionally, given sufficient training time, DREAM further achieves up to 3.7% and 5.8% accuracy improvements for gradient matching and distribution matching respectively which surpass DC-BENCH's 1.3%.

### 4.4. Visualizations

Gradient Difference Curve. As the dataset distillation training is constrained by matching the training gradients, a smaller gradient difference also indicates a better matching effect. Therefore, we also visualize the gradient difference curve of the dataset distillation in Fig. [4c,](#page-7-0) which is calculated by the training loss Eq. [2.](#page-2-0) We add the DREAM strategy to DC, DSA and IDC methods. Throughout the training process, DREAM holds a smaller gradient difference compared with the baseline methods. On the one hand, it verifies the effectiveness of DREAM on improving the training

<span id="page-7-3"></span><span id="page-7-0"></span>Image /page/7/Figure/0 description: The image displays four plots labeled (a), (b), (c), and (d). Plot (a) is a line graph showing "Logarithmic Feature Migration" on the y-axis and "Iteration" on the x-axis, with two lines representing "Ours" (red) and "Random" (green). Plot (b) shows "Top-1 Accuracy(%)" on the left y-axis and "Top-5 Accuracy(%)" on the right y-axis, with "Clustering Interval" on the x-axis. It includes two lines: "Top-1" (green with triangles) and "Top-5" (blue with squares). Plot (c) displays "Gradient Difference(DC/DSA)" on the left y-axis and "Gradient Difference(IDC)" on the right y-axis, with "Iteration" on the x-axis. It features six lines representing "DC" (green dashed), "DC+DREAM" (green solid), "DSA" (blue dashed), "DSA+DREAM" (blue solid), "IDC" (red dashed), and "IDC+DREAM" (red solid). Plot (d) shows "Test Accuracy" on the y-axis and "Number of classes" on the x-axis, with five lines representing "DREAM" (red with diamonds), "IDC" (orange with circles), "IDC-I" (green with diamonds), "Herding" (purple with diamonds), and "DSA" (blue with diamonds).

Figure 4: (a): The feature migration during the training process. (b): Ablation study on different clustering interval. (c): The training loss curve during the training process. (d): The continual learning accuracy curve.

<span id="page-7-1"></span>Image /page/7/Figure/2 description: The image displays two scatter plots side-by-side, each containing numerous small gray dots representing data points. Both plots are labeled at the bottom right: the left plot is labeled "Ours" and the right plot is labeled "Random". Each plot is also marked with several red stars, indicating specific points within the data distribution. The "Ours" plot shows red stars distributed across different clusters of gray dots, while the "Random" plot shows red stars concentrated more towards the edges and in sparser areas of the data distribution.

Figure 5: The sample distribution comparison on the final distilled images (marked as red stars) between our proposed DREAM (left) and random sampling (right).

efficiency to reduce the gradient difference in limited iterations. On the other hand, the large fluctuations of the baseline methods also validate the existence of noisy gradients generated by random sampling.

Sample Distribution Visualization. In order to more intuitively demonstrate the effectiveness of our proposed DREAM on generating synthetic sets well covering the original sample distribution, we visualize the t-SNE graphs of the synthetic images for both random sampling and DREAM. As shown in Fig. [5,](#page-7-1) the final distribution constrained by DREAM strategy evenly cover the whole class, while random sampling generates biased optimization results. Furthermore, a large percentage of samples are pulled to the distribution edge in the random sampling results, which also validates that the matching is biased by boundary samples with larger gradients. By consistently providing proper gradient supervision, DREAM achieves a more diverse and robust distillation result.

Synthetic Image Visualization. In order to more intuitively demonstrate the effects of DREAM on the distilled images, we compare the distillation results of adding the proposed DREAM strategy or not in Fig. [6.](#page-7-2) DREAM improves the quality of the distilled datasets from two perspectives. Firstly, the images optimized by DREAM show more

<span id="page-7-2"></span>Image /page/7/Picture/7 description: The image displays two grids of generated images, likely from a machine learning model. The top grid contains 30 images arranged in 5 rows and 6 columns. The bottom grid also contains 30 images, arranged in the same 5x6 format. The images themselves appear to be abstract representations, with some resembling cars or other objects, but are generally low-resolution and somewhat distorted. The overall impression is a collection of generated samples, possibly for comparison or demonstration purposes.

Figure 6: The distilled dataset comparison between DC (Upper row) and DC with DREAM strategy (Bottom row) on CIFAR-10 (plane, car, dog, cat classes). DREAM introduces more obvious categorical characteristics and variety to the distilled image. Best viewed in color. More visualization is provided in supplementary material.

obvious categorical characteristics. Secondly, DREAM introduces more variety to the distilled images. With these two improvements, DREAM helps the distilled datasets to obtain better validation performance.

# 4.5. Application on Continual Learning

Dataset distillation generates compact datasets that are able to represent the original ones, which can thus be applied to continual learning problems [\[38,](#page-9-12) [1,](#page-8-8) [52,](#page-10-4) [25\]](#page-9-8). We further validate the effectiveness of the proposed DREAM strategy on the continual learning scenarios in Fig. [4d.](#page-7-0) Following the settings in [\[56,](#page-10-1) [25\]](#page-9-8), we conduct a 5-step class<span id="page-8-16"></span>incremental experiment on CIFAR-100, each step with 20 classes. For better demonstrating the generalization capability of DREAM, the distillation synthesis is conducted on ConvNet-3, and the validation on ResNet-10.

DREAM consistently maintains performance advantages over other approaches throughout the training process, and the performance gap is further enlarged as the learnt class number is gradually increased. It proves that better distillation quality helps the model construct clearer decision boundaries and memorize the discriminative information.

## 5. Conclusion

In this paper, we propose a novel Dataset distillation by REpresentAtive Matching (DREAM) strategy to address the training efficiency problem for dataset distillation. By only matching with the representative original images, DREAM reduces the optimization instability, and reaches a smoother and more robust training process. It is able to be easily plugged into popular dataset distillation frameworks to reduce the training iterations by more than 8 times without performance drop. The stable optimization also provides higher final performance and generalization capability. The more efficient matching allows future works to design more complicated matching metrics.

# 6. Limitations and Future Works

Although the proposed DREAM strategy significantly improves the training efficiency of optimization-based dataset distillation methods, the calculation burden is still large when the image size and the class number increases. It is difficult for these methods to handle ultra large-scale datasets like ImageNet [\[10\]](#page-8-13), even if the training efficiency has been improved by DREAM. We will explore more resource-friendly ways to conduct dataset distillation in future works.

## Acknowledgement

This research is supported by the National Research Foundation, Singapore under its AI Singapore Programme (AISG Award No: AISG2-PhD-2021-08- 008). Yang You's research group is being sponsored by NUS startup grant (Presidential Young Professorship), Singapore MOE Tier-1 grant, ByteDance grant, ARCTIC grant, SMI grant and Alibaba grant. The research is also supported by the National Natural Science Foundation of China (No. 62173302).

### References

<span id="page-8-8"></span>[1] Rahaf Aljundi, Min Lin, Baptiste Goujaud, and Yoshua Bengio. Gradient based sample selection for online continual learning. In *NeurIPS*, pages 11817–11826, 2019. [2,](#page-1-0) [8](#page-7-3)

- <span id="page-8-10"></span>[2] David Arthur and Sergei Vassilvitskii. k-means++: The advantages of careful seeding. Technical report, Stanford, 2006. [3,](#page-2-1) [4](#page-3-1)
- <span id="page-8-17"></span>[3] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [13](#page-12-0)
- <span id="page-8-4"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, pages 4750–4759, 2022. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-1) [5,](#page-4-0) [6,](#page-5-5) [13](#page-12-0)
- <span id="page-8-7"></span>[5] Cody Coleman, Christopher Yeh, Stephen Mussmann, Baharan Mirzasoleiman, Peter Bailis, Percy Liang, Jure Leskovec, and Matei Zaharia. Selection via proxy: Efficient data selection for deep learning. *arXiv preprint arXiv:1906.11829*, 2019. [2](#page-1-0)
- <span id="page-8-5"></span>[6] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dcbench: Dataset condensation benchmark. In *NeurIPS*, 2022. [1,](#page-0-1) [7](#page-6-2)
- <span id="page-8-15"></span>[7] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [7](#page-6-2)
- <span id="page-8-2"></span>[8] Martin Danelljan, Goutam Bhat, Fahad Shahbaz Khan, and Michael Felsberg. Eco: Efficient convolution operators for tracking. In *CVPR*, pages 6638–6646, 2017. [1](#page-0-1)
- <span id="page-8-3"></span>[9] Zhou Daquan, Kai Wang, Jianyang Gu, Xiangyu Peng, Dongze Lian, Yifan Zhang, Yang You, and Jiashi Feng. Dataset quantization. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, 2023. [1](#page-0-1)
- <span id="page-8-13"></span>[10] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, pages 248–255. Ieee, 2009. [5,](#page-4-0) [9](#page-8-16)
- <span id="page-8-12"></span>[11] Chris Ding and Xiaofeng He. Cluster merging and splitting in hierarchical clustering algorithms. In *ICDM.*, pages 139– 146. IEEE, 2002. [3](#page-2-1)
- <span id="page-8-0"></span>[12] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020. [1](#page-0-1)
- <span id="page-8-6"></span>[13] Jiawei Du, Yidi Jiang, Vincent Y. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *CVPR*, pages 3749–3758, 2023. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-8-11"></span>[14] Martin Ester, Hans-Peter Kriegel, Jörg Sander, Xiaowei Xu, et al. A density-based algorithm for discovering clusters in large spatial databases with noise. In *KDD*, pages 226–231, 1996. [3](#page-2-1)
- <span id="page-8-9"></span>[15] Edward W Forgy. Cluster analysis of multivariate data: efficiency versus interpretability of classifications. *Biometrics*, 21:768–769, 1965. [3,](#page-2-1) [4](#page-3-1)
- <span id="page-8-14"></span>[16] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *CVPR*, pages 4367– 4375, 2018. [5](#page-4-0)
- <span id="page-8-1"></span>[17] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and

Yoshua Bengio. Generative adversarial networks. *Communications of the ACM*, 63(11):139–144, 2020. [1](#page-0-1)

- <span id="page-9-11"></span>[18] Chengcheng Guo, Bo Zhao, and Yanbing Bai. Deepcore: A comprehensive library for coreset selection in deep learning. *arXiv preprint arXiv:2204.08499*, 2022. [2](#page-1-0)
- <span id="page-9-21"></span>[19] Greg Hamerly and Charles Elkan. Alternatives to the kmeans algorithm that find better clusterings. In *CIKM*, pages 600–607, 2002. [3](#page-2-1)
- <span id="page-9-4"></span>[20] Kaiming He, Xinlei Chen, Saining Xie, Yanghao Li, Piotr Dollar, and Ross Girshick. Masked autoencoders are scalable ´ vision learners. In *CVPR*, pages 16000–16009, 2022. [1](#page-0-1)
- <span id="page-9-0"></span>[21] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, pages 770–778, 2016. [1,](#page-0-1) [5](#page-4-0)
- <span id="page-9-29"></span>[22] Gao Huang, Zhuang Liu, Laurens Van Der Maaten, and Kilian Q Weinberger. Densely connected convolutional networks. In *CVPR*, pages 4700–4708, 2017. [5](#page-4-0)
- <span id="page-9-15"></span>[23] Zixuan Jiang, Jiaqi Gu, Mingjie Liu, and David Z Pan. Delving into effective gradient matching for dataset condensation. *arXiv preprint arXiv:2208.00311*, 2022. [2,](#page-1-0) [12](#page-11-0)
- <span id="page-9-22"></span>[24] Tero Karras, Miika Aittala, Janne Hellsten, Samuli Laine, Jaakko Lehtinen, and Timo Aila. Training generative adversarial networks with limited data. *NeurIPS*, 33:12104– 12114, 2020. [3](#page-2-1)
- <span id="page-9-8"></span>[25] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. *arXiv preprint arXiv:2205.14959*, 2022. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-1) [5,](#page-4-0) [6,](#page-5-5) [8,](#page-7-3) [12,](#page-11-0) [13](#page-12-0)
- <span id="page-9-25"></span>[26] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [5,](#page-4-0) [12](#page-11-0)
- <span id="page-9-9"></span>[27] Agata Lapedriza, Hamed Pirsiavash, Zoya Bylinskii, and Antonio Torralba. Are all training examples equally valuable? *arXiv preprint arXiv:1311.6510*, 2013. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-9-27"></span>[28] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998. [5,](#page-4-0) [12](#page-11-0)
- <span id="page-9-18"></span>[29] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, 2022. [2,](#page-1-0) [12,](#page-11-0) [13](#page-12-0)
- <span id="page-9-2"></span>[30] Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei, Zheng Zhang, Stephen Lin, and Baining Guo. Swin transformer: Hierarchical vision transformer using shifted windows. In *ICCV*, pages 10012–10022, 2021. [1](#page-0-1)
- <span id="page-9-17"></span>[31] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022. [2,](#page-1-0) [12,](#page-11-0) [13](#page-12-0)
- <span id="page-9-30"></span>[32] Jonathan Lorraine, Paul Vicol, and David Duvenaud. Optimizing millions of hyperparameters by implicit differentiation. In *International Conference on Artificial Intelligence and Statistics*, pages 1540–1552. PMLR, 2020. [12](#page-11-0)
- <span id="page-9-26"></span>[33] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Bo Wu, and Andrew Y Ng. Reading digits in natural images with unsupervised feature learning. 2011. [5,](#page-4-0) [12](#page-11-0)
- <span id="page-9-32"></span>[34] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *ICLR*, 2020. [13](#page-12-0)

- <span id="page-9-16"></span>[35] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *NeurIPS*, 34:5186–5198, 2021. [2,](#page-1-0) [13](#page-12-0)
- <span id="page-9-24"></span>[36] Sehban Omer. fast-pytorch-kmeans, 9 2020. [4](#page-3-1)
- <span id="page-9-6"></span>[37] Ziheng Qin, Kai Wang, Zangwei Zheng, Jianyang Gu, Xiangyu Peng, Daquan Zhou, and Yang You. Infobatch: Lossless training speed up by unbiased dynamic data pruning. *arXiv preprint arXiv:2303.04947*, 2023. [1](#page-0-1)
- <span id="page-9-12"></span>[38] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. icarl: Incremental classifier and representation learning. In *CVPR*, pages 2001–2010, 2017. [2,](#page-1-0) [8](#page-7-3)
- <span id="page-9-1"></span>[39] Joseph Redmon, Santosh Divvala, Ross Girshick, and Ali Farhadi. You only look once: Unified, real-time object detection. In *CVPR*, pages 779–788, 2016. [1](#page-0-1)
- <span id="page-9-20"></span>[40] Hajar Rehioui, Abdellah Idrissi, Manar Abourezq, and Faouzia Zegrari. Denclue-im: A new approach for big data clustering. *Procedia Computer Science*, 83:560–567, 2016. [3](#page-2-1)
- <span id="page-9-13"></span>[41] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. *arXiv preprint arXiv:1708.00489*, 2017. [2](#page-1-0)
- <span id="page-9-14"></span>[42] Sam Shleifer and Eric Prokop. Using small proxy datasets to accelerate hyperparameter search. *arXiv preprint arXiv:1906.04887*, 2019. [2](#page-1-0)
- <span id="page-9-5"></span>[43] Ben Sorscher, Robert Geirhos, Shashank Shekhar, Surya Ganguli, and Ari Morcos. Beyond neural scaling laws: beating power law scaling via data pruning. *Advances in Neural Information Processing Systems*, 35:19523–19536, 2022. [1](#page-0-1)
- <span id="page-9-10"></span>[44] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-9-23"></span>[45] Ngoc-Trung Tran, Viet-Hung Tran, Ngoc-Bao Nguyen, Trung-Kien Nguyen, and Ngai-Man Cheung. Towards good practices for data augmentation in gan training. *arXiv preprint arXiv:2006.05338*, 2:3, 2020. [3](#page-2-1)
- <span id="page-9-3"></span>[46] Yi-Hsuan Tsai, Wei-Chih Hung, Samuel Schulter, Kihyuk Sohn, Ming-Hsuan Yang, and Manmohan Chandraker. Learning to adapt structured output space for semantic segmentation. In *CVPR*, pages 7472–7481, 2018. [1](#page-0-1)
- <span id="page-9-28"></span>[47] Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*, 2016. [5](#page-4-0)
- <span id="page-9-31"></span>[48] Paul Vicol, Jonathan P Lorraine, Fabian Pedregosa, David Duvenaud, and Roger B Grosse. On implicit bias in overparameterized bilevel optimization. In *International Conference on Machine Learning*, pages 22234–22259. PMLR, 2022. [12](#page-11-0)
- <span id="page-9-19"></span>[49] Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023. [2](#page-1-0)
- <span id="page-9-7"></span>[50] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, pages 12196–12205, 2022. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-1) [6,](#page-5-5) [13](#page-12-0)

- <span id="page-10-2"></span>[51] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2,](#page-1-0) [13](#page-12-0)
- <span id="page-10-4"></span>[52] Felix Wiewel and Bin Yang. Condensed composite memory continual learning. In *IJCNN*, pages 1–8. IEEE, 2021. [2,](#page-1-0) [8](#page-7-3)
- <span id="page-10-9"></span>[53] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashionmnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017. [5](#page-4-0)
- <span id="page-10-5"></span>[54] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, pages 12674– 12685. PMLR, 2021. [2,](#page-1-0) [6,](#page-5-5) [12,](#page-11-0) [13](#page-12-0)
- <span id="page-10-3"></span>[55] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021. [2,](#page-1-0) [5,](#page-4-0) [6,](#page-5-5) [13](#page-12-0)
- <span id="page-10-1"></span>[56] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2020. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-1) [5,](#page-4-0) [6,](#page-5-5) [8,](#page-7-3) [12,](#page-11-0) [13](#page-12-0)
- <span id="page-10-7"></span>[57] Shengyu Zhao, Zhijian Liu, Ji Lin, Jun-Yan Zhu, and Song Han. Differentiable augmentation for data-efficient gan training. *NeurIPS*, 33:7559–7570, 2020. [3](#page-2-1)
- <span id="page-10-8"></span>[58] Zhengli Zhao, Zizhao Zhang, Ting Chen, Sameer Singh, and Han Zhang. Image augmentations for gan training. *arXiv preprint arXiv:2006.02595*, 2020. [3](#page-2-1)
- <span id="page-10-0"></span>[59] Zangwei Zheng, Mingyuan Ma, Kai Wang, Ziheng Qin, Xiangyu Yue, and Yang You. Preventing zero-shot transfer degradation in continual learning of vision-language models. *arXiv preprint arXiv:2303.06628*, 2023. [1](#page-0-1)
- <span id="page-10-6"></span>[60] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, 2022. [2,](#page-1-0) [13](#page-12-0)

<span id="page-11-0"></span>

### 7. Comparisons with More Methods

We compare the distilled dataset performance between DREAM and more methods in Tab. [7.](#page-12-1) The experiments are conducted under 1, 10, and 50 images-per-class (IPC) settings on MNIST [\[28\]](#page-9-27), SVHN [\[33\]](#page-9-26), CIFAR-10, and CIFAR-100 [\[26\]](#page-9-25) datasets. DREAM achieves SOTA results on most cases. Especially when IPC is small, DREAM has gained significant performance gap over other methods, which also validates the effectiveness of matching representative samples. RFAD [\[31\]](#page-9-17) employs ConvNet with 1024 convolutional channels, while our results are reported based on 128 channel ConvNet. Except for IPC=1 CIFAR-10, DREAM distills better synthetic images than RFAD. HaBa [\[29\]](#page-9-18) involves a data hallucination process, which generates more samples from base images. It holds higher performance on IPC=10 CIFAR-10 and IPC=1 CIFAR-100, while in other circumstances, DREAM has superior performances.

## 8. Accuracy Curve Visualization

We apply the DREAM strategy to more dataset distillation methods, such as DC  $[56]$ , DSA  $[54]$ , etc. In addition to stable performance improvements, we visualize the accuracy curve during training in Fig. [7.](#page-12-2) It can be observed that compared with the original methods, DREAM only requires one fifth and one tenth of the iteration number on the DC and DSA to achieve the original performance, respectively. Further increasing the training time brings continuous performance improvement, which also proves that our method is universal and is able to be easily plugged for popular dataset distillation frameworks. The above experiments are all based on the setting of 10 images-per-class on CIFAR10.

## 9. DREAM on Distribution Matching

In addition to gradient matching, we also explore the applicability of our method in embedding distribution matching. For distribution matching, the optimization is constrained by:

$$
S^* = \arg\min_{\theta} \mathbf{D} \left( \xi(\mathcal{M}_{\theta}(\mathcal{A}(S))), \xi(\mathcal{M}_{\theta}(\mathcal{A}(T)))) \right), \quad (3)
$$

where  $\xi$  represents averaging the features in the channel dimension. For gradient matching, the boundary samples generate large gradients which bias the optimization, while for distribution matching, the boundary samples shift the average feature. Random sampling introduces random factors to the shifts and degrades the matching efficiency. DREAM, on the contrary, ensures the evenness and diversity of the original images for matching. It largely reduces the feature shifts and consistently provide appropriate supervision for the optimization. We conduct the experiments on IDC  $[25]$  with distribution matching. Under the setting of 10 imagesper-class on the CIFAR10 dataset, the original IDC performs much poorer than gradient matching, which is also stated in [\[25\]](#page-9-8). Applying DREAM completely reversed this situation by improving the dataset performance by a large margin. Besides, it only takes less than one tenth of the original iteration number to reach the performance, as shown in Fig. [7c.](#page-12-2)

## 10. Distilled Dataset Visualization

In order to more intuitively demonstrate the effects on the distilled images, we compare the distillation results of adding the proposed DREAM strategy or not in Fig. [8.](#page-13-0) DREAM improves the quality of the distilled datasets from two perspectives. Firstly, the images optimized by DREAM show more obvious categorical characteristics. Secondly, DREAM introduces more variety to the distilled images. With these two improvements, dream brings better performance to the distilled datasets.

We provide some extra visualizations of the distilled images on MNIST, FashionMNIST and SVHN in Fig. [9.](#page-13-1)

#### 11. Differences from Related Works

There are some recent works focusing on improving the efficiency of dataset distillation. RFAD reduces the calculation of neural tangent kernel matrix in Kernel Inducing Points (KIP) from  $O(|S^2|)$  to  $O(|S|)$  by using random feature approximation [\[31\]](#page-9-17). It takes into account the similarity between Neural Tangent Kernel (NTK) and Neural Network Gaussian Process (NNGP) kernels. RFAD focuses on reducing the calculation complexity in KIP, while our proposed DREAM is aiming at improving the matching efficiency through selecting representative original images, which has no contradicts.

Jiang et al. analyze the shortcomings of gradient matching method and propose the idea of matching multi-level gradients from the angle perspective [\[23\]](#page-9-15). There are also many other methods  $[32, 48]$  $[32, 48]$  $[32, 48]$ , which analyze the shortcomings of existing methods from the perspective of two-level optimization and improve the efficiency. Comparatively, DREAM addresses the matching efficiency problem from the sampling perspective for both gradient matching and embedding distribution matching. DREAM is able to be easily plugged into other dataset distillation methods to significantly reduce the required training iterations.

## 12. Clustering Analysis

We further analyze the extra time cost caused by the clustering process in Tab. [8.](#page-12-3) For CIFAR-10, in each inner loop, the matching process and image updating cost 0.2s. Every 10 inner loops, a clustering process is conducted. The clustering process takes 1s, so by average the clustering time for

|                  | <b>MNIST</b> |                              |      |                          | <b>SVHN</b> |                          |      | CIFAR <sub>10</sub> |                          |                          | CIFAR100                 |                          |  |
|------------------|--------------|------------------------------|------|--------------------------|-------------|--------------------------|------|---------------------|--------------------------|--------------------------|--------------------------|--------------------------|--|
| Dataset          |              | 10                           | 50   | 1                        | 10          | 50                       | 1    | 10                  | 50                       | 1                        | 10                       | 50                       |  |
| $DD$ [51]        | Ξ.           | 79.5                         |      |                          |             | $\overline{\phantom{a}}$ | -    | 36.8                | $\overline{\phantom{a}}$ |                          |                          |                          |  |
| LD[3]            | 60.9         | 87.3                         | 93.3 |                          |             | $\overline{\phantom{a}}$ | 25.7 | 38.3                | 42.5                     | 11.5                     | $\overline{\phantom{a}}$ |                          |  |
| DC [56]          | 91.7         | 97.4                         | 98.8 | 31.2                     | 76.1        | 82.3                     | 28.3 | 44.9                | 53.9                     | 12.8                     | 25.2                     |                          |  |
| DSA [54]         | 88.7         | 97.8                         | 99.2 | 27.5                     | 79.2        | 84.4                     | 28.8 | 52.1                | 60.6                     | 13.9                     | 32.3                     | 42.8                     |  |
| DM $[55]$        | 89.7         | 97.5                         | 98.6 | $\overline{\phantom{a}}$ |             | -                        | 26.0 | 48.9                | 63.0                     | 11.4                     | 29.7                     | 43.6                     |  |
| <b>CAFE</b> [50] | 93.1         | 97.2                         | 98.6 | 42.6                     | 75.9        | 81.3                     | 30.3 | 46.3                | 55.5                     | 12.9                     | 27.8                     | 37.9                     |  |
| MTT[4]           |              | $\qquad \qquad \blacksquare$ | ۰    |                          |             | -                        | 46.3 | 65.3                | 71.6                     | 24.3                     | 40.1                     | 47.7                     |  |
| IDC $[25]$       | 94.2         | 98.4                         | 99.1 | 68.5                     | 87.5        | 90.1                     | 50.6 | 67.5                | 74.5                     | $\overline{\phantom{a}}$ | 45.1                     | $\overline{\phantom{a}}$ |  |
| KIP $[34, 35]$   | 90.1         | 97.5                         | 98.3 | 57.3                     | 75.0        | 80.5                     | 49.9 | 62.7                | 68.6                     | 15.7                     | 28.3                     | $\overline{\phantom{a}}$ |  |
| $R$ FAD $[31]$   | 94.4         | 98.5                         | 98.8 | 52.2                     | 74.9        | 80.9                     | 53.6 | 66.3                | 71.1                     | 26.3                     | 33.0                     | $\overline{\phantom{a}}$ |  |
| HaBa $[29]$      | 92.4         | 97.4                         | 98.1 | 69.8                     | 83.2        | 88.3                     | 48.3 | 69.9                | 74.0                     | 33.4                     | 40.2                     | 47.0                     |  |
| FRePo [60]       | 93.0         | 98.6                         | 99.2 | $\overline{\phantom{a}}$ |             | $\overline{\phantom{0}}$ | 46.8 | 65.5                | 71.7                     | 28.7                     | 42.5                     | 44.3                     |  |
| <b>DREAM</b>     | 95.7         | 98.6                         | 99.2 | 69.8                     | 87.9        | 90.5                     | 51.1 | 69.4                | 74.8                     | 29.5                     | 46.8                     | 52.6                     |  |

<span id="page-12-1"></span><span id="page-12-0"></span>Table 7: Top-1 accuracy of test models trained on distilled synthetic images on multiple datasets. The distillation training is conducted with ConvNet-3.

<span id="page-12-2"></span>Image /page/12/Figure/2 description: This image contains three line graphs, each titled "Accuracy-Iteration." The x-axis for all graphs represents "Iteration" from 0 to 1000. The y-axis for the first two graphs represents "Accuracy" from 0.25 to 0.50 and 0.25 to 0.60 respectively. The y-axis for the third graph represents "Accuracy" from 50 to 70. The first graph shows two lines: a magenta line labeled "DC+DREAM" and a teal line labeled "DC." The second graph shows two lines: a magenta line labeled "DSA+DREAM" and a teal line labeled "DSA." The third graph shows two lines: a magenta line labeled "IDC(feat)+DREAM" and a teal line labeled "IDC(feat)." All graphs show an increasing trend in accuracy with iterations, with the "+DREAM" lines generally performing better or similarly to the other lines.

(a) The accuracy curve of adding DREAM (b) The accuracy curve of adding DREAM (c) The accuracy curve of adding DREAM strategy to DC. strategy to DSA. strategy to distribution matching.

Figure 7: Applying the DREAM strategy brings stable performance and efficiency improvements.

<span id="page-12-3"></span>Table 8: Time cost of adding DREAM strategy (s).

| <b>Datasets</b> | Methods           | Clustering | Update<br>Images | Inner<br>Loop |
|-----------------|-------------------|------------|------------------|---------------|
| CIFAR-10        | IDC [25]<br>DREAM | -          | 0.2<br>0.2       | 0.2<br>0.3    |
| CIFAR-100       | IDC [25]<br>DREAM | -          | 2.0<br>2.0       | 2.0<br>2.1    |

each inner loop is 0.1s. The total average inner loop time is 0.3s, compared to the original 0.2s. Considering that we only need one tenth to one fifth of the iterations to obtain the original performance, we save more than 70% of the time. For CIFAR-100 with more classes, the extra clustering time is one twentieth of the original image updating time, which is negligible. DREAM significantly improves the matching efficiency and reduces the required training time for dataset distillation.

<span id="page-13-0"></span>Image /page/13/Figure/0 description: This image displays four grids of generated images, labeled (a) DC, (b) DC+DREAM, (c) DSA, and (d) DSA+DREAM. Each grid contains numerous small images, predominantly featuring dogs and some cars, arranged in a 10x10 matrix. The overall presentation suggests a comparison of image generation techniques.

Figure 8: Applying DREAM improves the image quality and sample diversity.

<span id="page-13-1"></span>Image /page/13/Picture/2 description: This image displays three grids of images, labeled (a) MNIST, (b) FashionMNIST, and (c) SVHN. Grid (a) shows ten rows and ten columns of handwritten digits from 0 to 9. Grid (b) shows ten rows and ten columns of clothing items, including t-shirts, trousers, sweaters, dresses, skirts, shoes, and handbags. Grid (c) shows ten rows and ten columns of street view house numbers (SVHN) dataset images, which are also digits from 0 to 9, but with more color and variation than the MNIST dataset. The overall figure compares these three image datasets.

Figure 9: Example visualizations of the distilled images on MNIST, FashionMNIST and SVHN.
# <span id="page-0-1"></span><span id="page-0-0"></span>Sequential Subset Matching for Dataset Distillation

Jiawei Du $^{12}$  , <PERSON> Shi $^{3\ddagger}$ , <PERSON> $^{12\%}$ 

<sup>1</sup> Centre for Frontier AI Research (CFAR), Agency for Science, Technology and Research (A\*STAR), Singapore <sup>2</sup> Institute of High Performance Computing (IHPC), Agency for Science, Technology and Research (A\*STAR), Singapore <sup>3</sup> Department of Statistics, Purdue University {dujw,<PERSON>\_<PERSON>}@cfar.a-star.edu.sg, <EMAIL>

#### Abstract

Dataset distillation is a newly emerging task that synthesizes a small-size dataset used in training deep neural networks (DNNs) for reducing data storage and model training costs. The synthetic datasets are expected to capture the essence of the knowledge contained in real-world datasets such that the former yields a similar performance as the latter. Recent advancements in distillation methods have produced notable improvements in generating synthetic datasets. However, current state-of-the-art methods treat the entire synthetic dataset as a unified entity and optimize each synthetic instance equally. This static optimization approach may lead to performance degradation in dataset distillation. Specifically, we argue that static optimization can give rise to a coupling issue within the synthetic data, particularly when a larger amount of synthetic data is being optimized. This coupling issue, in turn, leads to the failure of the distilled dataset to extract the high-level features learned by the deep neural network (DNN) in the latter epochs. In this study, we propose a new dataset distillation strategy called Sequential Subset Matching (SeqMatch), which tackles this problem by adaptively optimizing the synthetic data to encourage sequential acquisition of knowledge during dataset distillation. Our analysis indicates that SeqMatch effectively addresses the coupling issue by sequentially generating the synthetic instances, thereby enhancing its performance significantly. Our proposed SeqMatch outperforms state-of-the-art methods in various datasets, including SVNH, CIFAR-10, CIFAR-100, and Tiny ImageNet. Our code is available at [https://github.com/shqii1j/seqmatch.](https://github.com/shqii1j/seqmatch)

## 1 Introduction

Recent advancements in Deep Neural Networks (DNNs) have demonstrated their remarkable ability to extract knowledge from large-scale real-world data, as exemplified by the impressive performance of the large language model GPT-3, which was trained on a staggering 45 terabytes of text data [\[4\]](#page-10-0). However, the use of such massive datasets comes at a significant cost in terms of data storage, model training, and hyperparameter tuning.

The challenges associated with the use of large-scale datasets have motivated the development of various techniques aimed at reducing datasets size while preserving their essential characteristics. One such technique is dataset distillation  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$  $[5, 6, 13, 22, 29, 35, 42, 48, 50–53]$ , which involves synthesizing a smaller dataset that effectively captures the knowledge contained within the original dataset. Models trained on these synthetic datasets have been shown to achieve comparable performance to those trained on the full dataset. In recent years, dataset distillation has garnered increasing attention from the deep learning community and has been leveraged in various practical applications, including

<sup>‡</sup>Work completed during internship at A\*STAR

<sup>§</sup>Corresponding Author

<span id="page-1-2"></span>continual learning  $[41, 52, 53]$  $[41, 52, 53]$  $[41, 52, 53]$  $[41, 52, 53]$  $[41, 52, 53]$ , neural architecture search  $[21, 39, 40, 51, 53]$  $[21, 39, 40, 51, 53]$  $[21, 39, 40, 51, 53]$  $[21, 39, 40, 51, 53]$  $[21, 39, 40, 51, 53]$  $[21, 39, 40, 51, 53]$  $[21, 39, 40, 51, 53]$  $[21, 39, 40, 51, 53]$ , and privacy-preserving tasks  $[12, 15, 31]$  $[12, 15, 31]$  $[12, 15, 31]$  $[12, 15, 31]$  $[12, 15, 31]$ , among others.

Existing methods for dataset distillation, as proposed in  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$  $[5, 11, 13, 33, 37, 38, 47, 51, 53]$ , have improved the distillation performance through enhanced optimization methods. These approaches have achieved commendable improvements in consolidating knowledge from the original dataset and generating superior synthetic datasets. However, the knowledge condensed by these existing methods primarily originates from the easy instances, which exhibit a rapid reduction in training loss during the early stages of training. These easy instances constitute the majority of the dataset and typically encompass low-level, yet commonly encountered visual features (e.g. edges and textures [\[49\]](#page-12-12)) acquired in the initial epochs of training. In contrast, the remaining, less frequent, but more challenging instances encapsulate high-level features (e.g. shapes and contours) that are extracted in the subsequent epochs and significantly impact the generalization capability of deep neural networks (DNNs). The findings depicted in [Figure 1](#page-1-0) reveal that an overemphasis on low-level features hinders the extraction and condensation of high-level features from hard instances, thereby resulting in a decline in performance.

<span id="page-1-0"></span>Image /page/1/Figure/2 description: The image contains two plots. The plot on the left is titled "Loss vs Epoch" and shows the loss of two models, MTT and SeqMatch-MTT, over 50 epochs. The y-axis is labeled "Easy" and "Hard" and ranges from 0.0 to 4.5. The plot on the right is titled "Performance Comparison" and is a bar chart comparing the accuracy of MTT and SeqMatch-MTT on CIFAR10 and CIFAR100 datasets. The y-axis is labeled "Accuracy(%)" and ranges from 40 to 75. On CIFAR10, MTT has an accuracy of approximately 71% and SeqMatch-MTT has an accuracy of approximately 74%. On CIFAR100, MTT has an accuracy of approximately 48% and SeqMatch-MTT has an accuracy of approximately 52%.

Figure 1: Left: MTT [\[5\]](#page-10-1) fails to extract adequate highlevel features. The loss drop rate between easy and hard instances is employed as the metric to evaluate the condensation efficacy of low-level and high-level features. The upper solid lines represent the loss change of hard instances, while the lower dashed lines depict the loss change of easy instances. The inability to decrease the loss of hard instances indicates MTT's inadequacy in capturing high-level features. In contrast, our proposed SeqMatch successfully minimizes the loss for both hard and easy instances. **Right:** The consequent performance improvement of SeqMatch in CIFAR [\[25\]](#page-11-6) datasets. Experiments are conducted with 50 images per class (ipc  $= 50$ ).

In this paper, we investigate the factors that hinder the efficient condensation of high-level features in dataset distillation. Firstly, we reveal that DNNs are optimized through a process of learning from low-level visual features and gradually adapting to higher-level features. The condensation of high-level features determines the effectiveness of dataset distillation. Secondly, we argue that existing dataset distillation methods fail to extract high-level features because they treat the synthetic data as a unified entity and optimize each synthetic instance unvaryingly. Such static optimization makes the synthetic instances become coupled with each other easier in cases where more synthetic instances are optimized. As a result, increasing the size of synthetic dataset will over-condense the low-level features but fail to condense additional knowledge from the real dataset, let alone the higher-level features.

Building upon the insights derived from our analysis, we present a novel dataset distillation strategy, termed Sequential Subset Matching (Seq-

Match), which is designed to extract both low-level and high-level features from the real dataset, thereby improving dataset distillation. Our approach adopts a simple yet effective strategy for reorganizing the synthesized dataset  $S$  during the distillation and evaluation phases. Specifically, we divide the synthetic dataset into multiple subsets and encourage each subset to acquire knowledge in the order that DNNs learn from the real dataset. Our approach can be seamlessly integrated into existing dataset distillation methods. The experiments, as shown in [Figure 1,](#page-1-0) demonstrate that SeqMatch effectively enables the latter subsets to capture high-level features. This, in turn, leads to a substantial improvement in performance compared to the baseline method MTT, which struggles to compress higher-level features from the real dataset. Extensive experiments demonstrate that SeqMatch outperforms state-of-the-art methods, particularly in high compression ratio<sup>1</sup> scenarios, across a range of datasets including CIFAR-10, CIFAR-100, TinyImageNet, and subsets of the ImageNet.

In a nutshell, our contribution can be summarized as follows.

• we examine the inefficacy of current dataset distillation in condensing hard instances from the original dataset. We present insightful analyses regarding the plausible factors contributing to this inefficacy and reveal the inherent preference of dataset distillation in condensing knowledge.

<span id="page-1-1"></span><sup>¶</sup> compression ratio = compressed dataset size / full dataset size [\[8\]](#page-10-7)

<span id="page-2-1"></span>• We thereby propose a novel dataset distillation strategy called Sequential Subset Matching (SeqMatch) to targetedly encourage the condensing of higher-level features. SeqMatch seamlessly integrates with existing dataset distillation methods, offering easy implementation. Experiments on diverse datasets demonstrate the effectiveness of SeqMatch, achieving state-of-the-art performance.

### 2 Related work

Coreset selection is the traditional dataset reduction approach by selecting representative prototypes from the original dataset  $[2, 7, 17, 43, 45]$  $[2, 7, 17, 43, 45]$  $[2, 7, 17, 43, 45]$  $[2, 7, 17, 43, 45]$  $[2, 7, 17, 43, 45]$  $[2, 7, 17, 43, 45]$  $[2, 7, 17, 43, 45]$  $[2, 7, 17, 43, 45]$  $[2, 7, 17, 43, 45]$ . However, the non-editable nature of the coreset limits its performance potential. The idea of synthesizing the "coreset" can be traced back to Wang et al. [\[47\]](#page-12-11). Compared to coreset selection, dataset distillation has demonstrated greatly superior performance. Based on the approach of optimizing the synthetic data, dataset distillation can be taxonomized into two types: data-matching methods and meta-learning methods [\[30\]](#page-11-7).

Data-matching methods encourage the synthetic data to imitate the influence of the target data, involving the gradients, trajectories, and distributions. Zhao and Bilen [\[52\]](#page-12-5) proposed distribution matching to update synthetic data. Zhao et al. [\[53\]](#page-12-3) matched the gradients of the target and synthetic data in each iteration for optimization. This approach led to the development of several advanced gradientmatching methods  $[5, 20, 22, 51]$  $[5, 20, 22, 51]$  $[5, 20, 22, 51]$  $[5, 20, 22, 51]$  $[5, 20, 22, 51]$  $[5, 20, 22, 51]$  $[5, 20, 22, 51]$ . Trajectory-matching methods  $[5, 9, 13]$  $[5, 9, 13]$  $[5, 9, 13]$  $[5, 9, 13]$  further matched multi-step gradients to optimize the synthetic data, achieving state-of-the-art performance. Factorization-based methods [\[11,](#page-10-6) [29,](#page-11-1) [33\]](#page-11-5) distilled the synthetic data into a low-dimensional manifold and used a decoder to recover the source instances from the factorized features.

Meta-learning methods treat the synthetic data as the parameters to be optimized by a meta (or outer) algorithm [\[3,](#page-10-12) [11,](#page-10-6) [32,](#page-11-9) [34,](#page-11-10) [37,](#page-12-9) [38,](#page-12-10) [54\]](#page-12-15). A base (or inner) algorithm solves the supervised learning problem and is nested inside the meta (or outer) algorithm with respect to the synthetic data. The synthetic data can be directly updated to minimize the empirical risk of the network. Kernel ridge regression (KRR) based methods [\[34,](#page-11-10) [37,](#page-12-9) [38\]](#page-12-10) have achieved remarkable performance among meta-learning methods.

Both data-matching and meta-learning methods optimize each synthetic instance equally. The absence of variation in converged synthetic instances may lead to the extraction of similar knowledge and result in over-representation of low-level features.

### 3 Preliminaries

**Background** Throughout this paper, we denote the target dataset as  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$ . Each pair of data sample is drawn i.i.d. from a natural distribution D, and  $x_i \in \mathbb{R}^d$ ,  $y_i \in \mathcal{Y} = \{0, 1, \dots, C - 1\}$ <br>where d is the dimension of input data and C is the number of classes. We denote the synthetic dataset as  $\mathcal{S} = \{ (s_i, y_i) \}_{i=1}^{|\mathcal{S}|}$  where  $s_i \in \mathbb{R}^d$ ,  $y_i \in \mathcal{Y}$ . Each class of S contains ipc (images per class) data pairs. Thus,  $|\mathcal{S}| = \text{ipc} \times C$  and  $\text{ipc}$  is typically set to make  $|\mathcal{S}| \ll |\mathcal{T}|$ .

We employ  $f_\theta$  to denote a deep neural network f with weights  $\theta$ . An ideal training progress is to search for an optimal weight parameter  $\hat{\theta}$  that minimizes the expected risk over the natural distribution D, which is defined as  $L_{\mathcal{D}}(f_{\theta}) \triangleq \mathbb{E}_{(x,y)\sim\mathcal{D}}[\ell(f_{\theta}(x), y)]$ . However, as we can only access the training set  $T$  sampled from the natural distribution  $\mathcal{D}$ , the practical training approach of the network f is to minimizing the empirical risk  $L_{\mathcal{T}}(f_{\theta})$  minimization (ERM) on the training set  $\mathcal{T}$ , which is defined as

$$
\hat{\theta} = \operatorname{alg}(\mathcal{T}, \theta_0) = \operatorname*{arg\,min}_{\theta} L_{\mathcal{T}}(f_{\theta}) \quad \text{where} \quad L_{\mathcal{T}}(f_{\theta}) = \frac{1}{|\mathcal{T}|} \sum_{x_i \in \mathcal{T}} \ell[f_{\theta}(x_i), y_i], \tag{1}
$$

where  $\ell$  can be any training loss function; alg is the given training algorithm that optimizes the initialized weights parameters  $\theta_0$  over the training set T;  $\theta_0$  is initialized by sampling from a distribution  $P_{\theta_0}$ .

Dataset distillation aims to condense the knowledge of  $\mathcal T$  into the synthetic dataset  $\mathcal S$  so that training over the synthetic dataset  $S$  can achieve a comparable performance as training over the target dataset  $\mathcal T$ . The objective of dataset distillation can be formulated as,

<span id="page-2-0"></span>
$$
\mathbb{E}_{(x,y)\sim\mathcal{D},\theta_0\sim P_{\theta_0}}\left[\ell(f_{\mathtt{alg}(\mathcal{T},\theta_0)}(x),y)\right] \simeq \mathbb{E}_{(x,y)\sim\mathcal{D},\theta_0\sim P_{\theta_0}}\left[\ell(f_{\mathtt{alg}(\mathcal{S},\theta_0)}(x),y)\right].
$$
 (2)

<span id="page-3-3"></span>Gradient Matching Methods We take gradient matching methods as the backbone method to present our distillation strategy. Matching the gradients introduced by  $\mathcal T$  and  $\mathcal S$  helps to solve  $\mathcal S$ in [Equation 2.](#page-2-0) By doing so, gradient matching methods achieve advanced performance in dataset distillation. Specifically, gradient matching methods introduce a distance metric  $D(\cdot, \cdot)$  to measure the distance between gradients. A widely-used distance metric [\[53\]](#page-12-3) is defined as  $D(X, Y) =$  $\sum_{i=1}^I \left(1-\frac{\langle X_i,Y_i\rangle}{\|X_i\|\|Y_i} \right)$  $\frac{\langle X_i, Y_i \rangle}{\|X_i\| \|Y_i\|}$ , where  $X, Y \in \mathbb{R}^{I \times J}$  and  $X_i, Y_i \in \mathbb{R}^J$  are the  $i^{\text{th}}$  columns of X and Y respectively. With the defined distance metric  $D(\cdot, \cdot)$ , gradient matching methods consider solving

<span id="page-3-2"></span>
$$
\widehat{\mathcal{S}} = \underset{\substack{\mathcal{S} \subset \mathbb{R}^d \times \mathcal{Y} \\ |\mathcal{S}| = \text{ipc} \times C}}{\arg \min} \mathop{\mathbb{E}}_{\theta_0 \sim P_{\theta_0}} \left[ \sum_{m=1}^M \mathcal{L}(\mathcal{S}, \theta_m) \right], \quad \text{where} \quad \mathcal{L}(\mathcal{S}, \theta) = D(\nabla_{\theta} L_{\mathcal{S}}(f_{\theta}), \nabla_{\theta} L_{\mathcal{T}}(f_{\theta})) \tag{3}
$$

where  $\theta_i$  is the intermediate weights which is continuously updated by training the network  $f_{\theta_0}$  over the target dataset  $\mathcal T$ . The methods employ M as the hyperparameter to control the length of teacher trajectories to be matched starting from the initialized weights  $\theta_0 \sim P_{\theta_0}$ .  $\mathcal{L}(\mathcal{S}.\theta)$  is the matching loss. The teacher trajectory  $\{\theta_0, \theta_1, \dots, \theta_M\}$  is equivalent to a series of gradients  $\{g_1, g_2, \dots, g_M\}$ . To ensure the robustness of the synthetic dataset  $\bar{S}$  to different weights initializations,  $\theta_0$  will be sampled from  $P_{\theta_0}$  for many times. As a consequence, the distributions of the gradients for training can be represented as  $\{P_{g_1}, P_{g_2}, \cdots, P_{g_M}\}.$ 

<span id="page-3-1"></span>Algorithm 1 Training with SeqMatch in Distillation Phase.

**Input:** Target dataset  $\mathcal{T}$ ; Number of subsets  $K$ ; Iterations  $N$  in updating each subset; A base distillation method A.

1: Initialize the synthetic dataset  $S_{a11}$ 

2: Divide  $S_{a11}$  into K subsets of equal size  $\lfloor \frac{|S_{a11}|}{K} \rfloor$ , i.e.,  $S_{a11} = S_1 \cup S_2 \cup \cdots \cup S_K$ 3: for each  $S_k$  do 4:  $\triangleright$  Optimize 4:  $\triangleright$  Optimize each subset  $S_k$  sequentially:<br>5: **repeat** 

- 5: repeat
- 6: if  $k = 1$  then
- 7: Initialize network weights  $\theta_0^k \sim P_{\theta_0}$
- 8: else
- 9: Load network weights  $\theta_0^k \sim P_{\theta_N^{k-1}}$  saved in optimizing last subset  $\mathcal{S}_{k-1}$
- 10: **for**  $i = 1$  to N **do**

11:  $\triangleright$  Update Network weights by subset  $S_k$ :<br>  $\theta_i^k = \text{alg}(\mathcal{S}_k \cup \mathbb{S}^{(k-1)}, \theta_{i-1}^k)$ 

- $12.$ 13:  $\rhd$  Update  $S_k$  by the base distillation method:
- 14:  $S_k \leftarrow \mathcal{A}(\mathcal{T}, \mathcal{S}_k, \theta_i^k)$
- 15: Record and save updated network weights  $\theta_N^{k-1}$

16: until Converge

```
Output: Distilled synthetic dataset S_{a11}
```

## 4 Method

Increasing the size of a synthetic dataset is a straightforward approach to incorporating additional high-level features. However, our findings reveal that simply optimizing more synthetic data leads to an excessive focus on knowledge learned from easy instances. In this section, we first introduce the concept of sequential knowledge acquisition in a standard training procedure (refer to [subsection 4.1\)](#page-3-0). Subsequently, we argue that the varying rate of convergence causes certain portions of the synthetic data to abandon the extraction of further knowledge in the later stages (as discussed in [Figure 4.2\)](#page-4-0). Finally, we present our proposed strategy, Sequential Subset Matching (referred to as SeqMatch), which is outlined in Algorithm [1](#page-3-1) in [subsection 4.3.](#page-5-0)

<span id="page-3-0"></span>

##### 4.1 Features Are Represented Sequentially

Many studies have observed the sequential acquisition of knowledge in training DNNs. Zeiler et al. [\[49\]](#page-12-12) revealed that DNNs are optimized to extract low-level visual features, such as edges and

<span id="page-4-1"></span>textures, in the lower layers, while higher-level features, such as object parts and shapes, were represented in the higher layers. Han et al. [\[16\]](#page-10-13) leverages the observation that DNNs learn the knowledge from easy instances first, and gradually adapt to hard instances[\[1\]](#page-10-14) to propose noisy learning methods. The sequential acquisition of knowledge is a critical aspect of DNN.

However, effectively condensing knowledge throughout the entire training process presents significant challenges for existing dataset distillation methods. While the synthetic dataset  $S$  is employed to learn from extensive teacher trajectories, extending the length of these trajectories during distillation can exacerbate the issue of domain shifting in gradient distributions, thereby resulting in performance degradation. This is primarily due to the fact that the knowledge extracted from the target dataset  $\mathcal T$ varies across different epochs, leading to corresponding shifts in the domains of gradient distributions. Consequently, the synthetic dataset  $\bar{S}$  may struggle to adequately capture and consolidate knowledge from prolonged teacher trajectories.

To enhance distillation performance, a common approach is to match a shorter teacher trajectory while disregarding knowledge extracted from the latter epochs of  $T$ . For instance, in the case of the CIFAR-10 dataset, optimal hyperparameters for  $M$  (measured in epochs) in the MTT [\[5\]](#page-10-1) method were found to be 2, 20, 40 for  $\text{ipc} = 1, 10, 50$  settings, respectively. The compromise made in matching a shorter teacher trajectory unexpectedly resulted in a performance gain, thereby confirming the presence of excessive condensation on easy instances.

Taking into account the sequential acquisition of knowledge during deep neural network (DNN) training is crucial for improving the generalization ability of synthetic data. Involving more synthetic data is the most straightforward approach to condense additional knowledge from longer teacher trajectory. However, our experimental findings, as illustrated in [Figure 1,](#page-1-0) indicate that current gradient matching methods tend to prioritize the consolidation of knowledge derived from easy instances in the early epochs. Consequently, we conducted further investigations into the excessive emphasis on low-level features in existing dataset distillation methods.

##### 4.2 Coupled Synthetic Dataset

The coupling issue within the synthetic dataset impedes its effectiveness in condensing additional high-level features. Existing dataset distillation methods optimize the synthetic dataset  $S$  as a unified entity, resulting in the backpropagated gradients used to update  $S$  being applied globally. The gradients on each instance only differ across different initializations and preassigned labels, implying that instances sharing a similar initialization within the same class will converge similarly. Consequently, a portion of the synthetic data only serves the purpose of alleviating the gradient matching error for the pre-existing synthetic data.

Consider a synthetic dataset  $S$  is newly initialized to be distilled from a target dataset  $\mathcal T$ . The distributions of the gradients for distillation are  $\{P_{g_1}, P_{g_2}, \cdots, P_{g_M}\}\$ , and the sampled gradients for training is  $\{g_1, g_2, \dots, g_M\}$ . Suppose that  $G$  is the integrated gradients calculated by  $S$ , by minimizing the loss function as stated in

<span id="page-4-0"></span>Image /page/4/Figure/7 description: The image is a line graph titled "Distillation discrepancy between S+ and S-". The y-axis is labeled "Acc.(S+) - Acc.(S-)" and ranges from -2.0% to 10.0%. The x-axis is labeled "E [||R(si, fθ0)||1] - E [||R(si, fθ0)||1]" and ranges from 0.000 to 0.025. There are two lines on the graph: a blue line labeled "MTT" and an orange line labeled "SeqMatch". The MTT line generally increases from approximately 0.0% at x=0.000 to 10.0% at x=0.025. The SeqMatch line fluctuates between approximately 0.5% and 1.5% for most of the x-axis range, with a dip to -1.0% around x=0.005 and a peak of 1.5% around x=0.012.

Figure 2: The accuracy discrepancy between the networks trained using  $S^+$  and  $S^-$  separately. The discrepancy will increase with the magnitude of  $R(s_i, f_{\theta_m})$ . These results verified the coupling issue between  $S^+$ and  $S^-$ , and our proposed method SeqMatch successfully mitigates the coupling issue. More experimental details can be found in [subsection 5.3.](#page-7-0)

[Equation 3,](#page-3-2) the gradients used for updating  $s_i$  when  $\theta = \theta_m$  would be

$$
\nabla_{s_i} \mathcal{L}(S, \theta_m) = \frac{\partial \mathcal{L}}{\partial G} \cdot \frac{\partial G}{\partial \nabla_{\theta_m} \ell(f_{\theta_m}(s_i), y_i)} \cdot \frac{\partial \nabla_{\theta_m} \ell(f_{\theta_m}(s_i), y_i)}{\partial s_i}
$$
  
$$
= \frac{\partial \mathcal{L}}{\partial G} \cdot R(s_i, f_{\theta_m}), \quad \text{where} \quad R(s_i, f_{\theta_m}) \triangleq \frac{\partial \nabla_{\theta_m} \ell(f_{\theta_m}(s_i), y_i)}{\partial s_i}. \quad (4)
$$

we have  $\frac{\partial G}{\partial \nabla_{\theta_m} \ell(f_{\theta_m}(s_i), y_i)} = 1$ , because G is accumulated by the gradients of each synthetic data, i.e.,  $G = \nabla_{\theta_m} L_{\mathcal{S}}(f_{\theta_m}) = \sum_{i=1}^{\|\mathcal{S}\|} \nabla_{\theta_m} \ell(f_{\theta_m}(s_i), y_i)$ . Here we define the amplification function  $R(s_i, f_{\theta_m}) \subset \mathbb{R}^d$ . Then, the gradients on updating synthetic instance  $\nabla_{s_i} \mathcal{L}(\mathcal{S}, \theta_m)$  shares the same  $\frac{\partial \mathcal{L}}{\partial G}$  and only varies in  $R(s_i, f_{\theta_m})$ . The amplification function  $R(s_i, f_{\theta_m})$  is only affected by the pre-assigned label and initialization of  $s_i$ .

More importantly, the magnitude of  $R(s_i, f_{\theta_m})$  determines the rate of convergence of each synthetic instance  $s_i$ . Sorted by the  $l_1$ -norm of amplification function  $||R(s_i, f_{\theta_m})||_1$  can be divided into two subsets  $S^+$  and  $S^-$ .  $S^+$  contains the synthetic instances with greater values of  $R(s_i, f_{\theta_m})$  than those in  $S^-$ . That implies that instances in  $S^+$  converge faster to minimize  $D(\nabla_{\theta_m} L_{S^+}(f_{\theta_m}), g_m)$ , and S<sup>+</sup> is optimized to imitate  $g_m$ . On the other hand, the instances in S<sup>-</sup> converge slower and are optimized to minimize  $D(\nabla_{\theta_m} L_{\mathcal{S}^-}(f_{\theta_m}), \epsilon)$ , where  $\epsilon$  represents the gradients matching error  $\epsilon$  of  $S^+$ , i.e.,  $\epsilon = g_m - \nabla_{\theta_m} L_{\mathcal{S}^+}(f_{\theta_m})$ . Therefore,  $S^-$  is optimized to imitate  $\epsilon$  and its eff achieved by compensating for the gradients matching error of  $S^+$ .  $S^-$  is coupled with  $S^+$  and unable to capture the higher-level features in the latter epochs.

We conducted experiments to investigate whether  $S^-$  solely compensates for the gradient matching error of  $S^+$  and is unable to extract knowledge independently. To achieve this, we sorted  $S^+$  and S<sup>-</sup> by the l<sub>1</sub>-norm of the amplification function  $||R(s_i, f_{\theta_m})||_1$  and trained separate networks with  $S^+$  and  $S^-$ . As depicted in [Figure 2,](#page-4-0) we observed a significant discrepancy in accuracy, which increased with the difference in magnitude of  $R(s_i, f_{\theta_m})$ . Further details and discussion are provided in [subsection 5.3.](#page-7-0) These experiments verify the coupling issue wherein  $S^-$  compensates for the matching error of  $S^+$ , thereby reducing its effectiveness in condensing additional knowledge.

<span id="page-5-0"></span>

##### 4.3 Sequential Subset Matching

We can use a standard deep learning task as an analogy for the dataset distillation problem, then the synthetic dataset  $S$  can be thought of as the weight parameters that need to be optimized. However, simply increasing the size of the synthetic dataset is comparable to multiplying the parameters of a model in an exact layer without architecting the newly added parameters, and the resulting performance improvement is marginal. We thereby propose SeqMatch to reorganize the synthetic dataset  $S$  to utilize the newly added synthetic data.

We incorporate additional variability into the optimization process of synthetic data to encourage the capture of higher-level feature extracted in the latter training progress. To do this, SeqMatch divides the synthetic dataset S into K subsets equally, i.e.,  $S = S_1 \cup S_2 \cup \cdots \cup S_K$ ,  $|S_k| = \lfloor \frac{|S|}{K} \rfloor$  $\frac{|\mathcal{S}|}{K}$ . SeqMatch optimizes each  $S_k$  by solving

$$
\widehat{S}_k = \underset{\substack{S_k \subset \mathbb{R}^d \times \mathcal{Y} \\ |S_k| = \lfloor |S|/K \rfloor}}{\arg \min} \mathbb{E} \left[ \sum_{m=(k-1)n}^{kn} \mathcal{L}(\mathcal{S}_k \cup \mathbb{S}^{(k-1)}, \theta_m) \right],\tag{5}
$$

where  $\mathbb{S}^{(k-1)} = \mathcal{S}_1 \cup \mathcal{S}_2 \cup \cdots \cup \mathcal{S}_{k-1}$ , which represents the union set of the subsets in the former.  $\mathbb{S}^{(k-1)}$  are fixed and only  $\mathcal{S}_k$  will be updated. The subset  $\mathcal{S}_k$  is encouraged to match the corresponding  $k^{th}$  segment of the teacher trajectory to condense the knowledge in the latter epoch. Let  $n = \lfloor \frac{M}{K} \rfloor$ denote the length of trajectory segment to be matched by each subset  $S_k$  in the proposed framework. To strike a balance between providing adequate capacity for distillation and avoiding coupled synthetic data, the size of each subset  $S_k$  is well-controlled by K.

In the distillation phase, each subset is arranged in ascending order to be optimized sequentially. We reveal that the first subset  $S_1$  with  $\frac{1}{K}$  size of the original synthetic dataset  $\tilde{S}$  is sufficient to condense adequate knowledge in the former epoch. For the subsequent subset  $S_k$ , we encourage the  $k^{th}$  subset  $\mathcal{S}_k$  condense the knowledge different from those condensed in the previous subsets. This is achieved by minimizing the matching loss  $\mathcal{L}(\mathcal{S}_k \cup \mathbb{S}^{(k-1)}, \theta_m)$  while only  $\mathcal{S}_k$  will be updated.

During the evaluation phase, the subsets of the synthetic dataset are used sequentially to train the neural network  $f_{\theta}$ , with the weight parameters  $\theta$  being iteratively updated by  $\theta_k = \text{alg}(S_k, \theta_{k-1})$ . This training process emulates the sequential feature extraction of real dataset  $\tau$  during training. Further details regarding SeqMatch and the optimization of  $\theta^*$  can be found in Algorithm[.1.](#page-3-1)

# <span id="page-6-0"></span>5 Experiment

In this section, we provide implementation details for our proposed method, along with instructions for reproducibility. We compare the performance of SeqMatch against state-of-the-art dataset distillation methods on a variety of datasets. To ensure a fair and comprehensive comparison, we follow up the experimental setup as stated in  $[8, 30]$  $[8, 30]$  $[8, 30]$ . We provide more experiments to verify the effectiveness of SeqMatch including the results on ImageNet subsets and analysis experiments in Appendix [A.1](#page-13-0) due to page constraints.

#### 5.1 Experimental Setup

Datasets: We evaluate the performance of dataset distillation methods on several widely-used datasets across various resolutions. MNIST [\[28\]](#page-11-11), which is a fundamental classification dataset, is included with a resolution of  $28 \times 28$ . SVNH [\[36\]](#page-11-12) is also considered, which is composed of RGB images of house numbers cwith a resolution of  $32 \times 32$ . CIFAR10 and CIFAR100 [\[25\]](#page-11-6), two datasets frequently used in dataset distillation, are evaluated in this study. These datasets consist of 50, 000 training images and 10, 000 test images from 10 and 100 different categories, respectively. Additionally, our proposed method is evaluated on the Tiny ImageNet [\[27\]](#page-11-13) dataset with a resolution of  $64 \times 64$  and on the ImageNet [\[24\]](#page-11-14) subsets with a resolution of  $128 \times 128$ .

Evaluation Metric: The evaluation metric involves distillation phase and evaluation phase. In the former, the synthetic dataset is optimized with a distillation budget that typically restricts the number of images per class (ipc). We evaluate the performance of our method and baseline methods under the settings ipc =  $\{10, 50\}$ . We do not evaluate the setting with ipc = 1 since our approach requires  $\text{inc} \geq 2$ . To facilitate a clear comparison, we mark the factorization-based baselines with an asterisk (\*) since they often employ an additional decoder, following the suggestion in  $[30]$ . We employ 4-layer ConvNet [\[14\]](#page-10-15) in Tiny ImageNet dataset whereas for the other datasets we use a 3-layer ConvNet [\[14\]](#page-10-15).

In the evaluation phase, we utilize the optimized synthetic dataset to train neural networks using a standard training procedure. Specifically, we use each synthetic dataset to train five networks with random initializations for 1, 000 iterations and report the mean accuracy and its standard deviation of the results.

Implementation Details. To ensure the reproducibility of SeqMatch, we provide detailed implementation specifications. Our method relies on a single hyperparameter, denoted by  $K$ , which determines the number of subsets. In order to balance the inclusion of sufficient knowledge in each segment with the capture of high-level features in the later stages, we set  $K = \{2, 3\}$  for the scenarios where  $\text{inc} = \{10, 50\}$ , respectively. Notably, our evaluation results demonstrate that the choice of K remains consistent across the various datasets.

As a plug-in strategy, SeqMatch requires a backbone method for dataset synthesis. Each synthetic subset is optimized using a standard training procedure, specific to the chosen backbone method. The only hyperparameters that require adjustment in the backbone method are those that control the segments of the teacher trajectory to be learned by the synthetic dataset, whereas the remaining hyperparameters emain consistent without adjustment. Such adjustment is to ensure each synthetic subset effectively condenses the knowledge into stages. The precise hyperparameters of the backbone methods are presented in Appendix [A.3.](#page-14-0) We conduct our experiments on the server with four Tesla V100 GPUs.

#### 5.2 Results

Our proposed SeqMatch is plugged into the methods MTT [\[5\]](#page-10-1) and IDC [\[23\]](#page-11-15), which are denoted as SeqMatch-MTT and SeqMatch-IDC, respectively. As shown in [Table 1,](#page-7-1) the classification accuracies of ConvNet [\[14\]](#page-10-15) trained using each dataset distillation method are summarized. The results indicate that SeqMatch significantly outperforms the backbone method across various datasets, and even surpasses state-of-the-art baseline methods in different settings. Our method is demonstrated to outperform the state-of-the-art baseline methods in different settings among different datasets. Notably, SeqMatch achieves a greater performance improvement in scenarios with a high compression ratio (i.e.,  $\Delta$  ipc = 50). For instance, we observe a 3.5% boost in the performance of MTT [\[5\]](#page-10-1), achieving 51.2% accuracy on CIFAR-100. Similarly, we observe a 1.9% performance enhancement in IDC [\[23\]](#page-11-15),

<span id="page-7-4"></span><span id="page-7-1"></span>Table 1: Performance comparison of dataset distillation methods across a variety of datasets. Abbreviations of GM, TM, DM,META stand for gradient matching, trajectory matching, distribution matching, and meta-learning respectively. We reproduce the results of MTT [\[5\]](#page-10-1) and IDC [\[23\]](#page-11-15) and cite the results of the other baselines [\[30\]](#page-11-7). The best results of non-factorized methods (without decoders) are highlighted in orange font. The best results of factorization-based methods are highlighted in blue font.

| Methods                           | Schemes     | <b>MNIST</b>             |                      | <b>SVHN</b>       |                   | CIFAR-10          |                   | $CIFAR-100$       |                   | Tiny ImageNet                         |
|-----------------------------------|-------------|--------------------------|----------------------|-------------------|-------------------|-------------------|-------------------|-------------------|-------------------|---------------------------------------|
|                                   |             | $\overline{10}$          | 50                   | $\overline{10}$   | 50                | $\overline{10}$   | 50                | $\overline{10}$   | 50                | $\overline{10}$                       |
| DD [47]                           | <b>META</b> | 79.5<br>$\pm 8.1$        |                      |                   |                   | 36.8<br>$\pm 1.2$ |                   |                   |                   |                                       |
| $DC$ [53]                         | <b>GM</b>   | 94.7<br>$\pm 0.2$        | 98.8<br>$\pm 0.2$    | 76.1<br>$\pm 0.6$ | 82.3<br>$+0.3$    | 44.9<br>±0.5      | 53.9<br>±0.5      | 32.3<br>$+0.3$    | 42.8<br>$\pm 0.4$ |                                       |
| DSA [51]                          | <b>GM</b>   | 97.8                     | 99.2                 | 79.2              | 84.4              | 52.1              | 60.6              | 32.3              | 42.8              |                                       |
| DM [52]                           | DM          | $\pm 0.1$<br>97.3        | $\pm 0.1$<br>94.8    | $\pm 0.5$         | $+0.4$            | $\pm 0.5$<br>48.9 | ±0.5<br>63.0      | $+0.3$<br>29.7    | $\pm 0.4$<br>43.6 | 12.9                                  |
| CAFE $[46]$                       | DM          | $+0.3$<br>97.5           | $+0.2$<br>98.9       | 77.9              | 82.3              | $+0.6$<br>50.9    | ± 0.4<br>62.3     | $+0.3$<br>31.5    | $\pm 0.4$<br>42.9 | $\pm 0.4$                             |
|                                   |             | $\pm 0.1$<br>97.5        | $\pm 0.2$<br>98.3    | $\pm 0.6$<br>75.0 | $\pm 0.4$<br>85.0 | $\pm 0.5$<br>62.7 | ± 0.4<br>68.6     | ±0.2<br>28.3      | $\pm 0.2$         |                                       |
| KIP [37, 38]                      | <b>KRR</b>  | ±0.0                     | $\pm 0.1$            | ± 0.1             | $\pm 0.1$         | $\pm 0.3$         | ±0.2              | ± 0.1             |                   |                                       |
| FTD $[13]$                        | TM          |                          |                      |                   |                   | 66.6<br>±0.3      | 73.8<br>$\pm 0.2$ | 43.4<br>±0.3      | 50.7<br>$\pm 0.3$ | 24.5<br>$\pm 0.2$                     |
| MTT[5]                            | TM          | 97.3<br>$+0.1$           | 98.5                 | 79.9              | 87.7<br>$+0.3$    | 65.3<br>±0.7      | 71.6<br>$\pm 0.2$ | 40.1              | 47.7<br>$\pm 0.2$ | 23.2                                  |
| SeqMatch-MTT                      | TM          | 97.6                     | $\pm 0.1$<br>99.0    | $\pm$<br>80.2     | 88.5              | 66.2              | 74.4              | ±0.4<br>41.9      | 51.2              | $\pm 1.3$<br>23.8                     |
| IDC <sup>*</sup> [23] $\parallel$ | <b>GM</b>   | $\pm 0.2$<br>98.4        | $\pm 0.1$<br>99.1    | ±0.6<br>87.3      | $\pm 0.2$<br>90.2 | ±0.6<br>67.5      | ±0.5<br>74.5      | ±0.5<br>44.8      | ±0.3<br>51.4      | $\pm 0.3$<br>$\overline{\phantom{a}}$ |
| SeqMatch-IDC*                     | <b>GM</b>   | $\pm 0.1$<br>98.6        | $\pm 0.1$<br>99.2    | $\pm 0.2$<br>87.2 | $\pm 0.1$<br>92.1 | $\pm 0.5$<br>68.3 | $\pm 0.1$<br>75.3 | ±0.2<br>45.1      | $\pm 0.4$<br>51.9 |                                       |
|                                   | <b>META</b> | $\pm 0.1$<br>99.3        | $_{\pm 0.0}$<br>99.4 | $\pm 0.2$<br>89.1 | $\pm 0.1$<br>89.5 | $\pm 0.2$<br>71.2 | $\pm 0.2$<br>73.6 | ±0.3<br>42.9      | ±0.3              |                                       |
| $RTP^*[11]$                       |             | ±0.5                     | ±0.4                 | $\pm 0.2$         | $\pm 0.2$         | ±0.4              | ± 0.4             | ± 0.7             |                   |                                       |
| HaBa $*$ [33]                     | TM          | $\overline{\phantom{a}}$ |                      | 83.2<br>$\pm 0.4$ | 88.3<br>$\pm 0.1$ | 69.9<br>$\pm 0.4$ | 74.0<br>±0.2      | 40.2<br>±0.2      | 47.0<br>$\pm 0.2$ |                                       |
| Whole                             |             | 99.6<br>$_{\pm 0.0}$     |                      | 95.4<br>$\pm 0.1$ |                   | 84.8<br>±0.1      |                   | 56.2<br>$\pm 0.3$ |                   | 37.6<br>$\pm 0.4$                     |

achieving 92.1% accuracy on SVNH, which approaches the 95.4% accuracy obtained using the real dataset. These results suggest that our method is effective in mitigating the adverse effects of coupling and effectively condenses high-level features in high compression ratio scenarios.

Cross-Architecture Generalization: We also conducted experiments to evaluate cross-architecture generalization, as illustrated in [Table 2.](#page-7-3) The ability to generalize effectively across different architectures is crucial for the practical application of dataset distillation. We evaluated our proposed SeqMatch on the CIFAR-10 dataset with  $\text{ipc} = 50$ . Following the evaluation metric established in [\[13,](#page-10-3) [46\]](#page-12-16), three additional neural network architectures were utilized for evaluation: ResNet [\[19\]](#page-11-16), VGG [\[44\]](#page-12-17), and AlexNet [\[26\]](#page-11-17). Our SeqMatch approach demonstrated a significant improvement in performance during cross-architecture evaluation, highlighting its superior generalization ability.

Sequential Knowledge Acquisition: We conducted experiments on CIFAR-10 with  $\text{ipc} =$ 50, presented in [Figure 1,](#page-1-0) to investigate the inability of existing baseline methods to capture the knowledge learned in the latter epoch, as discussed in [subsection 4.1.](#page-3-0) Inspired by [\[16\]](#page-10-13), we utilized the change in instance-wise loss on real dataset to measure the effectiveness of condensing high-level features. Specifically, we recorded the loss of each instance from the real dataset  $T$  at every epoch, where the network was trained with synthetic dataset for only 20

<span id="page-7-0"></span>5.3 Discussions Table 2: Cross-Architecture Results trained with ConvNet on CIFAR-10 with  $\text{ipc} = 50$ . We cite the results reported in Du et al. [\[13\]](#page-10-3).

<span id="page-7-3"></span>

| Method       | Evaluation Model    |                     |                     |                     |
|--------------|---------------------|---------------------|---------------------|---------------------|
|              | ConvNet             | ResNet18            | VGG11               | AlexNet             |
| DC [53]      | 53.9<br>±0.5        | 20.8<br>±1.0        | 38.8<br>±1.1        | 28.7<br>±0.7        |
| CAFE [46]    | 55.5<br>±0.4        | 25.3<br>±0.9        | 40.5<br>±0.8        | 34.0<br>±0.6        |
| MTT [5]      | 71.6<br>±0.2        | 61.9<br>±0.7        | 55.4<br>±0.8        | 48.2<br>±1.0        |
| FTD [13]     | 73.8<br>±0.2        | 65.7<br>±0.3        | 58.4<br>±1.6        | 53.8<br>±0.9        |
| SeqMatch-MTT | 74.4<br>±0.5        | 68.4<br>±0.9        | 64.2<br>±0.7        | 50.7<br>±1.0        |
| SeqMatch-IDC | <b>75.3</b><br>±0.2 | <b>69.7</b><br>±0.6 | <b>73.4</b><br>±0.1 | <b>72.0</b><br>±0.2 |

<span id="page-7-2"></span> $\parallel$ Although IDC [\[23\]](#page-11-15) is not categorized as a factorization-based method, it employs data parameterization to better improve the performance of synthetic dataset. Therefore, we compare IDC to the factorization-based method as factorization can be treated as a kind of special data parameterization.

<span id="page-8-1"></span>iterations in each epoch. To distinguish hard instances from easy ones, we employed k-means algorithm [\[18\]](#page-11-18) to cluster all instances in the real dataset into two clusters based on the recorded instance-wise loss. The distribution of instances in terms of difficulty is as follows: 77% are considered easy instances, while 23% are classified as hard instances.

We evaluated MTT[\[5\]](#page-10-1) and SeqMatch as mentioned above. Our results show that MTT[5] overcondenses the knowledge learned in the former epoch. In contrast, SeqMatch is able to successfully capture the knowledge learned in the latter epoch.

Coupled Synthetic Subsets: In order to validate our hypothesis that the synthetic subset  $S^-$  is ineffective at condensing knowledge independently and results in over-condensation on the knowledge learned in the former epoch, we conducted experiments as shown in [Figure 2.](#page-4-0) We sorted the subsets  $S^+$  and  $S^-$  of the same size by the  $l_1$ -norm of the amplification function  $|R(s_i, f_{\theta_m})|$  as explained in [Figure 4.2.](#page-4-0) We then recorded the accuracy discrepancies between the separate networks trained by  $S^+$  and  $S^-$  with respect to the mean  $l_1$ -norm difference, i.e.,  $\mathbb{E}_{s_i \in S^+}[R(s_i, f_{\theta_0})|1]$  –  $\mathbb{E}_{s_i \in \mathcal{S}^-}[|R(s_i, f_{\theta_0})|_1].$ 

As shown in [Figure 2,](#page-4-0) the accuracy discrepancies increased linearly with the  $l_1$ -norm difference, which verifies our hypothesis that  $S^-$  is coupled with  $S^+$  and this coupling leads to the excessive condensation on low-level features. However, our proposed method, SeqMatch, is able to alleviate the coupling issue by encouraging  $S^-$  to condense knowledge more efficiently.

<span id="page-8-0"></span>Image /page/8/Figure/4 description: The image displays a grid of car images, organized into four rows labeled "Baseline", "1st Subset", "2nd Subset", and "3rd Subset". Each row contains eight individual car images. The images are somewhat blurry and appear to be generated or processed, with varying colors and lighting conditions. The cars depicted are diverse in type and color, including red, blue, silver, and yellow vehicles, seen from different angles.

Figure 3: Visualization example of "car" synthetic images distilled by MTT [\[5\]](#page-10-1) and SeqMatch from  $32 \times 32$ CIFAR-10 (ipc  $= 50$ ).

**Synthetic Image Visualization:** In order to demonstrate the distinction between MTT [\[5\]](#page-10-1) and SeqMatch, we visualized synthetic images within the "car" class from CIFAR-10  $\left[25\right]$  and visually compared them. As depicted in [Figure 3,](#page-8-0) the synthetic images produced by MTT exhibit more concrete features and closely resemble actual "car" images. Conversely, the synthetic images generated by SeqMatch in the  $2^{nd}$  and  $3^{rd}$  subsets possess more abstract attributes and contain complex car shapes. We provide more visualizations of the synthetic images in Appendix [A.2.](#page-13-1)

##### 5.4 Limitations and Future Work

We acknowledge the limitations of our work from two perspectives. Firstly, our proposed sequential optimization of synthetic subsets increases the overall training time, potentially doubling or tripling it. To address this, future research could investigate optimization methods that allow for parallel optimization of each synthetic subset. Secondly, as the performance of subsequent synthetic subsets builds upon the performance of previous subsets, a strategy is required to adaptively distribute the distillation budget of each subset. Further research could explore strategies to address this limitation and effectively enhance the performance of dataset distillation, particularly in high compression ratio scenarios.

# 6 Conclusion

In this study, we provide empirical evidence of the failure in condensing high-level features in dataset distillation attributed to the sequential acquisition of knowledge in training DNNs. We reveal that the static optimization of synthetic data leads to a bias in over-condensing the low-level features, predominantly extracted from the majority during the initial stages of training. To address this issue in a targeted manner, we introduce an adaptive and plug-in distillation strategy called SeqMatch. Our proposed strategy involves the division of synthetic data into multiple subsets, which are sequentially optimized, thereby promoting the effective condensation of high-level features learned in the later epochs. Through comprehensive experimentation on diverse datasets, we validate the effectiveness of our analysis and proposed strategy, achieving state-of-the-art performance.

# Acknowledgements

This work is support by Joey Tianyi Zhou's A\*STAR SERC Central Research Fund (Use-inspired Basic Research) and the Singapore Government's Research, Innovation and Enterprise 2020 Plan (Advanced Manufacturing and Engineering domain) under Grant A18A1b0045.

# **References**

- <span id="page-10-14"></span>[1] Devansh Arpit, Stanisław Jastrz˛ebski, Nicolas Ballas, David Krueger, Emmanuel Bengio, Maxinder S Kanwal, Tegan Maharaj, Asja Fischer, Aaron Courville, Yoshua Bengio, et al. A closer look at memorization in deep networks. In *International conference on machine learning*, pages 233–242. PMLR, 2017. [5](#page-4-1)
- <span id="page-10-8"></span>[2] Olivier Bachem, Mario Lucic, and Andreas Krause. Practical coreset constructions for machine learning. *arXiv preprint arXiv:1703.06476*, 2017. [3](#page-2-1)
- <span id="page-10-12"></span>[3] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [3](#page-2-1)
- <span id="page-10-0"></span>[4] Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. *Advances in neural information processing systems*, 33:1877–1901, 2020. [1](#page-0-0)
- <span id="page-10-1"></span>[5] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-0) [2,](#page-1-2) [3,](#page-2-1) [5,](#page-4-1) [7,](#page-6-0) [8,](#page-7-4) [9,](#page-8-1) [14,](#page-13-2) [15](#page-14-1)
- <span id="page-10-2"></span>[6] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3739–3748, 2023. [1](#page-0-0)
- <span id="page-10-9"></span>[7] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. *arXiv preprint arXiv:1203.3472*, 2012. [3](#page-2-1)
- <span id="page-10-7"></span>[8] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. *arXiv preprint arXiv:2207.09639*, 2022. [2,](#page-1-2) [7](#page-6-0)
- <span id="page-10-11"></span>[9] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. *arXiv preprint arXiv:2211.10586*, 2022. [3](#page-2-1)
- <span id="page-10-16"></span>[10] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A largescale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [14](#page-13-2)
- <span id="page-10-6"></span>[11] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. *arXiv preprint arXiv:2206.02916*, 2022. [2,](#page-1-2) [3,](#page-2-1) [8](#page-7-4)
- <span id="page-10-4"></span>[12] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? *arXiv preprint arXiv:2206.00240*, 2022. [2](#page-1-2)
- <span id="page-10-3"></span>[13] Jiawei Du, Yidi Jiang, Vincent TF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. *arXiv preprint arXiv:2211.11004*, 2022. [1,](#page-0-0) [2,](#page-1-2) [3,](#page-2-1) [8,](#page-7-4) [14](#page-13-2)
- <span id="page-10-15"></span>[14] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4367–4375, 2018. [7,](#page-6-0) [14](#page-13-2)
- <span id="page-10-5"></span>[15] Jack Goetz and Ambuj Tewari. Federated learning via synthetic data. *arXiv preprint arXiv:2008.04489*, 2020. [2](#page-1-2)
- <span id="page-10-13"></span>[16] Bo Han, Quanming Yao, Xingrui Yu, Gang Niu, Miao Xu, Weihua Hu, Ivor Tsang, and Masashi Sugiyama. Co-teaching: Robust training of deep neural networks with extremely noisy labels. *Advances in neural information processing systems*, 31, 2018. [5,](#page-4-1) [8](#page-7-4)
- <span id="page-10-10"></span>[17] Sariel Har-Peled and Akash Kushal. Smaller coresets for k-median and k-means clustering. In *Proceedings of the twenty-first annual symposium on Computational geometry*, pages 126–134, 2005. [3](#page-2-1)

- <span id="page-11-18"></span>[18] John A Hartigan and Manchek A Wong. Algorithm as 136: A k-means clustering algorithm. *Journal of the royal statistical society. series c (applied statistics)*, 28(1):100–108, 1979. [9](#page-8-1)
- <span id="page-11-16"></span>[19] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [8](#page-7-4)
- <span id="page-11-8"></span>[20] Zixuan Jiang, Jiaqi Gu, Mingjie Liu, and David Z Pan. Delving into effective gradient matching for dataset condensation. *arXiv preprint arXiv:2208.00311*, 2022. [3](#page-2-1)
- <span id="page-11-3"></span>[21] Haifeng Jin, Qingquan Song, and Xia Hu. Auto-keras: An efficient neural architecture search system. In *Proceedings of the 25th ACM SIGKDD international conference on knowledge discovery & data mining*, pages 1946–1956, 2019. [2](#page-1-2)
- <span id="page-11-0"></span>[22] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022. [1,](#page-0-0) [3](#page-2-1)
- <span id="page-11-15"></span>[23] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. *arXiv preprint arXiv:2205.14959*, 2022. [7,](#page-6-0) [8](#page-7-4)
- <span id="page-11-14"></span>[24] Alexander Kolesnikov, Lucas Beyer, Xiaohua Zhai, Joan Puigcerver, Jessica Yung, Sylvain Gelly, and Neil Houlsby. Big transfer (bit): General visual representation learning. In *European conference on computer vision*, pages 491–507. Springer, 2020. [7](#page-6-0)
- <span id="page-11-6"></span>[25] Alex Krizhevsky, Vinod Nair, and Geoffrey Hinton. CIFAR-10 and CIFAR-100 datasets. *URl: https://www. cs. toronto. edu/kriz/cifar. html*, 6(1):1, 2009. [2,](#page-1-2) [7,](#page-6-0) [9,](#page-8-1) [14](#page-13-2)
- <span id="page-11-17"></span>[26] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Advances in neural information processing systems*, 25, 2012. [8](#page-7-4)
- <span id="page-11-13"></span>[27] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [7,](#page-6-0) [14](#page-13-2)
- <span id="page-11-11"></span>[28] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998. [7](#page-6-0)
- <span id="page-11-1"></span>[29] Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*, 2022. [1,](#page-0-0) [3](#page-2-1)
- <span id="page-11-7"></span>[30] Shiye Lei and Dacheng Tao. A comprehensive survey to dataset distillation. *arXiv preprint arXiv:2301.05603*, 2023. [3,](#page-2-1) [7,](#page-6-0) [8](#page-7-4)
- <span id="page-11-4"></span>[31] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Soft-label anonymous gastric x-ray image distillation. In *2020 IEEE International Conference on Image Processing (ICIP)*, pages 305–309. IEEE, 2020. [2](#page-1-2)
- <span id="page-11-9"></span>[32] Ping Liu, Xin Yu, and Joey Tianyi Zhou. Meta knowledge condensation for federated learning. *arXiv preprint arXiv:2209.14851*, 2022. [3](#page-2-1)
- <span id="page-11-5"></span>[33] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *arXiv preprint arXiv:2210.16774*, 2022. [2,](#page-1-2) [3,](#page-2-1) [8,](#page-7-4) [14](#page-13-2)
- <span id="page-11-10"></span>[34] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022. [3](#page-2-1)
- <span id="page-11-2"></span>[35] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. *arXiv preprint arXiv:2302.06755*, 2023. [1](#page-0-0)
- <span id="page-11-12"></span>[36] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Bo Wu, and Andrew Y Ng. Reading digits in natural images with unsupervised feature learning. 2011. [7](#page-6-0)

- <span id="page-12-9"></span>[37] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [2,](#page-1-2) [3,](#page-2-1) [8](#page-7-4)
- <span id="page-12-10"></span>[38] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [2,](#page-1-2) [3,](#page-2-1) [8](#page-7-4)
- <span id="page-12-6"></span>[39] Hieu Pham, Melody Guan, Barret Zoph, Quoc Le, and Jeff Dean. Efficient neural architecture search via parameters sharing. In *International conference on machine learning*, pages 4095– 4104. PMLR, 2018. [2](#page-1-2)
- <span id="page-12-7"></span>[40] Pengzhen Ren, Yun Xiao, Xiaojun Chang, Po-Yao Huang, Zhihui Li, Xiaojiang Chen, and Xin Wang. A comprehensive survey of neural architecture search: Challenges and solutions. *ACM Computing Surveys (CSUR)*, 54(4):1–34, 2021. [2](#page-1-2)
- <span id="page-12-4"></span>[41] Andrea Rosasco, Antonio Carta, Andrea Cossu, Vincenzo Lomonaco, and Davide Bacciu. Distilled replay: Overcoming forgetting through synthetic samples. *arXiv preprint arXiv:2103.15851*, 2021. [2](#page-1-2)
- <span id="page-12-0"></span>[42] Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *arXiv preprint arXiv:2301.04272*, 2023. [1](#page-0-0)
- <span id="page-12-13"></span>[43] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. *arXiv preprint arXiv:1708.00489*, 2017. [3](#page-2-1)
- <span id="page-12-17"></span>[44] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [8](#page-7-4)
- <span id="page-12-14"></span>[45] Ivor W Tsang, James T Kwok, Pak-Ming Cheung, and Nello Cristianini. Core vector machines: Fast svm training on very large data sets. *Journal of Machine Learning Research*, 6(4), 2005. [3](#page-2-1)
- <span id="page-12-16"></span>[46] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196–12205, 2022. [8](#page-7-4)
- <span id="page-12-11"></span>[47] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [2,](#page-1-2) [3,](#page-2-1) [8](#page-7-4)
- <span id="page-12-1"></span>[48] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023. [1](#page-0-0)
- <span id="page-12-12"></span>[49] Matthew D Zeiler and Rob Fergus. Visualizing and understanding convolutional networks. In *Computer Vision–ECCV 2014: 13th European Conference, Zurich, Switzerland, September 6-12, 2014, Proceedings, Part I 13*, pages 818–833. Springer, 2014. [2,](#page-1-2) [4](#page-3-3)
- <span id="page-12-2"></span>[50] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 11950–11959, 2023. [1](#page-0-0)
- <span id="page-12-8"></span>[51] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [2,](#page-1-2) [3,](#page-2-1) [8](#page-7-4)
- <span id="page-12-5"></span>[52] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023. [2,](#page-1-2) [3,](#page-2-1) [8](#page-7-4)
- <span id="page-12-3"></span>[53] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021. [1,](#page-0-0) [2,](#page-1-2) [3,](#page-2-1) [4,](#page-3-3) [8](#page-7-4)
- <span id="page-12-15"></span>[54] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022. [3](#page-2-1)

<span id="page-13-2"></span>

## A More Experiements

<span id="page-13-0"></span>

##### A.1 ImageNet Subsets

To assess the efficacy of our approach, we conducted experiments on subsets of the ImageNet dataset [\[10\]](#page-10-16). These subsets were constructed by selecting ten pertinent categories from the ImageNet-1k dataset  $[10]$ , with a resolution of  $128 \times 128$ . Consequently, the ImageNet subsets pose greater challenges compared to the CIFAR-10/100 [\[25\]](#page-11-6) and Tiny ImageNet [\[27\]](#page-11-13) datasets. We adhered to the configuration of the ImageNet subsets as suggested by previous studies  $[5, 13, 33]$  $[5, 13, 33]$  $[5, 13, 33]$  $[5, 13, 33]$  $[5, 13, 33]$ , encompassing subsets such as ImageNette (diverse objects), ImageWoof (dog breeds), ImageFruits (various fruits), and ImageMeow (cats).

To synthesize the dataset, we employed a 5-layer ConvNet  $[14]$  with a parameter setting of  $\text{ipc} = 10$ . The evaluation of the synthetic dataset involved performing five trials with randomly initialized networks. We compared the outcomes of our proposed method, referred to as SeqMatch, with the baseline approach MTT [\[5\]](#page-10-1), as well as the plug-in strategies FTD [\[13\]](#page-10-3) and HaBa [\[33\]](#page-11-5) which build upon MTT.

The comprehensive results are presented in Table [3.](#page-13-3) Our proposed SeqMatch consistently outperformed the baseline MTT across all subsets. Notably, we achieved a performance improvement of 4.3% on the ImageFruit subset. Additionally, SeqMatch demonstrated superior performance compared to HaBa and achieved comparable results to FTD.

<span id="page-13-3"></span>Table 3: The performance comparison trained with 5-layer ConvNet on the ImageNet subsets with a resolution of  $128 \times 128$ . We cite the results as reported in MTT [\[5\]](#page-10-1), FTD [\[13\]](#page-10-3) and HaBa [\[33\]](#page-11-5). The latter two methods, FTD and HaBa, are plug-in strategies that build upon the foundation of MTT. Our proposed approach, SeqMatch, exhibits superior performance compared to both MTT and HaBa, demonstrating a significant improvement in results.

|               |           | ImageNette ImageWoof ImageFruit ImageMeow |           |           |
|---------------|-----------|-------------------------------------------|-----------|-----------|
| Real dataset  | 87.4      | 67.0                                      | 63.9      | 66.7      |
|               | $+1.0$    | $+1.3$                                    | $+2.0$    | $+1.1$    |
| MTT [5]       | 63.0      | 35.8                                      | 40.3      | 40.4      |
|               | $+1.3$    | $+1.8$                                    | $+1.3$    | $+2.2$    |
| FTD $[13]$    | 67.7      | 38.8                                      | 44.9      | 43.3      |
|               | $\pm 1.3$ | $\pm 1.4$                                 | $\pm 1.5$ | $\pm 0.6$ |
| HaBa $*$ [33] | 64.7      | 38.6                                      | 42.5      | 42.9      |
|               | $\pm 1.6$ | $\pm 1.2$                                 | ±1.6      | ±0.9      |
| SeqMatch-MTT  | 66.9      | 38.7                                      | 44.6      | 44.8      |
|               | ±1.7      | $+1.1$                                    | ±1.7      | $\pm 1.2$ |
| SeqMatch-FTD  | 70.6      | 41.1                                      | 46.5      | 45.4      |
|               | ±1.5      | ±1.4                                      | ±1.2      | $\pm 1.2$ |

<span id="page-13-1"></span>

##### A.2 More Visualizations

Instance-wise loss change: We have presented the average loss change of easy and hard instances in [Figure 1,](#page-1-0) revealing that MTT failed to effectively condense the knowledge learned from the hard instances. To avoid the bias introduced by averaging, we have meticulously recorded and visualized the precise loss change of each individual instance. This is accomplished by employing a heatmap representation, as demonstrated in [Figure 6.](#page-15-0) Each instance is depicted as a horizontal line exhibiting varying colors, with deeper shades of blue indicating higher loss values. Following the same clustering approach as depicted in Figure [1,](#page-1-0) we proceed to visualize the hard instances at the top and the easy instances at the bottom.

The individual loss changes of MTT, depicted in Figure [6,](#page-15-0) remain static across epochs. The losses of easy instances decrease to a small value during the initial stages, while the losses of hard instances persist at a high value until the end of training. These results confirm that MTT excessively focuses on low-level features. In contrast, the visualization of SeqMatch clearly exhibits the effect of color gradient, indicating a decrease in loss for the majority of instances. Notably, the losses of hard instances experience a significant decrease when a subsequent subset is introduced. These results validate that SeqMatch effectively consolidates knowledge in a sequential manner.

<span id="page-14-1"></span>Image /page/14/Figure/0 description: This image contains two contour plots side-by-side, both illustrating instance-wise loss from the target dataset over epochs. The left plot is titled "MTT" and the right plot is titled "SeqMatch". Both plots have the x-axis labeled "Epoch" ranging from 0 to 45. The y-axis for both plots is labeled "Instance loss from target dataset". A color bar on the right indicates the "Instance-wise Loss", with values ranging from 0.0 (light green) to 1.0 (dark blue). The MTT plot shows a generally smooth gradient of loss, with higher losses concentrated at the top and lower losses towards the bottom and right. The SeqMatch plot exhibits a more complex pattern with distinct horizontal bands of varying loss values across different epochs, suggesting a more dynamic or fluctuating loss behavior during training.

Figure 4: The heatmap illustrates the loss change of each instance in the real dataset across epochs. Each row in the heatmap represents an instance, while the deeper blue color denotes higher instance-wise loss. The network is trained with the synthetic datasets distilled by MTT and SeqMatch. Left: MTT fails to reduce the loss of hard instances while excessively reducing the loss of easy instances. Right: SeqMatch minimizes the loss of both the hard and easy instances.

<span id="page-14-2"></span>Image /page/14/Figure/2 description: This image displays a grid of images, organized into rows labeled "IPC2 MTT", "IPC3 MTT", "1st Subset", "2nd Subset", and "3rd Subset". Each row contains 12 smaller images. The columns appear to represent different categories or classes of objects. The images themselves are somewhat abstract or blurry, but recognizable objects include birds, cars, dogs, deer, horses, and boats. The overall presentation suggests a comparison or visualization of results from a machine learning model, possibly related to image classification or generation.

Figure 5: Visualization example of synthetic images distilled by MTT [\[5\]](#page-10-1) and SeqMatch from  $32 \times 32$  CIFAR-10  $(ipc = {2, 3})$ . SeqMatch(ipc=2) is seamlessly embedded as the first two rows within the nameSeqMatch(ipc=3) visualization.

**Synthetic Dataset Visualization:** We compare the synthetic images with  $\text{ipc} = \{2, 3\}$  from the CIFAR10 dataset to highlight the differences between subsets of Seqmatch in [Figure 5.](#page-14-2) We provide more visualizations of the synthetic datasets for  $\text{ipc} = 10$  from the  $128 \times 128$  resolution ImageNet dataset: ImageWoof subset in [Figure 7](#page-16-0) and ImageMeow subset in [Figure 8.](#page-17-0) In addition, parts of the visualizations of synthetic images from the  $32 \times 32$  resolution CIFAR-100 dataset are showed in [Figure 6.](#page-15-0) We observe that the synthetic images generated by SeqMatch in the subsequent subset contains more abstract features than the previous subset.

<span id="page-14-0"></span>

##### A.3 Hyperparameter Details

The hyperparameters K of SeqMatch-MTT is set with  $\{2, 3\}$  for the settings ipc =  $\{10, 50\}$ , respectively. The optimal value of hyperparameter  $K$  is obtained via grid searches within the set  $\{2, 3, 4, 5, 6\}$  in a validation set within the CIFAR-10 dataset. We find that the subset with a small size will fail to condense the adequate knowledge from the corresponding segment of teacher trajectories,

<span id="page-15-0"></span>Image /page/15/Picture/0 description: The image displays two grids of synthetic images, each containing 70 images arranged in 7 rows and 10 columns. The top grid shows images of apples, fish, babies, bears, bedrooms, flowers, insects, bicycles, and bottles. The bottom grid also shows similar categories of images, with variations in the style and composition. The images appear to be generated by a machine learning model, possibly for visualization or data augmentation purposes.

Figure 6: Visualization of the first 10 classes of synthetic images distilled by SeqMatch from  $32 \times 32$  CIFAR- $100$  (ipc = 10). The initial 5 image rows and the final 5 image rows match the first and second subsets, respectively.

resulting in performance degradation in the subsequent subsets. For the rest of the hyperparamters, we report them in [Table 4.](#page-16-1)

 $\mathbb{F}_{\text{ImageFruit}}$  has different setting of Max Start Epoch from other ImageNet subesets: {10,10}

<span id="page-16-0"></span>Image /page/16/Figure/0 description: The image displays two grids of smaller images, each grid containing 8 rows and 10 columns of abstract, colorful images. The overall impression is a mosaic of stylized animal faces and figures, with many images featuring prominent eyes, fur-like textures, and canine or feline features. The colors are varied, with blues, greens, yellows, and reds appearing frequently, often blended in a dreamlike or impressionistic manner. The arrangement suggests a visualization of learned features or patterns from a machine learning model, possibly related to animal recognition.

Figure 7: Visualization of the synthetic images distilled by SeqMatch from  $32 \times 32$  ImageWoof (ipc = 10). The initial 5 image rows and the final 5 image rows match the first and second subsets, respectively.

<span id="page-16-1"></span>

| Table 4: Hyperparameter values we used for SeqMatch-MTT in the main result table. Most of the     |
|---------------------------------------------------------------------------------------------------|
| hyperparameters "Max Start Epoch" and "Synthetic Step" are various with the subsets, we use a     |
| sequential numbers to denote the parameters used in the corresponding subsets. "Img." denotes the |
| abbreviation of ImageNet.                                                                         |

| ipc                       | CIFAR-10  |              | CIFAR-100 |              | Tiny Img.<br>10 | Img. Subsets<br>10 |
|---------------------------|-----------|--------------|-----------|--------------|-----------------|--------------------|
|                           | 10        | 50           | 10        | 50           |                 |                    |
| K                         | 2         | 3            | 2         | 3            | 2               | 2                  |
| Max Start Epoch           | ${20,10}$ | ${20,20,10}$ | ${20,40}$ | ${40,20,20}$ | ${20,10}$       | ${10,5}$ **        |
| Synthetic Step            | ${30,80}$ | 30           | 30        | 80           | 20              | 20                 |
| Expert Epoch              | ${2,3}$   | 2            | 2         | 2            | 2               | 2                  |
| Synthetic Batch Size      | -         | -            | -         | 125          | 100             | 20                 |
| Learning Rate (Pixels)    | 100       | 100          | 1000      | 1000         | 10000           | 100000             |
| Learning Rate (Step Size) | $1e-5$    | $1e-5$       | $1e-5$    | $1e-5$       | $1e-4$          | $1e-6$             |
| Learning Rate (Teacher)   | 0.001     | 0.01         | 0.01      | 0.01         | 0.01            | 0.01               |

<span id="page-17-0"></span>Image /page/17/Picture/0 description: The image displays two grids of synthetic images, each containing 64 smaller images arranged in an 8x8 grid. The top grid and the bottom grid are visually similar, with each smaller image appearing to be a stylized representation of an animal's face, possibly cats, lions, or leopards, rendered with vibrant, abstract colors and patterns. The overall impression is a visualization of features learned by a neural network.

Figure 8: Visualization of the synthetic images distilled by SeqMatch from  $32 \times 32$  ImageMeow (ipc = 10). The initial 5 image rows and the final 5 image rows match the first and second subsets, respectively.
# <span id="page-0-1"></span>DataDAM: Efficient Dataset Distillation with Attention Matching

<PERSON><sup>1</sup>; <PERSON><PERSON><sup>1\*</sup>, <PERSON><PERSON><PERSON><sup>2,3</sup>, <PERSON><sup>2</sup>, <PERSON><sup>1</sup>, and <PERSON><PERSON><sup>1</sup>

<sup>1</sup>University of Toronto <sup>2</sup>Royal Bank of Canada (RBC) <sup>3</sup>University of Waterloo

{ahmad.sajedi,samir.khaki}@mail.utoronto.ca

Code: <https://github.com/DataDistillation/DataDAM>

## Abstract

Researchers have long tried to minimize training costs in deep learning while maintaining strong generalization across diverse datasets. Emerging research on dataset distillation aims to reduce training costs by creating a small synthetic set that contains the information of a larger real dataset and ultimately achieves test accuracy equivalent to a model trained on the whole dataset. Unfortunately, the synthetic data generated by previous methods are not guaranteed to distribute and discriminate as well as the original training data, and they incur significant computational costs. Despite promising results, there still exists a significant performance gap between models trained on condensed synthetic sets and those trained on the whole dataset. In this paper, we address these challenges using efficient Dataset Distillation with Attention Matching (DataDAM), achieving state-of-the-art performance while reducing training costs. Specifically, we learn synthetic images by matching the spatial attention maps of real and synthetic data generated by different layers within a family of randomly initialized neural networks. Our method outperforms the prior methods on several datasets, including CIFAR10/100, TinyImageNet, ImageNet-1K, and subsets of ImageNet-1K across most of the settings, and achieves improvements of up to 6.5% and 4.1% on CIFAR100 and ImageNet-1K, respectively. We also show that our high-quality distilled images have practical benefits for downstream applications, such as continual learning and neural architecture search.

## 1. Introduction

Deep learning has been highly successful in various fields, including computer vision and natural language processing, due to the use of large-scale datasets and modern Deep Neural Networks (DNNs) [\[14,](#page-9-0) [23,](#page-9-1) [17,](#page-9-2) [26,](#page-9-3) [43\]](#page-10-0).

<span id="page-0-0"></span>Image /page/0/Figure/10 description: The image displays two t-SNE plots labeled "CAFE" and "DataDAM" at the top, showing clusters of data points represented by colored stars. Below these plots, a line graph labeled "(b)" illustrates testing accuracy versus images per class. The graph features multiple colored lines representing different methods: "Random" (blue), "DC" (yellow), "DSA" (green), "DM" (red), "DataDAM" (purple), and "Whole Dataset" (black). The x-axis ranges from 0 to 1000 images per class, and the y-axis ranges from 10% to 90% testing accuracy. The caption "Figure 1: (a) Data distribution of the distilled images on the CI" is partially visible at the bottom.

Figure 1: (a) Data distribution of the distilled images on the CI-FAR10 dataset with 50 images per class (IPC50) for CAFE [\[52\]](#page-10-1) and DataDAM. (b) Performance comparison with state-of-the-art methods on the CIFAR10 dataset for varying IPCs.

However, extensive infrastructure resources for training, hyperparameter tuning, and architectural searches make it challenging to reduce computational costs while maintaining comparable performance. Two primary approaches to address this issue are model-centric and data-centric. Model-centric methods involve model compression techniques [\[24,](#page-9-4) [57,](#page-10-2) [1,](#page-9-5) [55,](#page-10-3) [59,](#page-11-0) [44,](#page-10-4) [27\]](#page-9-6), while data-centric methods concentrate on constructing smaller datasets with enough information for training, which is the focus of this paper. A traditional data-centric approach is the coreset selection method, wherein we select a representative subset of an original dataset  $[41, 8, 4, 46, 49]$  $[41, 8, 4, 46, 49]$  $[41, 8, 4, 46, 49]$  $[41, 8, 4, 46, 49]$  $[41, 8, 4, 46, 49]$  $[41, 8, 4, 46, 49]$  $[41, 8, 4, 46, 49]$  $[41, 8, 4, 46, 49]$  $[41, 8, 4, 46, 49]$ ; however, these methods have limitations as they rely on heuristics to generate a coarse approximation of the whole dataset, which may lead to a suboptimal solution for downstream tasks like image

<sup>\*</sup>Equal contribution

<span id="page-1-0"></span>classification [\[49,](#page-10-7) [41\]](#page-10-5). Dataset distillation (or condensation) [\[53\]](#page-10-8) is proposed as an alternative, which distills knowledge from a large training dataset into a smaller synthetic set such that a model trained on it achieves competitive testing performance with one trained on the real dataset. The condensed synthetic sets contain valuable information, making them a popular choice for various machine learning applications like continual learning [\[53,](#page-10-8) [64,](#page-11-1) [62\]](#page-11-2), neural architecture search [\[13,](#page-9-9) [63,](#page-11-3) [64\]](#page-11-1), federated learning [\[58,](#page-10-9) [66\]](#page-11-4), and privacypreserving [\[15,](#page-9-10) [50\]](#page-10-10) tasks.

Dataset distillation was first proposed by Wang *et al*. [\[53\]](#page-10-8) where bi-level meta-learning was used to optimize model parameters on synthetic data in the inner loop and refine the data with meta-gradient updates to minimize the loss on the original data in the outer loop. Various methods have been proposed to overcome the computational expense of this method, including approximating the inner optimization with kernel methods [\[5,](#page-9-11) [38,](#page-10-11) [37,](#page-10-12) [65\]](#page-11-5), surrogate objectives like gradient matching [\[64,](#page-11-1) [62,](#page-11-2) [33\]](#page-10-13), trajectory matching [\[9\]](#page-9-12), and distribution matching [\[52,](#page-10-1) [63\]](#page-11-3). The kernel-based methods and gradient matching work still require bi-level optimization and second-order derivation computation, making training a difficult task. Trajectory matching [\[9\]](#page-9-12) demands significant GPU memory for extra disk storage and expert model training. CAFE [\[52\]](#page-10-1) uses dynamic bi-level optimization with layer-wise feature alignment, but it may generate biased images and incur a significant time cost (Figure [1\)](#page-0-0). Thus, these methods are not scalable for larger datasets such as ImageNet-1K  $[14]$ . Distribution matching (DM)  $[63]$  was proposed as a scalable solution for larger datasets by skipping optimization steps in the inner loop. However, DM usually underperforms compared to prior methods [\[9\]](#page-9-12).

In this paper, we propose a new framework called "Dataset Distillation with Attention Matching (DataDAM)" to overcome computational problems, achieve an unbiased representation of the real data distribution, and outperform the performance of the existing methods. Due to the effectiveness of randomly initialized networks in generating strong representations that establish a distance-preserving embedding of the data [\[7,](#page-9-13) [45,](#page-10-14) [19,](#page-9-14) [63\]](#page-11-3), we leverage multiple randomly initialized DNNs to extract meaningful representations from real and synthetic datasets. We align their most discriminative feature maps using the Spatial Attention Matching (SAM) module and minimize the distance between them with the MSE loss. We further reduce the last-layer feature distribution disparities between the two datasets with a complementary loss as a regularizer. Unlike existing methods [\[64,](#page-11-1) [52,](#page-10-1) [9\]](#page-9-12), our approach does not rely on pre-trained network parameters or employ bi-level optimization, making it a promising tool for synthetic data generation. The generated synthetic dataset does not introduce any bias into the data distribution while outperforming concurrent methods, as shown in Figure [1.](#page-0-0)

The contributions of our study are:

[C1]: We proposed an effective end-to-end dataset distillation method with attention matching and feature distribution alignment to closely approximate the distribution of the real dataset with low computational costs.

[C2]: Our method is evaluated on computer vision datasets with different resolutions, where it achieves stateof-the-art results across multiple benchmark settings. Our approach offers up to a 100x reduction in training costs while simultaneously enabling cross-architecture generalizations.

[C3]: Our distilled data can enhance downstream applications by improving memory efficiency for continual learning and accelerating neural architecture search through a more representative proxy dataset.

## 2. Related Work

Dataset Distillation. Wang *et al*. [\[53\]](#page-10-8) first introduced dataset distillation by expressing network parameters as a function of synthetic data and optimizing the synthetic set to minimize the training loss on real training data. Later works extended this approach with soft labels [\[5\]](#page-9-11) and a generator network [\[48\]](#page-10-15). Researchers have proposed simplifying the neural network model in bi-level optimization using kernel methods, such as ridge regression, which has a closedform solution [\[5,](#page-9-11) [65\]](#page-11-5), and a kernel ridge regression model with Neural Tangent Kernel [\[32\]](#page-10-16) (NTK) that approximates the inner optimization [\[38,](#page-10-11) [37\]](#page-10-12). Alternatively, some studies have utilized surrogate objectives to address unrolled optimization problems. Dataset condensation (DC) [\[64\]](#page-11-1) and DCC [\[33\]](#page-10-13) generate synthetic images by matching the weight gradients of neural networks on real and distilled training datasets, while Zhao *et al*. [\[62\]](#page-11-2) improve gradient matching with data augmentation. MTT [\[9\]](#page-9-12) matches model parameter trajectories trained with synthetic and real datasets, and CAFE [\[63\]](#page-11-3) and DM [\[52\]](#page-10-1) match features generated by a model using distilled and real datasets. However, these methods have limitations, including bi-level optimization [\[64,](#page-11-1) [62,](#page-11-2) [52,](#page-10-1) [32\]](#page-10-16), second-order derivative computation  $[64]$ , generating biased examples [\[62,](#page-11-2) [52\]](#page-10-1), and massive GPU memory demands [\[9,](#page-9-12) [65\]](#page-11-5). In contrast, our approach matches the spatial attention map in intermediate layers, reducing memory costs while outperforming most existing methods on standard benchmarks.

Coreset Selection. Coreset selection is another datacentric approach that chooses a representative subset of an original dataset using heuristic selection criteria. For example, random selection [\[41\]](#page-10-5) selects samples randomly; Herding [\[8,](#page-9-7) [4\]](#page-9-8) selects the samples closest to the cluster center for each class center; K-Center [\[46\]](#page-10-6) chooses multiple center points of a class to minimize the maximum distance between data points and their nearest center point; and [\[49\]](#page-10-7) identifies

<span id="page-2-1"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: This figure illustrates the architecture of the Domain Adaptation Method (DAM) which includes Spatial Attention Matching (SAM). Part (a) shows the overall framework with a real dataset Tk and a synthetic dataset Sk. Both datasets are passed through a series of layers (Layer 1 to Layer L) of a neural network. The outputs from these layers are fed into the Spatial Attention Matching (SAM) module. The SAM module then calculates a total loss (Ltotal) composed of MSE Loss and MMD Loss. Part (b) details the process within the SAM module, which involves a Spatial Mapping Function, Spatial-wise Vectorization, Normalization, and Batch Averaging. Forward pass and backpropagation are indicated by arrows.

Figure 2: (a) Illustration of the proposed DataDAM method. DataDAM includes a Spatial Attention Matching (SAM) module to capture the dataset's distribution and a complementary loss for matching the feature distributions in the last layer of the encoder network. (b) The internal architecture of the SAM module.

training samples that are easily forgotten during the training process. However, heuristics-based methods may not be optimal for downstream tasks like image classification, and finding an informative corset may be challenging when the dataset's information is not concentrated in a few samples. Instead, our approach learns a computationally efficient synthetic set that is not limited to a subset of the original training samples.

Attention Mechanism. Attention has been widely used in deep learning to improve performance on various tasks [\[2,](#page-9-15) [54,](#page-10-17) [60\]](#page-11-6), with initial applications in natural language processing by Bahdanau *et al*. [\[2\]](#page-9-15) for language translation. Attention has since been used in computer vision, with global attention models [\[54\]](#page-10-17) for improved classification accuracy on image datasets and convolutional block attention modules [\[56\]](#page-10-18) for learning to attend to informative feature maps. Attention has also been used for model compression in knowledge distillation [\[60\]](#page-11-6). However, this mechanism has not been explored in the context of dataset distillation. To fill this gap, we propose a spatial attention matching module to approximate the distribution of the real dataset.

## 3. Methodology

In this section, we propose a novel end-to-end framework called Dataset Distillation with Attention Matching (DataDAM), which leverages attention maps to synthesize data that closely approximates the real training data distribution. The high dimensionality of training images makes

it difficult to estimate the real data distribution accurately. Therefore, we represent each training image using spatial attention maps generated by different layers within a family of randomly initialized neural networks. These maps effectively highlight the most discriminative regions of the input image that the network focuses on at different layers (early, intermediate, and last layers) while capturing low-, mid-, and high-level representation information of the image. Although each individual network provides a partial interpretation of the image, the family of these randomly initialized networks produces a more comprehensive representation.

### 3.1. Dataset Distillation with Attention Matching

Given a large-scale dataset  $\mathcal{T} = \{(\boldsymbol{x}_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  containing  $|\mathcal{T}|$  real image-label pairs, we first initialize a learnable synthetic dataset  $S = \{ (s_j, y_j) \}_{j=1}^{|S|}$  with  $|S|$  synthetic image and label pairs, by using either random noise or a selection of real images obtained through random sampling or a clustering algorithm such as K-Center  $[13, 46]$  $[13, 46]$  $[13, 46]$ . For each class k, we sample a batch of real and synthetic data (*i.e.*  $B_k^{\mathcal{T}}$  and  $B_k^{\mathcal{S}}$ , resp.) and extract features using a neural network  $\phi_{\theta}(\cdot)$ with standard network random initialization  $\theta$  [\[22\]](#page-9-16). Figure [2](#page-2-0) shows the proposed approach, where the neural network  $\phi_{\theta}(\cdot)$ , consisting of L layers, is employed to embed the real and synthetic sets. The network generates feature maps for each dataset, represented as  $\phi_{\bm{\theta}}(\mathcal{T}_k) = [\bm{f}_{\bm{\theta},1}^{\mathcal{T}_k}, \cdots, \bm{f}_{\bm{\theta},L}^{\mathcal{T}_k}]$  and  $\phi_{\theta}(\mathcal{S}_k) = [f^{\mathcal{S}_k}_{\theta,1}, \cdots, f^{\mathcal{S}_k}_{\theta, L}]$ , respectively. The feature  $f^{\mathcal{T}_k}_{\theta, l}$ is a multi-dimensional array in  $\mathbb{R}^{|B_k^{\mathcal{T}}| \times C_l \times W_l \times H_l}$ , coming from the real dataset in the  $l^{\text{th}}$  layer, where  $C_l$  represents the

<span id="page-3-3"></span>number of channels and  $H_l \times W_l$  is the spatial dimensions. Similarly, a feature  $f_{\theta, l}^{\mathcal{S}_k}$  is extracted for the synthetic set.

The Spatial Attention Matching (SAM) module then generates attention maps for the real and synthetic images using a feature-based mapping function  $A(\cdot)$ . The function takes the feature maps of each layer (except the last layer) as an input and outputs two separate attention maps:  $A\big(\phi_{\bm{\theta}}(\mathcal{T}_k)\big) =$  $[a_{\theta,1}^{\mathcal{T}_k}, \cdots, a_{\theta,L-1}^{\mathcal{T}_k}]$  and  $A(\phi_{\theta}(\mathcal{S}_k)) = [a_{\theta,1}^{\mathcal{S}_k}, \cdots, a_{\theta,L-1}^{\mathcal{S}_k}]$ for the real and synthetic sets, respectively. Prior studies  $[60, 61]$  $[60, 61]$  $[60, 61]$  have shown that the absolute value of a hidden neuron activation can indicate its importance for a given input, thus we create a spatial attention map by aggregating the absolute values of the feature maps across the channel dimension. This means that the feature map  $f_{\theta, l}^{\mathcal{T}_k}$  of the  $l^{\text{th}}$  layer is converted into a spatial attention map  $\boldsymbol{a}^{\mathcal{T}_k}_{\boldsymbol{\theta},l} \in \mathbb{R}^{|B_k^{\mathcal{T}}| \times W_l \times H_l}$ using the following pooling operation:

$$
A(\boldsymbol{f}_{\boldsymbol{\theta},l}^{\mathcal{T}_k}) = \sum_{i=1}^{C_l} |(\boldsymbol{f}_{\boldsymbol{\theta},l}^{\mathcal{T}_k})_i|^p, \qquad (1)
$$

where,  $(f_{\theta,l}^{\mathcal{T}_k})_{i} = f_{\theta,l}^{\mathcal{T}_k}(:,i,:,:)$  is the feature map of channel i from the  $l^{\text{th}}$  layer and the power and absolute value operations are applied element-wise. The resulting attention map emphasizes the spatial locations associated with neurons with the highest activations. This helps retain the most informative regions and generates a more efficient feature descriptor. In a similar manner, the attention maps for synthetic data can be obtained as  $a_{\theta,l}^{\mathcal{S}_k}$ . The effect of parameter  $p$  is studied in the supplementary materials.

To capture the distribution of the original training set at different levels of representations, we compare the normalized spatial attention maps of each layer (excluding the last layer) between the real and synthetic sets using the loss function  $\mathcal{L}_{SAM}$ , which is formulated as

$$
\mathop{\mathbb{E}}_{\boldsymbol{\theta}\sim P_{\boldsymbol{\theta}}} \bigg[ \sum_{k=1}^K \sum_{l=1}^{L-1} \left\| \mathbb{E}_{\mathcal{T}_k} \Big[ \frac{z_{\boldsymbol{\theta},l}^{\mathcal{T}_k}}{\left\| z_{\boldsymbol{\theta},l}^{\mathcal{T}_k} \right\|_2} \Big] - \mathbb{E}_{\mathcal{S}_k} \Big[ \frac{z_{\boldsymbol{\theta},l}^{\mathcal{S}_k}}{\left\| z_{\boldsymbol{\theta},l}^{\mathcal{S}_k} \right\|_2} \Big] \right\|^2 \bigg], (2)
$$

where,  $z_{\theta,l}^{\mathcal{T}_k} = vec(\mathbf{a}_{\theta,l}^{\mathcal{T}_k}) \in \mathbb{R}^{|B_k^{\mathcal{T}}| \times (W_l \times H_l)}$  and  $z_{\theta,l}^{\mathcal{S}_k} =$  $vec(\boldsymbol{a}_{\boldsymbol{\theta},l}^{S_k}) \in \mathbb{R}^{|B_k^S| \times (W_l \times H_l)}$  are the  $l^{\text{th}}$  pair of vectorized attention maps along the spatial dimension for the real and synthetic sets, respectively. The parameter  $K$  is the number of categories in a dataset, and  $P_{\theta}$  denotes the distribution of network parameters. It should be noted that normalization of the attention maps in the SAM module improves performance on the syntactic set (see supplementary materials).

Despite the ability of  $\mathcal{L}_{SAM}$  to approximate the real data distribution, a discrepancy still exists between the synthetic and real training sets. The features in the final layer of neural network models encapsulate the highest-level abstract information of the images in the form of an embedded representation, which has been shown to effectively capture

the semantic information of the input data [\[42,](#page-10-19) [63,](#page-11-3) [35,](#page-10-20) [21\]](#page-9-17). Therefore, we leverage a complementary loss as a regularizer to promote similarity in the mean vectors of the embeddings between the two datasets for each class. To that end, we employ the widely known Maximum Mean Discrepancy (MMD) loss,  $\mathcal{L}_{MMD}$ , which is calculated within a family of kernel mean embeddings in a Reproducing Kernel Hilbert Space (RKHS) [\[21\]](#page-9-17). The  $\mathcal{L}_{MMD}$  loss is formulated as

<span id="page-3-1"></span>
$$
\mathop{\mathbb{E}}\limits_{\boldsymbol{\theta}\sim P_{\boldsymbol{\theta}}} \left[ \sum_{k=1}^K \left\| \mathbb{E}_{\mathcal{T}_k} \left[ \tilde{\boldsymbol{f}}_{\boldsymbol{\theta},L}^{\mathcal{T}_k} \right] - \mathbb{E}_{\mathcal{S}_k} \left[ \tilde{\boldsymbol{f}}_{\boldsymbol{\theta},L}^{\mathcal{S}_k} \right] \right\|_{\mathcal{H}}^2 \right], \qquad (3)
$$

where  $H$  is a reproducing kernel Hilbert space. The  $\tilde{f}^{\mathcal{T}_k}_{\boldsymbol{\theta},L}$  =  $vec(f^{\mathcal{T}_k}_{\boldsymbol{\theta},L})$   $\in$   $\mathbb{R}^{|B^{\mathcal{T}}_k| \times (C_L \times W_L \times H_L)}$  and  $\tilde{f}^{\mathcal{S}_k}_{\boldsymbol{\theta},L}$  =  $vec(f_{\theta,L}^{\mathcal{S}_k}) \in \mathbb{R}^{|B_k^{\mathcal{S}}| \times (C_L \times W_L \times H_L)}$  are the final feature maps of the real and synthetic sets in vectorized form with both the channel and spatial dimensions included. We estimate the expectation terms in Equations [2](#page-3-0) and [3](#page-3-1) empirically if groundtruth data distributions are not available. Finally, we learn the synthetic dataset by solving the following optimization problem using SGD with momentum:

$$
S^* = \underset{S}{\arg\min} \ (\mathcal{L}_{\text{SAM}} + \lambda \mathcal{L}_{\text{MMD}}), \tag{4}
$$

where  $\lambda$  is the task balance parameter. Further information on the effect of  $\lambda$  is discussed in Section [6.2.2.](#page-13-0) Note that our approach assigns a fixed label to each synthetic sample and keeps it constant during training. A summary of the learning algorithm can be found in Algorithm [1.](#page-3-2)

<span id="page-3-2"></span>Algorithm 1 Dataset Distillation with Attention Matching

**Input:** Real training dataset  $\mathcal{T} = \{(\boldsymbol{x}_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  $i=1$ **Required:** Initialized synthetic samples for  $K$  classes, Deep neural network  $\phi_{\theta}$  with parameters  $\theta$ , Probability distribution over randomly initialized weights  $P_{\theta}$ , Learning rate  $\eta_{\mathcal{S}}$ , Task balance parameter  $\lambda$ , Number of training iterations I.

- <span id="page-3-0"></span>1: Initialize synthetic dataset  $S$
- 2: for  $i = 1, 2, \cdots, I$  do
- 3: Sample  $\theta$  from  $P_{\theta}$
- 4: Sample mini-batch pairs  $B_k^{\mathcal{T}}$  and  $B_k^{\mathcal{S}}$  from the real and synthetic sets for each class k
- 5: Compute  $\mathcal{L}_{SAM}$  and  $\mathcal{L}_{MMD}$  using Equations [2](#page-3-0) and [3](#page-3-1)
- 6: Calculate  $\mathcal{L} = \mathcal{L}_{\text{SAM}} + \lambda \mathcal{L}_{\text{MMD}}$
- 7: Update the synthetic dataset using  $S \leftarrow S \eta_S \nabla_S \mathcal{L}$ 8: end for

**Output:** Synthetic dataset  $S = \{ (s_i, y_i) \}_{i=1}^{|S|}$  $i=1$ 

## 4. Experiments

In this section, we demonstrate the effectiveness of DataDAM in improving the performance of dataset distillation. We introduce the datasets and implementation details

<span id="page-4-4"></span><span id="page-4-2"></span>

|                  |          |             | IPC Ratio% Resolution | Random | Herding                                                                                     | <b>Coreset Selection</b>                | K-Center Forgetting                                                                                                         | $DD^{\dagger}$ [53]                                       | $LD^{\dagger}$ [5] | <b>DC</b> [64] | DSA [62]                                                                                                                           | Training Set Synthesis<br>DM [63] | CAFE [52] KIP [38]                                                                                                                                                                                                                                                                                                                                                                                          |                                    | DataDAM                                           | <b>Whole Dataset</b> |
|------------------|----------|-------------|-----------------------|--------|---------------------------------------------------------------------------------------------|-----------------------------------------|-----------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|--------------------|----------------|------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------|---------------------------------------------------|----------------------|
| $CIFAR-10$       | 10<br>50 | 0.02<br>0.2 | 32<br>32<br>32        |        |                                                                                             |                                         | $14.4 \pm 2.0$ 21.5 $\pm$ 1.2 21.5 $\pm$ 1.3 13.5 $\pm$ 1.2<br>$143.4 \pm 1.0$ 40.4 $\pm$ 0.6 27.0 $\pm$ 1.4 23.3 $\pm$ 1.1 | ۰<br>$\sim$                                               |                    |                |                                                                                                                                    |                                   | $25.7 \pm 0.7$ $28.3 \pm 0.5$ $28.8 \pm 0.7$ $26.0 \pm 0.8$ $31.6 \pm 0.8$ $29.8 \pm 1.0$ $32.0 \pm 1.2$<br>$26.0 \pm 1.2$ 31.6 $\pm$ 0.7 14.7 $\pm$ 0.9 23.3 $\pm$ 1.0 36.8 $\pm$ 1.2 38.3 $\pm$ 0.4 44.9 $\pm$ 0.5 52.1 $\pm$ 0.5 48.9 $\pm$ 0.6 50.9 $\pm$ 0.5 46.1 $\pm$ 0.7 54.2 $\pm$ 0.8<br>$42.5 \pm 0.4$ 53.9 $\pm$ 0.5 60.6 $\pm$ 0.5 63.0 $\pm$ 0.4 62.3 $\pm$ 0.4 53.2 $\pm$ 0.7 67.0 $\pm$ 0.4 |                                    |                                                   | $84.8 + 0.1$         |
| $CIFAR-100$      | 10<br>50 | 0.2<br>10   | 32<br>32<br>32        |        | $130.0 \pm 0.4$ 33.7 $\pm$ 0.5 30.5 $\pm$ 0.3                                               |                                         | $4.2 \pm 0.3$ $8.3 \pm 0.3$ $8.4 \pm 0.3$ $4.5 \pm 0.2$<br>$14.6 \pm 0.5$ 17.3 $\pm$ 0.3 17.3 $\pm$ 0.3 15.1 $\pm$ 0.3      | ۰<br>$\sim$                                               | $\sim$<br>$\sim$   |                |                                                                                                                                    |                                   | $11.5 \pm 0.4$ $12.8 \pm 0.3$ $13.9 \pm 0.3$ $11.4 \pm 0.3$ $14.0 \pm 0.3$ $12.0 \pm 0.2$ $14.5 \pm 0.5$<br>$25.2 \pm 0.3$ 32.3 $\pm$ 0.3 29.7 $\pm$ 0.3 31.5 $\pm$ 0.2 29.0 $\pm$ 0.3 34.8 $\pm$ 0.5<br>$30.6 \pm 0.6$ 42.8 $\pm$ 0.4 43.6 $\pm$ 0.4 42.9 $\pm$ 0.2                                                                                                                                        | $\sim$                             | $49.4 \pm 0.3$                                    | $56.2 + 0.3$         |
| Tiny ImageNet 10 | 50       | 0.2<br>10   | 64<br>64<br>64        |        | $1.4 \pm 0.1$ $2.8 \pm 0.2$<br>$5.0 \pm 0.2$ 6.3 $\pm$ 0.2<br>$15.0 \pm 0.4$ 16.7 $\pm$ 0.3 | $\sim$<br>÷<br>$\overline{\phantom{a}}$ | $1.6 + 0.1$<br>$5.1 \pm 0.2$<br>$15.0 \pm 0.3$                                                                              | ٠<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{0}}$ |                    |                | $5.3 + 0.1$ $5.7 + 0.1$ $3.9 + 0.2$<br>$12.9 \pm 0.1$ $16.3 \pm 0.2$ $12.9 \pm 0.4$<br>$12.7 \pm 0.4$ 5.1 $\pm$ 0.2 25.3 $\pm$ 0.2 |                                   |                                                                                                                                                                                                                                                                                                                                                                                                             | $\sim$<br>$\overline{\phantom{a}}$ | $8.3 \pm 0.4$<br>$18.7 \pm 0.3$<br>$28.7 \pm 0.3$ | $37.6 \pm 0.4$       |

Table 1: The performance (testing accuracy %) comparison to state-of-the-art methods. We distill the given number of images per class using the training set, train a neural network on the synthetic set from scratch, and evaluate the network on the testing data. IPC: image(s) per class. Ratio (%): the ratio of distilled images to the whole training set. The works  $DD^{\dagger}$  and  $LD^{\dagger}$  use AlexNet [\[30\]](#page-9-18) for CIFAR-10 dataset. All other methods use a 128-width ConvNet for training and evaluation. Bold entries are the best results. Note: some entries are marked as absent due to scalability issues or unreported values. For more information, refer to the supplementary materials.

for reproducibility (Section [4.1\)](#page-4-0), compare our method with state-of-the-art benchmarks (Section [4.2\)](#page-4-1), conduct ablation studies to evaluate each component's efficacy and transferability across various architectures (Section [6.2.2\)](#page-13-0), and show some visualizations (Section [6.3\)](#page-17-0). Finally, we demonstrate the applicability of our method to the common tasks of continual learning and neural architecture search (Section [4.5\)](#page-7-0).

<span id="page-4-0"></span>

### 4.1. Experimental Setup

Datasets. Our method was evaluated on CIFAR10/100 datasets [\[29\]](#page-9-19), which have a resolution of  $32 \times 32$ , in line with state-of-the-art benchmarks. For medium-resolution data, we resized the Tiny ImageNet  $[31]$  and ImageNet-1K [\[14\]](#page-9-0) datasets to 64  $\times$  64. Previous work on dataset distillation [\[9\]](#page-9-12) introduced subsets of ImageNet-1K that focused on categories and aesthetics, including assorted objects, dog breeds, and birds. We utilized these subsets, namely ImageNette, ImageWoof, and ImageSquawk, which consist of 10 classes, as high-resolution (128  $\times$  128) datasets in our experimental studies. For more detailed information on the datasets, please refer to the supplementary materials.

Network Architectures. We use a ConvNet architecture [\[18\]](#page-9-20) for the distillation task, similar to prior research. The default ConvNet has three identical convolutional blocks and a linear classifier. Each block includes a 128-kernel  $3 \times 3$ convolutional layer, instance normalization, ReLU activation, and  $3 \times 3$  average pooling with a stride of 2. We adjust the network for medium- and high-resolution data by adding a fourth and fifth convolutional block to account for the higher resolutions, respectively. In all experiments, we initialize the network parameters using normal initialization [\[22\]](#page-9-16).

Evaluation. We evaluate the methods using standard measures from prior studies  $[63, 64, 52, 62]$  $[63, 64, 52, 62]$  $[63, 64, 52, 62]$  $[63, 64, 52, 62]$  $[63, 64, 52, 62]$  $[63, 64, 52, 62]$  $[63, 64, 52, 62]$ . We generate five sets of small synthetic images using 1, 10, and 50 images per class (IPC) from a real training dataset. Next, we train 20 neural network models on each synthetic set using an SGD optimizer with a learning rate of 0.01. We report the mean and standard deviation over 100 models for each experiment to assess the effectiveness of the performance of distilled datasets. Additionally, we evaluate computational costs using run-time expressed per step, averaged over 100 iterations, and peak GPU memory usage during 100 iterations of training. Finally, we visualize the unbiasedness of state-of-the-art methods using t-SNE visualization [\[51\]](#page-10-22).

<span id="page-4-3"></span>

|             | IPC | Ratio% | Resolution | Random         | DM [63]        | DataDAM        | Whole Dataset  |
|-------------|-----|--------|------------|----------------|----------------|----------------|----------------|
| ImageNet-1K | 1   | 0.078  | 64         | $0.5 \pm 0.1$  | $1.3 \pm 0.1$  | $2.0 \pm 0.1$  | $33.8 \pm 0.3$ |
|             | 2   | 0.156  | 64         | $0.9 \pm 0.1$  | $1.6 \pm 0.1$  | $2.2 \pm 0.1$  |                |
|             | 10  | 0.780  | 64         | $3.1 \pm 0.2$  | $5.7 \pm 0.1$  | $6.3 \pm 0.0$  |                |
|             | 50  | 3.902  | 64         | $7.6 \pm 1.2$  | $11.4 \pm 0.9$ | $15.5 \pm 0.2$ |                |
| ImageNette  | 1   | 0.105  | 128        | $23.5 \pm 4.8$ | $32.8 \pm 0.5$ | $34.7 \pm 0.9$ | $87.4 \pm 1.0$ |
|             | 10  | 1.050  | 128        | $47.7 \pm 2.4$ | $58.1 \pm 0.3$ | $59.4 \pm 0.4$ |                |
| ImageWoof   | 1   | 0.110  | 128        | $14.2 \pm 0.9$ | $21.1 \pm 1.2$ | $24.2 \pm 0.5$ | $67.0 \pm 1.3$ |
|             | 10  | 1.100  | 128        | $27.0 \pm 1.9$ | $31.4 \pm 0.5$ | $34.4 \pm 0.4$ |                |
| ImageSquawk | 1   | 0.077  | 128        | $21.8 \pm 0.5$ | $31.2 \pm 0.7$ | $36.4 \pm 0.8$ | $87.5 \pm 0.3$ |
|             | 10  | 0.770  | 128        | $40.2 \pm 0.4$ | $50.4 \pm 1.2$ | $55.4 \pm 0.9$ |                |

| Table 2: The performance (testing accuracy % ) comparison to state- |
|---------------------------------------------------------------------|
| of-the-art methods on ImageNet-1K [14] and ImageNet subsets [9].    |

Implementation Details. We employ the SGD optimizer with a fixed learning rate of 1 to learn synthetic datasets with 1, 10, and 50 IPCs. We learn low- and medium/highresolution synthetic images in 8000 iterations with a task balance  $(\lambda)$  of 0.01 and 0.02, respectively. Following from [\[62\]](#page-11-2), we apply the differentiable augmentation strategy for learning and evaluating the synthetic set. For dataset reprocessing, we utilized the Kornia implementation of Zero Component Analysis (ZCA) with default parameters, following previous works [\[38,](#page-10-11) [9\]](#page-9-12). All experiments are conducted on two Nvidia A100 GPUs. Further details on hyperparameters are available in the supplementary materials.

<span id="page-4-1"></span>

### 4.2. Comparison to State-of-the-art Methods

Competitive Methods. We evaluate DataDAM against four corset selection approaches and eight advanced methods for training set synthesis. The corset selection methods include Random selection [\[41\]](#page-10-5), Herding [\[8,](#page-9-7) [4\]](#page-9-8), K-Center [\[46\]](#page-10-6), and Forgetting [\[49\]](#page-10-7). We also compare our approach with state-of-the-art distillation methods, including Dataset Distillation [\[53\]](#page-10-8) (DD), Flexible Dataset Distillation [\[5\]](#page-9-11) (LD),

<span id="page-5-2"></span>Dataset Condensation [\[64\]](#page-11-1) (DC), Dataset Condensation with Differentiable Siamese Augmentation [\[62\]](#page-11-2) (DSA), Distribution Matching [\[63\]](#page-11-3) (DM), Aligning Features [\[52\]](#page-10-1) (CAFE), Kernel Inducing Points [\[38,](#page-10-11) [37\]](#page-10-12) (KIP). As a distributionmatching centered work, we don't directly compare with Matching Training Trajectories [\[9\]](#page-9-12) (MTT) as they incur the additional cost of training expert models and significant overhead in the distillation process (see Table [4\)](#page-5-0), hence not an equitable comparison with our strategy. To ensure reproducibility, we downloaded publicly available distilled data for each baseline method and trained models using our experimental setup. We make minor adjustments to some methods to ensure a fair comparison, and for those that did not conduct experiments on certain data, we implemented them using the released author codes. For details on the implementation of baselines and comparisons to other methods such as generative models [\[39,](#page-10-23) [6,](#page-9-21) [34\]](#page-10-24), please refer to the supplementary materials.

Performance Comparison. We compare our method with selection- and synthesis-based approaches in Tables [1](#page-4-2) and [2.](#page-4-3) The results demonstrate that training set synthesis methods outperform coreset methods, especially when the number of images per class is limited to 1 or 10. This is due to the fact that synthetic training data is not limited to a specific set of real images. Moreover, our method consistently outperforms all baselines in most settings for low-resolution datasets, with improvements on the top competitor, DM, of 4.0% and 5.8% for the CIFAR10/100 datasets when using IPC50. This indicates that our DataDAM can achieve up to 88% of the upper-bound performance with just 10% of the training dataset on CIFAR100 and up to 79% of the performance with only 1% of the training dataset on CIFAR10. For medium- and high-resolution datasets, including Tiny ImageNet, ImageNet-1K, and ImageNet subsets, DataDAM also surpasses all baseline models across all settings. While existing methods fail to scale up to the ImageNet-1K due to memory or time constraints, DataDAM achieved accuracies of 2.0%, 2.2%, 6.3%, and 15.5% for 1, 2, 10, and 50 IPC, respectively, surpassing DM and Random by a significant margin. This improvement can be attributed to our methodology, which captures essential layer-wise information through spatial attention maps and the feature map of the last layer. Our ablation studies provide further evidence that the performance gain is directly related to the discriminative ability of the method in the synthetic image learning scheme.

Cross-architecture Generalization. In this section, we test our learned synthetic data across different unseen neural architectures, consistent with state-of-the-art benchmarks [\[64,](#page-11-1) [63\]](#page-11-3). To that end, synthetic data was generated from CIFAR10 using one architecture (T) with IPC50 and then transferred to a new architecture (E), where it was trained from scratch and tested on real-world data. Popular CNN architectures like ConvNet [\[18\]](#page-9-20), AlexNet [\[30\]](#page-9-18), VGG-11 [\[47\]](#page-10-25),

<span id="page-5-1"></span>

|                  | $T \setminus E$ | ConvNet        | AlexNet        | VGG-11         | ResNet-18      |
|------------------|-----------------|----------------|----------------|----------------|----------------|
| DC [64]          | ConvNet         | $53.9 \pm 0.5$ | $28.8 \pm 0.7$ | $38.8 \pm 1.1$ | $20.9 \pm 1.0$ |
| <b>CAFE</b> [52] | ConvNet         | $62.3 \pm 0.4$ | $43.2 \pm 0.4$ | $48.8 \pm 0.5$ | $43.3 \pm 0.7$ |
| <b>DSA</b> [62]  | ConvNet         | $60.6 \pm 0.5$ | $53.7 \pm 0.6$ | $51.4 \pm 1.0$ | $47.8 \pm 0.9$ |
| DM [63]          | ConvNet         | $63.0 \pm 0.4$ | $60.1 \pm 0.5$ | $57.4 \pm 0.8$ | $52.9 \pm 0.4$ |
| KIP [38]         | ConvNet         | $56.9 \pm 0.4$ | $53.2 \pm 1.6$ | $53.2 \pm 0.5$ | $47.6 \pm 0.8$ |
| <b>DataDAM</b>   | ConvNet         | $67.0 \pm 0.4$ | $63.9 \pm 0.9$ | $64.8 \pm 0.5$ | $60.2 \pm 0.7$ |
|                  | AlexNet         | $61.8 \pm 0.6$ | $60.6 \pm 0.9$ | $61.8 \pm 0.6$ | $56.4 \pm 0.7$ |
|                  | VGG-11          | $56.5 \pm 0.4$ | $53.7 \pm 1.5$ | $56.2 \pm 0.6$ | $52.0 \pm 0.7$ |

Table 3: Cross-architecture testing performance (%) on CIFAR10 with 50 images per class. The synthetic set is trained on one architecture (T) and then evaluated on another architecture (E).

and ResNet-18 [\[23\]](#page-9-1) are used to examine the generalization performance.

Table [3](#page-5-1) shows that DataDAM outperforms state-of-theart across unseen architectures when the synthetic data is learned with ConvNet. We achieve a margin of 3.8% and 7.4% when transferring to AlexNet and VGG-11, respectively, surpassing the best method, DM. Additionally, the remaining architectures demonstrate improvement due to the robustness of our synthetic images and their reduced architectural bias, as seen in the natural appearance of the distilled images (Figure [17\)](#page-19-0).

Training Cost Analysis. In dataset distillation, it is crucial to consider the resource-time costs of various methods, particularly in terms of scalability. This study compares our method to state-of-the-art benchmarks presented in Table [4.](#page-5-0) We demonstrate a significantly lower run-time by almost 2 orders of magnitude compared to most state-of-the-art results. Our method, like DM, has an advantage over methods such as DC, DSA, and MTT that require costly inner-loop bi-level optimization. It should be noted that DataDAM can leverage information from randomly initialized neural networks without training and consistently achieve superior performance.

<span id="page-5-0"></span>

| Method         | run time(sec)      |                    |                     | GPU memory(MB) |       |       |
|----------------|--------------------|--------------------|---------------------|----------------|-------|-------|
|                | IPC1               | IPC10              | IPC50               | IPC1           | IPC10 | IPC50 |
| DC[64]         | $0.16 	extpm 0.01$ | $3.31 	extpm 0.02$ | $15.74 	extpm 0.10$ | 3515           | 3621  | 4527  |
| DSA[62]        | $0.22 	extpm 0.02$ | $4.47 	extpm 0.12$ | $20.13 	extpm 0.58$ | 3513           | 3639  | 4539  |
| <b>DM</b> [63] | $0.08 	extpm 0.02$ | $0.08 	extpm 0.02$ | $0.08 	extpm 0.02$  | 3323           | 3455  | 3605  |
| MTT[9]         | $0.36 	extpm 0.23$ | $0.40 	extpm 0.20$ | OOM                 | 2711           | 8049  | OOM   |
| DataDAM        | $0.09 	extpm 0.01$ | $0.08 	extpm 0.01$ | $0.16 	extpm 0.04$  | 3452           | 3561  | 3724  |

Table 4: Training time and GPU memory comparisons for state-ofthe-art synthesis methods. Run time is expressed per step, averaged over 100 iterations. GPU memory is expressed as the peak memory usage during 100 iterations of training. All methods were run on an A100 GPU for CIFAR-10. OOM (out-of-memory) is reported for methods that are unable to run within the GPU memory limit.

### 4.3. Ablation Studies

In this section, we evaluate the robustness of our method under different experimental configurations. All experiments averaged performance over 100 randomly initialized ConvNets across five synthetic sets. The CIFAR10 dataset is

<span id="page-6-3"></span><span id="page-6-0"></span>Image /page/6/Figure/0 description: The image is a line graph showing the testing accuracy (%) on the y-axis against the training epoch on the x-axis. There are three lines representing different methods: Random (blue), K-Center (orange), and Gaussian Noise (green). The Random and K-Center lines show similar trends, starting around 50% accuracy at epoch 0 and increasing to around 65% by epoch 1000. Both lines fluctuate slightly but generally stay above 60% accuracy for the rest of the training epochs shown. The Gaussian Noise line starts at approximately 42% accuracy at epoch 0 and increases to around 54% by epoch 1000. This line fluctuates between 50% and 58% accuracy for the remainder of the training epochs. The K-Center line consistently shows the highest accuracy, followed by the Random line, and then the Gaussian Noise line.

Figure 3: Test accuracy evolution of synthetic image learning on CIFAR10 with IPC50 under three different initializations: Random, K-Center, and Gaussian noise.

used for all studies. The most relevant ablation studies to our method are included here; further ablative experiments are included in the supplementary materials.

Exploring the importance of different initialization methods for synthetic images. In dataset distillation, synthetic images are usually initialized through Gaussian noise or sampled from the real data; however, the choice of initialization method has proved to be crucial to the overall performance [\[13\]](#page-9-9). To assess the robustness of DataDAM, we conducted an empirical evaluation with an IPC50 under three initialization conditions: Random selection, K-Center [\[13,](#page-9-9) [46\]](#page-10-6), and Gaussian noise (Figure [3\)](#page-6-0). As reported in [\[13\]](#page-9-9), other works including [\[63,](#page-11-3) [62,](#page-11-2) [64\]](#page-11-1) have seen benefits to testing performance and convergence speed by leveraging K-Center as a smart selection. Empirically, we show that our method is robust across both random and K-Center with only a minute performance gap, and thus the initialization of synthetic data is not as crucial to our final performance. Finally, when comparing with noise, we notice a performance reduction; however, based on the progression over the training epochs, it appears our method is successful in transferring the information from the real data onto the synthetic images. For further detailed experimental results, please refer to the supplementary materials.

Evaluation of task balance  $\lambda$  in DataDAM. It is common in machine learning to use regularization to prevent overfitting and improve generalization. In the case of DataDAM, the regularizing coefficient  $\lambda$  controls the tradeoff between the attention matching loss  $\mathcal{L}_{SAM}$  and the maximum mean discrepancy loss  $\mathcal{L}_{\text{MMD}}$ , which aims to reduce the discrepancy between the synthetic and real training distributions. The experiments conducted on the CIFAR10 dataset with IPC 10 showed that increasing the value of  $\lambda$  improved the performance of DataDAM up to a certain point (Figure [4\)](#page-6-1). This is because, at lower values of  $\lambda$ , the attention matching loss dominates the training process, while at higher values of  $\lambda$ , the regularizer contributes more effectively to the overall performance. The results in Figure [4](#page-6-1) also indicate that the method is robust to larger regularization terms, as shown by

<span id="page-6-1"></span>Image /page/6/Figure/5 description: The image is a line graph showing the relationship between task balance lambda and testing accuracy. The x-axis is labeled "Task balance \lambda" and ranges from 0.0 to 100.0 on a logarithmic scale. The y-axis is labeled "Testing Accuracy (%)" and ranges from 49 to 55. The line graph shows an increasing trend in testing accuracy as task balance lambda increases from 0.0 to 0.005, reaching a peak of approximately 54.5% at lambda = 0.005. After this peak, the accuracy slightly decreases and then plateaus around 53.5% for higher values of lambda. The shaded area around the line represents a confidence interval.

Figure 4: The effect of task balance  $\lambda$  on the testing accuracy (%) for CIFAR10 dataset with IPC10 configuration.

the plateau to the right of 0.01. Therefore, a task balance of 0.01 is chosen for all experiments on low-resolution data and 0.02 on medium- and high-resolution data.

Evaluation of loss components in DataDAM. We conducted an ablation study to evaluate the contribution of each loss component, namely spatial attention matching loss  $(\mathcal{L}_{SAM})$  and the complementary loss  $(\mathcal{L}_{MMD})$ , to the final performance of DataDAM. As seen in table [5,](#page-6-2) the joint use of  $\mathcal{L}_{\text{MMD}}$  and  $\mathcal{L}_{\text{SAM}}$  led to state-of-the-art results, while using  $\mathcal{L}_{\text{MMD}}$  alone resulted in significant underperformance, as it emphasizes the extraction of high-level abstract data but fails to capture different level representations of the real training distribution. On the other hand,  $\mathcal{L}_{SAM}$  alone outperformed the base complementary loss, indicating the extracted discriminative features contain significant information about the training but still have room for improvement. To highlight the importance of intermediate representations, we compared our attention-based transfer approach with the transfer of layer-wise feature maps, similar to CAFE [\[52\]](#page-10-1), and demonstrated a significant performance gap (see "Feature Map Transfer" in Table [5\)](#page-6-2). Overall, our findings support the use of attention to match layer-wise representations and a complementary loss to regulate the process.

<span id="page-6-2"></span>

| $\mathcal{L}_{{\text{MMD}}}$ | $\mathcal{L}_{{\text{SAM}}}$ | Feature Map Transfer | Testing Performance (%) |
|------------------------------|------------------------------|----------------------|-------------------------|
| $\checkmark$                 | -                            | -                    | $48.9 \pm 0.6$          |
| -                            | $\checkmark$                 | -                    | $49.8 \pm 0.7$          |
| -                            | -                            | $\checkmark$         | $47.2 \pm 0.3$          |
| $\checkmark$                 | $\checkmark$                 | -                    | $54.2 \pm 0.8$          |

Table 5: Evaluation of loss components in DataDAM.

Exploring the effect of each layer in DataDAM. Following the previous ablation, it is equally important to examine how each layer affects the final performance. As shown in Table [6,](#page-7-1) different layers perform differently since each provides different levels of information about the data distributions. This finding supports the claim that matching spatial atten-

<span id="page-7-4"></span>tion maps in individual layers alone cannot obtain promising results. As a result, to improve the overall performance of the synthetic data learning process, it is crucial to transfer different levels of information about the real data distribution using the SAM module across all intermediate layers.

<span id="page-7-1"></span>

| Layer 1 | Layer 2 | Last Layer | Testing Performance (%)                                                                |
|---------|---------|------------|----------------------------------------------------------------------------------------|
| ۰<br>۰  | -<br>-  |            | $48.9 \pm 0.6$<br>$50.2 \pm 0.4$<br>$51.5 \pm 1.0$<br>$49.8 \pm 0.7$<br>$54.2 \pm 0.8$ |

Table 6: Evaluation of each layer's impact in ConvNet (3-layer). The output is transferred under  $\mathcal{L}_{\text{MMD}}$  while the effects of the specified layers are measured through  $\mathcal{L}_{\text{SAM}}$ . We evaluate the performance of the CIFAR10 dataset with IPC10.

Network Distributions. We investigate the impact of network initialization on DataDAM's performance by training 1000 ConvNet architectures with random initializations on the original training data and categorizing their learned states into five buckets based on testing performance. We sampled networks from each bucket and trained our synthetic data using IPCs 1, 10, and 50. As illustrated in Table [7,](#page-7-2) our findings indicate that DataDAM is robust across various network initializations. This is attributed to the transfer of attention maps that contain relevant and discriminative information rather than the entire feature map statistics, as shown in [\[52\]](#page-10-1). These results reinforce the idea that achieving state-of-the-art performance does not require inner-loop model training.

<span id="page-7-2"></span>

| IPC | Random            | 0-20       | 20-40      | 40-60             | 60-80      | ≥ 80       |
|-----|-------------------|------------|------------|-------------------|------------|------------|
| 1   | <b>32.0</b> ± 2.0 | 30.8 ± 1.1 | 30.7 ± 1.7 | 31.5 ± 1.9        | 26.2 ± 1.8 | 26.9 ± 1.3 |
| 10  | <b>54.2</b> ± 0.8 | 54.0 ± 0.7 | 53.1 ± 0.5 | 52.1 ± 0.8        | 51.2 ± 0.7 | 51.7 ± 0.7 |
| 50  | <b>67.0</b> ± 0.4 | 66.2 ± 0.4 | 66.4 ± 0.4 | <b>67.0</b> ± 0.5 | 65.8 ± 0.5 | 65.3 ± 0.6 |

Table 7: Performance of synthetic data learned with IPCs 1, 10, and 50 for different network initialization. Models are trained on the training set and grouped by their respective accuracy levels.

### 4.4. Visualization

Data Distribution. To evaluate whether our method can capture a more accurate distribution from the original dataset, we use t-SNE [\[51\]](#page-10-22) to visualize the features of real and synthetic sets generated by DM, DSA, CAFE, and DataDAM in the embedding space of the ResNet-18 architecture. Figure [5](#page-7-3) shows that methods such as DSA and CAFE are biased towards the edges of their clusters and not representative of the training data. Much like DM, our results indicate a more equalized distribution, allowing us to better capture the data distribution. Preserving dataset distributions is of utmost importance in fields like ethical machine learning since methods that cannot be impartial in capturing data distribution can lead to bias and discrimination. Our method's capacity to capture the distribution of data makes it more appropriate

than other approaches in these conditions, particularly in fields such as facial detection for privacy  $[10]$ .

<span id="page-7-3"></span>Image /page/7/Figure/9 description: The image displays four pairs of scatter plots, each labeled with a method name: DM, DSA, CAFE, and DataDAM. Each pair consists of two clusters of points, one colored red and the other blue. The points are represented by small dots, with some larger star-shaped markers overlaid on them. In the DM and DSA plots, the red and blue clusters are relatively well-separated. The CAFE plots show a greater degree of overlap between the red and blue clusters, with several blue stars appearing within the red cluster and vice versa. The DataDAM plots also exhibit some overlap, but the separation is generally better than in the CAFE plots. The stars appear to highlight specific points within the clusters.

Figure 5: Distributions of synthetic images learned by four methods on CIFAR10 with IPC50. The stars represent the synthetic data dispersed amongst the original training dataset.

Synthetic Images. We have included samples from our learned synthetic images for different resolutions in Figure [17.](#page-19-0) In low-resolution images, the objects are easily distinguishable, and their class labels can be recognized intuitively. As we move to higher-resolution images, the objects become more outlined and distinct from their backgrounds. These synthetic images have a natural look and can be transferred well to different architectures. Moreover, the high-resolution images accurately represent the relevant colors of the objects and provide more meaningful data for downstream tasks. For more visualizations, refer to the supplementary materials.

<span id="page-7-0"></span>

### 4.5. Applications

We assess the effectiveness of DataDAM's performance through the use of two prevalent applications involving dataset distillation algorithms: *continual learning* and *neural architecture search*.

Continual Learning. Continual learning trains a model incrementally with new task labels to prevent catastrophic forgetting [\[41\]](#page-10-5). One approach is to maintain a replay buffer that stores balanced training examples in memory and train the model exclusively on the latest memory, starting from scratch [\[41,](#page-10-5) [3,](#page-9-23) [40\]](#page-10-26). Efficient storage of exemplars is crucial for optimal continual learning performance, and condensed data can play a significant role. We use the class-incremental setting from [\[63\]](#page-11-3) with an augmented buffer size of 20 IPC to conduct class-incremental learning on the CIFAR100 dataset. We compare our proposed memory construction approach with random [\[40\]](#page-10-26), herding  $[8, 4, 41]$  $[8, 4, 41]$  $[8, 4, 41]$  $[8, 4, 41]$  $[8, 4, 41]$ , DSA  $[62]$ , and DM  $[63]$ methods at 5 and 10 learning steps. In each step, including the initial one, we added 400 and 200 distilled images to the replay buffer, respectively, following the class split of [\[63\]](#page-11-3). The test accuracy is the performance metric, and default data preprocessing and ConvNet are used for each approach.

Figure [7](#page-8-0) shows that our memory construction approach consistently outperforms others in both settings. Specifically, DataDAM achieves final test accuracies of 39.7% and 39.7% in 5-step and 10-step learning, respectively, outperforming DM (34.4% and 34.7%), DSA (31.7% and 30.3%), herding

<span id="page-8-2"></span>Image /page/8/Picture/0 description: This image displays four grids of images, each representing a different dataset: (a) CIFAR10, (b) CIFAR100, (c) Tiny ImageNet, and (d) ImageNet-1K. Each grid contains multiple smaller images, with labels below each individual image indicating the object or concept it represents. The images themselves appear to be generated or synthesized, with varying degrees of clarity and color saturation. The overall presentation is a comparative visualization of image generation or representation across different scales and complexities of datasets.

Figure 6: Example distilled images from 32x32 CIFAR10/100 (IPC10), 64x64 Tiny ImageNet (IPC1), and 64x64 ImageNet-1K (IPC1).

(28.1% and 27.4%), and random (24.8% and 24.8%). Notably, the final performance of DataDAM, DM, and random selection methods remains unchanged upon increasing the number of learning steps, as these methods independently learn the synthetic datasets for each class. Our findings reveal that DataDAM provides more informative training to the models than other baselines, resulting in more effective prevention of memory loss associated with past tasks.

<span id="page-8-0"></span>Image /page/8/Figure/3 description: The image contains two line graphs side-by-side, both plotting testing accuracy (%) against the number of classes. The left graph ranges from 20 to 100 classes on the x-axis and 20% to 80% accuracy on the y-axis. The right graph ranges from 10 to 100 classes on the x-axis and 20% to 80% accuracy on the y-axis. Both graphs display five lines representing different methods: Random (blue), Herding (yellow), DSA (green), DM (red), and DataDAM (purple). Each line shows a general downward trend in testing accuracy as the number of classes increases. The DataDAM line consistently shows the highest accuracy across both graphs, while the Random line shows the lowest. Shaded regions around each line indicate variability or confidence intervals.

Figure 7: (Left): Showcases 5-step and (Right): Showcases 10-step continual learning with tolerance region.

Neural Architecture Search. Our synthetic sets can be used as a proxy set to accelerate model evaluation in Neural Architecture Search (NAS). Following [\[64\]](#page-11-1), we establish a 720 ConvNet search space on CIFAR10 with a grid varying in network depth, width, activation, normalization, and pooling layers. We compared our method with Random, DSA, CAFE, early stopping, and DM. Each architecture was trained on the proxy set (synthetic 50 IPC) for 200 epochs and the whole dataset for 100 epochs to establish a baseline performance metric. Early stopping still uses the entire dataset, but we limit the iterations to those of the proxy set, as in  $[63]$ . For each method, we rank all the architectures based on the validation performance and report the testing accuracy of the best-selected model when trained on the whole dataset in Table [8.](#page-8-1) DataDAM achieved the best accuracy among the competitors, with an accuracy of 89.0%, which is very similar to the original training data at 89.2%, indicating the potential of our proxy set to accurately represent the training data. Furthermore, we calculated Spearman's correlation over the entire search space to evaluate the robustness of our learned data in architecture searching. The correlation is calculated between the testing performances of each method when trained on the proxy versus the original training data. Our method achieves the highest correlation (0.72), indicating that it generates a suitable proxy set that is generalizable across the entire search space and encodes the most important and relevant information from the training data into a condensed form. For more experimentation with NAS, refer to the supplementary materials.

<span id="page-8-1"></span>

|                 | Random     | DSA        | DM         | CAFE       | Ours        | Early-stopping | Whole Dataset  |
|-----------------|------------|------------|------------|------------|-------------|----------------|----------------|
| Performance (%) | 88.9       | 87.2       | 87.2       | 83.6       | <b>89.0</b> | 88.9           | 89.2           |
| Correlation     | 0.70       | 0.66       | 0.71       | 0.59       | <b>0.72</b> | 0.69           | 1.00           |
| Time cost (min) | 206.4      | 206.4      | 206.6      | 206.4      | 206.4       | 206.2          | 5168.9         |
| Storage (imgs)  | <b>500</b> | <b>500</b> | <b>500</b> | <b>500</b> | <b>500</b>  | $5 	imes 10^4$ | $5 	imes 10^4$ |

| Table 8: Neural architecture search on CIFAR10. |
|-------------------------------------------------|
|-------------------------------------------------|

## 5. Conclusion and Limitations

Our proposed method, Dataset Distillation with Attention Matching (DataDAM), efficiently captures real datasets' most informative and discriminative information. It consists of two modules, spatial attention matching (SAM) and lastlayer feature alignment, that match attention maps and embedded representations generated by different layers in randomly initialized neural networks, respectively. We conduct extensive experiments on datasets with different resolutions to show that DataDAM could lower CNN training costs while maintaining superior generalization performance. We also offer two applications that take advantage of our distilled set: continual learning and neural architecture search. In the future, we plan to apply DataDAM to more fine-grained datasets and explore the analytical concepts behind them.

Limitations. DataDAM exhibits robust generalization across various CNN architectures, but it is limited to convolutional networks due to its formulation. For example, it, along with other data distillation algorithms, faces challenges in achieving successful cross-architecture generalization on ViT (Vision Transformer) models. Additionally, all data distillation methods, including DataDAM, need to be re-optimized when the distillation ratio changes, which can limit efficiency in some applications.

## References

- <span id="page-9-5"></span>[1] Hossam Amer, Ahmed H Salamah, Ahmad Sajedi, and Enhui Yang. High performance convolution using sparsity and patterns for inference in deep convolutional neural networks. *arXiv preprint arXiv:2104.08314*, 2021. [1](#page-0-1)
- <span id="page-9-15"></span>[2] Dzmitry Bahdanau, Kyung Hyun Cho, and Yoshua Bengio. Neural machine translation by jointly learning to align and translate. In *3rd International Conference on Learning Representations*, 2015. [3](#page-2-1)
- <span id="page-9-23"></span>[3] Jihwan Bang, Heesu Kim, YoungJoon Yoo, Jung-Woo Ha, and Jonghyun Choi. Rainbow memory: Continual learning with a memory of diverse samples. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 8218–8227, 2021. [8](#page-7-4)
- <span id="page-9-8"></span>[4] Eden Belouadah and Adrian Popescu. Scail: Classifier weights scaling for class incremental learning. In *Proceedings of the IEEE/CVF winter conference on applications of computer vision*, pages 1266–1275, 2020. [1,](#page-0-1) [2,](#page-1-0) [5,](#page-4-4) [8](#page-7-4)
- <span id="page-9-11"></span>[5] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [2,](#page-1-0) [5](#page-4-4)
- <span id="page-9-21"></span>[6] Andrew Brock, Jeff Donahue, and Karen Simonyan. Large scale gan training for high fidelity natural image synthesis. In *International Conference on Learning Representations*, 2019. [6,](#page-5-2) [13](#page-12-0)
- <span id="page-9-13"></span>[7] Weipeng Cao, Xizhao Wang, Zhong Ming, and Jinzhu Gao. A review on neural networks with random weights. *Neurocomputing*, 275:278–287, 2018. [2](#page-1-0)
- <span id="page-9-7"></span>[8] Francisco M Castro, Manuel J Marín-Jiménez, Nicolás Guil, Cordelia Schmid, and Karteek Alahari. End-to-end incremental learning. In *Proceedings of the European conference on computer vision (ECCV)*, pages 233–248, 2018. [1,](#page-0-1) [2,](#page-1-0) [5,](#page-4-4) [8](#page-7-4)
- <span id="page-9-12"></span>[9] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [2,](#page-1-0) [5,](#page-4-4) [6,](#page-5-2) [12,](#page-11-8) [13](#page-12-0)
- <span id="page-9-22"></span>[10] Umur A Ciftci, Gokturk Yuksek, and Ilke Demir. My face my choice: Privacy enhancing deepfakes for social media anonymization. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 1369– 1379, 2023. [8](#page-7-4)
- <span id="page-9-27"></span>[11] Ekin D Cubuk, Barret Zoph, Dandelion Mane, Vijay Vasudevan, and Quoc V Le. Autoaugment: Learning augmentation policies from data. *arXiv preprint arXiv:1805.09501*, 2018. [14](#page-13-1)
- <span id="page-9-28"></span>[12] Ekin D Cubuk, Barret Zoph, Jonathon Shlens, and Quoc V Le. Randaugment: Practical automated data augmentation with a reduced search space. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition workshops*, pages 702–703, 2020. [14](#page-13-1)
- <span id="page-9-9"></span>[13] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dcbench: Dataset condensation benchmark. In *Thirty-sixth Conference on Neural Information Processing Systems Datasets and Benchmarks Track*. [2,](#page-1-0) [3,](#page-2-1) [7,](#page-6-3) [14,](#page-13-1) [16,](#page-15-0) [18,](#page-17-1) [19](#page-18-0)
- <span id="page-9-0"></span>[14] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database.

In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [1,](#page-0-1) [2,](#page-1-0) [5,](#page-4-4) [12](#page-11-8)

- <span id="page-9-10"></span>[15] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, pages 5378–5396. PMLR, 2022. [2](#page-1-0)
- <span id="page-9-29"></span>[16] Xuanyi Dong and Yi Yang. Nas-bench-201: Extending the scope of reproducible neural architecture search. In *International Conference on Learning Representations*, 2020. [16](#page-15-0)
- <span id="page-9-2"></span>[17] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. In *International Conference on Learning Representations*, 2021. [1](#page-0-1)
- <span id="page-9-20"></span>[18] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4367–4375, 2018. [5,](#page-4-4) [6,](#page-5-2) [13,](#page-12-0) [18](#page-17-1)
- <span id="page-9-14"></span>[19] Raja Giryes, Guillermo Sapiro, and Alex M Bronstein. Deep neural networks with random gaussian weights: A universal classification strategy? *IEEE Transactions on Signal Processing*, 64(13):3444–3457, 2016. [2](#page-1-0)
- <span id="page-9-26"></span>[20] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial networks. *Communications of the ACM*, 63(11):139–144, 2020. [13](#page-12-0)
- <span id="page-9-17"></span>[21] Arthur Gretton, Karsten M Borgwardt, Malte J Rasch, Bernhard Schölkopf, and Alexander Smola. A kernel two-sample test. *The Journal of Machine Learning Research*, 13(1):723– 773, 2012. [4,](#page-3-3) [14](#page-13-1)
- <span id="page-9-16"></span>[22] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Delving deep into rectifiers: Surpassing human-level performance on imagenet classification. In *ICCV*, 2015. [3,](#page-2-1) [5](#page-4-4)
- <span id="page-9-1"></span>[23] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [1,](#page-0-1) [6,](#page-5-2) [18](#page-17-1)
- <span id="page-9-4"></span>[24] Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2015. [1](#page-0-1)
- <span id="page-9-24"></span>[25] J Howard. Imagenette: A smaller subset of 10 easily classified classes from imagenet, and a little more french, 2019. [12,](#page-11-8) [13](#page-12-0)
- <span id="page-9-3"></span>[26] Jacob Devlin Ming-Wei Chang Kenton and Lee Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding. In *Proceedings of NAACL-HLT*, pages 4171–4186, 2019. [1](#page-0-1)
- <span id="page-9-6"></span>[27] Samir Khaki and Weihan Luo. Cfdp: Common frequency domain pruning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR) Workshops*, pages 4714–4723, June 2023. [1](#page-0-1)
- <span id="page-9-25"></span>[28] Diederik P Kingma and Max Welling. Auto-encoding variational bayes. *arXiv preprint arXiv:1312.6114*, 2013. [13](#page-12-0)
- <span id="page-9-19"></span>[29] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [5,](#page-4-4) [12](#page-11-8)
- <span id="page-9-18"></span>[30] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Communications of the ACM*, 60(6):84–90, 2017. [5,](#page-4-4) [6,](#page-5-2) [18](#page-17-1)

- <span id="page-10-21"></span>[31] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [5,](#page-4-4) [12](#page-11-8)
- <span id="page-10-16"></span>[32] Jaehoon Lee, Lechao Xiao, Samuel Schoenholz, Yasaman Bahri, Roman Novak, Jascha Sohl-Dickstein, and Jeffrey Pennington. Wide neural networks of any depth evolve as linear models under gradient descent. *Advances in neural information processing systems*, 32, 2019. [2](#page-1-0)
- <span id="page-10-13"></span>[33] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022. [2](#page-1-0)
- <span id="page-10-24"></span>[34] Yujia Li, Kevin Swersky, and Rich Zemel. Generative moment matching networks. In *International conference on machine learning*, pages 1718–1727. PMLR, 2015. [6,](#page-5-2) [13](#page-12-0)
- <span id="page-10-20"></span>[35] Chao Ma, Jia-Bin Huang, Xiaokang Yang, and Ming-Hsuan Yang. Hierarchical convolutional features for visual tracking. In *Proceedings of the IEEE international conference on computer vision*, pages 3074–3082, 2015. [4](#page-3-3)
- <span id="page-10-27"></span>[36] Mehdi Mirza and Simon Osindero. Conditional generative adversarial nets. *arXiv preprint arXiv:1411.1784*, 2014. [13](#page-12-0)
- <span id="page-10-12"></span>[37] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel-ridge regression. In *International Conference on Learning Representations*, 2021. [2,](#page-1-0) [6,](#page-5-2) [12,](#page-11-8) [14](#page-13-1)
- <span id="page-10-11"></span>[38] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [2,](#page-1-0) [5,](#page-4-4) [6,](#page-5-2) [12](#page-11-8)
- <span id="page-10-23"></span>[39] Gaurav Parmar, Dacheng Li, Kwonjoon Lee, and Zhuowen Tu. Dual contradistinctive generative autoencoder. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 823–832, 2021. [6,](#page-5-2) [13](#page-12-0)
- <span id="page-10-26"></span>[40] Ameya Prabhu, Philip HS Torr, and Puneet K Dokania. Gdumb: A simple approach that questions our progress in continual learning. In *Computer Vision–ECCV 2020: 16th European Conference, Glasgow, UK, August 23–28, 2020, Proceedings, Part II 16*, pages 524–540. Springer, 2020. [8](#page-7-4)
- <span id="page-10-5"></span>[41] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. icarl: Incremental classifier and representation learning. In *Proceedings of the IEEE conference on Computer Vision and Pattern Recognition*, pages 2001–2010, 2017. [1,](#page-0-1) [2,](#page-1-0) [5,](#page-4-4) [8](#page-7-4)
- <span id="page-10-19"></span>[42] Kuniaki Saito, Kohei Watanabe, Yoshitaka Ushiku, and Tatsuya Harada. Maximum classifier discrepancy for unsupervised domain adaptation. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 3723–3732, 2018. [4](#page-3-3)
- <span id="page-10-0"></span>[43] Ahmad Sajedi, Samir Khaki, Konstantinos N Plataniotis, and Mahdi S Hosseini. End-to-end supervised multilabel contrastive learning. *arXiv preprint arXiv:2307.03967*, 2023. [1](#page-0-1)
- <span id="page-10-4"></span>[44] Ahmad Sajedi, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Subclass knowledge distillation with known subclass labels. In *2022 IEEE 14th Image, Video, and Multidimensional Signal Processing Workshop (IVMSP)*, pages 1–5. IEEE, 2022. [1](#page-0-1)
- <span id="page-10-14"></span>[45] Andrew M Saxe, Pang Wei Koh, Zhenghao Chen, Maneesh Bhand, Bipin Suresh, and Andrew Y Ng. On random weights

and unsupervised feature learning. In *Icml*, volume 2, page 6, 2011. [2](#page-1-0)

- <span id="page-10-6"></span>[46] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *International Conference on Learning Representations*, 2018. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-1) [5,](#page-4-4) [7,](#page-6-3) [19](#page-18-0)
- <span id="page-10-25"></span>[47] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [6,](#page-5-2) [18](#page-17-1)
- <span id="page-10-15"></span>[48] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *International Conference on Machine Learning*, pages 9206–9216. PMLR, 2020. [2](#page-1-0)
- <span id="page-10-7"></span>[49] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. In *International Conference on Learning Representations*, 2019. [1,](#page-0-1) [2,](#page-1-0) [5](#page-4-4)
- <span id="page-10-10"></span>[50] Nikolaos Tsilivis, Jingtong Su, and Julia Kempe. Can we achieve robustness from data alone? *arXiv preprint arXiv:2207.11727*, 2022. [2](#page-1-0)
- <span id="page-10-22"></span>[51] Laurens Van der Maaten and Geoffrey Hinton. Visualizing data using t-sne. *Journal of machine learning research*, 9(11), 2008. [5,](#page-4-4) [8,](#page-7-4) [18](#page-17-1)
- <span id="page-10-1"></span>[52] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196–12205, 2022. [1,](#page-0-1) [2,](#page-1-0) [5,](#page-4-4) [6,](#page-5-2) [7,](#page-6-3) [8,](#page-7-4) [18](#page-17-1)
- <span id="page-10-8"></span>[53] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [2,](#page-1-0) [5](#page-4-4)
- <span id="page-10-17"></span>[54] Xiaolong Wang, Ross Girshick, Abhinav Gupta, and Kaiming He. Non-local neural networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 7794–7803, 2018. [3](#page-2-1)
- <span id="page-10-3"></span>[55] Yi Ru Wang, Samir Khaki, Weihang Zheng, Mahdi S. Hosseini, and Konstantinos N. Plataniotis. Conetv2: Efficient auto-channel size optimization for cnns. In *2021 20th IEEE International Conference on Machine Learning and Applications (ICMLA)*, pages 998–1003, 2021. [1](#page-0-1)
- <span id="page-10-18"></span>[56] Sanghyun Woo, Jongchan Park, Joon-Young Lee, and In So Kweon. Cbam: Convolutional block attention module. In *Proceedings of the European conference on computer vision (ECCV)*, pages 3–19, 2018. [3](#page-2-1)
- <span id="page-10-2"></span>[57] Jiaxiang Wu, Cong Leng, Yuhang Wang, Qinghao Hu, and Jian Cheng. Quantized convolutional neural networks for mobile devices. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4820–4828, 2016. [1](#page-0-1)
- <span id="page-10-9"></span>[58] Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. Feddm: Iterative distribution matching for communication-efficient federated learning. In *Workshop on Federated Learning: Recent Advances and New Challenges (in Conjunction with NeurIPS 2022)*. [2](#page-1-0)

- <span id="page-11-8"></span><span id="page-11-0"></span>[59] Xiyu Yu, Tongliang Liu, Xinchao Wang, and Dacheng Tao. On compressing deep models by low rank and sparse decomposition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 7370–7379, 2017. [1](#page-0-1)
- <span id="page-11-6"></span>[60] Sergey Zagoruyko and Nikos Komodakis. Paying more attention to attention: Improving the performance of convolutional neural networks via attention transfer. *arXiv preprint arXiv:1612.03928*, 2016. [3,](#page-2-1) [4](#page-3-3)
- <span id="page-11-7"></span>[61] Matthew D Zeiler and Rob Fergus. Visualizing and understanding convolutional networks. In *Computer Vision– ECCV 2014: 13th European Conference, Zurich, Switzerland, September 6-12, 2014, Proceedings, Part I 13*, pages 818–833. Springer, 2014. [4](#page-3-3)
- <span id="page-11-2"></span>[62] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [2,](#page-1-0) [5,](#page-4-4) [6,](#page-5-2) [7,](#page-6-3) [8,](#page-7-4) [12,](#page-11-8) [13,](#page-12-0) [14,](#page-13-1) [16,](#page-15-0) [18](#page-17-1)
- <span id="page-11-3"></span>[63] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514– 6523, 2023. [2,](#page-1-0) [4,](#page-3-3) [5,](#page-4-4) [6,](#page-5-2) [7,](#page-6-3) [8,](#page-7-4) [9,](#page-8-2) [12,](#page-11-8) [13,](#page-12-0) [16,](#page-15-0) [18](#page-17-1)
- <span id="page-11-1"></span>[64] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *Ninth International Conference on Learning Representations 2021*, 2021. [2,](#page-1-0) [5,](#page-4-4) [6,](#page-5-2) [7,](#page-6-3) [9,](#page-8-2) [16,](#page-15-0) [18](#page-17-1)
- <span id="page-11-5"></span>[65] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *Advances in Neural Information Processing Systems*, 2022. [2,](#page-1-0) [12](#page-11-8)
- <span id="page-11-4"></span>[66] Yanlin Zhou, George Pu, Xiyao Ma, Xiaolin Li, and Dapeng Wu. Distilled one-shot federated learning. *arXiv preprint arXiv:2009.07999*, 2020. [2](#page-1-0)

## 6. Supplementary Materials

### 6.1. Implementation Details

#### 6.1.1 Datasets

We carried out experiments on the following datasets: CI-FAR10/100 [\[29\]](#page-9-19), TinyImageNet [\[31\]](#page-10-21), ImageNet-1K [\[14\]](#page-9-0), and subsets of ImageNet-1K including ImageNette [\[25\]](#page-9-24), ImageWoof [\[25\]](#page-9-24), and ImageSquawk [\[9\]](#page-9-12). CIFAR10/100 is a standard computer vision dataset consisting of natural images with colored 32x32 pixels. It has 10 coarse-grained labels (CIFAR10) and 100 fine-grained labels (CIFAR100), each with 50,000 training samples and 10,000 tests. The classes of the CIFAR10 are "Airplane", "Car", "Bird", "Cat", "Deer", "Dog", "Frog", "Horse", "Ship", and "Truck," which are mutually exclusive. TinyImageNet is a subset of the ImageNet-1K dataset with 200 classes. The dataset contains 100,000 high-resolution training images and 10,000 test examples that are downsized to 64x64. ImageNet-1K is a standard large-scale dataset with 1,000 classes, including 1,281,167 training examples and 50,000 testing images. Following [\[65,](#page-11-5) [63\]](#page-11-3), we resize ImageNet-1K images to 64x64 resolution to match TinyImageNet. Compared to CIFAR10/100, Tiny-ImageNet and ImageNet-1K are more challenging because of

their diverse classes and higher image resolution. To further extend dataset distillation, we take a step forward by applying our method to even higher-resolution images, specifically 128x128 subsets of ImageNet. In previous dataset distillation research [\[9\]](#page-9-12), subsets were introduced based on categories and aesthetics, encompassing birds, fruits, and cats. In this study, we utilize ImageNette (assorted objects), ImageWoof (dog breeds), and ImageSquawk (birds) to provide additional examples of our algorithm's effectiveness. For a detailed enumeration of ImageNet classes in each of our datasets, please refer to Table [9.](#page-12-1)

### 6.1.2 Data Preprocessing

We implemented a standardized preprocessing approach for all datasets, following the methodology outlined in [\[62\]](#page-11-2). To ensure optimal model performance during both training and evaluation, we utilized several popular transformations, including color jittering, cropping, cutout, scaling, and rotation, as differentiable augmentation strategies across all datasets. For the CIFAR10/100 datasets, we additionally applied Kornia zero-phase component analysis (ZCA) whitening, using the same setting as [\[9\]](#page-9-12). However, we refrained from using ZCA preprocessing for the medium- and high-resolution datasets due to the computational expense of the full-size ZCA transformation. As a result, the distilled images for these datasets display checkboard artifacts (see Figures [25,](#page-27-0) [26,](#page-28-0) [27,](#page-29-0) [28,](#page-30-0) and [29\)](#page-31-0). It is worth noting that we visualized the distilled images by directly applying the reverse transformation based on the corresponding data preprocessing without any further modifications.

#### 6.1.3 Implementations of Prior Works

To ensure fair comparisons with prior works, we obtained publicly available distilled data for each baseline method and trained models using our experimental setup. We utilized the same ConvNet architecture with three, four, or five layers, depending on the image resolutions, and applied the same preprocessing technique across all methods. In cases where our results were comparable or inferior to those reported in the original papers, we presented their default numbers directly. Regarding the Kernel Inducing Points (KIP) method [\[37,](#page-10-12) [38\]](#page-10-11), we made a slight modification by employing a 128kernel ConvNet instead of the original 1024-kernel version. We did our best to reproduce prior methods that did not conduct experiments on some datasets by following the released author codes. However, for methods that encountered scalability issues on high-resolution datasets, we were unable to obtain the relevant performance scores.

<span id="page-12-1"></span><span id="page-12-0"></span>

| Dataset         | 0                     | 1                   | 2                  | 3        | 4               | 5                   | 6                      | 7        | 8                | 9                   |
|-----------------|-----------------------|---------------------|--------------------|----------|-----------------|---------------------|------------------------|----------|------------------|---------------------|
| ImageNette [25] | Tench                 | English<br>Springer | Cassette<br>Player | Chainsaw | Church          | French Horn         | Garbage<br>Truck       | Gas Pump | Golf Ball        | Parachute           |
| ImageWoof [25]  | Australian<br>Terrier | Border Terrier      | Samoyed            | Beagle   | Shih-Tzu        | English<br>Foxhound | Rhodesian<br>Ridgeback | Dingo    | Golden Retriever | English<br>Sheepdog |
| ImageSquawk [9] | Peacock               | Flamingo            | Macaw              | Pelican  | King<br>Penguin | Bald Eagle          | Toucan                 | Ostrich  | Black Swan       | Cockatoo            |

Table 9: Class listings for our ImageNet subsets.

<span id="page-12-2"></span>

|                           |                                         | <b>Hyperparameters</b>                                          | Options/                 | <b>Value</b>                  |
|---------------------------|-----------------------------------------|-----------------------------------------------------------------|--------------------------|-------------------------------|
| Category                  | <b>Parameter Name</b>                   | <b>Description</b>                                              | Range                    |                               |
|                           | Learning Rate $\eta_s$ (images)         | Step size towards global/local minima                           | (0, 10.0]                | $IPC \leq 50: 1.0$            |
|                           |                                         |                                                                 |                          | IPC > 50: 10.0                |
|                           | Learning Rate $\eta_{\theta}$ (network) | Step size towards global/local minima                           | (0, 1.0]                 | 0.01                          |
|                           | <b>Optimizer</b> (images)               | Updates synthetic set to approach global/local minima           | SGD with                 | Momentum: 0.5                 |
|                           |                                         |                                                                 | Momentum                 | Weight Decay: 0.0             |
| Optimization              | <b>Optimizer</b> (network)              | Updates model to approach global/local minima                   | SGD with                 | Momentum: 0.9                 |
|                           |                                         |                                                                 | Momentum                 | Weight Decay: $5e-4$          |
|                           | Scheduler (images)                      |                                                                 |                          |                               |
|                           | Scheduler (network)                     | Decays the learning rate over epochs                            | StepLR                   | Decay rate: 0.5               |
|                           |                                         |                                                                 |                          | Step size: 15.0               |
|                           | <b>Iteration Count</b>                  | Number of iterations for learning synthetic data                | $[1,\infty)$             | 8000                          |
| <b>Loss Function</b>      | Task Balance $\lambda$                  | <b>Regularization Multiplier</b>                                | $[0,\infty)$             | Low Resolution: 0.01          |
|                           |                                         |                                                                 |                          | High Resolution: 0.02         |
|                           | <b>Power Value</b> p                    | Exponential power for amplification in the SAM module           | $[1,\infty)$             |                               |
|                           | <b>Loss Configuration</b>               | Type of error function used to measure distribution discrepancy |                          | Mean Squared Error            |
|                           | <b>Normalization Type</b>               | Type of normalization used in the SAM module on attention maps  |                          | L2                            |
|                           |                                         |                                                                 | brightness               | 1.0                           |
|                           | Color                                   | Randomly adjust (jitter) the color components of an image       | saturation               | 2.0                           |
|                           |                                         |                                                                 | contrast                 | 0.5                           |
| <b>DSA</b> Augmentations  | <b>Crop</b>                             | Crops an image with padding                                     | ratio crop pad           | 0.125                         |
|                           | Cutout                                  | Randomly covers input with a square                             | cutout ratio             | 0.5                           |
|                           | <b>Flip</b>                             | Flips an image with probability p in range:                     | (0, 1.0]                 | 0.5                           |
|                           | <b>Scale</b>                            | Shifts pixels either column-wise or row-wise                    | scaling ratio            | $\overline{1.2}$              |
|                           | Rotate                                  | Rotates image by certain angle                                  | $0^\circ - 360^\circ$    | $-15^{\circ}$ , $+15^{\circ}$ |
|                           | <b>Conv Laver Weights</b>               | The weights of convolutional layers                             | R bounded by kernel size | <b>Uniform Distribution</b>   |
| <b>Encoder Parameters</b> | <b>Activation Function</b>              | The non-linear function at the end of each layer                |                          | ReLU                          |
|                           | <b>Normalization Laver</b>              | Type of normalization layer used after convolutional blocks     | ٠                        | <b>InstanceNorm</b>           |

Table 10: Hyperparameters Details.

#### 6.1.4 Hyperparameters

In order to ensure that our methodology can be reproduced, we have included a Table [10](#page-12-2) listing all the hyperparameters used in this work. For the baseline methods, we utilized the default parameters that the authors specified in their original papers. We used the same hyperparameter settings across all experiments unless otherwise stated. Specifically, we employed an SGD optimizer with a learning rate of 1 for learning synthetic sets and a learning rate of 0.01 for training neural network models. For low-resolution datasets, we used a 3-layer ConvNet, while for medium- and high-resolution datasets, we followed the recommendation of [\[63\]](#page-11-3) and used a 4-layer and 5-layer ConvNet, respectively. In all experiments, we used a mini-batch of 256 real images from each class to learn the synthetic set. Additionally, we conducted ablation studies on certain hyperparameters, such as task balance  $\lambda$ and the power parameter  $p$  in the Spatial Attention Matching (SAM) modules, which are discussed in Section [6.2.2.](#page-13-0)

#### 6.2. Additional Results and Further Analysis

##### 6.2.1 Comparison to More Baselines

We conducted a comparison between images created by the DataDAM and popular generative models such as variational auto-encoders (VAEs) [\[28,](#page-9-25) [39\]](#page-10-23) and generative adversarial networks (GANs) [\[20,](#page-9-26) [36,](#page-10-27) [6,](#page-9-21) [34\]](#page-10-24) to evaluate their data efficiency. For this purpose, we selected state-of-the-art models, including the DC-VAE [\[39\]](#page-10-23), cGAN [\[36\]](#page-10-27), BigGAN [\[6\]](#page-9-21), and GMMN [\[34\]](#page-10-24). The DC-VAE generates a model with dual contradistinctive losses, which improves the generative autoencoder's inference and synthesis abilities simultaneously. The cGAN model is conditioned on both the generator and discriminator, while BigGAN uses differentiable augmentation techniques [\[62\]](#page-11-2). On the other hand, GMMN aims to learn an image generator that can map a uniform distribution to a real image distribution. We trained these models on the CIFAR10 dataset with varying numbers of images per class (1, 10, and 50 IPCs) using ConvNet's (3-layer) architecture [\[18\]](#page-9-20) and evaluated their performance on real testing images. Our results, presented in Table [11,](#page-13-2) indicate that our proposed method significantly outperforms these generative models.

<span id="page-13-1"></span>The DataDAM generates superior training images that offer more informative data for training DNNs, while the primary goal of the generative models is to create realistic-looking images that can deceive humans. Therefore, the efficiency of images produced by generative models is similar to that of randomly selected coresets.

We also employed another baseline approach, which is learning synthetic images through distribution matching using vanilla maximum mean discrepancy [\[21\]](#page-9-17) (MMD) in the pixel space. By utilizing MMD loss with a linear kernel, we achieved improved performance compared to randomly selected real images and generative models (see Table [11\)](#page-13-2). However, DataDAM surpasses the results of vanilla MMD since it generates more informative synthetic images by utilizing the information of the feature extractor at various levels of representation.

<span id="page-13-2"></span>

| IPC | Random         | DC-VAE         | cGAN           | BigGAN         | GMMN           | MMD            | DataDAM        |
|-----|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
| 1   | $14.4 pm 2.0$ | $15.7 pm 2.1$ | $16.3 pm 1.4$ | $15.8 pm 1.2$ | $16.1 pm 2.0$ | $22.7 pm 0.6$ | $32.0 pm 1.2$ |
| 10  | $26.0 pm 1.2$ | $29.8 pm 1.0$ | $27.9 pm 1.1$ | $31.0 pm 1.4$ | $32.2 pm 1.3$ | $34.9 pm 0.3$ | $54.2 pm 0.8$ |
| 50  | $43.4 pm 1.0$ | $44.0 pm 0.8$ | $43.8 pm 0.9$ | $46.2 pm 0.9$ | $45.3 pm 1.0$ | $50.9 pm 0.3$ | $67.0 pm 0.4$ |

Table 11: Comparison of the DataDAM's performance to popular generative models and the MMD baseline on the CIFAR10 dataset using ConvNets. The "Random" category denotes randomly selected real images.

<span id="page-13-0"></span>

##### 6.2.2 More Ablation Studies

Evaluation of power parameter  $p$  in the SAM module. This section examines how the  $p$ -norm impacts the efficiency of spatial-wise attention maps in the SAM module. In Figure [8,](#page-13-3) we evaluate the testing accuracy of the DataDAM on CIFAR10 with IPC 10 for various values of  $p$ . Our method proves to be robust across a broad range of  $p$  values, indicating that it is not significantly affected by changes in the degree of discrepancy measured by  $\mathcal{L}_{SAM}$ . However, when the power is raised to 8, the DataDAM gives more weight to spatial locations that correspond to the neurons with the highest activations. In other words, it prioritizes the most discriminative parts, potentially ignoring other important components that may be crucial in approximating the data distribution. This could negatively impact the testing performance to some extent.

Exploring the effect of Gaussian noise initialization for synthetic images on DataDAM. To augment our results in the main paper, we present an extended training configuration for initialization from Gaussian noise. We conducted this experiment on CIFAR10 with IPC 50. As seen in Figure [9,](#page-13-4) the Gaussian noise initialization scheme takes longer to converge to a competitive accuracy level. Despite underperforming in comparison to Random and K-Center initialization, it still demonstrates the ability of our proposed method to distill information from the real dataset onto pure random

<span id="page-13-3"></span>Image /page/13/Figure/7 description: The image is a line graph showing the testing accuracy (%) on the y-axis against the power parameter p in the SAM module on the x-axis. The x-axis ranges from 1 to 8, with ticks at 1, 2, 3, 4, 5, 6, 7, and 8. The y-axis ranges from 52.5 to 55.5, with ticks at 52.5, 53.0, 53.5, 54.0, 54.5, 55.0, and 55.5. The line graph shows data points at (1, 54.0), (2, 53.7), (4, 54.2), and (8, 53.0). A shaded region around the line indicates a confidence interval. The line starts at approximately 54.0% accuracy for p=1, drops to about 53.7% for p=2, rises to about 54.2% for p=4, and then decreases to about 53.0% for p=8.

Figure 8: The effect of the power parameter  $p$  on the final testing accuracy (%) for the CIFAR10 dataset with IPC 10 configuration.

<span id="page-13-4"></span>Image /page/13/Figure/9 description: The image is a line graph showing the testing accuracy (%) on the y-axis against the training epoch on the x-axis. The x-axis ranges from 0 to 40,000, with major tick marks every 5,000 epochs. The y-axis ranges from 30% to 70%, with major tick marks every 5%. A single green line, labeled "Gaussian Noise" in the legend, represents the testing accuracy. The accuracy starts at around 30% at epoch 0 and steadily increases, reaching approximately 61% by epoch 40,000. There is a noticeable dip in accuracy around epoch 20,000, where it drops to about 57% before recovering and continuing to increase. The line has a shaded green area around it, indicating some variability or confidence interval.

Figure 9: Test accuracy evolution of synthetic image learning on CIFAR10 with IPC 50 under Gaussian noise initialization.

noise. Moreover, it is capable of outperforming competitive methods, particularly KIP [\[37\]](#page-10-12) and DSA [\[62\]](#page-11-2). In Figure [10,](#page-14-0) we provide visualizations of the synthetic data generated from random noise during different iterations. These visualizations highlight how our method successfully transfers information from the real dataset to the random noise, especially when comparing the initial noise image with the final iteration.

Exploring the effect of different augmentation strategies in DataDAM. In this section, we explore the impact of augmentation methods on the effectiveness of our approach when evaluated on the CIFAR10 dataset with an IPC 10 configuration. We treat our method as a black box, as in the work of [\[13\]](#page-9-9), and assess the effects of various augmentation techniques such as AutoAugment [\[11\]](#page-9-27), RandAugment [\[12\]](#page-9-28), DSA [\[62\]](#page-11-2), and no augmentation on the distilled datasets during the evaluation phase. The results are presented in Figure [11.](#page-14-1) Our observations reveal that DSA delivers significantly better performance as it is integrated into the training process of the synthetic dataset and is more compatible with

<span id="page-14-0"></span>Image /page/14/Figure/0 description: This image displays the learning process of a generative model on the CIFAR-10 dataset, initialized from Gaussian noise. It shows eight columns, labeled (a) through (h), representing different training iterations: 0, 600, 2000, 5000, 15000, 25000, 35000, and 40000. Each column contains ten rows, with each row displaying multiple generated images for a specific class. The classes are labeled at the top of each row: plane, car, bird, cat, deer, dog, frog, horse, ship, and truck. The images progress from noisy, abstract patterns at iteration 0 to increasingly recognizable representations of the respective classes as the iterations increase. For instance, the 'plane' class shows a transition from random pixels to a discernible airplane shape, and similarly for other classes like 'car', 'dog', and 'ship'.

Figure 10: The learning process of all classes in the CIFAR10 dataset (IPC 50) initialized from Gaussian noise. We take two random images for each class and visualize their progression over the 40,000 training epochs.

the learning phase of the distilled images. Additionally, our findings indicate that augmentation is vital for training on synthetic data, as evidenced by the substantial differences between different augmentation methods and no augmentation. Therefore, applying augmentation techniques to our distilled images during evaluation can substantially enhance model performance.

Exploring the effect of different loss configurations in  $\mathcal{L}_{SAM}$ . In this section, we explore the impact of different loss configurations on attention loss  $(\mathcal{L}_{SAM})$ . To conduct this evaluation, we employed mean absolute error (MAE), cosine dissimilarity, and mean square error (MSE) as objective functions for  $\mathcal{L}_{SAM}$  to train a synthetic dataset on CIFAR10 with IPC 10. The results presented in Figure [12](#page-15-1) demonstrate that MSE yields the best results. Nonetheless, it is crucial to note that even with any of these configurations, our method still outperforms most of the competitive methods. Therefore, we can conclude that our approach performs well with any loss configuration, but a well-designed configuration can result in a substantial performance improvement of up to 2.0% in our ablation study.

Exploring the effect of normalization in the SAM module. In this section, we aim to evaluate the impact of the

<span id="page-14-1"></span>Image /page/14/Figure/5 description: A bar chart displays the testing accuracy (%) for different augmentation types. The x-axis is labeled "Augmentation Types" and shows four categories: "None", "Autoaug", "Randaug", and "DSA". The y-axis is labeled "Testing Accuracy (%)" and ranges from 48 to 55. The bar for "None" is red and reaches approximately 48.7%. The bar for "Autoaug" is green and reaches approximately 51.4%. The bar for "Randaug" is blue and reaches approximately 52.3%. The bar for "DSA" is orange and reaches approximately 54.2%. Error bars are present on top of each bar, indicating variability. The error bar for "None" extends from about 48.5% to 49%. The error bar for "Autoaug" extends from about 51.2% to 51.7%. The error bar for "Randaug" extends from about 52.1% to 52.5%. The error bar for "DSA" extends from about 54% to 54.9%.

Figure 11: The effect of different augmentation strategies during the evaluation phase on the final testing accuracy  $(\%)$  for the CIFAR10 dataset with IPC 10 configuration.

normalization block in the internal structure of the SAM module on testing accuracy. We conducted experiments by training distilled images for CIFAR10 with IPC 10 and testing three normalization techniques:  $L_1$  normalization,  $L_2$ 

<span id="page-15-1"></span><span id="page-15-0"></span>Image /page/15/Figure/0 description: A bar chart displays the testing accuracy (%) for three different loss configurations: Mean Absolute Error, Cosine Dissimilarity, and Mean Squared Error. The y-axis ranges from 50% to 56%, with grid lines at every 0.5%. The Mean Absolute Error bar reaches approximately 52.1% with an error bar extending to about 52.3%. The Cosine Dissimilarity bar reaches approximately 53.7% with an error bar extending to about 54.3%. The Mean Squared Error bar reaches approximately 54.2% with an error bar extending to about 55%. The x-axis is labeled "Loss Configurations".

Figure 12: The effect of loss configurations of  $\mathcal{L}_{SAM}$  on the final testing accuracy (%) for the CIFAR10 dataset with IPC 10 configuration.

<span id="page-15-2"></span>Image /page/15/Figure/2 description: A bar chart displays the testing accuracy (%) for different normalization types. The x-axis is labeled "Normalization Types" and shows three categories: "None", "L1", and "L2". The y-axis is labeled "Testing Accuracy (%)" and ranges from 50 to 56. The bar for "None" is red and reaches approximately 52.4%, with an error bar extending from about 51.7% to 53.3%. The bar for "L1" is green and reaches approximately 54%, with an error bar extending from about 53.5% to 54.5%. The bar for "L2" is blue and reaches approximately 54.2%, with an error bar extending from about 53.5% to 55%. The chart has a light gray background with horizontal grid lines.

Figure 13: The effect of different normalization blocks of the SAM module on the final testing accuracy (%) for the CIFAR10 dataset with IPC 10 configuration.

normalization, and no normalization. The results, as shown in Figure [13,](#page-15-2) indicate that  $L_2$  normalization is the most effective in terms of testing accuracy. By adding normalization, we reduce the magnitude of the attention loss  $\mathcal{L}_{SAM}$  in backpropagation, thus decreasing the chance of overshooting the global minima in the optimization space when modifying the input image's pixels. We can observe that both normalization schemes work well, but the absence of normalization leads to significant performance degradation. Therefore, we conclude that while the appropriate use of normalization is critical for the performance of the DataDAM, the type of normalization is not as significant.

##### 6.2.3 More Experiments and Analysis on Neural Architecture Search

Taking inspiration from  $[64, 62, 63]$  $[64, 62, 63]$  $[64, 62, 63]$  $[64, 62, 63]$  $[64, 62, 63]$ , we define a search space consisting of 720 ConvNets on the CIFAR10 dataset. We evaluate the models using our distilled data with IPC 50 as a proxy set under the neural architecture search (NAS) framework. We start with a base ConvNet and construct a uniform grid that varies in depth  $D \in \{1, 2, 3, \ldots\}$ 4}, width  $W \in \{32, 64, 128, 256\}$ , activation function  $A \in \{Sigmoid, ReLu, LeakyReLU\}$ , normalization technique  $N \in \{None, BatchNorm, LayerNorm, InstanteNorm,$ GroupNorm}, and pooling operation  $P \in \{None, MaxPool-\}$ ing, AvgPooling}. These candidates are then evaluated based on their validation performance and ranked accordingly.

In Figure [14,](#page-16-0) we displayed the performance rank correlation between the proxy set, generated using various methods, and the whole training dataset using Spearman's correlation across all 720 architectures. Each point in the graph represents a selected architecture. The x-axis represents the test accuracy of the model trained on the proxy set, while the y-axis represents the accuracy of the model trained on the whole dataset. Our analysis shows that all methods perform well. However, DataDAM has a higher concentration of dots close to the straight line, indicating a better proxy set for obtaining more reliable performance rankings of candidate architectures. These results are on par with the DataDAM's performance correlation (0.72), which is higher than other prior works. To further assess the effectiveness of our approach, we conducted an analysis of the top 20% of the search space, selecting 144 architectures with the highest validation accuracy. As depicted in Figure [15,](#page-16-1) our method outperforms most of the state-of-the-art methods, except for early stopping, where we only beat it by a small margin. Our evaluation of the correlation graphs indicates that DataDAM is capable of accurately correlating the performance of models trained on the proxy dataset with their performance on the whole training dataset. We substantiate these findings by presenting quantitative results of performance and Spearman's correlation in Table [12.](#page-15-3)

<span id="page-15-3"></span>

|                     | Random     | DSA        | DM         | CAFE        | Ours        | Early-stopping | Whole Dataset |
|---------------------|------------|------------|------------|-------------|-------------|----------------|---------------|
| Performance (%)     | 88.9       | 87.2       | 87.2       | 83.6        | <b>89.0</b> | 88.9           | 89.2          |
| Correlation Top 20% | 0.44       | 0.57       | 0.51       | 0.36        | <b>0.69</b> | 0.64           | 1.00          |
| Time cost (min)     | 33.0       | 31.2       | 32.2       | <b>30.7</b> | 34.8        | 37.1           | 5168.9        |
| Storage (imgs)      | <b>500</b> | <b>500</b> | <b>500</b> | <b>500</b>  | <b>500</b>  | 5 	imes 104    | 5 	imes 104   |

Table 12: Neural architecture search on CIFAR10 with a search space of the top 20% of the sample space with the highest validation accuracy.

Experiments on NAS-Bench-201. To conduct a more comprehensive analysis of the neural architecture search, we expanded the search space by including NAS-Bench-201  $[16]$  as recommended in  $[13]$ . Our aim is to compare

<span id="page-16-0"></span>Image /page/16/Figure/0 description: This figure displays six scatter plots, arranged in two rows and three columns, illustrating the performance rank correlation between proxy-set and whole dataset training across all 720 architectures. Each scatter plot is labeled with a letter (a) through (f) and a corresponding method name: (a) EarlyStop, (b) Random, (c) DSA, (d) DM, (e) CAFE, and (f) DataDAM. Each plot shows 'Testing Accuracy (%)' on the y-axis and 'Proxy-set Acc.' on the x-axis, with a reported 'Correlation' value for each method. The correlations range from 0.59 for CAFE to 0.72 for DataDAM. The data points in all plots generally show a positive correlation, indicating that higher proxy-set accuracy tends to correspond with higher testing accuracy.

Figure 14: Performance rank correlation between proxy set and whole dataset training across all 720 architectures.

<span id="page-16-1"></span>Image /page/16/Figure/2 description: This figure displays six scatter plots, arranged in two rows and three columns, illustrating the performance rank correlation between proxy-set accuracy and testing accuracy for different methods. The top row shows plots for 'Early-stopping' with a correlation of 0.64, 'Random' with a correlation of 0.44, and 'DSA' with a correlation of 0.57. The bottom row presents plots for 'DM' with a correlation of 0.51, 'CAFE' with a correlation of 0.36, and 'DataDAM' with a correlation of 0.69. Each plot has 'Proxy-set Acc.' on the x-axis and 'Testing Accuracy (%)' on the y-axis, with data points marked by blue plus signs.

Figure 15: Performance rank correlation between proxy-set and whole-dataset training across the top 20% of the search space (selecting 144 architectures with the highest validation accuracy).

<span id="page-17-3"></span><span id="page-17-1"></span>Image /page/17/Figure/0 description: The image displays five scatter plots arranged horizontally, each representing a different method: DC, DSA, DM, CAFE, and DataDAM. Each plot shows multiple clusters of points, with points within each cluster colored similarly. Some points in each cluster are marked with a star shape, indicating a potential distinction or feature. The clusters are arranged in a roughly circular pattern within each plot. The overall visual impression is a comparison of how these five methods group and represent data points.

Figure 16: Distributions of the synthetic images learned by five methods on the CIFAR10 dataset with IPC 50. The stars represent the synthetic data dispersed amongst the original dataset. The classes are as follows: plane, car, bird, cat, deer, dog, frog, horse, ship, truck.

the performance of DataDAM against other methods using the CIFAR10 dataset with IPC 50 as the proxy set. To create a search space, we randomly selected 100 networks from the 15,635 available models in NAS-Bench-201. We followed the configuration and settings presented in [\[13\]](#page-9-9), which involve training all models using five random seeds and ranking them based on their average accuracy on a validation set comprising 10,000 images. We used two metrics to evaluate the effectiveness of NAS: the performance correlation ranking between models trained on synthetic and real datasets and the top-1 performance in the search space. In contrast to the previous search space that concentrated on 720 ConvNet architectures, we observed a distinct trend in this larger NAS benchmark with modern architectures. According to Table [13,](#page-17-2) while most methods achieved negative correlations between performance on the proxy set and the entire dataset, our method had a small positive correlation and obtained competitive outcomes on the original dataset. This implies that DataDAM preserves the true strength of the underlying model more effectively than previous works. Nevertheless, despite the encouraging performance gains achieved by the best single model, utilizing the distilled data to guide model design remains a significant challenge. It is important to mention that the rank correlation presented in Table [13](#page-17-2) for the original real dataset is not 1.0. This is because a smaller architecture was used and the ranking was based on a validation set, as pointed out in  $[13]$ .

<span id="page-17-2"></span>

|             | Random  | DC      | DSA     | DM      | KIP     | MTT     | DataDAM                   | Whole Dataset |
|-------------|---------|---------|---------|---------|---------|---------|---------------------------|---------------|
| Correlation | $-0.06$ | $-0.19$ | $-0.37$ | $-0.37$ | $-0.50$ | $-0.09$ | <b><math>0.07</math></b>  | $0.7487$      |
| Top 1 (%)   | $91.9$  | $86.44$ | $73.54$ | $92.16$ | $92.91$ | $73.54$ | <b><math>93.96</math></b> | $93.5$        |

Table 13: Spearman's rank correlation results were obtained using NAS-Bench-201. The best performance achieved on the test set is 94.36% [\[13\]](#page-9-9).

<span id="page-17-0"></span>

##### 6.3. Additional Visualizations and Analysis

###### 6.3.1 More Analysis on Data Distribution

To complement the data distribution visualization results presented in the main paper, we have included t-SNE [\[51\]](#page-10-22) illustrations for all categories in Figure [16.](#page-17-3) We utilized t-SNE to show the features of real and synthetic sets generated by DC [\[64\]](#page-11-1), DSA [\[62\]](#page-11-2), DM [\[63\]](#page-11-3), CAFE [\[52\]](#page-10-1), and DataDAM in the embedding space of the ResNet-18 [\[23\]](#page-9-1) architecture. The

visualizations were applied to the CIFAR10 dataset with IPC 50 for all methodologies. As depicted in Figure [16,](#page-17-3) our approach, similar to DM, preserves the distribution of data with a well-balanced spread over the entire dataset. Conversely, other methods, such as DC, DSA, and CAFE, exhibit a significant bias toward the boundaries of certain clusters and have high false-positive rates for the majority of the classes. To put it simply, the t-SNE visualization validates that our method maintains a considerable degree of impartiality in accurately capturing the dataset distribution uniformly across all categories.

###### 6.3.2 Extended Visualizations of Synthetic Images

Visualization of the synthetic images trained with different model architectures in DataDAM. In this section, we present a qualitative comparison of the generated distilled images using different architectures to demonstrate how the choice of architecture influences the quality of the synthetic set. We assess the efficacy of the distilled data trained using ConvNet [\[18\]](#page-9-20), AlexNet [\[30\]](#page-9-18), and VGG-11 [\[47\]](#page-10-25) architectures on the CIFAR10 dataset with IPC 50. Our results, as depicted in Figure [17,](#page-19-0) reveal that the distilled data can encode the inductive bias of the chosen architecture. Specifically, the distilled images produced by the simplest architecture, i.e., ConvNet [\[18\]](#page-9-20), exhibit a natural appearance and can transfer well to other architectures (see Table 3 of the main paper). In contrast, the distilled images generated by modern architectures like VGG-11 [\[47\]](#page-10-25) exhibit different brightness and contrast than natural images. We found that increasing the complexity and number of convolutional layers in the feature extraction process led to brighter and more contrasting distilled images. This is likely because the attention loss  $(\mathcal{L}_{SAM})$ becomes more potent, resulting in a more substantial modulation effect on the input image pixels during backpropagation. This trend is noticeable in the distilled images generated by AlexNet [\[30\]](#page-9-18) and VGG-11 [\[47\]](#page-10-25). We note that the synthetic images may reflect the similarity between architectures, as evidenced by the similarity between the images produced by AlexNet and ConvNet. This finding suggests that the inductive biases of these two architectures are comparable.

Visualization of the synthetic images trained with dif-

<span id="page-18-0"></span>ferent loss components in DataDAM. This section involves a comparison of the synthetic images generated by utilizing different loss objectives, namely only  $\mathcal{L}_{MMD}$ , only  $\mathcal{L}_{SAM}$ , layer-wise feature map transfer loss, and the DataDAM loss. The CIFAR10 dataset with IPC 10 was used for this evaluation to qualitatively assess the contribution of each loss component. As shown in Figure [18,](#page-20-0) the visualization of DataDAM is a linear combination of the  $\mathcal{L}_{SAM}$  and  $\mathcal{L}_{MMD}$ visualizations, resulting in a brighter and more contrasted image compared to each loss component individually. The generated synthetic sets by  $\mathcal{L}_{SAM}$  and layer-wise feature transfer loss are somewhat similar since both losses match the information of feature maps generated by the real and synthetic datasets. However, the images distilled by  $\mathcal{L}_{SAM}$ are brighter and more contrasted due to the matching of the most discriminative parts of the images.

Visualization of the synthetic images trained with different layers in DataDAM. We conducted an experiment to analyze the distilled images produced by matching different layers of the ConvNet on real and synthetic datasets. Our study focused specifically on the CIFAR10 dataset with IPC 10. Figure [19](#page-21-0) demonstrates that the layers performed differently as each layer conveyed distinct information regarding the data distributions. Our approach, DataDAM, utilizes all intermediate and final layers, resulting in distilled images that possess greater brightness and contrast. This is primarily due to the matching of attention maps in each layer as well as the embedding representation of the final layer.

Visualization of the synthetic images trained with different initialization strategies in DataDAM. In this section, we presented the distilled images for the CIFAR10 dataset generated by IPC 50 using three distinct initialization methods: Random, K-Center [\[46,](#page-10-6) [13\]](#page-9-9), and Gaussian noise. Figure [20](#page-22-0) illustrates the learned representations of the synthesis images produced using each initialization strategy. We observed a striking resemblance between the distilled images obtained through Random and K-Center initialization, which further confirms the results presented in the main paper. In contrast, the images generated using Gaussian noise initialization have noticeable differences in comparison to others, but they have still been learned effectively, and they contain crucial information for each class. In summary, these qualitative observations provide additional evidence that our model is robust enough to handle variations in initialization conditions.

More distilled image visualization. We provide additional visualizations of the distilled images for all five datasets used in this work, namely CIFAR10 (Figures [21,](#page-23-0) [22\)](#page-24-0), CIFAR100 (Figures [23,](#page-25-0) [24\)](#page-26-0), TinyImageNet (Figure [25\)](#page-27-0), ImageNet-1K (Figure [26\)](#page-28-0), ImageNette (Figure [27\)](#page-29-0), Image-Woof (Figure [28\)](#page-30-0), and ImageSquawk (Figure [29\)](#page-31-0).

<span id="page-19-0"></span>Image /page/19/Figure/0 description: The image displays three grids of synthetic images generated using different model architectures on the CIFAR10 dataset. The top grid, labeled (a) ConvNet, shows a collection of images. The middle grid, labeled (b) AlexNet, also presents a variety of images. The bottom grid, labeled (c) VGG-11, contains another set of generated images. All three grids appear to be composed of numerous small, square images, each depicting various objects or scenes.

Figure 17: Learned synthetic images with different model architectures on the CIFAR10 dataset with IPC 50.

<span id="page-20-0"></span>Image /page/20/Picture/0 description: The image displays four grids of generated images, each labeled with a different condition. The top left grid is labeled "(a) LMMD", and the top right grid is labeled "(b) LSAM". The bottom left and bottom right grids are not explicitly labeled with text, but they appear to be similar to the top grids. Each grid contains multiple rows and columns of small, colorful images, with text labels above each image indicating the class it represents. The classes visible include "plane", "car", "bird", "cat", "deer", "dog", "frog", "horse", "ship", and "truck". The generated images within each grid show variations in quality and style, likely representing different outcomes of a generative model under different conditions.

(c) Feature Map Transfer Loss (d) DataDAM

Figure 18: Learned synthetic images with different loss functions on the CIFAR10 dataset with IPC 10.

<span id="page-21-0"></span>Image /page/21/Figure/0 description: The image displays a grid of images, organized into four sections labeled (a), (b), (c), and (d). Section (a) shows a grid of images labeled "Last layer (LMMD)". Section (b) shows a grid of images labeled "Layer 1 and Last Layer". Section (c) shows a grid of images labeled "Layer 2 and Last Layer". Section (d) shows a grid of images labeled "Layer 1 and Layer 2 (fine-tune)". Each grid contains multiple rows and columns of smaller images, with labels such as "plane", "car", "bird", "cat", "deer", "dog", "frog", "horse", "ship", and "truck" appearing above some of the images.

(d) Layer 1 and Layer 2  $(\mathcal{L}_{\text{SAM}})$  (e) All layers (DataDAM)

(e) All layers (DataDAM)

Image /page/21/Figure/3 description: Figure 19 shows learned synthetic images with different matching layers on the CIFAR10 dataset with IPC 10.

<span id="page-22-0"></span>Image /page/22/Figure/0 description: The image displays three grids of synthetic images generated using different initialization strategies on the CIFAR-10 dataset. The top grid, labeled (a) Random Initialization, shows a diverse collection of images. The middle grid, labeled (b) K-Center Initialization, also presents a variety of images, appearing somewhat more organized than the first. The bottom grid, labeled (c) Gaussian noise Initialization, contains images that seem to have a different visual characteristic compared to the other two, possibly due to the noise initialization. Each grid contains numerous small, square images, many of which have labels indicating the object class they represent, such as 'plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', and 'truck'.

Figure 20: Learned synthetic images with different initialization strategies on the CIFAR10 dataset with IPC 50.

<span id="page-23-0"></span>

| plane  | plane plane |                       | plane <b>in the plane</b>                        |                   | plane plane plane plane                                                                                                                                                                                                        |       | plane <b>in the plane</b> | plane by the set of the set of the set of the set of the set of the set of the set of the set of the set of the set of the set of the set of the set of the set of the set of the set of the set of the set of the set of the | plane <b>in the plane</b> |
|--------|-------------|-----------------------|--------------------------------------------------|-------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|---------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------|
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
| car    | car         | car                   | car                                              | car               | car                                                                                                                                                                                                                            | car   | $1$ car                   |                                                                                                                                                                                                                               | car                       |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
| bird   | bird        | bird                  | <b>bird</b>                                      | bird <b>Latin</b> | bird                                                                                                                                                                                                                           | bird  | <b>bird</b>               | bird                                                                                                                                                                                                                          | bird                      |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
| cat    | cat         | cat                   | cat                                              | cat               | cat                                                                                                                                                                                                                            | cat   | cat                       | $\blacksquare$ cat                                                                                                                                                                                                            | cat                       |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
| deer   |             | deer <b>deer</b> deer | deer                                             | deer              | deer <b>Allen</b>                                                                                                                                                                                                              | deer  | deer <b>Albem</b>         | deer                                                                                                                                                                                                                          | deer                      |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        | dog         | dog <sup>1</sup>      | dog                                              | dog               | dog                                                                                                                                                                                                                            | dog   | dog                       | dog                                                                                                                                                                                                                           | dog <sup>1</sup>          |
| dog    |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
| frog   | frog        | <b>Trog</b>           | frog                                             | frog              | <b>The Strong Property</b>                                                                                                                                                                                                     | frog  | frog <sup>1</sup>         | frog                                                                                                                                                                                                                          | frog                      |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        | horse horse | horse                 | horse                                            | horse             | horse                                                                                                                                                                                                                          | horse | horse                     | horse                                                                                                                                                                                                                         | horse                     |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
| ship.  | ship        | ship <b>Ship</b>      |                                                  | ship ship         | ship ship                                                                                                                                                                                                                      |       | ship                      | ship                                                                                                                                                                                                                          | ship                      |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
| truck- | truck       | truck                 | truck <b>the state of the state of the state</b> | truck             | truck the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the stat | truck |                           | truck truck                                                                                                                                                                                                                   | truck-                    |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |
|        |             |                       |                                                  |                   |                                                                                                                                                                                                                                |       |                           |                                                                                                                                                                                                                               |                           |

Figure 21: Distilled Image Visualization: CIFAR10 dataset with IPC 10.

<span id="page-24-0"></span>Image /page/24/Picture/0 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image is a photograph of an object or animal, and most of them have a label at the top indicating what is depicted. The labels include 'plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', and 'truck'. The images appear to be generated by a machine learning model, possibly for a dataset visualization or a generative model demonstration. The overall arrangement suggests a classification or generation task where different categories are presented.

Figure 22: Distilled Image Visualization: CIFAR10 dataset with IPC 50.

<span id="page-25-0"></span>Image /page/25/Picture/0 description: The image is a large grid of small images, each labeled with a word. The grid appears to be a visualization of a dataset, likely for machine learning, with each small image representing an instance of a class. The labels are varied and include common objects and animals such as 'apple', 'bear', 'beetle', 'bottle', 'boy', 'bridge', 'bus', 'butterfly', 'camel', 'car', 'castle', 'cat', 'chair', 'chimpanzee', 'clock', 'cloud', 'cockroach', 'cow', 'crab', 'crocodile', 'deer', 'dog', 'dolphin', 'duck', 'elephant', 'fish', 'flower', 'fox', 'frog', 'girl', 'grass', 'hamster', 'horse', 'house', 'kangaroo', 'keyboard', 'lamp', 'lion', 'lobster', 'lorry', 'machine\_learning', 'man', 'maple\_tree', 'monkey', 'motorcycle', 'mouse', 'mushroom', 'oak\_tree', 'ocean', 'oil\_tanker', 'orange', 'orchid', 'otter', 'palm\_tree', 'pear', 'people', 'peguin', 'phoebe', 'pig', 'pineapple', 'piranha', 'plate', 'plough', 'possum', 'rabbit', 'raccoon', 'ray', 'road', 'rocket', 'rose', 'sea', 'seal', 'shark', 'shrew', 'skunk', 'skyscraper', 'snake', 'spider', 'squirrel', 'streetcar', 'sunflower', 'sweet\_pepper', 'sweet\_pepper\_sweet\_pepper', 'sweet\_pepper\_sweet\_pepper\_sweet\_pepper', 'table', 'tank', 'telephone', 'television', 'tiger', 'tractor', 'train', 'trout', 'tulip', 'turtle', 'wardrobe', 'whale', 'willow\_tree', 'wolf', 'woman', 'worm'. The overall arrangement suggests a systematic display of image data and their corresponding classifications.

Figure 23: Distilled Image Visualization: CIFAR100 dataset with IPC 10.

<span id="page-26-0"></span>Image /page/26/Picture/0 description: This is a large grid of images, likely from a dataset, with each image labeled with a class name. The grid appears to be organized into rows and columns, with each cell containing a single image and its corresponding text label. The overall impression is a comprehensive visualization of image classes, possibly for a machine learning or computer vision context. The labels are varied, suggesting a diverse dataset with many different categories of objects or scenes.

Figure 24: Distilled Image Visualization: CIFAR100 dataset with IPC 50 (10 randomly selected images for each class).

<span id="page-27-0"></span>Image /page/27/Picture/0 description: The image is a grid of 100 smaller images, each with a label at the top. The grid is arranged in 10 rows and 10 columns. The smaller images appear to be generated by a machine learning model, possibly a generative adversarial network (GAN), as they are abstract and varied. The labels are diverse, ranging from animals like 'goldfish', 'black stork', and 'cougar' to objects like 'bullet train', 'candle', and 'computer keyboard', and even abstract concepts or places like 'academic gown', 'barbershop', and 'cliff dwelling'. The overall impression is a visualization of a model's ability to generate diverse images based on textual prompts.

Figure 25: Distilled Image Visualization: TinyImageNet dataset with IPC 1.

<span id="page-28-0"></span>Image /page/28/Picture/0 description: This is a grid of many small images, each with a label below it. The overall image is a visualization of a dataset, likely for machine learning, showing distilled images from the ImageNet dataset with IPC-1. The grid is densely packed, with hundreds of individual image-label pairs arranged in rows and columns. The individual images are diverse, showing various objects, animals, and scenes, though they appear somewhat abstract or stylized due to the distillation process. The labels are small text strings identifying the content of each image.

Figure 26: Distilled Image Visualization: ImageNet-1K dataset with IPC 1.

<span id="page-29-0"></span>Image /page/29/Picture/0 description: This is a grid of 70 images, arranged in 7 rows and 10 columns. Each image is a small, square representation of a different object or scene. The objects are categorized and labeled below each image. The categories include Tench, English Springer, Cassette Player, Chainsaw, Church, French Horn, Garbage Truck, Gas Pump, Golf Ball, and Parachute. The images appear to be generated or stylized, with some showing clear representations of the objects and others being more abstract or distorted. The overall presentation suggests a visualization of a dataset or a generative model's output.

Figure 27: Distilled Image Visualization: ImageNette dataset with IPC 10.

<span id="page-30-0"></span>Image /page/30/Picture/0 description: This is a grid of images of dogs, with each row representing a different breed. The breeds shown are Australian Terrier, Border Terrier, Samoyed, Beagle, Shih-Tzu, English Foxhound, Rhodesian Ridgeback, Dingo, Golden Retriever, and English Sheepdog. Each breed has multiple images, and some of the images are distorted or abstract, suggesting they are generated or processed images. The labels for each breed are clearly visible at the top of each column.

Figure 28: Distilled Image Visualization: ImageWoof dataset with IPC 10.

<span id="page-31-0"></span>Image /page/31/Picture/0 description: This image displays a grid of 70 smaller images, each featuring a different bird species. The birds are categorized into rows, with each row dedicated to a specific species. The species include ostriches, bald eagles, peacocks, macaws, cockatoos, toucans, black swans, flamingos, pelicans, and king penguins. Each image within the grid is labeled with the corresponding bird's name. Some images are clear and show the bird in detail, while others are more abstract or distorted, suggesting they might be generated or processed images.

Figure 29: Distilled Image Visualization: ImageSquawk dataset with IPC 10.
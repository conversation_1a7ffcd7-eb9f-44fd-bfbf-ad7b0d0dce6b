# <span id="page-0-0"></span>Diffusion-Augmented Coreset Expansion for Scalable Dataset Distillation

<PERSON><sup>1\*</sup>, <PERSON><PERSON><sup>2†</sup>, <PERSON><PERSON><sup>3</sup>, <PERSON><PERSON><PERSON><sup>2</sup>,

<PERSON><PERSON><PERSON><sup>2</sup>, <PERSON><sup>2†</sup>, <PERSON><PERSON><sup>4</sup>, <PERSON><PERSON><PERSON><sup>2‡</sup>, <PERSON><PERSON><PERSON><sup>1‡</sup>

Vanderbilt University<sup>1</sup> Microsoft Research<sup>2</sup> University of California, San Diego<sup>3</sup> University of California, Davis<sup>4</sup>

> {ali.abbasi, soheil.kolouri}@vanderbilt.edu <EMAIL>

{gmahalingam, hshrivastava, pramod.sharma}@microsoft.com <EMAIL> , <EMAIL>

### Abstract

*With the rapid scaling of neural networks, data storage and communication demands have intensified. Dataset distillation has emerged as a promising solution, condensing information from extensive datasets into a compact set of synthetic samples by solving a bilevel optimization problem. However, current methods face challenges in computational efficiency, particularly with high-resolution data and complex architectures. Recently, knowledge-distillation-based dataset condensation approaches have made this process more computationally feasible. Yet, with the recent developments of generative foundation models, there is now an opportunity to achieve even greater compression, enhance the quality of distilled data, and introduce valuable diversity into the data representation. In this work, we propose a two-stage solution. First, we compress the dataset by selecting only the most informative patches to form a coreset. Next, we leverage a generative foundation model to dynamically expand this compressed set in real-time—enhancing the resolution of these patches and introducing controlled variability to the coreset. Our extensive experiments demonstrate the robustness and efficiency of our approach across a range of dataset distillation benchmarks. We demonstrate a significant improvement of over 10% compared to the state-of-the-art on several large-scale dataset distillation benchmarks. The code will be released soon.*

## 1. Introduction

With the rapid advancement of deep learning, the scale of neural networks and the datasets required to train them have expanded dramatically, introducing significant computational challenges. One promising approach to mitigate these demands is to explore the potential of "small data," a research direction introduced by Wang et al. [\[34\]](#page-9-0) and known as dataset distillation. Dataset distillation focuses on synthesizing a compact yet highly informative dataset from the original large-scale data, allowing models trained on this smaller set to achieve performance comparable to those trained on the full dataset [\[37\]](#page-9-1). By reducing training overhead, storage, and communication requirements while preserving the essential knowledge of the larger dataset, dataset distillation offers transformative potential across multiple areas of machine learning research. Its impact is particularly significant in applications that 1) require repeated training over large datasets, as seen in neural architecture search [\[27\]](#page-9-2), 2) depend on constrained memory storage, such as memory replay in continual learning  $[21]$ , and 3) involve knowledge sharing and communication across distributed machine learning agents, such as in federated learning [\[35\]](#page-9-3).

Dataset distillation is classically formulated as a bilevel optimization problem, where the inner loop trains a model on a synthesized dataset, and the outer loop adjusts this synthetic dataset to maximize the model's performance on the original large-scale dataset. However, this approach presents two significant challenges: 1) it is computationally and memory intensive, as the outer optimization requires backpropagation through the entire unrolled computation graph of the model's training process in the inner loop; and 2) it often leads to synthetic images with spurious, non-realistic features due to overfitting to the specific architecture used during optimization, which limits generalization across architectures [\[2\]](#page-8-1). To address the former challenge and scale up computation, researchers have proposed methods such as gradient matching [\[41\]](#page-9-4), which aligns the gradients of syn-

<sup>\*</sup> Work has been partly done during an internship at Microsoft Research

<sup>†</sup> Contributed to the project while working at Microsoft Research

<sup>‡</sup> Equal contribution

<span id="page-1-0"></span>thetic and original data to improve scalability, and training trajectory matching [\[1\]](#page-8-2), which matches training trajectories between models trained on synthetic and original datasets to enhance distillation efficiency. To address the latter problem, recent studies emphasize the importance of realism in distilled data for achieving cross-architecture generalizability [\[2,](#page-8-1) [24,](#page-8-3) [28,](#page-9-5) [36\]](#page-9-6), showing that incorporating generative priors and enhancing the diversity and realism of synthetic datasets can significantly improve the generalization capabilities of models trained on distilled data.

Recently, Sun et al. [\[28\]](#page-9-5) introduced the Realistic, Diverse, and Efficient Dataset Distillation (RDED) method, an optimization-free approach that achieves high-resolution and large-scale image dataset distillation by emphasizing the realism and diversity of the distilled images. RDED selects a diverse set of informative patches directly cropped from the original data and combines these patches into new images to form the synthetic dataset. To guide this selection, RDED uses a "teacher" model trained on the large-scale dataset to identify informative patches and assigns a soft label for each patch. A "student" model is then trained on these informative patches along with their corresponding soft labels, effectively employing knowledge distillation for dataset distillation.

In RDED, we observe a trade-off between patch diversity and realism for a fixed compression budget, i.e., images per class (IPC). Increasing diversity requires reducing the size of patches to fit more patches within the limited pixel space. However, decreasing patch sizes also reduces their realism, as downsampling acts as a low-pass filter, causing a loss of fine-grained details. In this context, we pose two questions: 1) Is it possible to pack more patches into a finite pixel space without sacrificing realism? and 2) Can we enhance diversity within a fixed number of patches? Following the RDED framework [\[28\]](#page-9-5), to increase the number of patches without sacrificing realism, super-resolution techniques can be used to enhance low-resolution patches back to high-resolution quality. Additionally, diversity within a fixed number of patches can be achieved through realistic augmentations that preserve the natural image manifold. We show that modern Latent Diffusion Models (LDMs) provide both these capabilities, enabling high-quality super-resolution and naturalistic diversity enhancements. We demonstrate that enhancing the realism and diversity of the distilled dataset using LDMs results in a significant performance boost across various dataset distillation benchmarks.

As LDMs become faster and more accessible [\[20\]](#page-8-4), the latency and computational costs of using them in dataset distillation are decreasing, enabling on-the-fly image augmentation. This trend lowers the barrier to incorporating LDMs, making their use more practical. Moreover, as LDMs become increasingly common in machine learning workflows, it's reasonable to expect that the student model could also leverage a diffusion model, ensuring consistent processing in teacher-student setups. This alignment makes LDMenhanced distillation methods more feasible and appealing as LDMs continue to improve in efficiency and accessibility.

Our experiments across multiple datasets and model architectures demonstrate that distilling a dataset into a small set of images—such as one image per category—and training a student model on this distilled set results in higher accuracy than state-of-the-art dataset distillation methods. For example, on the ImageNette [\[13\]](#page-8-5) dataset using a ResNet-18 architecture, our approach achieves 51.4% accuracy, while RDED [\[28\]](#page-9-5), a recent comparable baseline, reaches only 35.8% accuracy.

Our specific contributions in this paper are as follows:

- 1. Using fast latent diffusion models (LDM) for on the fly coreset expansion in dataset distillation.
- 2. Employing knowledge distillation with generative models for dataset distillation.
- 3. Significantly advancing state-of-the-art performance in large-scale dataset distillation.

### 2. Related Works

Since its introduction by Wang et al. [\[34\]](#page-9-0), numerous variations of dataset distillation have been developed. Below, we review some of these methods as well as other related works to our proposed framework.

Bi-level optimization provides a natural framework for formalizing dataset distillation [\[34\]](#page-9-0). However, as previously mentioned, this approach is generally intractable due to the significant computational and memory demands required to backpropagate through the unrolled computational graph of the inner model optimization. Various methods have been proposed to ameliorate this issue by introducing surrogate objective functions for the outer optimization. These include methods that match gradients [\[15,](#page-8-6) [41\]](#page-9-4), extracted features [\[33\]](#page-9-7) and their distributions [\[40\]](#page-9-8), and training trajectories [\[1,](#page-8-2) [7\]](#page-8-7).

Core-set selection offers a natural approach to dataset distillation by selecting the most "valuable" subset of the training data rather than synthesizing a condensed version. Core-setbased methods vary primarily in the difficulty-based metrics they use to evaluate sample importance. For example, sample scores such as Gradient Normed (GraNd) and Error L2 Norm (EL2N) were introduced in [\[19\]](#page-8-8) to guide core-set selection. Meanwhile, the forgetting score, initially proposed in [\[29\]](#page-9-9), has recently been employed to perform dataset distillation progressively [\[3\]](#page-8-9). Recent works extend the concept of core-set selection by selecting a subset of pixels, tokens, or patches within chosen images, achieving further dataset compression [\[28,](#page-9-5) [43\]](#page-9-10). For instance, Sun et al. [\[28\]](#page-9-5) select important image patches identified by a teacher model, while [\[43\]](#page-9-10) use a subset of image tokens or patches and apply Masked Auto-Encoders [\[11\]](#page-8-10) to reconstruct the missing patches, re-

<span id="page-2-1"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: This figure illustrates a method for training a student model using a teacher model and a latent diffusion model. The process is divided into three stages. Stage 1 shows the teacher model being trained on a dataset of images, including a Shih Tzu and two dogs in a park. Stage 2 demonstrates the creation of a coreset by cropping important patches from images, such as a Yorkshire Terrier and a Samoyed dog, and feeding them to the teacher model to obtain ground truth labels. Stage 3 details the generation of high-quality variations of Samoyed dogs using a latent diffusion model with noise corruption and random seeds. These variations are then fed to the teacher model to produce softmax outputs, which are ultimately used for training the student model. The coreset consists of four images: a Yorkshire Terrier, a Samoyed dog, a Shih Tzu, and a dog in a park.

Figure 1. Proposed framework illustration: starting with an image dataset  $D$ , a teacher model is trained on the image-label pairs. Leveraging the uncertainty signal from the teacher's logits, following [\[28\]](#page-9-5), we identify the most important patch from each image to form a coreset. These patches are then upsampled, noise-corrupted using fixed random seeds, and processed through a multi-step diffusion model to achieve simultaneous super-resolution and introduce variations to the coreset. For each random seed and generated high-resolution image, the teacher's soft label is obtained. The student then uses these important patches and random seeds to recreate the high-resolution images and regress over the teacher's corresponding soft labels. Note that, similar to traditional geometric augmentation techniques, this super-resolution and augmentation process is performed on the fly and discarded once the student's gradient is computed.

sulting in dataset distillation conditioned on a generative model. In our work, we use important patches similar to [\[28\]](#page-9-5) but leverage generative modeling, and more precisely LDMs, to increase diversity and maximize compression, similar to [\[43\]](#page-9-10).

Realism priors have been explored in several studies to enhance the realism of distilled data. For example, Cazenavette et al. [\[1\]](#page-8-2) show that overfitting to a specific architecture often stems from optimizing in the pixel space, which reduces realism. They address this by using generative models—specifically, Generative Adversarial Networks (GANs), to perform optimization in the latent space, producing a more realistic distilled dataset leading to better cross-architecture generalization. Other approaches use a trained teacher model and align the feature statistics of synthetic distilled data with those of a larger dataset [\[24,](#page-8-3) [36\]](#page-9-6), which implicitly improves image realism. In our work, we leverage generative priors to enhance both the realism and diversity of the distilled dataset.

Diffusion models have recently gained prominence as powerful tools for data augmentation and generation across various learning tasks [\[14,](#page-8-11) [22,](#page-8-12) [25,](#page-8-13) [31,](#page-9-11) [39\]](#page-9-12). For example, Zhang et al. [\[39\]](#page-9-12) combine diffusion models with MAEs to expand small-scale datasets by generating new, informative, and diverse images, effectively creating realism-aware augmen-

tations of limited datasets. DiffuseMix [\[14\]](#page-8-11) utilizes diffusion models and introduces a unique approach that blends real and generated images, producing hybrid augmentations. However, due to the slow generation speed of diffusion models, these augmentations are often pre-generated and cached, leading to high memory demands. With growing interest in diffusion models and advances in fast sampling techniques, such as SDXL-Turbo [\[23\]](#page-8-14), it is now feasible to generate on-the-fly augmentations during model training. In our work, we leverage SDXL-Turbo to super-resolve and augment our mined, important patches.

## 3. Method

Our proposed method for dataset distillation comprises three main steps. In Step 1, a teacher model is trained on the full training dataset. In Step 2, a compact coreset of important image patches is selected. In Step 3, the selected image patches are first upsampled and then noise-corrupted using different fixed random seeds. Each seed generates a distinct highquality variation of the low-resolution patch using a Latent Diffusion Model (LDM). Multiple such variations are generated for each patch using different seeds. The high-quality images are then processed by the teacher model to obtain the softmax outputs of its classifier. Finally, the low-quality important patches, random seeds, and their corresponding

<span id="page-3-1"></span>soft labels are transferred to the student model. Figure [1](#page-2-0) demonstrates these steps.

Upon receiving the distilled data from the teacher, the student replicates the upsampling, noise corruption, and LDM denoising steps using the provided low-quality patches and random seeds to generate the same high-quality variations as produced by the teacher. The student then trains on the teacher's soft labels for the generated images.

### 3.1. Coreset Selection

We follow the methodology of [\[28\]](#page-9-5) for forming the coreset of important patches. Given  $\mathcal{D} = \{(x_i, y_i)\}_{i=1}^N$ , where  $x_i \in \mathbb{R}^{H \times W \times 3}$  are the images and  $y_i \in \mathbb{R}^K$  are the corresponding class labels, we first train a teacher model  $f_{\theta} : \mathbb{R}^{H \times W \times 3} \rightarrow$  $\mathbb{R}^K$  parameterized by  $\theta$  on  $\mathcal{D}$ . Then, for a given image  $x_i$ from the dataset, we generate  $P$  random crops and resize them to be  $\lfloor \frac{H}{r} \rfloor \times \lfloor \frac{W}{r} \rfloor$ , where  $r > 1$  is a scalar indicating the patch-to-image compression ratio. Denoting  $x_i^j$  as the *j*-th random patch from  $x_i$ , we deliberately use a smaller patch size compared to the full dimensions of  $x_i$  to compress the information, i.e.,  $x_i^j \in \mathbb{R}^{\lfloor H/r \rfloor \times \lfloor W/r \rfloor \times 3}$ . To construct our coreset, we first select the most informative patch from each image. This is achieved by choosing the patch  $x_i^j$  that minimizes the cross-entropy loss:

$$
x_i^* = \underset{j}{\text{arg min }} \text{CE}(f_{\theta}(x_i^j), y_i), \tag{1}
$$

where  $j = 1, 2, \dots, P$ , and  $CE(\cdot, \cdot)$  denotes the cross entropy loss. Next, to create the coreset under a fixed Images Per Class (IPC) memory budget, we form the coreset by choosing  $P$  patches with the lowest cross entropy loss. At the end of this step, for each class  $c$ , we will have a total of  $IPC \times r^2$  patches, satisfying the memory constraint. Figure [2](#page-3-0) shows the selected coreset for the ImageNette dataset [\[13\]](#page-8-5) for IPC=1. We note that increasing  $r$  allows us to store a greater number of important patches for a fixed IPC budget, thereby enhancing diversity. However, this comes at the cost of reduced realism due to the loss in resolution. Finally, we acknowledge that selecting patches based on minimum cross-entropy does not inherently ensure diversity. However, our results demonstrate that the diffusion model effectively compensates for any potential lack of diversity among the selected important patches. In the next step, we will describe our method for increasing realism despite increasing r.

#### 3.2. Coreset Augmentation and Super Resolution

Data augmentation has long been a cornerstone for introducing variations into training data. In vision applications, simple and efficient geometric transformations such as rotations, flips, and noise additions are commonly used to artificially increase the dataset size. These augmentations are computationally inexpensive and can be performed on the fly. With recent advancements in Latent Diffusion Models (LDMs)

<span id="page-3-0"></span>Image /page/3/Picture/8 description: This is a collage of 30 images arranged in a 5x6 grid. The images depict a variety of subjects including people fishing, dogs, chainsaws, buildings, musical instruments (French horns), trucks, gas pumps, golf balls, and people skydiving. The images are diverse and cover a range of activities and objects.

Figure 2. The extracted coreset for IPC=1 from ImageNette.

[\[23\]](#page-8-14), the development of few-step LDMs has significantly reduced the sampling latency traditionally associated with diffusion models. In this work, we leverage this advancement to perform realistic augmentations on important patches on the fly. Realistic variations are generated dynamically during the student model's batch processing and discarded once the batch is processed, ensuring compliance with memory storage constraints on the client side.

### 3.2.1. Diffusion Preliminary

Latent Diffusion Models (LDMs) consist of an autoencoder and a UNet denoiser. The autoencoder is an encoder-decoder architecture that is initially trained separately to map the image from pixel space to a lower-dimensional latent space and then reconstruct it back to the original image space with minimal reconstruction error. Once the autoencoder is trained, all noise corruptions and denoising are performed in the latent space. Let  $z_0 = \text{Encode}(x)$  be the latent representation of image x, and let  $t \in \{0, 1, 2, \dots, T\}$  represent an arbitrary noising step. For training an LDM, t is uniformly sampled from the set of steps, and Gaussian noise is added to  $z_0$  in proportion to  $t$ . The noisy latent representation is given by:

$$
z_t = \sqrt{\bar{\alpha}_t} z_0 + \sqrt{1 - \bar{\alpha}_t} \epsilon,
$$

where  $\sqrt{\bar{\alpha}_t}$  is the data-noise interpolation coefficient and  $\epsilon \sim \mathcal{N}(0, I)$ . The denoiser, denoted by  $\epsilon_{\theta}$ , is then trained by minimizing the following loss function:

$$
\mathcal{L}_{ldm} = \|\epsilon_{\theta}(z_t, t, c) - \epsilon\|^2
$$

Here,  $c$  is the conditioning vector. In the case of a text-toimage diffusion model,  $c$  is the output of a text encoder that predicts the textual embedding of the image caption.

#### 3.2.2. Using real Data as Anchors

Several works in the literature have investigated the potential of synthetic images for training downstream classifiers [\[8,](#page-8-15) [12,](#page-8-16) [30\]](#page-9-13). A consistent finding across these studies is that synthetic images can improve classifier performance when augmented with real data. However, even state-of-the-art synthetic images exhibit a slight distribution shift when not anchored to real images [\[8,](#page-8-15) [22\]](#page-8-12). Consequently, real data

<span id="page-4-1"></span><span id="page-4-0"></span>Image /page/4/Figure/0 description: The image displays a series of images of a Shih Tzu dog, illustrating a latent interpolation process. The top row shows labels: "x̂₁\*", "x̅₁\*", "Latent Interpolation", "x̅₂\*", and "x̂₂\*". The bottom row shows corresponding images with labels: "Real Patch", "γ = 0", "γ = 0.25", "γ = 0.5", "γ = 0.75", "γ = 1", and "Real Patch". The images show a progression from a close-up of the dog's face on the left to a full-body shot in a grassy, outdoor setting on the right, with intermediate images showing gradual changes in the dog's appearance and background.

Figure 3. Performing mixup in the latent space of the LDM's autoencoder.

and synthetic samples augmented from real data tend to outperform purely synthetic samples.

In this work, we propose to use the low-resolution patches stored during the coreset selection step as anchors on the manifold of training distribution. Let  $x_i^* \in \mathbb{R}^{\lfloor H/r \rfloor \times \lfloor W/r \rfloor \times 3}$ be the  $i<sup>th</sup>$  low-resolution patch subsampled in the previous step. We propose to first upsample the patch using 2D interpolation to the original image dimensions:

$$
\hat{x}_i^* = \text{INTERP}(x_i^*),
$$

where  $\hat{x}_i^* \in \mathbb{R}^{H \times W \times 3}$ . The upsampled patch matches the original image dimensions in  $D$  but still retains low quality. We propose using this low-quality image as an anchor for the LDM. Let  $\hat{z}_i^*$  be the latent code of the upsampled patch. We first add a small amount of noise to  $\hat{z}_i^*$ :

$$
\hat{z}_{i,t'}^* = \sqrt{\bar{\alpha}_{t'}} \hat{z}_i^* + \sqrt{1 - \bar{\alpha}_{t'}} \epsilon,
$$
\n(2)

where  $t' \in \{1, 2, \ldots, T\}$ . We define  $\rho$  as  $\frac{t'}{T}$  $\frac{t'}{T} \in [0,1]$  and use it as a hyperparameter that controls the amount of noise. This small noise addition perturbs the data slightly off the training data distribution manifold. We then iteratively denoise the low-quality latent back to the manifold using  $\epsilon_{\theta}$ , following the backward diffusion process:

$$
\hat{z}_{i,t'-1}^* = \frac{1}{\sqrt{\alpha_{t'}}}\left(\hat{z}_{i,t'}^* - \frac{1-\alpha_{t'}}{\sqrt{1-\bar{\alpha}_{t'}}}\epsilon_\theta(\hat{z}_{i,t'}^*,t',c)\right) + \sigma_t\epsilon'
$$

where  $\epsilon' \sim \mathcal{N}(0, I)$ , and  $\bar{\alpha}_{t'} = \prod_{s=1}^{t'} \alpha_s$ . The denoised sample is denoted as  $\bar{z}_i^* = \hat{z}_{i,0}^*$ .

The denoised image  $\bar{x}_i^* = \text{Decode}(\bar{z}_i^*)$  possesses two key properties: 1) since the denoiser is trained on high-resolution images, the denoised image will also be high-resolution, making  $\bar{x}_i^*$  high quality; and 2) as a projection onto the manifold of the training distribution,  $\bar{x}_i^*$  does not necessarily recover the same anchor patch, with the contents of  $\bar{x}_i^*$ varying slightly based on  $c$  and  $\rho$ . Therefore, the final transformation results in a combination of super-resolution and semantic augmentation.

### 3.3. Mixup in Latent Space

Mixup [\[38\]](#page-9-14) has become a widely adopted data augmentation method for training vision models. It encourages local linearity in the model by enforcing that the linear mixture of input images corresponds to the linear mixture of their outputs. This concept was later extended to manifold mixup [\[32\]](#page-9-15), which better aligns with the underlying data manifold. In our approach, we assume that both the student and teacher models have access to an expressive LDM. This allows us to leverage the LDM to perform mixup operations in the latent space, effectively implementing manifold mixup, to further augment the student's limited set of samples. Let  $\hat{z}_1^*$  represent the latent code of an upsampled patch in the training data belonging to class  $k$ . To augment this patch, we randomly sample another data point  $\hat{z}_2^*$  from the same class and perform linear interpolation in the latent space with a mixing parameter  $\gamma$ , defined as  $\hat{z}_{\text{interp}} = \gamma \hat{z}_1^* + (1 - \gamma) \hat{z}_2^*$ . The interpolated latent code is then used for augmentation via the LDM. Figure [3](#page-4-0) presents qualitative results of the augmented samples generated using mixup. In our ablation study, we highlight the performance improvements achieved by mixing latent codes.

#### 3.4. Putting it all together

For each IPC, we extract  $r^2$  low-resolution patches from our coreset selection. For each patch, we generate  $m$  highquality augmentations using the diffusion model and pass them through the teacher model to obtain soft labels, resulting in a total of  $m \times r^2$  soft labels. In all our experiments, we set  $m$  equal to the number of training epochs for the student. Notably, regenerating the high-quality augmentations requires only a single random seed. Lastly, we emphasize that the storage overhead for soft labels is present in many of the recent methods that combine knowledge distillation and dataset distillation, such as RDED [\[28\]](#page-9-5), SRe2L [\[36\]](#page-9-6), and G-VBSM [\[24\]](#page-8-3).

<span id="page-5-0"></span>

|               |            | <b>MTT</b>     | SRe2L          | G-VBSM         | <b>RDED</b>    | Ours      |
|---------------|------------|----------------|----------------|----------------|----------------|-----------|
|               |            | ConvNet        | ResNet-18      | ResNet-18      | ResNet-18      | ResNet-18 |
|               | $IPC=1$    | $8.8 \pm 0.3$  | $2.62 \pm 0.1$ |                | $9.7 \pm 0.4$  | 19.4      |
| Tiny-ImageNet | $IPC=10$   | $23.2 \pm 0.2$ | $16.1 \pm 0.2$ |                | $41.9 \pm 0.2$ | 46.2      |
|               | $IPC = 50$ | $28.0 \pm 0.3$ | $41.1 \pm 0.4$ | $47.6 \pm 0.3$ | $58.2 \pm 0.1$ | 53.4      |
|               | $IPC=1$    | $28.6 \pm 0.8$ | $13.3 \pm 0.5$ |                | $17.9 \pm 1.0$ | 25.0      |
| ImageWoof     | $IPC=10$   | $35.8 \pm 1.8$ | $20.2 \pm 0.2$ |                | $44.4 \pm 1.8$ | 47.6      |
|               | $IPC = 50$ |                | $23.3 \pm 0.3$ |                | $71.7 \pm 0.3$ | 77.1      |
| ImageNette    | $IPC=1$    | $47.7 \pm 0.9$ | $19.1 \pm 1.1$ |                | $35.8 \pm 1.0$ | 51.4      |
|               | $IPC=10$   | $63.0 \pm 1.3$ | $29.4 \pm 3.0$ |                | $61.4 \pm 0.4$ | 73.6      |
|               | $IPC = 50$ |                | $40.9 \pm 0.3$ |                | $80.4 \pm 0.4$ | 87.6      |
| ImageNet-1k   | $IPC=1$    |                | $0.1 \pm 0.1$  |                | $6.6 \pm 0.2$  | 13.9      |
|               | $IPC=10$   |                | $21.3 \pm 0.6$ | $31.4 \pm 0.5$ | $42.0 \pm 0.1$ | 52.1      |
|               | $IPC = 50$ |                | $46.8 \pm 0.2$ | $51.8 \pm 0.4$ | $56.5 \pm 0.1$ | 54.9      |

Table 1. Results on higher-resolution benchmarks. We compared our method against three knowledge-distillation-based approaches and MTT as a bilevel optimization method. Blank cells for the MTT method indicate its lack of scalability, while for G-VBSM represent results not reported by the authors. The results demonstrate that our approach is either superior or on par with the baselines. For ImageNet-1k, ImageWoof, and ImageNette, we used  $112 \times 112$  patches, while Tiny-ImageNet experiments utilized  $32 \times 32$  patches. In all experiments, the teacher and student models share the same architecture. Cross-architectural analysis results are detailed in the ablation studies.

## 4. Experiments

We evaluated our method against both knowledgedistillation-based and bilevel-optimization-based approaches across several high-resolution benchmarks:

- 1. Tiny-ImageNet [\[18\]](#page-8-17): This dataset includes 200 classes of  $64 \times 64$  images derived from the original ImageNet-1k dataset. For our experiments, we selected patches at a resolution of  $32 \times 32$ .
- 2. ImageWoof and ImageNette [\[13\]](#page-8-5): These datasets are 10 class subsets of ImageNet-1k, with an original resolution of  $224 \times 224$ . ImageWoof focuses on different dog breeds, while Imagenette covers a broad array of categories spanning animals and objects. To ensure comparability with RDED, we used a patch size of  $112 \times 112$ .
- 3. ImageNet-1k [\[5\]](#page-8-18): A comprehensive dataset containing 1000 classes of  $224 \times 224$  images representing a diverse set of categories. Consistent with [\[28\]](#page-9-5), we employed patches of  $112 \times 112$ .

While our method demonstrates its core strength on highresolution benchmarks that benefit from super-resolution capabilities, we also benchmarked on CIFAR-10 and CIFAR-100 [\[16\]](#page-8-19) to address the challenges bilevel methods face with high-resolution images and widely used ResNet architectures. For these datasets, we used  $16 \times 16$  patches.

The following baseline methods, including both bileveloptimization-based and knowledge-distillation-based approaches, were used for evaluation:

- 1. RDED [\[28\]](#page-9-5): The first method that synthesizes collages of important patches, selected based on the teacher model's cross-entropy loss.
- 2. SRe2L [\[36\]](#page-9-6): Leverages batch norm statistics of the

teacher model to perform model inversion, facilitating the synthesis of diverse samples.

- 3. G-VBSM [\[24\]](#page-8-3): Extending [\[36\]](#page-9-6), this approach leverages rich statistical information from batch norm layers of multiple pretrained teachers to synthesize data.
- 4. MTT [\[1\]](#page-8-2): The first approach to define the objective of outer-level optimization by matching the training trajectory of the student model to the expert's.
- 5. IDM [\[42\]](#page-9-16): Proposes efficient data distillation through distribution matching between synthetic and real data.
- 6. Tesla [\[4\]](#page-8-20): Simplifies gradient calculations for trajectorymatching-based methods, enhancing the computational efficiency of [\[1\]](#page-8-2).
- 7. DATM [\[9\]](#page-8-21): This work improves the trajectory matching methods and aligns the complexity of generated patterns to the dataset's size.

Implementation Details: We conducted all experiments using the float16 variant of the SDXL-Turbo diffusion model, while setting num inference steps=5. For most experiments, we employed the AdamW optimizer with a learning rate of 0.001 and a weight decay of 0.01, training for 300 epochs. Detailed descriptions of each experimental setup and the corresponding hyperparameters are provided in the supplementary material.

### 4.1. Effect of Patch Size

RDED [\[28\]](#page-9-5) proposes compressing visual information by utilizing patches smaller than the original image dimensions. To explore the performance dynamics associated with varying patch sizes under a fixed memory budget, we conducted a study where, limited to storing one  $224 \times 224$  image per class, we evaluated performance as patch sizes decreased

<span id="page-6-2"></span>

|          |        | Tesla   | MTT     | IDM     | DATM    | G-VBSM  | RDED    | Ours    | SRe2L    | G-VBSM   | RDED     | Ours     |
|----------|--------|---------|---------|---------|---------|---------|---------|---------|----------|----------|----------|----------|
|          |        | ConvNet | ConvNet | ConvNet | ConvNet | ConvNet | ConvNet | ConvNet | ResNet18 | ResNet18 | ResNet18 | ResNet18 |
| CIFAR10  | IPC=1  | 48.5    | 46.3    | 45.6    | 46.9    | -       | 23.5    | 38.2    | 16.6     | -        | 22.9     | 31.0     |
|          | IPC=10 | 66.4    | 65.3    | 58.6    | 66.8    | 46.5    | 50.2    | 64.56   | 29.3     | 53.5     | 37.1     | 47.7     |
|          | IPC=50 | 72.6    | 71.6    | 67.5    | 76.1    | 54.3    | 68.4    | 73.9    | 45.0     | 59.2     | 62.1     | 70.4     |
| CIFAR100 | IPC=1  | 24.8    | 24.3    | 20.1    | 27.9    | 16.4    | 19.6    | 42.63   | 6.6      | 25.9     | 11.0     | 31.2     |
|          | IPC=10 | 41.7    | 40.1    | 45.1    | 47.2    | 38.7    | 48.1    | 49.0    | 31.6     | 59.5     | 42.6     | 57.7     |
|          | IPC=50 | 47.9    | 47.7    | 50.0    | 55.0    | 45.7    | 57.0    | 53.5    | 50.2     | 65.0     | 62.6     | 62.2     |

Table 2. Results on CIFAR-10 and CIFAR-100 datasets. We evaluated our method against various bilevel-optimization and knowledgedistillation-based approaches using ResNet-18 and ConvNet-3 as the student/teacher architectures. For these low-resolution benchmarks, we stored and communicated  $16 \times 16$  patches prior to super-resolution and augmentation. The results show that our method performs comparably to or better than the knowledge-distillation-based approaches. In all experiments, the student and teacher architectures were identical.

and their number increased. In the  $IPC = 1$  scenario, we stored  $r^2$  patches of size  $\frac{H}{r} \times \frac{W}{r}$ , with r ranging from 2 to 8. For this analysis, we selected ImageWoof and Imagenette as benchmarks due to their differing classification dynamics: ImageWoof consists of 10 dog breeds with high inter-class similarity, while Imagenette includes 10 widely diverse categories with minimal inter-class relationship.

Figure. [5](#page-7-0) illustrates that in RDED, increasing the number of patches enhances diversity but reduces realism, revealing an optimal performance point at  $r = 4$ . In contrast, our method demonstrates a different trend: we consistently outperform RDED at all values of  $r$ , and our performance continues to improve as the patch count increases. This is achieved through our use of a diffusion model for superresolution, which not only preserves realism but also enhances diversity by introducing variations to the data, complementing the diversity gains from the increased patch count. However, this improved performance comes at the cost of additional diffusion calls, leading to increased training time. Table [3](#page-6-0) presents the average elapsed time per training epoch across various patch sizes.

<span id="page-6-0"></span>

|                     | sz112 | sz74 | sz56 | sz44 | sz32  | sz28  |
|---------------------|-------|------|------|------|-------|-------|
| Epoch time<br>(sec) | 2.53  | 2.76 | 6.39 | 8.23 | 16.58 | 22.49 |

Table 3. Keeping IPC=1, one can increase  $r$  to reduce the patch size and increase number of patches. We show the training time for each epoch in seconds on four RTX A6000 GPUs while varying the patch size.

#### 4.2. Effect of Super-resolution and Augmentation

By denoising the latent code of a patch after adding partial noise, both content variation and super-resolution are achieved in the recovered image. In this study, we aim to disentangle these two operations to analyze their individual impact on accuracy. To simulate super-resolution with minimal augmentation, we set the ratio  $\rho = \frac{t'}{T} = 0.4$ , which we qualitatively observe to achieve super-resolution while

 $(\rho = 0.8)$ Superres+Aug+ 25.0 51.4

Superres  $(\rho = 0.5)$ 

Superres+Aug

Mixup ( $\rho = 0.8$ )

<span id="page-6-1"></span>and latent mixup are incorporated during training.

Table 4. Ablation study to highlight the contributions of individual components in our framework and to separate the effects of superresolution from augmentation. The experiments were performed on the ImageWoof and ImageNette datasets, with IPC=1 and using  $112 \times 112$  patches. The student is a ResNet-18 model.

RDED | 17.9 | 35.8 Only text cond. 19.6 35.1

preserving the original content. Conversely, at  $\rho = 0.8$ , additional variation is also introduced into the patches. Figure. [4](#page-7-1) presents qualitative results from this disentanglement.

In Table [4,](#page-6-1) we illustrate the contribution of various components to overall performance. In "RDED," training is conducted exclusively on real patches. "Only text cond." refers to training on synthetic data using only the textual prompt, "A photo of *category name*," without storing real patches. Setting  $\rho = 0.4$  generates high-quality samples with minimal augmentation, while  $\rho = 0.8$  enables both augmentation and super-resolution. The results indicate that the best performance is achieved when super-resolution, augmentation,

ImageWoof ImageNette

19.5 39.5

21.6 47.7

#### 4.3. Cross-architectural Analysis

Bilevel-optimization-based methods often struggle with poor cross-architectural transferability, meaning the performance of a student model significantly degrades when its architecture differs from that of the expert model used for dataset distillation. To address this, GLaD [\[2\]](#page-8-1) leverages the prior of a generative model to synthesize more realistic samples.

<span id="page-7-4"></span><span id="page-7-1"></span>Image /page/7/Picture/0 description: This image displays a comparison of image super-resolution techniques applied to two different dog breeds. The top row shows a Samoyed, and the bottom row shows a Shih Tzu. Each row presents three images: "Low-quality patch," "Mostly Super-resolution," and "Super-resolution + Augmentation." Below the bottom row, the images are labeled with "ρ = 0.0," "ρ = 0.4," and "ρ = 0.8," indicating different levels of a parameter, likely related to the augmentation or resolution process. The "Acc" label on the right suggests that accuracy is being measured or compared across these different methods.

Figure 4. Qualitative illustration of the generated samples. By adjusting  $\rho$ , we can control the level of augmentation, allowing us to effectively distinguish between the contributions of superresolution and augmentation.

Cazenavette et al. [\[2\]](#page-8-1) demonstrate that this enhanced realism substantially improves the cross-architectural performance of distilled datasets. Since our method also utilizes the prior of a generative model, we compare its cross-architectural capabilities with those of GLaD in both high-resolution and low-resolution settings.

In Table [5,](#page-7-2) we report results using a ConvNet model as the expert/teacher in the IPC=1 setting. The values reflect the average performance across four different student architectures: VGG11 [\[26\]](#page-8-22), ViT [\[6\]](#page-8-23), AlexNet [\[17\]](#page-8-24), and ResNet-18 [\[10\]](#page-8-25). Since GLaD does not scale to the full resolution of  $224 \times 224$ , their ImageNette and ImageWoof images were downsampled to  $128 \times 128$ ; we adjusted our setting accordingly to ensure a fair comparison. Table [6](#page-7-3) provides a similar cross-architectural analysis on CIFAR-10. Both tables show that our method achieves superior cross-architecture performance.

## 5. Conclusion

Recent advancements in dataset distillation have underscored the significance of realistic and diverse data representations. Some approaches emphasize the value of realism for generalizability, while others explore the capabilities of generative models to enhance the diversity and quality of distilled datasets. Building on these insights, our proposed method leverages modern Latent Diffusion Models (LDMs) to address both realism and diversity. By combining coreset selection with generative augmentations, we achieve significant improvements in dataset distillation benchmarks, demonstrating the control in the state-of-the-art performance across various datasets. And the state-of-the-art performance as  $\alpha$  is the effectively distinguish between the contribution of super-<br>us to effectively distinguis

<span id="page-7-0"></span>Image /page/7/Figure/6 description: The image contains two line graphs side-by-side, both titled "IPC=1". The left graph is titled "ImageWoof" and the right graph is titled "ImageNette". Both graphs plot "Acc" on the y-axis against "Image-to-Patch Ratio" on the x-axis. The x-axis labels are numerical values from 2 to 8, with corresponding numbers in parentheses below them: (112), (74), (56), (44), (37), (32), (28). Two lines are plotted in each graph, labeled "RDED" (blue) and "Ours" (orange). In the "ImageWoof" graph, the "RDED" line starts at approximately 17, rises to a peak of 25 at a ratio of 4, then decreases to about 23 at a ratio of 8. The "Ours" line starts at 25 and steadily increases to approximately 39 at a ratio of 8. In the "ImageNette" graph, the "RDED" line starts at approximately 35, rises to about 44 at a ratio of 3, then decreases to about 28 at a ratio of 8. The "Ours" line starts at approximately 52 and steadily increases to approximately 72 at a ratio of 8.

Figure 5. Study on the impact of patch size on student performance. In the RDED case, there is a trade-off between realism and diversity: reducing patch dimensions allows for more patches to fit in memory but significantly lowers the quality of downsampled patches without super-resolution. In contrast, our method benefits from increased performance by adding more patches, albeit at the cost of additional computation due to diffusion model calls. The x-axis represents varying  $r$  values, with the numbers in parentheses indicating the corresponding patch sizes  $(\frac{224}{r})$ .

<span id="page-7-2"></span>

|                   | Imagenette | ImageWoof |
|-------------------|------------|-----------|
| <b>MTT [1]</b>    | 24.1       | 16.0      |
| <b>MTT + GLaD</b> | 30.4       | 17.1      |
| <b>DC [41]</b>    | 28.2       | 17.4      |
| <b>DC + GLaD</b>  | 31.0       | 17.8      |
| <b>DM [40]</b>    | 20.6       | 14.5      |
| <b>DM + GLaD</b>  | 21.9       | 15.2      |
| <b>Ours</b>       | 30.79      | 19.74     |

Table 5. Cross-architectural analysis on ImageNette and Image-Woof at a resolution of  $128 \times 128$  with IPC=1. The teacher model is a ConvNet, while the student architectures include VGG11, ViT, ResNet18, and AlexNet. The reported results represent the average performance across these architectures. In this setup,  $64 \times 64$ patches were communicated to the student models within the memory constraints.

<span id="page-7-3"></span>

|                   | <b>AlexNet</b> | ResNet18    | ViT         | Average     |
|-------------------|----------------|-------------|-------------|-------------|
| <b>MTT</b>        | 26.8           | 23.4        | 21.2        | 23.8        |
| <b>MTT + GLaD</b> | 27.9           | 30.2        | 22.7        | 26.9        |
| DC                | 25.9           | 27.3        | 22.9        | 25.4        |
| <b>DC + GLaD</b>  | 26.0           | 27.6        | <b>23.4</b> | 25.7        |
| DM                | 22.9           | 22.2        | 21.3        | 22.1        |
| <b>DM + GLaD</b>  | 25.1           | 22.5        | 23.0        | 23.5        |
| <b>Ours</b>       | <b>31.3</b>    | <b>36.0</b> | 21.9        | <b>29.7</b> |

Table 6. Cross-architectural analysis on the CIFAR-10 dataset using a ConvNet as the teacher and various student architectures. The results demonstrate superior overall cross-architecture performance achieved by our method.

Our experiments validate the effectiveness of high-quality augmentations and mixup operations in the latent space, showcasing the power of LDMs to enhance dataset compression while preserving crucial data attributes.

# References

- <span id="page-8-2"></span>[1] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [2,](#page-1-0) [3,](#page-2-1) [6,](#page-5-0) [8](#page-7-4)
- <span id="page-8-1"></span>[2] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3739–3748, 2023. [1,](#page-0-0) [2,](#page-1-0) [7,](#page-6-2) [8](#page-7-4)
- <span id="page-8-9"></span>[3] Xuxi Chen, Yu Yang, Zhangyang Wang, and Baharan Mirzasoleiman. Data distillation can be like vodka: Distilling more times for better quality. In *The Twelfth International Conference on Learning Representations*, 2024. [2](#page-1-0)
- <span id="page-8-20"></span>[4] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [6](#page-5-0)
- <span id="page-8-18"></span>[5] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [6](#page-5-0)
- <span id="page-8-23"></span>[6] Alexey Dosovitskiy. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020. [8](#page-7-4)
- <span id="page-8-7"></span>[7] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 3749–3758, 2023. [2](#page-1-0)
- <span id="page-8-15"></span>[8] Lijie Fan, Kaifeng Chen, Dilip Krishnan, Dina Katabi, Phillip Isola, and Yonglong Tian. Scaling laws of synthetic images for model training... for now. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7382–7392, 2024. [4](#page-3-1)
- <span id="page-8-21"></span>[9] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023. [6](#page-5-0)
- <span id="page-8-25"></span>[10] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [8](#page-7-4)
- <span id="page-8-10"></span>[11] Kaiming He, Xinlei Chen, Saining Xie, Yanghao Li, Piotr Dollár, and Ross Girshick. Masked autoencoders are scalable vision learners. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 16000– 16009, 2022. [2](#page-1-0)
- <span id="page-8-16"></span>[12] Ruifei He, Shuyang Sun, Xin Yu, Chuhui Xue, Wenqing Zhang, Philip Torr, Song Bai, and Xiaojuan Qi. Is synthetic data from generative models ready for image recognition? *arXiv preprint arXiv:2210.07574*, 2022. [4](#page-3-1)
- <span id="page-8-5"></span>[13] Jeremy Howard and Sylvain Gugger. Imagenette: A smaller subset of 10 easily classified classes from imagenet. [https:](https://github.com/fastai/imagenette)

[//github.com/fastai/imagenette](https://github.com/fastai/imagenette), 2019. Accessed: 2024-11-14. [2,](#page-1-0) [4,](#page-3-1) [6](#page-5-0)

- <span id="page-8-11"></span>[14] Khawar Islam, Muhammad Zaigham Zaheer, Arif Mahmood, and Karthik Nandakumar. Diffusemix: Label-preserving data augmentation with diffusion models. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 27621–27630, 2024. [3](#page-2-1)
- <span id="page-8-6"></span>[15] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022. [2](#page-1-0)
- <span id="page-8-19"></span>[16] Alex Krizhevsky, Vinod Nair, and Geoffrey Hinton. Cifar-10 and cifar-100 datasets. *URl: https://www. cs. toronto. edu/kriz/cifar. html*, 6(1):1, 2009. [6](#page-5-0)
- <span id="page-8-24"></span>[17] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Advances in neural information processing systems*, 25, 2012. [8](#page-7-4)
- <span id="page-8-17"></span>[18] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [6](#page-5-0)
- <span id="page-8-8"></span>[19] Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. *Advances in neural information processing systems*, 34:20596–20607, 2021. [2](#page-1-0)
- <span id="page-8-4"></span>[20] Dustin Podell, Zion English, Kyle Lacey, Andreas Blattmann, Tim Dockhorn, Jonas Muller, Joe Penna, and Robin Rombach. ¨ Sdxl: Improving latent diffusion models for high-resolution image synthesis. *arXiv preprint arXiv:2307.01952*, 2023. [2](#page-1-0)
- <span id="page-8-0"></span>[21] Mattia Sangermano, Antonio Carta, Andrea Cossu, and Davide Bacciu. Sample condensation in online continual learning. In *2022 International Joint Conference on Neural Networks (IJCNN)*, pages 01–08. IEEE, 2022. [1](#page-0-0)
- <span id="page-8-12"></span>[22] Mert Bülent Sarıyıldız, Karteek Alahari, Diane Larlus, and Yannis Kalantidis. Fake it till you make it: Learning transferable representations from synthetic imagenet clones. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 8011–8021, 2023. [3,](#page-2-1) [4](#page-3-1)
- <span id="page-8-14"></span>[23] Axel Sauer, Dominik Lorenz, Andreas Blattmann, and Robin Rombach. Adversarial diffusion distillation. In *European Conference on Computer Vision*, pages 87–103. Springer, 2025. [3,](#page-2-1) [4](#page-3-1)
- <span id="page-8-3"></span>[24] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 16709–16718, 2024. [2,](#page-1-0) [3,](#page-2-1) [5,](#page-4-1) [6](#page-5-0)
- <span id="page-8-13"></span>[25] Jordan Shipard, Arnold Wiliem, Kien Nguyen Thanh, Wei Xiang, and Clinton Fookes. Diversity is definitely needed: Improving model-agnostic zero-shot classification via stable diffusion. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 769–778, 2023. [3](#page-2-1)
- <span id="page-8-22"></span>[26] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [8](#page-7-4)

- <span id="page-9-2"></span>[27] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *International Conference on Machine Learning*, pages 9206–9216. PMLR, 2020. [1](#page-0-0)
- <span id="page-9-5"></span>[28] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 9390–9399, 2024. [2,](#page-1-0) [3,](#page-2-1) [4,](#page-3-1) [5,](#page-4-1) [6](#page-5-0)
- <span id="page-9-9"></span>[29] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. In *International Conference on Learning Representations*. [2](#page-1-0)
- <span id="page-9-13"></span>[30] Brandon Trabucco, Kyle Doherty, Max Gurinas, and Ruslan Salakhutdinov. Effective data augmentation with diffusion models. *arXiv preprint arXiv:2302.07944*, 2023. [4](#page-3-1)
- <span id="page-9-11"></span>[31] Brandon Trabucco, Kyle Doherty, Max A Gurinas, and Ruslan Salakhutdinov. Effective data augmentation with diffusion models. In *The Twelfth International Conference on Learning Representations*, 2024. [3](#page-2-1)
- <span id="page-9-15"></span>[32] Vikas Verma, Alex Lamb, Christopher Beckham, Amir Najafi, Ioannis Mitliagkas, David Lopez-Paz, and Yoshua Bengio. Manifold mixup: Better representations by interpolating hidden states. In *International conference on machine learning*, pages 6438–6447. PMLR, 2019. [5](#page-4-1)
- <span id="page-9-7"></span>[33] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196–12205, 2022. [2](#page-1-0)
- <span id="page-9-0"></span>[34] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-9-3"></span>[35] Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. Feddm: Iterative distribution matching for communication-efficient federated learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 16323–16332, 2023. [1](#page-0-0)
- <span id="page-9-6"></span>[36] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems*, 36, 2024. [2,](#page-1-0) [3,](#page-2-1) [5,](#page-4-1) [6](#page-5-0)
- <span id="page-9-1"></span>[37] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023. [1](#page-0-0)
- <span id="page-9-14"></span>[38] Hongyi Zhang, Moustapha Cisse, Yann N. Dauphin, and David Lopez-Paz. mixup: Beyond empirical risk minimization. In *International Conference on Learning Representations*, 2018. [5](#page-4-1)
- <span id="page-9-12"></span>[39] Yifan Zhang, Daquan Zhou, Bryan Hooi, Kai Wang, and Jiashi Feng. Expanding small-scale datasets with guided imagination. *Advances in neural information processing systems*, 36:76558–76618, 2023. [3](#page-2-1)

- <span id="page-9-8"></span>[40] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*, pages 6514–6523, 2023. [2,](#page-1-0) [8](#page-7-4)
- <span id="page-9-4"></span>[41] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *Ninth International Conference on Learning Representations 2021*, 2021. [1,](#page-0-0) [2,](#page-1-0) [8](#page-7-4)
- <span id="page-9-16"></span>[42] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023. [6](#page-5-0)
- <span id="page-9-10"></span>[43] Daquan Zhou, Kai Wang, Jianyang Gu, Xiangyu Peng, Dongze Lian, Yifan Zhang, Yang You, and Jiashi Feng. Dataset quantization. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 17205– 17216, 2023. [2,](#page-1-0) [3](#page-2-1)
<span id="page-0-1"></span>

## Not All Samples Should Be Utilized Equally: Towards Understanding and Improving Dataset Distillation

S<PERSON><PERSON><sup>1,2</sup> <PERSON><PERSON><sup>2</sup> <PERSON><PERSON><sup>2</sup> <PERSON><PERSON><sup>3</sup> Linfeng  $Z$ hang<sup>1,2</sup> Junchi Yan $1*$ <sup>1</sup>School of Artificial Intelligence, Shanghai Jiao Tong University

<sup>2</sup>EPIC Lab, Shanghai Jiao Tong University

<sup>3</sup>National University of Singapore

{shaobowang1009,yanjunchi}@sjtu.edu.cn

### Abstract

*Dataset Distillation (DD) aims to synthesize a small dataset capable of performing comparably to the original dataset. Despite the success of numerous DD methods, theoretical exploration of this area remains unaddressed. In this paper, we take an initial step towards understanding various matching-based DD methods from the perspective of sample difficulty. We begin by empirically examining sample difficulty, measured by gradient norm, and observe that different matching-based methods roughly correspond to specific difficulty tendencies. We then extend the neural scaling laws of data pruning to DD to theoretically explain these matchingbased methods. Our findings suggest that prioritizing the synthesis of easier samples from the original dataset can enhance the quality of distilled datasets, especially in low IPC (image-per-class) settings. Based on our empirical observations and theoretical analysis, we introduce the Sample Difficulty Correction (SDC) approach, designed to predominantly generate easier samples to achieve higher dataset quality. Our SDC can be seamlessly integrated into existing methods as a plugin with minimal code adjustments. Experimental results demonstrate that adding SDC generates higher-quality distilled datasets across 7 distillation methods and 6 datasets.*

### <span id="page-0-0"></span>1. Introduction

In an era of data-centric AI, scaling laws [\[17\]](#page-8-0) have shifted the focus to data quality. Under this scenario, dataset distillation (DD) [\[36,](#page-9-0) [43](#page-9-1)[–45\]](#page-9-2) has emerged as a solution for creating high-quality data summaries. Unlike data pruning methods [\[1,](#page-8-1) [7,](#page-8-2) [13,](#page-8-3) [41,](#page-9-3) [42,](#page-9-4) [49\]](#page-9-5) that directly select data points

from original datasets, DD methods are designed to generate novel data points through learning. The utility of DD methods has been witnessed in fields such as privacy protection [\[4,](#page-8-4) [6,](#page-8-5) [11,](#page-8-6) [26\]](#page-8-7), continual learning [\[14,](#page-8-8) [29,](#page-9-6) [35,](#page-9-7) [50\]](#page-9-8), and neural architecture search [\[2,](#page-8-9) [31,](#page-9-9) [39\]](#page-9-10)

Among the various DD techniques, matching-based methods, particularly gradient matching (GM) [\[18,](#page-8-10) [23,](#page-8-11) [51,](#page-9-11) [52\]](#page-9-12) and trajectory matching (TM) [\[3,](#page-8-12) [8,](#page-8-13) [12,](#page-8-14) [15\]](#page-8-15), have demonstrated outstanding performance. However, a gap remains between their theoretical understanding and empirical success. To offer a unified explanation of these methods, we aim to explore the following question:

Question 1: *Is there a unified theory to explain existing matching-based DD methods?*

To address Question 1, we first empirically examine the differences between matching-based distillation methods. It is widely acknowledged that sample difficulty (Definition [1\)](#page-2-0) is a crucial metric in data-centric AI that significantly affects model performance, as seen in dataset pruning [\[27,](#page-8-16) [28,](#page-9-13) [38,](#page-9-14) [40\]](#page-9-15), and large language model prediction [\[9,](#page-8-17) [24,](#page-8-18) [25\]](#page-8-19). To track the differences between current distillation methods, we follow [\[34\]](#page-9-16) and analyze sample difficulty using the GraDN metric (Definition [2\)](#page-2-1). Surprisingly, we discover that the GraDN score is increased in GM-based methods (Figure  $1(a)$  $1(a)$ ), while TM-based methods may reduce this metric (Figure  $1(c)$  $1(c)$ ). These distinct trends indicate that the difficulty of samples utilized in GM-based methods is elevated (Figure  $2(a)$  $2(a)$ ), whereas in TM-based methods, it is reduced (Figure  $2(b)$  $2(b)$ ) during the distillation process.

Motivated by these observations, we develop a theoretical explanation for current DD methods from the perspective of sample difficulty. Specifically, we draw upon the *neural scaling law* in the data pruning theory [\[38\]](#page-9-14) to connect sample difficulty with performance. As shown in Figure [4\(](#page-4-0)c), our theory indicates that in matching-based DD

<sup>\*</sup>Corresponding Author.

<span id="page-1-2"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays two sets of comparative graphs related to dataset distillation methods. The left side, labeled "GM-based dataset distillation," features two plots: (a) a line graph showing "GraDN score" against "Training completion" for methods DC, DSA, and DSAC, with and without SDC, indicating that samples used in synthesis are becoming increasingly difficult; and (b) a bar chart comparing the "Accuracy (%)" of DC, DSA, and DSAC methods, with and without SDC, illustrating that SDC improves the quality of distilled datasets in GM-based methods. The right side, labeled "TM-based dataset distillation," also includes two plots: (c) a line graph showing "GraDN score" against "Training completion" for methods MTT, TESLA, FTD, and DATM, with and without SDC, with an inset showing a zoomed-in view of the 60%-100% training completion range; and (d) a bar chart comparing the "Accuracy (%)" of MTT, TESLA, FTD, and DATM methods, with and without SDC, indicating that SDC improves the quality of distilled datasets in TM-based methods.

Figure 1. We conducted experiments of GM-based methods on the FashionMNIST dataset and TM-based methods on the CIFAR-100 dataset. (a) Average gradient norms of network parameters for different GM-based methods are enhanced during distillation. The shade represents the gap before and after adding our SDC. (b) Test performance w/ and w/o our proposed SDC on different GM-based methods. Our SDC are incorporated to improve all matching-based methods. (c) Average gradient norms of network parameters for different TMbased methods are alleviated during distillation. (d) Test performance w/ and w/o our proposed SDC on different TM-based methods. Note that the average gradient norms are smoothed using the exponential moving average. Best viewed in color.

methods, when the synthetic dataset is small—specifically, when the images-per-class (IPC) is low—the optimal strategy is to primarily focus on easier samples rather than harder ones to enhance performance. Based on our theory, we further explain why TM-based methods usually outperform GM-based methods in real scenarios.

Beyond developing a theoretical framework, we take steps to explore solutions for improving current approaches. This raises another key research question:

Question 2: *Is it empirically feasible to identify a loss function that surpasses the performance of the matching loss by controlling the difficulty of learned patterns during distillation?*

To address Question 2, based on our empirical observations and theoretical analysis, we propose the novel *Sample Difficulty Correction* (SDC) method to improve the synthetic dataset quality in current matching-based distillation methods. We do this by guiding the distillation method to focus more on easy samples than hard samples, adding an implicit gradient norm regularizer to enhance quality.

Our contributions are listed as follows:

- We empirically investigate *sample difficulty* from the perspective of gradient norm in distillation methods, linking it to synthetic dataset quality. We propose that GM-based methods focus on difficult samples during optimization, while TM-based methods show no dominant preference for difficulty. This may explain the poorer performance of GM-based methods than TM-based methods.
- We theoretically elucidate the mechanism of matchingbased DD methods from the perspective of sample difficulty. Adapting the neural scaling law theory from data pruning [\[38\]](#page-9-14) to distillation settings, we provide insights into how matching strategies evolve with the size of the synthetic dataset. Consequently, we propose that focusing on matching easy samples is a better strategy when

the synthetic dataset is small.

• We introduce *Sample Difficulty Correction* (SDC) to improve the quality of synthetic datasets in current matching-based DD methods. Our method demonstrates superior generalization performance across 7 distillation methods (DC [\[52\]](#page-9-12), DSA [\[51\]](#page-9-11), DSAC [\[23\]](#page-8-11), MTT [\[3\]](#page-8-12), FTD [\[12\]](#page-8-14), TESLA [\[8\]](#page-8-13), DATM  $[15]$ ) and 6 datasets (MNIST [\[10\]](#page-8-20), FashionMNIST [\[47\]](#page-9-17), SVHN [\[33\]](#page-9-18), CIFAR-10/100 [\[19\]](#page-8-21), and Tiny-ImageNet [\[21\]](#page-8-22)).

### 2. Preliminaries and Related Work

Dataset distillation involves synthesizing a small, condensed dataset  $\mathcal{D}_{syn}$  that efficiently encapsulates the informational essence of a larger, authentic dataset  $\mathcal{D}_{\text{real}}$ .

Gradient Matching (GM) based methods are pivotal in achieving distillation by ensuring the alignment of training gradients between surrogate models trained on both the original dataset  $\mathcal{D}_{real}$  and the synthesized dataset  $\mathcal{D}_{syn}$ . This method is first introduced by DC [\[52\]](#page-9-12). Let  $\theta_t$  represent the network parameters sampled from distribution  $P_{\theta}$  at step t, and C symbolizes the categories within  $\mathcal{D}_{\text{real}}$ . The crossentropy loss  $\mathcal{L}$ , is employed to assess the matching loss by comparing the gradient alignment over a time horizon of T steps. The formal optimization objective of DC is:

<span id="page-1-1"></span>
$$
\mathop {\arg \min }\limits_{{\mathcal{D}_{\textit{syn}}}}\mathop {\mathbb{E}}\limits_{{\theta_0} \sim {P_{\theta }},c \sim C} \left[ {\sum\limits_{t = 0}^T { \mathbf{D}\left( {{\nabla _\theta }{\mathcal{L}}_{{\mathcal{D}}_{\textit{real}}^c}}\left( {{\theta _t}} \right),{\nabla _\theta }{\mathcal{L}}_{{\mathcal{D}}_{\textit{syn}}^{c}}\left( {{\theta _t}} \right)} \right)} \right],\tag{1}
$$

where D measures the cumulative distances (*e.g.*,  $\cosh L_2$  distance in DC) between the gradients of weights corresponding to each category output. The parameter updates for  $\theta$  are executed in an inner loop via gradient descent, with a specified learning rate  $\eta$ :

$$
\theta_{t+1} \leftarrow \theta_t - \eta \cdot \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{syn}}(\theta_t). \tag{2}
$$

<span id="page-2-5"></span><span id="page-2-2"></span>Image /page/2/Figure/0 description: The image displays three subfigures labeled (a), (b), and (c), each illustrating different optimization strategies. A legend on the left defines symbols: blue circles represent 'easy samples', red circles represent 'hard samples', and a yellow star represents 'synthetic samples'. Dotted blue arrows indicate 'dominate optimization', dotted red arrows indicate the same, and a solid orange arrow indicates 'actual optimization'. Each subfigure shows a scatter plot of gray dots, a dashed circle labeled 'difficulty boundary', several blue and red circles, and a yellow star. Subfigure (a) is titled 'Gradient Matching', subfigure (b) is titled 'Trajectory Matching', and subfigure (c) is titled 'Matching with SDC'. In all subfigures, the synthetic sample (yellow star) is positioned near the center, with easy samples (blue circles) further out and hard samples (red circles) even further out. The arrows show the direction of optimization from the synthetic sample towards the easy and hard samples, with the 'actual optimization' arrow pointing towards the cluster of easy samples.

Figure 2. Comparison of matching-based dataset distillation methods from a sample difficulty perspective. (a) Gradient matching-based methods mainly utilize hard samples during synthesizing. (b) Trajectory matching-based methods do not explicitly take the difficulty of samples into consideration. (c) Our SDC encourages matching-based methods to prioritize the synthesis of easy samples.

Building upon this, DSA [\[51\]](#page-9-11) enhances DC by implementing consistent image augmentations on both  $\mathcal{D}_{\text{real}}$  and  $\mathcal{D}_{syn}$  throughout the optimization process. Moreover, DCC [\[23\]](#page-8-11) refines the gradient matching objective by incorporating class contrastive signals at each gradient matching step, which results in enhanced stability and performance. Combining DSA and DCC, DSAC [\[23\]](#page-8-11) further introduces improvements by synergizing these techniques. The revised optimization objective for DCC and DSAC is formulated as:

$$
\underset{\mathcal{D}_{syn}}{\operatorname{arg min}} \mathbb{E}_{{\theta_0} \sim P_{\theta}} \left[ \sum_{t=0}^T \mathbf{D} \left( \mathbb{E}_{c \in C} \left[ \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{real}^c} (\theta_t) \right], \mathbb{E}_{c \in C} \left[ \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{syn}^c} (\theta_t) \right] \right) \right] \quad (3)
$$

Trajectory matching (TM) based approaches aim to match the training trajectories of surrogate models by optimizing over both the real dataset  $\mathcal{D}_{\text{real}}$  and the synthesized dataset  $\mathcal{D}_{syn}$ . TM-based methods were initially proposed in MTT [\[3\]](#page-8-12). Let term  $\tau^{\mathcal{D}_{\text{real}}}$  denote the expert training trajectories, represented as a sequential array of parameters  $\{\theta_t^{\mathcal{D}_{\text{real}}}\}_{t=0}^T$ , obtained from training a network on the real dataset  $\mathcal{D}_{\mathsf{real}}$ . In parallel,  $\theta_{t}^{\mathcal{D}_{\mathsf{syn}}}$  refers to the parameter set of the network trained on  $\mathcal{D}_{syn}$  at step t. In each iteration, parameters  $\theta_t^{\mathcal{D}_{\mathsf{real}}}$  and  $\theta_{t+M}^{\mathcal{D}_{\mathsf{real}}}$  are randomly selected from the expert trajectory pool  $\{\tau^{\mathcal{D}_{\text{real}}}\}\,$ , serving as the initial and target parameters for trajectory alignment, where  $M$  is a predetermined hyperparameter. TM-based methods enhance the synthetic dataset  $\mathcal{D}_{syn}$  by minimizing the loss defined as:

<span id="page-2-4"></span>
$$
\underset{\mathcal{D}_{syn}}{\arg\min} \mathop{\mathbb{E}}_{\theta_0 \sim P_{\theta}} \left[ \sum_{t=0}^{T-M} \frac{\mathbf{D} \left( \theta_{t+M}^{\mathcal{D}_{\text{real}}}, \theta_{t+N}^{\mathcal{D}_{\text{syn}}} \right)}{\mathbf{D} \left( \theta_{t+M}^{\mathcal{D}_{\text{real}}}, \theta_{t}^{\mathcal{D}_{\text{real}}} \right)} \right], \qquad (4)
$$

where  $D$  is a distance metric (*e.g.*,  $L_2$  distance in MTT) and  $N \ll M$  is a predefined hyperparameter.  $\theta_{t+N}^{\mathcal{D}_{syn}}$  is derived through an inner optimization using the cross-entropy loss  $\mathcal L$  with the learning rate  $\eta$ :

$$
\theta_{t+i+1}^{\mathcal{D}_{syn}} \leftarrow \theta_{t+i}^{\mathcal{D}_{syn}} - \eta \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{syn}}(\theta_{t+i}^{\mathcal{D}_{syn}}), \text{ where } \theta_{t}^{\mathcal{D}_{syn}} := \theta_{t}^{\mathcal{D}_{real}}.
$$
\n(5)

Similarly, TESLA [\[8\]](#page-8-13) utilizes linear algebraic manipulations and soft labels to increase compression efficiency, FTD [\[12\]](#page-8-14) aims to seek a flat trajectory to avoid accumulated trajectory error, and DATM [\[15\]](#page-8-15) considers matching only necessary parts of trajectory with difficulty alignment.

### 3. Method

<span id="page-2-3"></span>

### 3.1. A Closer Look at Sample Difficulty

In this subsection, we aim to intuitively understand dataset distillation through the concept of sample difficulty (Definition [1\)](#page-2-0), which is pivotal in data-centric AI [\[5,](#page-8-23) [9,](#page-8-17) [24,](#page-8-18) [27,](#page-8-16) [28,](#page-9-13) [40,](#page-9-15) [48\]](#page-9-19). We begin by empirically observing the evolution of sample difficulty during the distillation process. Firstly, we introduce the commonly used definition of sample difficulty, namely the GraDN score (Definition [2\)](#page-2-1), and validate the reliability of this metric. Furthermore, we track the GraDN score across current dataset distillation methods to delve deeper into their underlying mechanisms.

<span id="page-2-0"></span>Definition 1 (Sample Difficulty [\[30\]](#page-9-20)). *Given a training pair* (x, y) *and a series of pretrained models at training time t*, the sample difficulty, denoted  $\chi(x, y; \Theta_t)$ , is defined as *the expected probability of*  $(x, y)$  *being misclassified by an ensemble of models*  $\theta_t \in \Theta_t$ *. Formally, it is presented as:* 

$$
\chi(x, y; \Theta_t) = \mathbb{E}_{\theta_t \in \Theta_t} \left[ \mathbbm{1} \left( y \neq \theta_t(x) \right) \right],\tag{6}
$$

*where*  $\mathbb{1}(z)$  *is an indicator function that equals 1 if the boolean input* z *is true, and 0 otherwise. In this case, the indicator function equals to 1 if the sample*  $(x, y)$  *is misclassified by the model with parameters*  $\theta_t$ *, and 0 otherwise.* 

<span id="page-2-1"></span>Definition 2 (GraDN Score [\[34\]](#page-9-16)). *Consider a training pair*  $(x, y)$ *, with*  $\mathcal L$  *representing the loss function. At time t, the GraDN score for* (x, y) *is calculated as the average gradient norm of the loss* L *across a diverse ensemble of models*

<span id="page-3-2"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: This image contains four scatter plots. The left two plots are labeled "CIFAR-10" and the right two plots are labeled "CIFAR-100". The y-axis for all plots is "Sample difficulty" ranging from 0.0 to 0.8. The x-axis for the first plot (a) is "Gradient norm" ranging from 0 to 100. The x-axis for the second plot (b) is "Loss" ranging from 0 to 4. The x-axis for the third plot (c) is "Gradient norm" ranging from 0 to 50. The x-axis for the fourth plot (d) is "Loss" ranging from 0 to 5. A legend indicates that the data points are colored according to class, with classes 0 through 9 represented by different colors from red to pink.

Figure 3. The statistical relationship between sample difficulty  $\chi(x, y; \Theta_t)$ , gradient norm GraDN(x, y;  $\Theta_t$ ), and average validation loss for each sample  $(x, y)$  on a series of models with  $\theta_t \in \Theta_t$ . We observe a significant positive correlation between sample difficulty and both the gradient norm and the loss. Experiments were conducted using ResNet-18 on CIFAR-10 and ResNet-34 on CIFAR-100. Each score was evaluated across 20 pretrained models. For CIFAR-100, 10 categories were randomly selected for visualization. The relationships depicted are: (a) sample difficulty vs. gradient norm on CIFAR-10, (b) sample difficulty vs. loss on CIFAR-10, (c) sample difficulty vs. gradient norm on CIFAR-100, and (d) sample difficulty vs. loss on CIFAR-100.

*with parameters*  $\theta_t \in \Theta_t$ *:* 

$$
Graph(x, y; \Theta_t) = \mathbb{E}_{\theta_t \in \Theta_t} \left[ \|\nabla_{\theta} \mathcal{L}(x, y; \theta_t)\|_2 \right], \quad (7)
$$

*where*  $\nabla_{\theta} \mathcal{L}(x, y; \theta_t)$  *denotes the gradient of loss*  $\mathcal L$  *on sample*  $(x, y)$  w.r.t. *the model parameters*  $\theta_t$ *, and*  $\|\cdot\|_2$  *denotes*  $L_2$  *norm.* 

According to [\[30\]](#page-9-20), the difficulty of each sample can be assessed by the misclassification ratio across a series of pretrained models (Definition [1\)](#page-2-0). Additionally, from an optimization perspective, it can be represented by the gradient norm of the loss on a series of pretrained models for this sample (Definition [2\)](#page-2-1). In our study, we adopt Definition [2](#page-2-1) to evaluate sample difficulty as interpreted by various matching methods.

Empirical verification of the relationship between sample difficulty and gradient norm. We conducted experiments to verify the reliability of the GraDN score in classifying the CIFAR-10 and CIFAR-100 datasets by training a set of models. As depicted in Figure [3,](#page-3-0) the GraDN score shows a clear positive correlation with the sample difficulty. For easier samples, GraDN scores are generally lower, exerting minimal impact on the network's gradient flow. Conversely, for harder samples, higher GraDN scores indicate a significant impact on the optimization directions of the models. We show detailed results of the relationships between these metrics in Appendix [1.](#page-0-0)

Exploring sample difficulty across different distillation methods. Beyond sample difficulty under classification scenarios, we now extend our observations to matching-based distillation methods. Specifically, we examined the average gradient norm of the training crossentropy loss across network parameters during the distillation process. As shown in Figure  $1(a)(c)$  $1(a)(c)$ , we found that the

average gradient norm (corresponding to the GraDN score) tends to increase in GM-based methods (signifying harder samples), whereas it decreases in TM-based methods (indicating easier samples). This unexpected phenomenon motivates us to further theoretically explore matching-based distillation methods from the perspective of sample difficulty.

<span id="page-3-1"></span>

### 3.2. An Analytical Theory for Explaining Matchingbased Dataset Distillation

In Section [3.1,](#page-2-3) we empirically observed distinct trends in *sample difficulty* across various dataset distillation methods. Here, we propose an analytical theory based on the *neural scaling law* to formally analyze sample difficulty in matching-based methods. We extend the theory of data pruning presented by [\[38\]](#page-9-14) and validate its applicability within the context of DD using an expert-student perceptron model. Unlike data pruning, where the pruned dataset is directly selected from the original dataset, DD involves synthesizing a small, new, unseen dataset.

We start our analysis with tools from statistical mechanics [\[32\]](#page-9-21). Let us consider a classification problem in dataset  $\mathcal{D}^{\text{real}}$  containing  $d_{\text{real}}$  samples  $\{x_i, y_i\}_{i=1,\dots,d_{\text{real}}}$ , where  $x_i \in \mathbb{R}^d \sim \mathcal{N}(0, I_d)$  are *i.i.d.* zero-mean, unit variance Gaussian inputs, and  $y_i = sign(\theta^{\mathcal{D}_{real}^{\top}} x_i) \in \{-1, +1\}$ are labels generated by an expert perceptron  $\theta^{\mathcal{D}_{\text{real}}} \in \mathbb{R}^d$ . Our analysis is within the high-dimensional statistics limit, where  $d, d_{\text{real}} \rightarrow \infty$  while maintaining the ratio of total training samples to parameters  $\alpha_{\text{tot}} = d_{\text{real}}/d$  at  $O(1)$ . The general distillation algorithm proceeds as follows:

1. Train a student perceptron on  $\mathcal{D}_{\text{real}}$  for a few epochs to obtain weights  $\theta^{\text{probe}}$ . The gap between can be measured by the angle  $\gamma$  between the probe student  $\theta^{\text{probe}}$ and the expert  $\theta^{\text{real}}$ . If  $\theta^{\text{probe}} \approx \theta^{\text{real}}$ , we denote the  $\theta^{\text{probe}}$  as a *perfect* probe ( $\gamma = 0$ ). Otherwise, in *imper*-

<span id="page-4-2"></span><span id="page-4-0"></span>Image /page/4/Figure/0 description: The image displays three plots related to dataset distillation. Plot (a) shows test error (%) on the y-axis versus synthetic samples per parameter (αsyn) on the x-axis. It includes theoretical curves and simulation data points for various fractions of synthesized data, ranging from 10% to 100%. Plot (b) is a heatmap showing the difference between Acc(hard) and Acc(easy) on the y-axis, with synthetic samples per parameter (αsyn) on the x-axis. The color bar indicates values from -20 to +5. Plot (c) illustrates the analytic theory of dataset distillation, depicting a circular region with diagonal bands representing different data distributions. Arrows indicate that hard samples are utilized when synthesizing large Dreal (αsyn is large), and easy samples are utilized when synthesizing small Dreal (αsyn is small), noting that this is usually not the case in typical DD with small IPC.

Figure 4. An analytic theory of dataset distillation. (a) Test error  $\varepsilon$  as a function of the synthetic samples per parameter  $\alpha_{syn}$  and fraction of data synthesized f in a perfect expert setting ( $\gamma = 0$ ). (b) We show the difference in test accuracy in learning synthetic dataset by learning from hard vs easy samples, revealing the change in distillation strategy. (c) Our theory suggests that when we synthesize small dataset (small  $\alpha_{syn}$ ), the better distillation strategy is to utilize the easy samples.

*fect* probe cases,  $\gamma \neq 0$ .

- 2. Compute the margin  $m_i = \theta^{\text{probe}^\top}(y_i x_i)$  for each training example, categorizing large (small) margins as easy (hard) samples.
- 3. Generate a synthetic dataset  $\mathcal{D}_{syn}$  of size  $d_{syn} = f d_{real}$ , by learning from the hardest samples from  $\mathcal{D}_{\text{real}}$  for few steps. The ratio of total samples of the synthetic dataset to parameters is  $\alpha_{\text{syn}} = d_{\text{syn}}/d$ .
- 4. Train a new perceptron  $\theta^{\mathcal{D}_{syn}}$  on  $\mathcal{D}_{syn}$ , aiming to classify training data with the maximal margin  $\kappa$  =  $\min_i \theta^{\mathcal{D}^{\top}_\mathsf{syn}} y_i x_i.$

We analyze the test error  $\varepsilon$  of this final perceptron  $\theta^{\mathcal{D}_{syn}}$ as a function of  $\alpha_{syn}$ , f, and the angle  $\gamma$  between the probe student  $\theta^{\text{probe}}$  and the expert  $\theta^{\mathcal{D}_{\text{real}}}$ . Similar to [\[38\]](#page-9-14), we define  $\theta^{\rm probe}$  as a random Gaussian vector conditioned to have angle  $\gamma$  with the expert. Under this scenario, we can derive an asymptotically exact analytic expression for  $\varepsilon(\alpha_{syn}, f, \gamma)$ (see Appendix [4](#page-5-0) for details):

<span id="page-4-1"></span>
$$
\varepsilon = \frac{\cos^{-1}(R)}{\pi}, \quad \text{where } R = \frac{\theta^{\mathcal{D}_{\text{real}}^{-1}} \theta^{\mathcal{D}_{\text{syn}}}}{\|\theta^{\mathcal{D}_{\text{real}}}\|_2 \|\theta^{\mathcal{D}_{\text{syn}}}\|_2} \qquad (8)
$$

Likewise in [\[38\]](#page-9-14), we can solve  $R$  with saddle point equations in Appendix [4.1,](#page-5-1) enabling direct predictions of the test error  $\varepsilon$  according to Eq [\(8\)](#page-4-1).

Verification of the neural scaling law in dataset distillation. We first evaluated the correctness of our theory in the perfect expert-student setting ( $\gamma = 0$ ). As shown in Figure [4\(](#page-4-0)a), we observed an excellent match between our analytic theory (solid curves) and numerical simulations (dots) of perceptron learning at parameters  $d = 200$  in dataset distillation. We also verify our theory in imperfect probe settings when  $\gamma \neq 0$ , as shown in Appendix [4.2.](#page-5-2)

The neural scaling law for dataset distillation. We further investigate the relationship between the distillation ratio ( $\alpha_{\text{syn}}$ ), the fraction of data synthesized (f), and the final accuracy of  $\theta^{\mathcal{D}_{syn}}$  under various distillation strategies,

such as synthesizing  $\mathcal{D}_{syn}$  from hard or easy samples. Similar to data pruning, when  $f = 1$  (no distillation), the test error follows the classical perceptron learning powerlaw scaling,  $\varepsilon \propto \alpha_{syn}^{-1}$ . In other cases, our findings reveal that for smaller  $\alpha_{syn}$  (smaller synthetic datasets), learning from the hard samples results in poorer performance than no distillation. Conversely, for larger  $\alpha_{syn}$ , focusing on the hard samples yields substantially better outcomes than no distillation. We find that in limited data regimes, matching the easy samples, which have the largest margins, offers a more effective distillation strategy. This finding highlights that in most cases of DD (where  $d_{syn} \ll d_{\text{real}}$ ), it is crucial for the model to first learn from the basic characteristics in  $\mathcal{D}_{\text{real}}$ ; hence, prioritizing easy samples facilitates reaching a moderate error level more swiftly.

Understanding GM-based and TM-based methods with the neural scaling law. As depicted in Figure  $1(a)$  $1(a)$ , we observe that GM-based methods typically incorporate the hard samples within the synthetic dataset. This trend is due to the GM-based matching loss in Eq.( [1\)](#page-1-1), which predominantly penalizes samples with large gradients (hard samples) (shown in Figure [2\(](#page-2-2)b)). However, in most DD settings, the size of synthetic dataset  $\mathcal{D}_{syn}$  is usually small. Therefore, according to our theory, we should mainly focus on synthesizing the dataset by matching easy samples to achieve higher dataset quality. In contrast, the simplicity of the synthetic samples generated by TM-based methods, as shown in Figure [1\(](#page-1-0)a), is not directly concerned through distillation. From Eq.( [4\)](#page-2-4), it is evident that TMbased methods prioritize parameter alignment, thus penalizing the matching term without explicitly targeting sample difficulty (shown in Figure  $2(c)$  $2(c)$ ). This approach results in a synthetic dataset that may be generated by learning samples of randomly vary in difficulty. We can provide a explanation that TM-based methods generalize well in real scenarios than GM-based methods because of they do not explicitly focus on synthesizing by matching hard samples.

<span id="page-5-4"></span><span id="page-5-3"></span>

### 3.3. Matching with Sample Difficulty Correction

Based on our theoretical analysis of matching-based dataset distillation, we propose a novel method to enhance existing techniques for synthesizing higher-quality distilled datasets. Although TM-based methods have achieved relative success on current benchmark datasets, they do not explicitly consider sample difficulty, which could ensure higher synthetic dataset quality.

A direct approach to impose constraints on sample difficulty is to calculate the gradient norm for each sample as a metric to determine its utility. Let us consider the case of GM-based methods. At step  $t$ , a batch of real samples  $\mathcal{B}_{\text{real}}^c \sim \mathcal{D}_{\text{real}}^c$  of class  $c \in C$  is to be matched with the gradients of a synthetic batch  $\mathcal{B}^c_{syn} \sim \mathcal{D}^c_{syn}$ . To decide whether to utilize each sample in  $\mathcal{B}_{\text{real}}^c$ , it is natural to compute the gradient norm of each sample and utilize those with a score smaller than a predefined threshold  $\tau$ . Specifically, a sample  $(x, y)$  is utilized if  $\|\nabla_{\theta} \mathcal{L}(x, y; \theta_t)\|_2 < \tau$ . Consequently, the modified loss for matching only easy samples is:

$$
\mathcal{L}_{\tilde{\mathcal{B}}_{\text{real}}^c} = \mathbb{E}_{(x,y)\in\tilde{\mathcal{B}}_{\text{real}}^c} \left[ \mathcal{L}(x,y;\theta_t) \right],
$$
  
$$
\mathcal{L}_{\tilde{\mathcal{B}}_{\text{sym}}^c} = \mathbb{E}_{(x,y)\in\tilde{\mathcal{B}}_{\text{sym}}^c} \left[ \mathcal{L}(x,y;\theta_t) \right],
$$
 (9)

where  $\tilde{\mathcal{B}}_{\text{real}}^c = \{(x, y) | (x, y) \in \mathcal{B}_{\text{real}}^c, \|\nabla_{\theta} \mathcal{L}(x, y; \theta_t)\|_2 \leq$  $\tau$ } denotes the modified batch with only easy samples, and  $\tilde{\mathcal{B}}_{syn}^c$  denote a sampled batch from  $\mathcal{D}_{syn}^c$  with the same size as  $\tilde{\mathcal{B}}_{\text{real}}^c$ . The corresponding matching loss should be:

$$
\tilde{L}(\theta_t) = \mathbf{D} \left( \nabla_{\theta} \mathcal{L}_{\tilde{\mathcal{B}}_{\text{real}}^c} (\theta_t), \nabla_{\theta} \mathcal{L}_{\tilde{\mathcal{B}}_{\text{syn}}^c} (\theta_t) \right), \quad (10)
$$

However, the computational cost of constructing reduced easy sample batch  $\tilde{\mathcal{B}}_{\text{real}}^c$  from  $\mathcal{B}_{\text{real}}^c$  is unrealistic in realworld scenarios because it requires calculating the gradient norm for each sample independently, resulting in a tenfold or greater increase in time. Besides, determining the difficulty threshold  $\tau$  is also ad-hoc and challenging for each sample. Therefore, we take an alternative approach, *i.e.*, we consider adding the overall sample difficulty of the whole batch  $\mathcal{B}_{syn}^c$  as an implicit regularization term in the matching loss function. Our proposed methods, named *Sample Difficulty Correction* (SDC), can be incorporated into current matching methods with minimal adjustment of code implementation. Specifically, for a single-step GM, we have the following modified loss:

$$
L_{\lambda}(\theta_{t}) = \underbrace{\mathbf{D}\left(\nabla_{\theta} \mathcal{L}_{\mathcal{B}_{\text{real}}^{c}}\left(\theta_{t}\right), \nabla_{\theta} \mathcal{L}_{\mathcal{B}_{\text{syn}}^{c}}\left(\theta_{t}\right)\right)}_{\text{Gradient Matching Loss}} + \underbrace{\lambda \left\|\nabla_{\theta} \mathcal{L}_{\mathcal{B}_{\text{syn}}^{c}}\right\|_{2}}_{\text{Gradient Norm Regularization}}
$$
(11)

For TM-based methods that do not explicitly focus on sample difficulty during distillation, we compute the average

gradient norm of the whole dataset  $\mathcal{D}_{syn}$  during the optimization of the student network  $\theta_t^{\mathcal{D}_{syn}}$  w.r.t. the training loss as the regularization term. Specifically, we have:

$$
L_{\lambda}(\theta_t^{\mathcal{D}_{syn}}) = \underbrace{\mathbf{D}\left(\theta_{t+M}^{\mathcal{D}_{real}}, \theta_{t+N}^{\mathcal{D}_{syn}}\right)/\mathbf{D}\left(\theta_{t+M}^{\mathcal{D}_{real}}, \theta_{t}^{\mathcal{D}_{real}}\right)}_{\text{Trajectory Matching Loss}} + \underbrace{\lambda \left\|\nabla_{\theta} \mathcal{L}_{\mathcal{D}_{syn}}\right\|_{2}}_{\text{Gradient Norm Regularization}}(12)
$$

By adding the gradient norm regularization, we can implicitly enforce current matching-based methods to mainly concentrate on synthesizing easy samples to achieve better synthetic data quality. We provide the algorithm pseudocodes for GM- and TM-based methods in Appendix [2.3.](#page-10-0)

<span id="page-5-0"></span>

## 4. Experiments

<span id="page-5-1"></span>

### 4.1. Basic Settings

Datasets and baselines. For GM-based methods, we followed previous works to conduct experiments on MNIST [\[10\]](#page-8-20), FashionMNIST [\[47\]](#page-9-17), SVHN [\[33\]](#page-9-18) datasets. We utilized current GM-based methods, including DC [\[52\]](#page-9-12), DSA [\[51\]](#page-9-11), and DSAC [\[23\]](#page-8-11) as baselines. For TM-based methods, we followed the recent papers to use CIFAR-10, CIFAR-100 [\[19\]](#page-8-21), and Tiny ImageNet [\[21\]](#page-8-22) datasets. We performed experiments on current baselines including MTT [\[3\]](#page-8-12), FTD [\[12\]](#page-8-14), TESLA [\[8\]](#page-8-13), and DATM [\[15\]](#page-8-15). We added our *Sample Difficulty Correction* (SDC) for all these baseline methods. To ensure a fair comparison, we employed identical hyperparameters for GM-based and TM-based methods with and without SDC while keeping all other variables constant, such as model architecture and augmentations. As per convention, for TM-based methods, we used max test accuracy, while for GM-based methods, we utilized the test accuracy from the last iteration. We also compared our methods with classical data pruning algorithms including Random, Herding [\[46\]](#page-9-22), and Forgetting [\[41\]](#page-9-3). All hyperparameters are detailed in Appendix [2.1.](#page-10-1)

Neural networks for distillation.We used ConvNet as default to conduct experiments. Consistent with other previous methods, we used 3-layer ConvNet for CIFAR-10, CIFAR-100, MNIST, SVHN, and FashionMNIST, and 4 layer ConvNet for Tiny ImageNet.

<span id="page-5-2"></span>

### 4.2. Main Results

GM-based methods on MNIST, FashionMNIST, and SVHN. As presented in Table [1,](#page-6-0) we report the results of three GM-based methods applied to MNIST, FashionM-NIST, and SVHN datasets. Each method was evaluated with IPC (images-per-class) values of 1, 10, and 50. Notably, adding SDC improves the test accuracy of baseline methods across all datasets and IPC values, demonstrating

| <b>Dataset</b>       |                | <b>MNIST</b>   |                |                | FashionMNIST   |                |                | <b>SVHN</b>    |                |
|----------------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
| <b>IPC</b>           |                | 10             | 50             |                | 10             | 50             |                | 10             | 50             |
| Ratio $(\%)$         | 0.02           | 0.2            | л.             | 0.2            | 2              | 10             | 0.2            | 2              | 10             |
| Random               | $64.9 \pm 3.5$ | $95.1 \pm 0.9$ | $97.9 \pm 0.2$ | $51.4 \pm 3.8$ | $73.8 \pm 0.7$ | $82.5 \pm 0.7$ | $14.6 \pm 1.6$ | $35.1 \pm 4.1$ | $70.9 \pm 0.9$ |
| Herding              | $89.2 \pm 1.6$ | $93.7 \pm 0.3$ | $94.8 \pm 0.2$ | $67.0 \pm 1.9$ | $71.1 \pm 0.7$ | $71.9 \pm 0.8$ | $20.9 \pm 1.3$ | $50.5 \pm 3.3$ | $72.6 \pm 0.8$ |
| Forgetting           | $35.5 \pm 5.6$ | $68.1 \pm 3.3$ | $88.2 \pm 1.2$ | $42.0 \pm 5.5$ | $53.9 \pm 2.0$ | $55.0 \pm 1.1$ | $12.1 \pm 1.7$ | $16.8 \pm 1.2$ | $27.2 \pm 1.5$ |
| DC                   | $91.8 \pm 0.4$ | $97.4 \pm 0.2$ | $98.5 \pm 0.1$ | $70.3 \pm 0.7$ | $82.1 \pm 0.3$ | $83.6 \pm 0.2$ | $31.1 \pm 1.3$ | $75.3 \pm 0.6$ | $82.1 \pm 0.2$ |
| $+SDC$               | $92.0 \pm 0.4$ | $97.5 \pm 0.1$ | $98.9 \pm 0.1$ | $70.7 \pm 0.5$ | $82.4 \pm 0.3$ | $84.7\pm0.2$   | $31.4 \pm 1.2$ | $76.0 \pm 0.5$ | $82.3 \pm 0.3$ |
| <b>DSA</b>           | $88.9 \pm 0.8$ | $97.2 \pm 0.1$ | $99.1 \pm 0.1$ | $70.1 \pm 0.4$ | $84.7 \pm 0.2$ | $88.7 \pm 0.2$ | $29.4 \pm 1.0$ | $79.2 \pm 0.4$ | $84.3 \pm 0.4$ |
| $+SDC$               | $89.2 \pm 0.4$ | $97.3 \pm 0.1$ | $99.2 \pm 0.4$ | $70.5 \pm 0.5$ | $84.8 \pm 0.2$ | $88.9 \pm 0.1$ | $30.6 \pm 1.0$ | $79.4 \pm 0.4$ | $85.3 \pm 0.4$ |
| <b>DSAC</b>          | $89.2 \pm 0.7$ | $97.7 \pm 0.1$ | $98.8 \pm 0.1$ | $71.8 \pm 0.7$ | $84.9 \pm 0.2$ | $88.5 \pm 0.2$ | $47.5 \pm 1.8$ | $80.1 \pm 0.5$ | $87.3 \pm 0.2$ |
| $+SDC$               | $89.7 \pm 0.7$ | $97.8 \pm 0.1$ | $98.9 \pm 0.1$ | $72.2 \pm 0.6$ | $85.1 \pm 0.1$ | $88.7 \pm 0.1$ | $48.1 \pm 1.6$ | $80.4 \pm 0.3$ | $87.4 \pm 0.2$ |
| <b>Whole Dataset</b> |                | $99.6 \pm 0.0$ |                |                | $93.5 \pm 0.1$ |                |                | $95.4 \pm 0.1$ |                |

<span id="page-6-1"></span><span id="page-6-0"></span>Table 1. Comparison of test accuracy (%) results of GM-based dataset distillation methods w/ and w/o SDC on MNIST, FashionMNIST, and SVHN datasets.

the effectiveness of our approach. Notably, adding SDC to the original method improved the test accuracy of DSA by 1.2% on the SVHN dataset with IPC = 1, and by  $1\%$  with IPC = 50. For DC on the FashionMNIST dataset with IPC  $= 50$ , the test accuracy was increased by 1.1% with SDC. All hyperparameters are detailed in Table [4.](#page-13-0)

TM-based methods on CIFAR-10/100 and Tiny ImageNet. As shown in Table [2,](#page-7-0) we present the results of four TM-based methods trained on CIFAR-10, CIFAR-100 and Tiny ImageNet. By incorporating the average gradient norm as a regularization term during matching with SDC, the resulting test accuracy was generally improved. Notably, employing SDC improved the test accuracy of FTD on CIFAR-10 by  $1.2\%$  with IPC = 10 and  $1.1\%$  with IPC = 50, and enhanced the test accuracy of DATM on Tiny ImageNet by 0.6%. For FTD, we used EMA (exponential moving average) just as in the original method $[12]$ . All hyperparameters are detailed in Tables [5,](#page-13-1) [6,](#page-14-0) [7,](#page-14-1) and [8.](#page-15-0)

Generalization performance to other architectures. We evaluated the generalizability of synthetic datasets generated through distillation. We used DSAC and DATM, which are current SOTA methods in GM-based and TMbased distillation, respectively. After distillation, the synthetic datasets were assessed using various neural networks, including ResNet-18 [\[16\]](#page-8-24), VGG-11 [\[37\]](#page-9-23), AlexNet [\[20\]](#page-8-25), LeNet [\[22\]](#page-8-26) and MLP. As shown in Table [3,](#page-7-1) even though our synthetic datasets were distilled using ConvNet, it generalizes well across most networks. Notably, for the experiment of DATM on CIFAR-10 with  $IPC = 1$ , employing SDC resulted in an accuracy improvement of 4.61% when using AlexNet. Employing SDC to DSAC led to an accuracy improvement of  $0.9\%$  on SVHN with IPC = 10 when using MLP. Additional results can be found in Appendix [3.1.](#page-2-3)

## 4.3. Further Discussions

Discussion of SDC coefficient  $\lambda$ . The selection of the regularization coefficient  $\lambda$  is pivotal for the quality of the distilled dataset. Our theory suggests that a larger  $\lambda$  typically produces better synthetic datasets for smaller IPC values. Ideally, for low IPC settings, it is better to employ a large  $\lambda$ to strongly penalize sample difficulty, whereas, for high IPC settings, the required  $\lambda$  can be small or even close to zero in extreme cases. For simplicity and to maintain consistency across different datasets and baseline methods, we have set  $\lambda = 0.002$  as the default value in most of our experiments. As demonstrated in Figure [5,](#page-7-2) this choice of  $\lambda$  aligns with the IPC values. Results for FTD and TESLA are based on CIFAR-10, results for DSA are based on SVHN, and results for DSAC are based on MNIST. Additionally, we further show that the choice of  $\lambda$  is not sensitive in Appendix [3.3.](#page-5-3)

Adaptive sample difficulty correction by adaptively increasing  $\lambda$  during distillation. While our SDC seeks simplicity in regularization, DATM [\[15\]](#page-8-15) claims that the matching difficulty is increased through optimization. Inspired by their observation, we implemented a strategy where  $\lambda$  increases progressively throughout the matching phases. This method is designed to incrementally adjust the focus from easier to more complex patterns. Inspired by their observation, we applied an *Adaptive Sample Difficulty Correction* (ASDC) strategy in our experiments with a TMbased method on the CIFAR-100 with IPC  $= 1$  and with a GM-based method on the FashionMNIST with IPC = 1. The  $\lambda$  of DATM was initialized to 0.02 and logarithmically increased to 0.08 over 10,000 iterations and DSAC was initialized to 0.002 and logarithmically increased to 0.008 over 10,000 steps. For DATM, we use max test accuracy, while for DSAC, we use test accuracy. Experimental results of ASDC validate its potential to significantly enhance learn-

| Dataset              |                | CIFAR-10       |                |                | $CIFAR-100$    |                |                | Tiny ImageNet  |                          |
|----------------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|--------------------------|
| <b>IPC</b>           |                | 10             | 50             |                | 10             | 50             |                | 10             | 50                       |
| Ratio $(\%)$         | 0.02           | 0.2            | 1              | 0.2            | 2              | 10             | 0.2            | 2              | 10                       |
| Random               | $14.4 \pm 2.0$ | $26.0 \pm 1.2$ | $43.4 \pm 1.0$ | $4.2 \pm 0.3$  | $14.6 \pm 0.5$ | $30.0 \pm 0.4$ | $1.4\pm 0.1$   | $5.0 \pm 0.2$  | $15.0 \pm 0.4$           |
| Herding              | $21.5 \pm 1.2$ | $31.6 \pm 0.7$ | $40.4 \pm 0.6$ | $8.4 \pm 0.3$  | $17.3 \pm 0.3$ | $33.7 \pm 0.5$ | $2.8 \pm 0.2$  | $6.3 \pm 0.2$  | $16.7 \pm 0.3$           |
| Forgetting           | $13.5 \pm 1.2$ | $23.3 \pm 1.0$ | $23.3 \pm 1.1$ | $4.5 \pm 0.2$  | $15.1 \pm 0.3$ | $30.5 \pm 0.3$ | $1.6 \pm 0.1$  | $5.1 \pm 0.2$  | $15.0 \pm 0.3$           |
| <b>MTT</b>           | $45.8 \pm 0.3$ | $64.7 \pm 0.5$ | $71.5 \pm 0.5$ | $23.9 \pm 1.0$ | $38.7 \pm 0.4$ | $47.3 \pm 0.1$ | $8.3 \pm 0.4$  | $20.6 \pm 0.2$ | $28.0 \pm 0.3$           |
| $+SDC$               | $46.2 \pm 0.7$ | $65.3 \pm 0.3$ | $71.8 \pm 0.5$ | $24.3 \pm 0.3$ | $38.8 \pm 0.3$ | $47.3 \pm 0.2$ | $8.5 \pm 0.2$  | $20.7 \pm 0.2$ | $28.0 \pm 0.2$           |
| <b>FTD</b>           | $46.7 \pm 0.7$ | $65.2 \pm 0.5$ | $72.2 \pm 0.1$ | $25.1 \pm 0.4$ | $42.5 \pm 0.1$ | $50.3 \pm 0.3$ | $10.9 \pm 0.1$ | $21.8 \pm 0.3$ |                          |
| $+SDC$               | $47.2 \pm 0.7$ | $66.4 \pm 0.4$ | $73.3 \pm 0.4$ | $25.4 \pm 0.3$ | $42.6 \pm 0.1$ | $50.5 \pm 0.3$ | $11.2 \pm 0.1$ | $22.2 \pm 0.2$ | $\overline{\phantom{0}}$ |
| <b>TESLA</b>         | $47.4 \pm 0.3$ | $65.0 \pm 0.7$ | $71.4 \pm 0.5$ | $23.9 \pm 0.3$ | $35.8 \pm 0.7$ | $44.9 \pm 0.4$ |                |                |                          |
| $+SDC$               | $47.9 \pm 0.7$ | $65.3 \pm 0.4$ | $71.8 \pm 0.2$ | $24.2 \pm 0.2$ | $35.9 \pm 0.2$ | $45.0 \pm 0.4$ | -              | -              | -                        |
| <b>DATM</b>          | $46.1 \pm 0.5$ | $66.4 \pm 0.6$ | $75.9 \pm 0.3$ | $27.7 \pm 0.3$ | $47.6 \pm 0.2$ | $52.1 \pm 0.1$ | $17.1 \pm 0.3$ | $30.1 \pm 0.3$ | $39.7 \pm 0.1$           |
| $+SDC$               | $46.4 \pm 0.4$ | $66.6 \pm 0.4$ | $76.1 \pm 0.2$ | $28.0 \pm 0.2$ | $47.8 \pm 0.2$ | $52.5 \pm 0.2$ | $17.4 \pm 0.2$ | $30.7 \pm 0.2$ | $39.9 \pm 0.2$           |
| <b>Whole Dataset</b> |                | $84.8 \pm 0.1$ |                |                | $56.2 \pm 0.3$ |                |                | $37.6 \pm 0.4$ |                          |

<span id="page-7-0"></span>Table 2. Comparison of test accuracy (%) results of TM-based dataset distillation methods w/ and w/o SDC on CIFAR-10, CIFAR-100, and Tiny ImageNet datasets.

<span id="page-7-1"></span>Table 3. Cross-architecture evaluation was conducted on the distilled dataset with (a)  $IPC = 1$  for the TM-based method (DATM) and (b) IPC = 10 for the GM-based method (DSAC), both w/ and w/o SDC. Adding SDC improves performance on unseen networks compared to current SOTA methods.

|     | Dataset       | Method      | ResNet-18 | VGG-11 | AlexNet | LeNet | MLP   |
|-----|---------------|-------------|-----------|--------|---------|-------|-------|
| (a) | CIFAR-10      | <b>DATM</b> | 29.62     | 25.12  | 19.38   | 23.41 | 23.08 |
|     |               | <b>+SDC</b> | 31.29     | 25.99  | 23.99   | 23.65 | 22.90 |
|     | CIFAR-100     | <b>DATM</b> | 11.52     | 8.74   | 1.95    | 6.71  | 6.47  |
|     |               | <b>+SDC</b> | 12.10     | 8.78   | 3.73    | 6.84  | 6.51  |
|     | Tiny ImageNet | <b>DATM</b> | 4.36      | 5.93   | 4.33    | 2.42  | 2.29  |
|     |               | <b>+SDC</b> | 4.74      | 6.45   | 4.34    | 2.79  | 2.32  |
| (b) | MNIST         | <b>DSAC</b> | 97.44     | 96.88  | 95.30   | 95.31 | 90.62 |
|     |               | <b>+SDC</b> | 97.65     | 97.13  | 95.73   | 95.67 | 90.93 |
|     | FashionMNIST  | <b>DSAC</b> | 82.17     | 82.59  | 80.73   | 79.82 | 80.09 |
|     |               | <b>+SDC</b> | 82.87     | 82.73  | 81.15   | 79.96 | 80.36 |
|     | SVHN          | <b>DSAC</b> | 70.59     | 76.44  | 49.66   | 55.98 | 39.11 |
|     |               | <b>+SDC</b> | 71.03     | 76.63  | 49.78   | 56.27 | 40.03 |

ing by finetuning regularization according to the complexity of the learned patterns. Figure [6](#page-7-2) illustrates that ASDC further improves our method within SOTA matching methods. Additional results are provided in Appendix [3.2.](#page-3-1)

## 5. Conclusion

In this study, we empirically examine the matching-based dataset distillation method in relation to *sample difficulty*, observing clear trends as measured by gradient norm. Additionally, we adapt a neural scaling law from data pruning to theoretically explain dataset distillation. Our theoretical analysis suggests that for small synthetic datasets, the optimal approach is to generate data using easier samples from the original dataset rather than harder ones. To facilitate

<span id="page-7-2"></span>Image /page/7/Figure/7 description: A bar chart shows the optimal lambda for four different distillation methods: FTD, TESLA, DSA, and DSAC. Each method has three bars representing IPC values of 1, 10, and 50. The y-axis is on a logarithmic scale and ranges from 10^-3 to 10^-1. For FTD, the optimal lambda is approximately 10^-2 for IPC=1, 1.5x10^-3 for IPC=10, and 2x10^-4 for IPC=50. For TESLA, the optimal lambda is approximately 9x10^-3 for IPC=1, 1.5x10^-3 for IPC=10, and 2x10^-4 for IPC=50. For DSA, the optimal lambda is approximately 4x10^-3 for IPC=1, 5x10^-4 for IPC=10, and 2x10^-4 for IPC=50. For DSAC, the optimal lambda is approximately 10^-1 for IPC=1, 3x10^-3 for IPC=10, and 1.3x10^-3 for IPC=50.

Figure 5. Optimal  $\lambda$  values for various matching-based distillation methods with SDC, performed on datasets with different IPC values.

Image /page/7/Figure/9 description: The image displays two line graphs, labeled (a) and (b), illustrating the relationship between training completion and accuracy. Graph (a) shows three lines representing DATM+ASDC (red circles), DATM+SDC (orange diamonds), and DATM (blue squares). The accuracy ranges from approximately 25.5% to 28.2%, with training completion on the x-axis from 10% to 100%. Graph (b) shows three lines representing DSAC+ASDC (red circles), DSAC+SDC (orange diamonds), and DSAC (blue squares). The accuracy in this graph ranges from approximately 70% to 73%, with training completion on the x-axis from 10% to 100%.

Figure 6. Flexibly adjusting sample difficulty correction with an adaptive increase in  $\lambda$  results in higher accuracy compared to standard SDC and baseline methods. We present the results of (a) DATM and (b) DSAC.

this, we propose a simplicity-centric regularization method, termed *Sample Difficulty Correction* (SDC), aimed at improving synthetic data quality by predominantly utilizing easier samples in the data generation process. This method can be easily incorporated to existing matching-based methods, and can be implemented with a few lines of code. Experimental results underscore the importance of proper regularization within the optimization process for dataset distillation. We anticipate that this work will deepen the theoretical understanding of dataset distillation.

# References

- <span id="page-8-1"></span>[1] Amro Abbas, Kushal Tirumala, Dániel Simig, Surya Ganguli, and Ari S Morcos. Semdedup: Data-efficient learning at web-scale through semantic deduplication. *arXiv preprint arXiv:2303.09540*, 2023. [1](#page-0-1)
- <span id="page-8-9"></span>[2] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [1](#page-0-1)
- <span id="page-8-12"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-5) [6](#page-5-4)
- <span id="page-8-4"></span>[4] Dingfan Chen, Raouf Kerkouche, and Mario Fritz. Private set generation with discriminative information. *Advances in Neural Information Processing Systems*, 35:14678–14690, 2022. [1](#page-0-1)
- <span id="page-8-23"></span>[5] Jongwon Choi, Kwang Moo Yi, Jihoon Kim, Jinho Choo, Byoungjip Kim, Jinyeop Chang, Youngjune Gwon, and Hyung Jin Chang. Vab-al: Incorporating class imbalance and difficulty with variational bayes for active learning. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 6749–6758, 2021. [3](#page-2-5)
- <span id="page-8-5"></span>[6] Ming-Yu Chung, Sheng-Yen Chou, Chia-Mu Yu, Pin-Yu Chen, Sy-Yen Kuo, and Tsung-Yi Ho. Rethinking backdoor attacks on dataset distillation: A kernel method perspective. *arXiv preprint arXiv:2311.16646*, 2023. [1](#page-0-1)
- <span id="page-8-2"></span>[7] Cody Coleman, Christopher Yeh, Stephen Mussmann, Baharan Mirzasoleiman, Peter Bailis, Percy Liang, Jure Leskovec, and Matei Zaharia. Selection via proxy: Efficient data selection for deep learning. *arXiv preprint arXiv:1906.11829*, 2019. [1](#page-0-1)
- <span id="page-8-13"></span>[8] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-5) [6](#page-5-4)
- <span id="page-8-17"></span>[9] Peng Cui, Dan Zhang, Zhijie Deng, Yinpeng Dong, and Jun Zhu. Learning sample difficulty from pre-trained models for reliable prediction. *Advances in Neural Information Processing Systems*, 36, 2024. [1,](#page-0-1) [3](#page-2-5)
- <span id="page-8-20"></span>[10] Li Deng. The mnist database of handwritten digit images for machine learning research. *IEEE Signal Processing Magazine*, 29(6):141–142, 2012. [2,](#page-1-2) [6](#page-5-4)
- <span id="page-8-6"></span>[11] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, pages 5378–5396. PMLR, 2022. [1](#page-0-1)
- <span id="page-8-14"></span>[12] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of*

*the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3749–3758, 2023. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-5) [6,](#page-5-4) [7](#page-6-1)

- <span id="page-8-3"></span>[13] Amirata Ghorbani and James Zou. Data shapley: Equitable valuation of data for machine learning. In *International conference on machine learning*, pages 2242–2251. PMLR, 2019. [1](#page-0-1)
- <span id="page-8-8"></span>[14] Jianyang Gu, Kai Wang, Wei Jiang, and Yang You. Summarizing stream data for memory-restricted online continual learning. *arXiv preprint arXiv:2305.16645*, 2023. [1](#page-0-1)
- <span id="page-8-15"></span>[15] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-5) [6,](#page-5-4) [7](#page-6-1)
- <span id="page-8-24"></span>[16] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [7](#page-6-1)
- <span id="page-8-0"></span>[17] Jared Kaplan, Sam McCandlish, Tom Henighan, Tom B Brown, Benjamin Chess, Rewon Child, Scott Gray, Alec Radford, Jeffrey Wu, and Dario Amodei. Scaling laws for neural language models. *arXiv preprint arXiv:2001.08361*, 2020. [1](#page-0-1)
- <span id="page-8-10"></span>[18] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022. [1](#page-0-1)
- <span id="page-8-21"></span>[19] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [2,](#page-1-2) [6](#page-5-4)
- <span id="page-8-25"></span>[20] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Advances in neural information processing systems*, 25, 2012. [7](#page-6-1)
- <span id="page-8-22"></span>[21] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [2,](#page-1-2) [6](#page-5-4)
- <span id="page-8-26"></span>[22] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998. [7](#page-6-1)
- <span id="page-8-11"></span>[23] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-5) [6](#page-5-4)
- <span id="page-8-18"></span>[24] Ming Li, Lichang Chen, Jiuhai Chen, Shwai He, Jiuxiang Gu, and Tianyi Zhou. Selective reflection-tuning: Studentselected data recycling for llm instruction-tuning. *arXiv preprint arXiv:2402.10110*, 2024. [1,](#page-0-1) [3](#page-2-5)
- <span id="page-8-19"></span>[25] Xinyu Lin, Wenjie Wang, Yongqi Li, Shuo Yang, Fuli Feng, Yinwei Wei, and Tat-Seng Chua. Data-efficient fine-tuning for llm-based recommendation. *arXiv preprint arXiv:2401.17197*, 2024. [1](#page-0-1)
- <span id="page-8-7"></span>[26] Noel Loo, Ramin Hasani, Mathias Lechner, Alexander Amini, and Daniela Rus. Understanding reconstruction attacks with the neural tangent kernel and dataset distillation. *arXiv preprint arXiv:2302.01428*, 2023. [1](#page-0-1)
- <span id="page-8-16"></span>[27] Adyasha Maharana, Prateek Yadav, and Mohit Bansal. D2 pruning: Message passing for balancing diversity and dif-

ficulty in data pruning. *arXiv preprint arXiv:2310.07931*, 2023. [1,](#page-0-1) [3](#page-2-5)

- <span id="page-9-13"></span>[28] Javier Maroto and Pascal Frossard. Puma: margin-based data pruning. *arXiv preprint arXiv:2405.06298*, 2024. [1,](#page-0-1) [3](#page-2-5)
- <span id="page-9-6"></span>[29] Wojciech Masarczyk and Ivona Tautkute. Reducing catastrophic forgetting with learning on synthetic data. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops*, pages 252–253, 2020. [1](#page-0-1)
- <span id="page-9-20"></span>[30] Kristof Meding, Luca M. Schulze Buschoff, Robert Geirhos, and Felix A. Wichmann. Trivial or impossible – dichotomous data difficulty masks model differences (on imagenet and beyond), 2022. [3,](#page-2-5) [4](#page-3-2)
- <span id="page-9-9"></span>[31] Dmitry Medvedev and Alexander D'yakonov. Learning to generate synthetic training data using gradient matching and implicit differentiation. In *International Conference on Analysis of Images, Social Networks and Texts*, pages 138– 150. Springer, 2021. [1](#page-0-1)
- <span id="page-9-21"></span>[32] Marc Mézard, Giorgio Parisi, and Miguel Angel Virasoro. *Spin glass theory and beyond: An Introduction to the Replica Method and Its Applications*. World Scientific Publishing Company, 1987. [4](#page-3-2)
- <span id="page-9-18"></span>[33] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Baolin Wu, Andrew Y Ng, et al. Reading digits in natural images with unsupervised feature learning. In *NIPS workshop on deep learning and unsupervised feature learning*, page 7. Granada, Spain, 2011. [2,](#page-1-2) [6](#page-5-4)
- <span id="page-9-16"></span>[34] Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training, 2023. [1,](#page-0-1) [3](#page-2-5)
- <span id="page-9-7"></span>[35] Andrea Rosasco, Antonio Carta, Andrea Cossu, Vincenzo Lomonaco, and Davide Bacciu. Distilled replay: Overcoming forgetting through synthetic samples, 2021. [1](#page-0-1)
- <span id="page-9-0"></span>[36] Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *arXiv preprint arXiv:2301.04272*, 2023. [1](#page-0-1)
- <span id="page-9-23"></span>[37] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [7](#page-6-1)
- <span id="page-9-14"></span>[38] Ben Sorscher, Robert Geirhos, Shashank Shekhar, Surya Ganguli, and Ari Morcos. Beyond neural scaling laws: beating power law scaling via data pruning. *Advances in Neural Information Processing Systems*, 35:19523–19536, 2022. [1,](#page-0-1) [2,](#page-1-2) [4,](#page-3-2) [5,](#page-4-2) [6,](#page-5-4) [7](#page-6-1)
- <span id="page-9-10"></span>[39] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *International Conference on Machine Learning*, pages 9206–9216. PMLR, 2020. [1](#page-0-1)
- <span id="page-9-15"></span>[40] Haoru Tan, Sitong Wu, Fei Du, Yukang Chen, Zhibin Wang, Fan Wang, and Xiaojuan Qi. Data pruning via moving-onesample-out. *Advances in Neural Information Processing Systems*, 36, 2024. [1,](#page-0-1) [3](#page-2-5)
- <span id="page-9-3"></span>[41] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018. [1,](#page-0-1) [6](#page-5-4)

- <span id="page-9-4"></span>[42] Shaobo Wang, Xiangqi Jin, Ziming Wang, Jize Wang, Jiajun Zhang, Kaixin Li, Zichen Wen, Zhong Li, Conghui He, Xuming Hu, and Linfeng Zhang. Data whisperer: Efficient data selection for task-specific llm fine-tuning via few-shot in-context learning. *Annual Meeting of the Association for Computational Linguistics*, 2025. [1](#page-0-1)
- <span id="page-9-1"></span>[43] Shaobo Wang, Yicun Yang, Zhiyuan Liu, Chenghao Sun, Xuming Hu, Conghui He, and Linfeng Zhang. Dataset distillation with neural characteristic function: A minmax perspective. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2025. [1](#page-0-1)
- [44] Shaobo Wang, Yantai Yang, Shuaiyu Zhang, Chenghao Sun, Weiya Li, Xuming Hu, and Linfeng Zhang. DRUPI: Dataset reduction using privileged information. In *The Future of Machine Learning Data Practices and Repositories at ICLR 2025*, 2025.
- <span id="page-9-2"></span>[45] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1](#page-0-1)
- <span id="page-9-22"></span>[46] Max Welling. Herding dynamical weights to learn. In *Proceedings of the 26th annual international conference on machine learning*, pages 1121–1128, 2009. [6](#page-5-4)
- <span id="page-9-17"></span>[47] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashionmnist: a novel image dataset for benchmarking machine learning algorithms, 2017. [2,](#page-1-2) [6](#page-5-4)
- <span id="page-9-19"></span>[48] Shuai Xie, Zunlei Feng, Ying Chen, Songtao Sun, Chao Ma, and Mingli Song. Deal: Difficulty-aware active learning for semantic segmentation. In *Proceedings of the Asian conference on computer vision*, 2020. [3](#page-2-5)
- <span id="page-9-5"></span>[49] Furui Xu\*, Shaobo Wang\*, Chenghao Sun, Jiajun Zhang, and Linfeng Zhang. Rethink dataset pruning from a generalization perspective. *The Future of Machine Learning Data Practices and Repositories at ICLR 2025*, 2025. [1](#page-0-1)
- <span id="page-9-8"></span>[50] Enneng Yang, Li Shen, Zhenyi Wang, Tongliang Liu, and Guibing Guo. An efficient dataset condensation plugin and its application to continual learning. *Advances in Neural Information Processing Systems*, 36, 2023. [1](#page-0-1)
- <span id="page-9-11"></span>[51] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-5) [6](#page-5-4)
- <span id="page-9-12"></span>[52] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [1,](#page-0-1) [2,](#page-1-2) [6](#page-5-4)

## Not All Samples Should Be Utilized Equally: Towards Understanding and Improving Dataset Distillation

Supplementary Material

## 1. More Result on the Relationships between Sample Difficulty, Gardient Norm, and Loss

In this section, we present further findings on the relationships between sample difficulty  $\chi(x, y; \Theta_t)$ , gradient norm GraDN( $x, y$ ;  $\Theta_t$ ), and the average validation loss for each sample  $(x, y)$  across a range of models characterized by  $\theta_t \in \Theta_t$ . The experiments were conducted using ResNet-18 on CIFAR-10 and ResNet-34 on CIFAR-100. Each metric was evaluated across 20 pretrained models. We randomly selected 1000 samples for each category in CIFAR-10, and 100 samples for each category in CIFAR-100. For CIFAR-100, 10 categories for visualization were randomly selected for visualization purposes. As shown in Figure [7,](#page-11-0) Figure [8,](#page-11-1) Figure [9,](#page-11-2) Figure [10,](#page-12-0) Figure [11,](#page-12-1) Figure [12,](#page-12-2) it reveals a significant positive correlation between sample difficulty, gradient norm and loss.

### 2. More Details of Experiments

<span id="page-10-1"></span>

### 2.1. Parameter Tables

### 2.1.1 GM-based Methods

Regarding the GM-based methods, Table [4](#page-13-0) provides the corresponding  $\lambda$  values after applying SDC. All results are obtained from a single experiment, and evaluated 20 times. Baseline results are obtained using identical configurations with the original methods' implementations (please refer to DC and  $DSA<sup>1</sup>$  $DSA<sup>1</sup>$  $DSA<sup>1</sup>$ , and DCC<sup>[2](#page-10-3)</sup>). Experiments with our SDC share consistent hyperparameters with the corresponding baselines.

## 2.1.2 TM-based Methods

The hyperparameters used in our TM-based methods differ slightly from the original methods (see original implemen-tations of MTT<sup>[3](#page-10-4)</sup>, DATM<sup>[4](#page-10-5)</sup>, TESLA<sup>[5](#page-10-6)</sup>, and FTD<sup>[6](#page-10-7)</sup>), particularly in terms of synthesis steps, number of evaluations, and evaluation interval. Our baseline results used the settings in Table [5,](#page-13-1) Table [6,](#page-14-0) Table [7](#page-14-1) and Table [8.](#page-15-0) The experiments

of applying SDC were conducted in the same setting as in the baselines. In Table [6,](#page-14-0) and Table [7,](#page-14-1) we report the optimal hyperparameters using the ConvNetD3 network. All combinations in Table [7](#page-14-1) and Table [8](#page-15-0) used the ZCA.

### 2.2. Limitation

Computational Cost: Similar to other methods, we have not yet addressed the large computational cost associated with the dataset distillation. Our experiments were conducted on a mix of RTX 2080 Ti, RTX 3090, RTX 4090, NVIDIA A100, and NVIDIA V100 GPUs. The cost in terms of computational resources and time remains significant for large datasets and high IPC experiments. For example, distilling Tiny ImageNet using DATM with IPC = 1 requires approximately 150GB of GPU memory, and for  $IPC = 50$ , a single experiment can take nearly 24 hours to complete.

**Hyperparameter Tuning:** The selection of the  $\lambda$  requires manual adjustment, which may involve additional costs. The extensive training durations and substantial GPU memory requirements make it challenging to conduct exhaustive experiments with multiple  $\lambda$  values to identify the global optimum, given our computational resource limitations. By exploring a wider range of  $\lambda$  values, it is possible to obtain better results.

<span id="page-10-0"></span>

## 2.3. Pseudocodes of adding SDC on Matching-based Distillation Methods

We provide detailed pseudocodes for GM-based methods and TM-based methods. We take DC as the standard GMbased method, and MTT as the standard TM-based method. The detailed pseudocodes are shown in Algorithm [1](#page-13-2) for GM-based methods and Algorithm [2](#page-13-3) for TM-based methods.

## 3. Exploring the Effectiveness of SDC in Additional Experiments

## 3.1. More Results on the Cross-architecture Evaluation

To evaluate the performance of distilled datasets on different network architectures using SDC (marked as +SDC in the tables) and other methods (DATM and DSAC), we conducted cross-architecture evaluation experiments. We compared the effects of DATM and SDC on CIFAR-10, CIFAR-100, and Tiny ImageNet datasets, and the effects of DSAC and SDC on MNIST, FashionMNIST, and SVHN datasets.

<span id="page-10-2"></span> $1$ [https : / / github . com / VICO - UoE /](https://github.com/VICO-UoE/DatasetCondensation) [DatasetCondensation](https://github.com/VICO-UoE/DatasetCondensation)

<span id="page-10-4"></span><span id="page-10-3"></span><sup>2</sup><https://github.com/Saehyung-Lee/DCC>

 $3$ [https : / / github . com / GeorgeCazenavette / mtt](https://github.com/GeorgeCazenavette/mtt-distillation)  [distillation](https://github.com/GeorgeCazenavette/mtt-distillation)

<span id="page-10-5"></span><sup>4</sup><https://github.com/NUS-HPC-AI-Lab/DATM>

<span id="page-10-6"></span><sup>5</sup><https://github.com/justincui03/tesla>

<span id="page-10-7"></span><sup>6</sup><https://github.com/AngusDujw/FTD-distillation>

<span id="page-11-0"></span>Image /page/11/Figure/0 description: This image displays a grid of scatter plots, with each plot representing a different class. The top row contains plots for Class 1, Class 2, Class 3, Class 4, and Class 5. The bottom row contains plots for Class 6, Class 7, Class 8, Class 9, and Class 10. All plots share the same y-axis label, "Sample difficulty", ranging from 0.0 to 0.7 (or 0.8 in some plots). The x-axis for all plots is labeled "Gradient norm". Each plot shows a collection of colored dots, with the color corresponding to the class indicated in the plot title and a legend on the right side of the image. The legend maps specific colors to class numbers from 0 to 9. The data points in each plot generally show an increasing trend of sample difficulty with increasing gradient norm, though the density and spread of points vary across classes.

Figure 7. The statistical relationship between sample difficulty  $\chi(x, y; \Theta_t)$ , gradient norm GraDN(x, y;  $\Theta_t$ ) for each sample  $(x, y)$  on a series of ResNet-18 models with parameters  $\theta_t \in \Theta_t$  on CIFAR-10. 1000 samples were randomly selected for each category.

<span id="page-11-1"></span>Image /page/11/Figure/2 description: The image displays a grid of scatter plots, with each plot representing a different class. The top row contains plots for Class 1 through Class 5, and the bottom row contains plots for Class 6 through Class 10. Each plot has 'Loss' on the x-axis and 'Gradient norm' on the y-axis. The data points in each plot are colored according to their class, with a legend on the right indicating the color mapping for Class 0 through Class 9. The plots show a general trend of increasing gradient norm with increasing loss, with variations in the spread and density of points for each class.

Figure 8. The statistical relationship between gradient norm GraDN(x, y;  $\Theta_t$ ) and average validation loss for each sample (x, y) on a series of ResNet-18 models with parameters  $\theta_t \in \Theta_t$  on CIFAR-10. 1000 samples were randomly selected for each category.

<span id="page-11-2"></span>Image /page/11/Figure/4 description: This image displays a grid of scatter plots, with each plot representing a different class of data. The plots are arranged in two rows and five columns. The x-axis of each plot is labeled "Sample difficulty", and the y-axis is labeled "Loss". A legend on the right side of the image associates different colored dots with classes 0 through 9. The first row contains plots for Class 1 (red dots), Class 2 (orange dots), Class 3 (yellow-green dots), Class 4 (green dots), and Class 5 (teal dots). The second row contains plots for Class 6 (cyan dots), Class 7 (blue dots), Class 8 (purple dots), Class 9 (pink dots), and Class 10 (red-pink dots). Each scatter plot shows a general trend where loss increases with sample difficulty, though the density and range of values vary between classes.

Figure 9. The statistical relationship between sample difficulty  $\chi(x, y; \Theta_t)$  and gradient norm GraDN(x, y;  $\Theta_t$ ) for each sample  $(x, y)$  on a series of ResNet-18 models with parameters  $\theta_t \in \Theta_t$  on CIFAR-10. 1000 samples were randomly selected for each category.

Finally, we further evaluated the performance differences between DSAC and SDC methods on MNIST, Fashion-MNIST, and SVHN datasets with IPC = 50. The crossarchitecture evaluation experiments for DSAC and DATM, as well as the use of the SDC method on datasets with IPC = 1 of DATM and IPC = 10 of DSAC, can be found in Table [3.](#page-7-1)

The results of evaluating distilled datasets learned through DATM and SDC methods on CIFAR-10, CIFAR-100, and Tiny ImageNet datasets using ResNet-18, VGG-11, AlexNet, LeNet, and MLP networks are presented in Table [9.](#page-15-1) For instance, on the CIFAR-100 dataset, the accuracy of the VGG-11 network improved by 1.46%. It can be

<span id="page-12-0"></span>Image /page/12/Figure/0 description: The image displays a grid of scatter plots, with each plot representing a different class (Class 1 through Class 10). The y-axis for all plots is labeled "Sample difficulty" and ranges from 0.0 to 0.7 or 0.8. The x-axis for all plots is labeled "Gradient norm" and ranges from approximately 0 to 50. Each plot shows a collection of colored dots, with the color corresponding to the class indicated in the plot. A legend on the right side of the image associates specific colors with class numbers (Class 0 through Class 9). The plots generally show a positive correlation between gradient norm and sample difficulty, meaning that as the gradient norm increases, the sample difficulty also tends to increase. Some classes exhibit a more linear relationship, while others show more scattered data points or distinct clusters.

Figure 10. The statistical relationship between sample difficulty  $\chi(x, y; \Theta_t)$ , gradient norm GraDN $(x, y; \Theta_t)$  for each sample  $(x, y)$  on a series of ResNet-34 models with parameters  $\theta_t \in \Theta_t$  on CIFAR-100. 100 samples were randomly selected for each category.

<span id="page-12-1"></span>Image /page/12/Figure/2 description: This image displays a grid of scatter plots, with each plot representing a different class from 0 to 10. The x-axis for all plots is labeled 'Loss', and the y-axis for all plots is labeled 'Gradient norm'. Each scatter plot shows the relationship between loss and gradient norm for a specific class, with data points colored according to their class as indicated by the legend on the right. For example, Class 0 points are red, Class 1 are orange, Class 2 are yellow, Class 3 are light green, Class 4 are green, Class 5 are teal, Class 6 are cyan, Class 7 are blue, Class 8 are purple, and Class 9 are pink. The plots are arranged in two rows, with Classes 0 through 5 in the top row and Classes 6 through 10 in the bottom row. The title above the plots indicates that they are based on 100 samples randomly selected for each category.

Figure 11. The statistical relationship between gradient norm GraDN $(x, y; \Theta_t)$  and average validation loss for each sample  $(x, y)$  on a series of ResNet-34 models with parameters  $\theta_t \in \Theta_t$  on CIFAR-100. 100 samples were randomly selected for each category.

<span id="page-12-2"></span>Image /page/12/Figure/4 description: The image displays a grid of scatter plots, with each plot representing a different class (Class 1 through Class 10). The plots are arranged in two rows. The top row contains plots for Class 1, Class 2, Class 3, Class 4, and Class 5. The bottom row contains plots for Class 6, Class 7, Class 8, Class 9, and Class 10. Each scatter plot has 'Loss' on the y-axis and 'Sample difficulty' on the x-axis. A legend on the right side of the image associates different colored dots with classes ranging from Class 0 to Class 9. The data points in each plot show a general trend of increasing loss with increasing sample difficulty, though the density and spread of points vary across classes. For example, Class 1 shows a clear positive correlation, while Class 6 and Class 7 show a more scattered distribution with a general upward trend.

Figure 12. The statistical relationship between sample difficulty  $\chi(x, y; \Theta_t)$  and gradient norm GraDN(x, y;  $\Theta_t$ ) for each sample  $(x, y)$ on a series of ResNet-34 models with parameters  $\theta_t \in \Theta_t$  on CIFAR-100. 100 samples were randomly selected for each category.

observed that the performance after applying SDC is generally better than DATM.

In our evaluation of distilled datasets learned through DSAC and SDC methods on MNIST, FashionMNIST, and SVHN datasets using the same network architectures, as detailed in Table [10,](#page-16-0) the results show that performance after applying SDC is superior to the DSAC method across datasets and network architectures. For example, on the MNIST dataset, the accuracy of the VGG-11 network improved by 1.37%, and on the SVHN dataset, the accuracy of the ResNet-18 improved by 0.61%.

Additionally, similar evaluation results on MNIST, FashionMNIST, and SVHN datasets with an IPC value of 50 are summarized in Table [11.](#page-16-1) For example, on the SVHN dataset, the accuracy of the LeNet network improved by 1.0%. It can be seen that with an increase in IPC value,

Table 4. The  $\lambda$  used for GM-based methods

<span id="page-13-0"></span>

| <b>Dataset</b> | <b>MNIST</b>  |                                                                        |    | FashionMNIST                              |  |  | <b>SVHN</b> |     |      |
|----------------|---------------|------------------------------------------------------------------------|----|-------------------------------------------|--|--|-------------|-----|------|
| $IPC$ 1        |               | 10                                                                     | 50 | $\begin{array}{ccc} & 1 & 10 \end{array}$ |  |  | 50   1      | -10 | 50   |
|                | $DC \t 0.001$ | $0.0005$ $0.001$   $0.0002$ $0.001$ $0.01$   $0.001$ $0.0005$ $0.0002$ |    |                                           |  |  |             |     |      |
| <b>DSA</b>     | 0.001         | $0.00002$ $0.001$ $0.0002$ $0.002$ $0.001$ $0.005$ $0.0005$            |    |                                           |  |  |             |     | 0.01 |
| <b>DSAC</b>    | 0.001         | $0.02$ $0.02$ $0.002$ $0.02$ $0.02$ $0.02$ $0.005$ $0.02$              |    |                                           |  |  |             |     | 0.02 |

<span id="page-13-1"></span>Table 5. Optimal hyperparameters for MTT. A synthesis batch size of "-" means that we used the full support set at each synthesis step.

| Dataset          | Model     | IPC | ZCA | Synthetic<br>Steps<br>(N) | Expert<br>Epochs<br>(M†) | Max Start<br>Epoch<br>(T+) | Synthetic<br>Batch<br>Size | Learning<br>Rate<br>(Pixels) | Learning<br>Rate<br>(Step Size) | Starting<br>Synthetic<br>Step Size | Num<br>Eval | Eval<br>Iteration | λ      |
|------------------|-----------|-----|-----|---------------------------|--------------------------|----------------------------|----------------------------|------------------------------|---------------------------------|------------------------------------|-------------|-------------------|--------|
| CIFAR-10         | ConvNetD3 | 1   | Y   | 50                        | 2                        | 2                          | -                          | 102                          | 10-7                            | 10-2                               | 5           | 100               | 0.0005 |
| CIFAR-10         | ConvNetD3 | 10  | Y   | 30                        | 2                        | 20                         | -                          | 102                          | 10-4                            | 10-2                               | 5           | 100               | 0.02   |
| CIFAR-10         | ConvNetD3 | 50  | N   | 30                        | 2                        | 40                         | -                          | 103                          | 10-5                            | 10-3                               | 5           | 100               | 0.0002 |
| CIFAR-100        | ConvNetD3 | 1   | Y   | 20                        | 3                        | 20                         | -                          | 103                          | 10-5                            | 10-2                               | 5           | 100               | 0.001  |
| CIFAR-100        | ConvNetD3 | 10  | N   | 20                        | 2                        | 20                         | -                          | 103                          | 10-5                            | 10-2                               | 5           | 100               | 0.02   |
| CIFAR-100        | ConvNetD3 | 50  | Y   | 80                        | 2                        | 40                         | 125                        | 103                          | 10-5                            | 10-2                               | 5           | 100               | 0.002  |
| Tiny<br>ImageNet | ConvNetD4 | 1   | N   | 10                        | 2                        | 10                         | -                          | 104                          | 10-4                            | 10-2                               | 5           | 100               | 0.005  |
| Tiny<br>ImageNet | ConvNetD4 | 10  | N   | 20                        | 2                        | 40                         | 200                        | 104                          | 10-4                            | 10-2                               | 3           | 200               | 0.02   |
| Tiny<br>ImageNet | ConvNetD4 | 50  | N   | 20                        | 2                        | 40                         | 300                        | 104                          | 10-4                            | 10-2                               | 3           | 200               | 0.02   |

<span id="page-13-2"></span>Algorithm 1 Gradient Matching with Sample Difficulty Correction

- **Input:** Training set  $\mathcal{D}_{\text{real}}$ , category set C, classification crossentropy loss  $\mathcal{L}$ , probability distribution for weights  $P_{\theta}$ , distance metric **D**, regularization coefficient  $\lambda$ , number of steps T, learning rate  $\eta$  for network parameters.
- 1: Initialize distilled data  $\mathcal{D}_{syn} \sim \mathcal{D}_{real}$ .
- 2: for each distillation step... do
- 3: ▷ Initialize network  $\theta_0 \sim P_\theta$
- 4: for  $t = 0 \rightarrow T$  do
- 5: for  $c = 0 \rightarrow C 1$  do
- 6: ⊳ Sample a mini-batch of distilled images:  $\mathcal{B}_{\text{real}}^c$  ~  $\mathcal{D}_{\mathsf{real}}^c$

7: ⊳ Sample a mini-batch of original images:  $\mathcal{B}_{syn}^c$  ~  $\mathcal{D}^c_\mathsf{syn}$ 

8: 
$$
\triangleright
$$
 Compute  $\mathcal{L}_{\mathcal{B}^c_{\text{syn}}}$  =  $\mathbb{E}_{(x,y)\in \tilde{\mathcal{B}}^c_{\text{real}}}$  [ $\mathcal{L}(x, y; \theta_t)$ ],  
 $\mathcal{L}_{\mathcal{B}^c_{\text{real}}} = \mathbb{E}_{(x,y)\in \tilde{\mathcal{B}}^c_{\text{real}}}$  [ $\mathcal{L}(x, y; \theta_t)$ ]

9: 
$$
-\sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}} \sum_{\text{real}}
$$

- 10:  $\bigcup_{\text{real}} \bigcup_{\text{real}} \bigcup_{\text{total}} \bigcup_{\text{bound}} \bigcup_{\text{sym}} \bigcup_{\text{in}} \bigcup_{\text{in}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}} \bigcup_{\text{out}}$
- 11: end for
- 12:  $\triangleright$  Update network *w.r.t.* classification loss:  $\theta_{t+1}$  $θ_t - η∇L<sub>Dsyn</sub>(θ<sub>t</sub>)$

## 13: end for

- 14: end for
- **Output:** distilled data  $\mathcal{D}_{syn}$

the performance after applying SDC remains better in most cases, further demonstrating the superiority of the SDC method in dataset distillation.

<span id="page-13-3"></span>

## Algorithm 2 Trajectory Matching with Sample Difficulty Correction

- **Input:** Set of expert parameter trajectories trained on  $\mathcal{D}_{\text{real}}\lbrace \tau_i^* \rbrace$ , the number of updates between starting and target expert params M, the number of updates to student network per distillation step  $N$ , differentiable augmentation function  $A$ , maximum start epoch  $T^+ < T$ , learning rate  $\eta$  for network parameters, regularization coefficient  $\lambda$ , classification cross-entropy  $\cos \mathcal{L}$ .
- 1: Initialize distilled data  $\mathcal{D}_{syn} \sim \mathcal{D}_{real}$ .
- 2: for each distillation step... do
- 3:  $\triangleright$  Sample expert trajectory:  $\tau^* \sim {\tau_i^*}$  with  $\tau^* =$  $\{\boldsymbol{\theta}_t^{\mathcal{D}_{\mathsf{real}}}\}_0^T$
- 4:  $\triangleright$  Choose random start epoch,  $t \leq T^+$
- 5:  $\triangleright$  Initialize student network with expert params:  $\theta_t^{\mathcal{D}_{syn}}$  :=  $\theta_t^{\mathcal{D}_{\mathsf{real}}}$
- 6: **for**  $n = 0 \rightarrow N 1$  **do**
- 7: ▷ Sample a mini-batch of distilled images:  $B_{syn}$  ∼  $\mathcal{D}_{syn}$

8: 
$$
\triangleright
$$
 Update student network w.r.t. classification loss:  $\theta_{t+n+1}^{\mathcal{D}_{syn}} = \theta_{t+n}^{\mathcal{D}_{syn}} - \eta \nabla \mathcal{L}_{\mathcal{A}(B_{syn})}(\theta_{t+n}^{\mathcal{D}_{syn}})$ 

$$
9: \qquad \textbf{end for}
$$

10: ▷ Compute loss between ending student and expert params:  $L = \frac{\|\theta_{t+N}^{\mathcal{D}_{syn}} - \theta_{t+M}^{\mathcal{D}_{real}}\|_2^2}{\| \theta_{t+N}^{\mathcal{D}_{real}} - \theta_{t+M}^{\mathcal{D}_{real}}\|_2^2}$ 

2

$$
\text{params: } L = \frac{\|\theta_{t+N} - \theta_{t+N} + \delta d\|_2}{\|\theta_{t+N}^{\mathcal{D}_{\text{real}}} - \theta_{t+N}^{\mathcal{D}_{\text{real}}}\|_2^2} + \lambda \left\| \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{\text{syn}}} \right\|_2^2
$$
  
11: ▶ Update  $\mathcal{D}_{\text{syn}} \ w.r.t. L$ 

12: end for

**Output:** distilled data  $\mathcal{D}_{syn}$ 

## 3.2. More Results on the Adaptive Sample Difficulty Correction

The dynamic adjustment of SDC, when applied to both DSA and FTD, consistently outperforms both the baseline

<span id="page-14-0"></span>Table 6. Optimal hyperparameters for TESLA. A synthesis batch size of "-" means that we used the full support set at each synthesis step.

| Dataset     | <b>IPC</b> | Matching<br><b>Steps</b> | Teacher<br>Epochs                                                                                                                                                                                                                                                                                                                                                                                                | Max Start<br>Epoch | Synthetic<br>Batch<br><b>Size</b> | Learning Rate<br>(Pixels)                    | Learning<br>Rate<br>(Step Size)     | <b>Starting</b><br>Synthetic<br>Step Size | <b>ZCA</b>  | $\lambda$                |
|-------------|------------|--------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------|-----------------------------------|----------------------------------------------|-------------------------------------|-------------------------------------------|-------------|--------------------------|
| CIFAR-10    | 10<br>50   | 50<br>30<br>26           | $\mathfrak{D}_{1}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{(1)} = \mathfrak{D}_{2}^{$<br>↑ | 20<br>40           | $\overline{\phantom{0}}$          | 10 <sup>2</sup><br>10 <sup>2</sup><br>$10^3$ | $10^{-7}$<br>$10^{-4}$<br>$10^{-5}$ | $10^{-2}$<br>$10^{-2}$<br>$10^{-3}$       | v<br>v<br>N | 0.01<br>0.002<br>0.02    |
| $CIFAR-100$ | 10<br>50   | 20<br>13<br>50           | 3<br>κ<br>∍                                                                                                                                                                                                                                                                                                                                                                                                      | 20<br>30<br>40     | 100                               | $10^{3}$<br>$10^3$<br>$10^3$                 | $10^{-5}$<br>$10^{-5}$<br>$10^{-5}$ | $10^{-2}$<br>$10^{-2}$<br>$10^{-2}$       | v<br>N<br>v | 0.001<br>0.002<br>0.0002 |

<span id="page-14-1"></span>Table 7. Optimal hyperparameters for FTD. A synthesis batch size of '-' means that we used the full support set at each synthesis step.

| Dataset   | IPC | Synthetic<br>Step | Expert<br>Epoch | Max Start<br>Epoch | Synthetic<br>Batch<br>Size | Learning<br>Rate<br>(Pixels) | Learning<br>Rate<br>(Step Size) | Learning<br>Rate<br>(Teacher) | Balance<br>coefficient | EMA<br>Decay | λ      |
|-----------|-----|-------------------|-----------------|--------------------|----------------------------|------------------------------|---------------------------------|-------------------------------|------------------------|--------------|--------|
| CIFAR-10  | 1   | 50                | 2               | 2                  | -                          | 100                          | $10^{-7}$                       | 0.01                          | 0.3                    | 0.9999       | 0.002  |
|           | 10  | 30                | 2               | 20                 | -                          | 100                          | $10^{-5}$                       | 0.001                         | 0.3                    | 0.9995       | 0.002  |
|           | 50  | 30                | 2               | 40                 | -                          | 1000                         | $10^{-5}$                       | 0.001                         | 1                      | 0.999        | 0.0002 |
| CIFAR-100 | 1   | 40                | 3               | 20                 | -                          | 1000                         | $10^{-5}$                       | 0.01                          | 1                      | 0.9995       | 0.002  |
|           | 10  | 20                | 2               | 40                 | -                          | 1000                         | $10^{-5}$                       | 0.01                          | 1                      | 0.9995       | 0.0002 |
|           | 50  | 80                | 2               | 40                 | 1000                       | 1000                         | $10^{-5}$                       | 0.01                          | 1                      | 0.999        | 0.002  |

<span id="page-14-2"></span>Image /page/14/Figure/4 description: The image contains two line graphs side-by-side, labeled (a) and (b). Both graphs plot Accuracy on the y-axis against Training completion on the x-axis. Graph (a) shows three lines: FTD+ASDC (red circles), FTD+SDC (orange diamonds), and FTD (blue squares). The y-axis ranges from 23% to 26%, and the x-axis ranges from 20% to 100%. In graph (a), FTD+ASDC and FTD+SDC reach approximately 25.8% accuracy by 40% training completion and maintain it, while FTD reaches approximately 25.2% accuracy by 40% and stays there. Graph (b) shows three lines: DSA+ASDC (red circles), DSA+SDC (orange diamonds), and DSA (blue squares). The y-axis ranges from 68% to 71%, and the x-axis ranges from 10% to 100%. In graph (b), all three lines show fluctuating accuracy, generally increasing from around 69.5% to over 70.5% by 100% training completion, with DSA+ASDC and DSA+SDC performing slightly better overall than DSA.

Figure 13. The application of ASDC on (a) FTD and (b) DSA. Gradually increasing  $\lambda$  gives better results than the baseline and the method after applying SDC.

methods and the baseline methods with SDC applied. As shown in Figure [13,](#page-14-2) we logarithmically increased the  $\lambda$  coefficient for DSA from 0.0002 to 0.002 over 1000 steps and for FTD from 0.002 to 0.008 over 10,000 iterations. The results clearly demonstrate that ASDC yields superior performance. Flexibly adjusting the sample difficulty correction by adaptively increasing  $\lambda$  yields higher accuracy compared to the standard SDC and baseline methods.

### 3.3. Sensitivity Analysis of SDC coefficient .

In this section, we conducted extensive experiments to study the sensitivity of the hyperparameter  $\lambda$ . Specifically, we conducted experiments of DSA on SVHN dataset with IPC  $= 1$ , and DC on SVHN dataset with IPC  $= 10$ . As shown in Figure [14,](#page-14-3) the choice of  $\lambda$  is not sensitive among different

matching-based dataset distillation methods.

<span id="page-14-3"></span>Image /page/14/Figure/10 description: This image contains two bar charts, labeled (a) DSA and (b) DC. Both charts plot Accuracy (%) on the y-axis against SDC coefficient λ on the x-axis. Chart (a) shows bars with accuracies around 30% for SDC coefficients of 0.0005, 0.001, 0.002, 0.005, and 0.02. The bar for 0.005 is highlighted with a dashed box and labeled SOTA. Chart (b) shows bars with accuracies ranging from approximately 75% to 80% for SDC coefficients of 5x10^-6, 2x10^-5, 5x10^-5, 2x10^-4, and 5x10^-4. The bar for 5x10^-6 is highlighted with a dashed box and labeled SOTA.

Figure 14. Sensitivity analysis of SDC coefficient  $\lambda$  on different distillation methods. We evaluated the sensitivity across different  $\lambda$ s, and showed that the choice of  $\lambda$  did not severely affect the final test performance. (a) DSA on SVHN dataset with  $IPC = 1$  (b) DC on SVHN dataset with  $IPC = 10$ .

## 4. Analytical Theory for Dataset Distillation

In this section, we introduce a theory, adapted from data pruning [\[38\]](#page-9-14), to the context of dataset distillation within an expert-student perceptron framework, utilizing the tools of statistical mechanics. We investigate the challenge of classifying a dataset  $\mathcal{D}_{\text{real}}$  consisting of  $d_{\text{real}}$  samples  ${x_i, y_i}_{i=1,\dots,d_{\text{real}}}$ , where the inputs  $x_i \sim \mathcal{N}(0, I_d)$  are i.i.d. zero-mean, unit-variance random Gaussian variables, and the labels  $y_i = sign(\theta^{\mathcal{D}_{real}^{\top}} x_i)$  are generated by an expert perceptron  $\theta^{\mathcal{D}_{\text{real}}} \in \mathbb{R}^d$ . We assume that the expert perceptron  $\theta^{\mathcal{D}_{\text{real}}}$  is randomly drawn from a uniform distribution

Table 8. Optimal hyperparameters for DATM.

<span id="page-15-0"></span>

| Dataset       | Model     | IPC | Synthetic<br>Step | Expert<br>Epoch | Min Start<br>Epoch | Current<br>Max Start<br>Epoch | Max Start<br>Epoch | Synthetic<br>Batch Size | Learning<br>Rate<br>(Label) | Learning<br>Rate<br>(Pixels) | Num<br>Eval | Eval<br>Iteration | λ      |
|---------------|-----------|-----|-------------------|-----------------|--------------------|-------------------------------|--------------------|-------------------------|-----------------------------|------------------------------|-------------|-------------------|--------|
| CIFAR-10      | ConvNetD3 | 1   | 80                | 2               | 0                  | 4                             | 4                  | 10                      | 5                           | 100                          | 5           | 500               | 0.0002 |
| CIFAR-10      | ConvNetD3 | 10  | 80                | 2               | 0                  | 10                            | 20                 | 100                     | 2                           | 100                          | 5           | 500               | 0.0005 |
| CIFAR-10      | ConvNetD3 | 50  | 80                | 2               | 0                  | 20                            | 40                 | 500                     | 2                           | 1000                         | 5           | 500               | 0.002  |
| CIFAR-100     | ConvNetD3 | 1   | 40                | 3               | 0                  | 10                            | 20                 | 100                     | 10                          | 1000                         | 5           | 500               | 0.02   |
| Tiny ImageNet | ConvNetD4 | 1   | 60                | 2               | 0                  | 15                            | 20                 | 200                     | 10                          | 10000                        | 5           | 500               | 0.002  |
| Tiny ImageNet | ConvNetD4 | 10  | 60                | 2               | 10                 | 50                            | 50                 | 250                     | 10                          | 100                          | 3           | 500               | 0.002  |
| Tiny ImageNet | ConvNetD4 | 50  | 80                | 2               | 40                 | 70                            | 70                 | 250                     | 10                          | 100                          | 3           | 500               | 0.002  |

<span id="page-15-1"></span>Table 9. Cross-architecture evaluation. We evaluated distilled datasets with IPC = 10 learned through DATM w/ and w/o SDC on different networks.

| Dataset       | Method | ResNet-18    | VGG-11       | AlexNet      | LeNet        | MLP          |
|---------------|--------|--------------|--------------|--------------|--------------|--------------|
| CIFAR-10      | DATM   | 36.48        | 37.32        | 33.19        | 32.56        | 27.21        |
|               | +SDC   | <b>38.33</b> | <b>38.22</b> | <b>34.56</b> | <b>33.17</b> | <b>27.62</b> |
| CIFAR-100     | DATM   | 17.87        | 14.71        | 15.09        | 11.76        | 11.52        |
|               | +SDC   | <b>18.97</b> | <b>16.17</b> | <b>15.73</b> | <b>12.44</b> | <b>11.87</b> |
| Tiny ImageNet | DATM   | 6.33         | 8.67         | 6.18         | 3.65         | 3.34         |
|               | +SDC   | <b>7.20</b>  | <b>9.13</b>  | <b>6.89</b>  | <b>3.88</b>  | <b>3.40</b>  |

on the sphere  $\theta^{\mathcal{D}_{\text{real}}} \sim \text{Unif}(\mathbb{S}^{d-1})$ √  $(d)$ ). Our analysis is situated within the high-dimensional statistics limit where  $d, d_{\text{real}} \to \infty$  but the ratio  $\alpha_{\text{real}} = d_{\text{real}}/d$  remains  $O(1)$ .

Specifically, consider synthesizing a dataset by matching only the samples with the smallest margin  $|z_i| = |\theta^{\text{probe}}\, x_i|$ along a probe student  $\theta^{\text{probe}}$ . The distilled dataset will then follow a distribution  $p(z)$  in the direction of  $\theta^{probe}$ while remaining isotropic in the null space of  $\theta^{\text{probe}}$ . We assume, without loss of generality, that  $\theta^{\text{probe}}$  has developed some overlap with the expert, quantified by the angle  $\gamma=\cos^{-1}\left(\frac{\theta^{\text{probe}\top}\theta^{\mathcal{D}_\text{real}}}{\|\theta^{\text{probe}\top}\|\sin\theta^{\mathcal{D}_\text{real}}}\right)$  $\frac{\theta^{\text{probe}}\mid \theta^{\mathcal{D}_{\text{real}}}}{\|\theta^{\text{probe}}\|_2 \|\theta^{\mathcal{D}_{\text{real}}}\|_2}\bigg).$ 

Once the dataset has been distilled, we consider training a new student  $\theta^{\mathcal{D}_{syn}}$  from scratch on this distilled dataset. A typical training algorithm aims to find the solution  $\theta^{\mathcal{D}_{syn}}$ which classifies the training data with maximal margin  $\kappa = \min_i (\theta^{\mathcal{D}_{\text{syn}}^{\top}} y_i x_i)$ . Our goal is to compute the generalization error  $\varepsilon$  of this student, governed by the overlap between the student and the expert:  $\varepsilon = \cos^{-1}(R)/\pi$ , where  $R = \frac{\theta^{\mathcal{D}^\top_{\mathsf{syn}}}\theta^{\mathcal{D}_{\mathsf{real}}}}{\theta^{\mathcal{D}_{\mathsf{syn}}}\theta^{\mathcal{D}_{\mathsf{real}}}}$  $\|\theta^{\mathcal{D}_{\sf syn}}\|_2 \|\theta^{\mathcal{D}_{\sf real}}\|_2$ .

We provide saddle point equations for the cosine similarity R between the probe  $\theta^{\mathcal{D}_{\text{probe}}}$  and the expert  $\theta^{\mathcal{D}_{\text{real}}}$ , which will be discussed in Section [4.1](#page-5-1) and Section [4.2.](#page-5-2) For our simulations, we set the parameter dimension  $d = 200$  for perfect probe settings, and set  $d = 50$  for imperfect probe settings. We averaged 100 simulation results to verify the

theory.

## 4.1. Perfect Expert-Teacher Settings

The solution is given by the following saddle point equations for perfect expert-teacher settings, *i.e.*,  $\gamma = 0$ . For any given  $\alpha_{syn}$ , these equations can be solved for the order parameters  $R$ ,  $\kappa$ . From these parameters, the generalization error can be computed as  $\varepsilon = \cos^{-1}(R)/\pi$ .

The first equation is split into two lines, which should be combined into a single block equation. The second equation is missing the closing bracket for the H function argument and the exponent for \$(\kappa - t)^2\$. The definition of H(x) is also incorrect, it should be \frac{1}{2} \left(1 - \frac{2}{\sqrt{\pi}} \int\_{0}^{\frac{x}{\sqrt{2}}} e^{-t^2} dt\right).

$$
R = \frac{2\alpha_{\text{syn}}}{f\sqrt{2\pi}\sqrt{1-R^2}} \int_{-\infty}^{\kappa} Dt \exp\left(-\frac{R^2t^2}{2(1-R^2)}\right) \times \left[1 - \exp\left(-\frac{\gamma(\gamma - 2Rt)}{2(1-R^2)}\right)\right] (\kappa - t)
$$
$$
1 - R^2 = \frac{2\alpha_{\text{syn}}}{f} \int_{-\infty}^{\kappa} Dt \left[H\left(-\frac{Rt}{\sqrt{1-R^2}}\right) - H\left(-\frac{Rt - \gamma}{\sqrt{1-R^2}}\right)\right] (\kappa - t)^2
$$
  
Where  $H(x) = \frac{1}{2} \left(1 - \frac{2}{\sqrt{\pi}} \int_{0}^{\frac{x}{\sqrt{2}}} e^{-t^2} dt\right)$ . This is

Where  $H(x) = \frac{1}{2}$  $1 - \frac{2}{\sqrt{\pi}} \int_0^{\left(\frac{x}{\sqrt{2}}\right)} e^{-t^2} dt$ . This cal-

culation produces the solid theoretical curves shown in Figure [4,](#page-4-0) which exhibit an excellent match with numerical simulations. Please refer [\[38\]](#page-9-14) for detailed deductions.

<span id="page-16-0"></span>Table 10. Cross-architecture evaluation. We evaluated distilled datasets with IPC = 1 learned through DSAC w/ and w/o SDC on different networks.

| Dataset             | Method      | ResNet-18    | VGG-11       | AlexNet      | LeNet        | MLP          |
|---------------------|-------------|--------------|--------------|--------------|--------------|--------------|
| <b>MNIST</b>        | <b>DSAC</b> | 88.58        | 79.58        | 83.63        | 83.46        | 72.78        |
|                     | <b>+SDC</b> | <b>88.70</b> | <b>80.95</b> | <b>83.92</b> | <b>83.66</b> | <b>73.51</b> |
| <b>FashionMNIST</b> | <b>DSAC</b> | 71.60        | 68.03        | 66.03        | 67.09        | 63.85        |
|                     | <b>+SDC</b> | <b>71.70</b> | <b>68.82</b> | <b>66.47</b> | <b>67.19</b> | <b>64.93</b> |
| <b>SVHN</b>         | <b>DSAC</b> | 33.04        | 32.32        | 14.63        | 20.89        | 13.32        |
|                     | <b>+SDC</b> | <b>33.65</b> | <b>33.84</b> | <b>17.18</b> | <b>22.40</b> | <b>13.86</b> |

<span id="page-16-1"></span>Table 11. Cross-architecture evaluation. We evaluated distilled datasets with IPC = 50 learned through DSAC w/ and w/o SDC on different networks.

| Dataset      | Method      | ResNet-18    | VGG-11       | AlexNet      | LeNet        | MLP          |
|--------------|-------------|--------------|--------------|--------------|--------------|--------------|
| <b>MNIST</b> | <b>DSAC</b> | 97.97        | 98.53        | 97.95        | 97.58        | 94.70        |
|              | <b>+SDC</b> | 97.95        | <b>98.57</b> | <b>97.97</b> | <b>97.62</b> | <b>94.75</b> |
| FashionMNIST | <b>DSAC</b> | 86.92        | 87.03        | <b>85.61</b> | 84.96        | 83.56        |
|              | <b>+SDC</b> | <b>86.96</b> | <b>87.20</b> | 85.58        | <b>85.36</b> | <b>83.78</b> |
| <b>SVHN</b>  | <b>DSAC</b> | 86.10        | 85.62        | 83.47        | 77.92        | 62.68        |
|              | <b>+SDC</b> | <b>86.32</b> | <b>85.86</b> | <b>83.85</b> | <b>78.92</b> | <b>63.47</b> |

## 4.2. Imperfect Expert-Teacher Settings

We have shown the perfect student settings in Section [4.1.](#page-5-1) When the probe student does not exactly match the expert, an additional parameter  $\theta$  characterizes the angle between the probe student and the expert. Furthermore, an additional order parameter  $\rho = \theta^{\mathcal{D}_{\text{real}}^{\top}} \theta^{\mathcal{D}_{\text{syn}}}$  represents the typical student-probe overlap, which must be optimized. Consequently, we derive three saddle point equations.

$$
\frac{R - \rho \cos \gamma}{\sin^2 \gamma} = \frac{\alpha_{\text{syn}}}{\pi \Lambda} \left\langle \int_{-\infty}^{\kappa} dt \exp \left( -\frac{\Delta(t, z)}{2\Lambda^2} \right) (\kappa - t) \right\rangle_z
$$

$$
1 - \frac{\rho^2 + R^2 - 2\rho R \cos \gamma}{\sin^2 \gamma} = 2\alpha_{\text{syn}} \left\langle \int_{-\infty}^{\kappa} dt \frac{e^{-\frac{(t - \rho z)^2}{2(1 - \rho^2)}}}{\sqrt{2\pi}\sqrt{1 - \rho^2}} H \left( \frac{\Gamma(t, z)}{\sqrt{1 - \rho^2 \Lambda}} \right) (\kappa - t)^2 \right\rangle_z
$$

$$
\frac{\rho - R \cos \gamma}{\sin^2 \gamma} = 2\alpha_{\text{syn}} \left\langle \int_{-\infty}^{\kappa} dt \frac{e^{-\frac{(t - \rho z)^2}{2(1 - \rho^2)}}}{\sqrt{2\pi}\sqrt{1 - \rho^2}} H \left( \frac{\Gamma(t, z)}{\sqrt{1 - \rho^2 \Lambda}} \right) \left( \frac{z - \rho t}{1 - \rho^2} \right) (\kappa - t) \right\rangle_z
$$

$$
+ \frac{1}{2\pi \Lambda} \left\langle \exp \left( -\frac{\Delta(t, z)}{2\Lambda^2} \right) \left( \frac{\rho R - \cos \gamma}{1 - \rho^2} \right) (\kappa - t) \right\rangle_z
$$

Where,

$$
\Lambda = \sqrt{\sin^2 \gamma - R^2 - \rho^2 + 2\rho R \cos \gamma}
$$

$$
\Gamma(t, z) = z(\rho R - \cos \gamma) - t(R - \rho \cos \gamma)
$$

$$
\Delta(t, z) = z^2 (\rho^2 + \cos^2 \gamma - 2\rho R \cos \gamma) + 2tz(R \cos \gamma - \rho) + t^2 \sin^2 \gamma
$$

The notation  $\langle \cdot \rangle_z$  denotes an average over the pruned data distribution  $p(z)$  for the probe student. For any given  $\alpha_{syn}$ ,  $p(z)$ ,  $\gamma$ , these equations can be solved for the order parameters  $R$ ,  $\rho$ ,  $\kappa$ . From these parameters, the generalization error can be readily obtained as  $\varepsilon = \cos^{-1}(R)/\pi$ . Our simulation results are shown in Figure [15.](#page-17-0) Please refer [\[38\]](#page-9-14) for detailed deductions.

## 5. Visualization Results

Additionally, we show our visualization of distilled datasets by adding SDC into current matching-based methods, as shown in Figure [16,](#page-17-1) Figure [17,](#page-18-0) Figure [18,](#page-19-0) Figure [19,](#page-20-0) and Figure [20.](#page-21-0)

<span id="page-17-0"></span>Image /page/17/Figure/0 description: This figure contains three plots, labeled (a), (b), and (c), each showing test error on the y-axis (on a logarithmic scale) versus synthetic samples per parameter on the x-axis. The plots are titled with gamma values: (a) gamma = 0 degrees, (b) gamma = 10 degrees, and (c) gamma = 20 degrees. Each plot displays multiple colored lines, representing different fractions of synthesized data, ranging from 20% (yellow) to 100% (purple). A dashed red line labeled "f\_min" is present in plots (b) and (c). The legend indicates the color coding for the fraction of data synthesized.

<span id="page-17-1"></span>Figure 15. Test error  $\varepsilon$  as a function of the synthetic samples per parameter  $\alpha_{syn}$  and fraction of data synthesized f in (a) the perfect expert setting ( $\gamma = 0$ ) (b) the perfect expert setting ( $\gamma = 10^{\circ}$ ) (c) the perfect expert setting ( $\gamma = 20^{\circ}$ ).

Image /page/17/Figure/2 description: A grid of 80 images, arranged in 8 rows and 10 columns. The images depict various objects and animals, including airplanes, cars, birds, cats, deer, dogs, horses, ships, and trucks. The images appear to be generated by a machine learning model, as they are somewhat abstract and stylized. The overall impression is a diverse collection of synthesized imagery.

Figure 16. (FTD + SDC, CIFAR-10, IPC = 10) Visualization of distilled images.

<span id="page-18-0"></span>Image /page/18/Picture/0 description: The image displays a grid of 64 smaller images, arranged in an 8x8 matrix. Each smaller image appears to be a generated or stylized visual, with a variety of colors, textures, and subjects. Some images show abstract patterns, while others seem to depict recognizable objects or scenes, such as animals (possibly dogs or birds), natural landscapes, or architectural elements. The overall impression is a collection of diverse visual outputs, possibly from a machine learning model or an artistic experiment.

Figure 17. (DATM + SDC, Tiny ImageNet, IPC = 1, 1/2) Visualization of distilled images.

<span id="page-19-0"></span>Image /page/19/Picture/0 description: The image is a grid of 80 small images, arranged in 8 rows and 10 columns. Each small image appears to be a generated image, possibly from a machine learning model, with a somewhat abstract or impressionistic style. The images vary in color and content, with some depicting what look like landscapes, figures, or abstract patterns. The overall impression is a collection of diverse, digitally created visuals.

Figure 18. (DATM + SDC, Tiny ImageNet, IPC = 1, 2 / 2) Visualization of distilled images.

<span id="page-20-0"></span>Image /page/20/Picture/0 description: The image displays a grid of 80 small images, each containing a single digit from 0 to 9. The digits are arranged in rows and columns, with each row showcasing variations of the same digit. For instance, the first row features multiple instances of the digit '0', the second row displays the digit '1', and so on, up to the ninth row showing the digit '9'. The digits themselves vary in clarity and style, with some appearing sharp and well-defined, while others are more blurred or distorted, exhibiting a range of colors and textures. The overall presentation suggests a visualization of generated or processed digits, possibly from a machine learning model.

Figure 19. (DSA + SDC, SVHN, IPC = 10) Visualization of distilled images.

<span id="page-21-0"></span>Image /page/21/Picture/0 description: The image displays a grid of 50 grayscale images, arranged in 10 rows and 5 columns. Each image depicts a piece of clothing or footwear, such as shirts, pants, shoes, and handbags. The images are generated by a machine learning model, likely for a fashion dataset, and appear to be stylized or abstract representations of these items. The background of all images is black, and the clothing items are rendered in shades of white and gray, giving them a somewhat ghostly or ethereal appearance. The overall impression is a collection of generated fashion items.

Figure 20. (DC + SDC, FashionMNIST, IPC = 10) Visualization of distilled images.
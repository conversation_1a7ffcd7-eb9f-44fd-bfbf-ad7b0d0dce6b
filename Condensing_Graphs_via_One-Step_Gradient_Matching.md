# <span id="page-0-0"></span>Condensing Graphs via One-Step Gradient Matching

<PERSON><sup>\*</sup> Michigan <NAME_EMAIL>

<PERSON> <NAME_EMAIL>

Xianfeng <NAME_EMAIL>

<PERSON><PERSON> Amazon <EMAIL>

Haoming <NAME_EMAIL>

Jiliang Tang Michigan <NAME_EMAIL>

Bing <NAME_EMAIL>

# KEYWORDS

Data-efficient Learning; Graph Generation; Graph Neural Networks

### ACM Reference Format:

<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. 2022. Condensing Graphs via One-Step Gradient Matching. In Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD '22), August 14–18, 2022, Washington, DC, USA. ACM, New York, NY, USA, [12](#page-11-0) pages[. https://doi.org/10.1145/3534678.3539429](https://doi.org/10.1145/3534678.3539429)

# 1 INTRODUCTION

Graph-structured data plays a key role in various real-world applications. For example, by exploiting graph structural information, we can predict the chemical property of a given molecular graph [\[48\]](#page-8-0), detect fraud activities in a financial transaction graph [\[36\]](#page-8-1), or recommend new friends to users in a social network [\[9\]](#page-8-2). Due to its prevalence, graph neural networks (GNNs) [\[2,](#page-8-3) [19,](#page-8-4) [35,](#page-8-5) [44\]](#page-8-6) have been developed to effectively extract meaningful patterns from graph data and thus tremendously facilitate computational tasks on graphs. Despite their effectiveness, GNNs are notoriously datahungry like traditional deep neural networks: they usually require massive datasets to learn powerful representations. Thus, training GNNs is often computationally expensive. Such cost even becomes prohibitive when we need to repeatedly train GNNs, e.g., in neural architecture search [\[24\]](#page-8-7) and continual learning [\[23\]](#page-8-8).

One potential solution to alleviate the aforementioned issue is dataset condensation or dataset distillation. It targets at constructing a small-synthetic training set that can provide sufficient information to train neural networks [\[4,](#page-8-9) [29,](#page-8-10) [30,](#page-8-11) [37,](#page-8-12) [38,](#page-8-13) [52,](#page-8-14) [54\]](#page-9-0). In particular, one of the representative methods, DC [\[54\]](#page-9-0), formulates the condensation goal as matching the gradients of the network parameters between small-synthetic and large-real training data. It has been demonstrated that such a solution can greatly reduce the training set size of image datasets without significantly sacrificing model performance. For example, using 100 images generated by DC can achieve 97.4% test accuracy on MNIST compared with 99.6% on the original dataset (60, 000 images). These condensed samples can significantly save space for storing datasets and speed up retraining neural networks in many critical applications, e.g., continual learning and neural architecture search. In spite of the recent advances in dataset distillation/condensation for images, limited attention has been paid on domains involving graph structures.

# arXiv:2206.07746v3 [cs.LG] 8 Sep 2022 arXiv:2206.07746v3 [cs.LG] 8 Sep 2022

# ABSTRACT

As training deep learning models on large dataset takes a lot of time and resources, it is desired to construct a small synthetic dataset with which we can train deep learning models sufficiently. There are recent works that have explored solutions on condensing image datasets through complex bi-level optimization. For instance, dataset condensation (DC) matches network gradients w.r.t. largereal data and small-synthetic data, where the network weights are optimized for multiple steps at each outer iteration. However, existing approaches have their inherent limitations: (1) they are not directly applicable to graphs where the data is discrete; and (2) the condensation process is computationally expensive due to the involved nested optimization. To bridge the gap, we investigate efficient dataset condensation tailored for graph datasets where we model the discrete graph structure as a probabilistic model. We further propose a one-step gradient matching scheme, which performs gradient matching for only one single step without training the network weights. Our theoretical analysis shows this strategy can generate synthetic graphs that lead to lower classification loss on real graphs. Extensive experiments on various graph datasets demonstrate the effectiveness and efficiency of the proposed method. In particular, we are able to reduce the dataset size by 90% while approximating up to 98% of the original performance and our method is significantly faster than multi-step gradient matching (e.g. 15× in CIFAR10 for synthesizing 500 graphs). Code is available at [https://github.com/amazon-research/DosCond.](https://github.com/amazon-research/DosCond)

# CCS CONCEPTS

• Computing methodologies  $\rightarrow$  Neural networks.

KDD '22, August 14–18, 2022, Washington, DC, USA

© 2022 Association for Computing Machinery.

ACM ISBN 978-1-4503-9385-0/22/08. . . \$15.00

<https://doi.org/10.1145/3534678.3539429>

<sup>∗</sup>Work done while author was on internship at Amazon.

Permission to make digital or hard copies of all or part of this work for personal or classroom use is granted without fee provided that copies are not made or distributed for profit or commercial advantage and that copies bear this notice and the full citation on the first page. Copyrights for components of this work owned by others than ACM must be honored. Abstracting with credit is permitted. To copy otherwise, or republish, to post on servers or to redistribute to lists, requires prior specific permission and/or a fee. Request <NAME_EMAIL>.

To bridge this gap, we investigate the problem of condensing graphs such that GNNs trained on condensed graphs can achieve comparable performance to those trained on the original dataset. However, directly applying existing solutions for dataset condensation [\[29,](#page-8-10) [38,](#page-8-13) [52,](#page-8-14) [54\]](#page-9-0) to graph domain faces some challenges. First, existing solutions have been designed for images where the data is continuous and they cannot output binary values to form the discrete graph structure. Thus, we need to develop a strategy that can handle the discrete nature of graphs. Second, they usually involve a complex bi-level problem that is computationally expensive to optimize: they require multiple iterations (inner iterations) of updating neural network parameters before updating the synthetic data for multiple iterations (outer iterations). It can be catastrophically inefficient for learning pairwise relations for nodes, of which the complexity is quadratic to the number of nodes. While one recent work targets at graph condensation for node classification [\[16\]](#page-8-15), it does not overcome these challenges because it does not produce discrete graph structures and its condensation process is costly.

To address the aforementioned challenges, we propose an efficient condensation method for graphs, where we follow DC [\[54\]](#page-9-0) to match the gradients of GNNs between synthetic graphs and real graphs. In order to produce discrete values, we model the graph structure as a probabilistic graph model and optimize the discrete structures in a differentiable manner. Based on this formulation, we further propose a one-step gradient matching strategy which only performs gradient matching for one single step. Consequently, the advantages of the proposed strategy are twofold. First, it significantly speeds up the condensation process while providing reasonable guidance for synthesizing condensed graphs. Second, it removes the burden of tuning hyper-parameters such as the number of outer/inner iterations of the bi-level optimization as required by DC. Furthermore, we demonstrate the effectiveness of the proposed one-step gradient matching strategy both theoretically and empirically. Our contributions can be summarized as follows:

- 1. We study a novel problem of learning discrete synthetic graphs for condensing graph datasets, where the discrete structure is captured via a graph probabilistic model that can be learned in a differentiable manner.
- 2. We propose a one-step gradient matching scheme that significantly accelerates the vanilla gradient matching process.
- 3. Theoretical analysis is provided to understand the rationality of the proposed one-step gradient matching. We show that learning with one-step matching produces synthetic graphs that lead to a smaller classification loss on real graphs.
- 4. Extensive experiments have demonstrated the effectiveness and efficiency of the proposed method. Particularly, we are able to reduce the dataset size by 90% while approximating up to 98% of the original performance and our method is significantly faster than multi-step gradient matching (e.g. 15× in CIFAR10 for synthesizing 500 graphs).

## 2 THE PROPOSED FRAMEWORK

Before detailing the framework, we first introduce the main notations used in this paper. We majorly focus on the graph classification task where the goal is to predict the labels of given graphs. Specifically, we denote a graph dataset as  $\mathcal{T} = \{G_1, \ldots, G_N\}$  with

ground-truth label set  $\mathcal Y$ . Each graph in  $\mathcal T$  is associated with a discrete adjacency matrix and a node feature matrix. Let  $\mathbf{A}_{(i)}, \mathbf{X}_{(i)}$ represent the adjacency matrix and the feature matrix of  $i$ -th real graph, respectively. Similarly, we use  $S = \{G'_1, \ldots, G'_{N'}\}$  and  $J'$  to indicate the synthetic graphs and their labels, respectively. Note that the number of synthetic graphs  $N'$  is essentially much smaller than that of real graphs  $N$ . We use  $d$  and  $n$  to denote the number of feature dimensions the number of nodes in each synthetic graph, respectively<sup>[1](#page-0-0)</sup>. Let C denote the number of classes and  $\ell$  denote the cross entropy loss. The goal of our work is to learn a set of synthetic graphs  $S$  such that a GNN trained on  $S$  can achieve comparable performance to the one trained on the much larger dataset  $\mathcal{T}$ .

In the following subsections, we first introduce how to apply the vanilla gradient matching to condensing graphs for graph classification (Section [2.1\)](#page-1-0). However, it cannot generate discrete graph structure and is highly inefficient. To correspondingly address these two limitations, we discuss the approach to handling the discrete nature of graphs (Section [2.2\)](#page-2-0) and propose an efficient solution, one-step gradient matching, which significantly accelerates the condensation process (Section [2.3\)](#page-2-1).

<span id="page-1-0"></span>

## 2.1 Gradient Matching as the Objective

Since we aim at learning synthetic graphs that are highly informative, one solution is to allow GNNs trained on synthetic graphs to imitate the training trajectory on the original large dataset. Dataset condensation [\[52,](#page-8-14) [54\]](#page-9-0) introduces a gradient matching scheme to achieve this goal. Concretely, it tries to reduce the difference of model gradients w.r.t. large-real data and small-synthetic data for model parameters at every training epoch. Hence, the model parameters trained on synthetic data will be close to these trained on real data at every training epoch. Let  $\theta_t$  denote the network parameters at the *t*-th epoch and  $f_{\theta_t}$  indicate the neural network parameterized by  $\theta_t$ . The condensation objective is expressed as:

<span id="page-1-1"></span>
$$
\min_{S} \sum_{t=0}^{T-1} D(\nabla_{\theta} \ell(f_{\theta_t}(S), \mathcal{Y}'), \nabla_{\theta} \ell(f_{\theta_t}(T), \mathcal{Y})),
$$
  
s.t.  $\theta_{t+1} = \text{opt}_{\theta}(\theta_t, S),$  (1)

where  $D(\cdot, \cdot)$  is a distance function, T is the number of steps of the whole training trajectory and  $opt_{\theta}(\cdot)$  is the optimization operator for updating parameter  $\theta$ . Note that Eq. [\(1\)](#page-1-1) is a bi-level problem where we need to learn the synthetic graphs  $S$  at the outer optimization and update model parameters  $\theta_t$  at the inner optimization. To learn synthetic graphs that generalize to a distribution of model parameters  $P_{\theta_0}$ , we sample  $\theta_0 \sim P_{\theta_0}$  and rewrite Eq. [\(1\)](#page-1-1) as:

<span id="page-1-2"></span>
$$
\min_{\mathcal{S}} \mathop{\mathbb{E}}_{\theta_0 \sim P_{\theta_0}} \left[ \sum_{t=0}^{T-1} D\left(\nabla_{\theta} \ell\left(f_{\theta_t}(\mathcal{S}), \mathcal{Y}'\right), \nabla_{\theta} \ell\left(f_{\theta_t}(\mathcal{T}), \mathcal{Y}\right)\right) \right],
$$
\n
$$
\text{s.t. } \theta_{t+1} = \text{opt}_{\theta}(\theta_t, \mathcal{S}). \tag{2}
$$

Discussion. The aforementioned strategy has demonstrated promising performance on condensing image datasets [\[52,](#page-8-14) [54\]](#page-9-0). However, it is not clear how to model the discrete graph structure. Moreover, the inherent bi-level optimization inevitably hinders its scalability. To tackle these shortcomings, we propose DosCond that models the

<sup>&</sup>lt;sup>1</sup>We set  $n$  to the average number of nodes in original dataset.

Condensing Graphs via One-Step Gradient Matching KDD '22, August 14-18, 2022, Washington, DC, USA

<span id="page-2-3"></span>KDD '22, August 14-18, 2022, Washington, DC, USA

structure as a probabilistic graph model and is optimized through one-step gradient matching. In the following subsections, we introduce the details of DosCond.

<span id="page-2-0"></span>

## 2.2 Learning Discrete Graph Structure

For graph classification, each graph in the dataset is composed of an adjacency matrix and a feature matrix. For simplicity, we use  $X' \in \mathbb{R}^{N' \times n \times d}$  to denote the node features in all synthetic graphs  $\mathcal{S}$ and  $A' \in \{0, 1\}^{N' \times n \times n}$  to indicate the graph structure information in  $\mathcal{S}.$  Note that  $f_{\theta_t}$  can be instantiated as any graph neural network and it takes both graph structure and node features as input. Then we rewrite the objective in Eq. [\(2\)](#page-1-2) as follows:

$$
\min_{\mathbf{A}', \mathbf{X}' \, \theta_0 \sim P_{\theta_0}} \left[ \sum_{t=0}^{T-1} D\left(\nabla_{\theta} \ell\left(f_{\theta_t}(\mathbf{A}', \mathbf{X}'), \mathbf{y}'\right), \nabla_{\theta} \ell\left(f_{\theta_t}(\mathcal{T}), \mathbf{y}\right)\right) \right],
$$
\n
$$
\text{s.t. } \theta_{t+1} = \text{opt}_{\theta}(\theta_t, \mathcal{S}), \tag{3}
$$

where we aim to learn both graph structure  $A'$  and node features  $X'$ . However, Eq. [\(3\)](#page-2-2) is challenging to optimize as it requires a function that outputs binary values. To address this issue, we propose to model the graph structure as a probabilistic graph model with Bernoulli distribution. Note that in the following, we reshape A' from  $N' \times n \times n$  to  $N' \times n^2$  for the purpose of demonstration only. Specifically, for each entry  $A'_{ij} \in \{0, 1\}$  in the adjacency matrix  $A'$ , it follows a Bernoulli distribution:

$$
P_{\Omega_{ij}}(\mathbf{A}'_{ij}) = \mathbf{A}'_{ij}\sigma(\Omega_{ij}) + (1 - \mathbf{A}'_{ij})\sigma(-\Omega_{ij}),
$$
 (4)

where  $\sigma(\cdot)$  is the sigmoid function;  $\Omega_{ij} \in \mathbb{R}$  is the success probability of the Bernoulli distribution and also the parameter to be learned. Since  $A'_{ij}$  is independent of all other entries, the distribution of  $A'$ can be modeled as:

$$
P_{\Omega}(\mathbf{A}') = \prod_{i=1}^{N'} \prod_{j=1}^{n^2} P_{\Omega_{ij}}\left(\mathbf{A}'_{ij}\right).
$$
 (5)

Then, the objective in Eq. [\(2\)](#page-1-2) needs to be modified to

$$
\min_{\mathbf{A}',\mathbf{X}'} \mathop{\mathbb{E}}_{\theta_0 \sim P_{\theta_0}} \left[ \mathop{\mathbb{E}}_{\mathbf{A}' \sim P_{\Omega}} \left[ \ell(\mathbf{A}'(\Omega), \mathbf{X}', \theta_0) \right] \right]. \tag{6}
$$

With the new parameterization, we obtain a function that outputs discrete values but it is not differentiable due to the involved sampling process. Thus, we employ the reparameterization method [\[27\]](#page-8-16), binary concrete distribution, to refactor the discrete random variable into a differentiable function of its parameters and a random variable with fixed distribution. Specifically, we first sample  $\alpha$  ∼ Uniform(0, 1), and edge weight  $A'_{ij}$  ∈ [0, 1] is calculated by:

$$
A'_{ij} = \sigma \left( \left( \log \alpha - \log(1 - \alpha) + \Omega_{ij} \right) / \tau \right),\tag{7}
$$

where  $\tau \in (0, \infty)$  is the temperature parameter that controls the continuous relaxation. As  $\tau \rightarrow 0$ , the random variable  $A'_{ij}$  smoothly approaches the Bernoulli distribution. In other words, we have  $\lim_{\tau\to 0} P\left(A'_{ij} = 1\right) = \sigma(\Omega_{ij}).$  While small  $\tau$  is necessary for obtaining discrete samples, large  $\tau$  is useful in getting large gradients as suggested by [\[27\]](#page-8-16). In practice, we employ an annealing sched-ule [\[1\]](#page-8-17) to gradually decrease the value of  $\tau$  in training. With the reparameterization trick, the objective function becomes differentiable w.r.t.  $\Omega_{ij}$  with well-defined gradients. Then we rewrite our

objective as:

$$
\min_{\Omega, X' \theta_0 \sim P_{\theta_0}} \mathbb{E}_{\alpha \sim \text{Uniform}(0,1)} \left[ \ell(\mathbf{A}'(\Omega), X', \theta_0) \right] =
$$
\n
$$
\mathbb{E}_{\theta_0} \left[ \mathbb{E} \left[ \sum_{t=0}^{T-1} D \left( \nabla_{\theta} \ell \left( f_{\theta_t}(\mathbf{A}'(\Omega), X'), \mathbf{J}' \right), \nabla_{\theta} \ell \left( f_{\theta_t}(\mathcal{T}), \mathbf{J} \right) \right) \right] \right],
$$
\n
$$
\text{s.t. } \theta_{t+1} = \text{opt}_{\theta}(\theta_t, \mathcal{S}).
$$
\n(8)

<span id="page-2-1"></span>

## 2.3 One-Step Gradient Matching

<span id="page-2-2"></span>The vanilla gradient matching scheme in Eq. [\(2\)](#page-1-2) presents a bi-level optimization problem. To solve this problem, we need to update the synthetic graphs  $S$  at the outer loop and then optimize the network parameters  $\theta_t$  at the inner loop. The nested loops heavily impede the scalability of the condensation method, which motivates us to design a new strategy for efficient condensation. In this work, we propose a one-step gradient matching scheme where we only match the network gradients for the model initializations  $\theta_0$  while discarding the training trajectory of  $\theta_t$ . Essentially, this strategy approximates the overall gradient matching loss for  $\theta_t$  with the initial matching loss at the first epoch, which we term as one-step matching loss. The intuition is: the one-step matching loss informs us about the direction to update the synthetic data, in which, we have empirically observed a strong decrease in the cross-entropy loss (on real samples) obtained from the model trained on synthetic data. Hence, we can drop the summation symbol  $\sum_{t=0}^{T-1}$  in Eq. [\(8\)](#page-2-3) and simplify Eq. [\(8\)](#page-2-3) as follows:

$$
\min_{\Omega,\mathbf{X}'}\mathop{\mathbb{E}}_{\theta_0}\bigg[\mathop{\mathbb{E}}_{\alpha}\big[D\left(\nabla_{\theta}\ell\left(f_{\theta_0}(\mathbf{A}'(\Omega),\mathbf{X}'),\mathbf{Y}'\right),\nabla_{\theta}\ell\left(f_{\theta_0}(\mathcal{T}),\mathbf{Y}\right)\right)\big]\bigg],\quad(9)
$$

where we sample  $\theta_0 \sim P_{\theta_0}$  and  $\alpha \sim \text{Uniform}(0, 1)$ . Compared with Eq. [\(8\)](#page-2-3), one-step gradient matching avoids the expensive nestedloop optimization and directly updates the synthetic graph S. It greatly simplifies the condensation process. In practice, as shown in Section [3.3,](#page-6-0) we find this strategy yields comparable performance to its bi-level counterpart while enabling much more efficient condensation. Next, we provide theoretical analysis to understand the rationality of the proposed one-step gradient matching scheme.

Theoretical Understanding. We denote the cross entropy loss on the real graphs as  $\ell_{\mathcal{T}}(\theta) = \sum_i \ell_i(\mathbf{A}_{(i)}, \mathbf{X}_{(i)}, \theta)$  and that on synthetic graphs as  $\ell_{\mathcal{S}}(\theta) = \ell_{\mathcal{S}}(A'_{(i)}, X'_{(i)}, \theta)$ . Let  $\theta^*$  denote the optimal parameter and  $\theta_t$  be the parameter trained on S at the *t*-th epoch by optimizing  $\ell_{\mathcal{S}}(\theta)$ . For notation simplicity, we assume that  $\overline{\mathbf{A}}$  and  $\overline{\mathbf{A}}'$ are already normalized. The matrix norm ∥ · ∥ is the Frobenius norm. We focus on the GNN of Simple Graph Convolutions (SGC) [\[43\]](#page-8-18) to study our problem since SGC has a simpler architecture but shares a similar filtering pattern as GCN.

<span id="page-2-4"></span>THEOREM 1. When we use a  $K$ -layer SGC as the GNN used in condensation, i.e.,  $f_{\theta}(\mathbf{A}_{(i)}, \mathbf{X}_{(i)}) = Pool(\mathbf{A}_{(i)}^{K} \mathbf{X}_{(i)} \mathbf{W}_{1}) \mathbf{W}_{2}$  with  $\theta =$  $\left[\mathbf{W}_1; \mathbf{W}_2 \right]$  and assume that all network parameters satisfy  $\|\theta\|^2 \leq$  $M^2(M > 0)$ , we have

$$
\min_{t=0,1,\dots,T-1} \ell_{\mathcal{T}}\left(\theta_{t}\right) - \ell_{\mathcal{T}}\left(\theta^{*}\right) \leq \sum_{t=0}^{T-1} \frac{\sqrt{2}M}{T} \|\nabla_{\theta}\ell_{\mathcal{T}}\left(\theta_{t}\right) - \nabla_{\theta}\ell_{S}\left(\theta_{t}\right) \| + \frac{3M}{2\sqrt{T}} \cdot \frac{C-1}{C N'} \sqrt{\sum_{i} \gamma_{i} \|\mathbf{1}^{\top} \mathbf{A}_{(i)}^{K} \mathbf{X}_{(i)}^{\prime\|\|^{2}}} \qquad (10)
$$

where  $\gamma_i = 1$  if we use sum pooling in  $f_\theta$ ;  $\gamma_i = \frac{1}{n_i}$  if we use mean pooling, with  $n_i$  as the number of nodes in the *i*-th synthetic graph.

We provide the proof of Theorem 1 in Appendix [B.1.](#page-9-1) Theorem 1 suggests that the smallest gap between the resulted loss (by training on synthetic graphs) and the optimal loss has an upper bound. This upper bound depends on two terms: (1) the difference of gradients w.r.t. real data and synthetic data and (2) the norm of input matrices. Thus, the theorem justifies that reducing the gradient difference w.r.t real and synthetic graphs can help learn desirable synthetic data that preserves sufficient information to train GNNs well. Based on Theorem 1, we have the following proposition.

PROPOSITION 1. Assume the largest gradient gap happens at 0-th epoch, i.e.,  $\|\nabla_{\theta}\ell_{\mathcal{T}}(\theta_0)-\nabla_{\theta}\ell_{S}(\theta_0)\| = \max_{t} \|\nabla_{\theta}\ell_{\mathcal{T}}(\theta_t)-\nabla_{\theta}\ell_{S}(\theta_t)\|$ with  $t = 0, 1, ..., T - 1$ , we have

$$
\min_{t=0,1,\dots,T-1} \ell_{\mathcal{T}}\left(\theta_{t}\right) - \ell_{\mathcal{T}}\left(\theta^{*}\right) \leq \sqrt{2}M \left\|\nabla_{\theta}\ell_{\mathcal{T}}\left(\theta_{0}\right) - \nabla_{\theta}\ell_{S}\left(\theta_{0}\right)\right\|
$$

$$
+ \frac{3M}{2\sqrt{T}} \cdot \frac{C-1}{CN'} \sqrt{\sum_{i} \gamma_{i} \left\|1^{T} \mathbf{A}_{(i)}^{K} \mathbf{X}_{(i)}^{'} \right\|^{2}}.
$$
 $(11)$ 

We omit the proof for the proposition since it is straightforward. The above proposition suggests that the smallest gap between the  $\ell_{\mathcal{T}}(\theta_t)$  and  $\ell_{\mathcal{T}}(\theta^*)$  is bounded by the one-step matching loss and the norm  $||\mathbf{1}^\top \mathbf{A}_{(i)}^{K} \mathbf{X}_{(i)}^{V}||^2$ . As we will show in Section [3.3.4,](#page-6-1) when using mean pooling, the second term tend to have a smaller scale than the first one and can be neglected; the second term matters more when we use sum pooling. Hence, we solely optimize the one-step gradient matching loss for GNNs with mean pooling and additionally include the second term (the norm of input matrices) as a regularization for GNNs with sum pooling. As such, when we consider the optimal loss  $\ell_{\mathcal{T}}(\theta^*)$  as a constant, reducing the one-step matching loss indeed learns synthetic graphs that lead to a smaller loss on real graphs. This demonstrates the rationality of one-step gradient matching from a theoretical perspective.

Remark 1. Note that the spectral analysis from [\[43\]](#page-8-18) demonstrated that both GCN and SGC share similar graph filtering behaviors. Thus practically, we extend the one-step gradient matching loss from  $K$ -layer SGC to  $K$ -layer GCN and observe that the proposed framework works well under the non-linear scenario.

Remark 2. While we focus on the graph classification task, it is straightforward to extend our framework to node classification and we obtain similar conclusions for node classification as shown in Theorem 2 in Appendix [B.2.](#page-10-0)

## 2.4 Final Objective and Training Algorithm

In this subsection, we describe the final objective function and the detailed training algorithm. We note that the objective in Eq. [\(8\)](#page-2-3) involves two nested expectations, we adopt Monte Carlo to approximately optimize the objective function. Together with one-step gradient matching, we have

$$
\min_{\Omega, X' \ \theta_0 \sim P_{\theta_0} \ \alpha \sim \text{Uniform}(0,1)} \left[ \left[ \ell(A'(\Omega), X', \theta_0) \right] \right] \tag{12}
$$

$$
\approx \sum_{k_1=1}^{K_1} \sum_{k_2=1}^{K_2} D\left(\nabla_{\theta} \ell\left(f_{\theta_0}(\mathbf{A}'(\Omega), \mathbf{X}'), \mathbf{y'}\right), \nabla_{\theta} \ell\left(f_{\theta_0}(\mathcal{T}), \mathbf{y}\right)\right)
$$

where  $K_1$  is the number of sampled model initializations and  $K_2$  is the number of sampled graphs. We find that  $K_2 = 1$  is able to yield good performance in our experiments.

Regularization. In addition to the one-step gradient matching loss, we note that the proposed DosCond can be easily integrated with various priors as regularization terms. In this work, we focus on exerting sparsity regularization on the adjacency matrix, since a denser adjacency matrix will lead to higher cost for training graph neural networks. Specifically, we penalize the difference of the sparsity between  $\sigma(\Omega)$  and a given sparsity  $\epsilon$ :

$$
\ell_{\text{reg}} = \max\left(\frac{1}{|\Omega|} \sum_{i,j} \sigma(\Omega_{ij}) - \epsilon, 0\right). \tag{13}
$$

<span id="page-3-1"></span>We initialize  $\sigma(\Omega)$  and X' as randomly sampled training graphs<sup>[2](#page-0-0)</sup> and set  $\epsilon$  to the average sparsity of initialized  $\sigma(\Omega)$  so as to maintain a low sparsity. On top of that, as we discussed earlier in Section [2.3,](#page-2-1) we include the following regularization for GNNs with sum pooling:

<span id="page-3-0"></span>
$$
\ell_{\text{reg2}} = \frac{3}{2\sqrt{2T}} \cdot \frac{C - 1}{C N'} \sqrt{\sum_{i} ||\mathbf{1}^\top \mathbf{A}_{(i)}^{\prime K} \mathbf{X}_{(i)}^\prime ||^2}
$$
(14)

Training Algorithm. We provide the details of our proposed framework in Algorithm [1](#page-9-2) in Appendix [A.1.](#page-9-3) Specifically, we sample  $K_1$  model initializations  $\theta_0$  to perform one-step gradient matching. Following the convention in DC [\[54\]](#page-9-0), we match gradients and update synthetic graphs for each class separately in order to make matching easier. For class  $c$ , we first retrieve the synthetic graphs of that class, denoted as  $(A'_c, X'_c, Y'_c) \sim S$ , and sample a batch of real graphs  $(A_c, X_c, \mathcal{Y}_c)$ . We then forward them to the graph neural network and calculate the one-step gradient matching loss together with the regularization term. Afterwards,  $\Omega$  and  $X'$  are updated via gradient descent. It is worth noting that the training process for each class can be run in parallel since the graph updates for one class is independent of another class.

Comparison with DC. Recall that the gradient matching scheme in DC involves a complex bi-level optimization. If we denote the number of inner-iterations as  $\tau_i$  and that of outer-iterations as  $\tau_o$ , its computational complexity can be  $\tau_i \times \tau_o$  of our method. Thus DC is significantly slower than *DosCond*. In addition to speeding up condensation, DosCond removes the burden of tuning some hyper-parameters, i.e., the number of iterations for outer/inner optimization and learning rate for updating  $f_\theta$ , which can potentially save us enormous training time when learning larger synthetic sets.

Comparison with Coreset Methods. Coreset methods [\[32,](#page-8-19) [42\]](#page-8-20) select representative data samples based on some heuristics calculated on the pre-trained embedding. Thus, it requires training the model first. Given the cheap cost on calculating and ranking heuristics, the major computational bottleneck for coreset method is on pre-training the neural network for a certain number of iterations. Likewise, our proposed DosCond has comparable complexity because it also needs to forward and backward the neural network for multiple iterations. Thus, their efficiency difference majorly depends on how many epochs we run for learning synthetic graphs in

 $^{2}$ If an entry in the real adjacency matrix is 1, the corresponding value in  $\Omega$  is initialized as a large value, e.g.,5.

|                           | Graphs/Cls. | Ratio | Random                                                                                  | Herding                                                                                 | K-Center                                                                                | <b>DCG</b>                                                                              | DosCond                                                                                 | <b>Whole Dataset</b>                                                                    |
|---------------------------|-------------|-------|-----------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------|
| ogbg-molbace<br>(ROC-AUC) | 1           | 0.2%  | $0.580 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.067}$ | $0.548 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.034}$ | $0.548 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.034}$ | $0.623 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.046}$ | $0.657 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.034}$ |                                                                                         |
|                           | 10          | 1.7%  | $0.598 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.073}$ | $0.639 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.039}$ | $0.591 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.056}$ | $0.655 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.033}$ | $0.674 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.035}$ | $0.714 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.005}$ |
|                           | 50          | 8.3%  | $0.632 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.047}$ | $0.683 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.022}$ | $0.589 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.025}$ | $0.652 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.013}$ | $0.688 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.012}$ |                                                                                         |
| ogbg-molbbbp<br>(ROC-AUC) | 1           | 0.1%  | $0.519 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.016}$ | $0.546 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.019}$ | $0.546 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.019}$ | $0.559 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.044}$ | $0.581 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.005}$ |                                                                                         |
|                           | 10          | 1.2%  | $0.586 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.040}$ | $0.605 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.019}$ | $0.530 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.039}$ | $0.568 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.032}$ | $0.605 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.008}$ | $0.646 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.004}$ |
|                           | 50          | 6.1%  | $0.606 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.020}$ | $0.617 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.003}$ | $0.576 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.019}$ | $0.579 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.032}$ | $0.620 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.007}$ |                                                                                         |
| ogbg-molhiv<br>(ROC-AUC)  | 1           | 0.01% | $0.719 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.009}$ | $0.721 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.002}$ | $0.721 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.002}$ | $0.718 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.013}$ | $0.726 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.003}$ |                                                                                         |
|                           | 10          | 0.06% | $0.720 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.011}$ | $0.725 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.006}$ | $0.713 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.009}$ | $0.728 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.002}$ | $0.728 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.005}$ | $0.757 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.007}$ |
|                           | 50          | 0.3%  | $0.721 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.014}$ | $0.725 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.003}$ | $0.725 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.006}$ | $0.726 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.010}$ | $0.731 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.004}$ |                                                                                         |
| DD<br>(Accuracy)          | 1           | 0.2%  | $57.69 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{4.92}$  | $61.97 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.32}$  | $61.97 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.32}$  | $58.81 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{2.90}$  | $70.42 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{2.21}$  |                                                                                         |
|                           | 10          | 2.1%  | $64.69 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{2.55}$  | $69.79 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{2.30}$  | $63.46 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{2.38}$  | $61.84 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.44}$  | $73.53 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.13}$  | 78.92±0.64                                                                              |
|                           | 50          | 10.6% | $67.29 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.53}$  | $73.95 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.70}$  | $67.41 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.92}$  | $61.27 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.01}$  | $77.04 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.86}$  |                                                                                         |
| MUTAG<br>(Accuracy)       | 1           | 1.3%  | $67.47 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{9.74}$  | $70.84 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{7.71}$  | $70.84 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{7.71}$  | $75.00 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{8.16}$  | $82.21 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.61}$  |                                                                                         |
|                           | 10          | 13.3% | $77.89 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{7.55}$  | $80.42 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.89}$  | $81.00 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{2.51}$  | $82.66 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.68}$  | $82.76 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{2.31}$  | $88.63 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.44}$  |
|                           | 20          | 26.7% | $78.21 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{5.13}$  | $80.00 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.10}$  | $82.97 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{4.91}$  | $82.89 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.03}$  | $83.26 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{2.34}$  |                                                                                         |
| NCI1<br>(Accuracy)        | 1           | 0.1%  | $51.27 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.22}$  | $53.98 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.67}$  | $53.98 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.67}$  | $51.14 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.08}$  | $56.58 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.48}$  |                                                                                         |
|                           | 10          | 0.6%  | $54.33 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{3.14}$  | $57.11 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.56}$  | $53.21 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.44}$  | $51.86 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.81}$  | $58.02 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.05}$  | $71.70 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.20}$  |
|                           | 50          | 3.0%  | $58.51 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.73}$  | $58.94 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.83}$  | $56.58 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{3.08}$  | $52.17 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.90}$  | $60.07 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.58}$  |                                                                                         |
| CIFAR10<br>(Accuracy)     | 1           | 0.06% | $15.61 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.52}$  | $22.38 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.49}$  | $22.37 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.50}$  | $21.60 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.42}$  | $24.70 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.70}$  |                                                                                         |
|                           | 10          | 0.2%  | $23.07 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.76}$  | $28.81 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.35}$  | $20.93 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.62}$  | $29.27 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.77}$  | $30.70 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.23}$  | $50.75 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.14}$  |
|                           | 50          | 1.1%  | $30.56 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.81}$  | $33.94 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.37}$  | $24.17 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.51}$  | $34.47 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.52}$  | $35.34 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.14}$  |                                                                                         |
| E-commerce<br>(Accuracy)  | 1           | 0.2%  | $51.31 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{2.89}$  | $52.18 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.25}$  | $52.36 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.38}$  | $57.14 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.72}$  | $60.82 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.23}$  |                                                                                         |
|                           | 10          | 0.9%  | $54.99 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{2.74}$  | $56.83 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.87}$  | $56.49 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.36}$  | $61.03 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.32}$  | $64.73 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.34}$  | $69.25 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.50}$  |
|                           | 20          | 3.6%  | $57.80 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{3.58}$  | $62.56 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.71}$  | $62.76 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{0.45}$  | $64.92 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.35}$  | $67.71 colorbox{rgb}{0.9333333333333333,0.9333333333333333,0.9333333333333333}{1.22}$  |                                                                                         |

<span id="page-4-0"></span>Table 1: The classification performance comparison to baselines. We report the ROC-AUC for the first three datasets and accuracies (%) for others. Whole Dataset indicates the performance with original dataset.

DosCond and for pre-training the model embedding in coreset methods. In practice, we find that DosCond even requires less training cost than the coreset methods as shown in Section [3.2.2.](#page-5-0)

# 3 EXPERIMENT

In this section, we conduct experiments to evaluate DosCond. Particularly, we aim to answer the following questions: (a) how well can we condense a graph dataset and (b) how efficient is DosCond. Our code can be found in the supplementary files.

## 3.1 Experimental settings

Datasets. To evaluate the performance of our method, we use multiple molecular datasets from Open Graph Benchmark (OGB) [\[13\]](#page-8-21) and TU Datasets (DD, MUTAG and NCI1) [\[28\]](#page-8-22) for graph-level property classification, and one superpixel dataset CIFAR10 [\[8\]](#page-8-23). We also introduce a real-world e-commerce dataset. In particular, we randomly sample 1,109 sub-graphs from a large, anonymized internal knowledge graph. Each sub-graph is created from the ego network of a random selected product on the e-commerce website. We form a binary classification problem aiming at predicting the product category of the central product node in each sub-graph. We use the public splits for OGB datasets and CIFAR10. For TU Datasets and the e-commerce dataset, we randomly split the graphs into 80%/10%/10% for training/validation/test. Detailed dataset statistics are shown in Appendix [A.2.](#page-9-4)

Baselines. We compare our proposed methods with four baselines that produce discrete structures: three coreset methods (Random, Herding [\[42\]](#page-8-20) and K-Center [\[10,](#page-8-24) [32\]](#page-8-19)), and a dataset condensation method DCG [\[54\]](#page-9-0): (a) Random: it randomly picks graphs from the training dataset. (b) Herding: it selects samples that are closest to the cluster center. Herding is often used in replay-based methods for continual learning [\[3,](#page-8-25) [31\]](#page-8-26). (c) K-Center: it selects the center samples to minimize the largest distance between a sample and its nearest center. (d) DCG: As vanilla DC [\[54\]](#page-9-0) cannot generate discrete structure, we randomly select graphs from training and apply DC to learn the features for them, which we term as DCG. We use the implementations provided by Zhao et al. [\[54\]](#page-9-0) for Herding, K-Center and DCG. Note that coreset methods only select existing samples from training while DCG learns the node features.

Evaluation Protocol. To evaluate the effectiveness of the proposed method, we test the classification performance of GNNs trained with condensed graphs on the aforementioned graph datasets. Concretely, it involves three stages: (1) learning synthetic graphs, (2) training a GCN on the synthetic graphs and (3) test the performance of GCN. We first generate the condensed graphs following the procedure in Algorithm 1. Then we train a GCN classifier with the condensed graphs. Finally we evaluate its classification performance on the real graphs from test set. For baseline methods, we first get the selected/condensed graphs and then follow the same procedure. We repeat the generation process of condensed graphs 5 times with different random seeds and train GCN on these graphs

with 10 different random seeds. In all experiments, we report the mean and standard deviation of these results.

Parameter Settings. When learning the synthetic graphs, we adopt 3-layer GCN with 128 hidden units as the model for gradient matching. The learning rates for structure and feature parameters are set to 1.0 (0.01 for ogbg-molbace and CIFAR10) and 0.01, respectively. We set  $K_1$  to 1000 and  $\beta$  to 0.1. Additionally, we use mean pooling to obtain graph representation for all datasets except ogbg-molhiv. We use sum pooling for ogbg-molhiv as it achieves better classification performance on the real dataset. During the test stage, we use GCN with the same architecture and we train the model for 500 epochs (100 epochs for ogbg-molhiv) with an initial learning rate of 0.001.

## 3.2 Performance with Condensed Graphs

3.2.1 Classification Performance Comparison. To validate the effectiveness of the proposed framework, we measure the classification performance of GCN trained on condensed graphs. Specifically, we vary the number of learned synthetic graphs per class in the range of {1, 10, 50} ({1, 10, 20} for MUTAG and E-commerce) and train a GCN on these graphs. Then we evaluate the classification performance of the trained GCN on the original test graphs. Following the convention in OGB [\[13\]](#page-8-21), we report the ROC-AUC metric for ogbg-molbace, ogbg-molbbbp and ogbg-molhiv; for other datasets we report the classification accuracy  $(\%)$ . The results are summarized in Table [1.](#page-4-0) Note that the Ratio column presents the ratio of synthetic graphs to original graphs and we name it as condensation ratio; the Whole Dataset column shows the GCN performance achieved by training on the original dataset. From the table, we make the following observations:

- (a) The proposed DosCond consistently achieves better performance than the baseline methods under different condensation ratios and different datasets. Notably, when generating only 2 graphs on ogbg-molbace dataset (0.2%), we achieve an ROC-AUC of 0.657 while the performance on full training set is 0.714, which means we approximate 92% of the original performance with only 0.2% data. Likewise, we are able to approximate 96.5% of the original performance on ogbg-molhiv with 0.3% data. By contrast, baselines underperform our method by a large margin. Similar observations can be made on other datasets, which demonstrates the effectiveness of learned synthetic graphs in preserving the information of the original dataset.
- (b) Increasing the number of synthetic graphs can improve the classification performance. For example, we can approximate the original performance by 89%/93%/98% with 0.2%/2.1%/10.6% data on DD. More synthetic samples indicate more learnable parameters that can preserve the information residing in the original dataset and present more diverse patterns that can help train GNNs better. This observation is in line with our experimental results in Section [3.3.1.](#page-6-2)
- (c) The performance on CIFAR10 is less promising due to the limit number of synthetic graphs. We posit that the dataset has more complex topology and feature information and thus requires more parameters to preserve sufficient information. However, we note that our method still outperforms the baseline methods especially when producing only 1 sample per class, which

Table 2: Comparison of running time (minutes).

<span id="page-5-1"></span>

|    |       | CIFAR10          |                  | ogbg-molhiv                                           | DD               |                  |
|----|-------|------------------|------------------|-------------------------------------------------------|------------------|------------------|
|    |       |                  |                  | G./Cls.   Herding DosCond   Herding DosCond   Herding |                  | DosCond          |
| 1  | 44.5m | 4.7 <sub>m</sub> | 4.3 <sub>m</sub> | 0.66m                                                 | 1.6 <sub>m</sub> | 1.5 <sub>m</sub> |
| 10 | 44.5m | 4.9 <sub>m</sub> | 4.3 <sub>m</sub> | 0.67 <sub>m</sub>                                     | 1.6 <sub>m</sub> | 1.5 <sub>m</sub> |
| 50 | 44.5m | 5.7 <sub>m</sub> | 4.3 <sub>m</sub> | 0.68 <sub>m</sub>                                     | 1.6 <sub>m</sub> | 2.0 <sub>m</sub> |

suggests that our method is much more data-efficient. Moreoever, we are able to promote the performance on CIFAR10 by learning a larger synthetic set as shown in Section [3.3.1.](#page-6-2)

(d) Learning both synthetic graph structure and node features is necessary for preserving the information in original graph datasets. By checking the performance DCG, which only learns node features based on randomly selected graph structure, we see that DCG underperforms DosCond by a large margin in most cases. This indicates that learning node features solely is sub-optimal for condensing graphs.

<span id="page-5-0"></span>3.2.2 Efficiency Comparison. Since one of our goals is to enable scalable dataset condensation, we now evaluate the efficiency of DosCond. We compare DosCond with the coreset method Herding, as it is less time-consuming than DCG and generally achieves better performance than other baselines. We adopt the same setting as in Table [1:](#page-4-0) 1000 iterations for *DosCond*, i.e.,  $K_1 = 1000$ , and 500 epochs (100 epochs for ogbg-molhiv) for pre-training the graph convolutional network as required by Herding. We also note that pre-training the neural network need to go over the whole dataset at every epoch while DosCond only processes a batch of graphs. In Table [2,](#page-5-1) we report the running time on an NVIDIA V100 GPU for CIFAR10, ogbg-molhiv and DD. From the table, we make two observations:

- (a) DosCond can be faster than Herding. In fact, DosCond requires less training time in all the cases except in DD with 50 graphs per class. Herding needs to fully training the model on the whole dataset to obtain good-quality embedding, which can be quite time-consuming. On the contrary, DosCond only requires matching gradients for  $K_1$  initializations and does not need to fully train the model on the large real dataset.
- (b) The running time of DosCond increases with the increase of the number of synthetic graphs N'. It is because DosCond processes the condensed graphs at each iteration, of which the time complexity is  $O(N'L(n^2d + nd^2))$  for an *L*-layer GCN. Thus, the additional complexity depends on  $N'$ . By contrast, the increase of ′ has little impact on Herding since the process of selecting samples based on pre-defined heuristic is very fast.
- (c) The average nodes in synthetic graph  $n$  also impacts the training cost of DosCond. For instance, the training cost on ogbg-molhiv  $(n=26)$  is much lower than that on DD ( $n=285$ ), and the gap of cost between the two methods on ogbg-molhiv and DD is very different. As mentioned earlier, it is because the complexity of the forward process in GCN is  $O(N'L(n^2d + nd^2))$  for N' condensed graphs with node size of  $n$ .

To summarize, the efficiency difference of Herding and DosCond depends on the number of condensed/selected samples and the training iterations adopted in practice and we empirically found that DosCond consumes less training cost.

Condensing Graphs via One-Step Gradient Matching

<span id="page-6-6"></span><span id="page-6-3"></span>Image /page/6/Figure/2 description: The image displays two rows of figures related to parameter analysis. The top row contains four plots. Figure 1(a) shows "Learning larger synthetic set" with "Absolute Accuracy (%)" on the y-axis and "Number of Samples Per Class" on the x-axis, comparing "DosCond" and "Random". Figure 1(b) shows "One-Step v.s. bi-Level matching" with "Relative Accuracy (%)" on the y-axis and "Training Time (min)" on the x-axis, comparing "DosCond" and "DosCond-Bi". Figures 1(c) and 1(d) show "Varying \beta on DD" and "Varying \beta on NCI1" respectively, with "Accuracy/Sparsity (%)" on the y-axis and "\beta" on the x-axis, plotting "Acc" and "Sparsity" for both. The bottom row contains four t-SNE visualizations. Figure 2(a) is labeled "Random", 2(b) is labeled "DCG", 2(c) is labeled "DosCond", and 2(d) is labeled "Whole Dataset". Each t-SNE plot shows points colored blue for "class 0" and orange for "class 1", illustrating data distribution.

Image /page/6/Figure/3 description: Figure 2: T-SNE visualizations of embedding learned with condensed graphs on DD.

<span id="page-6-0"></span>

## 3.3 Further Investigation

In this subsection, we perform further investigations to provide a better understanding of our proposed method.

<span id="page-6-2"></span>3.3.1 Increasing the Number of Synthetic Graphs. We study whether the classification performance can be further boosted when using larger synthetic size. Concretely, we vary the size of the learned graphs from 1 to 300 and report the results of absolute and relative accuracy w.r.t. whole dataset training accuracy for CIFAR10 in Figure [1a.](#page-6-3) It is clear to see that both Random and DosCond achieve better performance when we increase the number of samples used for training. Moreover, our method outperforms the random baseline under different condensed dataset sizes. It is worth noting that the performance gap between the two methods diminishes with the increase of the number of samples. This is because the random baseline will finally approach the whole dataset training if we continue to enlarge the size of the condensed set, in which the performance can be considered as the upper bound of DosCond.

3.3.2 Ablation Study. To examine how different model components affect the model performance, we perform ablation study on the proposed one-step gradient matching and regularization terms. We create an ablation of our method, namely DosCond-Bi, which adopts the vanilla gradient matching scheme that involves a bi-level optimization. Without loss of generality, we compare the training time and classification accuracy of DosCond and DosCond-Bi in the setting of learning 50 graphs/class synthetic graphs on CIFAR10 dataset. The results are summarized in Figure [2c](#page-6-4) and we can see that DosCond needs approximately 5 minutes to reach the performance of DosCond-Bi trained for 75 minutes, which indicates that DosCond only requires 6.7% training cost. It further demonstrates the efficiency of the proposed one-step gradient matching strategy.

Next we study the effect of sparsity regularization on DosCond. Specifically, we vary the sparsity coefficient  $\beta$  in the range of {0, 0.001, 0.01, 0.1, 1, 10} and report the classification accuracy and

<span id="page-6-5"></span><span id="page-6-4"></span>graph sparsity on DD and NCI datasets in Figure [2d.](#page-6-5) Note that the graph sparsity is defined as the ratio of the number of edges to the square of the number of nodes. As shown in the figure, when  $\beta$  gets larger, we exert a stronger regularization on the learned graphs and the graphs become more sparse. Furthermore, the increased sparsity does not affect the classification performance. This is a desired property since sparse graphs can save much space for storage and reduce training cost for GNNs. We also remove the regularization of Eq. [\(14\)](#page-3-0) for ogbg-molhiv, we obtain the performance of 0.724/ 0.727/0.731 for 1/10/50 graphs per class, which is slightly worse than the one with this regularization.

3.3.3 Visualization. We further investigate whether GCN can learn discriminative representations from the synthetic graphs learned by DosCond. Specifically, we use t-SNE [\[34\]](#page-8-27) to visualize the learned graph representation from GCN trained on different condensed graphs. We train a GCN on graphs produced by different methods and use it to extract the latent representation for real graphs from test set. Without loss of generality, we provide the t-SNE plots on DD dataset with 50 graphs per class in Figure [2.](#page-6-6) It is observed that the graph representations learned with randomly selected graphs are mixed for different classes. This suggests that using randomly selected graphs cannot help GCN learn discriminative features. Similarly, DCG graphs also resulted in poorly trained GCN that outputs indistinguishable graph representations. By contrast, the representations are well separated for different classes when learned with DosCond graphs (Figure [2c](#page-6-6)) and they are as discriminative as those learned on the whole training dataset (Figure [2d](#page-6-6)). This demonstrates that the graphs learned by DosCond preserve sufficient information of the original dataset so as to recover the original performance.

<span id="page-6-1"></span>3.3.4 Scale of the two terms in Eq. [\(11\)](#page-3-1). As mentioned earlier in Section [2.3,](#page-2-1) the scale of the first term is essentially larger than the second term in Eq. [\(11\)](#page-3-1). We now perform empirical study to verify this statement. Since both terms contain the factor  $M$ , we simply

|                                | Cora, $r=2.6%$              | Citeseer, $r=1.8%$          | Pubmed, $r=0.3%$            | Arxiv, $r=0.25%$              | Flickr, $r=0.1%$             |
|--------------------------------|-----------------------------|-----------------------------|-----------------------------|-------------------------------|------------------------------|
| <i>GCond</i><br><i>DosCond</i> | 80.1 (75.9s)<br>80.0 (3.5s) | 70.6 (71.8s)<br>71.0 (2.8s) | 77.9 (51.7s)<br>76.0 (1.3s) | 59.2 (494.3s)<br>59.0 (32.9s) | 46.5 (51.9s)<br>46.1 (14.3s) |
| <b>Whole Dataset</b>           | 81.5                        | 71.7                        | 79.3                        | 71.4                          | 47.2                         |

<span id="page-7-3"></span>Table 3: Node classification accuracy (%) comparison. The numbers in parentheses indicate the running time for 100 epochs and  *indicates the ratio of number of nodes in the condensed graph to that in the original graph.* 

<span id="page-7-1"></span><span id="page-7-0"></span>Image /page/7/Figure/4 description: Figure 3 displays two plots illustrating the scale of two terms, labeled l1 and l2, across epochs. Plot (a), titled 'DD', shows the loss value on the y-axis ranging from 0.00 to 0.12 and epochs on the x-axis from 0 to 1000. The l1 term (blue line) starts at approximately 0.12 and rapidly decreases to near 0.01 within the first 100 epochs, remaining low thereafter. The l2 term (red line) is a constant at approximately 0.01. Plot (b), titled 'ogbg-molhiv', shows the loss value on the y-axis ranging from 0.0 to 4.0 and epochs on the x-axis from 0 to 1000. The l1 term (blue line) starts at approximately 3.8 and drops sharply to around 0.5 within the first 50 epochs, then fluctuates between 0.2 and 0.8. The l2 term (red line) is a constant at approximately 0.7. Both plots indicate that the l1 term generally decreases over epochs, while the l2 term remains relatively stable.

<span id="page-7-2"></span>drop it and focus on studying  $\ell_1 = \sqrt{2} \|\nabla_{\theta} \ell_{\mathcal{T}}(\theta_0) - \nabla_{\theta} \ell_S(\theta_0)\|$ and  $\ell_2 = \frac{3}{2}$  $\frac{3}{2\sqrt{T}} \cdot \frac{C-1}{CN'} \sqrt{\sum_i \gamma_i ||\mathbf{1}^\top \mathbf{A}_{(i)}^{\prime K} \mathbf{X}_{(i)}^{\prime}||^2}$ . Specifically, we set T to 500 and  $\overrightarrow{N'}$  to 50, and plot the changes of these two terms during the training process of DosCond. The results on DD (with mean pooling) and ogbg-molhiv (with sum pooling) are shown in Figure [3.](#page-7-0) We can observe that the scale of  $\ell_1$  is much larger than  $\ell_2$  at the first few epochs when using mean pooling as shown in Figure [3a.](#page-7-1) By contrast,  $\ell_2$  is not negligible when using sum pooling as shown in Figure [3b](#page-7-2) and it is desired to include it as a regularization term in this case. These observations provide support for ours discussion of theoretical analysis in Section [2.3.](#page-2-1)

<span id="page-7-4"></span>

## 3.4 Node Classification

Next, we investigate whether the proposed method works well in node classification so as to support our analysis in Theorem 2 in Appendix [B.2.](#page-10-0) Specifically, following GCond [\[16\]](#page-8-15), a condensation method for node classification, we use 5 node classification datasets: Cora, Citeseer, Pubmed [\[19\]](#page-8-4), ogbn-arxiv [\[13\]](#page-8-21) and Flickr [\[51\]](#page-8-28). The dataset statistics are shown in [5.](#page-9-5) We follow the settings in GCond to generate one condensed graph for each dataset, train a GCN on the condensed graph, and evaluate its classification performance on the original test nodes. To adopt DosCond into node classification, we replace the bi-level gradient matching scheme in GCond with our proposed one-step gradient matching. The results of classification accuracy and running time per epoch are summarized in Table [3.](#page-7-3) From the table, we make the following observations:

- (a) The proposed DosCond achieves similar performance as GCond and the performance is also comparable to the original dataset. For example, we are able to approximate the original training performance by 99% with only 2.6% data on Cora. It demonstrates the effectiveness of DosCond in the node classification case and justifies Theorem 2 from an empirical perspective.
- (b) The training cost of DosCond is essentially lower than GCond as DosCond avoids the expensive bi-level optimization. By examining their running time, we can see that DosCond is up to 40 times faster than GCond.

We further note that GCond produces weighted graphs which require storing the edge weights in float formats, while DosCond outputs discrete graph structure which can be stored as binary values. Hence, the graphs learned by *DosCond* are more memory-efficient.

# 4 RELATED WORK

Graph Neural Networks. As the generalization of deep neural network to graph data, graph neural networks (GNNs) [\[15,](#page-8-29) [19,](#page-8-4) [20,](#page-8-30) [26,](#page-8-31) [33,](#page-8-32) [35,](#page-8-5) [40,](#page-8-33) [43,](#page-8-18) [44\]](#page-8-6) have revolutionized the field of graph representation learning through effectively exploiting graph structural information. GNNs have achieved remarkable performances in basic graphrelated tasks such as graph classification [\[12,](#page-8-34) [45\]](#page-8-35), link prediction [\[9\]](#page-8-2) and node classification [\[19\]](#page-8-4). Recent years have also witnessed their great success achieved in many real-world applications such as recommender systems [\[9\]](#page-8-2), computer vision [\[22\]](#page-8-36), drug discovery [\[7\]](#page-8-37) and etc. GNNs take both adjacency matrix and node feature matrix as input and output node-level representations or graph-level representations. Essentially, they follow a message-passing scheme [\[11\]](#page-8-38) where each node first aggregates the information from its neighborhood and then transforms the aggregated information to update its representation. Furthermore, there is significant progress in developing deeper GNNs [\[14,](#page-8-39) [25\]](#page-8-40), self-supervised GNNs [\[39,](#page-8-41) [49,](#page-8-42) [50\]](#page-8-43) and graph data augmentation [\[5,](#page-8-44) [55,](#page-9-6) [56\]](#page-9-7).

Dataset Distillation & Dataset Condensation. It is widely received that training neural networks on large datasets can be pro-hibitively costly. To alleviate this issue, dataset distillation (DD) [\[38\]](#page-8-13) aims to distill knowledge of a large training dataset into a small number of synthetic samples. DD formulates the distillation process as a learning-to-learning problem and solves it through bilevel optimization. To improve the efficiency of DD, dataset condensation (DC) [\[52,](#page-8-14) [54\]](#page-9-0) is proposed to learn the small synthetic dataset by matching the gradients of the network parameters w.r.t. large-real and small-synthetic training data. It has been demonstrated that these condensed samples can facilitate critical applications such as continual learning [\[18,](#page-8-45) [21,](#page-8-46) [52–](#page-8-14)[54\]](#page-9-0), neural architecture search [\[29,](#page-8-10) [30,](#page-8-11) [46\]](#page-8-47) and privacy-preserving scenarios [\[6\]](#page-8-48) Recently, following the gradient matching scheme in DC, Jin et al. [\[16\]](#page-8-15) propose a condensation method to condense a large-scale graph to a small graph for node classification. Different from [\[16\]](#page-8-15) which learns weighted graph structure, we aim to solve the challenge of learning discrete structure and we majorly target at graph classification. Moreover, our method avoids the costly bi-level optimization and is much more efficient than the previous work. A detailed comparison is included in Section [3.4.](#page-7-4)

# 5 CONCLUSION

Training graph neural networks on a large-scale graph dataset consumes high computational cost. One solution to alleviate this issue is to condense the large graph dataset into a small synthetic dataset.

In this work, we propose a novel framework DosCond that adopts a one-step gradient matching strategy to efficiently condenses real graphs into a small number of informative graphs with discrete structures. We further justify the proposed method from both theoretical and empirical perspectives. Notably, our experiments show that we are able to reduce the dataset size by 90% while approximating up to 98% of the original performance. In the future, we plan to investigate interpretable condensation methods and diverse applications of the condensed graphs.

# ACKNOWLEDGEMENT

Wei Jin and Jiliang Tang are supported by the National Science Foundation (NSF) under grant numbers IIS1714741, CNS1815636, IIS1845081, IIS1907704, IIS1928278, IIS1955285, IOS2107215, and IOS2035472, the Army Research Office (ARO) under grant number W911NF-21-1-0198, and Amazon.com, Inc.

#### REFERENCES

- <span id="page-8-17"></span>[1] Abubakar Abid, Muhammad Fatih Balin, and James Zou. 2019. Concrete autoencoders for differentiable feature selection and reconstruction. arXiv preprint arXiv:1901.09346 (2019).
- <span id="page-8-3"></span>[2] Peter W Battaglia, Jessica B Hamrick, Victor Bapst, Alvaro Sanchez-Gonzalez, Vinicius Zambaldi, Mateusz Malinowski, Andrea Tacchetti, David Raposo, Adam Santoro, Ryan Faulkner, et al. 2018. Relational inductive biases, deep learning, and graph networks. ArXiv preprint (2018).
- <span id="page-8-25"></span>[3] Francisco M Castro, Manuel J Marín-Jiménez, Nicolás Guil, Cordelia Schmid, and Karteek Alahari. 2018. End-to-end incremental learning. In ECCV.
- <span id="page-8-9"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. 2022. Dataset distillation by matching training trajectories. In CVPR.
- <span id="page-8-44"></span>[5] Kaize Ding, Zhe Xu, Hanghang Tong, and Huan Liu. 2022. Data augmentation for deep graph learning: A survey. arXiv preprint arXiv:2202.08235 (2022).
- <span id="page-8-48"></span>[6] Tian Dong, Bo Zhao, and Lingjuan Lyu. 2022. Privacy for Free: How does Dataset Condensation Help Privacy?. In ICML.
- <span id="page-8-37"></span>[7] David Duvenaud, Dougal Maclaurin, Jorge Aguilera-Iparraguirre, Rafael Gómez-Bombarelli, Timothy Hirzel, Alán Aspuru-Guzik, and Ryan P. Adams. 2015. Convolutional Networks on Graphs for Learning Molecular Fingerprints. In NeurIPS.
- <span id="page-8-23"></span>[8] Vijay Prakash Dwivedi, Chaitanya K Joshi, Thomas Laurent, Yoshua Bengio, and Xavier Bresson. 2020. Benchmarking Graph Neural Networks. arXiv preprint arXiv:2003.00982 (2020).
- <span id="page-8-2"></span>[9] Wenqi Fan, Yao Ma, Qing Li, Yuan He, Yihong Eric Zhao, Jiliang Tang, and Dawei Yin. 2019. Graph Neural Networks for Social Recommendation. In WWW.
- <span id="page-8-24"></span>[10] Reza Zanjirani Farahani and Masoud Hekmatfar. 2009. Facility location: concepts, models, algorithms and case studies.
- <span id="page-8-38"></span>[11] Justin Gilmer, Samuel S. Schoenholz, Patrick F. Riley, Oriol Vinyals, and George E. Dahl. 2017. Neural Message Passing for Quantum Chemistry. In ICML.
- <span id="page-8-34"></span>[12] Zhichun Guo, Chuxu Zhang, Wenhao Yu, John Herr, Olaf Wiest, Meng Jiang, and Nitesh V Chawla. 2021. Few-shot graph learning for molecular property prediction. In Proceedings of the Web Conference 2021. 2559–2567.
- <span id="page-8-21"></span>[13] Weihua Hu, Matthias Fey, Marinka Zitnik, Yuxiao Dong, Hongyu Ren, Bowen Liu, Michele Catasta, and Jure Leskovec. 2020. Open Graph Benchmark: Datasets for Machine Learning on Graphs. In NeurIPS.
- <span id="page-8-39"></span>[14] Wei Jin, Xiaorui Liu, Yao Ma, Charu Aggarwal, and Jiliang Tang. 2022. Feature Overcorrelation in Deep Graph Neural Networks: A New Perspective. In KDD.
- <span id="page-8-29"></span>[15] Wei Jin, Yao Ma, Xiaorui Liu, Xianfeng Tang, Suhang Wang, and Jiliang Tang. 2020. Graph Structure Learning for Robust Graph Neural Networks. In KDD.
- <span id="page-8-15"></span>[16] Wei Jin, Lingxiao Zhao, Shichang Zhang, Yozen Liu, Jiliang Tang, and Neil Shah. 2022. Graph Condensation for Graph Neural Networks. In ICLR 2022.
- <span id="page-8-49"></span>[17] Krishnateja Killamsetty, Durga S, Ganesh Ramakrishnan, Abir De, and Rishabh Iyer. 2021. GRAD-MATCH: Gradient Matching based Data Subset Selection for Efficient Deep Model Training. In ICML. PMLR.
- <span id="page-8-45"></span>[18] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. 2022. Dataset Condensation via Efficient Synthetic-Data Parameterization. arXiv:2205.14959 (2022).
- <span id="page-8-4"></span>[19] Thomas N. Kipf and Max Welling. 2017. Semi-Supervised Classification with Graph Convolutional Networks. In ICLR.
- <span id="page-8-30"></span>[20] Johannes Klicpera, Aleksandar Bojchevski, and Stephan Günnemann. 2019. Predict then Propagate: Graph Neural Networks meet Personalized PageRank. In ICLR 2019.
- <span id="page-8-46"></span>[21] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. 2022. Dataset Condensation with Contrastive Signals. In ICML.

- <span id="page-8-36"></span>[22] Guohao Li, Matthias Müller, Ali K. Thabet, and Bernard Ghanem. 2019. Deep-GCNs: Can GCNs Go As Deep As CNNs?. In ICCV.
- <span id="page-8-8"></span>[23] Zhizhong Li and Derek Hoiem. 2017. Learning without forgetting. IEEE transactions on pattern analysis and machine intelligence 40, 12 (2017), 2935–2947.
- <span id="page-8-7"></span>[24] Hanxiao Liu, Karen Simonyan, and Yiming Yang. 2019. DARTS: Differentiable Architecture Search. In ICLR.
- <span id="page-8-40"></span>[25] Meng Liu, Hongyang Gao, and Shuiwang Ji. 2020. Towards deeper graph neural networks. In KDD.
- <span id="page-8-31"></span>[26] Meng Liu, Youzhi Luo, Kanji Uchino, Koji Maruhashi, and Shuiwang Ji. 2022. Generating 3D Molecules for Target Protein Binding. In ICML.
- <span id="page-8-16"></span>[27] Chris J Maddison, Andriy Mnih, and Yee Whye Teh. 2016. The concrete distribution: A continuous relaxation of discrete random variables. arXiv preprint arXiv:1611.00712 (2016).
- <span id="page-8-22"></span>[28] Christopher Morris, Nils M Kriege, Franka Bause, Kristian Kersting, Petra Mutzel, and Marion Neumann. 2020. Tudataset: A collection of benchmark datasets for learning with graphs. arXiv preprint arXiv:2007.08663 (2020).
- <span id="page-8-10"></span>[29] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. 2021. Dataset Meta-Learning from Kernel Ridge-Regression. In ICLR.
- <span id="page-8-11"></span>[30] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. 2021. Dataset distillation with infinitely wide convolutional networks. NeurIPS 34 (2021).
- <span id="page-8-26"></span>[31] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H. Lampert. 2017. iCaRL: Incremental Classifier and Representation Learning. In CVPR.
- <span id="page-8-19"></span>[32] Ozan Sener and Silvio Savarese. 2018. Active Learning for Convolutional Neural Networks: A Core-Set Approach. In ICLR.
- <span id="page-8-32"></span>[33] Xianfeng Tang, Yandong Li, Yiwei Sun, Huaxiu Yao, Prasenjit Mitra, and Suhang Wang. 2020. Transferring robustness for graph neural network against poisoning attacks. In WSDM.
- <span id="page-8-27"></span>[34] Laurens Van der Maaten and Geoffrey Hinton. 2008. Visualizing data using t-SNE. Journal of machine learning research 11 (2008).
- <span id="page-8-5"></span>[35] Petar Velickovic, Guillem Cucurull, Arantxa Casanova, Adriana Romero, Pietro Liò, and Yoshua Bengio. 2018. Graph Attention Networks. In ICLR.
- <span id="page-8-1"></span>[36] Daixin Wang, Jianbin Lin, Peng Cui, Quanhui Jia, Zhen Wang, Yanming Fang, Quan Yu, Jun Zhou, Shuang Yang, and Yuan Qi. 2019. A Semi-Supervised Graph Attentive Network for Financial Fraud Detection. In ICDM.
- <span id="page-8-12"></span>[37] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. 2022. Cafe: Learning to condense dataset by aligning features. In CVPR.
- <span id="page-8-13"></span>[38] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. 2018. Dataset distillation. ArXiv preprint (2018).
- <span id="page-8-41"></span>[39] Yu Wang, Wei Jin, and Tyler Derr. 2022. Graph Neural Networks: Self-supervised Learning. In Graph Neural Networks: Foundations, Frontiers, and Applications. Springer, 391–420.
- <span id="page-8-33"></span>[40] Yu Wang, Yuying Zhao, Yushun Dong, Huiyuan Chen, Jundong Li, and Tyler Derr. 2022. Improving Fairness in Graph Neural Networks via Mitigating Sensitive Attribute Leakage. In KDD.
- <span id="page-8-50"></span>[41] Jeremy Watt, Reza Borhani, and Aggelos K Katsaggelos. 2020. Machine learning refined: Foundations, algorithms, and applications. Cambridge University Press.
- <span id="page-8-20"></span><span id="page-8-18"></span>[42] Max Welling. 2009. Herding dynamical weights to learn. In ICML. [43] Felix Wu, Amauri H. Souza Jr., Tianyi Zhang, Christopher Fifty, Tao Yu, and
- Kilian Q. Weinberger. 2019. Simplifying Graph Convolutional Networks. In ICML.
- <span id="page-8-6"></span>[44] Zonghan Wu, Shirui Pan, Fengwen Chen, Guodong Long, Chengqi Zhang, and Philip S Yu. 2019. A comprehensive survey on graph neural networks. ArXiv preprint (2019)
- <span id="page-8-35"></span>[45] Keyulu Xu, Weihua Hu, Jure Leskovec, and Stefanie Jegelka. 2019. How Powerful are Graph Neural Networks?. In ICLR.
- <span id="page-8-47"></span>[46] Shuo Yang, Zeke Xie, Hanyu Peng, Min Xu, Mingming Sun, and Ping Li. 2022. Dataset Pruning: Reducing Training Data by Examining Generalization Influence. arXiv preprint arXiv:2205.09329 (2022).
- <span id="page-8-51"></span>[47] Rahul Yedida, Snehanshu Saha, and Tejas Prashanth. 2021. LipschitzLR: Using theoretically computed adaptive learning rates for fast convergence. Applied Intelligence 51, 3 (2021), 1460–1478.
- <span id="page-8-0"></span>[48] Zhitao Ying, Jiaxuan You, Christopher Morris, Xiang Ren, William L. Hamilton, and Jure Leskovec. 2018. Hierarchical Graph Representation Learning with Differentiable Pooling. In NeurIPS.
- <span id="page-8-42"></span>Yuning You, Tianlong Chen, Yang Shen, and Zhangyang Wang. 2021. Graph Contrastive Learning Automated. In ICML.
- <span id="page-8-43"></span>[50] Yuning You, Tianlong Chen, Zhangyang Wang, and Yang Shen. 2020. When Does Self-Supervision Help Graph Convolutional Networks?. In ICML.
- <span id="page-8-28"></span>[51] Hanqing Zeng, Hongkuan Zhou, Ajitesh Srivastava, Rajgopal Kannan, and Viktor K. Prasanna. 2020. GraphSAINT: Graph Sampling Based Inductive Learning Method. In ICLR.
- <span id="page-8-14"></span>[52] Bo Zhao and Hakan Bilen. 2021. Dataset Condensation with Differentiable Siamese Augmentation. In ICML (Proceedings of Machine Learning Research).
- [53] Bo Zhao and Hakan Bilen. 2021. Dataset Condensation with Distribution Matching. arXiv preprint arXiv:2110.04181 (2021).

- <span id="page-9-0"></span>[54] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. 2021. Dataset Condensation with Gradient Matching. In ICLR.
- <span id="page-9-6"></span>[55] Tong Zhao, Gang Liu, Stephan Günnemann, and Meng Jiang. 2022. Graph Data Augmentation for Graph Machine Learning: A Survey. arXiv:2202.08871 (2022).
- <span id="page-9-7"></span>[56] Tong Zhao, Yozen Liu, Leonardo Neves, Oliver Woodford, Meng Jiang, and Neil Shah. 2021. Data augmentation for graph neural networks. In AAAI.

# A EXPERIMENTAL SETUP

<span id="page-9-3"></span>

## A.1 Algorithm

Algorithm 1: DosCond for Condensing Graphs

<span id="page-9-2"></span>1: **Input:** Training data  $\mathcal{T} = (A, X, \mathcal{Y})$ 

- 2: Required: Pre-defined condensed labels  $\mathcal{Y}'$ , graph neural network  $f_{\theta}$ , temperature  $\tau$ , desired sparsity  $\epsilon$ , regularization coefficient  $\beta$ , learning rates  $\eta_1$ ,  $\eta_2$ , number of epochs  $K_1$ .
- 3: Initialize  $\Omega, X'$
- 4: for  $k = 0, ..., K_1 1$  do
- 5: Sample  $θ_0 ~ ∼ P_{θ_0}$
- 6: Sample  $\alpha \sim \text{Uniform}(0, 1)$
- 7: Compute  $A' = \sigma ((\log \alpha \log(1 \alpha) + \Omega) / \tau)$
- 8: **for**  $c = 0, ..., C 1$  **do**
- 9: Sample  $(A_c, X_c, \mathcal{Y}_c) \sim \mathcal{T}$  and  $(A'_c, X'_c, \mathcal{Y}'_c) \sim \mathcal{S}$
- 10: Compute  $\ell_T = \ell \left( f_{\theta_0}(\mathbf{A}_c, \mathbf{X}_c), \mathbf{y}_c \right)$
- 11: Compute  $\ell_S = \ell \left( f_{\theta_0} (A_c', X_c'), \mathcal{Y}_c' \right)$
- 12: Compute  $\ell_{\text{reg}} = \max(\sum_{i,j} \sigma(\Omega_{ij}) \epsilon, 0)$
- 13: Update  $\Omega \leftarrow \Omega \eta_1 \nabla_{\Omega} (D(\nabla_{\theta_0} \ell_T, \nabla_{\theta_0} \ell_S) + \beta \ell_{\text{reg}})$
- 14: Update  $X' \leftarrow X' \eta_2 \nabla_{X'} (D(\nabla_{\theta_0} \ell_T, \nabla_{\theta_0} \ell_S) + \beta \ell_{reg})$
- 15: end for
- 16: end for
- 17: Return:  $(Ω, X', Y')$

<span id="page-9-4"></span>

## A.2 Dataset Statistics and Code

Dataset statistics are shown in Table 4 and 5. We provide our code in the supplementary file for the purpose of reproducibility.

Table 4: Graph classification dataset statistics.

| Dataset      | Type        | #Clases | #Graphs | Avg. Nodes | Avg. Edges |
|--------------|-------------|---------|---------|------------|------------|
| CIFAR10      | Superpixel  | 10      | 60,000  | 117.6      | 941.07     |
| ogbg-molhiv  | Molecule    | 2       | 41,127  | 25.5       | 54.9       |
| ogbg-molbace | Molecule    | 2       | 1,513   | 34.1       | 36.9       |
| ogbg-molbbbp | Molecule    | 2       | 2,039   | 24.1       | 26.0       |
| MUTAG        | Molecule    | 2       | 188     | 17.93      | 19.79      |
| NCI1         | Molecule    | 2       | 4,110   | 29.87      | 32.30      |
| DD           | Molecule    | 2       | 1,178   | 284.32     | 715.66     |
| E-commerce   | Transaction | 2       | 1,109   | 33.7       | 56.3       |

<span id="page-9-5"></span>Table 5: Node classification dataset statistics.

| Dataset  | #Nodes  | #Edges    | #Classes | #Features |
|----------|---------|-----------|----------|-----------|
| Cora     | 2,708   | 5,429     | 7        | 1,433     |
| Citeseer | 3,327   | 4,732     | 6        | 3,703     |
| Pubmed   | 19,717  | 44,338    | 3        | 500       |
| Arxiv    | 169,343 | 1,166,243 | 40       | 128       |
| Flickr   | 89,250  | 899,756   | 7        | 500       |

# B PROOFS

<span id="page-9-1"></span>

## B.1 Proof of Theorem [1](#page-2-4)

Let  $A_{(i)}$ ,  $X_{(i)}$  denote the adjacency matrix and the feature matrix of *i*-th real graph, respectively. We denote the cross entropy loss on the real samples as  $\ell_{\mathcal{T}}(\theta) = \sum_i \ell_i(\mathbf{A}_{(i)}, \mathbf{X}_{(i)}, \theta)$  and denote that on synthetic samples as  $\ell_S(\theta) = \ell_S(A'_{(i)}, X'_{(i)}, \theta)$ . Let  $\theta^*$  denote the optimal parameter and let  $\theta_t$  be the parameter trained on condensed data at *t*-th epoch by optimizing  $\ell_S(\theta)$ . For simplicity of notations, we assume A and A' are already normalized. Part of the proof is inspired from the work [\[17\]](#page-8-49).

THEOREM 1. When we use a linearized  $K$ -layer SGC as the GNN used in condensation, i.e.,  $f_{\theta}(\mathbf{A}_{(i)}, \mathbf{X}_{(i)}) = \text{Pool}(\mathbf{A}_{(i)}^K \mathbf{X}_{(i)} \mathbf{W}_1) \mathbf{W}_2$ with  $\theta = [\mathbf{W}_1; \mathbf{W}_2]$  and assume that all network parameters satisfy  $\|\theta\|^2 \le M^2(M>0)$ , we have

$$
\min_{t=0,1,\dots,T-1} \ell_{\mathcal{T}}\left(\theta_{t}\right) - \ell_{\mathcal{T}}\left(\theta^{*}\right) \leq \sum_{t=0}^{T-1} \frac{\sqrt{2}M}{T} \|\nabla_{\theta}\ell_{\mathcal{T}}\left(\theta_{t}\right) - \nabla_{\theta}\ell_{S}\left(\theta_{t}\right)\|
$$
$$
+ \frac{3M}{2\sqrt{T}} \cdot \frac{C-1}{CN'} \sqrt{\sum_{i} \gamma_{i} \|\mathbf{1}^{\top} \mathbf{A}_{(i)}^{K} \mathbf{X}_{(i)}^{\prime\|}^{2}} (15)
$$

where  $\gamma_i = 1$  if we use sum pooling in  $f_\theta$ ;  $\gamma_i = \frac{1}{n_i}$  if we use mean pooling, with  $n_i$  being the number of nodes in i-th synthetic graph.

Proof. We start by proving that  $\ell_{\mathcal{T}}(\theta)$  is convex and  $\ell_{S}(\theta)$  is lipschitz continuous when we use  $f_{\theta}(\mathbf{A}_{(i)}, \mathbf{X}_{(i)}) = \text{Pool}(\mathbf{A}_{(i)}^K \mathbf{X}_{(i)} \mathbf{W}_1) \mathbf{W}_2$ as the mapping function. Before proving these two properties, we first rewrite  $f_{\theta}(\mathbf{A}_{(i)}, \mathbf{X}_{(i)})$  as:

$$
f_{\theta}(\mathbf{A}_{(i)}, \mathbf{X}_{(i)}) = \begin{cases} \mathbf{1}^{\top} \mathbf{A}_{(i)}^{K} \mathbf{X}_{(i)} \mathbf{W}_{1} \mathbf{W}_{2} & \text{if use sum pooling,} \\ \frac{1}{n_{i}} \mathbf{1}^{\top} \mathbf{A}_{(i)}^{K} \mathbf{X}_{(i)} \mathbf{W}_{1} \mathbf{W}_{2} & \text{if use mean pooling,} \end{cases}
$$
(16)

where *n* is the number of nodes in  $A_{(i)}$  and 1 is an  $n_i \times 1$  matrix filled with constant one. From the above equation we can see that  $f_{\theta}$  with different pooling methods only differ in a multiplication factor  $\frac{1}{n_i}$ . Thus, in the following we focus on  $f_\theta$  with sum pooling to derive the major proof.

#### I. For $f_{\theta}$ with sum pooling:

Substitute **W** for  $\mathbf{W}_1 \mathbf{W}_2$  and we have  $f_\theta(\mathbf{A}_{(i)}, \mathbf{X}_{(i)}) = \mathbf{1}^\top \mathbf{A}_{(i)}^K \mathbf{X}_{(i)} \mathbf{W}$ for the case with sum pooling. Next we show that  $\ell_{\mathcal{T}}(\theta)$  is convex and  $\ell_S(\theta)$  is lipschitz continuous when we use  $f_{\theta}(\mathbf{A}_{(i)}, \mathbf{X}_{(i)}) =$  $\mathbf{1}^\top \mathbf{A}_{(i)}^K \mathbf{X}_{(i)} \mathbf{W}$  with  $\theta = \mathbf{W}$ .

(a) Convexity of  $\ell_{\mathcal{T}}(\theta)$ . From chapter 4 of the book [\[41\]](#page-8-50), we know that softmax classification  $f(\mathbf{W}) = \mathbf{X}\mathbf{W}$  with cross entropy loss is convex w.r.t. the parameters W. In our case, the mapping function  $f_{\theta}(\mathbf{A}_{(i)}, \mathbf{X}_{(i)}) = \mathbf{1}^{\top} \mathbf{A}_{(i)}^K \mathbf{X}_{(i)} \mathbf{W}$  applies an affine function on XW. Given that applying affine function does not change the convexity, we know that  $\ell_{\mathcal{T}}(\theta)$  is convex.

(b) Lipschitz continuity of  $\ell_S(\theta)$ . In [\[47\]](#page-8-51), it shows that the lipschitz constant of softmax regression with cross entropy loss is  $\frac{C-1}{Cm}||X||$ , where  $X$  is the input feature matrix,  $C$  is the number of classes and *m* is the number of samples. Since  $\ell_S(\theta)$  is cross entropy loss and  $f_\theta$ is linear, we know that the  $f_{\theta}$  is lipschitz continuous and it satisfies:

<span id="page-9-8"></span>
$$
\nabla_{\theta} \ell_{S}(\theta) \le \frac{C - 1}{C N'} \sqrt{\sum_{i} ||\mathbf{1}^{\top} \mathbf{A}_{(i)}^{\prime K} \mathbf{X}_{(i)}^{\prime}||^2}
$$
(17)

*Condensing Graphs via One-Step Gradient Matching*

With (a) and (b), we are able to proceed our proof. First, from the convexity of  $\ell_{\mathcal{T}}(\theta)$  we have

<span id="page-10-1"></span>
$$
\ell_{\mathcal{T}}\left(\theta_{t}\right) - \ell_{\mathcal{T}}\left(\theta^{*}\right) \leq \nabla_{\theta} \ell_{\mathcal{T}}\left(\theta_{t}\right)^{T}\left(\theta_{t} - \theta^{*}\right) \tag{18}
$$

We can rewrite  $\nabla_{\theta} \ell_{\mathcal{T}} (\theta_t)^T (\theta_t - \theta^*)$  as follows:

$$
\nabla_{\theta} \ell_{\mathcal{T}} (\theta_t)^T (\theta_t - \theta^*) = (\nabla_{\theta} \ell_{\mathcal{T}} (\theta_t)^T - \nabla_{\theta} \ell_{S} (\theta_t)^T + \nabla_{\theta} \ell_{S} (\theta_t)^T) (\theta_t - \theta^*)
$$
  
=  $( \nabla_{\theta} \ell_{\mathcal{T}} (\theta_t)^T - \nabla_{\theta} \ell_{S} (\theta_t)^T) (\theta_t - \theta^*) + \nabla_{\theta} \ell_{S} (\theta_t)^T (\theta_t - \theta^*)$  (19)

Given that we use gradient descent to update network parameters, we have  $\nabla_{\theta} \ell_S (\theta_t) = \frac{1}{n} (\theta_t - \theta_{t+1})$  where  $\eta$  is the learning rate. Then we have,

$$
\nabla_{\theta} \ell_{S} (\theta_{t})^{T} (\theta_{t} - \theta^{*}) = \frac{1}{\eta} (\theta_{t} - \theta_{t+1})^{T} (\theta_{t} - \theta^{*})
$$
  
= 
$$
\frac{1}{2\eta} (\|\theta_{t} - \theta_{t+1}\|^2 + \|\theta_{t} - \theta^{*}\|^2 - \|\theta_{t+1} - \theta^{*}\|^2)
$$
  
= 
$$
\frac{1}{2\eta} (\|\eta \nabla_{\theta} \ell_{S} (\theta_{t})\|^2 + \|\theta_{t} - \theta^{*}\|^2 - \|\theta_{t+1} - \theta^{*}\|^2)
$$
 (20)

Combining Eq. [\(18\)](#page-10-1) and Eq. [\(20\)](#page-10-2) we have,

$$
\ell_{\mathcal{T}}(\theta_t) - \ell_{\mathcal{T}}(\theta^*) \le (\nabla_{\theta} \ell_{\mathcal{T}}(\theta_t)^T - \nabla_{\theta} \ell_{S}(\theta_t)^T) (\theta_t - \theta^*)
$$
  
+ 
$$
\frac{1}{2\eta} \left( \|\eta \nabla_{\theta} \ell_{S}(\theta_t)\|^2 + \|\theta_t - \theta^*\|^2 - \|\theta_{t+1} - \theta^*\|^2 \right) (21)
$$

We sum up the two sides of the above inequality for different values of  $t \in [0, T-1]$ :

$$
\sum_{t=0}^{T-1} \ell_{\mathcal{T}}(\theta_t) - \ell_{\mathcal{T}}(\theta^*) \le \sum_{t=0}^{T-1} (\nabla_{\theta} \ell_{\mathcal{T}}(\theta_t)^T - \nabla_{\theta} \ell_{S}(\theta_t)^T) (\theta_t - \theta^*)
$$
$$
+ \frac{1}{2\eta} \sum_{t=0}^{T-1} ||\eta \nabla_{\theta} \ell_{S}(\theta_t)||^2 + \frac{1}{2\eta} ||\theta_0 - \theta^*||^2 - \frac{1}{2\eta} ||\theta_T - \theta^*||^2
$$
(22)

Since  $\frac{1}{2n} \|\theta_T - \theta^*\|^2 \ge 0$ , we have

$$
\sum_{t=0}^{T-1} \ell_{\mathcal{T}}(\theta_t) - \ell_{\mathcal{T}}(\theta^*) \le \sum_{t=0}^{T-1} (\nabla_{\theta} \ell_{\mathcal{T}}(\theta_t)^T - \nabla_{\theta} \ell_{S}(\theta_t)^T) (\theta_t - \theta^*)
$$
$$
+ \frac{1}{2\eta} \sum_{t=0}^{T-1} ||\eta \nabla_{\theta} \ell_{S}(\theta_t)||^2 + \frac{1}{2\eta} ||\theta_0 - \theta^*||^2 \tag{23}
$$

As we assume that  $||\theta||^2 \le M^2$ , we have  $||\theta - \theta^*||^2 \le 2||\theta||^2 = 2M^2$ . Then Eq. [\(23\)](#page-10-3) can be rewritten as,

$$
\sum_{t=0}^{T-1} \ell_T(\theta_t) - \ell_T(\theta^*) \le \sum_{t=0}^{T-1} \sqrt{2}M \|\nabla_{\theta}\ell_T(\theta_t) - \nabla_{\theta}\ell_S(\theta_t)\| + \frac{1}{2\eta} \sum_{t=0}^{T-1} \|\eta \nabla_{\theta}\ell_S(\theta_t)\|^2 + \frac{M^2}{\eta} \tag{24}
$$

Recall that  $\ell_S(\theta)$  is lipschitz continuous as shown in Eq. [\(17\)](#page-9-8), and combine  $\min_{t=0,1,...,T-1} (\ell_{\mathcal{T}}(\theta_t) - \ell_{\mathcal{T}}(\theta^*)) \leq \frac{\sum_{t=0}^{T-1} \ell_{\mathcal{T}}(\theta_t) - \ell_{\mathcal{T}}(\theta^*)}{T}$ 

$$
\begin{split} \min_{t=0,1,...,T-1}\ell_{\mathcal{T}}\left(\theta_{t}\right)-&\ell_{\mathcal{T}}\left(\theta^{*}\right)\leq\sum_{t=0}^{T-1}\frac{\sqrt{2}M}{T}\left\|\nabla_{\theta}\ell_{\mathcal{T}}\left(\theta_{t}\right)-\nabla_{\theta}\ell_{S}\left(\theta_{t}\right)\right\|\\ &+\frac{\eta(C-1)^{2}}{2C^{2}N^{\prime2}}\sum_{i}\left\|1^{\top}\mathbf{A}_{(i)}^{\prime K}\mathbf{X}_{(i)}^{\prime}\right\|^{2}+\frac{M^{2}}{T\eta}\end{split} \tag{25}
$$

Condensing Graphs via One-Step Gradient Matching KDD '22, August 14-18, 2022, Washington, DC, USA

Then we choose 

 $\eta = \frac{M}{\sqrt{T} \sqrt{\sum_i ||\mathbf{1}^\top \mathbf{A}_{(i)}^K \mathbf{X}_{(i)}||^2}}$  and we can get:

$$
\min_{t=0,1,...,T-1} \ell_{\mathcal{T}}(\theta_t) - \ell_{\mathcal{T}}(\theta^*) \le \sum_{t=0}^{T-1} \frac{\sqrt{2}M}{T} \|\nabla_{\theta} \ell_{\mathcal{T}}(\theta_t) - \nabla_{\theta} \ell_{S}(\theta_t)\| + \frac{3M}{2\sqrt{T}} \cdot \frac{C-1}{CN'} \sqrt{\sum_i ||\mathbf{1}^\top \mathbf{A}_{(i)}^K \mathbf{X}_{(i)}||^2} \quad (26)
$$

## II. For $f_\theta$ with mean pooling:

Following similar derivation as in the case of sum pooling, we have

$$
\begin{aligned} \min_{t=0,1,...,T-1}\ell_{\mathcal{T}}\left(\theta_{t}\right)-\ell_{\mathcal{T}}\left(\theta^{*}\right) & \leq \sum_{t=0}^{T-1}\frac{\sqrt{2}M}{T}\left\|\nabla_{\theta}\ell_{\mathcal{T}}\left(\theta_{t}\right)-\nabla_{\theta}\ell_{S}\left(\theta_{t}\right)\right\| \\ & +\frac{3M}{2\sqrt{T}}\cdot\frac{C-1}{CN'}\sqrt{\sum_{i}\frac{1}{n_{i}}\left\|1^{\top}A'^{K}_{(i)}X'_{(i)}\right\|^{2}} \end{aligned} \tag{27}
$$

<span id="page-10-2"></span>where  $n_i$  is the number of nodes in *i*-th synthetic graph.  $\Box$ 

<span id="page-10-0"></span>

## B.2 Theorem for Node Classification Case

We adopt similar notations for representing the data in node classification but note that there is only one graph for node classification task. Let  $A \in \{0, 1\}^{N \times N}$ ,  $A' \in \{0, 1\}^{N' \times N'}$  denote the adjacency matrix for real graph and synthetic graph, respectively. Let  $X \in \mathbb{R}^{N \times d}$ ,  $X' \in \mathbb{R}^{N' \times d}$  denote the feature matrix for real graph and synthetic graph, respectively. We denote the cross entropy loss on the real samples as  $\ell_{\mathcal{T}}(\theta)$  and denote that on synthetic samples as  $\ell_{\mathcal{S}}(\theta)$ .

THEOREM 2. When we use a K-layer SGC as the model used in condensation, i.e.,  $f_{\theta}(\mathbf{A}, \mathbf{X}, \theta) = \mathbf{A}^K \mathbf{X} \mathbf{W}$  with  $\theta = \mathbf{W}$  and assume that all network parameters satisfy  $||\theta||^2 \leq M^2(M > 0)$ , we have

$$
\begin{split} \min_{t=0,1,...,T-1}\ell_{\mathcal{T}}\left(\theta_{t}\right)-&\ell_{\mathcal{T}}\left(\theta^{*}\right)\leq\sum_{t=0}^{T-1}\frac{\sqrt{2}M}{T}\left\|\nabla_{\theta}\ell_{\mathcal{T}}\left(\theta_{t}\right)-\nabla_{\theta}\ell_{S}\left(\theta_{t}\right)\right\|+\frac{3M}{2\sqrt{T}}\cdot\frac{C-1}{CN'}\left\|\mathbf{A}^{\prime K}\mathbf{X}^{\prime}\right\|\end{split} \tag{28}
$$

PROOF. We start by proving that  $\ell_{\mathcal{T}}(\theta)$  is convex and  $\ell_{\mathcal{S}}(\theta)$  is lipschitz continuous when  $f_{\theta}(\mathbf{A}, \mathbf{X}, \theta) = \mathbf{A}^K \mathbf{X} \mathbf{W}$ .

<span id="page-10-3"></span>(a) Convexity of  $\ell_{\mathcal{T}}(\theta)$ : Similar to the graph classification case, the Hessian matrix of  $\ell_{\mathcal{T}}(\theta)$  in node classification is positive semidefinite and thus  $\ell_{\mathcal{T}}(\theta)$  is convex.

(b) Lipschitz continuity of  $\ell_S(\theta)$ : As shown in [\[47\]](#page-8-51), the lipschitz constant of softmax regression with cross entropy loss is  $\frac{\dot{C}-1}{Cm}||X||$ with  $C$  being the number of classes and  $m$  being the number of samples. Thus, we know that the lipschitz constant of  $\ell_S(\theta)$  is  $\frac{C-1}{CN'}\|\mathbf{A'}^K\mathbf{X'}\|$ , which indicates  $\nabla_\theta \ell_S(\theta) \leq \frac{C-1}{CN'}\|\mathbf{A'}^K\mathbf{X'}\|$ .

<span id="page-10-4"></span>From the convexity of  $\ell_{\mathcal{T}}(\theta)$ , we still have the following inequal-ity (see Eq. [\(24\)](#page-10-4)). Then recall that  $\ell_S(\theta)$  is lipschitz continuous and  $\nabla_{\theta} \ell_{S}(\theta) \leq \frac{C-1}{CN'} ||A^{\prime K} X'||$ , and combine min  $(\ell_{\mathcal{T}}(\theta_t) - \ell_{\mathcal{T}}(\theta^*)) \leq$  $\frac{\sum_{t=0}^{T-1} \ell_{\mathcal{T}}(\theta_t) - \ell_{\mathcal{T}}(\theta^*)}{T}$ 

$$
\min_{t=0,1,\dots,T-1} \ell_{\mathcal{T}}\left(\theta_{t}\right) - \ell_{\mathcal{T}}\left(\theta^{*}\right) \leq \sum_{t=0}^{T-1} \frac{\sqrt{2}M}{T} \|\nabla_{\theta}\ell_{\mathcal{T}}\left(\theta_{t}\right) - \nabla_{\theta}\ell_{S}\left(\theta_{t}\right) \| + \frac{\eta(C-1)^{2}}{2C^{2}N'^{2}} \|\mathbf{A}'^{K}\mathbf{X}'\|^{2} + \frac{M^{2}}{T\eta}
$$
(29)

<span id="page-11-0"></span>KDD '22, August 14-18, 2022, Washington, DC, USA Wei Jin et al.

Then we choose 
$$
\eta = \frac{M}{\sqrt{T} ||A'^K X'||}
$$
 and we can get:  
\n
$$
\min_{t=0,1,...,T-1} \ell_T(\theta_t) - \ell_T(\theta^*) \le \sum_{t=0}^{T-1} \frac{\sqrt{2}M}{T} ||\nabla_\theta \ell_T(\theta_t) - \nabla_\theta \ell_S(\theta_t) ||
$$
\n
$$
+ \frac{3M}{2\sqrt{T}} \cdot \frac{C-1}{CN'} ||A'^K X'|| \qquad (30)
$$

Wei Jin et al.

 $\Box$
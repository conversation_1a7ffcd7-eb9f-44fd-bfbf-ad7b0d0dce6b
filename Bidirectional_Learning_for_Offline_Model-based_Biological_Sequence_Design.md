# Bidirectional Learning for Offline Model-based Biological Sequence Design

Can (Sam) Chen<sup>12</sup> <PERSON><PERSON><PERSON><sup>3</sup> <PERSON><PERSON><sup>1</sup> <PERSON>ates<sup>1</sup>

## Abstract

Offline model-based optimization aims to maximize a black-box objective function with a static dataset of designs and their scores. In this paper, we focus on biological sequence design to maximize some sequence score. A recent approach employs bidirectional learning, combining a forward mapping for exploitation and a backward mapping for constraint, and it relies on the neural tangent kernel (NTK) of an infinitely wide network to build a proxy model. Though effective, the NTK cannot learn features because of its parametrization, and its use prevents the incorporation of powerful pre-trained Language Models (LMs) that can capture the rich biophysical information in millions of biological sequences. We adopt an alternative proxy model, adding a linear head to a pretrained LM, and propose a linearization scheme. This yields a closed-form loss and also takes into account the biophysical information in the pretrained LM. In addition, the forward mapping and the backward mapping play different roles and thus deserve different weights during sequence optimization. To achieve this, we train an auxiliary model and leverage its weak supervision signal via a bi-level optimization framework to effectively learn how to balance the two mappings. Further, by extending the framework, we develop the first learning rate adaptation module *Adaptive*- $\eta$ , which is compatible with all gradient-based algorithms for offline model-based optimization. Experimental results on DNA/protein sequence design tasks verify the effectiveness of our algorithm. Our code is available [here.](https://anonymous.4open.science/r/BIB-ICML2023-Submission/README.md)

# 1. Introduction

Offline model-based optimization aims to maximize a blackbox objective function with a static dataset of designs and

their scores. This offline setting is realistic since in many real-world scenarios we do not have interactive access to the ground-truth evaluation. The design tasks of interest include material, aircraft, and biological sequence [\(Trabucco](#page-9-0) [et al., 2021\)](#page-9-0). In this paper, we focus on biological sequence design, including DNA/protein sequence, with the goal of maximizing some specified property of these sequences.

A wide variety of methods have been proposed for biological sequence design, including evolutionary algorithms [\(Sinai](#page-9-1) [et al., 2020;](#page-9-1) [Ren et al., 2022\)](#page-9-2), reinforcement learning methods [\(Angermueller et al., 2019\)](#page-8-0), Bayesian optimization [\(Terayama et al., 2021\)](#page-9-3), search/sampling using generative models [\(Brookes et al., 2019;](#page-8-1) [Chan et al., 2021\)](#page-8-2), and GFlowNets [\(Jain et al., 2022\)](#page-8-3). Recently, gradient-based techniques have emerged as an effective alternative [\(Tra](#page-9-0)[bucco et al., 2021\)](#page-9-0). These approaches first train a deep neural network (DNN) on the static dataset as a proxy and then obtain the new designs by directly performing gradient ascent steps on the existing designs. Such methods have been widely used in biological sequence design [\(Norn et al., 2021;](#page-9-4) [Tischer et al., 2020;](#page-9-5) [Linder & Seelig, 2020\)](#page-9-6). One obstacle is the out-of-distribution issue, where the trained proxy model is inaccurate for the newly generated sequences.

To mitigate the out-of-distribution issue, recent work proposes regularization of the model [\(Trabucco et al., 2021;](#page-9-0) [Yu](#page-10-0) [et al., 2021;](#page-10-0) [Fu & Levine, 2021\)](#page-8-4) or the design itself [\(Chen](#page-8-5) [et al., 2022b\)](#page-8-5). The first category focuses on training a better proxy by introducing inductive biases such as robustness [\(Yu](#page-10-0) [et al., 2021\)](#page-10-0). The second category introduces bidirectional learning [\(Chen et al., 2022b\)](#page-8-5), which consists of a forward mapping and a backward mapping, to optimize the design directly. Specifically, the backward mapping leverages the high-scoring design to predict the static dataset and vice versa for the forward mapping, which distills the information of the static dataset into the high-scoring design. This approach achieves state-of-the-art performances on a variety of tasks. Though effective, the proposed bidirectional learning relies on the neural tangent kernel (NTK) of an infinite-width model to yield a closed-form loss, which is a key component of its successful operation. The NTK cannot learn features due to its parameterization [\(Yang & Hu,](#page-10-1) [2021\)](#page-10-1) and thus the bidirectional learning cannot incorporate the wealth of biophysical information from Language Models (LMs) pre-trained over a vast corpus of unlabelled

 $1\text{McGill University }^2$ Mila - Quebec AI Institute  $3\text{Huawei}$ Noah's Ark Lab. Correspondence to: Can (Sam) Chen <<EMAIL>>.

sequences [\(Elnaggar et al., 2021;](#page-8-6) [Ji et al., 2021\)](#page-8-7).

To solve this issue, we construct a proxy model by combining a finite-width pre-trained LM with an additional layer. We then linearize the resultant proxy model, inspired by the recent progress in deep linearization [\(Achille et al., 2021;](#page-8-8) [Dukler et al., 2022\)](#page-8-9). This scheme not only yields a closedform loss but also exploits the rich biophysical information that has been distilled in the pre-trained LM. In addition, the forward mapping encourages exploitation in the sequence space and the backward mapping serves as a constraint to mitigate the out-of-distribution issue. It is vital to maintain an appropriate balance between exploitation and constraint, and this can vary across design tasks as well as during the optimization process. We introduce a hyperparameter  $\gamma$  to control the balance. However, how to properly select  $\gamma$  is challenging because of the problem's offline optimization nature. Thus, we develop a bi-level optimization framework *Adaptive*-γ. In this framework, we train an auxiliary model and leverage its weak supervision signal to effectively update γ. To sum up, we propose *BIdirectional learning for model-based Biological sequence design* (BIB).

Since the offline nature prohibits standard cross-validation strategies for hyperparameter tuning, all current gradientbased offline model-based algorithms preset the learning rate  $\eta$ . There is a danger of poor selection, and to address this, we propose an *Adaptive*-η module, which effectively adapts the learning rate  $\eta$  via the weak supervision signal from the trained auxiliary model. To the best of our knowledge,  $Adaptive-\eta$  is the first learning rate adaptation module for gradient-based algorithms for offline model-based optimization. We discuss the relationship between the *Adaptive* modules and previous hyperparameter optimization work in Sec. [5.](#page-6-0) Experiments on DNA and protein sequence design tasks verify the effectiveness of BIB and *Adaptive*-η.

To summarize, our contributions are three-fold:

- Instead of adopting the NTK, we construct a proxy model by combining a pre-trained biological LM with an additional trainable layer. We then linearize the proxy model, leveraging the recent progress on deep linearization. This yields a closed-form loss computation in bidirectional learning and allows us to exploit the rich biophysical information distilled into the LM via pre-training over millions of biological sequences.
- We propose a bi-level optimization framework *Adaptive*-γ where we leverage weak signals from an auxiliary model to achieve a satisfactory trade-off between design exploitation and constraint.
- We further extend this bi-level optimization framework to *Adaptive*-η. As the first learning rate tuning scheme in offline model-based optimization, *Adaptive*-η allows learning rate adaptation for any gradient-based algorithm.

<span id="page-1-0"></span>

## 2. Preliminaries

### 2.1. Offline Model-based Optimization

Offline model-based optimization aims to find a design  $X$ to maximize some unknown objective  $f(X)$ . This can be formally written as,

$$
\boldsymbol{X}^* = \arg\max_{\boldsymbol{X}} f(\boldsymbol{X}), \tag{1}
$$

where we have access to a size-N dataset  $D =$  $\{(\boldsymbol{X}_1, y_1)\}, \cdots, \{(\boldsymbol{X}_N, y_N)\}\$  with  $\boldsymbol{X}_i$  representing a certain design and  $y_i$  denoting the design score. In this paper,  $X_i$  represents a biological sequence design, including DNA and protein sequences, and  $y_i$  represents a property of the biological sequence such as the fluorescence level of the green fluorescent protein [\(Sarkisyan et al., 2016\)](#page-9-7).

#### 2.2. Biological Sequence Representation

Following [\(Norn et al., 2021;](#page-9-4) [Killoran et al., 2017;](#page-9-8) [Linder &](#page-9-9) [Seelig, 2021\)](#page-9-9), we adopt the position-specific scoring matrix to represent a length-L protein sequence as  $X \in \mathbb{R}^{L \times 20}$ , where 20 represents 20 different kinds of amino acids. For a real-world protein,  $X[l, :]$  ( $0 \le l \le L - 1$ ) is a one-hot vector denoting one kind of amino acid. During optimization,  $X[l, :]$  is a continuous vector and softmax $(X[l, :])$ represents the probability distribution of all 20 amino acids in the position *l*. Similarly, for a DNA sequence, we have  $X \in \mathbb{R}^{L \times 4}$  where 4 represents 4 different DNA bases.

The protein sequence  $X$  is fed into the embedding layer of the LM, which produces the embedding,

$$
e = EMB(softmax(X)). \tag{2}
$$

The main block of the LM takes e as input and outputs biophysical features. The DNA LM, which adopts the kmer representation, is a little different from protein LMs. See Appendix [7.1](#page-10-2) for details.

#### 2.3. Gradient Ascent on Sequence

A common approach to the posed offline model-based optimization is to train a proxy  $f_{\theta}(X)$  on the offline dataset,

$$
\boldsymbol{\theta}^* = \arg\min_{\boldsymbol{\theta}} \frac{1}{N} \sum_{i=1}^N (f_{\boldsymbol{\theta}}(\boldsymbol{X}_i) - y_i)^2.
$$
 (3)

Then we can obtain the high-scoring design  $X_h$  by T gradient ascent steps:

$$
\boldsymbol{X}_{t+1} = \boldsymbol{X}_t + \eta \nabla_{\boldsymbol{X}} f_{\boldsymbol{\theta}^*}(\boldsymbol{X}) |_{\boldsymbol{X} = \boldsymbol{X}_t}, \quad \text{for } t \in [0, T-1],
$$
\n(4)

where the high-scoring design  $X_h$  can be obtained as  $X_T$ .

Considering the discrete nature of biological sequences, the input of  $f_{\theta}(\cdot)$  should be discrete one-hot vectors. Following [\(Norn et al., 2021\)](#page-9-4), we can perform the following conversion and predict the score via:

$$
\hat{\boldsymbol{X}}_i = softmax(\boldsymbol{X}_i), \qquad (5)
$$

$$
Z_i = onehot(argmax(\hat{X}_i)), \qquad (6)
$$

$$
\hat{y} = f_{\theta}(\mathbf{Z}_i). \tag{7}
$$

Then the gradient regarding  $X_i$  can be approximated as,

$$
\frac{df_{\boldsymbol{\theta}}(\boldsymbol{Z}_i)}{dx_i} \approx \frac{df_{\boldsymbol{\theta}}(\boldsymbol{Z}_i)}{dz_i} \frac{d\hat{\boldsymbol{x}}_i}{dx_i},
$$
\n(8)

where we unroll the matrices  $\boldsymbol{X}_i$ ,  $\hat{\boldsymbol{X}}_i$  and  $\boldsymbol{Z}_i$  as vectors  $\boldsymbol{x}_i$ ,  $\hat{x}_i$  and  $z_i$  for notational convenience. This approximation allows us to use backpropagation directly from the proxy to the sequence design  $X_i$ . For brevity, we will still use  $f_{\theta}(X_i)$  to represent the proxy.

#### 2.4. Bidirectional Learning

As shown in Figure [1,](#page-3-0) bidirectional learning [\(Chen et al.,](#page-8-5) [2022b\)](#page-8-5), consists of two mappings: the forward mapping leverages the static dataset  $(X_l, y_l)$  to predict the score  $y_h$ of the high-scoring design  $X_h$ , and the backward mapping leverages the high-scoring design data  $(X_h, y_h)$  to predict the static dataset  $(X_l, y_l)$ . The forward mapping loss is

$$
\mathcal{L}_{l2h}(\boldsymbol{X}_h) = ||y_h - f_{\boldsymbol{\theta}^*}^l(\boldsymbol{X}_h)||^2, \qquad (9)
$$

where  $\theta^*$  is given by

$$
\boldsymbol{\theta}^* = \underset{\boldsymbol{\theta}}{\arg\min} \|\boldsymbol{y}_l - f_{\boldsymbol{\theta}}^l(\boldsymbol{X}_l)\|^2 + \beta \|\boldsymbol{\theta}\|^2, \qquad (10)
$$

where  $\beta > 0$  is a regularization parameter. The backward mapping loss can be written as

<span id="page-2-0"></span>
$$
\mathcal{L}_{h2l}(\boldsymbol{X}_h) = \|\boldsymbol{y}_l - f^h_{\boldsymbol{\theta}^*(\boldsymbol{X}_h)}(\boldsymbol{X}_l)\|^2, \qquad (11)
$$

where  $\boldsymbol{\theta}^*(\boldsymbol{X}_h)$  is given by

$$
\boldsymbol{\theta}^*(\boldsymbol{X}_h) = \underset{\boldsymbol{\theta}}{\arg\min} \|\boldsymbol{y}_h - \boldsymbol{f}_{\boldsymbol{\theta}}^h(\boldsymbol{X}_h)\|^2 + \beta \|\boldsymbol{\theta}\|^2. \quad (12)
$$

The high-scoring design  $X_h$  is optimized against the bidirectional learning loss  $\mathcal{L}(\boldsymbol{X}_h) = \mathcal{L}_{l2h}(\boldsymbol{X}_h) + \mathcal{L}_{h2l}(\boldsymbol{X}_h).$ 

## 3. Method

In this section, we first illustrate how to leverage deep linearization to compute the bidirectional learning loss in a closed form. Subsequently, we introduce a hyperparameter  $\gamma$  to control the balance between the forward mapping and the backward mapping. We then develop a novel bi-level optimization framework *Adaptive*-γ, which leverages a weak supervision signal from an auxiliary model to effectively update  $\gamma$ . Last but not least, we extend this framework to *Adaptive-η*, which enables us to adapt the learning rate  $\eta$ for all gradient-based offline model-based algorithms. We summarize our method in Algorithm [1.](#page-3-1)

### 3.1. Deep Linearization for Bidirectional Learning

In bidirectional learning, the backward mapping loss is intractable for a finite neural network, so [Chen et al.](#page-8-5) [\(2022b\)](#page-8-5) employ a neural network with infinite width, which yields a closed-form loss via the NTK. This however makes it impossible to incorporate the rich biophysical information that has been distilled into a pre-trained LM [\(Yang & Hu,](#page-10-1) [2021\)](#page-10-1). Considering this, we construct a proxy model by combining a finite-width pre-trained LM with an additional layer. We then linearize the resultant proxy model, inspired by the recent progress in deep linearization which has established that an overparameterized DNN model is close to its linearization [\(Achille et al., 2021;](#page-8-8) [Dukler et al., 2022\)](#page-8-9).

Denote by  $\theta_0 = (\theta_{pt}, \theta_{init}^{lin}) \in \mathcal{R}^{D \times 1}$  the proxy model parameters derived by combining the parameters of the pre-trained LM  $\theta_{pt}$  and a random initialization of the linear layer  $\theta_{init}^{lin}$ . In this paper, we adopt the pre-trained DNABERT [\(Ji et al., 2021\)](#page-8-7) and Prot-BERT [\(Elnaggar et al.,](#page-8-6) [2021\)](#page-8-6) models, and compute the average of token embeddings as the extracted feature, which is fed into the linear layer to build the proxy. We also study how our method performs as a function of the pre-trained LM quality in Appendix [7.2.](#page-10-3) Then we can construct a linear approximation for the proxy model:

$$
f_{\theta}(X) \approx f_{\theta_0}(X) + \bigtriangledown_{\theta} f_{\theta_0}(X) \cdot (\theta - \theta_0), \qquad (13)
$$

where  $f_{\theta}(X), f_{\theta_0}(X) \in \mathcal{R}, \ \nabla_{\theta} f_{\theta_0}(X) \in \mathcal{R}^{1 \times D}$  and  $\bigtriangledown_{\theta} f_{\theta_0}(X) \in \mathcal{R}^{D \times 1}$ . Intuitively, if the fine-tuning does not significantly change  $\theta_0$ , then this linearization is a good approximation. By leveraging this linearization, we can obtain a closed-form solution for Eq.[\(12\)](#page-2-0) as:

$$
\theta^*(\boldsymbol{X}_h) = (\bigtriangledown_{\theta} f_{\theta_0}(\boldsymbol{X}_h)^{\top} \bigtriangledown_{\theta} f_{\theta_0}(\boldsymbol{X}_h) + \beta \boldsymbol{I})^{-1} \n\bigtriangledown_{\theta} f_{\theta_0}(\boldsymbol{X}_h)^{\top} (y_h - f_{\theta_0}(\boldsymbol{X}_h)) + \theta_0.
$$
\n(14)

Building on this result, we can compute the bidirectional learning loss as:

$$
\mathcal{L}_{bi}(\boldsymbol{X}_h) = \frac{1}{2} (\|y_h - \boldsymbol{K}_{\boldsymbol{X}_h \boldsymbol{X}_l} (\boldsymbol{K}_{\boldsymbol{X}_l \boldsymbol{X}_l} + \beta \boldsymbol{I})^{-1} \n(\boldsymbol{y}_l - f_{\boldsymbol{\theta}_0}(\boldsymbol{X}_l)) \|^2 + \|\boldsymbol{y}_l - \boldsymbol{K}_{\boldsymbol{X}_l \boldsymbol{X}_h} (\boldsymbol{K}_{\boldsymbol{X}_h \boldsymbol{X}_h} + \beta \boldsymbol{I})^{-1} (\boldsymbol{y}_h - f_{\boldsymbol{\theta}_0}(\boldsymbol{X}_h)) \|^2),
$$
\n(15)

where  $\bm{K}(\bm{X}_i,\bm{X}_j)=\bigtriangledown_{\bm{\theta}}f_{\bm{\theta_0}}(\bm{X}_i)\top\bigtriangledown_{\bm{\theta}}f_{\bm{\theta_0}}(\bm{X}_j).$  Following [\(Dukler et al., 2022\)](#page-8-9), we can also only linearize the last layer of the network for simplicity, which defines the following kernel,

$$
\boldsymbol{K}(\boldsymbol{X}_i, \boldsymbol{X}_j) = BERT(\boldsymbol{X}_i)^\top BERT(\boldsymbol{X}_j), \qquad (16)
$$

where  $BERT(X)$  denotes the feature of the sequence X extracted by BERT. Its kernel nature makes this approach suitable for small-data tasks [\(Arora et al., 2020\)](#page-8-10), especially in drug discovery with high labeling cost of DNA/proteins.

Image /page/3/Figure/1 description: The image illustrates a machine learning process involving forward and backward mapping between a static dataset and high-scoring data. The static dataset, labeled 'TFBInd8(r)', consists of sequences of DNA bases (A, G, C, T) represented as columns, with associated numerical values labeled as 'yl'. The forward mapping shows a process where the static data 'Xl' and 'yl' are used to optimize a parameter 'theta\*' through a loss function 'Ll2h(Xh) = ||yh - f\_theta\*(Xh)||^2' and a regularization term '+ beta||theta||^2'. The backward mapping shows the reverse process, using 'Xh' (unknown high-scoring data) and 'yl' to optimize 'theta\*(Xh)' through a loss function 'Lh2l(Xh) = ||yl - f\_theta\_\*(Xh)||^2' and a regularization term '+ beta||theta||^2'. The high-scoring data is represented by 'Xh' (unknown) and 'yh' with a value of 10.0.

Figure 1. Illustration of bidirectional learning [Chen et al.](#page-8-5) [\(2022b\)](#page-8-5) where  $(X_l, y_l)$  denotes the static dataset,  $y_h$  is a large predefined target score and  $X_h$  is the high-scoring design we aim to find.

Algorithm 1 Bidirectional Learning for Offline Modelbased Biological Sequence Design

<span id="page-3-1"></span>**Input:** The static dataset  $\mathcal{D} = (\boldsymbol{X}_l, \boldsymbol{y}_l)$ , the predefined target score  $y_h = 10$ , # iterations T, the pre-trained biological LM parameterized by  $\theta_0$ , the auxiliary model  $f_{aux}(\cdot)$ , the regularization  $\beta$ .

Initialize  $X_0$  as the sequence with the highest score in  $D$ for  $\tau = 0$  to  $T - 1$  do

Leverage Adaptive- $\gamma$  in Sec [3.2](#page-3-1) to update the balance  $\gamma$  by Eq. [\(21\)](#page-3-2)

if Adapt learning rate then Leverage Adaptive- $\eta$  in Sec [3.3](#page-4-0) to update the learning rate  $\eta$  by Eq. [\(23\)](#page-4-1)

end if

Optimize  $X$  by minimizing the bidirectional learning loss  $\mathcal{L}_{bi}(\mathbf{X}_{\tau}, \gamma)$  in Eq. ([17](#page-3-3)):

 $\boldsymbol{X}_{\tau+1} = \boldsymbol{X}_{\tau} - \eta OPT(\nabla_{\boldsymbol{X}} \mathcal{L}_{bi}(\boldsymbol{X}_{\tau}, \gamma))$ end for Return  $\boldsymbol{X}_h^* = \boldsymbol{X}_T$ 

### 3.2. Adaptive- $\gamma$

The forward mapping and the backward mapping play different roles in the sequence optimization process: the forward mapping encourages the high-scoring sequence to search for a higher target score (exploitation) and the backward mapping serves as a constraint. Since different sequences require different degrees of constraint, we introduce an extra hyperparameter  $\gamma \in [0, 1]$  to control the balance between the corresponding terms in the loss function:

$$
\mathcal{L}_{bi}(\boldsymbol{X}_h, \gamma) = \gamma \mathcal{L}_{l2h}(\boldsymbol{X}_h) + (1 - \gamma) \mathcal{L}_{h2l}(\boldsymbol{X}_h).
$$
 (17)

Thus  $\gamma = 1.0$  corresponds to the forward mapping alone,  $\gamma = 0$  results in backward mapping, and  $\gamma = 0.5$  leads to the bidirectional loss of [\(Chen et al., 2022b\)](#page-8-5).

It is non-trivial to determine the most suitable value for  $\gamma$ 

<span id="page-3-0"></span>since we do not know the ground-truth score for a new design. One possible solution is to train an auxiliary  $f_{aux}(\cdot)$  to serve as a proxy evaluation. A reasonable auxiliary is a simple regression model fitted to the offline dataset. Although this auxiliary model cannot yield ground-truth scores, it can provide weak supervision signals to update  $\gamma$ , since the auxiliary model and the bidirectional learning provide complementary information. This is similar to co-teaching [\(Han](#page-8-11) [et al., 2018\)](#page-8-11) where two models leverage each other's view.

Formally, we introduce the *Adaptive*-γ framework. Given a good choice of  $\gamma$ , the produced  $\mathbf{X}_h$  is expected to have a high score  $f_{aux}(\boldsymbol{X}_h)$ , based on which we can choose  $\gamma$ . To make the search for  $\gamma$  more efficient, we can formulate this process as a bi-level optimization problem:

$$
\gamma^* = \arg\max_{\gamma} f_{aux}(\boldsymbol{X}_h^*(\gamma)), \qquad (18)
$$

$$
\text{s.t.} \quad \boldsymbol{X}_h^*(\gamma) = \operatorname*{arg\,min}_{\boldsymbol{X}_h} \mathcal{L}_{bi}(\boldsymbol{X}_h, \gamma). \tag{19}
$$

We can then use the hyper-gradient  $\frac{\partial f_{aux}(X_h^*(\gamma))}{\partial \gamma}$  to update  $\gamma$ . Specifically, the inner level solution can be approximated via a gradient descent step with a learning rate  $\eta$ :

<span id="page-3-2"></span>
$$
\boldsymbol{X}_h^*(\gamma) = \boldsymbol{X}_h - \eta \frac{d\mathcal{L}_{bi}(\boldsymbol{X}_h, \gamma)}{d\boldsymbol{X}_h^\top}.
$$
 (20)

<span id="page-3-3"></span>For the outer level, we update  $\gamma$  by hyper-gradient ascent:

$$
\gamma = \gamma + \eta' \frac{df_{aux}(\mathbf{X}_h^*(\gamma))}{d\gamma} = \gamma + \eta' \frac{df_{aux}(\mathbf{X}_h)}{dx_h} \frac{dx_h^*(\gamma)}{d\gamma}
$$
$$
= \gamma + \eta' \eta \frac{df_{aux}(\mathbf{X}_h)}{dx_h} \frac{d\mathcal{L}_{h2l}(\mathbf{X}_h) - \mathcal{L}_{l2h}(\mathbf{X}_h)}{dx_h^{\top}}, (21)
$$

where we unroll the matrix form  $X_h$  as a vector form  $x_h$ for better illustration.

<span id="page-4-0"></span>

### 3.3. Adaptive- $\eta$

We now extend the *Adaptive*-γ framework to *Adaptive*- $\eta$ . As the first learning rate adaptation module for offline model-based optimization, *Adaptive*-η is compatible with all gradient-based algorithms and can effectively finetune the learning rate  $\eta$  via the auxiliary model's weak supervision signal. All gradient-based methods that maximize  $\mathcal{L}_{\theta}(X)$  with respect to X have the following general form:

$$
\boldsymbol{X}_{t+1} = \boldsymbol{X}_t + \eta OPT(\nabla_{\boldsymbol{X}} \mathcal{L}_{\boldsymbol{\theta}}(\boldsymbol{X}) | \boldsymbol{X} = \boldsymbol{X}_t), \quad \text{for } t \in [0, T-1],
$$
\n(22)

where  $\eta$  represents the learning rate of the optimizer. For methods such as simple gradient ascent (Grad), COMs [\(Tra](#page-9-0)[bucco et al., 2021\)](#page-9-0), ROMA [\(Yu et al., 2021\)](#page-10-0) and NEMO [\(Fu](#page-8-4) [& Levine, 2021\)](#page-8-4),  $\mathcal{L}_{\theta}(\cdot)$  is related to the proxy model  $f_{\theta}(\cdot)$ ; for BDI [\(Chen et al., 2022b\)](#page-8-5) and our proposed method, BIB,  $\mathcal{L}_{\theta}(\cdot)$  is the negative of the bidirectional learning loss, i.e.,  $\mathcal{L}_{\theta} = -\mathcal{L}_{bi}.$ 

Though the learning rate  $\eta$  can be adapted in some optimizers such as Adam [\(Kingma & Ba, 2015\)](#page-9-10), these adaptations rely on only the past optimization history and do not consider the weak supervision signal from the auxiliary model. Our *Adaptive*-η optimizes η by solving:

$$
\eta^* = \arg\max_{\eta} f_{aux}(\boldsymbol{X}_h^*(\eta)), \qquad (23)
$$

where  $\eta$  can be updated via gradient ascent methods. Considering the sequence optimization procedure is highly sensitive to the learning rate  $\eta$ , we reset  $\eta$  to  $\eta_0$  at each iteration and update  $\eta$  from  $\eta_0$ ,

$$
\eta = \eta_0 - \eta' \frac{df_{aux}(\mathbf{X}_h^*(\eta))}{d\eta}.
$$
 (24)

In general, this stabilizes the optimization procedure.

## 4. Experiments

We conduct extensive experiments on DNA and protein design tasks, and aim to answer three research questions: (1) How does BIB compare with state-of-the-art algorithms? (2) Is every design component necessary in BIB? (3) Does the *Adaptive*-η module improve gradient-based methods?

<span id="page-4-2"></span>

### 4.1. Benchmark

We conduct experiments on two DNA tasks: TFBind8(r) and TFBind10(r), following [\(Chen et al., 2022b\)](#page-8-5) and three protein tasks: avGFP, AAV and E4B, in [\(Ren et al., 2022\)](#page-9-2) which have the most data points. See Appendix [7.3](#page-10-4) for more details on task definitions and oracle evaluations. We also study how our method performs with different task dataset sizes in Appendix [7.4.](#page-11-0)

Following [\(Trabucco et al., 2021\)](#page-9-0), we select the top  $N =$ 128 most promising sequences for each comparison method. Among these sequences, we report the maximum normalized ground truth score as the evaluation metric following [\(Ren et al., 2022\)](#page-9-2).

### 4.2. Comparison Methods

We compare BIB with two groups of baselines: the gradientbased methods and the non-gradient-based methods. For a fair comparison, the pre-trained LM is used for all methods involving a proxy and we don't finetune the LM. The gradient-based methods include: 1) Grad: gradient ascent on existing sequences to obtain new sequences; 2) COMs [\(Trabucco et al., 2021\)](#page-9-0): lower bounds the DNN model by the ground-truth values and then applies gradient ascent; 3) ROMA [\(Yu et al., 2021\)](#page-10-0): incorporates a smoothness prior into the DNN model before gradient ascent steps; 4) NEMO [\(Fu & Levine, 2021\)](#page-8-4): leverages the normalized maximum-likelihood estimator to bound the distance between the DNN model and the ground-truth values; 5) BDI [\(Chen et al., 2022b\)](#page-8-5): adopts the infinitely wide NN and its NTK to compute bidirectional learning loss.

<span id="page-4-1"></span>The non-gradient-based methods include: 1) BO-qEI [\(Wil](#page-10-5)[son et al., 2017\)](#page-10-5): builds an acquisition function for sequence exploration; 2) CMA-ES [\(Hansen, 2006\)](#page-8-12): estimates the covariance matrix to adjust the sequence distribution towards the high-scoring region; 3) AdaLead [\(Sinai et al., 2020\)](#page-9-1): performs a hill-climbing search on the proxy and then queries the sequences with high predictions; 4) CbAS [\(Brookes](#page-8-1) [et al., 2019\)](#page-8-1): builds a generative model for sequences above a property threshold and gradually adapts the distribution by increasing the threshold; 5) PEX [\(Ren et al., 2022\)](#page-9-2): prioritizes the evolutionary search for protein sequences with low mutation counts; 6) GENH [\(Chan et al., 2021\)](#page-8-2): enhances the score through a learned latent space.

<span id="page-4-3"></span>

### 4.3. Training Details

We follow the training setting in [\(Chen et al., 2022b\)](#page-8-5) if not specified. We choose  $OPT$  as the Adam optimizer [\(Kingma](#page-9-10) [& Ba, 2015\)](#page-9-10) for all gradient-based methods. We implement the auxiliary model as a linear layer with the feature from the pre-trained LM. We set the number of iterations  $T$  as 25 for all experiments following [\(Norn et al., 2021\)](#page-9-4) and  $\eta_0$ as 0.1 following [\(Chen et al., 2022b\)](#page-8-5). We run every setting over 16 trials and report the mean and standard deviation. See Appendix [7.5](#page-11-1) for other training details.

### 4.4. Results and Analysis

We report all experimental results in Table [1](#page-6-1) and plot the ranking statistics in Figure [2.](#page-5-0) We make the following ob-servations. (1) As shown in Table [1,](#page-6-1) BIB consistently outperforms the Grad method on all tasks, which demonstrates that our BIB can effectively mitigate the out-of-distribution

Image /page/5/Figure/1 description: This is a box plot that ranks different algorithms. The y-axis lists the algorithms: BO-qEI, CMA-ES, AdaLead, CbAS, PEX, GENH, Grad, COMs, ROMA, NEMO, BDI, and BIB(ours). The x-axis is labeled "Rank" and ranges from 1 to 12. Each algorithm has a box plot representing its performance, with a black triangle indicating the median. For example, BIB(ours) has a red box plot with its median at rank 2. BDI has a purple box plot with its median at rank 9. NEMO has a yellow box plot with its median at rank 5. ROMA has a green box plot with its median at rank 5. COMs has a beige box plot with its median at rank 5. Grad has a white box plot with its median at rank 5. GENH has an orange box plot with its median at rank 11. PEX has a white box plot with its median at rank 7. CbAS has a blue box plot with its median at rank 9. AdaLead has a cyan box plot with its median at rank 5. CMA-ES has a gray box plot with its median at rank 5. BO-qEI has a purple box plot with its median at rank 5.

Figure 2. Rank minima and maxima are represented by whiskers; vertical lines and black triangles denote medians and means.

issue. (2) Furthermore, BIB outperforms BDI on 4 out of 5 tasks, which demonstrates the effectiveness of the pretrained biological LM over NTK. We have conducted experiments, reported in Appendix [7.6,](#page-11-2) to verify that the ranking of prediction performance is NN > linearized pre-trained  $LM > NTK$ . The reason why BDI outperforms BIB on TF-Bind10(r) may be that short sequences do not rely much on the rich sequential information from the pre-trained LM. (3) As shown in Figure [2,](#page-5-0) the gradient-based methods generally perform better than the non-gradient-based methods, as also observed by [\(Trabucco et al., 2021\)](#page-9-0). (4) The gradient-based methods are inferior for the AAV task. One possible reason is that the design space of AAV  $(20^{28})$  is much smaller than those of avGFP ( $20^{239}$ ) and E4B ( $20^{102}$ ), which makes the generative modeling and evolutionary algorithms more suitable. (5) This conjecture is also supported by the experimental results on two DNA design tasks. We compute the average ranking of gradient-based methods and non-gradbased methods on TFBind10(r) as 3.5 and 9.5, respectively, and the average ranking of gradient-based methods and nongrad-based methods on TFBind8(r) as 5.8 and 6.8, respectively. The advantage of gradient-based methods are larger  $(9.5-3.5=6.0)$  in TFBind10(r) than that  $(6.8-5.8=1.0)$ in TFBind8(r). (6) The generative modeling methods CbAS and GENH yield poor results on all tasks, probably because the high-dimensional data distribution is very hard to model. (7) Overall, BIB attains the best performance in 3 out of 5 tasks and achieves the best ranking results as shown in Table [1](#page-6-1) and Figure [2.](#page-5-0)

We also visualize the trend of performance (the maximum normalized ground truth score) and trade-off  $\gamma$  as a function of T on TFBind8(r) in Figure [3\(](#page-6-2)a) and avGFP in Figure 3(b). The performance generally increases with the time step  $T$ and then stabilizes, which demonstrates the effectiveness and robustness of BIB. Furthermore, we find that the  $\gamma$  values of TFBind8(r) and avGFP generally increase at first. This means that BIB reduces the impact of the constraint to encourage a more aggressive search for a high target value during the initial phase. Then  $\gamma$  of TFBind8(r) continues to increase while the  $\gamma$  of avGFP decreases. We conjecture that the difference is caused by the sequence length. Small mutations of a biological sequence are enough to yield a good candidate [\(Ren et al., 2022\)](#page-9-2). For the length-239 protein in avGFP, dramatic mutations 1) are not necessary and 2) can easily lead to out-of-distribution points. The weak supervision signal from the auxiliary model therefore encourages a tighter constraint towards the static dataset. By contrast, the DNA sequence is relatively short and a more widespread search of the sequence space can yield better results. To investigate this conjecture, we further visualize the trend of E4B in Figure [3\(](#page-6-2)c). E4B also has long sequences (102) and we can observe its similar first-increase-then-decrease trend, although it is not as pronounced.

<span id="page-5-1"></span><span id="page-5-0"></span>

### 4.5. Ablation Studies

In this subsection, we conduct ablation studies to verify the effectiveness of the forward mapping, the backward mapping, and the *Adaptive*-γ module of BIB. We stress that forward mapping and backward mapping are not our contributions. In this paper, we propose deep linearization for bidirectional mappings. This section explores whether both mappings remain effective after deep linearization has been performed. We report the experimental results in Table [2.](#page-6-3)

Forward mapping & Backward mapping. We can observe that bidirectional learning ( $\gamma = 0.5$ ) performs better than both forward mapping ( $\gamma = 1.0$ ) and backward mapping ( $\gamma = 0.0$ ) alone in most tasks, which demonstrates the effectiveness of forward mapping and backward mapping. The advantage of *bidirectional mappings over the forward mapping* is larger in the long-sequence tasks like avGFP (238) and E4B (102) compared with the shortsequence tasks. A possible explanation is that the constraint is more important for long sequence tasks than short sequence design since the search space is large and many mutations can easily go out of distribution.

**Adaptive-** $\gamma$ . BIB learns  $\gamma$  and this leads to improvements over bidirectional mappings ( $\gamma = 0.5$ ) for all tasks, verifying the effectiveness of Adaptive- $\gamma$ . We also consider the following variant,

$$
\mathbf{X}^* = \arg\min_{\mathbf{X}_h} \mathcal{L}_{bi}(\mathbf{X}_h, 0.5) - f_{aux}(\mathbf{X}_h), \quad (25)
$$

which jointly optimizes the bidirectional learning loss  $\mathcal{L}_{bi}(\boldsymbol{X}_h, 0.5)$  and the auxiliary term  $f_{aux}(\boldsymbol{X}_h)$ . We found this yields similar or even worse results than pure bidirectional learning. The reason may be that the weak supervision signal from  $f_{aux}(\boldsymbol{X}_h)$  can serve as a guide to update the scalar  $\gamma$  but not as a component of the main optimization objective that directly updates the sequence.

In the final column of Table [2,](#page-6-3) we examine the Adaptive- $\eta$ module. Adding this module leads to improvements on all five tasks, which demonstrates its effectiveness.

<span id="page-6-1"></span>

|                                                    | Table 1. Experimental results (maximum normalized ground truth score) for comparison. |                   |                   |                   |                   |           |             |  |
|----------------------------------------------------|---------------------------------------------------------------------------------------|-------------------|-------------------|-------------------|-------------------|-----------|-------------|--|
| Method                                             | TFBind8(r)                                                                            | TFBind10(r)       | avGFP             | AAV               | E4B               | Rank Mean | Rank Median |  |
| $\mathcal{D}(\text{best})$                         | 0.242                                                                                 | 0.248             | 0.314             | 0.452             | 0.224             |           |             |  |
| $BO-qEI$                                           | $0.940 \pm 0.032$                                                                     | $0.595 \pm 0.028$ | $0.888 \pm 0.015$ | $0.591 \pm 0.002$ | $0.436 \pm 0.004$ | 6.0/12    | 7.0/12      |  |
| <b>CMA-ES</b>                                      | $0.930 \pm 0.034$                                                                     | $0.617 \pm 0.031$ | $0.909 \pm 0.004$ | $0.470 \pm 0.006$ | $0.748 \pm 0.009$ | 6.6/12    | 6.0/12      |  |
| AdaLead                                            | $0.941 \pm 0.032$                                                                     | $0.602 \pm 0.028$ | $0.885 \pm 0.016$ | $0.581 \pm 0.002$ | $0.433 \pm 0.003$ | 6.2/12    | 8.0/12      |  |
| CbAS                                               | $0.878 \pm 0.049$                                                                     | $0.610 \pm 0.035$ | $0.785 \pm 0.057$ | $0.543 \pm 0.002$ | $0.349 \pm 0.003$ | 9.0/12    | 10.0/12     |  |
| <b>PEX</b>                                         | $0.924 \pm 0.041$                                                                     | $0.612 \pm 0.026$ | $0.874 \pm 0.033$ | $0.588 \pm 0.002$ | $0.397 \pm 0.004$ | 7.2/12    | 8.0/12      |  |
| <b>GENH</b>                                        | $0.323 \pm 0.000$                                                                     | $0.448 \pm 0.000$ | $0.793 \pm 0.000$ | $0.452 \pm 0.000$ | $0.228 \pm 0.000$ | 11.4/12   | 11.0/12     |  |
| Grad                                               | $0.941 \pm 0.026$                                                                     | $0.630 \pm 0.029$ | $0.913 \pm 0.027$ | $0.463 \pm 0.005$ | $1.219 \pm 0.061$ | 5.0/12    | 5.0/12      |  |
| <b>COMs</b>                                        | $0.921 \pm 0.039$                                                                     | $0.637 \pm 0.065$ | $0.938 \pm 0.048$ | $0.511 \pm 0.005$ | $0.829 \pm 0.026$ | 5.0/12    | 5.0/12      |  |
| <b>ROMA</b>                                        | $0.926 \pm 0.032$                                                                     | $0.634 \pm 0.061$ | $0.975 \pm 0.133$ | $0.471 \pm 0.005$ | $1.198 \pm 0.042$ | 4.8/12    | 4.0/12      |  |
| <b>NEMO</b>                                        | $0.930 \pm 0.038$                                                                     | $0.632 \pm 0.024$ | $0.914 \pm 0.026$ | $0.505 \pm 0.005$ | $1.036 \pm 0.046$ | 4.8/12    | 5.0/12      |  |
| <b>BDI</b>                                         | $0.823 \pm 0.000$                                                                     | $0.678 \pm 0.000$ | $0.873 \pm 0.000$ | $0.452 \pm 0.000$ | $0.224 \pm 0.000$ | 9.0/12    | 11.0/12     |  |
| $\overline{\mathbf{B}}\mathbf{IB}_{(\text{ours})}$ | $0.952 \pm 0.033$                                                                     | $0.639 \pm 0.032$ | $1.060 \pm 0.016$ | $0.501 + 0.007$   | $1.255 + 0.029$   | 2.4/12    | 1.0/12      |  |

Image /page/6/Figure/2 description: The image displays three plots, labeled (a) TFBind8(r), (b) avGFP, and (c) E4B. Each plot shows two lines: a red line representing 'Performance' and a blue line representing 'Trade-off γ'. The x-axis for all plots is labeled 'T' and ranges from 0 to 40. The y-axis on the left of each plot represents 'Performance' and ranges from 0.25 to 1.0 or 1.25. The y-axis on the right of each plot represents 'Trade-off γ' and ranges from 0.4 to 1.0. In plot (a), both performance and trade-off increase rapidly initially and then plateau around 1.0. In plot (b), performance increases and fluctuates between 0.9 and 1.1, while the trade-off increases initially and then decreases to around 0.35. In plot (c), performance increases and fluctuates significantly between 0.25 and 1.25, while the trade-off increases and then levels off around 1.0.

Figure 3. Trend of performance and trade-off  $\gamma$  as a function of T.

|  |  | <i>Table 2.</i> Ablation studies on BIB components. |  |
|--|--|-----------------------------------------------------|--|
|  |  |                                                     |  |

<span id="page-6-3"></span>

| Task     | $\gamma = 0.0$ | $\gamma = 1.0$ | $\gamma = 0.5$ | $\gamma = 0.5 +$ Joint | BIB   | $BIB + Ada-\eta$ |
|----------|----------------|----------------|----------------|------------------------|-------|------------------|
| TFB8(r)  | 0.936          | 0.933          | 0.947          | 0.935                  | 0.952 | 0.956            |
| TFB10(r) | 0.611          | 0.637          | 0.616          | 0.622                  | 0.639 | 0.639            |
| avGFP    | 0.920          | 0.966          | 1.018          | 1.006                  | 1.060 | 1.082            |
| AAV      | 0.449          | 0.458          | 0.480          | 0.420                  | 0.501 | 0.525            |
| E4B      | 0.778          | 0.903          | 1.198          | 1.176                  | 1.255 | 1.301            |

### 4.6. Adaptive- $\eta$

In this subsection, we aim to further demonstrate the effectiveness of the Adaptive- $\eta$  module on all six gradient-based methods. We conduct experiments on two tasks: TFBind8(r) and avGFP. Since the use of the infinitely wide neural network leads to poor performance for BDI, we modify its implementation via deep linearization so that it can make use of the pre-trained LM.

As shown in Table [3,](#page-7-0) *Adaptive*-η provides a consistent gain for all scenarios, which demonstrates the widespread applicability and effectiveness of the module. Furthermore, *Adaptive-η* leads to a maximum improvement of  $1.4\%$  in TFBind8(r) and  $12.5\%$  in avGFP. ROMA is the algorithm that benefits the most. One possible explanation is that ROMA incorporates a local smoothness prior that leads to more stable gradients, with which *Adaptive*-η can be more effective. Similar to Sec [4.5,](#page-5-1) we consider the variant,

$$
\mathbf{X}^* = \arg\max_{\mathbf{X}_h} \mathcal{L}_{\theta}(\mathbf{X}_h) + f_{aux}(\mathbf{X}_h), \qquad (26)
$$

<span id="page-6-2"></span>which performs joint optimization instead of bi-level optimization on two objectives. As shown in Table [3,](#page-7-0) joint optimization generally deteriorates the performance. This again verifies that the auxiliary model can only serve as a guide instead of contributing to the main objective.

<span id="page-6-0"></span>

## 5. Related Work

Biological sequence design. There has been a wide range of algorithms for biological sequence design. Evolutionary algorithms [\(Sinai et al., 2020;](#page-9-1) [Ren et al., 2022\)](#page-9-2) leverage the learned surrogate model to provide evolution guidance towards the high-scoring region. [\(Angermueller et al., 2019\)](#page-8-0) propose a flexible reinforcement learning framework where sequence design is a sequential decision-making problem. Bayesian optimization methods propose candidate solutions via an acquisition function [\(Terayama et al., 2021\)](#page-9-3). Deep generative model methods design sequences in the latent space [\(Chan et al., 2021\)](#page-8-2) or gradually adapt the distribution towards the high-scoring region [\(Brookes et al., 2019\)](#page-8-1). GFlowNets [\(Jain et al., 2022\)](#page-8-3) amortize the cost of search over learning and encourage diversity. Gradient-based methods leverage a surrogate model and its gradient information to maximize the desired property [\(Chen et al., 2022b;](#page-8-5) [Norn](#page-9-4) [et al., 2021;](#page-9-4) [Tischer et al., 2020;](#page-9-5) [Linder & Seelig, 2020\)](#page-9-6). Our proposed BIB belongs to the last category and leverages the rich biophysical information [\(Ji et al., 2021;](#page-8-7) [Elnaggar](#page-8-6)

<span id="page-7-0"></span>

| Method | TFBind8(r) |       |       |       |          |          | avGFP |          |          |          |          |          |
|--------|------------|-------|-------|-------|----------|----------|-------|----------|----------|----------|----------|----------|
|        | Grad       | COMs  | ROMA  | NEMO  | BDI      | BIB      | Grad  | COMs     | ROMA     | NEMO     | BDI      | BIB      |
| Normal | 0.941      | 0.921 | 0.926 | 0.930 | 0.947    | 0.952    | 0.913 | 0.938    | 0.975    | 0.914    | 1.018    | 1.060    |
| Joint  | 0.941      | 0.921 | 0.931 | 0.932 | 0.935    | 0.925    | 0.913 | 0.905    | 0.923    | 0.906    | 1.006    | 1.009    |
| Gain   | 0.000      | 0.000 | 0.005 | 0.002 | $-0.008$ | $-0.027$ | 0.000 | $-0.033$ | $-0.052$ | $-0.008$ | $-0.012$ | $-0.051$ |
| Ada-η  | 0.941      | 0.928 | 0.939 | 0.935 | 0.951    | 0.956    | 0.916 | 0.952    | 0.998    | 0.920    | 1.024    | 1.082    |
| Gain   | 0.000      | 0.007 | 0.013 | 0.005 | 0.004    | 0.004    | 0.003 | 0.014    | 0.023    | 0.006    | 0.006    | 0.022    |

Table 3. [A](#page-8-6)daptive- $\eta$  on all gradient-based methods.

Table 4. [H](#page-8-6)yperparameter optimization from a gradient-based bi-level optimization view.

<span id="page-7-1"></span>

| Scenario            | Inner variables | Inner Objective                                           | Outer variables | Outer Objective          |
|---------------------|-----------------|-----------------------------------------------------------|-----------------|--------------------------|
| model training      | model params    | minimize training loss (e.g. cross-entropy loss)          | hyperparams     | minimize validation loss |
| design optimization | design params   | minimize training loss (e.g. bidirectional learning loss) | hyperparams     | maximize auxiliary score |

[et al., 2021\)](#page-8-6) to directly optimize the biological sequence.

Offline model-based optimization. A majority of sequence design algorithms [\(Angermueller et al., 2019;](#page-8-0) [Sinai](#page-9-1) [et al., 2020;](#page-9-1) [Ren et al., 2022\)](#page-9-2) focus on the online setting where wet-lab experimental results in the current round are analyzed to propose candidates in the next round. The problem of this setting is that wet-lab experiments are often very expensive, and thus a pure data-driven, offline approach is attractive and has received substantial research attention recently [\(Trabucco et al., 2022;](#page-9-11) [Kolli et al., 2022;](#page-9-12) [Beckham](#page-8-13) [et al., 2022\)](#page-8-13). Gradient-based methods have proven to be effective [\(Trabucco et al., 2021;](#page-9-0) [Yu et al., 2021;](#page-10-0) [Fu & Levine,](#page-8-4) [2021;](#page-8-4) [Chen et al., 2022b\)](#page-8-5). Among these algorithms, [\(Chen](#page-8-5) [et al., 2022b\)](#page-8-5) propose bidirectional mappings to distill information from the static dataset into a high-scoring design, which achieves state-of-the-art performances on a variety of tasks. However, this bidirectional learning is designed for general tasks, like robot and material design, and the rich biophysical information in millions of biological sequences is ignored. In this paper, we leverage recent advances in deep linearization to incorporate the rich biophysical information into bidirectional learning.

Bi-level optimization for hyperparameter optimization. Gradient-based bi-level optimization [\(Liu et al., 2021;](#page-9-13) [Chen](#page-8-14) [et al., 2022a\)](#page-8-14) has been widely used in hyperparameter opti-

mization to improve model training [\(Maclaurin et al., 2015;](#page-9-14) [Franceschi et al., 2017;](#page-8-15) [Pedregosa, 2016;](#page-9-15) [Lorraine et al.,](#page-9-16) [2020;](#page-9-16) [Donini et al., 2019;](#page-8-16) [Franceschi et al., 2018;](#page-8-17) [Chen](#page-8-18) [et al., 2021;](#page-8-18) [Luketina et al., 2016;](#page-9-17) [Vicol et al., 2022;](#page-10-6) [Mi](#page-9-18)[caelli & Storkey, 2021;](#page-9-18) [Bohdal et al., 2021;](#page-8-19) [Baydin et al.,](#page-8-20) [2017\)](#page-8-20). As shown in the *model training* scenario of Table [4,](#page-7-1) the inner level optimizes model parameters by minimizing the training loss, and the outer level optimizes hyperparameters by minimizing the validation loss. More specifically, [\(Maclaurin et al., 2015\)](#page-9-14) exactly reverse the optimization dynamics to compute hyperparameter gradients. [\(Luketina](#page-9-17) [et al., 2016\)](#page-9-17) locally adjust hyperparameters to minimize validation loss. [\(Franceschi et al., 2018\)](#page-8-17) unify hyperparameter optimization and meta-learning via bi-level optimization. [\(Donini et al., 2019\)](#page-8-16) use past optimization information to simulate future behavior and compute the hypergradients efficiently. All of these previous works aim to improve model training and belong to the *model training* scenario in Table [4.](#page-7-1) By contrast, our Adaptive- $\gamma$  and Adaptive- $\eta$  belong to the second scenario: *design optimization*.

In our setting of offline model-based optimization, there is no validation set to provide hypergradient information to update hyperparameters, including the trade-off parameter  $\gamma$  and the learning rate  $\eta$ . Instead, inspired by previous work [\(Angermueller et al., 2019;](#page-8-0) [Trabucco et al., 2021;](#page-9-0) [Chan et al., 2021\)](#page-8-2) that uses an auxiliary model to select candidates, we use the auxiliary to provide a weak supervision signal for hyperparameter optimization. This leads to a bi-level optimization task: the inner level optimizes design parameters by minimizing the training loss (bidirectional learning loss), and the outer level optimizes hyperparameters by maximizing the auxiliary score. As we can see, our formulation is different from that of previous work and thus can not compare with them directly.

## 6. Conclusion

In this paper, we propose bidirectional learning for offline model-based biological sequence. Our work is built on the recently proposed bidirectional learning approach [\(Chen](#page-8-5) [et al., 2022b\)](#page-8-5), which is designed for general inputs and relies on the NTK of an infinitely wide network to yield a closedform loss computation. Though effective, the NTK cannot learn features. We build a proxy model using the pre-trained LM model with a linear head and apply the deep linearization scheme to the proxy, which can yield a closed-form loss and incorporate the wealth of biophysical information at the same time. In addition, we propose *Adaptive*-γ to maintain a proper balance between the forward mapping and the backward mapping by leveraging a weak supervision signal from an auxiliary model. Based on this framework, we further propose *Adaptive*-η, the first learning rate adaptation strategy compatible with all gradient-based offline

model-based algorithms. Experimental results on DNA and protein sequence design tasks verify the effectiveness of BIB and *Adaptive*-η.

We discuss potential negative impacts in Appendix [7.7,](#page-11-3) reproducibility in Appendix [7.8](#page-11-4) and limitations of our research work in Appendix [7.9.](#page-11-5)

## **References**

- <span id="page-8-8"></span>Achille, A., Golatkar, A., Ravichandran, A., Polito, M., and Soatto, S. Lqf: linear quadratic fine-tuning. In *Proc. Comp. Vision. Pattern. Rec.(CVPR)*, 2021.
- <span id="page-8-0"></span>Angermueller, C., Dohan, D., Belanger, D., Deshpande, R., Murphy, K., and Colwell, L. Model-based reinforcement learning for biological sequence design. In *Proc. Int. Conf. Learning Rep. (ICLR)*, 2019.
- <span id="page-8-10"></span>Arora, S., Du, S. S., Li, Z., Salakhutdinov, R., Wang, R., and Yu, D. Harnessing the power of infinitely wide deep nets on small-data tasks. In *Proc. Int. Conf. Learning Rep. (ICLR)*, 2020.
- <span id="page-8-21"></span>Barrera, L. A. et al. Survey of variation in human transcription factors reveals prevalent DNA binding changes. *Science*, 2016.
- <span id="page-8-20"></span>Baydin, A. G., Cornish, R., Rubio, D. M., Schmidt, M., and Wood, F. Online learning rate adaptation with hypergradient descent. *arXiv preprint arXiv:1703.04782*, 2017.
- <span id="page-8-13"></span>Beckham, C., Piche, A., Vazquez, D., and Pal, C. Towards good validation metrics for generative models in offline model-based optimisation. *arXiv preprint arXiv:2211.10747*, 2022.
- <span id="page-8-19"></span>Bohdal, O., Yang, Y., and Hospedales, T. Evograd: Efficient gradient-based meta-learning and hyperparameter optimization. *Proc. Adv. Neur. Inf. Proc. Syst (NeurIPS)*, 2021.
- <span id="page-8-1"></span>Brookes, D., Park, H., and Listgarten, J. Conditioning by adaptive sampling for robust design. In *Proc. Int. Conf. Machine Learning (ICML)*, 2019.
- <span id="page-8-22"></span>Bryant, D. H., Bashir, A., Sinai, S., Jain, N. K., Ogden, P. J., Riley, P. F., Church, G. M., Colwell, L. J., and Kelsic, E. D. Deep diversification of an AAV capsid protein by machine learning. *Nature Biotechnology*, 2021.
- <span id="page-8-2"></span>Chan, A., Madani, A., Krause, B., and Naik, N. Deep extrapolation for attribute-enhanced generation. *Proc. Adv. Neur. Inf. Proc. Syst (NeurIPS)*, 2021.
- <span id="page-8-18"></span>Chen, C., Zheng, S., Chen, X., Dong, E., Liu, X. S., Liu, H., and Dou, D. Generalized data weighting via class-level

gradient manipulation. *Proc. Adv. Neur. Inf. Proc. Syst (NeurIPS)*, 2021.

- <span id="page-8-14"></span>Chen, C., Chen, X., Ma, C., Liu, Z., and Liu, X. Gradientbased bi-level optimization for deep learning: A survey. *arXiv preprint arXiv:2207.11719*, 2022a.
- <span id="page-8-5"></span>Chen, C., Zhang, Y., Fu, J., Liu, X., and Coates, M. Bidirectional learning for offline infinite-width model-based optimization. *Proc. Adv. Neur. Inf. Proc. Syst (NeurIPS)*, 2022b.
- <span id="page-8-16"></span>Donini, M., Franceschi, L., Pontil, M., Majumder, O., and Frasconi, P. Marthe: Scheduling the learning rate via online hypergradients. *arXiv preprint arXiv:1910.08525*, 2019.
- <span id="page-8-9"></span>Dukler, Y., Achille, A., Paolini, G., Ravichandran, A., Polito, M., and Soatto, S. DIVA: Dataset derivative of a learning task. In *Proc. Int. Conf. Learning Rep. (ICLR)*, 2022.
- <span id="page-8-6"></span>Elnaggar, A., Heinzinger, M., Dallago, C., Rehawi, G., Wang, Y., Jones, L., Gibbs, T., Feher, T., Angerer, C., Steinegger, M., et al. ProtTrans: towards cracking the language of lifes code through self-supervised deep learning and high performance computing. *IEEE Trans. Pattern Analysis and Machine Intelligence*, 2021.
- <span id="page-8-15"></span>Franceschi, L., Donini, M., Frasconi, P., and Pontil, M. Forward and reverse gradient-based hyperparameter optimization. In *Proc. Int. Conf. Machine Learning (ICML)*, 2017.
- <span id="page-8-17"></span>Franceschi, L., Frasconi, P., Salzo, S., Grazzi, R., and Pontil, M. Bilevel programming for hyperparameter optimization and meta-learning. In *Proc. Int. Conf. Machine Learning (ICML)*, 2018.
- <span id="page-8-4"></span>Fu, J. and Levine, S. Offline model-based optimization via normalized maximum likelihood estimation. *Proc. Int. Conf. Learning Rep. (ICLR)*, 2021.
- <span id="page-8-11"></span>Han, B., Yao, Q., Yu, X., Niu, G., Xu, M., Hu, W., Tsang, I., and Sugiyama, M. Co-teaching: robust training of deep neural networks with extremely noisy labels. *Proc. Adv. Neur. Inf. Proc. Syst (NeurIPS)*, 2018.
- <span id="page-8-12"></span>Hansen, N. The CMA evolution strategy: a comparing review. *Towards A New Evolutionary Computation*, 2006.
- <span id="page-8-3"></span>Jain, M., Bengio, E., Hernandez-Garcia, A., Rector-Brooks, J., Dossou, B. F., Ekbote, C. A., Fu, J., Zhang, T., Kilgour, M., Zhang, D., et al. Biological sequence design with GFlowNets. In *Proc. Int. Conf. Machine Learning (ICML)*, 2022.
- <span id="page-8-7"></span>Ji, Y., Zhou, Z., Liu, H., and Davuluri, R. V. DNABERT: pre-trained bidirectional encoder representations from

transformers model for DNA-language in genome. *Bioinformatics*, 2021.

- <span id="page-9-8"></span>Killoran, N., Lee, L. J., Delong, A., Duvenaud, D., and Frey, B. J. Generating and designing DNA with deep generative models. *arXiv preprint arXiv:1712.06148*, 2017.
- <span id="page-9-10"></span>Kingma, D. P. and Ba, J. Adam: a method for stochastic optimization. In *Proc. Int. Conf. Learning Rep. (ICLR)*, 2015.
- <span id="page-9-12"></span>Kolli, S., Lu, A. X., Geng, X., Kumar, A., and Levine, S. Data-driven optimization for protein design: workflows, algorithms and metrics. In *ICLR2022 Machine Learning for Drug Discovery*, 2022.
- <span id="page-9-22"></span>Kumar, V., Liu, H., and Wu, C. Drug repurposing against SARS-CoV-2 receptor binding domain using ensemblebased virtual screening and molecular dynamics simulations. *Computers in Biology and Medicine*, 2021.
- <span id="page-9-20"></span>Lee, Y. and Yu, H. ProtFIM: Fill-in-middle protein sequence design via protein language models, 2023. URL [https:](https://openreview.net/forum?id=9XAZBUfnefS) [//openreview.net/forum?id=9XAZBUfnefS](https://openreview.net/forum?id=9XAZBUfnefS).
- <span id="page-9-6"></span>Linder, J. and Seelig, G. Fast differentiable DNA and protein sequence optimization for molecular design. *arXiv preprint arXiv:2005.11275*, 2020.
- <span id="page-9-9"></span>Linder, J. and Seelig, G. Fast activation maximization for molecular sequence design. *BMC Bioinformatics*, 2021.
- <span id="page-9-13"></span>Liu, R., Gao, J., Zhang, J., Meng, D., and Lin, Z. Investigating bi-level optimization for learning and vision from a unified perspective: A survey and beyond. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2021.
- <span id="page-9-16"></span>Lorraine, J., Vicol, P., and Duvenaud, D. Optimizing millions of hyperparameters by implicit differentiation. In *International Conference on Artificial Intelligence and Statistics*, 2020.
- <span id="page-9-17"></span>Luketina, J., Berglund, M., Greff, K., and Raiko, T. Scalable gradient-based tuning of continuous regularization hyperparameters. In *Proc. Int. Conf. Machine Learning (ICML)*, 2016.
- <span id="page-9-14"></span>Maclaurin, D., Duvenaud, D., and Adams, R. Gradientbased hyperparameter optimization through reversible learning. In *Proc. Int. Conf. Machine Learning (ICML)*, 2015.
- <span id="page-9-18"></span>Micaelli, P. and Storkey, A. J. Gradient-based hyperparameter optimization over long horizons. *Proc. Adv. Neur. Inf. Proc. Syst (NeurIPS)*, 2021.

- <span id="page-9-23"></span>Murray, C. J., Ikuta, K. S., Sharara, F., Swetschinski, L., Aguilar, G. R., Gray, A., Han, C., Bisignano, C., Rao, P., Wool, E., et al. Global burden of bacterial antimicrobial resistance in 2019: a systematic analysis. *The Lancet*, 2022.
- <span id="page-9-4"></span>Norn, C., Wicky, B. I., Juergens, D., Liu, S., Kim, D., Tischer, D., Koepnick, B., Anishchenko, I., Baker, D., and Ovchinnikov, S. Protein sequence design by conformational landscape optimization. *Proceedings of the National Academy of Sciences*, 2021.
- <span id="page-9-21"></span>Paszke, A., Gross, S., Massa, F., Lerer, A., Bradbury, J., Chanan, G., Killeen, T., Lin, Z., Gimelshein, N., Antiga, L., et al. Pytorch: an imperative style, high-performance deep learning library. *Proc. Adv. Neur. Inf. Proc. Syst (NeurIPS)*, 2019.
- <span id="page-9-15"></span>Pedregosa, F. Hyperparameter optimization with approximate gradient. In *Proc. Int. Conf. Machine Learning (ICML)*, 2016.
- <span id="page-9-2"></span>Ren, Z., Li, J., Ding, F., Zhou, Y., Ma, J., and Peng, J. Proximal exploration for model-guided protein sequence design. *Proc. Int. Conf. Machine Learning (ICML)*, 2022.
- <span id="page-9-7"></span>Sarkisyan, K. S. et al. Local fitness landscape of the green fluorescent protein. *Nature*, 2016.
- <span id="page-9-1"></span>Sinai, S., Wang, R., Whatley, A., Slocum, S., Locane, E., and Kelsic, E. D. Adalead: a simple and robust adaptive greedy search algorithm for sequence design. *arXiv preprint arXiv:2010.02141*, 2020.
- <span id="page-9-19"></span>Starita, L. M., Pruneda, J. N., Lo, R. S., Fowler, D. M., Kim, H. J., Hiatt, J. B., Shendure, J., Brzovic, P. S., Fields, S., and Klevit, R. E. Activity-enhancing mutations in an E3 ubiquitin ligase identified by high-throughput mutagenesis. *Proceedings of the National Academy of Sciences*, 2013.
- <span id="page-9-3"></span>Terayama, K., Sumita, M., Tamura, R., and Tsuda, K. Blackbox optimization for automated discovery. *Accounts of Chemical Research*, 2021.
- <span id="page-9-5"></span>Tischer, D., Lisanza, S., Wang, J., Dong, R., Anishchenko, I., Milles, L. F., Ovchinnikov, S., and Baker, D. Design of proteins presenting discontinuous functional sites using deep learning. *Biorxiv*, 2020.
- <span id="page-9-0"></span>Trabucco, B., Kumar, A., Geng, X., and Levine, S. Conservative objective models for effective offline modelbased optimization. In *Proc. Int. Conf. Machine Learning (ICML)*, 2021.
- <span id="page-9-11"></span>Trabucco, B., Geng, X., Kumar, A., and Levine, S. Design-Bench: benchmarks for data-driven offline model-based optimization. *arXiv preprint arXiv:2202.08450*, 2022.

- <span id="page-10-6"></span>Vicol, P., Lorraine, J. P., Pedregosa, F., Duvenaud, D., and Grosse, R. B. On implicit bias in overparameterized bilevel optimization. In *Proc. Int. Conf. Machine Learning (ICML)*, 2022.
- <span id="page-10-5"></span>Wilson, J. T., Moriconi, R., Hutter, F., and Deisenroth, M. P. The reparameterization trick for acquisition functions. *arXiv preprint arXiv:1712.00424*, 2017.
- <span id="page-10-1"></span>Yang, G. and Hu, E. J. Tensor programs iv: feature learning in infinite-width neural networks. In *Proc. Int. Conf. Machine Learning (ICML)*, 2021.
- <span id="page-10-0"></span>Yu, S., Ahn, S., Song, L., and Shin, J. Roma: robust model adaptation for offline model-based optimization. *Proc. Adv. Neur. Inf. Proc. Syst (NeurIPS)*, 2021.

## 7. Appendix

<span id="page-10-2"></span>

### 7.1. DNA Embedding

To incorporate richer contextual information, the DNA LM [\(Ji et al., 2021\)](#page-8-7) adopts the  $k$ -mer sequence representation, which is widely used in DNA sequence analysis. For example, the sequence  $ATGGCT$  has its 3-mer representation as  $\{ATG, TGG, GGC, GCT\}$ . In this paper, we adopt its 3-mer representation and compute the probability of the 3-mer token by multiplying the probabilities of the three individual bases. The 3-mer representation is then sent to the pre-trained DNA LM.

<span id="page-10-3"></span>

#### 7.2. Different Pretrained LMs

As shown in Table [6,](#page-11-6) we have tested the ProtBERT, ProtAlbert, and ProtBert-BFD models and found that better-quality models generally work better. The publicly available pretrained DNA models are limited and thus we only perform experiments on the protein tasks. [\(Elnaggar et al., 2021\)](#page-8-6) demonstrate that the language model performances follow the ordering: ProtBert-BFD > ProtBert > ProtAlbert. We can see that the performance ranks over the three protein tasks avGFP, AAV, and E4B are the same in Table [6.](#page-11-6)

<span id="page-10-4"></span>

#### 7.3. Task Details

We conduct experiments on two DNA tasks following [\(Chen](#page-8-5) [et al., 2022b\)](#page-8-5) and three protein tasks in [\(Ren et al., 2022\)](#page-9-2) which have the most data points. We report the dataset details in Table [5.](#page-11-7)

DNA Task 1 TFBind8(r). The goal is to find a length-8 DNA sequence to maximize the binding activity score with a particular transcription factor, SIX6REFR1 [\(Barrera](#page-8-21) [et al., 2016\)](#page-8-21). We sample 5000 data points for the offline algorithms following [\(Chen et al., 2022b\)](#page-8-5).

**DNA Task 2 TFBind10(r).** The task TFBind10(r) is the same as  $TFBind8(r)$  except that the goal is to find a length-10 DNA sequence. Both DNA tasks measure the entire search space and we adopt these measurements as the approximate ground-truth evaluation.

Protein Task 1 avGFP. This task aims to find a protein sequence with approximately 239 amino acids to maximize the fluorescence level of Green Fluorescent Proteins [\(Sark](#page-9-7)[isyan et al., 2016\)](#page-9-7). The task oracle is constructed by using the full unobserved dataset (around 52,000 points) following [\(Ren et al., 2022\)](#page-9-2). The oracle passes the average of the residue embeddings from the pre-trained Prot-T5 [\(Elnag](#page-8-6)[gar et al., 2021\)](#page-8-6) into a linear layer and then fits the dataset. The following two task oracles take the same form. Offline algorithms can only access the lowest-scoring 26,000 points.

Protein Task 2 AAV. The goal is to engineer a 28-amino acid segment (positions 561–588) of the VP1 protein to remain viable for gene therapy [\(Bryant et al., 2021\)](#page-8-22). We use the entire 284, 000 data points to build the oracle and the lowest-scoring 142, 000 points for the offline algorithms.

Protein Task 3 E4B. This task aims to design a protein (around 102 amino acids) to maximize the ubiquitination rate to the target protein [\(Starita et al., 2013\)](#page-9-19). The full dataset with around 100, 000 points is used to build the oracle and the bottom half is used for the offline algorithms.

[\(Lee & Yu, 2023\)](#page-9-20) evaluate the sequence generation and structure conservation simultaneously with the help of Alphafold2, which provides a more accurate evaluation.

Oracle Parameterization. The parameterization of the oracle is different from that of the regression model from two aspects: 1) model architecture; 2) pre-trained information source. First, the oracle adopts the Prot-T5 model which consists of an encoder and a decoder, while the regression model adopts the Prot-BERT model which only has an encoder. Second, Prot-T5 is trained on the BFD and UniRef100 datasets and ProtBert is trained on the UniRef50 dataset. These two points demonstrate that the oracle and the regression model are different function classes. We choose the Prot-T5 model as the oracle because this is the state-of-the-art protein LM to extract features and recent work [\(Elnaggar et al., 2021\)](#page-8-6) has demonstrated its effectiveness. In order to test how related the Prot-T5 (oracle)/Prot-BERT(proxy) models are, we trained them on a sampled training dataset and compared the test predictions of the testing set. By evaluating the Pearson correlation coefficient (PCC) between the two prediction errors PCC(ProtT5 predictions - test labels, ProtBERT predictions -test labels), we obtain 0.0104 on avGFP, −0.0005 on AAV, and −0.0062 on E4B. These results suggest that the two models are not strongly related in terms of the predictions they form.

Following [\(Trabucco et al., 2021\)](#page-9-0), we select the top  $N =$ 

<span id="page-11-7"></span>

| Table 5. Dataset details. |                     |                     |                     |                               |                               |
|---------------------------|---------------------|---------------------|---------------------|-------------------------------|-------------------------------|
| Task                      | Metric              | Min of $\mathcal D$ | Max of $\mathcal D$ | Min of $\mathcal{D}_{entire}$ | Max of $\mathcal{D}_{entire}$ |
| TFBind8(r)                | binding activity    | 0.000               | 0.242               | 0.000                         | 1.000                         |
| TFBind10(r)               | binding activity    | -1.859              | -0.869              | -1.859                        | 2.129                         |
| avGFP                     | fluorescence level  | 1.283               | 2.175               | 1.283                         | 4.123                         |
| AAV                       | viruses viability   | -11.176             | -1.814              | -11.176                       | 9.536                         |
| E4B                       | ubiquitination rate | -3.589              | -0.770              | -3.589                        | 8.998                         |

<span id="page-11-6"></span>Table 6. Experimental results on different pre-trained LMs for comparison.

| Pre-trained LM         | avGFP             | AAV               | E4B               |
|------------------------|-------------------|-------------------|-------------------|
| ProtAlbert             | $0.907 \pm 0.004$ | $0.478 \pm 0.004$ | $0.552 \pm 0.023$ |
| $ProtBert_{(adopted)}$ | $1.060 \pm 0.016$ | $0.501 \pm 0.007$ | $1.255 \pm 0.029$ |
| ProtBert-BFD           | $1.119 \pm 0.116$ | $0.549 \pm 0.009$ | $1.880 \pm 0.054$ |

128 most promising sequences for each comparison method. Among these sequences, we report the maximum normalized ground truth score as the evaluation metric following [\(Ren et al., 2022\)](#page-9-2).

<span id="page-11-0"></span>

#### 7.4. Different Dataset Size

As shown in Table [7,](#page-12-0) we have tested the performance of BDI as a function of dataset size  $(N= 20, 40, 60, 80, 100)$  in TFBind8(r) and TFBind10(r) since they have exact oracle evaluations. We see that performance is already good for N=20 for TFBind8(r) and N=40 for TFBind10(r).

<span id="page-11-1"></span>

#### 7.5. Training Details

We use Pytorch [\(Paszke et al., 2019\)](#page-9-21) to run all experiments on one V100 GPU. Following the setting in [\(Norn et al.,](#page-9-4)  $2021$ ), we introduce a length- $L$  protein sequence as a continuous random matrix  $\boldsymbol{X}_h \in R^{L \times 20}$  ( $\boldsymbol{X}_h \in R^{L \times 4}$  for DNA), initialized using a normal distribution with the mean 0 and the standard deviation of 0.01. To make this sequence correspond correctly to the candidate, we exchange the largest value in  $X[l, :]$  with the value in the amino acid index.

<span id="page-11-2"></span>

### 7.6. Ranking Performance

As for prediction performances, the rank should be: a NN  $>$  linearized pre-trained LM  $>$  NTK. We have conducted experiments to verify this. We sample half of the data, train a model to predict another half data, and report the mean squared loss here and in Table [8.](#page-12-1) A small mean squared loss indicates a good prediction performance; thus, we have verified the above ranking order.

<span id="page-11-3"></span>

#### 7.7. Negative Impact

Protein sequence design aims to find a protein sequence with a particular biological function, which has a broad application scope. This can lead to improved drugs that are highly beneficial to society. For instance, designing the antibody protein for SARS-COV-2 can potentially save millions of human lives [\(Kumar et al., 2021\)](#page-9-22) and designing novel anti-microbial peptides (short protein sequences) is central to tackling the growing public health risks caused by antimicrobial resistance [\(Murray et al., 2022\)](#page-9-23). Unfortunately, it is possible to direct the research results towards harmful purposes such as the design of biochemical weapons. As researchers, we believe that we must be aware of the potential harm of any research outcomes, and carefully consider whether the possible benefits outweigh the risks of harmful consequences. We also must recognize that we cannot control how the research may be used. In the case of this paper, we are confident that there is a much greater chance that the research outcomes will have a beneficial effect. We do not consider that there are any immediate ethical concerns with the research endeavour.

<span id="page-11-4"></span>

#### 7.8. Reproducibility Statement

We provide the code implementation of BIB and *Adaptive*- $\eta$  [here](https://anonymous.4open.science/r/BIB-ICML2023-Submission/README.md) and we also attach the code in the supplementary material. We describe DNA/protein benchmarks in Sec. [4.1](#page-4-2) and training details in Sec. [4.3.](#page-4-3) We explain how to obtain the sequence embedding from the pre-trained LM and how to perform gradient ascent on the sequence in Sec. [2.](#page-1-0)

<span id="page-11-5"></span>

#### 7.9. Limitations

We note that, due to the nature of offline optimization, we propose designs that are outside the available datasets. Evaluation of the performance of such designs must then rely on training an oracle model based on the entire available data. We are careful to ensure that (i) the oracle is significantly structurally different from the model(s) used within the optimization algorithm, (ii) the oracle is accurate, especially in terms of rank correlation; and (iii) oracle residuals and regression model residuals are uncorrelated. However, even after these measures are taken, there is a danger that the proposed designs are not biologically optimal, i.e., that the oracle performance is not genuinely reflective of performance that would be observed in experiments. This concern can only be alleviated via further biological experiments that examine how well off-line optimization algorithms perform in practice.

Table 7. Experimental results on different size datasets for comparison.

<span id="page-12-0"></span>

| Dataset size | 20                                                                                                    | 40 | 60 | 80 | 100 |
|--------------|-------------------------------------------------------------------------------------------------------|----|----|----|-----|
| TFBind8(r)   | $0.849 \pm 0.027$ $0.883 \pm 0.036$ $0.890 \pm 0.033$ $0.911 \pm 0.042$ $0.923 \pm 0.049$             |    |    |    |     |
|              | TFBind10(r) $0.248 \pm 0.000$ $0.596 \pm 0.035$ $0.602 \pm 0.023$ $0.616 \pm 0.024$ $0.632 \pm 0.036$ |    |    |    |     |

Table 8. Mean squared prediction losses for comparison.

<span id="page-12-1"></span>

| Table 8. Mean squared prediction losses for comparison. |                   |                   |                   |                    |                   |
|---------------------------------------------------------|-------------------|-------------------|-------------------|--------------------|-------------------|
| Method                                                  | TFBInd8(r)        | TFBInd10(r)       | avGFP             | AAV                | E4B               |
| Finetuned NN                                            | $0.101 pm 0.001$ | $1.130 pm 0.041$ | $0.323 pm 0.006$ | $5.148 pm 0.074$  | $0.683 pm 0.012$ |
| Linearized NN                                           | $0.107 pm 0.000$ | $1.618 pm 0.000$ | $3.956 pm 0.000$ | $23.041 pm 0.000$ | $1.050 pm 0.000$ |
| NTK                                                     | $0.111 pm 0.000$ | $1.840 pm 0.000$ | $4.866 pm 0.000$ | $24.451 pm 0.000$ | $1.075 pm 0.000$ |
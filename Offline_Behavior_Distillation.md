# Offline Behavior Distillation

Shiye Lei School of Computer Science The University <NAME_EMAIL>

Sen Zhang School of Computer Science The University <NAME_EMAIL>

Dacheng Tao College of Computing & Data Science Nanyang <NAME_EMAIL>

## Abstract

Massive reinforcement learning (RL) data are typically collected to train policies offline without the need for interactions, but the large data volume can cause training inefficiencies. To tackle this issue, we formulate offline behavior distillation (OBD), which synthesizes limited expert behavioral data from sub-optimal RL data, enabling rapid policy learning. We propose two naive OBD objectives, DBC and PBC, which measure distillation performance via the decision difference between policies trained on distilled data and either offline data or a near-expert policy. Due to intractable bi-level optimization, the OBD objective is difficult to minimize to small values, which deteriorates PBC by its distillation performance guarantee with quadratic discount complexity  $\mathcal{O}(1/(1-\gamma)^2)$ . We theoretically establish the equivalence between the policy performance and action-value weighted decision difference, and introduce action-value weighted PBC (Av-PBC) as a more effective OBD objective. By optimizing the weighted decision difference, Av-PBC achieves a superior distillation guarantee with linear discount complexity  $\mathcal{O}(1/(1 - \gamma))$ . Extensive experiments on multiple D4RL datasets reveal that Av-PBC offers significant improvements in OBD performance, fast distillation convergence speed, and robust cross-architecture/optimizer generalization. The code is available at <https://github.com/LeavesLei/OBD>.

## 1 Introduction

Due to the costs and dangers associated with interactions in reinforcement learning (RL), learning policies from pre-collected RL data has become increasingly popular [\[Levine et al., 2020\]](#page-10-0). Consequently, numerous offline RL datasets have been constructed [\[Fu et al., 2020\]](#page-9-0). However, these offline data are typically massive and collected by sub-optimal or even random policies, leading to inefficiencies in policy training. Inspired by dataset distillation (DD) [\[Wang et al., 2018,](#page-11-0) [Zhao et al.,](#page-11-1) [2021,](#page-11-1) [Lei and Tao, 2024\]](#page-10-1), which synthesizes a small number of training images while preserving model training effects, we further investigate the following question: *Can we distill vast sub-optimal RL data into limited expert behavioral data?* Achieving this would enable rapid offline policy learning via behavioral cloning (BC) [\[Pomerleau, 1991\]](#page-10-2), which can (1) reduce the training cost and enable green AI; (2) facilitate downstream tasks by using distilled data as prior knowledge (*e.g.* continual RL [\[Gai et al., 2023\]](#page-9-1), multi-task RL [\[Yu et al., 2021\]](#page-11-2), efficient policy pretraining [\[Goecks et al., 2019\]](#page-9-2), offline-to-online fine-tuning [\[Zhao et al., 2022\]](#page-11-3)); and (3) protect data privacy [\[Qiao and Wang, 2023\]](#page-10-3).

Unlike DD whose objective is prediction accuracy and directly obtainable from real data, the policy performance in RL is measured by the expected return through interactions with environment. In an

38th Conference on Neural Information Processing Systems (NeurIPS 2024).

offline paradigm, where direct interaction with environment is not possible, a metric based on RL data is necessary to guide the RL data distillation. Therefore, we formalize the offline behavior distillation (OBD): a limited set of behavioral data, comprising (state, action) pairs, is synthesized from sub-optimal RL data, so that policies trained on the compact synthetic dataset by BC can achieve small OBD objective loss, which incarnates high return when deploying policies in the environment.

The key obstacle for OBD is constructing a proper objective that efficiently and accurately estimates the policy performance based on the sub-optimal offline dataset, allowing for a rational evaluation of the distilled data. To this end, data-based BC (DBC) and policy-based BC (PBC) present two naive OBD objectives. Specifically, DBC reflects the policy performance by measuring the mismatch between the policy decision and vanilla offline data. Leveraging existing offline RL algorithms that can extract near-optimal policies from sub-optimal data [\[Levine et al., 2020\]](#page-10-0), PBC improves upon DBC by correcting actions in offline data using a near-optimal policy before measuring the decision difference. However, due to the complex bi-level optimization in OBD, the objectives are difficult to minimize effectively, resulting in an inferior distillation performance guarantee with the *quadratic* discount complexity  $\mathcal{O}(1/(1-\gamma)^2)$  for PBC (Theorem [1\)](#page-4-0). We tackle this problem and propose the action-value weighted PBC (Av-PBC) as the OBD objective with superior distillation guarantee by taking inspirations from our theoretical findings. Concretely, we theoretically prove the equivalence between the policy performance gap and the action-value weighted decision difference (Theorem [2\)](#page-4-1). Then, by optimizing the weighted decision difference, we can obtain a much tighter distillation performance guarantee with *linear* discount complexity  $\mathcal{O}(1/(1 - \gamma))$  (Corollary [1\)](#page-4-2). Consequently, we weigh PBC with the simple action value, introducing Av-PBC as the OBD objective.

Extensive experiments on nine datasets of D4RL benchmark [\[Fu et al., 2020\]](#page-9-0) with multiple environments and data qualities illustrate that our Av-PBC remarkably promotes the OBD performance, which is measured by normalized return, by 82.8% and 25.7% compared to baselines of DBC and PBC, respectively. Moreover, Av-PBC has a significant convergence speed and requires only a quarter of distillation steps compared to DBC and PBC. By evaluating the synthetic data in terms of different network architectures and training optimizers, we show that distilled datasets possess decent cross-architecture/optimizer performance. Apart from evaluations on single policy, we also investigate policy ensemble performance by training multiple policies on the synthetic dataset and combining them to generate actions. The empirical findings demonstrate that the ensemble operation can significantly enhance the performance of policies trained on Av-PBC-distilled data by 25.8%.

Our contributions can be summarized as:

- We formulate the offline behavior distillation problem, and present two naive OBD objectives of DBC and the improved PBC;
- We demonstrate the unpleasant distillation performance guarantee of  $\mathcal{O}(1/(1-\gamma)^2)$  for PBC, and theoretically derive a novel objective of Av-PBC that has much tighter performance guarantee of  $\mathcal{O}(1/(1-\gamma));$
- Extensive experiments on multiple offline RL datasets verify significant improvements on OBD performance and speed by Av-PBC.

## 2 Related works

Offline RL Data collection can be both hazardous (*e.g.* autonomous driving) and costly (*e.g.* healthcare) with the online learning paradigm of RL. To alleviate the online interaction, offline RL has been developed to learn the policy from a pre-collected dataset gathered by sub-optimal behavior policies [\[Lange et al., 2012,](#page-10-4) [Fu et al., 2020\]](#page-9-0). However, the offline paradigm limits exploration and results in the distributional shift problem: (1) the state distribution discrepancy between learned policy and behavior policy at test time; and (2) only in-dataset state transitions are sampled when conducting Bellman backup [\[Bellman, 1966\]](#page-9-3) during the training period [\[Levine et al., 2020\]](#page-10-0). Various offline RL algorithms have been proposed to mitigate the distributional shift problem. [Fujimoto and Gu](#page-9-4) [\[2021\]](#page-9-4), [Tarasov et al.](#page-10-5) [\[2024\]](#page-10-5) introduce policy constrain that control the discrepancy between learned policy and behavior policy. To address the problem of over-optimistic estimation on out-of-distribution actions, [Kumar et al.](#page-10-6) [\[2020\]](#page-10-6), [Nakamoto et al.](#page-10-7) [\[2023\]](#page-10-7), [Kostrikov et al.](#page-10-8) [\[2022\]](#page-10-8) propose to regularize the learned value function for conservative Q learning. Moreover, ensemble approaches have also proven effective in offline RL [\[An et al., 2021\]](#page-9-5). Readers can refer to [\[Tarasov et al., 2022\]](#page-10-9) for a detailed comparison of offline RL methods. Albeit these advancements, the offline dataset is extremely large

(million-level) and contains sensitive information (*e.g.* medical history) [\[Qiao and Wang, 2023\]](#page-10-3), necessitating consideration of training efficiency, data storage, and privacy concerns. To address these issues, we distill a small behavioural dataset from vast subpar offline RL data to enable efficient policy learning via BC.

**Dataset Distillation** Given the resource constraints in era of big data, numerous approaches have focused on improving learning efficiency through memory-efficient model [\[Han et al., 2016,](#page-9-6) [Jing](#page-9-7) [et al., 2021\]](#page-9-7) and effective data utilization [\[Mirzasoleiman et al., 2020,](#page-10-10) [Jing et al., 2023,](#page-9-8) [Lei et al.,](#page-10-11) [2023\]](#page-10-11). Recently, dataset distillation (DD) has emerged as a promising technique for condensing large real datasets into significantly smaller synthetic ones, such that models trained on these tiny synthetic datasets achieve comparable generalization performance to those trained on large original datasets [\[Sachdeva and McAuley, 2023,](#page-10-12) [Yu et al., 2024,](#page-11-4) [Lei and Tao, 2024\]](#page-10-1). This approach addresses key issues such as training inefficiency, data storage limitations, and data privacy concerns. There are two primary frameworks for DD: the meta-learning framework, which formulates dataset distillation as a bi-level optimization problem [\[Wang et al., 2018,](#page-11-0) [Deng and Russakovsky, 2022\]](#page-9-9), and the matching framework, which matches the synthetic and real datasets in terms of gradient [\[Zhao et al., 2021,](#page-11-1) [Zhao and Bilen, 2021\]](#page-11-5), feature [\[Zhao and Bilen, 2023,](#page-11-6) [Wang et al., 2022\]](#page-11-7), or training trajectory [\[Cazenavette et al., 2022,](#page-9-10) [Cui et al., 2023\]](#page-9-11).

While most DD methods focus on image data, [Lupu et al.](#page-10-13) [\[2024\]](#page-10-13) propose behavior distillation (BD), extending DD to online RL regime. In (online) BD, a small number of state-action pairs are synthesized for fast BC training by (1) directly computing policy returns through online interactions; and (2) estimating the meta-gradient *w.r.t.* synthetic data via evolution strategies (ES) [\[Salimans et al.,](#page-10-14) [2017\]](#page-10-14). We underline that our OBD is not an extension of online BD, but rather a novel and parallel field because of different objectives that incur distinct challenges: (1) online BD uses the ground truth objective, *i.e.*, policy return, by sampling many long episodes from environments. As a result, backpropagating the meta-gradient of return *w.r.t.* synthetic data is extremely inefficient, and [Lupu](#page-10-13) [et al.](#page-10-13) [\[2024\]](#page-10-13) tackle the challenge by estimating meta-gradient with the zero-order algorithm of ES; and (2) OBD objective solely relies on offline data instead of long episode sampling, thereby making meta-gradient backpropagation relatively efficient and feasible, and the primary obstacle for OBD lies in designing an appropriate objective that accurately reflects the policy performance.

## 3 Preliminaries

Reinforcement Learning The problem of reinforcement learning can be described as the Markov decision process (MDP)  $\langle S, A, T, r, \gamma, d^0 \rangle$ , where S is a set of states  $s \in S$ , A is the set of actions  $a \in \mathcal{A}$ ,  $\mathcal{T}(s'|s, a)$  denotes the transition probability function,  $r(s, a)$  is the reward function,  $\gamma \in (0, 1)$ is the discount factor, and  $d^0(s)$  is the initial state distribution [\[Sutton and Barto, 2018\]](#page-10-15). We assume that the reward function is bounded by  $R_{\text{max}}$ , *i.e.*,  $r(s, a) \in [0, R_{\text{max}}]$  for all  $(s, a) \in S \times A$ . The objective of RL is to learn a policy  $\pi(a|s)$  that maximizes the long-term expected return  $J(\pi) = \mathbb{E}_{\pi} \left[ \sum_{t=0}^{\infty} \gamma^t r_t \right]$ , where  $r_t = r(s_t, a_t)$  is the reward at t-step, and  $\gamma$  usually is close to 1 to consider long-horizon rewards in the most RL tasks. We define  $d^t_\pi(s) = \Pr(s_t = s; \pi)$ I to consider long-horizon rewards in the most KL tasks. We define  $a_{\pi}(s) = 11(s_t - s, n)$ <br>and  $ρ_{\pi}^t(s, a) = \Pr(s_t = s, a_t = a; \pi)$  as t-th step state distribution and state-action distribution, respectively. Then, the discounted stationary state distribution  $d_\pi(s) = (1 - \gamma) \sum_{t=0}^{\infty} \gamma^t d_\pi^t(s)$ , and the discounted stationary state-action distribution  $\rho_{\pi}(s, a) = (1 - \gamma) \sum_{t=0}^{\infty} \gamma^{t} \rho_{\pi}^{t}(s, a)$ . Intuitively, the state (state-action) distribution depicts the overall "frequency" of visiting a state (state-action) with  $\pi$ . The action-value function of  $\pi$  is  $q_{\pi}(s, a) = \mathbb{E}_{\pi} \left[ \sum_{t=0}^{\infty} \gamma^{t} r_{t} \mid s_{0} = s, a_{0} = a \right]$ , which is the expected return starting from s, taking the action a. Since  $r_t \geq 0$ , we have  $q_\pi(s, a) \geq 0$  for all  $(s, a)$ .

Instead of interacting with the environment, offline RL learns the policy from a sub-optimal offline dataset  $\mathcal{D}_{off} = \{(s_i, a_i, s'_i, r_i)\}_{i=1}^{N_{off}}$  with specially designed Bellman backup [\[Levine et al., 2020\]](#page-10-0). Although  $\mathcal{D}_{\text{off}}$  is normally collected by sub-optimal behavior policies, offline RL algorithms can recapitulate a near-optimal policy  $\pi^*$  and value function  $q_{\pi^*}$  from  $\mathcal{D}_{off}$ .

Behavioral Cloning [\[Pomerleau, 1991\]](#page-10-2) can be regarded as a special offline RL algorithm and only copes with high-quality data. Given the expert demonstrations  $\mathcal{D}_{BC} = \{(s_i, a_i)\}_{i=1}^{N_{BC}}$ , the policy network  $\pi_\theta$  parameterized by  $\theta$  is trained by cloning the behavior of the expert dataset  $\mathcal{D}_{BC}$ in a supervised manner:  $\min_{\theta} \ell_{BC}(\theta, \mathcal{D}_{BC}) := \mathbb{E}_{(s,a)\sim \mathcal{D}_{BC}} \left[ (\pi_{\theta}(a|s) - \hat{\pi}^*(a|s))^2 \right]$ , where  $\hat{\pi}^*(a|s) =$ 

 $\sum_{i=1}^{N_{\text{BC}}} \mathbb{I}(s_i = s, a_i = a)$  $\frac{\sum_{i=1}^{n} \mathbb{I}(s_i=s,a_i=a)}{\sum_{i=1}^{N_{BC}} \mathbb{I}(s_i=s)}$  is an empirical estimation based on  $\mathcal{D}_{BC}$ . Compared to general offline RL algorithms that deal with subpar 4-tuples of  $\mathcal{D}_{\text{off}}$ , BC only handles expert 2-tuples, while it has better convergence speed due to the supervised paradigm. This paper aims to distill massive sub-optimal 4-tuples into a few expert 2-tuples, thereby enabling rapid policy learning via BC.

#### 3.1 Problem Setup

We first introduce behavior distillation [\[Lupu et al., 2024\]](#page-10-13) that aims to synthesize few data points  $D = D_{syn} = \{(s_i, a_i)\}_{i=1}^{N_{syn}}$  with small  $N_{syn}$  from the environment, so the policy trained on  $D_{syn}$  has a large expected return  $J$ . The problem of behavior distillation can be formalized as follows:

$$
\mathcal{D}_{\text{syn}}^* = \arg \max_{\mathcal{D}} J\left(\pi_{\theta(\mathcal{D})}\right) \quad \text{s.t.} \quad \theta(\mathcal{D}) = \arg \min_{\theta} \ell_{\text{BC}}(\theta, \mathcal{D}). \tag{1}
$$

During behavior distillation, the return  $J$  is directly estimated by the interaction between policy and environment. However, in the offline setting, the environment can not be touched, and only the previously collected dataset  $\mathcal{D}_{\text{off}}$  is provided. Hence, we employ  $\mathcal{H}(\pi_{\theta}, \mathcal{D}_{\text{off}})$  as a surrogate loss to estimate the policy performance of  $\pi_{\theta}$  given the offline data  $\mathcal{D}_{\text{off}}$  without interactions with the environment. Then, by setting  $N_{syn} \ll N_{off}$ , *offline behavior distillation* can be formulated as below:

$$
\mathcal{D}_{\text{syn}}^{*} = \arg\min_{\mathcal{D}} \mathcal{H}\left(\pi_{\theta(\mathcal{D})}, \mathcal{D}_{\text{off}}\right) \quad \text{s.t.} \quad \theta(\mathcal{D}) = \arg\min_{\theta} \ell_{\text{BC}}(\theta, \mathcal{D}). \tag{2}
$$

#### 3.2 Backpropagation through Time

The formulation of offline behavior distillation is a bi-level optimization problem: the inner loop optimizes the policy network parameters based on the synthetic dataset with BC by multiple iterations of  $\{\theta_1, \theta_2, \dots, \theta_T\}$ . During the outer loop iteration, synthetic data are updated by minimizing the surrogate loss  $H$ . With the nested loop, the synthetic dataset gradually converges to one of the optima. This bi-level optimization can be solved by backpropagation through time (BPTT) [\[Werbos, 1990\]](#page-11-8):

$$
\nabla_{\mathcal{D}} \mathcal{H} = \frac{\partial \mathcal{H}}{\partial \mathcal{D}} = \frac{\partial \mathcal{H}}{\partial \theta^{(T)}} \left( \sum_{k=0}^{k=T} \frac{\partial \theta^{(T)}}{\partial \theta^{(k)}} \cdot \frac{\partial \theta^{(k)}}{\partial \mathcal{D}} \right), \text{ and } \frac{\partial \theta^{(T)}}{\partial \theta^{(k)}} = \prod_{i=k+1}^{T} \frac{\partial \theta^{(i)}}{\partial \theta^{(i-1)}}. \tag{3}
$$

Although BPTT provides a feasible solution to compute the meta-gradient for OBD, the objective  $H$ is hardly minimized to near zero in practice owing to the severe complexity and non-convexity of bi-level optimization [\[Wiesemann et al., 2013\]](#page-11-9).

## 4 Methods

The key challenge in OBD is *determining an appropriate objective loss*  $\mathcal{H}(\pi_\theta, \mathcal{D}_{off})$  *to estimate the performance of*  $\pi_{\theta}$ . While policy performance could be naturally estimated using episode return by learning a MDP environment from  $\mathcal{D}_{\text{off}}$ , as done in model-based offline RL [\[Kidambi et al., 2020\]](#page-10-16), this approach is computationally expensive. Apart from the considerable time required to sample the episode for evaluation, the corresponding gradient computation is also inefficient: although Policy Gradient Theorem  $\frac{\partial J}{\partial \theta} = \sum_s d_{\pi}(s) \sum_a q_{\pi}(s, a) \nabla_{\theta} \pi_{\theta}(a|s)$  provides a way to compute metagradients [\[Sutton and Barto, 2018\]](#page-10-15), the gradient estimation often exhibits high variance due to the lack of information *w.r.t.*  $d_{\pi}(s)$  and  $q_{\pi}(s, a)$ .

#### 4.1 Data-based and Policy-based BC

Compared to both sampling and gradient computation inefficiency of policy return, directly using  $\mathcal{D}_{\text{off}}$  is a more feasible way to estimate the policy performance in OBD, and a natural option is BC loss, *i.e.*,  $\mathcal{H}(\pi_{\theta}, \mathcal{D}_{\text{off}}) = \ell_{\text{BC}}(\theta, \mathcal{D}_{\text{off}})$ , which we refer to as **data-based BC (DBC)**. However, as  $\mathcal{D}_{\text{off}}$  is collected by sub-optimal policies, DBC hardly evaluates the policy performance accurately.

Benefiting from offline RL algorithms, we can extract the near-optimal policy  $\pi^*$  and corresponding value function  $q_{\pi^*}$  from  $\mathcal{D}_{\text{off}}$  via carefully designed Bellman updates. Consequently, a more rational choice is to correct actions in  $\mathcal{D}_{off}$  with  $\pi^*$ , leading to  $\mathcal{H}(\pi, \mathcal{D}_{off}) = \mathbb{E}_{s \sim \mathcal{D}_{off}} [D_{TV}(\pi^*(\cdot|s), \pi(\cdot|s))],$ where  $D_{\text{TV}}(\pi^*(\cdot | s), \pi(\cdot | s)) = \frac{1}{2} \sum_{a \in \mathcal{A}} [|\pi^*(a|s) - \pi(a|s)|]$  is the total variation (TV) distance

that measures the decision difference between  $\pi^*$  and  $\pi$  at state s, and we term this metric as **policybased BC (PBC).** With the exemplar  $\pi^*$ , offline behavior distillation performance  $J(\pi)$ , where  $\pi$  is trained on  $\mathcal{D}_{syn}$ , can be guaranteed by the following theorem.

<span id="page-4-0"></span>**Theorem 1** (Theorem 1 in [\[Xu et al., 2020\]](#page-11-10)). *Given two policies of*  $\pi^*$  *and*  $\pi$  *with*  $\mathbb{E}_{s \sim d_{\pi^*}(s)} \left[ D_{\text{TV}} \left( \pi^*(\cdot | s), \pi(\cdot | s) \right) \right] \leq \epsilon$ , we have  $|J(\pi^*) - J(\pi)| \leq \frac{2R_{\text{max}}}{(1-\gamma)^2} \epsilon$ .

Remark 1. *The proof of Theorem [1](#page-4-0) does not necessitate that* π ∗ *is superior to* π*, and thus substituting*  $s \sim d_{\pi^*}(s)$  in  $\mathbb{E}_{s \sim d_{\pi^*}(s)} [D_{\text{TV}}(\pi^*(\cdot | s), \pi(\cdot | s))] \leq \epsilon$  with  $s \sim d_{\pi}(s)$  does not alter the outcome.

Theorem [1](#page-4-0) elucidates that  $\pi$  has close performance to the good policy  $\pi^*$  as long as they act similarly, and  $J(\pi) \to J(\pi^*)$  if their decision difference  $D_{\text{TV}}(\pi^* \vec{(\cdot | s)}, \pi(\cdot | s)) \to 0$ . This is optimistic for the conventional BC setting where the loss can be easily optimized to near zero. However, because of intractable bi-level optimization, the empirical objective  $\epsilon$  is rarely decreased to small values in OBD. According to [\[Xu et al., 2020\]](#page-11-10), the upper bound in Theorem [1](#page-4-0) is tight as quadratic discount complexity  $\mathcal{O}(1/(1-\gamma)^2)$  is inevitable in the worst-case, implying that the distillation performance guarantee collapses quickly as the PBC objective increases. To this end, a more effective OBD objective should be considered to ensure stronger distillation guarantees.

#### 4.2 Action-value weighted PBC

The preceding analysis highlights the inferior distillation guarantee of  $\mathcal{O}(1/(1-\gamma)^2)$  with PBC. To establish a superior OBD objective, we prove the equivalence between the performance gap of  $J(\pi^*) - J(\pi)$  and action-value weighted  $\pi^* (a|s) - \pi (a|s)$  (Theorem [2\)](#page-4-1). By optimizing the weighted decision difference, the performance gap can be non-vacuously bounded with a reduced discount complexity of  $\mathcal{O}(1/(1 - \gamma))$  (Corollary [1\)](#page-4-2). Motivated by these theoretical insights, we propose action-value weighted PBC as the OBD objective for a tighter distillation performance guarantee.

<span id="page-4-1"></span>**Theorem 2.** For any two policies  $\pi$  and  $\pi^*$ , we have

$$
J(\pi^*) - J(\pi) = \frac{1}{1 - \gamma} \mathbb{E}_{s \sim d_{\pi}(s)} \left[ q_{\pi^*}(s, \cdot) \left( \pi^* (\cdot | s) - \pi (\cdot | s) \right) \right],\tag{4}
$$

*where the dot notation*  $(\cdot)$  *is a summation over the action space, i.e.,*  $q_{\pi^*}(s, \cdot)(\pi^*(\cdot|s) - \pi(\cdot|s)) =$  $\sum_{a \in \mathcal{A}} q_{\pi^*} (s, a) (\pi^* (a|s) - \pi (a|s)).$ 

**Proof Sketch.** (1) With RL definitions, we represent  $J(\pi^*) - J(\pi)$  by

<span id="page-4-3"></span>
$$
J(\pi^*) - J(\pi) = \mathbb{E}_{s \sim d_{\pi^*}^0(s)} \left[ q_{\pi^*}(s, \cdot) \left( \pi^*(\cdot | s) - \pi(\cdot | s) \right) \right] + \mathbb{E}_{\rho_{\pi}^1(s, a)} \left[ q_{\pi^*}(s, a) - q_{\pi}(s, a) \right];
$$

(2) then we prove the iterative formula *w.r.t.*  $\mathbb{E}_{\rho^n_{\pi}(s,a)}[q_{\pi^*}(s,a) - q_{\pi}(s,a)]$ :

$$
\mathbb{E}_{\rho_{\pi}^{n}(s,a)}\left[q_{\pi^{*}}(s,a)-q_{\pi}(s,a)\right]
$$
  
= $\gamma \mathbb{E}_{s \sim d_{\pi}^{n+1}(s)}\left[q_{\pi^{*}}(s,\cdot) \left(\pi^{*}(\cdot|s)-\pi(\cdot|s)\right)\right]+\gamma \mathbb{E}_{\rho_{\pi}^{n+1}(s,a)}\left[q_{\pi^{*}}(s,a)-q_{\pi}(s,a)\right];$ 

(3) integrating the two equations above yields the desired result

$$
J(\pi^*) - J(\pi) = \sum_{t=0}^{\infty} \gamma^t \mathbb{E}_{s \sim d^t_{\pi}(s)} \left[ q_{\pi^*}(s, \cdot) \left( \pi^*(\cdot | s) - \pi(\cdot | s) \right) \right].
$$

The complete proof can be found in Appendix [A.1.](#page-12-0) Since  $q_{\pi^*}(s, a)$  *represents the expected return*  $u$ nder the decent policy  $\pi^*$  when staring from  $(s,a)$  and reaches the maximum if  $\pi^*$  is truly optimal, it *can be interpreted as the importance of*  $(s, a)$ , and higher return is likely to be achieved when starting from more important  $(s, a)$ . Consequently, the gap between  $J(\pi^*)$  and  $J(\pi)$  directly depends on the importance-weighted decision difference between  $\pi^*$  and  $\pi$ . Based on Theorem [2](#page-4-1) and  $q_{\pi^*} \ge 0$ , we can readily derive a bound on the guarantee on  $|J(\pi^*) - J(\pi)|$  by applying the triangle inequality.

<span id="page-4-2"></span>**Corollary 1.** *Given two policies of*  $\pi^*$  *and*  $\pi$  *with*  $\mathbb{E}_{s \sim d_{\pi}(s)} [q_{\pi^*}(s, \cdot) | \pi^*(\cdot|s) - \pi(\cdot|s) |] \leq \epsilon$ , we *have*  $|J(\pi^*) - J(\pi)| \leq \frac{1}{1-\gamma} \epsilon$ *.* 

**Tightness** Since only the triangle inequality is applied, there exists the worst case for  $\pi$  where  $\pi^*(a|s) - \pi(a|s) < 0$  holds only when  $q_{\pi^*}(s, a) = 0$ . This makes the inequality collapse to equality in Corollary [1,](#page-4-2) thereby demonstrating that the upper bound in Corollary [1](#page-4-2) is *non-vacuous*.

Algorithm 1: Action-value weighted PBC

<span id="page-5-1"></span>**Input** :offline RL dataset  $\mathcal{D}_{\text{off}}$ , synthetic data size  $N_{\text{syn}}$ , loop step  $\overline{T, T_{\text{out}}}$ , learning rate  $\alpha_0$ ,  $\alpha_1$ , momentum rate  $\beta_0$ ,  $\beta_1$ **Output**: synthetic dataset  $\mathcal{D}_{syn}$  $\pi^*,\widetilde{q}_{\pi^*}\leftarrow \mathsf{OfflineRL}({\cal D}_{\tt off})$ Initialize  $\mathcal{D}_{syn} = \{(s_i, a_i)\}_{i=1}^{N_{syn}}$  by randomly sampling  $(s_i, a_i) \sim \mathcal{D}_{off}$ for  $t_{\text{out}} = 1$  *to*  $T_{\text{out}}$  do Randomly initialize policy network parameters  $\theta_0$  $\triangleright$  Behavioral cloning with synthetic data. for  $t = 1$  *to*  $T$  do Compute the BC loss *w.r.t.* synthetic data  $\mathcal{L}_{t-1} = \ell_{BC}(\theta_{t-1}, \mathcal{D}_{syn})$ Update  $\theta_t \leftarrow \texttt{GradDescent}(\nabla_{\theta_{t-1}}\mathcal{L}_{t-1}, \alpha_0, \beta_0)$ end Construct the minibatch  $\mathcal{B} = \{(s_i, a_i)\}_{i=1}^{|\mathcal{B}|}$  by sampling  $s_i \sim \mathcal{D}_{\text{off}}$  and  $a_i \sim \pi^*(\cdot | s_i)$ Compute  $\mathcal{H}(\pi_{\theta_T}, \mathcal{B}) = \frac{1}{|\mathcal{B}|} \sum_{i=1}^{|\mathcal{B}|} q_{\pi^*}(s_i, a_i) (\pi_{\theta_T}(a_i|s_i) - \pi^*(a_i|s_i))^2$ Update  $\mathcal{D}_{syn} \leftarrow \texttt{GradDescent}(\nabla_{\mathcal{D}_{syn}} \mathcal{H}(\pi_{\theta_T}, \mathcal{B}), \alpha_1, \beta_1)$ end

**Comparison to Thm.** [1](#page-4-0) With the fact  $q_{\pi^*}(s, a) \leq \sum_{t=0}^{\infty} R_{\max} = \frac{R_{\max}}{1-\gamma}$ , we have

$$
\mathbb{E}_{s \sim d_{\pi}(s)} \left[ q_{\pi^*}\left(s, \cdot\right) | \pi^*(\cdot|s) - \pi(\cdot|s) | \right] \leq \frac{R_{\max}}{1 - \gamma} \mathbb{E}_{s \sim d_{\pi}(s)} \left[ | \pi^*(\cdot|s) - \pi(\cdot|s) | \right],\tag{5}
$$

therefore our bound in Corollary [1](#page-4-2) is significantly tighter than Theorem [1,](#page-4-0) as  $q_{\pi^*}(s, a) = \sum_{t=0}^{\infty} R_{\max}$ requires  $\pi^*$  to achieve the maximum reward at every step. This condition is particularly difficult for sparse-reward environments where most  $r(s, a)$  are close to zero. Moreover, combining the proof of Theorem [2](#page-4-1) and Eq. [5](#page-4-3) provides a more straightforward proof of Theorem [1.](#page-4-0)

As shown by the theoretical analysis, action-value weighted objective offers stronger distillation guarantees due to the linear discount factor complexity  $\mathcal{O}(1/(1-\gamma))$ . This improvement alleviates the loose guarantee caused by limited optimization in OBD compared to former quadratic  $\mathcal{O}(1/(1-\gamma)^2)$ . Accordingly, we propose action-value weighted PBC (Av-PBC) as the OBD objective:

<span id="page-5-0"></span>
$$
\mathcal{H}(\pi, \mathcal{D}_{\text{off}}) = \mathbb{E}_{s \sim \mathcal{D}_{\text{off}}} \left[ q_{\pi^*}(s, \cdot) \left( \pi(\cdot | s) - \pi^*(\cdot | s) \right)^2 \right]. \tag{6}
$$

While Av-PBC is theoretically induced, it is quite intuitive to understand: states s in  $\mathcal{D}_{\text{off}}$  are normally sampled by a mixture of policies instead of the expert  $\pi^*$ . If we sampled a bad state s with extremely small  $q_{\pi^*}(s, a)$ , measuring the decision difference between  $\pi$  and  $\pi^*$  will be less important. As for practical implementation, Eq. [6](#page-5-0) requires summing over the entire action space A to compute  $\sum_{a \in A}$ , which is highly inefficient for large |A|. Considering the expert policy is typically highly concentrated, *i.e.*, only a few actions are selected by  $\pi^*$  with large action values, we instead sample  $a \sim \pi^*(\cdot|s)$  to efficiently estimate Eq. [6.](#page-5-0) The pseudo-code of Av-PBC is presented in Algorithm [1.](#page-5-1)

## 5 Experiments

In this section, we evaluate the proposed ODB algorithms across multiple offline RL datasets from perspectives of (1) distillation performance, (2) distillation convergence speed, (3) cross-architecture and cross-optimizer generalization, and (4) policy ensemble performance *w.r.t.* distilled data.

Datasets We conduct offline behavior distillation on D4RL [\[Fu et al., 2020\]](#page-9-0), a widely used offline RL benchmark. Specifically, OBD algorithms are evaluated on three popular environments of Halfcheetah, Hopper, and Walker2D. For each environment, three offline RL datasets of varying quality are provided by D4RL, *i.e.*, medium-replay (M-R), medium (M), and medium-expert (M-E) datasets. Thus, a total of  $3 \times 3 = 9$  datasets are employed to assess OBD algorithms. medium dataset is collected from the environment with "medium" level policies; medium-replay dataset consists of recording all samples in the replay buffer observed during training this "medium" level policy; and medium-expert dataset is a mixture of expert demonstrations and sub-optimal data.

<span id="page-6-0"></span>Table 1: Offline behavior distillation performance on D4RL offline datasets. The result for Random Selection (Random) is obtained by repeating 10 times. For DBC, PBC, and Av-PBC, the results are averaged across five seeds and the last five evaluation steps. The best OBD result for each dataset is marked with **bold** scores, and orange-colored scores denote instances where OBD outperforms BC.

| Method        | Halfcheetach |      |      | Hopper |      |       | Walker2D |      |       | Average |
|---------------|--------------|------|------|--------|------|-------|----------|------|-------|---------|
|               | M-R          | M    | M-E  | M-R    | M    | M-E   | M-R      | M    | M-E   |         |
| Random        | 0.9          | 1.8  | 2.0  | 19.1   | 19.2 | 11.6  | 1.9      | 4.9  | 6.7   | 7.6     |
| DBC           | 2.5          | 28.2 | 29.0 | 12.1   | 37.8 | 31.1  | 6.1      | 29.3 | 11.7  | 20.9    |
| <b>PBC</b>    | 19.4         | 30.9 | 20.5 | 35.6   | 25.1 | 33.4  | 41.5     | 33.2 | 34.0  | 30.4    |
| Av-PBC        | 35.9         | 36.9 | 22.0 | 40.9   | 32.5 | 38.7  | 55.0     | 39.5 | 42.1  | 38.2    |
| BC (Whole)    | 14.0         | 42.3 | 59.8 | 22.9   | 50.2 | 51.7  | 14.6     | 65.9 | 89.6  | 45.7    |
| OffRL (Whole) | 45.8         | 47.6 | 50.8 | 98.0   | 56.4 | 107.3 | 87.4     | 84.0 | 109.0 | 70.1    |

Setup The advanced offline RL algorithm of Cal-QL [\[Nakamoto et al., 2023\]](#page-10-7) is utilized to extract the decent  $\pi^*$  and  $q_{\pi^*}$  from  $\mathcal{D}_{\text{off}}$ . A four-layer MLP serves as the default architecture for policy networks. The size of synthetic data  $N_{syn}$  is set to 256. Standard SGD is employed in both inner and outer optimization, and learning rates  $\alpha_0 = 0.1$  and  $\alpha_1 = 0.1$  for the inner and outer loop, respectively, and corresponding momentum rates  $\beta_0 = 0$  and  $\beta_1 = 0.9$ . Additional implementation details are provided in Appendix [B.](#page-13-0)

**Evaluation** To accesss the performance of  $\mathcal{D}_{syn}$ , we train policy networks on  $\mathcal{D}_{syn}$  with standard BC, and obtain the corresponding averaged return by interacting with the environment for 10 episodes. We use normalized return [\[Fu et al., 2020\]](#page-9-0) for better visualization: normalized return  $=$  $100 \times \frac{\text{return } - \text{ random return}}{\text{experiment}}$ , where random return and expert return refer to returns of random policies and the expert policy (online SAC [\[Haarnoja et al., 2018\]](#page-9-12)), respectively.

**Baselines** (1) *Random Selection*: randomly selecting  $N_{syn}$  real state-action pairs from  $\mathcal{D}_{off}$ ; (2) *DBC*; (3) *PBC*; (4) *Av-PBC*. We also report policy performance of behavioral cloning and Cal-QL in terms of training on the whole offline dataset  $\mathcal{D}_{\text{off}}$  for a comprehensive comparison.

#### 5.1 Main Results

We first investigate the performance of various OBD algorithms (DBC, PBC, Av-PBC) across offline datasets of varying quality and environments, as detailed in Table [1.](#page-6-0) Several observations are obtained from the results: (1) offline behavior distillation effectively synthesize informative data that enhance policy training (DBC/PBC/Av-PBC *vs.* Random Selection); (2) PBC demonstrates better distillation performance than the basic DBC, especially given the low-quality RL data, highlighting the benefit of action correction in the sub-optimal data (30.4 *vs.* 20.9); (3) Av-PBC considerably outperforms PBC across all datasets (38.2 *vs.* 30.4); (4) when the offline data are collected by low-quality policies (medium-replay), Av-PBC can surpass BC trained on the whole data, while it gradually lags behind BC with higher-quality offline data (medium-replay and medium-expert); (5) given that the objective of OBD is to approximate the decent policy extracted by offline RL algorithms, offline RL serves as an upper bound for OBD performance. In summary, the empirical results show that Av-PBC increases OBD performance by a substantial margin compared to the baselines (82.8% for DBC and 25.7% for PBC).

An interesting phenomenon observed with Av-PBC is that *synthetic data distilled from* medium-replay *offline datasets exhibit better performance than those distilled from* medium-expert *offline datasets*. We explain here: while medium-expert data offer better quality, medium-replay data contains more diverse states due to being sampled by a mixture of less-trained policies that explore a wider rage of states. This is similar to exploration-exploitation dilemma in RL [\[Sutton and Barto, 2018\]](#page-10-15) and underscores the importance of state coverage in original data for OBD.

**Training Time Comparison** To further illustrate the advantages of OBD, we compare the time required for training polices on original data versus OBD-distilled data. For synthetic data with a size of 256, only 100 optimization steps are necessary, corresponding to a training time of 0.2s, while 25k∼125k steps are required for BC on original data. With distilled data, the training time can be reduced by over 99.5%. A detailed list of training steps for all datasets is provided in Appendix [C.](#page-13-1)

Image /page/7/Figure/0 description: This figure displays a collection of nine line graphs, organized into a 3x3 grid, comparing the performance of four different algorithms: BC (dashed line), DBC (blue line), PBC (red line), and Av-PBC (yellow line). Each graph plots 'Normalized Return' on the y-axis against 'Step' on the x-axis, with steps ranging from 0 to 200k or 0 to 100k depending on the specific graph. The graphs are categorized by environment (Halfcheetah, Hopper, Walker2D) and difficulty level (medium-replay, medium, medium-expert). The top row shows 'medium-replay' tasks, the middle row shows 'medium' tasks, and the bottom row shows 'medium-expert' tasks. The x-axis labels are consistently shown as multiples of 25k or 20k. The y-axis scales vary slightly across the graphs, generally ranging from 0.0 to 0.4, 0.5, 0.6, or 0.7. Shaded regions around each line indicate the variance or confidence interval for each algorithm's performance. The figure is further divided into three columns, labeled (a) Halfcheetah, (b) Hopper, and (c) Walker2D at the bottom.

<span id="page-7-0"></span>Figure 1: Plots of OBD performance, represented by the normalized returns of policies trained on synthetic data, as functions of distillation steps on (a) Halfcheetah; (b) Hopper; and (c) Walker2D environment. Each curve is averaged over five random seeds.

<span id="page-7-1"></span>Table 2: Offline behavior distillation performance across various policy network architectures and optimizers (Optim). Red-colored scores and green-colored scores in brackets denote the performance degradation and improvement, respectively, compared to the default training setting. The results are averaged over five random seeds and the last five evaluation steps.

| Arch/Opt     |          | Halfcheetach |      |       | Hopper |      |       | Walker2D |      |       | Average   |
|--------------|----------|--------------|------|-------|--------|------|-------|----------|------|-------|-----------|
|              |          | $M-R$        | M    | $M-E$ | $M-R$  | M    | $M-E$ | $M-R$    | M    | $M-E$ |           |
| Architecture | 2-layer  | 37.1         | 35.9 | 10.9  | 29.9   | 26.2 | 33.9  | 49.2     | 41.3 | 51.1  | 35.1(3.1) |
|              | 3-layer  | 38.6         | 39.7 | 19.4  | 39.0   | 28.1 | 41.5  | 63.2     | 44.1 | 55.3  | 41.0(2.8) |
|              | 5-layer  | 36.1         | 37.7 | 20.0  | 37.1   | 29.1 | 36.6  | 52.0     | 36.7 | 31.6  | 35.2(3.0) |
|              | 6-layer  | 32.1         | 36.0 | 17.3  | 36.9   | 29.6 | 32.8  | 47.1     | 28.2 | 25.5  | 31.7(6.5) |
|              | Residual | 36.9         | 36.4 | 20.0  | 38.8   | 29.8 | 40.3  | 47.5     | 35.7 | 37.1  | 35.8(2.4) |
| Optim        | Adam     | 35.8         | 37.6 | 22.9  | 40.5   | 31.2 | 40.2  | 55.8     | 41.9 | 47.7  | 39.3(1.1) |
|              | AdamW    | 36.8         | 37.9 | 21.4  | 40.6   | 33.3 | 41.1  | 55.4     | 44.2 | 43.2  | 39.3(1.1) |
|              | SGDm     | 36.4         | 37.3 | 21.8  | 40.4   | 30.9 | 39.2  | 54.7     | 40.2 | 42.1  | 38.1(0.1) |

Convergence Speed of OBD To compare the convergence speed of OBD algorithms, we plot the performance of various OBD algorithms over distillation step; please see Figure [1.](#page-7-0) These plots demonstrate that Av-PBC not only improves the OBD performance, but has significant convergence speed and requires only a quarter of the distillation steps compared to DBC and PBC, which is essential for OBD considering the compute-intensive bi-level optimization.

Cross Architecture and Optimizer Performance We evaluate the synthetic data across various training configurations to assess the cross-architecture/optimizer generalization of Av-PBC. Concretely, we employ the data distilled by Av-PBC with the default network (4-layer MLP) and optimizer (SGD) to train (1) different networks of 2/3/5/6-layer and residual MLPs and (2) the default 4-layer MLP with different optimizers of Adam, AdamW, and SGDm (SGD with momentum=0.9). The

<span id="page-8-0"></span>Table 3: Offline behavior distillation performance on D4RL offline datasets with ensemble num of 10. Green-colored scores in brackets denote the performance improvement compared to the non-ensemble setting. The results are averaged over five random seeds and the last five evaluation steps.

| Method | Halfcheetach |      |      | Hopper |      |      | Walker2D |      |      | Average    |
|--------|--------------|------|------|--------|------|------|----------|------|------|------------|
|        | M-R          | M    | M-E  | M-R    | M    | M-E  | M-R      | M    | M-E  |            |
| DBC    | 2.0          | 30.0 | 31.8 | 9.3    | 44.9 | 43.3 | 5.8      | 50.6 | 33.6 | 27.9 (7.0) |
| PBC    | 12.9         | 33.4 | 31.6 | 36.6   | 36.7 | 41.8 | 64.1     | 41.6 | 42.0 | 37.9 (7.3) |
| Av-PBC | 39.8         | 41.4 | 37.2 | 39.7   | 27.6 | 38.8 | 75.9     | 58.6 | 73.7 | 48.1 (9.9) |

results are presented in Table [2.](#page-7-1) As shown in the last column of average performance, we observe that (1) albeit a slight drop, synthetic data distilled by Av-PBC are still valid in training different policy networks, and (2) the performance of distilled data is relatively robust to the variation of optimizers. Therefore, the Av-PBC-distilled data possess satisfied cross-architecture/optimizer performance.

Policy Ensemble on OBD Data With the tiny distilled dataset, policy ensemble can be efficiently performed to further enhance policy performance. This is achieved by training multiple policy networks on synthetic data and then combining their outputs to generate actions. To evaluate the performance gain from policy ensemble, we train 10 policy networks with different seeds; please see Table [3.](#page-8-0) The results demonstrate that (1) policies trained on synthetic data can be substantially boosted through ensemble (25.8% for Av-PBC); and (2) Av-PBC exhibits a larger performance gain than DBC and PBC (9.9 *vs.* 7.0/7.3), highlighting the advantages of Av-PBC in policy ensemble.

# 6 Discussion

Applications Distilled behavioral data encapsulate critical decision-making knowledge from offline RL data and associated environment, making them highly applicable to various downstream RL tasks. Through BC on distilled data, we can *rapidly pretrain a good policy* for online RL fine-tuning [\[Goecks et al., 2019\]](#page-9-2). On the other hand, after offline pretraining, the policy can be further enhanced by online fine-tuning, while there exists *catastrophic forgetting w.r.t.* offline data knowledge during fine-tuning [\[Luo et al., 2023\]](#page-10-17). To tackle this challenge, [Zhao et al.](#page-11-3) [\[2022\]](#page-11-3) propose to use BC loss *w.r.t.* offline data as a constraint during the fine-tuning phase. By replacing the massive offline data with distilled data, we can achieve more efficient loss computation and thus better algorithm convergence. A similar approach can be achieved to circumvent catastrophic forgetting in continual offline RL [\[Gai et al., 2023\]](#page-9-1), where the goal is to learn a sequence of offline RL tasks while retaining good performance across all tasks. Moreover, *multi-task offline RL* [\[Yu et al., 2021\]](#page-11-2), which learns multiple RL tasks jointly from a combination of specific offline datasets, also receives benefits from OBD in terms of efficiency by alternative training on the mixture of distilled data via BC [\[Lupu et al., 2024\]](#page-10-13).

Beyond benefits in efficient policy training, OBD shows potential for *protecting data privacy*: given that offline datasets often contain sensitive information, such as medical records, privacy concerns are significant in offline RL due to various privacy attacks on the learned policies [\[Qiao and Wang, 2023\]](#page-10-3). OBD can enhance privacy preservation by publishing smaller, distilled datasets instead of the full, sensitive data. Besides, distilled behavioral data is also beneficial for *explainable RL* by highlighting the critical states and corresponding actions. A example of this is provided in Appendix [D.](#page-14-0)

Limitations The OBD data are 2-tuples of (state, action) and exclude reward. Thus, the distilled data are solely leveraged by the supervised BC and invalid for conventional RL algorithms with Bellman backup. Despite this deficiency, OBD data can still facilitate the applications above by efficiently injecting high-quality decision-making knowledge into policy networks with BC loss.

We note that two major challenges remain in current OBD algorithms: distillation inefficiency and policy performance degradation. While our Av-PBC substantially decreases the distillation steps, the OBD process is still computationally expensive (25 hours for 50k distillation steps on a single NVIDIA V100 GPU) due to the bi-level optimization involved. Moreover, there remains a notable performance gap between OBD and the whole data with offline RL algorithms (38.2 *vs.* 70.1 in Table [1\)](#page-6-0). These limitations also shed light on future directions in improving the efficiency of OBD and bridging the gap between synthetic data and the original offline RL dataset.

## 7 Conclusion

In this paper we integrate the advanced dataset distillation with offline RL data, formalizing the concept of offline behavior distillation (OBD). We introduce two OBD objectives: the naive offline data-based BC (DBC) and its policy-corrected variant, PBC. Through comprehensive theoretical analysis, we demonstrate that PBC offers inferior OBD performance guarantee of  $\mathcal{O}(1/(1-\gamma)^2)$ under complex bi-level optimization, which inevitably incurs significant distillation loss.. To tackle this issue, we theoretically establish the equivalence between policy performance gap and actionvalue weighted decision difference, leading to the proposal of action-value weighted BC (Av-PBC). This novel Av-PBC objective significantly improves the performance guarantee to  $\mathcal{O}(1/(1 - \gamma))$ . Extensive experiments on multiple offline RL datasets demonstrate that Av-PBC vastly enhances OBD performance and accelerates the distillation process by several times.

### References

- <span id="page-9-5"></span>Gaon An, Seungyong Moon, Jang-Hyun Kim, and Hyun Oh Song. Uncertainty-based offline reinforcement learning with diversified q-ensemble. *Advances in neural information processing systems*, 34:7436–7447, 2021.
- <span id="page-9-3"></span>Richard Bellman. Dynamic programming. *science*, 153(3731):34–37, 1966.
- <span id="page-9-10"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022.
- <span id="page-9-11"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *Proceedings of the International Conference on Machine Learning (ICML)*, 2023.
- <span id="page-9-9"></span>Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In Alice H. Oh, Alekh Agarwal, Danielle Belgrave, and Kyunghyun Cho, editors, *Advances in Neural Information Processing Systems*, 2022. URL [https://openreview.net/forum?id=RYZyj\\_wwgfa](https://openreview.net/forum?id=RYZyj_wwgfa).
- <span id="page-9-0"></span>Justin Fu, Aviral Kumar, Ofir Nachum, George Tucker, and Sergey Levine. D4rl: Datasets for deep data-driven reinforcement learning, 2020.
- <span id="page-9-4"></span>Scott Fujimoto and Shixiang Gu. A minimalist approach to offline reinforcement learning. In A. Beygelzimer, Y. Dauphin, P. Liang, and J. Wortman Vaughan, editors, *Advances in Neural Information Processing Systems*, 2021. URL <https://openreview.net/forum?id=Q32U7dzWXpc>.
- <span id="page-9-1"></span>Sibo Gai, Donglin Wang, and Li He. Offline experience replay for continual offline reinforcement learning. *arXiv preprint arXiv:2305.13804*, 2023.
- <span id="page-9-2"></span>Vinicius G Goecks, Gregory M Gremillion, Vernon J Lawhern, John Valasek, and Nicholas R Waytowich. Integrating behavior cloning and reinforcement learning for improved performance in dense and sparse reward environments. *arXiv preprint arXiv:1910.04281*, 2019.
- <span id="page-9-12"></span>Tuomas Haarnoja, Aurick Zhou, Pieter Abbeel, and Sergey Levine. Soft actor-critic: Off-policy maximum entropy deep reinforcement learning with a stochastic actor. In *International conference on machine learning*, pages 1861–1870. PMLR, 2018.
- <span id="page-9-6"></span>Song Han, Huizi Mao, and William J Dally. Deep compression: Compressing deep neural networks with pruning, trained quantization and huffman coding. *International Conference on Learning Representations (ICLR)*, 2016.
- <span id="page-9-7"></span>Yongcheng Jing, Yiding Yang, Xinchao Wang, Mingli Song, and Dacheng Tao. Meta-aggregator: Learning to aggregate for 1-bit graph neural networks. In *ICCV*, 2021.
- <span id="page-9-8"></span>Yongcheng Jing, Chongbin Yuan, Li Ju, Yiding Yang, Xinchao Wang, and Dacheng Tao. Deep graph reprogramming. In *CVPR*, 2023.

- <span id="page-10-16"></span>Rahul Kidambi, Aravind Rajeswaran, Praneeth Netrapalli, and Thorsten Joachims. Morel: Modelbased offline reinforcement learning. *Advances in neural information processing systems*, 33: 21810–21823, 2020.
- <span id="page-10-8"></span>Ilya Kostrikov, Ashvin Nair, and Sergey Levine. Offline reinforcement learning with implicit q-learning. In *International Conference on Learning Representations*, 2022. URL [https://](https://openreview.net/forum?id=68n2s9ZJWF8) [openreview.net/forum?id=68n2s9ZJWF8](https://openreview.net/forum?id=68n2s9ZJWF8).
- <span id="page-10-6"></span>Aviral Kumar, Aurick Zhou, George Tucker, and Sergey Levine. Conservative q-learning for offline reinforcement learning. *Advances in Neural Information Processing Systems*, 33:1179–1191, 2020.
- <span id="page-10-4"></span>Sascha Lange, Thomas Gabel, and Martin Riedmiller. Batch reinforcement learning. In *Reinforcement learning: State-of-the-art*, pages 45–73. Springer, 2012.
- <span id="page-10-1"></span>Shiye Lei and Dacheng Tao. A comprehensive survey of dataset distillation. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 46(01):17–32, jan 2024. ISSN 1939-3539. doi: 10.1109/TPAMI.2023.3322540.
- <span id="page-10-11"></span>Shiye Lei, Hao Chen, Sen Zhang, Bo Zhao, and Dacheng Tao. Image captions are natural prompts for text-to-image models. *arXiv preprint arXiv:2307.08526*, 2023.
- <span id="page-10-0"></span>Sergey Levine, Aviral Kumar, George Tucker, and Justin Fu. Offline reinforcement learning: Tutorial, review, and perspectives on open problems. *arXiv preprint arXiv:2005.01643*, 2020.
- <span id="page-10-17"></span>Yicheng Luo, Jackie Kay, Edward Grefenstette, and Marc Peter Deisenroth. Finetuning from offline reinforcement learning: Challenges, trade-offs and practical solutions. *arXiv preprint arXiv:2303.17396*, 2023.
- <span id="page-10-13"></span>Andrei Lupu, Chris Lu, Jarek Luca Liesen, Robert Tjarko Lange, and Jakob Nicolaus Foerster. Behaviour distillation. In *The Twelfth International Conference on Learning Representations*, 2024. URL <https://openreview.net/forum?id=qup9xD8mW4>.
- <span id="page-10-10"></span>Baharan Mirzasoleiman, Jeff Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In *International Conference on Machine Learning*, pages 6950–6960. PMLR, 2020.
- <span id="page-10-7"></span>Mitsuhiko Nakamoto, Yuexiang Zhai, Anikait Singh, Max Sobol Mark, Yi Ma, Chelsea Finn, Aviral Kumar, and Sergey Levine. Cal-QL: Calibrated offline RL pre-training for efficient online finetuning. In *Thirty-seventh Conference on Neural Information Processing Systems*, 2023. URL <https://openreview.net/forum?id=GcEIvidYSw>.
- <span id="page-10-2"></span>Dean A Pomerleau. Efficient training of artificial neural networks for autonomous navigation. *Neural computation*, 3(1):88–97, 1991.
- <span id="page-10-3"></span>Dan Qiao and Yu-Xiang Wang. Offline reinforcement learning with differential privacy. In *Thirty-seventh Conference on Neural Information Processing Systems*, 2023. URL [https:](https://openreview.net/forum?id=YVMc3KiWBQ) [//openreview.net/forum?id=YVMc3KiWBQ](https://openreview.net/forum?id=YVMc3KiWBQ).
- <span id="page-10-12"></span>Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *Transactions on Machine Learning Research*, 2023. ISSN 2835-8856. URL <https://openreview.net/forum?id=lmXMXP74TO>. Survey Certification.
- <span id="page-10-14"></span>Tim Salimans, Jonathan Ho, Xi Chen, Szymon Sidor, and Ilya Sutskever. Evolution strategies as a scalable alternative to reinforcement learning. *arXiv preprint arXiv:1703.03864*, 2017.
- <span id="page-10-15"></span>Richard S Sutton and Andrew G Barto. *Reinforcement learning: An introduction*. MIT press, 2018.
- <span id="page-10-9"></span>Denis Tarasov, Alexander Nikulin, Dmitry Akimov, Vladislav Kurenkov, and Sergey Kolesnikov. CORL: Research-oriented deep offline reinforcement learning library. In *3rd Offline RL Workshop: Offline RL as a "Launchpad"*, 2022. URL <https://openreview.net/forum?id=SyAS49bBcv>.
- <span id="page-10-5"></span>Denis Tarasov, Vladislav Kurenkov, Alexander Nikulin, and Sergey Kolesnikov. Revisiting the minimalist approach to offline reinforcement learning. *Advances in Neural Information Processing Systems*, 36, 2024.

- <span id="page-11-7"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196–12205, 2022.
- <span id="page-11-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-11-8"></span>Paul J Werbos. Backpropagation through time: what it does and how to do it. *Proceedings of the IEEE*, 78(10):1550–1560, 1990.
- <span id="page-11-9"></span>Wolfram Wiesemann, Angelos Tsoukalas, Polyxeni-Margarita Kleniati, and Berç Rustem. Pessimistic bilevel optimization. *SIAM Journal on Optimization*, 23(1):353–380, 2013.
- <span id="page-11-10"></span>Tian Xu, Ziniu Li, and Yang Yu. Error bounds of imitating policies and environments. *Advances in Neural Information Processing Systems*, 33:15737–15749, 2020.
- <span id="page-11-4"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 46(01):150–170, jan 2024. ISSN 1939-3539. doi: 10.1109/TPAMI.2023.3323376.
- <span id="page-11-2"></span>Tianhe Yu, Aviral Kumar, Yevgen Chebotar, Karol Hausman, Sergey Levine, and Chelsea Finn. Conservative data sharing for multi-task offline reinforcement learning. *Advances in Neural Information Processing Systems*, 34:11501–11516, 2021.
- <span id="page-11-5"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021.
- <span id="page-11-6"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*, 2023.
- <span id="page-11-1"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021. URL [https://openreview.net/](https://openreview.net/forum?id=mSAKhLYLSsl) [forum?id=mSAKhLYLSsl](https://openreview.net/forum?id=mSAKhLYLSsl).
- <span id="page-11-3"></span>Yi Zhao, Rinu Boney, Alexander Ilin, Juho Kannala, and Joni Pajarinen. Adaptive behavior cloning regularization for stable offline-to-online reinforcement learning. *arXiv preprint arXiv:2210.13846*, 2022.

# A Proofs

## <span id="page-12-0"></span>A.1 Proof of Theorem [2](#page-4-1)

*Proof.* With the definitions  $\rho_{\pi}^n(s, a) = \pi(a|s)d_{\pi}^{n-1}(s)$  and  $J(\pi) = \mathbb{E}_{\rho_{\pi}^1(s, a)}[q_{\pi}(s, a)]$ , we have

$$
J(\pi^*) - J(\pi)
$$

$$
= \mathbb{E}_{\rho_{\pi^*}(s,a)} [q_{\pi^*}(s,a)] - \mathbb{E}_{\rho_{\pi}^1(s,a)} [q_{\pi}(s,a)]
$$

$$
= \sum_{(s,a)\in S\times A} [\rho_{\pi^*}^1(s,a)q_{\pi^*}(s,a) - \rho_{\pi}^1(s,a)q_{\pi}(s,a)]
$$

$$
= \sum_{(s,a)\in S\times A} [\pi^*(a|s)d_{\pi^*}^0(s)q_{\pi^*}(s,a) - \pi(a|s)d_{\pi}^0(s)q_{\pi}(s,a)]
$$

$$
= \sum_{(s,a)\in S\times A} [\pi^*(a|s)d_{\pi^*}^0(s)q_{\pi^*}(s,a) - \pi(a|s)d_{\pi^*}^0(s)q_{\pi^*}(s,a) + \pi(a|s)d_{\pi^*}^0(s)q_{\pi^*}(s,a) - \pi(a|s)d_{\pi}^0(s)q_{\pi}(s,a)] \quad (d_{\pi^*}^0(s) \equiv d_{\pi}^0(s) \equiv d^0(s))
$$

$$
= \mathbb{E}_{s\sim d_{\pi^*}^0(s)} \left[ \sum_{a\in A} (\pi^*(a|s) - \pi(a|s))q_{\pi^*}(s,a) \right] + \mathbb{E}_{\rho_{\pi}^1(s,a)} [q_{\pi^*}(s,a) - q_{\pi}(s,a)] \quad (7)
$$

For the term  $q_{\pi^*}(s, a) - q_{\pi}(s, a)$ , we have

<span id="page-12-2"></span>
$$
q_{\pi^{*}}(s, a) - q_{\pi}(s, a)
$$

$$
= r(s, a) + \gamma \mathbb{E}_{s' \sim \mathcal{T}(s'|s, a)} \left[ \sum_{a' \in \mathcal{A}} \pi^{*}(a'|s') q_{\pi^{*}}(s', a') \right]
$$

$$
- r(s, a) - \gamma \mathbb{E}_{s' \sim \mathcal{T}(s'|s, a)} \left[ \sum_{a' \in \mathcal{A}} \pi(a'|s') q_{\pi}(s', a') \right]
$$

$$
= \gamma \mathbb{E}_{s' \sim \mathcal{T}(s'|s, a)} \left[ \sum_{a' \in \mathcal{A}} \pi^{*}(a'|s') q_{\pi^{*}}(s', a') - \pi(a'|s') q_{\pi}(s', a') \right] (8)
$$

Furthermore, due to  $d_{\pi}^{n+1}(s') = \rho_{\pi}^{n}(s, a) \mathcal{T}(s'|s, a)$  we have

<span id="page-12-1"></span>
$$
\mathbb{E}_{\rho_{\pi}^{n}(s,a)}[q_{\pi^{*}}(s,a) - q_{\pi}(s,a)]
$$

$$
= \gamma \mathbb{E}_{\rho_{\pi}^{n}(s,a)}\left[\mathbb{E}_{s'\sim\mathcal{T}(s'|s,a)}\left[\sum_{a'\in\mathcal{A}}\pi^{*}(a'|s')q_{\pi^{*}}(s',a') - \pi(a'|s')q_{\pi}(s',a')\right]\right]
$$

$$
= \gamma \mathbb{E}_{s\sim d_{\pi}^{n+1}(s)}\left[\sum_{a\in\mathcal{A}}\pi^{*}(a|s)q_{\pi^{*}}(s,a) - \pi(a|s)q_{\pi}(s,a)\right]
$$

$$
= \gamma \mathbb{E}_{s\sim d_{\pi}^{n+1}(s)}\left[\sum_{a\in\mathcal{A}}\pi^{*}(a|s)q_{\pi^{*}}(s,a) - \pi(a|s)q_{\pi^{*}}(s,a) + \pi(a|s)q_{\pi^{*}}(s,a) - \pi(a|s)q_{\pi}(s,a)\right]
$$

$$
= \gamma \mathbb{E}_{s\sim d_{\pi}^{n+1}(s)}\left[\sum_{a\in\mathcal{A}}(\pi^{*}(a|s) - \pi(a|s))q_{\pi^{*}}(s,a)\right] + \gamma \mathbb{E}_{s\sim d_{\pi}^{n+1}(s)}\left[\sum_{a\in\mathcal{A}}\pi(a|s)q_{\pi^{*}}(s,a) - \pi(a|s)q_{\pi}(s,a)\right]
$$

$$
= \gamma \mathbb{E}_{s\sim d_{\pi}^{n+1}(s)}\left[\sum_{a\in\mathcal{A}}(\pi^{*}(a|s) - \pi(a|s))q_{\pi^{*}}(s,a)\right] + \gamma \mathbb{E}_{\rho_{\pi}^{n+1}(s,a)}[q_{\pi^{*}}(s,a) - q_{\pi}(s,a)] \qquad (9)
$$

Plugging the iterative formula of Eq. [9](#page-12-1) into Eq. [7](#page-12-2) yields the desired equality:

$$
J(\pi^*) - J(\pi)
$$
  
=  $\mathbb{E}_{s \sim d_{\pi^*}^0(s)} \left[ \sum_{a \in A} (\pi^*(a|s) - \pi(a|s)) q_{\pi^*}(s, a) \right] + \mathbb{E}_{\rho_{\pi}^1(s, a)} [q_{\pi^*}(s, a) - q_{\pi}(s, a)]$   
=  $\sum_{t=0}^{\infty} \gamma^t \mathbb{E}_{s \sim d_{\pi}^t(s)} \left[ \sum_{a \in A} (\pi^*(a|s) - \pi(a|s)) q_{\pi^*}(s, a) \right]$   
=  $\frac{1}{1 - \gamma} \mathbb{E}_{s \sim d_{\pi}(s)} \left[ \sum_{a \in A} (\pi^*(a|s) - \pi(a|s)) q_{\pi^*}(s, a) \right]$  (10)

The last equation uses the definition that  $d_{\pi}(s) = (1-\gamma) \sum_{t=0}^{\infty} \gamma^{t} d_{\pi}^{t}(s)$ . The proof is completed.

## <span id="page-13-0"></span>B Implementation Details

This section provides all the additional implementation details of our experiments.

**OBD Settings** The policy network is a 4-layer multilayer perceptron (MLP) with a width of 256. The synthetic data are initialized by randomly selecting  $N_{syn}$  state-action pairs from the offline data. For DBC and PBC, the distillation step  $T_{\mathtt{out}}$  is set to  $200\mathrm{k}$  for <code>Halfcheetah</code> and <code>Walker2D</code> and  $50\mathrm{k}$ for Hopper, respectively. For Av-PBC, the distillation step  $T_{\mathtt{out}}$  is set to  $50\mathrm{k}$  for Halfcheetah and Walker2D and 20k for Hopper, respectively. The inner loop step  $T_{\text{in}}$  is set to 100.

Offline RL Policy Training We use the advanced offline RL algorithm of Cal-QL [\[Nakamoto](#page-10-7) [et al., 2023\]](#page-10-7) to extract the decent policy  $\pi^*$  and corresponding q value function  $q_{\pi^*}$  from sub-optimal offline data, and the implementation in [\[Tarasov et al., 2022\]](#page-10-9) is employed in our experiments with default hyper-parameter setting.

Cross-architecture Experiments. The width of MLPs are both 256. The residual MLP is a 4-layer MLP, and the intermediate layers are packaged into the residual block.

## <span id="page-13-1"></span>C Training Time Comparison

<span id="page-13-2"></span>

| Table 4: The size and required training steps for convergence for each offline dataset. M denotes the |  |  |
|-------------------------------------------------------------------------------------------------------|--|--|
| million for simplicity. The size and step for synthetic data (Synset) are listed in the last column.  |  |  |

|             | Halfcheetach |    |     | Hopper |    |     | Walker2D |    |     | Synset |
|-------------|--------------|----|-----|--------|----|-----|----------|----|-----|--------|
|             | M-R          | M  | M-E | M-R    | M  | M-E | M-R      | M  | M-E |        |
| <b>Size</b> | 0.2M         | 1M | 2M  | 0.4M   | 1M | 2M  | 0.3M     | 1M | 2M  | 256    |
| Step (k)    | 40           | 25 | 100 | 80     | 50 | 100 | 60       | 50 | 125 | 0.1    |

For the whole original data, offline RL algorithms require dozens of hours. Therefore, we solely compare the training time of BC on synthetic data and BC on original data. Because the training time varies with GPU models (NVIDIA V100 used in our experiments), we report the optimization step, which has a linear relationship to training time, required for training convergence for each original dataset, as shown in Table [4.](#page-13-2)

# <span id="page-14-0"></span>D Examples of Distilled Data

We present several examples of distilled behavioral data for Halfcheetah in Figure [2.](#page-14-1) The top row illustrates the distilled states, while the bottom row depicts the subsequent states after executing the corresponding distilled actions within the environment. The figure demonstrates that (1) the distilled states prioritize "critical states" or "imbalanced states" (for the cheetah) more than "balanced states"; and (2) the states following the execution of distilled actions are closer to "balanced states" compared to the initial distilled states. These examples offer insights into the explainability of reinforcement learning processes.

Image /page/14/Picture/2 description: The image displays a sequence of 10 frames arranged in two rows, illustrating a process involving distilled states and actions. The top row, labeled "Distilled state," shows a stick-figure robot in various poses, seemingly in motion. The bottom row, labeled "Next state," shows the corresponding subsequent states of the robot. Between the two rows, orange arrows labeled "Distilled action" indicate the transition from the distilled state to the next state. The robot appears to be crawling or moving along a checkered surface.

 $\Omega$ . Examples of distilled behavioral data. The top row shows the distilled states, and the  $\Omega$ Figure 2: Examples of distilled behavioral data. The top row shows the distilled states, while the bottom row presents the subsequent state following the execution of the corresponding distilled actions within the environment.

# <span id="page-14-1"></span>E The Performance of Av-PBC across Different Synthetic Data Sizes

We investigate the impact of varying synthetic data size on OBD performance. The results, as shown in Table [5,](#page-14-2) suggest that OBD performance improves with an increase in synthetic data size. This enhancement is attributed to the larger synthetic datasets conveying more comprehensive information regarding the RL environment and associated decision-making processes.

<span id="page-14-2"></span>Table 5: The Av-PBC performance on D4RL offline datasets with different synthetic data sizes.

| <b>Dataset</b>  | Synthetic Data Size |      |      |      |      |
|-----------------|---------------------|------|------|------|------|
|                 | 16                  | 32   | 64   | 128  | 256  |
| Halfcheetah M-R | 6.9                 | 15.3 | 23.8 | 33.2 | 35.9 |
| Hopper M-R      | 27.3                | 29.9 | 32.3 | 38.1 | 40.9 |
| Walker2D M-R    | 14.8                | 21.8 | 34.0 | 50.0 | 55.0 |
# <span id="page-0-0"></span>Unlocking the Potential of Federated Learning: The Symphony of Dataset Distillation via Deep Generative Latents

<PERSON><PERSON><sup>∗1</sup> <PERSON><PERSON><sup>∗1</sup> <PERSON><PERSON><sup>1</sup> <PERSON><PERSON><PERSON><sup>1</sup> <PERSON><PERSON><PERSON><PERSON><sup>2</sup> <PERSON><sup>1</sup> <PERSON><PERSON><sup>1</sup>

<PERSON><PERSON>

1

<sup>1</sup>Duke University  $\frac{2}{\pi}$  Czech Technical University

<EMAIL>

∗ Equal contribution

### Abstract

*Data heterogeneity presents significant challenges for federated learning (FL). Recently, dataset distillation techniques have been introduced, and performed at the client level, to attempt to mitigate some of these challenges. In this paper, we propose a highly efficient FL dataset distillation framework on the* server *side, significantly reducing both the computational and communication demands on local devices while enhancing the clients' privacy. Unlike previous strategies that perform dataset distillation on local devices and upload synthetic data to the server, our technique enables the server to leverage prior knowledge from pre-trained deep generative models to synthesize essential data representations from a heterogeneous model architecture. This process allows local devices to train smaller surrogate models while enabling the training of a larger global model on the server, effectively minimizing resource utilization. We substantiate our claim with a theoretical analysis, demonstrating the asymptotic resemblance of the process to the hypothetical ideal of completely centralized training on a heterogeneous dataset. Empirical evidence from our comprehensive experiments indicates our method's superiority, delivering an accuracy enhancement of up to 40% over non-dataset-distillation techniques in highly heterogeneous FL contexts, and surpassing existing dataset-distillation methods by 18%. In addition to the high accuracy, our framework converges faster than the baselines because rather than the server trains on several sets of heterogeneous data distributions, it trains on a multi-modal distribution. Our code is available at <https://github.com/FedDG23/FedDG-main.git>*

### 1. Introduction

Federated Learning (FL), a recently very popular approach in the realm of IoT and AI, enables a multitude of devices to collaboratively learn a shared model, while keeping the training data localized, thereby addressing privacy concerns inherent in traditional centralized training methods [\[1\]](#page-10-0). In practice, however, FL faces a significant challenge in the form of data heterogeneity due to the diverse and non-i.i.d. nature of client data, shaped by varying user preferences and usage patterns [\[2](#page-10-1)[–4\]](#page-10-2). To address this complex challenge, which impacts the effectiveness and efficiency of the learning process, several previous methods have been proposed, such as [\[5,](#page-10-3) [6\]](#page-10-4). Recent research, such as FedDM [\[7](#page-10-5)], integrates dataset distillation (DD) techniques into FL, performing DD on local devices and uploading synthetic data to the server, which has shown exciting progress in addressing the challenge of data heterogeneity.

However, existing methods incorporating DD in FL are not without their limitations. One significant issue is their inability to enhance knowledge generalization across different model architectures, a challenge especially relevant in FL due to the variability of device resources and capabilities. Furthermore, these methods often compromise data privacy principles, as they require clients to upload synthetic data directly to the server. Addressing these concerns, we propose an advanced, server-centric FL DD framework, which significantly bolsters data privacy and substantially enhances knowledge generalization across different model architectures in various settings to reduce the computational load and communication overhead on client devices. Our methodology is structured into three pivotal components in each communication round. Firstly, it facilitates the training of compact surrogate models on local devices, which are then updated to the server, accommodating diverse resource constraints. Secondly, leveraging pre-trained deep generative models, the server synthesizes distilled data representations via matching training trajectories of the local surrogate models. Subsequently, the server employs this distilled synthetic data to refine a more comprehensive global model. Our extensive experimental analysis underscores the effectiveness of our approach. We demonstrate a notable 40% increase in accuracy compared to traditional

<span id="page-1-1"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays two comparative diagrams illustrating the outcomes of FedAvg and FedDGM. The left diagram, labeled 'FedAvg', shows a central node labeled \"\*\" with three radiating lines connecting to nodes labeled \"θ\*1\", \"θ\*2\", and \"θ\*3\". Above these nodes are three probability distributions labeled \"c1\", \"c2\", and \"c3\", each represented by a blue curve. To the right of the central diagram, a single blue curve depicts a multi-modal distribution. The right diagram, labeled 'FedDGM', shows a 2D scatter plot with axes labeled 'FedDGM'. A blue dot labeled \"θ\*\" is positioned in the upper right quadrant. Below and to the left of this blue dot are three green dots, with arrows pointing towards the blue dot, indicating movement or convergence. Above the scatter plot are three probability distributions labeled \"c1\", \"c2\", and \"c3\", each represented by a green curve. To the left of the scatter plot, a green curve depicts a multi-modal distribution.

Figure 1. Schema of aggregation methods like FedAvg compared to our framework. The ideal problem would be to train on the central multimodal dataset, including all of the data. FedAvg can be considered as solving each problem individually and averaging the final solution, which is not guaranteed to be closer than a certain distance to the true minimum regardless of the quality of the local training. FedDGM attempts to recreate the dataset in the most relevant way for training, by combining distilled datasets from each client.

non-dataset-distillation techniques within varied FL environments, as exemplified by our results on the CIFAR-10 benchmark. Furthermore, our method outperforms existing DD techniques by 18% and shows a remarkable performance improvement of around 10% on high-resolution image datasets like ImageNet.

To motivate the principle, consider Figure [1](#page-1-0) While standard aggregation-based FL frameworks such as FedAvg average the gradients/parameters from each local training rounds together, this effectively can be thought of as training on the datasets individually and computing the arithmetic mean of the result. Recall fundamentally that the ideal problem of centrally learning the entire dataset, that in the FL context is impossible due to privacy or technical constraints. By contrast, in our schema, we attempt to solve the ideal problem by attempting to recreate the distribution by combining the datasets from those distilled on training each client's data. Thus, if the distribution, as far as SGD iterates are concerned, is entirely identical, we would recreate the ideal training scenario. Of course, this is not possible in practice, however, fundamentally the quality of the training is only limited to the quality of the dataset distillation.

The contributions of our work are as follows:

- 1. We present a novel FL dataset distillation framework, allowing clients to train smaller models to mitigate computational costs, while the server aggregates this information to train a larger model.
- 2. We conduct a theoretical analysis demonstrating that, from an asymptotic perspective, our method is equivalent to the theoretical ideal of centralized training on a multi-modal distribution, representing the sum of heterogeneous data.
- 3. We conduct extensive experiments to underscore the effectiveness of our approach. Our empirical evidence demonstrates a significant improvement in model performance in highly heterogeneous FL contexts, setting

a new benchmark for dataset distillation methods in FL. We test our ideas on two widely adopted network architectures on popular datasets in the FL community (CIFAR10, ImageNet) under a wide range of choices of architecture backbones and compare them with several global state-of-the-art baselines. We have the following findings:

- F1: The advantages of our dataset distillation-driven FL approach are accentuated in comparison to aggregationbased FL training methods, particularly when distinct groups of clients manifest substantial differences in the distributions of their local data.
- F2: Theoretically, we find interesting implications of the distributional limit from the bilevel structure of the dataset distillation problem. Considering marginals at this limit motivates the approximation to the ideal problem with an approximate dataset.
- **F3:** Our method is architecture-agnostic and significantly enhances state-of-the-art performance on high-resolution images by leveraging prior information from a pre-trained generative model.
- F4: Our framework converges significantly faster than the state-of-the-arts. The reason is that our framework aggregates the knowledge from the clients by gathering synthetic data rather than averaging the local model parameters, which preserves the local data information much better.

### 2. Related Work

Federated Learning. Federated learning is a decentralized training paradigm composed of two procedures: local training and global aggregation. Therefore, most existing work focuses on either local training [\[6,](#page-10-4) [8,](#page-10-6) [9\]](#page-10-7) or global aggregation [\[10](#page-10-8), [11\]](#page-10-9) to learn a better global model. Dataset distillation has facilitated a range of applications, encompassing continual learning [\[12\]](#page-10-10), neural architecture search [\[13](#page-10-11), [14\]](#page-10-12), and federated learning [\[7,](#page-10-5) [15](#page-10-13), [16](#page-10-14)]. In particular, FedSynth [\[15\]](#page-10-13) suggests an approach to upstream communication in FL. Instead of sending the model update, each client learns and transmits a compact synthetic dataset. This dataset can then be used as the training data. FedDM [\[7](#page-10-5)] involves the creation of synthetic datasets on individual clients to align with the loss landscape of the original data by means of distribution matching. Nonetheless, a privacy concern arises in FedDM, and FedSynth in which the synthesized datasets are shared with the server. The authors in [\[16\]](#page-10-14) recommend employing iterative distribution matching to ensure that clients possess an equal share of balanced local synthetic data.

Coreset Selection. The most relevant existing research to our study pertains to the issue of coreset selection.

<span id="page-2-1"></span>This problem involves taking a fully labeled dataset and aiming to pick a subset from it so that a model trained on this selected subset can closely match the performance of a model trained on the complete dataset [\[17\]](#page-10-15). This technique has proven successful in various applications, including K-median clustering  $[18]$ , low-rank approximation [\[19,](#page-11-1) [20\]](#page-11-2), spectral approximation [\[21](#page-11-3)[–23\]](#page-11-4), and Bayesian inference [\[24\]](#page-11-5). Traditional coreset construction methods typically employ importance sampling based on sensitivity scores, which reflect the significance of each data point with respect to the objective function being minimized [\[18,](#page-11-0) [19](#page-11-1), [25](#page-11-6)]. These methods aim to provide high-confidence solutions. More recently, greedy algorithms, a subset of the Frank-Wolfe algorithm, have been introduced to offer worst-case guarantees for problems like Bayesian inference [\[24\]](#page-11-5).

Dataset Distillation Wang et al.[\[26\]](#page-11-7) pioneered the idea of dataset distillation, where they advocated representing the model's weights in relation to distilled images and refining them through gradient-based hyperparameter optimization. Following that, various research efforts have notably enhanced the results by incorporating methods such as acquiring soft labels [\[27\]](#page-11-8), intensifying the learning signal through gradient matching [\[13\]](#page-10-11), utilizing augmentations [\[12\]](#page-10-10), longrange trajectory matching by transferring the knowledge from pretrained experts [\[28\]](#page-11-9), learning a generative model to synthesize training data [\[29](#page-11-10)].

### 3. Method

#### 3.1. Overview

In this work, we introduce a novel  $FL$  training approach grounded in Dataset distillation with deep Generative Models (FedDGM). Diverging from conventional aggregation based FL training techniques like FedAvg [\[30\]](#page-11-11), Fed-DGM distinguishes itself by enabling collaborative training of a large global model with enhanced performance while maintaining a comparatively lower computational burden. In contrast to data distillation based FL methods, exemplified by FedDM [\[7](#page-10-5)], FedDGM sets itself apart by addressing privacy concerns more effectively than FedDM. This is achieved by exclusively transferring model parameters rather than synthetic data, ensuring a heightened level of privacy preservation.

In particular, to reduce the computational costs for clients, we enable clients to utilize a small *surrogate model*, which typically has fewer model parameters than the global model. Clients use this surrogate model for local training and send their trained models to the server. To avoid additional communication costs and privacy concerns, when the server receives the surrogate models from clients, it employs the Matching Training Trajectories

<span id="page-2-0"></span>

### Algorithm 1 The FedDGM Framework

**Input:**  $M$  clients indexed by  $m$ , participating-client number K, communication rounds  $T_g$ , server global model f with  $w_g^{(0)}$ , server surrogate model  $f_s$  with  $\theta_g^{(0)}$ , pre-trained deep generative model  $G_g$ .

Server executes:

```
initialize global model f with w_g^{(0)}initialize global surrogate model f_s with \theta_g^{(0)}for each round t = 0, 1, 2, ... do
         \mathbb{S}_t \leftarrow (random set of K clients)
         for each client m \in \mathbb{S}_t in parallel do
              broadcast \theta_g^{(t)} to clients
              \theta_m^{(t+1)} \leftarrow \text{ClientUpdate}(\theta_g^{(t)}, D_m)transmit \theta_m^{(t+1)} to the server
         for each client m \in \mathbb{S}_t do
              randomly initialize vectors \tilde{W}_m^{(t)}calculate latent vectors \tilde{Z}_m^{(t)} using \tilde{W}_m^{(t)}for t_d = 1,...,T_d do
                 generate distilled data \tilde{D}_m^{(t)} = G_g(\tilde{Z}_m^{(t)})initialize student network: \hat{\theta}_g^{(t)} = \theta_g^{(t)}for t_s = 1, 2, ... T_s do
                     update \hat{\theta}_g^{(t+i_s)} on \tilde{D}_m^{(t)} from \hat{\theta}_g^{(t+i_s-1)} by SGD
                 compute the Trajectory Matching loss \mathcal{L}_{MTT}update \tilde{Z}_m^{(t)} with respect to \mathcal{L}_{MTT}\tilde{D}_g^{(t+1)} = \{ \tilde{D}_m^{(t)} | \tilde{D}_m^{(t)} = G_g(\tilde{Z}_m^{(t)}), m \in \mathbb{S}_t \}update parameters to w_g^{(t+1)} on \tilde{D}_g^{(t+1)} by SGD
          update parameters to \theta_g^{(t+1)} on \tilde{D}_g^{(t+1)} by SGD
      return \theta_g^{(t+1)}ClientUpdate (\theta_g^{(t)}, D_m):
      initialize local surrogate model: \theta_m^{(t+1)} = \theta_g^{(t)}for t_l = 1, 2, ..., T_l do
          update \theta_m^{(t+1)} on D_m by SGD
      return \theta_m^{(t+1)}
```

(MTT) method [\[28](#page-11-9)] to distill synthetic data for each client. To enhance the training effectiveness of the synthetic data on the global model and the performance of FedDGM on high-resolution datasets, we leverage prior knowledge from pre-trained deep generative models to distill data. Subsequently, the server trains the global model on the distilled data. Moreover, the server uses the distilled data to train an extra global surrogate model, and then sends it back to the clients, enabling them to perform their local training in the next communication round. Fig. [2](#page-3-0) is an overview of Fed-DGM, and Algorithm [1](#page-2-0) summarizes our FedDGM.

#### 3.2. Problem Formulation

We begin by introducing some notations in FedDGM. Assuming that each client  $m$  has its own local training dataset  $D_m$  and a surrogate model parameterized by  $\theta_m^{(0)}$ . In each global communication round  $t$ , client  $m$  addresses the following optimization problem:

<span id="page-3-1"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: The figure illustrates the FedDGM framework, detailing the communication and model interactions between a server and multiple clients. The server hosts a global model and a global surrogate model, along with a trajectory matching component. Clients each have local data and a local surrogate model. The process begins with clients sending their data (Dm) and local surrogate models to the server (arrow 2). The server then uses this information to update its global model and global surrogate model (arrow 1). The server also processes client data through its global generator (Gg), producing intermediate representations (Z~m(t)) and final representations (D~m(t)) which are then used for trajectory matching (arrow 5). The global surrogate model receives aggregated information (arrow 6) and is updated based on the global model's output (arrow 7). The diagram includes a legend explaining different arrow types: forward, backward, client to server, server to client, and aggregation.

Figure 2. An overview of FedDGM. At each global communication round  $t$ , (1) the server sends global surrogate model to clients, (2) clients train local surrogate models on local data, (3) clients send local surrogate models to the server, (4) the server initializes latent vectors and generates synthetic data for each client  $m$ , (5) the server uses MTT to update latent vectors, (6) after distilling data, the server aggregates all synthetic data and gets the synthetic dataset  $\tilde{D}_g^{(t+1)}$ , and (7) the server uses the synthetic dataset to train the global model and global surrogate model.

$$
\theta_m^{(t+1)} = \min_{\theta \in B_r(\theta_g^{(t)})} \mathcal{L}(D_m; \theta),\tag{1}
$$

where  $\theta_g^{(t)}$  is the global surrogate model parameters sent from the server, and  $B_r(\theta_g^{(t)})$  is a r-radius ball around  $\theta_g^{(t)}$ . After client m sends its local surrogate model parameters  $\theta_m^{(t+1)}$  to the server, the server distills synthetic data  $\tilde{D}_m^{(t)}$  based on  $\theta_m^{(t+1)}$ . The server gets the synthetic dataset  $\tilde{D}_g^{(t+1)}$  by aggregating all  $\tilde{D}_m^{(t+1)}$ , and trains the global model parameterized by  $w_g^{(t)}$ , together with the global surrogate model parameterized by  $\theta_g^{(t)}$ , on  $\tilde{D}_g^{(t+1)}$ . Subsequently, the server sends the global surrogate model parameters back to clients. In the next section, we will delve into the core component of FedDGM, which is the data distillation on the server.

### 3.3. Global Data Distillation

Our data distillation incorporates two key techniques to address different issues: distillation via Matching Training Trajectories and optimization via deep generative latents.

#### 3.3.1 Distillation via Matching Training Trajectories

Previous FL training methods that incorporate data distillation directly transmit synthetic data between the server and clients [\[7\]](#page-10-5), leading to privacy concerns. In contrast, FedDGM adopts a different approach where the server only receives surrogate models from clients. This prevents the server from using dataset distillation methods that require access to the actual local data of clients, such as data condensation [\[13\]](#page-10-11) and distribution matching [\[31](#page-11-12)]. Hence, Fed-DGM employs an adaptive version of Matching Training Trajectories, a technique that solely utilizes checkpoints of model parameters saved throughout the training of clients' local surrogate models on their real datasets to distill data.

Specifically, after client  $m$  uploads its local surrogate model parameters  $\theta_m^{(t+1)}$ , the server trains a student network initialized with  $\theta_g^{(t)}$  on a randomly initialized synthetic dataset  $\tilde{D}_m^{(t)}$  for  $T_s$  epochs, where  $\theta_g^{(t)}$  is the global surrogate model parameters received by client  $m$  in the last communication round. We denote the post-training student model parameters as  $\hat{\theta}_g^{(t+T_s)}$ , and define the training trajectory matching loss as the normalized  $L_2$  distances between  $\hat{\theta}_g^{(t+T_s)}$  and client m's local surrogate model parameters  $\theta_m^{(t+1)}$ , i.e.,

$$
\mathcal{L}_{MTT} = \frac{||\hat{\theta}_g^{(t+T_s)} - \theta_m^{(t+1)}||^2}{||\theta_g^{(t)} - \theta_m^{(t+1)}||^2}.
$$
 (2)

Then the server updates the synthetic dataset  $\tilde{D}_m^{(t)}$  according to the training trajectory matching loss  $\mathcal{L}_{MTT}$ . The server repeats the above steps  $T_d$  times to obtain the updated synthetic dataset  $\tilde{D}_m^{(t)}$ .

In the above process, we directly use  $\theta_g^{(t)}$  to initialize the student network instead of randomly selecting a starting epoch from client  $m$ 's training trajectories. This is done to reduce communication costs, allowing the server to distill data even if it only receives updated surrogate model parameters  $\theta_m^{(t+1)}$  from client m. Due to the server's direct use of synthetic data for global model training, as opposed to aggregating model parameters or gradients sent by clients, data distillation significantly enhances the convergence speed of training the global model, noticeably reducing the global communication rounds. More importantly, it allows the global model on the server to have a different architecture from the local surrogate models on clients' devices, thereby reducing the computational costs for clients. To further improve the generalization of synthetic data on the global model, which often has a distinct architecture from clients' local surrogate models, we employ another key technique, optimization via deep generative latents, as introduced in the next section.

#### <span id="page-4-1"></span><span id="page-4-0"></span>3.3.2 Optimization via Deep Generative Latents

In the previous section, unlike the methods for distilling data under centralized learning and other FL training methods that distill data on the clients' side using real data for initializing synthetic data, we randomly initialize synthetic data for each global communication round. This is due to the server's inability to access the local training data of clients. However, distilling data directly using randomly initialized synthetic data tends to result in low-quality synthetic data, subsequently leading to poor performance of the global model. Furthermore, the global model and surrogate model often have different architectures, and data distillation employs the surrogate model, resulting in synthetic data that does not generalize well on the global model. Therefore, during data distillation on the server, we employ a novel technique called optimization via deep generative latents, where the server leverages prior knowledge obtained from a pre-trained generative model to optimize the distillation process.

In our FedDGM, the server possesses a pre-trained generative model denoted as  $G_q$ . Optimizing distilled data in the latent space of a generative model can result in better generalization on models with different architectures compared to directly optimizing in pixel space [\[29](#page-11-10)]. Hence, in each global communication round  $t$ , instead of using randomly initialized synthetic data, the server begins by generating latent vectors  $\tilde{Z}_m^{(t)}$  for each client  $m$  through some initialization methods. These latent vectors are then employed with the help of  $G_g$  to generate the distilled data  $\tilde{D}_m^{(t)}$ . Because  $G_g$  can propagate gradients, it doesn't interfere with the use of  $\mathcal{L}_{MTT}$  for optimizing the latent vectors  $\tilde{Z}_m^{(t)}$ .

We utilized the pre-trained StyleGAN-XL as our generative model. Since StyleGAN-XL has multiple latent spaces in its synthesis network, the choice of which latent space to use for distilling synthetic data is an intriguing question. We denote the distillation space corresponding to the  $n$ -th intermediate layer of  $G<sub>g</sub>$  as Fn space. A smaller n implies that distillation relies more on the prior knowledge of the generative model, resulting in more realistic synthetic data. Conversely, a larger  $n$  makes the synthetic data more expressive. In our experiments, we investigate the impact of different latent spaces on both the quality of the distilled data and the training performance.

We can also leverage prior knowledge from StyleGAN to initialize latent vectors, thus improving the quality of the synthetic data. StyleGAN incorporates a mapping network, which is typically a multi-layer perceptron, responsible for mapping the input vector  $W$  to an intermediate latent space. The mapping network transforms the latent space into a more expressive style space, offering fine-grained control over styles. Therefore, we can randomly generate some vectors  $\tilde{W}_m^{(t)}$  for client  $m$  and use  $G_g$  's mapping network, along

with the earlier layers of the synthesis network, to initialize latent vectors  $\tilde{Z}_m^{(t)}$  that possess prior knowledge.

## 4. Theoretical Analysis

In this section, we articulate the argument that, from an asymptotic standpoint, the described procedure becomes equivalent to the theoretical ideal of centralized training on the cumulative heterogeneous dataset. The central server is performing GD with gradually changing data, with iteration,

$$
\theta_g^{t+1} = \theta_g^t - s_g \sum_{m \in S_t} \nabla f(\theta_g^t; \tilde{D}_m^t)
$$

where  $s_q$  is the step-size and f is the loss function.

Recall that the dataset distillation optimization problem for each agent is defined by,

$$
\begin{array}{ll}\n\min \limits_{\tilde{D}_m^t} & f(\theta^*(\tilde{D}_m^t), D_m^t) \\
\text{s.t.} & \theta^*(\tilde{D}_m^t) \in \arg\min f(\theta, \tilde{D}_m^t)\n\end{array}
$$

One can consider the federated DD component as an iterative optimization process for this particular problem, for simplicity writing each major iteration as a full gradient update,

$$
\tilde{D}_m^{t+1} = \tilde{D}_m^t - s_D \nabla_{\theta} f(\theta^*(\tilde{D}_m^t), D_m^t) \frac{d\theta^*(\tilde{D}_m^t)}{dD_m^t}
$$

where the term  $\frac{d\theta^*(\tilde{D}_m^t)}{dD^t}$  $\frac{U(m)}{dD_m^t}$  represents the sensitivity of the training solution with respect to the distilled dataset. Consider a Stochastic Differential Equation (SDE) model of the training:

$$
d\theta_g^t = -\frac{1}{|S_t|} \sum_{m \in S_t} \nabla f(\theta_g^t; \tilde{D}_m^t) dt + dW_t,
$$
  
$$
d\tilde{D}_t = -\nabla_{\theta} f(\theta^*(\tilde{D}_m^t), D_m^t) \frac{d\theta^*(\tilde{D}_m^t)}{d\tilde{D}_m^t} + d\tilde{W}_t
$$

Note that the second does not depend on  $\theta_q$ , it thus drifts independently. This is of course a simplification as the Local SGD iterations for each client are constrained to be near  $\theta_a$ . As we do not know how far the local iterations are from optimality, and the ultimate intention is to perform dataset distillation as in the solution of the bilevel problem, we study the results with idealistic formalism.

We would need to prove some conditions for this to be the case, but the stationary distribution of this second diffusion can be written by taking the antiderivative of the drift term with respect to  $\tilde{D}^t$  and taking the negative exponential

$$
\pi(\tilde{D}_m^t|\theta^*)\propto \exp\left\{-\beta_D f(\theta^*(\tilde{D}_m^t), D_m^t)\right\}
$$

<span id="page-5-1"></span>where  $\beta = 1/s$ , the step-size of the SGD, which also corresponds to the entropy. As  $\beta \to \infty$ , the distribution converges to a delta distribution on interpolating the sample, and as  $\beta \to 0$ , far from mean samples are more likely to be taken. Now,  $\theta^*(\tilde{D}_m^t)$  also exhibits a distribution, considering the stationary Gibbs for the neural network,

$$
\pi(\theta^*|\tilde{D}_m^t) \propto \exp\left\{-\beta_* f(\theta, \tilde{D}_m^t)\right\}.
$$

As this network is overparametrized relative to the data, this distribution has multiple modes and a connected zero loss region in  $\theta$  space. If we consider iteratively sampling in an alternating fashion from  $\pi(\tilde{D}_m^t|\theta^*)$  to  $\pi(\theta^*|\tilde{D}_m^t)$ , then we know from [\[32](#page-11-13), Theorem 1] that the stationary distributions of  $\tilde{D}_m^t$  and  $\theta^*$  converge to their marginals. Let us now study the properties of the limiting marginal distributions. Detailed balance (as required for ergodicity, or convergence in distribution, see e.g. [\[33\]](#page-11-14)) requires that,

$$
\pi(\theta^*) \exp\left\{-\beta_D f(\theta^*, D_m^t)\right\} = \pi(\tilde{D}_m^t) \exp\left\{-\beta_* f(\theta^*, \tilde{D}_m^t)\right\}
$$

o

Taking  $\beta_* \to \infty$  we see that we have convergence in distribution of,

$$
\pi(\tilde{D}_m^t)=\exp\left\{-\beta_D f(\theta^*, D_m^t)\right\}\pi(\theta^*)
$$

with,

$$
\operatorname{supp}\left(\pi(\theta^{*})\right) \subseteq \operatorname{arg\,min} f(\theta,\tilde{D}^{t}_{m})
$$

Consider taking  $\beta_D \rightarrow \infty$  now, and thus,

$$
\text{supp}\left(\pi(\tilde{D}_m^t)\right) \subseteq
$$
  
$$
\left\{\tilde{D}_m^t : \arg\min f(\theta, \tilde{D}_m^t) \cap \arg\min f(\theta, D_m^t) \neq \emptyset\right\}.
$$

Finally, the solution of  $\theta_q$  is based on a deterministic gradient descent, i.e., there is no stochasticity, because the entire dataset  $\tilde{D}_{m}^{t}$  is small and hence can be loaded in memory. However,  $\tilde{D}_m^t$  itself is a stochastic process. Furthermore, as the network is overparametrized relative to the size of  $\tilde{D}_m^t$ , it has a submanifold of zero loss solutions.

<span id="page-5-0"></span>
$$
\theta_g^* \in \arg\min_{\theta} \sum_m f(\theta; \tilde{D}_m^t)
$$
 (3)

Now, if there exists  $\theta^*$  such that  $\theta^*$  arg min  $f(\theta, D_m^t)$  for all  $m$ , then it also solves  $(3)$ . Otherwise, SGD iterations for [\(3\)](#page-5-0) involve descent by  $\sum_{m} \nabla_{\theta} f(\theta; \tilde{D}_m^t)$ , which is the same as a linear combination of the  $m$  computations to find  $\arg \min f(\theta, D_m^t)$  for all m. Thus we have established the equivalency.

### 5. Experiment

#### 5.1. Experimental Setup

Datasets. We evaluate our method on the CIFAR-10 dataset and five 10-class subsets of ImageNet. Specifically, we

use CIFAR-10  $(32\times32)$  to evaluate the performance of FedDGM on low-resolution data and employ subsets of ImageNet (128  $\times$  128) to evaluate performance on highresolution data. Previous works introduced some subsets of ImageNet, such as ImageWoof (dogs) [\[34\]](#page-11-15), ImageMeow (cats) and ImageFruit (fruits) [\[28](#page-11-9)], as well as ImageFood (food) and ImageMisc (miscellaneous items) [\[29\]](#page-11-10). We provide a detailed list of the categories contained within each subset of ImageNet at the end of this section.

A distinguishing feature of FL is the non-identical distribution (non-i.i.d.) nature of local training data across clients. Assuming there are  $M$  clients, to simulate non-i.i.d. settings, we partition the training data by Dirichlet distribution  $\text{Dir}_{M}(\alpha)$  [\[35,](#page-11-16) [36\]](#page-11-17), where  $\alpha > 0$  is some parameter that controls the non-i.i.d. degree. A smaller  $\alpha$  implies a higher degree of non-i.i.d. By default, we have a total of  $M = 10$ clients, with  $\alpha$  set to 0.5. Furthermore, in Section [5.2,](#page-6-0) we will also investigate the performance of FedDGM under various non-i.i.d. scenarios.

Compared Methods. To provide a more comprehensive understanding of the efficiency of FedDGM, we select three FL training methods based on aggregation: FedAvg [\[30\]](#page-11-11), FedProx [\[5\]](#page-10-3), and FedNova [\[10\]](#page-10-8), as well as a method based on data distillation, FedDM [\[7\]](#page-10-5).

FL Training Settings. We have a total of  $M = 10$  clients, and for each global training round, the server selects all clients for aggregation. For the CIFAR-10 dataset, we set the training batch size to 256, and for subsets of ImageNet, the corresponding training batch size is 32. By default, each client conducts local training for  $T_l = 20$  epochs on its own local training data, and we tune  $T_l$  within the range  $[5, 10, 20, 30]$ . On the server side, for each global training round, the server updates the latent vector for  $T_d = 100$ iterations, and for each distillation step, the server updates the student network for  $T_s = 20$  epochs. The number of images per class (*IPC*) is 10. The images are distilled into F5 space (the 5th layer of StyleGAN-XL) for CIFAR-10 and F12 for subsets of ImageNet. After distilling the data, the server trains the global model for  $T<sub>g</sub> = 1000$  epochs with a training batch size of 256 for CIFAR-10 and 32 for subsets of ImageNet. In particular, we tune the layer of StyleGAN-XL in  $[0, 3, 4, 5, 6, 9]$  and IPC in  $[1, 5, 10, 20]$  for CIFAR-10.

To ensure a fair comparison, we maintain the same local training batch size as FedDGM for baseline methods. For FedAvg, FedProx, and FedNova, we also use the same local training epochs of  $T_l = 20$  as in FedDGM. For FedDM, we adopt the same IPC=10 and train the global model for  $T<sub>g</sub> = 1000$  epochs with the same training batch size, using the synthetic dataset. All the experiments are run for three times, and we report the mean validation accuracy  $\pm$  1 standard deviation for each evaluation case.

<span id="page-6-4"></span><span id="page-6-1"></span>Table 1. Using data distillation for FL training significantly enhances the performance of global models in extremely noni.i.d. scenarios. As data heterogeneity increases, FedDGM exhibits a significant advantage over all baseline methods. Impact of different values of  $\alpha$  on the CIFAR-10 dataset. Each column represents a specific architecture of the global model.

| $\alpha$ | Algorithm | ConvNet          | ResNet18         | VGG11            | ViT              | Average          |
|----------|-----------|------------------|------------------|------------------|------------------|------------------|
| 0.9      | FedAvg    | $70.1_{\pm 0.5}$ | $62.1_{\pm 0.7}$ | $66.2_{\pm 0.6}$ | $54.8_{\pm 0.2}$ | $63.3_{\pm 0.5}$ |
|          | FedProx   | $70.7_{\pm 0.3}$ | $63.0_{\pm 0.6}$ | $66.2_{\pm 0.5}$ | $55.0_{\pm 0.8}$ | $63.7_{\pm 0.5}$ |
|          | FedNova   | $70.6_{\pm 0.7}$ | $62.1_{\pm 1.0}$ | $66.6_{\pm 0.6}$ | $54.5_{\pm 0.4}$ | $63.5_{\pm 0.7}$ |
|          | FedDM     | $70.2_{\pm 0.2}$ | $73.4_{\pm 0.2}$ | $68.7_{\pm 0.4}$ | $51.6_{\pm 0.1}$ | $66.0_{\pm 0.3}$ |
|          | FedDGM    | $70.8_{\pm 0.5}$ | $73.8_{\pm 0.2}$ | $70.1_{\pm 0.4}$ | $55.3_{\pm 0.3}$ | $67.5_{\pm 0.4}$ |
| 0.5      | FedAvg    | $69.4_{\pm0.1}$  | $61.1_{\pm 0.8}$ | $64.4_{\pm 0.7}$ | $53.5_{\pm 0.2}$ | $62.1_{\pm 0.4}$ |
|          | FedProx   | $69.5_{\pm 0.5}$ | $62.3_{\pm 0.5}$ | $65.3_{\pm 0.4}$ | $53.4_{\pm 0.3}$ | $62.6_{\pm 0.4}$ |
|          | FedNova   | $69.5_{\pm 0.4}$ | $60.9_{\pm 0.7}$ | $64.8_{\pm 0.6}$ | $54.3_{\pm 1.2}$ | $62.4_{\pm 0.7}$ |
|          | FedDM     | $68.9_{\pm 0.3}$ | $72.9_{\pm 0.1}$ | $68.0_{\pm 0.3}$ | $51.0_{\pm 0.6}$ | $65.2_{\pm 0.3}$ |
|          | FedDGM    | $70.8_{\pm 0.9}$ | $73.7_{\pm 0.5}$ | $69.8_{\pm 0.3}$ | $55.9_{\pm 0.7}$ | $67.5_{\pm 0.6}$ |
| 0.1      | FedAvg    | $45.3_{\pm 3.2}$ | $38.0_{\pm 0.9}$ | $41.1_{\pm 2.2}$ | $43.6_{\pm 0.3}$ | $42.0_{\pm 1.7}$ |
|          | FedProx   | $45.2_{\pm 2.2}$ | $40.2_{\pm 0.4}$ | $40.3_{\pm 1.9}$ | $45.6_{\pm 0.6}$ | $42.8_{\pm 1.3}$ |
|          | FedNova   | $42.9_{\pm 1.7}$ | $35.8_{\pm 0.6}$ | $35.8_{\pm 1.7}$ | $42.1_{\pm 0.7}$ | $39.2_{\pm 1.2}$ |
|          | FedDM     | $62.0_{\pm 0.3}$ | $63.6_{\pm 0.3}$ | $61.4_{\pm 0.2}$ | $45.0_{\pm 0.2}$ | $58.0_{\pm 0.3}$ |
|          | FedDGM    | $68.9_{\pm 0.6}$ | $71.6_{\pm 0.2}$ | $68.1_{\pm 0.2}$ | $54.2_{\pm 0.7}$ | $65.7_{\pm 0.4}$ |
| 0.01     | FedAvg    | $14.3_{\pm 2.7}$ | $16.9_{\pm 2.6}$ | $19.3_{\pm 2.9}$ | $31.1_{\pm 1.7}$ | $20.4_{\pm 2.5}$ |
|          | FedProx   | $18.3_{\pm 2.5}$ | $16.7_{\pm 2.5}$ | $14.8_{\pm 3.4}$ | $28.8_{\pm 3.3}$ | $19.7_{\pm 2.9}$ |
|          | FedNova   | $10.1_{\pm 0.1}$ | $16.4_{\pm 1.8}$ | $13.3_{\pm 2.6}$ | $13.3_{\pm 2.4}$ | $13.3_{\pm 1.7}$ |
|          | FedDM     | $47.8_{\pm 0.4}$ | $48.1_{\pm 0.7}$ | $48.9_{\pm 0.5}$ | $36.3_{\pm 0.4}$ | $45.3_{\pm 0.5}$ |
|          | FedDGM    | $66.1_{\pm 0.7}$ | $69.5_{\pm 0.1}$ | $66.4_{\pm 0.3}$ | $51.5_{\pm 0.2}$ | $63.4_{\pm 0.3}$ |

Network Architectures. To demonstrate the crossarchitecture generalization of FedDGM, for data distillation-based FL methods (i.e., FedDM and Fed-DGM), we set the local models of clients as a 5-layer ConvNet [\[37\]](#page-11-18) and set the global model to be some larger models. Specifically, for CIFAR-10, we evaluate global models such as ConvNet, ResNet18 [\[38](#page-11-19)], VGG11 [\[39\]](#page-11-20), and ViT [\[40\]](#page-11-21), while for subsets of ImageNet, we consider global models like ConvNet, ResNet18, and ResNet34 [\[38](#page-11-19)]. For the remaining baseline methods, the structure of clients' models remains consistent with the server's global model.

### <span id="page-6-0"></span>5.2. Performance and Convergence Rate

Performance Across Different Datasets. We investigate the performance of FedDGM on both low-resolution data using CIFAR-10 and high-resolution data using ImageNet subsets across various non-i.i.d. degrees (different  $\alpha$  values) and architectures. As shown in Table [1,](#page-6-1) using data distillation for FL training significantly enhances the performance of global models in extremely non-i.i.d. scenarios (e.g.,  $\alpha = 0.01$ ). More importantly, our FedDGM consistently outperforms the baseline methods on CIFAR-10 across different global model architectures. Particularly, as data heterogeneity increases (i.e.,  $\alpha = 0.9 \rightarrow 0.01$ ), FedDGM exhibits a significant advantage over the baseline methods. This could be attributed to the way we distill data,

which compensates for the challenges in training posed by high data heterogeneity. When client  $i$  lacks data in class  $i$ , the global model parameters received by client  $i$  already contain information about data in class  $j$  from other clients, which allows the server to use client  $i$ 's local model parameters to distill data in class  $\dot{\jmath}$  with relatively good quality, as depicted in Fig. [3.](#page-6-2) To facilitate a quick understanding of the key findings in these extensive tables, we have presented the average accuracy, represented by the last column in Table [1,](#page-6-1) in Fig. [4.](#page-6-3) It is evident that, with increased heterogeneity (i.e., smaller  $\alpha$  values), the superiority of FedDGM becomes more pronounced. Unlike other baseline methods, which experience a substantial decline in performance as  $\alpha$ decreases, FedDGM maintains a relatively stable level of performance. We also conducted experiments on a high resolution dataset i.e., one of the subsets of ImageNet, ImageFruit under different data heterogeneity, and the results are shown in Table [2](#page-7-0) and Fig. [5.](#page-7-1)

The reported results in Table [2](#page-7-0) demonstrate a significant improvement achieved by FedDGM compared to the stateof-the-art. FedDGM led to a remarkable improvement of 15%, and 4% over FedDM for the most ( $\alpha = 0.01$ ) and the least ( $\alpha = 0.9$ ) heterogeneous setting. These findings further reinforce the claim that *greater data heterogeneity correlates with amplified performance improvement in the global model trained through our dataset distillation method FedDGM* . For a concise comprehension of the key findings within these extensive tables, we have summarized the average accuracy in the last column of Table [2](#page-7-0) in Fig. [5.](#page-7-1)

<span id="page-6-2"></span>Image /page/6/Picture/7 description: A collage of multiple images, mostly featuring trucks and construction vehicles. Some images show the front of white trucks, while others display the sides of various trucks, including a red and blue truck, and an orange construction vehicle with a warning sign. The images are varied in color and clarity, with some appearing dark or pixelated.

Figure 3. Illustrations of distilled data examples labeled as "truck". These examples are distilled using a client's local surrogate model whose local data does not contain "truck".

<span id="page-6-3"></span>Image /page/6/Figure/9 description: A bar chart displays the test accuracy (%) for different federated learning algorithms (FedAvg, FedProx, FedNova, FedDM, FedDGM) across various values of alpha (".9", ".5", ".1", ".01"). For alpha = .9, FedAvg achieves approximately 63% accuracy, FedProx 64%, FedNova 64%, FedDM 66%, and FedDGM 68%. For alpha = .5, FedAvg is around 62%, FedProx 63%, FedNova 63%, FedDM 65%, and FedDGM 67%. At alpha = .1, FedAvg is about 41%, FedProx 42%, FedNova 39%, FedDM 59%, and FedDGM 67%. Finally, for alpha = .01, FedAvg is approximately 20%, FedProx 20%, FedNova 12%, FedDM 46%, and FedDGM 64%. The chart indicates that FedDGM generally performs best, especially at lower alpha values, while FedNova shows the lowest accuracy among the tested algorithms.

Figure 4. The benefit of FedDGM is particularly pronounced when the data distributions are extremely non-i.i.d. Impact of different  $\alpha$  on the average accuracy across global models with different architectures on CIFAR10.

<span id="page-7-0"></span>Table 2. Using data distillation for FL training significantly enhances the performance of global models in extremely noni.i.d. scenarios. As data heterogeneity increases, FedDGM exhibits a significant advantage over all baseline methods. Impact of different values of  $\alpha$  on a high resolution dataset, i.e., one of the subsets of ImageNet (ImageFruit). Each column represents a specific architecture of the global model.

| $\alpha$ | Algorithm | ConvNet      | ResNet18     | ResNet34     | Average      |
|----------|-----------|--------------|--------------|--------------|--------------|
| 0.9      | FedAvg    | $43.9\pm0.3$ | $35.1\pm0.8$ | $41.8\pm0.4$ | $40.3\pm0.5$ |
|          | FedProx   | $48.5\pm0.9$ | $37.1\pm0.9$ | $38.9\pm1.4$ | $41.5\pm1.1$ |
|          | FedNova   | $46.8\pm0.7$ | $37.6\pm0.9$ | $42.2\pm1.1$ | $42.2\pm0.9$ |
|          | FedDM     | $49.7\pm0.2$ | $51.7\pm0.5$ | $56.7\pm0.9$ | $52.7\pm0.5$ |
|          | FedDGM    | $54.6\pm0.9$ | $55.7\pm0.7$ | $59.1\pm0.8$ | $56.5\pm0.8$ |
| 0.5      | FedAvg    | $42.7\pm1.9$ | $36.2\pm1.4$ | $39.5\pm2.6$ | $39.5\pm2.0$ |
|          | FedProx   | $43.9\pm0.2$ | $34.7\pm1.8$ | $42.4\pm3.3$ | $40.4\pm1.8$ |
|          | FedNova   | $45.7\pm1.2$ | $35.0\pm0.6$ | $39.1\pm1.0$ | $40.0\pm1.0$ |
|          | FedDM     | $45.7\pm1.7$ | $49.7\pm0.2$ | $53.7\pm1.5$ | $49.7\pm1.1$ |
|          | FedDGM    | $54.5\pm1.5$ | $55.1\pm0.7$ | $58.9\pm0.4$ | $56.2\pm0.8$ |
| 0.1      | FedAvg    | $42.1\pm1.5$ | $33.4\pm2.2$ | $38.0\pm1.5$ | $37.8\pm1.7$ |
|          | FedProx   | $39.8\pm1.2$ | $32.1\pm2.2$ | $34.2\pm1.3$ | $35.4\pm1.6$ |
|          | FedNova   | $40.6\pm1.0$ | $31.5\pm1.6$ | $34.9\pm2.5$ | $35.6\pm1.7$ |
|          | FedDM     | $42.3\pm1.2$ | $45.1\pm0.7$ | $48.7\pm0.7$ | $45.3\pm0.8$ |
|          | FedDGM    | $51.3\pm0.6$ | $51.6\pm1.8$ | $59.1\pm1.5$ | $54.0\pm1.3$ |
| 0.01     | FedAvg    | $23.7\pm0.1$ | $17.7\pm0.5$ | $16.7\pm0.9$ | $19.3\pm0.5$ |
|          | FedProx   | $21.8\pm1.7$ | $16.3\pm0.3$ | $17.7\pm0.8$ | $18.6\pm0.9$ |
|          | FedNova   | $22.3\pm1.5$ | $20.0\pm1.1$ | $17.1\pm0.6$ | $19.8\pm1.1$ |
|          | FedDM     | $32.9\pm1.5$ | $37.3\pm0.9$ | $34.5\pm0.7$ | $34.9\pm1.0$ |
|          | FedDGM    | $44.9\pm1.4$ | $48.5\pm0.7$ | $55.1\pm0.2$ | $49.5\pm0.8$ |

<span id="page-7-1"></span>Image /page/7/Figure/2 description: A bar chart displays the test accuracy (%) of five federated learning algorithms (FedAvg, FedProx, FedNova, FedDM, and FedDGM) across different values of alpha (0.9, 0.5, 0.1, and 0.01). For alpha = 0.9, the accuracies are approximately 40%, 41%, 42%, 53%, and 56% respectively. For alpha = 0.5, the accuracies are around 39%, 40%, 41%, 50%, and 55%. For alpha = 0.1, the accuracies are about 37%, 36%, 35%, 45%, and 53%. For alpha = 0.01, the accuracies are approximately 19%, 20%, 21%, 35%, and 50%. Error bars are present for each bar, indicating variability. The x-axis is labeled 'alpha', and the y-axis is labeled 'Test Accuracy (%)'.

Figure 5. Impact of different  $\alpha$  on the average accuracy across global models with different architectures on ImageFruit.

In the case of high-resolution data, according to re-sults in Table [3,](#page-7-2) utilizing data distillation for FL training significantly enhances the performance of models on high-resolution data, and FedDGM always outperforms baseline methods on all five ImageNet subsets when the server possesses global models with different architectures, such as ConvNet, ResNet18, and ResNet34. Specifically, our algorithm significantly outperforms FedDM and other aggregation-based methods on ImageMeow, Image-Woof, and ImageFruit. For example, FedDGM achieves 57.4±0.6% on ImageWoof when the global model is Con-

<span id="page-7-2"></span>Table 3. FedDGM is architecture-agnostic and performs effectively across both high-resolution and low-resolution datasets. Test accuracy across various subsets of ImageNet. FedDGM outperforms all baseline methods across different global model architectures on these five ImageNet subsets. The default data partitioning distribution is  $Dir<sub>10</sub>(0.5)$ .

| Dataset       | Algorithm | ConvNet                      | ResNet18                    | ResNet <sub>34</sub>        | Average          |  |
|---------------|-----------|------------------------------|-----------------------------|-----------------------------|------------------|--|
| ImMeow        | FedAvg    | $48.3_{\pm 1.5}$             | $34.8_{+2.1}$               | $40.7_{+1.3}$               | $41.3_{\pm 1.6}$ |  |
|               | FedProx   | 49.7 $\pm$ 0.4               | $38.5_{+2.0}$               | $44.3_{\pm 1.3}$            | 44.2 $\pm$ 1.2   |  |
|               | FedNova   | $50.6_{\pm 0.3}$             | $34.0_{\pm 0.6}$            | $38.3_{\pm 1.5}$            | $41.0_{\pm 0.8}$ |  |
|               | FedDM     | $51.7_{\pm 1.3}$             | 53.3 $\pm$ 0.7              | $61.2_{\pm 1.4}$            | 55.4 $\pm$ 1.1   |  |
|               | FedDGM    | $57.8_{\pm 1.2}$             | $56.2_{\pm 1.9}$            | $65.1_{\pm 0.6}$            | 59.7 $\pm$ 1.2   |  |
| ImWoof        | FedAvg    | $38.5{\scriptstyle \pm1.7}$  | $25.7_{\pm 1.5}$            | $29.5_{+2.1}$               | $31.2_{\pm 1.8}$ |  |
|               | FedProx   | $40.5 + 1.2$                 | $25.3_{\pm 0.4}$            | $29.7_{+2.0}$               | $31.8_{\pm 1.2}$ |  |
|               | FedNova   | $40.7_{\pm 1.2}$             | $26.5_{\pm 1.6}$            | $27.0_{\pm 1.6}$            | $31.4_{\pm 1.4}$ |  |
|               | FedDM     | $47.0_{\pm 1.8}$             | 52.7 $_{\pm 0.9}$           | $60.3{\scriptstyle \pm0.2}$ | $53.3_{\pm 1.0}$ |  |
|               | FedDGM    | $57.4_{\pm 2.6}$             | $62.3_{\pm 1.2}$            | 72.7 $_{\pm 0.6}$           | $64.1_{\pm 1.5}$ |  |
| ImFruit       | FedAvg    | $42.7_{\pm 1.9}$             | $36.2_{\pm 1.4}$            | $39.5_{\pm 2.6}$            | $39.5_{\pm 2.0}$ |  |
|               | FedProx   | 43.9 $\pm$ 0.2               | $34.7_{+1.8}$               | $42.4_{+3.3}$               | $40.4_{\pm 1.8}$ |  |
|               | FedNova   | $45.7_{\pm 1.2}$             | $35.0_{\pm 0.6}$            | $39.1_{+1.0}$               | $40.0_{\pm 1.0}$ |  |
|               | FedDM     | $45.7_{\pm 1.7}$             | 49.7 $_{\pm 0.2}$           | 53.7 $\pm$ 1.5              | 49.7 $\pm$ 1.1   |  |
|               | FedDGM    | 54.5 $\pm$ 1.5               | 55.1 $_{\pm 0.7}$           | 58.9 $\pm$ 0.4              | $56.2_{\pm 0.8}$ |  |
| ImFood        | FedAvg    | $45.1_{\pm 0.7}$             | $32.8_{+1.3}$               | $39.3_{+1.2}$               | $39.1_{+1.1}$    |  |
|               | FedProx   | $46.3_{\pm 2.8}$             | $33.4_{\pm 1.9}$            | $36.7_{\pm 0.7}$            | $38.8_{\pm 1.8}$ |  |
|               | FedNova   | $46.7_{\pm 2.3}$             | $32.7_{\pm 1.6}$            | $38.4_{\pm 1.1}$            | $39.3_{\pm 1.7}$ |  |
|               | FedDM     | $50.6_{\pm1.4}$              | $50.7_{\pm 0.9}$            | $59.8_{\pm0.4}$             | 53.7 $_{\pm0.9}$ |  |
|               | FedDGM    | 54.5 $\pm$ 2.2               | 52.9 $\pm$ 1.3              | $60.0_{\pm 0.8}$            | $55.8_{\pm 1.5}$ |  |
| <b>ImMisc</b> | FedAvg    | $56.8_{\pm 0.9}$             | $42.4_{\pm 0.7}$            | $45.5_{+3.2}$               | $48.2_{\pm 1.6}$ |  |
|               | FedProx   | 59.1 $\pm$ 1.9               | 44.7 $\pm$ 1.1              | $46.1_{\pm 1.8}$            | $50.0_{\pm 1.6}$ |  |
|               | FedNova   | $58.8_{\pm 1.0}$             | $42.7_{\pm 0.4}$            | $48.3_{\pm 0.2}$            | $50.0_{\pm 0.5}$ |  |
|               | FedDM     | $64.3_{\pm 1.1}$             | $63.9_{\pm 0.6}$            | $72.2_{\pm 0.3}$            | $66.8_{\pm 0.7}$ |  |
|               | FedDGM    | $65.6{\scriptstyle \pm 0.1}$ | $66.6{\scriptstyle \pm2.1}$ | $72.5_{\pm0.4}$             | $68.2_{\pm 1.1}$ |  |

vNet, while the best-performing baseline method, FedDM, only achieves  $47.0 \pm 1.8\%$ . This demonstrates a substantial improvement in our algorithm for high-resolution data. Also on ImageWoof, our algorithm achieves 72.7±0.6% on ResNet34, exceeding the next best method by over 10% improvement. *This significant improvement highlights its strong generalization capacity across diverse model architectures.* To facilitate ease of comparison, Fig. [6](#page-8-0) depicts the average results across architectures, as presented in the last column of Table [3.](#page-7-2)

Convergence Rate. Fig. [7](#page-8-1) illustrates the relationship between test accuracy and communication rounds for our Fed-DGM and baseline methods on CIFAR-10 across different model architectures. The data partitioning distribution is  $Dir_{10}(0.5)$ . Firstly, we can observe that our algorithm converges significantly faster than baseline methods on ConvNet, ResNet, and VGG11. It also converges faster than FedDM on ViT and has a comparable convergence speed with other baseline methods. In addition, we can observe that data distillation-based methods consistently exhibit a better convergence rate compared to aggregationbased methods, except when the global model is ViT. *This*

<span id="page-8-0"></span>Image /page/8/Figure/0 description: A bar chart displays the test accuracy (%) for different subsets: ImMeow, ImWoof, ImFood, and ImMisc. Each subset has five bars representing FedAvg (red), FedProx (cyan), FedNova (olive), FedDM (grey), and FedDGM (pink). For ImMeow, the accuracies are approximately 41%, 44%, 41%, 55%, and 60%. For ImWoof, they are around 31%, 32%, 31%, 54%, and 64%. For ImFood, the accuracies are about 39%, 41%, 41%, 55%, and 56%. Finally, for ImMisc, the accuracies are approximately 48%, 50%, 51%, 67%, and 69%. Error bars are present for each bar, indicating variability.

Figure 6. FedDGM is architecture-agnostic. Average accuracy across global models with different architectures on various ImageNet subsets. FedDGM outperforms all baseline methods across diverse architectures.

*is because the global model is trained directly on synthetic data, rather than being obtained through the aggregation of local model parameters or model updates.* The prior knowledge from deep generative models contributes to FedDGM having a better convergence rate compared to FedDM.

<span id="page-8-1"></span>Image /page/8/Figure/3 description: This image contains four plots, each showing the test accuracy (%) versus communication round for different federated learning algorithms (FedAvg, FedProx, FedNova, FedDM, FedDGM) applied to different neural network architectures (ConvNet, ResNet, VGG11, ViT). The x-axis for all plots represents the communication round, ranging from 0 to 20. The y-axis represents the test accuracy in percentage. For ConvNet, ResNet, and VGG11, the y-axis ranges from 0% to 70%. For ViT, the y-axis ranges from 25% to 55%. Each plot displays multiple lines, each representing a different algorithm, with shaded regions indicating the variance. In the ConvNet plot, FedDGM and FedDM achieve the highest accuracy, reaching around 68% by round 20, while FedNova and FedProx are close behind, and FedAvg is slightly lower. The ResNet plot shows FedDGM and FedDM again performing best, reaching approximately 72% and 70% respectively by round 20, with other algorithms performing lower. The VGG11 plot shows FedDGM and FedDM achieving the highest accuracy, around 68-70% by round 20, with the other algorithms performing similarly and slightly lower. The ViT plot shows FedAvg, FedProx, and FedNova achieving similar accuracies around 54-55% by round 20, while FedDM and FedDGM are slightly lower, around 50-52%.

Figure 7. FedDGM demonstrates better performance and faster convergence rates compared to other baselines. The relationship between test accuracy and communication rounds on CIFAR-10.

### 5.3. Impact of Hyperparameters

Impact of Different Latent Spaces. As mentioned earlier in Section [3.3.2,](#page-4-0) data distilled from different latent spaces may exhibit noticeable differences. While keeping the data partitioning fixed, we randomly select a client and show the synthetic data belonging to the "dog" class generated through data distillation using six different latent spaces, as in Fig. [8.](#page-8-2) We can observe that using the latent space corresponding to an earlier layer tends to make the distilled data more realistic. To investigate which latent space results in better data generalization, we experimented with these six different latent spaces of StyleGAN-XL on CIFAR-10, and the corresponding global model performance is shown in Fig. [9.](#page-8-3) We can observe that using the F5 space results in the best generalization of distilled synthetic data across different architectures. Additionally, except for the F9 space, the performance difference is relatively small when parameterizing the synthetic data in other latent spaces and consistently outperforms the baseline methods.

<span id="page-8-2"></span>Image /page/8/Picture/8 description: This is a grid of 40 images, each displaying a different dog. The images vary in quality and clarity, with some showing full body shots and others focusing on the dogs' faces. The dogs are of various breeds and colors, and some are in outdoor settings while others appear to be indoors. The overall impression is a collection of generated or sampled dog images, likely from a machine learning model.

Figure 8. Illustrations of distilled data generated from different latent spaces. Each row corresponds to a distinct latent space, arranged from top to bottom: F0, F3, F4, F5, F6, and F9. Employing latent spaces associated with earlier layers generally results in more realistic distilled data (i.e., those images at the top rows).

<span id="page-8-3"></span>Image /page/8/Figure/10 description: A bar chart displays the test accuracy (%) for different models: ConvNet, ResNet, VG G11, and ViT, along with an average. Each model group has six bars representing different factors (F0, F3, F4, F5, F6, F9), indicated by a legend. The y-axis ranges from 50% to 75%. For ConvNet, accuracies are approximately 70.5%, 71%, 69.5%, 70.5%, 70%, and 61%. For ResNet, accuracies are around 73.5%, 74%, 73%, 73.5%, 72.5%, and 63%. For VG G11, accuracies are about 69.5%, 69.5%, 69.5%, 70%, 69%, and 60.5%. For ViT, accuracies are roughly 55%, 55.5%, 56%, 56.5%, 54.5%, and 52.5%. The average accuracies are approximately 67%, 67.5%, 67%, 67.5%, 66%, and 59%.

Figure 9. Impact of distilling data in different latent spaces. Except for the F9 space, the performance difference among the other latent spaces is minimal. By default, we utilize the F5 space.

Impact of Different IPCs. To analyze the impact of different IPC values on FedDGM, we conduct experiments to test the performance of different global model architectures with IPC values of 1, 5, 10, and 20 on CIFAR-10, and the results are presented in Fig. [10.](#page-9-0) We can observe that as the IPC value increases, the test accuracy of global models with different architectures significantly improves. In particular, when IPC is set to 5, the performance of FedDGM is

comparable to aggregation-based baseline methods. When IPC is greater than or equal to 10, FedDGM outperforms all baseline methods. Considering the trade-off between global model performance and the computational cost on the server, we set the IPC value to 10.

<span id="page-9-0"></span>Image /page/9/Figure/1 description: A bar chart displays the test accuracy (%) for different models (ConvNet, ResNet, VGG11, ViT, and Average) across four IPC (Inter-Process Communication) settings: IPC 1, IPC 5, IPC 10, and IPC 20. The bars show a general trend of increasing test accuracy with higher IPC values for all models. For ConvNet, accuracies are approximately 52% (IPC 1), 67% (IPC 5), 71% (IPC 10), and 74% (IPC 20). ResNet shows accuracies around 51% (IPC 1), 68% (IPC 5), 74% (IPC 10), and 78% (IPC 20). VGG11 has accuracies of about 50% (IPC 1), 67% (IPC 5), 70% (IPC 10), and 73% (IPC 20). ViT's accuracies are approximately 39% (IPC 1), 52% (IPC 5), 56% (IPC 10), and 62% (IPC 20). The average accuracies are around 49% (IPC 1), 65% (IPC 5), 68% (IPC 10), and 71% (IPC 20). Error bars are present on each bar, indicating variability in the data.

Figure 10. As the value of IPC increases, the test accuracy across different global model architectures significantly improves. Impact of different IPCs. By default, we set IPC to 10.

Impact of Different Architectures of the Local Surrogate Model. To demonstrate consistent performance of Fed-DGM when clients' local surrogate models have different architectures, we select a ConvNet with 2, 3, and 4 layers as the local surrogate model, respectively, and the results are depicted in Fig. [11.](#page-9-1) We observe that FedDGM consistently outperforms FedDM across different architectures. Particularly, the cross-architecture performance of FedDGM remains nearly unaffected by the local surrogate model's architecture. As the architecture of the local surrogate model becomes simpler, the performance of FedDM deteriorates. Notably, when the local surrogate model is only a 2-layer ConvNet, FedDGM's test accuracy on different global models significantly surpasses FedDM. Furthermore, using local surrogate models of a smaller size can further reduce computational costs on both the server and client sides, and significantly decreases communication costs compared to aggregation-based baseline FL methods.

<span id="page-9-1"></span>Image /page/9/Figure/4 description: This figure contains three bar charts, labeled (a) 2 Layers, (b) 3 Layers, and (c) 4 Layers. Each bar chart displays the test accuracy (%) on the y-axis for different models (ConvNet, ResNet, VGG11, ViT) on the x-axis. Within each model category, there are two bars representing FedDGM (yellow) and FedDM (purple). In chart (a), FedDGM accuracies are approximately 67%, 74%, 70%, and 55% for ConvNet, ResNet, VGG11, and ViT, respectively. FedDM accuracies are around 64%, 66%, 65%, and 52%. In chart (b), FedDGM accuracies are about 69%, 73%, 70%, and 55%, while FedDM accuracies are approximately 68%, 71%, 68%, and 53%. In chart (c), FedDGM accuracies are roughly 71%, 73%, 70%, and 56%, and FedDM accuracies are around 69%, 72%, 69%, and 51%.

Figure 11. FedDGM consistently outperforms its main competitor, FedDM across various local surrogate model architectures. Impact of different architectures of the local surrogate model. We choose a ConvNet with 2 layers, 3 layers, and 4 layers, respectively.

Impact of Different Local Training Epochs. Fig. [12](#page-9-2)

shows the results when clients train local surrogate models with different numbers of local epochs  $T_l$  using their local real data. Interestingly, we can observe that as the number of local epochs increases, the mean accuracy of global models does not consistently improve. The global model's performance is best when the number of local epochs is set to 20, and deviating from this value, either higher or lower, results in a relatively small decrease in performance.

When the number of local epochs is small, local surrogate models are updated less frequently, leading to a slower convergence rate in the overall FL training process. On the other hand, when the number of local epochs increases, it introduces a larger gap between the post-training local surrogate model parameters  $\theta_m^{(t+1)}$  and the global model parameters  $\theta_g^{(t)}$  from the previous communication round. This increased gap makes it more challenging for the server to apply matching training trajectories to distill data. As a result, when the distillation step  $T_d$  remains constant, a larger number of local epochs can actually lead to a decrease in the quality of the distilled data.

<span id="page-9-2"></span>Image /page/9/Figure/9 description: A bar chart displays the test accuracy (%) for different models: ConvNet, ResNet, VGG11, and ViT, as well as an average. Each model has four bars representing local epochs of 5, 10, 20, and 30. The bars for ConvNet show accuracies around 69.5%, 70.5%, 70.5%, and 71% for epochs 5, 10, 20, and 30 respectively. ResNet's accuracies are approximately 72%, 73%, 74%, and 73.5%. VGG11 shows accuracies of about 68%, 69.5%, 70%, and 69.5%. ViT has accuracies around 55%, 55.5%, 55.5%, and 55.5%. The average accuracy across all models is approximately 72.5%, 73.5%, 74%, and 73.5%. Error bars are present for each bar, indicating variability. The y-axis ranges from 50% to 75%.

Figure 12. Test accuracy experiences a slight decrease when utilizing very high or very low local epochs. Impact of different numbers of local epochs. When the number of local epochs is 20, FedDGM achieves its best cross-architecture performance. Deviating from this value, whether greater or lesser than 20, results in a slight decrease in test accuracy.

Dataset Specifications. Our subsets are derived from ImageNet ILSVRC2012. For each subset, we select 10 classes with similar features from the 1000 classes in ImageNet. The training set of each ImageNet subset is composed of all the training images belonging to these 10 classes, while the validation set consists of all the validation images from the same classes. Table [4](#page-10-16) shows the specific categories that make up each subset.

# 6. Conclusion

This paper introduces a server-side federated learning (FL) framework that leverages pre-trained deep generative models for efficient and privacy-enhanced training. This approach reduces computational demands on local devices, enabling smaller local models and facilitating the training

Table 4. Categories in ImageNet subsets

<span id="page-10-16"></span>

| Subset  | 0                     | 1                 | 2              | 3              | 4               | 5                   | 6                      | 7                 | 8                   | 9                   |
|---------|-----------------------|-------------------|----------------|----------------|-----------------|---------------------|------------------------|-------------------|---------------------|---------------------|
| ImMeow  | Tabby<br>Cat          | Bengal<br>Cat     | Persian<br>Cat | Siamese<br>Cat | Egyptian<br>Cat | Lion                | Tiger                  | Jaguar            | Snow<br>Leopard     | Lynx                |
| ImWoof  | Australian<br>Terrier | Border<br>Terrier | Samoyed        | Beagle         | Shih-Tzu        | English<br>Foxhound | Rhodesian<br>Ridgeback | Dingo             | Golden<br>Retriever | English<br>Sheepdog |
| ImFruit | Pineapple             | Banana            | Strawberry     | Orange         | Lemon           | Pomegranate         | Fig                    | Bell<br>Pepper    | Cucumber            | Green<br>Apple      |
| ImFood  | Cheeseburger          | Hot-dog           | Pretzel        | Pizza          | French Loaf     | Ice-cream           | Guacamole              | Carbonara         | Bagel               | Trifle              |
| ImMisc  | Bubble                | Piggy<br>Bank     | Stoplight      | Coil           | Kimono          | Cello               | Combination<br>Lock    | Triumphal<br>Arch | Fountain            | Spaghetti<br>Squash |

of a larger global model on the server. Theoretical analysis shows an asymptotic resemblance to centralized training on a heterogeneous dataset. Empirical results demonstrate up to a 40% accuracy improvement over non-datasetdistillation techniques in highly heterogeneous FL contexts, outperforming existing methods by 18%. Notably, our framework achieves around a 10% performance increase on high-resolution image datasets and exhibits faster convergence.

### References

- <span id="page-10-0"></span>[1] Saeed Vahidian, Mahdi Morafah, and Bill Lin. Personalized federated learning by structured and unstructured pruning under data heterogeneity. In *2021 IEEE 41st International Conference on Distributed Computing Systems Workshops (ICDCSW)*, pages 27–34, 2021. [1](#page-0-0)
- <span id="page-10-1"></span>[2] Avishek Ghosh, Jichan Chung, Dong Yin, and Kannan Ramchandran. An efficient framework for clustered federated learning. *arXiv preprint arXiv:2006.04088*, 2020. [1](#page-0-0)
- [3] Saeed Vahidian, Mahdi Morafah, Weijia Wang, Vyacheslav Kungurtsev, Chen Chen, Mubarak Shah, and Bill Lin. Efficient distribution similarity identification in clustered federated learning via principal angles between client data subspaces. https://arxiv.org/abs/2209.10526, 2022.
- <span id="page-10-2"></span>[4] Umar Khalid, Hasan Iqbal, Saeed Vahidian, Jing Hua, and Chen Chen. CEFHRI: A communication efficient federated learning framework for recognizing industrial human-robot interaction. *CoRR*, abs/2308.14965, 2023. [1](#page-0-0)
- <span id="page-10-3"></span>[5] Tian Li, Anit Kumar Sahu, Manzil Zaheer, Maziar Sanjabi, Ameet Talwalkar, and Virginia Smith. Federated optimization in heterogeneous networks. *arXiv preprint arXiv:1812.06127*, 2018. [1,](#page-0-0) [6](#page-5-1)
- <span id="page-10-4"></span>[6] Saeed Vahidian, Sreevatsank Kadaveru, Woonjoon Baek, Weijia Wang, Vyacheslav Kungurtsev, Chen Chen, Mubarak Shah, and Bill Lin. When do curricula work in federated learning? volume abs/2212.12712, 2022. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-10-5"></span>[7] Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. Feddm: Iterative distribution matching for communication-efficient federated learning. 2022. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [6](#page-5-1)

- <span id="page-10-6"></span>[8] Qinbin Li, Bingsheng He, and Dawn Song. Model-Contrastive Federated Learning, March 2021. arXiv:2103.16257 [cs]. [2](#page-1-1)
- <span id="page-10-7"></span>[9] Tian Li, Anit Kumar Sahu, Manzil Zaheer, Maziar Sanjabi, Ameet Talwalkar, and Virginia Smith. Federated Optimization in Heterogeneous Networks, April 2020. arXiv:1812.06127 [cs, stat]. [2](#page-1-1)
- <span id="page-10-8"></span>[10] Jianyu Wang, Qinghua Liu, Hao Liang, Gauri Joshi, and H. Vincent Poor. Tackling the Objective Inconsistency Problem in Heterogeneous Federated Optimization, July 2020. arXiv:2007.07481 [cs, stat]. [2,](#page-1-1) [6](#page-5-1)
- <span id="page-10-9"></span>[11] Mikhail Yurochkin, Mayank Agarwal, Soumya Ghosh, Kristjan Greenewald, Trong Nghia Hoang, and Yasaman Khazaeni. Bayesian Nonparametric Federated Learning of Neural Networks, May 2019. arXiv:1905.12022 [cs, stat]. [2](#page-1-1)
- <span id="page-10-10"></span>[12] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In Marina Meila and Tong Zhang, editors, *Proceedings of the 38th International Conference on Machine Learning, ICML 2021, 18-24 July 2021, Virtual Event*, volume 139 of *Proceedings of Machine Learning Research*, pages 12674–12685. PMLR, 2021. [2,](#page-1-1) [3](#page-2-1)
- <span id="page-10-11"></span>[13] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching, 2021. [2,](#page-1-1) [3,](#page-2-1) [4](#page-3-1)
- <span id="page-10-12"></span>[14] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth O. Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *Proceedings of the 37th International Conference on Machine Learning, ICML 2020, 13-18 July 2020, Virtual Event*, volume 119 of *Proceedings of Machine Learning Research*, pages 9206–9216. PMLR, 2020. [2](#page-1-1)
- <span id="page-10-13"></span>[15] Shengyuan Hu, Jack Goetz, Kshitiz Malik, Hongyuan Zhan, Zhe Liu, and Yue Liu. Fedsynth: Gradient compression via synthetic data in federated learning. *CoRR*, abs/2204.01273, 2022. [2](#page-1-1)
- <span id="page-10-14"></span>[16] Chun-Yin Huang, Ruinan Jin, Can Zhao, Daguang Xu, and Xiaoxiao Li. Federated virtual learning on heterogeneous data with local-global distillation, 2023. [2](#page-1-1)
- <span id="page-10-15"></span>[17] Mohsen Joneidi, Saeed Vahidian, Ashkan Esmaeili, Weijia Wang, Nazanin Rahnavard, Bill Lin, and Mubarak Shah. Select to better learn: Fast and accurate deep learning using data selection from nonlinear manifolds. *Computer Vision*

*and Pattern Recognition, CVPR 2020. IEEE Conference on*, 2020. [3](#page-2-1)

- <span id="page-11-0"></span>[18] Sariel Har-Peled and Soham Mazumdar. On coresets for kmeans and k-median clustering. In *ACM Symposium on Theory of Computing*, pages 291–300. ACM, 2004. [3](#page-2-1)
- <span id="page-11-1"></span>[19] Michael B Cohen, Cameron Musco, and Christopher Musco. Input sparsity time low-rank approximation via ridge leverage score sampling. In *ACM-SIAM SODA*, pages 1758–1777. SIAM, 2017. [3](#page-2-1)
- <span id="page-11-2"></span>[20] Saeed Vahidian, Mohsen Joneidi, Ashkan Esmaeili, Siavash Khodadadeh, Sharare Zehtabian, and Bill Lin. Spectrum pursuit with residual descent for column subset selection problem: Theoretical guarantees and applications in deep learning. *IEEE Access*, 10:88164–88177, 2022. [3](#page-2-1)
- <span id="page-11-3"></span>[21] Pankaj K Agarwal, Sariel Har-Peled, and Kasturi R Varadarajan. Approximating extent measures of points. *Journal of the ACM*, 2004. [3](#page-2-1)
- [22] Saeed Vahidian, Baharan Mirzasoleiman, and Alexander Cloninger. Coresets for estimating means and mean square error with limited greedy samples. In *Proceedings of the Thirty-Sixth Conference on Uncertainty in Artificial Intelligence, UAI 2020, virtual online, August 3-6, 2020*, volume 124, pages 350–359, 2020.
- <span id="page-11-4"></span>[23] Mu Li, Gary L Miller, and Richard Peng. Iterative row sampling. In *IEEE FoCS*, pages 127–136. IEEE, 2013. [3](#page-2-1)
- <span id="page-11-5"></span>[24] Trevor Campbell and Tamara Broderick. Bayesian coreset construction via greedy iterative geodesic ascent. In *ICML*, 2018. [3](#page-2-1)
- <span id="page-11-6"></span>[25] Mario Lucic, Matthew Faulkner, Andreas Krause, and Dan Feldman. Training gaussian mixture models at scale via coresets. *JMLR*, 18(1):5885–5909, 2017. [3](#page-2-1)
- <span id="page-11-7"></span>[26] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *CoRR*, abs/1811.10959, 2018. [3](#page-2-1)
- <span id="page-11-8"></span>[27] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *International Joint Conference on Neural Networks, IJCNN 2021, Shenzhen, China, July 18-22, 2021*, pages 1–8. IEEE, 2021. [3](#page-2-1)
- <span id="page-11-9"></span>[28] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition, CVPR 2022, New Orleans, LA, USA, June 18-24, 2022*, pages 10708–10717. IEEE, 2022. [3,](#page-2-1) [6](#page-5-1)
- <span id="page-11-10"></span>[29] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior, 2023. [3,](#page-2-1) [5,](#page-4-1) [6](#page-5-1)
- <span id="page-11-11"></span>[30] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. Communicationefficient learning of deep networks from decentralized data. In *Artificial intelligence and statistics*, pages 1273–1282. PMLR, 2017. [3,](#page-2-1) [6](#page-5-1)
- <span id="page-11-12"></span>[31] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023. [4](#page-3-1)

- <span id="page-11-13"></span>[32] Mark J Schervish and Bradley P Carlin. On the convergence of successive substitution sampling. *Journal of Computational and Graphical statistics*, 1(2):111–127, 1992. [6](#page-5-1)
- <span id="page-11-14"></span>[33] Daniel W Stroock. *An introduction to Markov processes*, volume 230. Springer Science & Business Media, 2013. [6](#page-5-1)
- <span id="page-11-15"></span>[34] Fastai. Fastai/imagenette: A smaller subset of 10 easily classified classes from imagenet, and a little more french. [6](#page-5-1)
- <span id="page-11-16"></span>[35] Mahdi Morafah, Saeed Vahidian, Weijia Wang, and Bill Lin. Flis: Clustered federated learning via inference similarity for non-iid data distribution. *IEEE Open Journal of the Computer Society*, 4:109–120, 2023. [6](#page-5-1)
- <span id="page-11-17"></span>[36] Mahdi Morafah, Saeed Vahidian, Weijia Wang, and Bill Lin. Flis: Clustered federated learning via inference similarity for non-iid data distribution. *IEEE Open Journal of the Computer Society*, 4:109–120, 2023. [6](#page-5-1)
- <span id="page-11-18"></span>[37] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4367–4375, 2018. [7](#page-6-4)
- <span id="page-11-19"></span>[38] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [7](#page-6-4)
- <span id="page-11-20"></span>[39] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [7](#page-6-4)
- <span id="page-11-21"></span>[40] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020. [7](#page-6-4)
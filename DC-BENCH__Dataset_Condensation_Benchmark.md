# DC-BENCH: Dataset Condensation Benchmark

<PERSON><sup>1</sup>, <PERSON><PERSON><PERSON><sup>1</sup>, <PERSON><sup>2</sup>, <PERSON><PERSON><PERSON><PERSON><sup>1</sup> <sup>1</sup>Department of Computer Science, UCLA, <sup>2</sup>Google Research {justincui, ruocwang}@ucla.edu <EMAIL> <EMAIL>

# Abstract

Dataset Condensation is a newly emerging technique aiming at learning a tiny dataset that captures the rich information encoded in the original dataset. As the size of datasets contemporary machine learning models rely on becomes increasingly large, condensation methods become a prominent direction for accelerating network training and reducing data storage. Despite numerous methods have been proposed in this rapidly growing field, evaluating and comparing different condensation methods is non-trivial and still remains an open issue. The quality of condensed dataset are often shadowed by many critical contributing factors to the end performance, such as data augmentation and model architectures. The lack of a systematic way to evaluate and compare condensation methods not only hinders our understanding of existing techniques, but also discourages practical usage of the synthesized datasets. This work provides the first large-scale standardized benchmark on Dataset Condensation. It consists of a suite of evaluations to comprehensively reflect the generability and effectiveness of condensation methods through the lens of their generated dataset. Leveraging this benchmark, we conduct a large-scale study of current condensation methods, and report many insightful findings that open up new possibilities for future development. The benchmark library, including evaluators, baseline methods, and generated datasets, is open-sourced<sup>[1](#page-0-0)</sup> to facilitate future research and application.

# 1 Introduction

Dataset plays a central role in the performance of machine learning models. With advanced data collection and labeling tools, it becomes easier than ever to construct large scale datasets. The rapidly growing size of contemporary datasets not only posts challenges to data storage and preprocessing, but also makes it increasingly expensive to train machine learning models and design new methods, such as architecture, hyperparameter, and loss function [\[2,](#page-10-0) [15,](#page-10-1) [52,](#page-12-0) [5\]](#page-10-2). As a result, data condensation emerges as a promising direction that aims at compressing the original large scale dataset into a small subset of information-rich examples.

In this work, we focus on the newly emerging techniques where the condensed dataset comprises of a set of synthesized samples, learned to matching some statistics to the original dataset or maximizing some utility. In particular, [\[48\]](#page-12-1) proposed a dataset distillation algorithm to learn the synthesized dataset via bi-level optimization, showing outstanding performance than existing data selection methods under lower compression ratio<sup>[2](#page-0-1)</sup>. After that, many data condensation methods have been proposed to construct synthesized datasets based on various objectives, such as matching gradients [\[55,](#page-12-2) [53\]](#page-12-3), embeddings [\[54\]](#page-12-4), model parameters [\[4\]](#page-10-3), and kernel ridge regression [\[32,](#page-11-0) [33\]](#page-11-1). It has been reported that all these data-synthesis methods significantly outperform classical data-selection methods.

<span id="page-0-0"></span><sup>&</sup>lt;sup>1</sup>the benchmark is open sourced at [https://github.com/justincui03/dc\\_benchmark](https://github.com/justincui03/dc_benchmark)

<span id="page-0-1"></span> $2$ compression ratio = compressed dataset size / full dataset size

<sup>36</sup>th Conference on Neural Information Processing Systems (NeurIPS 2022).

Despite recent efforts in improving dataset condensation, evaluating and comparing different methods is non-trivial and still remains an open issue to date. Previous works [\[55,](#page-12-2) [54,](#page-12-4) [53,](#page-12-3) [4\]](#page-10-3) mainly evaluate condensation methods by training a randomly initialized model on the condensed dataset and report its test accuracy. During this process, many factors could come into play on the resulting performance, such as data augmentation and architecture. These factors are orthogonal to the condensation algorithms, but might significantly alter their performance. However, there does not exist a unified evaluation protocol among prior works that aligns them. Moreover, the relative performance of condensation methods on real-world downstream applications are also rarely discussed. The lack of a systematic way of evaluation prevents us from establishing fair and thorough comparisons of condensation methods, thereby hinders our understanding of existing algorithms and discourages their practical applications.

This work aims to provide the first benchmark to systematically evaluate data condensation methods through the lens of the condensed dataset. We start by asking the following question: What attributes should a high-quality condensed dataset possess? We identify three main criteria: 1). Models trained on the condensed dataset should achieve good performance across different training protocols, such as different data augmentations and architectures. 2). The condensed dataset should achieve higher performance above naive baseline (random subset of full dataset) across various compression ratios. 3). The condensed dataset should be able to benefit downstream tasks, such as accelerating Neural Architecture Search (NAS). Inspired by these criteria, we propose to measure the strength of condensation algorithms from the following four aspects: 1). *Performance under different augmentation* 2). *Transferability to different architectures* 3). *Performance under different compression ratio* 4). *Performance on NAS task*. These evaluations constitute the core of our benchmark library.

Leveraging the proposed benchmark, we conduct a large-scale comprehensive empirical analysis of state-of-the-art condensation methods. The collection of methods in our library covers four representative dataset condensation methods: Dataset Condensation with Gradient Matching (DC) [\[55\]](#page-12-2), Differentiable Siamese Augmentation (DSA) [\[53\]](#page-12-3), Distribution Matching (DM) [\[55\]](#page-12-2), and Training Trajectory Matching (TM) [\[4\]](#page-10-3). We further include two data-selection methods in our comparisons: random selection and K-Center - a simple algorithm with strong performance. The experimental results reveal insightful findings on the behavior of condensed dataset, such as:

- Among all existing methods in our comparison, TM demonstrates better performance on the proposed evaluation protocols, followed by DSA which often performs the second best, showing promising progressions in the field. However, TM is not scalable to larger datasets and compression ratio.
- Adding data augmentation to the **evaluation of condensed dataset alone** can significantly boost the performance of all methods.
- Condensation methods are most effective under extremely low compression ratios. As the ratio increases (e.g., CIFAR-10 with 400 images per class), all condensation methods perform similarly to the random selection baseline.
- Despite promising results on training a single model, current condensation methods perform poorly on large-scale real-world tasks, such as Neural Architecture Search and training deep networks beyond the pre-specified architecture. All existing methods encounter similar transferability to the simple K-Center baseline on CIFAR-10 and CIFAR-100.
- Using better data-selection methods (e.g., K-Center) as initialization can drastically improve the convergence speed and performance of condensation methods.

We hope our work could shed lights on deeper understanding of dataset condensation algorithms, designing more comprehensive evaluation, and stimulating future research in advancing the state-ofthe-art methods.

# 2 Related Work

## 2.1 Coreset selection methods

Coreset selection method aim to find a representative subset of original dataset [\[43,](#page-11-2) [17,](#page-10-4) [37,](#page-11-3) [8,](#page-10-5) [36,](#page-11-4) [3,](#page-10-6) [49,](#page-12-5) [38\]](#page-11-5). This line of work have enjoyed rich theoretical investigation and a long history of empirical development, which is worth a separate work. Since our main focus is on dataset synthesis methods, we focus on two commonly used selection-based baselines in dataset condensation literature.

Random Selection One naive method of condensation is to randomly pick data from the original dataset, which serves as the baseline for all condensation methods. The performance of random selection is expected to increase steadily as IPC increases, and reach full accuracy when the size of the subset equals the full dataset.

K-Center [\[49,](#page-12-5) [37,](#page-11-3) [17\]](#page-10-4) Another commonly used selection based coreset method is K-Center where multiple center points of a class are selected based on a distance function  $\mathcal L$  so that the distance between data points and their nearest center point is minimized.

### 2.2 Dataset condensation methods

Dataset condensation methods aims to synthesize a small set of data. When it is used for training, competitive performances can be achieved compared to training with the whole dataset. Below we introduce five representative state-of-the-art methods with each using a different technique.

DC - Dataset Condensation with Gradient Matching [\[55\]](#page-12-2) It proposes to infer the synthetic dataset by matching the optimization trajectory of a model trained on synthetic dataset to that on the original dataset. The optimization trajectory is defined as the gradient direction along SGD steps and the loss function to optimize is shown in Equatio[n1](#page-2-0) where  $S$  is the synthetic dataset, T is the number of iterations,  $T$  is the real dataset and  $\theta$  are model parameters.

<span id="page-2-0"></span>
$$
\min_{\mathcal{S}} E_{\theta_0 \sim P_{\theta_0}} \left[ \sum_{t=0}^{T-1} D(\nabla_{\theta} \mathcal{L}^{\mathcal{S}}(\theta_t), \nabla_{\theta} \mathcal{L}^{\mathcal{T}}(\theta_t)) \right]
$$
(1)

DSA - Dataset Condensation with Differentiable Siamese Augmentation [\[53\]](#page-12-3) DSA proposes to apply Differentiable Siamese Augmentation [\[56\]](#page-12-6) while learning synthetic image, resulting in more informative synthetic images. Similar to the loss function of DC in Equation [1,](#page-2-0) DSA applies  $A$  which is a family of image transformations that preserves the semantics of the input as shown in Equation [2.](#page-2-1)

$$
\min_{\mathcal{S}} D(\nabla_{\theta} \mathcal{L}(\mathcal{A}(\mathcal{S}, \omega^{\mathcal{S}}), \theta_t), \nabla_{\theta} \mathcal{L}(\mathcal{A}(\mathcal{T}, \omega^{\mathcal{T}}), \theta_t))
$$
\n(2)

<span id="page-2-1"></span>DM - Dataset Condensation with Distribution Matching [\[54\]](#page-12-4) Unlike DC and DSA, DM learns condensed dataset by directly matching the output features between real and synthetic samples. The features are acquired from a ConvNet model with randomized weights, which corresponds to data distribution in a randomly projected embedding space. The objective function is shown in Equation [3](#page-2-2) where  $\psi_v$  is a family of parametric functions to map the input into a lower dimensional space and  $\omega \sim \Omega$  is the augmentation parameter.

<span id="page-2-2"></span>
$$
\min_{\mathcal{S}} E_{v \sim P_v, \omega \sim \Omega} \parallel \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \psi_v(\mathcal{A}(x_i, \omega)) - \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \psi_v(\mathcal{A}(x_i, \omega)) \parallel^2
$$
(3)

TM - Dataset Distillation by Matching Training Trajectories [\[4\]](#page-10-3) is a recently proposed condensation method that builds a condensed dataset to match the parameter trajectory of full data set training. It first updates model parameters using Equation [4](#page-2-3) where  $D_{syn}$  is the synthetic dataset

$$
\hat{\theta}_{t+n+1} = \hat{\theta}_{t+n} - \alpha \nabla \ell(\mathcal{A}(D_{syn}); \hat{\theta}_{t+n})
$$
\n(4)

Then it optimizes the following loss shown in Equation [5](#page-2-4) where  $\hat{\theta}_{t+N}$  are the student parameters and  $\theta_{t+M}^*$  are the future expert parameters.

<span id="page-2-4"></span><span id="page-2-3"></span>
$$
\mathcal{L} = \frac{\|\hat{\theta}_{t+N} - \theta_{t+M}^*\|_2^2}{\|\theta_t^* - \theta_{t+M}^*\|_2^2}
$$
(5)

KIP - Dataset Meta-Learning from Kernel Ridge-Regression [\[32,](#page-11-0) [33\]](#page-11-1) It performs the condensation process using a new algorithm called Kernel Inducing Point(KIP) through approximating neural networks with kernel ridge-regression(KRR). The objective function is shown in Equation [6](#page-2-5) where  $K_{UV}$  is the matrix of kernel elements $(K(u, v))_{u \in U, v \in V}$  if U and V are sets.  $(X_s, y_s)$  is the support dataset and  $(X_t, y_t)$  is the target dataset.

<span id="page-2-5"></span>
$$
L(X_s, y_s) = \frac{1}{2} \| y_t - K_{X_t X_s} (K_{X_s X_s} + \lambda I)^{-1} y_s \|_2^2
$$
 (6)

# 2.3 Existing benchmarks

To the best of our knowledge, DC-Bench is the first comprehensive benchmark for dataset synthesis methods. Cross-architecture performance is evaluated in these work [\[54,](#page-12-4) [56,](#page-12-6) [4,](#page-10-3) [33\]](#page-11-1). However, the networks used are either too small or too similar. Neural Architecture Search is performed in [\[55,](#page-12-2) [56,](#page-12-6) [54\]](#page-12-4) with different search spaces and methods and it's missing in [\[4,](#page-10-3) [33\]](#page-11-1). With DC-Bench covering all these aspects and beyond with standardized procedures, we believe it will provide useful insights into existing methods and guide future research directions.

# 3 Dataset Condensation Benchmark

In this section, we will layout the details of our benchmark for evaluating different condensation methods. Section [3.1](#page-3-0) introduces our proposed evaluation protocols. Section [3.2](#page-5-0) explains our implementation details for each method.

In terms of evaluation datasets, we mainly consider three standard image datasets - CIFAR-10, CIFAR-100, and TinyImageNet. These datasets are widely adopted in prior works on dataset condensation. Note that we do not include smaller and simpler datasets such as MNIST [\[25\]](#page-11-6) or FASHION-MNIST [\[50\]](#page-12-7), because the performance of different methods are quite similar on these datasets. CIFAR-10 [\[22\]](#page-11-7) and CIFAR-100 [22] both have 50K training images and 10K testing images from 10 and 100 classes. TinyImageNet [\[24,](#page-11-8) [11\]](#page-10-7) is a subset of the large-scale ImageNet dataset with 200 classes. The training split contains 100K images, and both the validation and test set include 10K images.

<span id="page-3-0"></span>

## 3.1 Evaluation protocol

In this subsection, we will introduce how we measure the performance of a DC algorithm. More specifically, we will evaluate DC algorithms for four aspects: data augmentation, compression ratio, transferability, and performance on the downstream task of Neural Architecture Search.

### 3.1.1 Performance under different data augmentations

To evaluate the quality of a condensed dataset, a straightforward way is to train a randomly initialized model on it and evaluate the performance (test accuracy) of the model. However, even for the same architecture and condensed dataset, the resulting model can perform very differently under different training protocols, such as the choices of data augmentation and optimizers. Since the condensed datasets are usually very small, we observe that data augmentation methods can significantly impact the test accuracy. Therefore, we investigate the performance of existing condensation methods under four different data augmentation strategies, as listed below.

ImagenetAug is a simple manually designed augmentation containing random crop [\[23\]](#page-11-9), random horizontal flip and color jitters [\[23\]](#page-11-9). It is one of the most commonly used strategy for training ImageNet models [\[28,](#page-11-10) [19\]](#page-10-8).

Differentiable Siamese Augmentation (DSA) is a set of augmentation policies designed for improving the data efficiency of Generative Adversarial Networks (GAN) [\[16,](#page-10-9) [35,](#page-11-11) [42,](#page-11-12) [56\]](#page-12-6). The set includes six operations: crop, cutout [\[12\]](#page-10-10), flip, scale, rotate, and color jitters. At each iteration, one policy is sampled and applied to the input image. DSA is later extended to training synthesized images for dataset condensation [\[53\]](#page-12-3).

AutoAugment [\[9\]](#page-10-11) is a strong and widely adopted augmentation strategy discovered using AutoML. The searched polices contains a total 16 data augmentations from the popular PIL image library, plus two additional operations: cutout and sample pairing [\[20\]](#page-10-12). At each iteration, a pair of augmentations are sampled from the whole policy set and applied onto the search. It achieves state-of-the-art accuracy on CIFAR-10, CIFAR-100, SVHN [\[31\]](#page-11-13) and ImageNet (without additional data).

RandAugment [\[10\]](#page-10-13) is another popular method of automated policy search, with a different set of discovered policies than AutoAugment. In practice, both RandAugment and AutoAugment are commonly used for training image classification models. Therefore, we also include RandAugment in our benchmark for a more comprehensive evaluation.

*Metrics summary: Similar to real datasets, synthesis datasets should support various augmentations*

*that are tailored to end users' tasks. We evaluate both average and best cases, which could fit different user needs.*

### 3.1.2 Compression ratios

A critical dimension for evaluating dataset condensation methods is their performance under different compression ratios, measured by the number of synthetic Images allocated Per Class (IPC). IPC is typically set by the user of the dataset, according to practical requirements such as storage budget. One question arising naturally is that will dataset synthesis methods continue to compress information effectively under different IPCs? Since the right amount of data for various scenarios might be different (e.g. larger models often require much more data to train), we expect the user to potentially select a wide range of IPCs in practice. As a result, it is crucial to evaluate condensation methods under various compression ratios and identify their effective ranges. However, previous works mainly adopt three IPCs in their evaluation: 1, 10, 50, corresponding to  $0.02\%$ ,  $0.2\%$ , and  $1\%$  of the training split in CIFAR-10 [\[55,](#page-12-2) [53,](#page-12-3) [54,](#page-12-4) [4\]](#page-10-3); This range is far from comprehensive. In contrast, we evaluate existing condensation methods for up to 1000 IPCs, covering a much wider range of compression ratios. The results under this setting reveal many new insights into the behavior of dataset condensation.

*Metrics summary: As synthesis methods compress info from a large dataset into a small one, we expect a good synthesis method to continue outperforming selection based methods under various compression ratios.*

<span id="page-4-0"></span>

#### 3.1.3 Transferability across architectures

Another dimension of evaluation is on how dataset condensation methods perform across different model architectures. Concretely, a neural network is required to extract statistics from the original and synthetic dataset, and the synthetic dataset is optimized by aligning the extracted information. Therefore, we expect the condensed dataset to perform equally well when it is used to train different architectures. Several previous works [\[55,](#page-12-2) [53,](#page-12-3) [54,](#page-12-4) [4\]](#page-10-3) provide evaluations under this transfer setting; however the evaluations are mainly with on one dataset or under one IPC. Therefore the resulting conclusion might not generalize to different datasets or IPCs.

To have a deeper understanding for the transferability of condensation methods, we propose a comprehensive protocol that evaluates the performance of condensed datasets under five model architectures, three datasets (CIFAR-10, CIFAR-100, and TinyImageNet), and three IPCs (1, 10, 50). The architectures of choice are as follows:

MLP: We first consider a simple MLP architecture. The network includes 3 fully connected layers and the width is set to 128. The number of trainable parameters is around 411K.

ConvNet [\[23,](#page-11-9) [39,](#page-11-14) [40\]](#page-11-15): This is the standard architecture used for both training and evaluating synthetic dataset in previous condensation works. The default network contains three 3x3 convolution layers, each followed by 2x2 average pooling and instance normalization. The hidden embedding size is set to 128. There are around 320K trainable parameters. For TinyImageNet, we increase the number of layers to 4 for improved performance, as suggested in previous work [\[54,](#page-12-4) [4\]](#page-10-3).

ResNet18, ResNet152 [\[18\]](#page-10-14): They are commonly used ResNet architecture with 4/50 residual blocks respectively. Each block contains 2 convolution layers followed by ReLU activation and instance normalization (IN) [\[44\]](#page-11-16). There are 11M/60M trainable parameters in ResNet18/ResNet152.

ViT [\[14\]](#page-10-15): Vision Transformer is a new model architecture that's completely different convolutional networks. It splits an image into fixed-size patches and applies a standard transformer [\[45\]](#page-11-17) encoder on it. ViT achieves competitive results compared to state-of-the-art convolutional networks with far less computational resource. There are around 10M trainable parameters in our implementation of ViT.

*Metrics summary: Similar to real datasets, a high quality synthetic dataset should be able to be used for training models with various architectures.*

### 3.1.4 Neural Architecture Search

One of the most promising application of data condensation methods is accelerating Neural Architecture Search (NAS)  $[26, 47, 51, 27, 7, 34, 46, 6, 19]$  $[26, 47, 51, 27, 7, 34, 46, 6, 19]$  $[26, 47, 51, 27, 7, 34, 46, 6, 19]$  $[26, 47, 51, 27, 7, 34, 46, 6, 19]$  $[26, 47, 51, 27, 7, 34, 46, 6, 19]$  $[26, 47, 51, 27, 7, 34, 46, 6, 19]$  $[26, 47, 51, 27, 7, 34, 46, 6, 19]$  $[26, 47, 51, 27, 7, 34, 46, 6, 19]$  $[26, 47, 51, 27, 7, 34, 46, 6, 19]$ . The goal of NAS is to automatically search for a

top architectures from a vast search space. As a result, the search process typically requiring training and evaluating hundreds or thousands of candidate architectures to obtain their relative performance, which consumes a lot of computation resources [\[57\]](#page-12-10). Since the condensed dataset are much smaller than the original dataset, it can potentially be used to accelerate candidate training for NAS algorithms [\[55\]](#page-12-2). In the ideal case, the condensed dataset, when deployed to training architectures, can accurately reflect the relative strength of architectures. Concretely, the ranking order (Spearman correlation) of models trained on the condensed dataset should match those trained on the original dataset. To effectively evaluate the condensed dataset, we propose to adopt NAS-Bench-201 [\[13\]](#page-10-18), a large-scale benchmark database consisting of the ground-truth performance of 15,625 architectures of relatively large size (17 layers). This is different from previous works [\[55\]](#page-12-2) that only experimented with toy search space made of 720 simple ConvNet architectures.

*Metrics summary: With the primary goal of helping model development by accelerating training, a high quality synthetic dataset should preserve model ranking in Neural Architecture Search task.*

<span id="page-5-0"></span>

### 3.2 Implementation details

#### 3.2.1 Method of selection

We include 5 state of the art dataset condensation methods: DC, DSA, DM, TM and KIP and 2 baselines in condensation literature: random selection and K-Center.

Random Selection For random selection baseline, we uniformly sample a fixed number of images per class (IPC) from the original dataset.

K-Center We use similar approach as [\[55\]](#page-12-2) where we train a model on the whole dataset to extract features from each data point and use  $l_2$  distance to compute class centers. However, we just train the model for 1 epoch using the whole dataset. Our results in Table [1](#page-6-0) show that it outperforms all coreset methods used in [\[55,](#page-12-2) [54,](#page-12-4) [56,](#page-12-6) [4\]](#page-10-3) under almost all settings.

DC, DSA, DM, TM We use the default settings provided by the authors. The only change we made for DC and DSA is that when generating larger IPCs, we update the synthetic dataset per class instead of updating all classes at once as suggested by the author.

KIP Since KIP's code is not released, we use the released dataset by the author which are generated under the settings of ZCA preprocessing and no label learning at iteration 1000.

#### 3.2.2 Combining dataset selection with condensation methods

All dataset condensation methods require initializing the synthetic data with either random noise or selected real images. This posts a natural way of combining dataset selection with condensation methods to initialize condensed dataset with the results from data selection. We experiment with three initialization strategies: using 1). *Random selection*, 2). *K-Center*, and 3). *Gaussian noise*. In contrast to prior finding that observes similar results among different initialization methods, we show that (Section [4.6\)](#page-9-0) initialization plays an important role in accelerating the convergence of Dataset Condensation algorithms.

# 4 Empirical studies

### 4.1 Experimental setup

Following previous works, we use ConvNet (Section [3.1.3\)](#page-4-0) to generate the condensed dataset for all our experiments. We follow the default hyperparameters and training configurations of the considered methods, with two exceptions: 1). For IPCs above 50, we manually set and tune the number of iterations for outer and inner optimization for DC and DSA, as they are undefined for large IPCs in the original papers. 2). TM sometimes applies ZCA whitening as a preprocessing step during both synthetic image training and evaluation, and reported mixed results [\[4\]](#page-10-3). Since this is an orthogonal trick that can be applied to any condensation algorithms, we disable it as indicated by the author [\[4\]](#page-10-3) that it helps convergence and is not crucial to the performance. After the condensed dataset is generated, we train 5 randomly initialized network on it for 1000 epochs using SGD optimizer. Due to space limit, we only highlight and discuss the cases that lead to most insightful findings in this section. We refer to readers to the full set of results in the appendix.

<span id="page-6-0"></span>Table 1: Test accuracy for random selection, K-Center, DC, DSA, DM, and TM under different augmentation settings on CIFAR-10, CIFAR-100 and TinyImageNet. The performances without augmentation, best and average performances with augmentation are reported.

<span id="page-6-1"></span>Image /page/6/Figure/1 description: The image contains a table and three scatter plots. The table presents performance data for different datasets (CIFAR10, CIFAR100, TinyImageNet) across various condensation methods (Random, K-Center, DC, DSA, DM, KIP, TM) and augmentation strategies (n/a, avg, best). It also includes a 'Whole Dataset' column. The scatter plots, labeled (a), (b), and (c), visualize testing accuracy (%) against condensation methods for CIFAR10 IPC 10, CIFAR100 IPC 10, and TinyImageNet IPC 10, respectively. Each plot shows data points for different augmentation methods: ImagenetAug, Randaug, Autoaug, DSA, and No aug. The table includes notes indicating that italicized numbers highlight the best accuracy without augmentation, underlined numbers show the best average accuracy under different augmentations, and bold numbers represent the highest accuracy with the best augmentation.

Figure 1: Test accuracy for different methods with different augmentations.

### 4.2 Data augmentation

We start by evaluating dataset condensation methods using our benchmark with five augmentation strategies: DSA, AutoAugment, RandAugment, ImagenetAug, and no augmentation. Note that our empirical study is conducted from the end user perspective: i.e. we treat the generation of condensed dataset as blackbox, and apply different augmentations during the evaluation of the condensed datasets The results are visualized in Figure [1.](#page-6-1) We observe that **applying the right data** augmentation during evaluation significantly improves the performance of model trained on the condensed dataset. For example, on CIFAR-10 and IPC=10, the test accuracy of DC, DSA, DM and TM increases by 6.56%, 9.4%, 5.2% and 10.2% respectively.

One interesting comparison is between DC and DSA. Their only difference is that DSA applies augmentation during both synthetic dataset training and evaluation, whereas DC does not use any augmentation [\[55,](#page-12-2) [53\]](#page-12-3). In our experiment, we found that the performance of DC is largely underestimated, due to misalignment in the evaluation protocol. After applying the best augmentation, DC's performance increases by up to 6%. On the other hand, when data augmentation is disabled during evaluation, the performance of condensed dataset trained by DSA drops to a similar level of DC in many cases.

Although in some cases users may stick with the best augmentation associated with the condensed dataset, there exists cases that user wish to use different augmentations depending on the task at hand. Therefore, we propose to evaluate each method under: 1). no augmentation 2). best augmentation and 3). the average test accuracy under all augmentations. The numerical results are summarized in Table [1.](#page-6-0) We observe that: 1). DSA augmentation overall is the best strategies in our experiments. 2). Some methods exhibit much higher variance across different augmentation. For example, although DSA and TM achieves top 2 performance, they have a much wider accuracy range than DM. This indicates that DSA and TM might potentially overfit to the augmentation used during synthetic dataset learning.

Further, to establish fair comparison, we also reevaluate Data-Selection methods with augmentation enabled. This has been overlooked in many previous works where the baseline data-selection methods they compared against are all evaluated and reported without augmentation. As shown in Table [1,](#page-6-0) similar to Data-Synthesis methods, Data-Selection method also benefits significantly from augmentation. For instance, both random selection and K-Center achieves over 5% absolute gain in test accuracy on all dataset under IPC 50 (except for TinyImageNet which is close: 4.64%). Most noticeably, under 50 IPCs, K-Center even outperforms DC on CIFAR-100 and TinyImageNet, and on-par with DC on CIFAR-10. The impressive performance of K-Center showcases that the potential of selection-based methods is drastically underestimated in previous works.

Key Takeaways 1: Augmentation applied during evaluation alone can drastically increase model performances for both selection and condensation methods. This also causes several previous methods to be largely underestimated.

Key Takeaways 2: Some methods are particularly sensitive to data augmentation(e.g.KIP, TM). Regardless of the augmentations applied during data generation, downstream tasks still have to try out different types of augmentations in order to find the right augmentation that achieves the best results.

### 4.3 Different compression ratios

As previous discussed, prior works mainly evaluate condensed dataset of size up to 50 IPCs (1% compression ratio). However, this is a rather extreme case, and in most practical applications users may be willing to increase the compression ratio to get a more informative subset. To analyze the performance of condensation algorithms under larger compression ratios, we rerun the selected methods with IPC up to 1,000. For fair comparison, DSA augmentation is enabled during evaluation for all base methods, and the synthetic dataset is initialized from random real images.

Ideally, we expect the performance trajectory of condensation methods to approach the oracle accuracy (brown horizontal line) on the full dataset much faster than the random selection. However, this is not the case in practice. As shown in Figure [2,](#page-7-0) the performance gain of dataset condensation methods over selection based methods is only obvious for IPCs less than 200. As IPC increases further, the margin shrinks considerably and all methods perform similarly to the random selection baseline. Considering the fact that the synthetic datasets are initialized from random selection in the first place, it reveals that all current condensation methods fail to effectively explore the increased capacity brought by the extra IPCs. Moreover, although DM [\[54\]](#page-12-4) outperforms DSA [\[53\]](#page-12-3) with IPC 50, it's not able to consistently outperform DSA [\[53\]](#page-12-3) with larger

<span id="page-7-0"></span>Image /page/7/Figure/6 description: The image is a line graph showing the relationship between the number of images per class on the x-axis and testing accuracy (%) on the y-axis. There are five lines representing different condensation methods: Random (blue), K-Center (orange), DC (green), DSA (red), and DM (purple). A sixth line, labeled 'Whole dataset' (brown), represents the baseline. The graph shows that as the number of images per class increases, the testing accuracy generally increases for all methods. The 'Whole dataset' line is consistently the highest, indicating the best performance. The other methods show varying levels of accuracy, with DM and DSA performing better than K-Center, DC, and Random, especially at lower numbers of images per class. The accuracy ranges from approximately 25% to 85%.

Figure 2: Performance comparison with different compression ratios on CIFAR-10 using Random selection, K-Center, DC, DSA and DM.

IPCs which contradicts with the claim made in  $[54]$ . We **do not include TM in the plot as it does** not scale well to IPCs beyond 50 on CIFAR-10, in terms of both memory and run-time. The scalability becomes worse on larger dataset with more classes and higher resolutions. For ease of reference, we also report numerical results in Appendix Table [6.](#page-20-0)

Key Takeaways 1: Condensation methods perform better than selection methods under small IPCs. Key Takeaways 2: When IPC goes above 200, synthesis based methods' performance degrades to that of random selection baselines

## 4.4 Transferability

We evaluate the transferability of different condensation methods on the 3 datasets with IPC 1, 10 and 50 using the 5 architectures described previously. As showing in Figure [3,](#page-8-0) the performance of all synthetic methods drops when transferring to other architectures. Moreover, the relative ranking of different condensed dataset might not necessarily be preserved when transferred to different architectures. For instance, on CIFAR-100, while DC outperforms K-Center on ConvNet, it falls behind on ResNet architecture. Another example is on CIFAR-10, where we see that the dataset learned by the top-notch method, TM, transfers poorly to MLP networks. In addition, we observe that K-Center achieves the better transferability than several condensation methods (Table [2\)](#page-8-1).

One particular observation we want to point out is that none of the condensation methods we tested perform well when transferred to large models like ResNet152. We conjecture that it is because the larger ResNet152 might require more data to optimize than models of much smaller size, such as ConvNet. This is evidenced by the fact that ResNet152 performs worse than any other architecture on random selection with small IPCs. To further verify it, we trained ResNet152

<span id="page-8-0"></span>Image /page/8/Figure/0 description: This image contains three scatter plots side-by-side, each comparing the testing accuracy of different neural network architectures (ConvNet, MLP, ResNet18, ResNet152, ViT) across various condensation methods. The x-axis for all plots is labeled 'Condensation method' and includes categories like Random, K-Center, DC, DSA, DM, KIP, and TM. The y-axis for all plots is labeled 'Testing accuracy(%)'. Plot (a) is for CIFAR10 IPC 10, plot (b) is for CIFAR100 IPC 10, and plot (c) is for TinyImageNet IPC 10. The plots show that generally, ConvNet achieves the highest testing accuracy across most condensation methods and datasets, while ResNet152 consistently shows the lowest accuracy.

Figure 3: Synthetic dataset performance evaluated using different networks.

with 300 randomly selected images per class and obtain an accuracy of 45.59%; When the IPC reaches 1,000, ResNet152 achieves 70.29% accuracy, gradually gaining back its full potential.

This result serves as an extra piece of evidence that a good condensed method should operate robustly under different compression ratios. Please refer to appendix for the numerical results.

Key Takeaways 1: Dataset synthesis methods' performance drops on other architectures.

Key Takeaways 2: None of the condensation methods transfer well to large models such as ResNet152.

Key Takeaways 3: The relative ranking of different methods may not be preserved when transferring to different architectures.

<span id="page-8-1"></span>Table 2: Testing accuracy of different methods on ConvNet versus transferred to other architectures. The "Transfer" column records the average results on MLP, ResNet18, ResNet152 and ViT. All methods are evaluated with 10 IPCs. Results under other IPCs can be found in the Appendix.

|            | CIFAR-10     |              | CIFAR-100    |          | TinyImageNet |             |
|------------|--------------|--------------|--------------|----------|--------------|-------------|
|            | ConvNet      | Transfer     | ConvNet      | Transfer | ConvNet      | Transfer    |
| Random     | 31.00        | 24.16        | 18.64        | 11.31    | 6.88         | 3.53        |
| K-Center   | 41.19        | 31.01        | 25.04        | 15.31    | 11.38        | 5.42        |
| DC         | 50.99        | <b>32.22</b> | 28.42        | 11.95    | 12.83        | 3.74        |
| <b>DSA</b> | 52.96        | 31.15        | 32.23        | 15.77    | 16.34        | 6.75        |
| DM         | 47.64        | 30.66        | 29.23        | 13.59    | 13.51        | 4.08        |
| <b>KIP</b> | 47.23        | 23.54        | 29.04        | 11.76    | -            | -           |
| <b>TM</b>  | <b>63.66</b> | 31.55        | <b>38.18</b> | 16.48    | <b>20.11</b> | <b>6.91</b> |

### 4.5 Neural Architecture Search (NAS)

One promising application of DC is on Neural Architecture Search as shown in [\[55\]](#page-12-2). To evaluate this task, we randomly sample 100 networks from NAS-Bench-201 [\[13\]](#page-10-18), which contains ground-truth performance of 15,625 networks. All models are trained on CIFAR-10 for 50 epochs under 5 random seeds, and ranked according to their average accuracy on a held-out validation set of 10k images.

We reduce the number of repeated blocks from 15 to 3 during the search phase, as we found that original networks perform poorly when trained on condensed dataset due to their size. This is a common practice in NAS [\[28\]](#page-11-10). We measure the performance on task NAS with two metrics: 1). Correlation between the ranking of models trained on condensed dataset and original dataset 2). The ground-truth performance of the best architecture trained on the condensed

<span id="page-8-2"></span>Table 3: Spearman's rank correlation using NAS-Bench-201. The state-of-the-art performance on the test set is 94.36%. The rank correlation of original dataset is lower than 1.0 because we use a small architecture and perform ranking based on validation set.

|             | Random | K-Center | DC    | DSA   | DM    | KIP   | TM    | Original Dataset |
|-------------|--------|----------|-------|-------|-------|-------|-------|------------------|
| Correlation | -0.06  | 0.11     | -0.19 | -0.37 | -0.37 | -0.50 | -0.09 | 0.7487           |
| Top 1 (%)   | 91.9   | 91.78    | 86.44 | 73.54 | 92.16 | 92.91 | 73.54 | 93.5             |

dataset (Top 1). We argue that ranking correlation is more important than Top 1 accuracy, as it measures how well the condensed dataset can reflect the relative strength of various architectures [\[1\]](#page-10-19).

Although prior work on utilizing condensed dataset for NAS reports promising results (0.79 Spearman correlation using DC [\[55\]](#page-12-2)), they mainly consider toy search spaces made of 720 ConvNet architectures. With larger scale NAS benchmark with modern architectures, we have a different observation. As shown in Table [3,](#page-8-2) we found that there is little or even negative correlation between the performance on condensed and full dataset. All methods produce negative correlation except for K-Center. This shows that condensed dataset fails to preserve the true strength of the underlying model. Moreover, the best architecture discovered from DC and DSA's condensed dataset performs poorly on the full dataset. Our result indicates that, despite the performance gain brought by recent condensation methods on training a single specific model, it remains challenging to truly utilize the condensed dataset to guide model designs.

Key Takeaways 1: Although some previous works show promising results on toy networks, none of them are suitable for standardized neural architecture search tasks.

<span id="page-9-1"></span>Image /page/9/Figure/0 description: This image contains three line graphs, labeled (a), (b), and (c), each depicting testing accuracy (%) against training epochs. All three graphs show three lines representing 'Random' (blue with circles), 'KCenter' (orange with stars), and 'Gaussian noise' (green with squares). Graph (a) shows 'DM IPC 1' with testing accuracy ranging from 24% to 29%. Graph (b) shows 'DM IPC 10' with testing accuracy ranging from 46% to 52%. Graph (c) shows 'DM IPC 50' with testing accuracy ranging from 40% to 65%. In all graphs, the shaded areas around the lines represent a confidence interval.

Figure 4: Test accuracy comparison among initializing synthetic images with Random selection, K-Center and Gaussian noise on CIFAR-10 using DM.

Key Takeaways 2: As accelerating model training is one of the major use cases of condensed dataset, we encourage the community to incorporate this standardized NAS task popularized by modern architectures.

<span id="page-9-0"></span>

### 4.6 Combining Data-Selection methods with Data-Synthesis methods

Initialization of synthetic dataset is a relatively unexplored territory. Prior Data-Synthesis methods typically initialize the dataset from either Gaussian noise or randomly selected images. Since advanced selection methods such as K-Center outperforms random selection by a large margin, a natural question is whether condensation methods would benefit from images selected from K-Center. This also serves as a direct way of combining data selection methods with data synthesis methods. We test our hypothesis by using images from K-Center to initialize condensation methods. As shown in Figure [4,](#page-9-1) K-Center initialization not only converges faster, but also achieves improved end performance in some cases. On average, we observe that K-Center only requires about 30% of the computation budget to reach the same level of performance as random selection; The end performance is also 1.3% higher as well. The saving is desirable, especially considering the fact that the condensed dataset usually takes a long time to train (e.g. 15h for DSA under 50 IPCs on CIFAR-10). Due to space limit, we only show the curves of DM in the main text; the plots of other methods can be found in the appendix.

Key Takeaways: Synthetic data initialization plays an crucial role in the convergence and final performance of condensation methods.

# 5 DC-BENCH library

We design and implement an evaluation library that incorporates all the aforementioned protocols. To facilitate the research on dataset condensation, we set up a leaderboard ([https://dc-bench.](https://dc-bench.github.io/) [github.io/](https://dc-bench.github.io/)) to record the performance of existing condensation methods and new submissions. The entire benchmark, including the evaluation library, condensed datasets, and scripts to reproduce the results in this paper, can be found at ([https://github.com/justincui03/dc\\_benchmark](https://github.com/justincui03/dc_benchmark)). Both the leaderboard and the benchmark will be updated regularly to reflect the most recent progress in dataset condensation methods.

# 6 Outlook

Conclusion This paper introduces the first large-scale benchmark on dataset condensation methods. Leveraging the proposed benchmark, we conduct the first comprehensive empirical analysis of existing condensation algorithms. Our study reveals several scenarios where current method can be improved, leading to the following potential research directions: 1). (automated) designing better augmentation that suits the synthetic data, 2). improving the transferability of condensed dataset to other architectures 3). condensation methods for NAS 4). developing condensed methods that perform well for a wide range of compression ratios. and 5) effective ways to combine Data-Selection with Data-Synthesis methods. We hope the proposed benchmark could guide users to choose and evaluate various DC methods and facilitate future developments of advanced data condensation methods. **Limitation and outlook** While the current iteration of our benchmark is comprehensive at the moment, it could become limited in scope as the field advances. In the future, we plan to expand the scope of our DC-Bench to include more architectures, datasets, and downstream tasks. As current methods primarily focus on image classification, we will also incorporate tasks from other modality, such as text, graph, and audio.

# Acknowledgments and Disclosure of Funding

This work is supported in part by NSF under IIS-2008173 and IIS-2048280. CJH is also supported by research awards from Google and the Okawa Foundation.

### References

- <span id="page-10-19"></span>[1] Gabriel Bender et al. "Understanding and Simplifying One-Shot Architecture Search". In: *Proceedings of the 35th International Conference on Machine Learning*. Ed. by Jennifer Dy and Andreas Krause. Vol. 80. Proceedings of Machine Learning Research. Stockholmsmässan, Stockholm Sweden: PMLR, 2018, pp. 550–559. URL: [http://proceedings.mlr.press/](http://proceedings.mlr.press/v80/bender18a.html) [v80/bender18a.html](http://proceedings.mlr.press/v80/bender18a.html).
- <span id="page-10-0"></span>[2] James Bergstra and Yoshua Bengio. "Random search for hyper-parameter optimization." In: *Journal of machine learning research* 13.2 (2012).
- <span id="page-10-6"></span>[3] Francisco M Castro et al. "End-to-end incremental learning". In: *Proceedings of the European conference on computer vision (ECCV)*. 2018, pp. 233–248.
- <span id="page-10-3"></span>[4] George Cazenavette et al. "Dataset distillation by matching training trajectories". In: *arXiv preprint arXiv:2203.11932* (2022).
- <span id="page-10-2"></span>[5] Can Chen et al. "Bidirectional learning for offline infinite-width model-based optimization". In: *arXiv preprint arXiv:2209.07507* (2022).
- <span id="page-10-17"></span>[6] Xiangning Chen et al. "DrNAS: Dirichlet Neural Architecture Search". In: *International Conference on Learning Representations*. 2021. URL: [https://openreview.net/forum?](https://openreview.net/forum?id=9FWas6YbmB3) [id=9FWas6YbmB3](https://openreview.net/forum?id=9FWas6YbmB3).
- <span id="page-10-16"></span>[7] Xin Chen et al. "Progressive differentiable architecture search: Bridging the depth gap between search and evaluation". In: *Proceedings of the IEEE International Conference on Computer Vision*. 2019, pp. 1294–1303.
- <span id="page-10-5"></span>[8] Yutian Chen, Max Welling, and Alex Smola. "Super-samples from kernel herding". In: *Proceedings of the Twenty-Sixth Conference on Uncertainty in Artificial Intelligence*. 2010, pp. 109– 116.
- <span id="page-10-11"></span>[9] Ekin D Cubuk et al. "Autoaugment: Learning augmentation policies from data". In: *arXiv preprint arXiv:1805.09501* (2018).
- <span id="page-10-13"></span>[10] Ekin D Cubuk et al. "Randaugment: Practical automated data augmentation with a reduced search space". In: *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops*. 2020, pp. 702–703.
- <span id="page-10-7"></span>[11] Jia Deng et al. "Imagenet: A large-scale hierarchical image database". In: *2009 IEEE conference on computer vision and pattern recognition*. Ieee. 2009, pp. 248–255.
- <span id="page-10-10"></span>[12] Terrance DeVries and Graham W Taylor. "Improved regularization of convolutional neural networks with cutout". In: *arXiv preprint arXiv:1708.04552* (2017).
- <span id="page-10-18"></span>[13] Xuanyi Dong and Yi Yang. "Nas-bench-201: Extending the scope of reproducible neural architecture search". In: *arXiv preprint arXiv:2001.00326* (2020).
- <span id="page-10-15"></span>[14] Alexey Dosovitskiy et al. "An image is worth 16x16 words: Transformers for image recognition at scale". In: *arXiv preprint arXiv:2010.11929* (2020).
- <span id="page-10-1"></span>[15] Thomas Elsken, Jan Hendrik Metzen, and Frank Hutter. "Neural architecture search: A survey". In: *The Journal of Machine Learning Research* 20.1 (2019), pp. 1997–2017.
- <span id="page-10-9"></span>[16] Ian Goodfellow et al. *Deep learning*. Vol. 1. MIT Press, 2016.
- <span id="page-10-4"></span>[17] Sariel Har-Peled and Akash Kushal. "Smaller Coresets for k-Median and k-Means Clustering Symposium on Computational Geometry". In: *Google Scholar Google Scholar Digital Library Digital Library* (2005).
- <span id="page-10-14"></span>[18] Kaiming He et al. "Deep residual learning for image recognition". In: *Proceedings of the IEEE conference on computer vision and pattern recognition*. 2016, pp. 770–778.
- <span id="page-10-8"></span>[19] Shoukang Hu et al. "Generalizing Few-Shot NAS with Gradient Matching". In: *International Conference on Learning Representations*. 2021.
- <span id="page-10-12"></span>[20] Hiroshi Inoue. "Data augmentation by pairing samples for images classification". In: *arXiv preprint arXiv:1801.02929* (2018).
- <span id="page-10-20"></span>[21] Diederik P Kingma and Jimmy Ba. "Adam: A method for stochastic optimization". In: *arXiv preprint arXiv:1412.6980* (2014).

- <span id="page-11-7"></span>[22] Alex Krizhevsky, Geoffrey Hinton, et al. "Learning multiple layers of features from tiny images". In: (2009).
- <span id="page-11-9"></span>[23] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. "Imagenet classification with deep convolutional neural networks". In: *Advances in neural information processing systems* 25 (2012).
- <span id="page-11-8"></span>[24] Ya Le and Xuan Yang. "Tiny imagenet visual recognition challenge". In: ().
- <span id="page-11-6"></span>[25] Yann LeCun et al. "Gradient-based learning applied to document recognition". In: *Proceedings of the IEEE* 86.11 (1998), pp. 2278–2324.
- <span id="page-11-18"></span>[26] Liam Li and Ameet Talwalkar. *Random Search and Reproducibility for Neural Architecture Search*. 2019. arXiv: [1902.07638 \[cs.LG\]](https://arxiv.org/abs/1902.07638).
- <span id="page-11-19"></span>[27] Chenxi Liu et al. "Progressive Neural Architecture Search". In: *Lecture Notes in Computer Science* (2018), pp. 19–35.
- <span id="page-11-10"></span>[28] Hanxiao Liu, Karen Simonyan, and Yiming Yang. "DARTS: Differentiable Architecture Search". In: *International Conference on Learning Representations*. 2019.
- <span id="page-11-23"></span>[29] Stuart Lloyd. "Least squares quantization in PCM". In: *IEEE transactions on information theory* 28.2 (1982), pp. 129–137.
- <span id="page-11-24"></span>[30] Laurens van der Maaten and Geoffrey E. Hinton. "Visualizing Data using t-SNE". In: *Journal of Machine Learning Research* (2008).
- <span id="page-11-13"></span>[31] Yuval Netzer et al. "Reading digits in natural images with unsupervised feature learning". In: (2011).
- <span id="page-11-0"></span>[32] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. "Dataset Meta-Learning from Kernel Ridge-Regression". In: *International Conference on Learning Representations*. 2020.
- <span id="page-11-1"></span>[33] Timothy Nguyen et al. "Dataset Distillation with Infinitely Wide Convolutional Networks". In: *Advances in Neural Information Processing Systems*. 2021.
- <span id="page-11-20"></span>[34] Hieu Pham et al. "Efficient Neural Architecture Search via Parameters Sharing". In: *Proceedings of the 35th International Conference on Machine Learning*. Ed. by Jennifer Dy and Andreas Krause. Vol. 80. Proceedings of Machine Learning Research. Stockholmsmässan, Stockholm Sweden: PMLR, 2018, pp. 4095–4104.
- <span id="page-11-11"></span>[35] Alec Radford, Luke Metz, and Soumith Chintala. "Unsupervised representation learning with deep convolutional generative adversarial networks". In: *arXiv preprint arXiv:1511.06434* (2015).
- <span id="page-11-4"></span>[36] Sylvestre-Alvise Rebuffi et al. "icarl: Incremental classifier and representation learning". In: *Proceedings of the IEEE conference on Computer Vision and Pattern Recognition*. 2017, pp. 2001–2010.
- <span id="page-11-3"></span>[37] Ozan Sener and Silvio Savarese. "Active Learning for Convolutional Neural Networks: A Core-Set Approach". In: *International Conference on Learning Representations*. 2018.
- <span id="page-11-5"></span>[38] Ozan Sener and Silvio Savarese. "Active learning for convolutional neural networks: A core-set approach". In: *arXiv preprint arXiv:1708.00489* (2017).
- <span id="page-11-14"></span>[39] Karen Simonyan and Andrew Zisserman. "Very deep convolutional networks for large-scale image recognition". In: *arXiv preprint arXiv:1409.1556* (2014).
- <span id="page-11-15"></span>[40] Christian Szegedy et al. "Going deeper with convolutions". In: *Proceedings of the IEEE conference on computer vision and pattern recognition*. 2015, pp. 1–9.
- <span id="page-11-22"></span>[41] Mariya Toneva et al. "An empirical study of example forgetting during deep neural network learning". In: *arXiv preprint arXiv:1812.05159* (2018).
- <span id="page-11-12"></span>[42] Ngoc-Trung Tran et al. "Towards good practices for data augmentation in gan training". In: *arXiv preprint arXiv:2006.05338* 2 (2020), p. 3.
- <span id="page-11-2"></span>[43] Ivor W Tsang et al. "Core vector machines: Fast SVM training on very large data sets." In: *Journal of Machine Learning Research* 6.4 (2005).
- <span id="page-11-16"></span>[44] Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. "Improved texture networks: Maximizing quality and diversity in feed-forward stylization and texture synthesis". In: *Proceedings of the IEEE conference on computer vision and pattern recognition*. 2017, pp. 6924–6932.
- <span id="page-11-17"></span>[45] Ashish Vaswani et al. "Attention is all you need". In: *Advances in neural information processing systems* 30 (2017).
- <span id="page-11-21"></span>[46] Ruochen Wang et al. "RANK-NOSH: Efficient Predictor-Based Architecture Search via Non-Uniform Successive Halving". In: *ICCV*. 2021.

- <span id="page-12-8"></span>[47] Ruochen Wang et al. "Rethinking Architecture Selection in Differentiable NAS". In: *International Conference on Learning Representation*. 2021.
- <span id="page-12-1"></span>[48] Tongzhou Wang et al. "Dataset distillation". In: *arXiv preprint arXiv:1811.10959* (2018).
- <span id="page-12-5"></span>[49] Gert W Wolf. "Facility location: concepts, models, algorithms and case studies. Series: Contributions to Management Science". In: *International Journal of Geographical Information Science* 25.2 (2011), pp. 331–333.
- <span id="page-12-7"></span>[50] Han Xiao, Kashif Rasul, and Roland Vollgraf. "Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms". In: *arXiv preprint arXiv:1708.07747* (2017).
- <span id="page-12-9"></span>[51] Quanming Yao et al. "Efficient Neural Architecture Search via Proximal Iterations". In: *AAAI*. 2020.
- <span id="page-12-0"></span>[52] Chris Ying et al. "Nas-bench-101: Towards reproducible neural architecture search". In: *International Conference on Machine Learning*. PMLR. 2019, pp. 7105–7114.
- <span id="page-12-3"></span>[53] Bo Zhao and Hakan Bilen. "Dataset condensation with differentiable siamese augmentation". In: *International Conference on Machine Learning*. PMLR. 2021, pp. 12674–12685.
- <span id="page-12-4"></span>[54] Bo Zhao and Hakan Bilen. "Dataset Condensation with Distribution Matching". In: *arXiv preprint arXiv:2110.04181* (2021).
- <span id="page-12-2"></span>[55] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. "Dataset Condensation with Gradient Matching". In: *International Conference on Learning Representations*. 2020.
- <span id="page-12-6"></span>[56] Shengyu Zhao et al. "Differentiable augmentation for data-efficient gan training". In: *Advances in Neural Information Processing Systems* 33 (2020), pp. 7559–7570.
- <span id="page-12-10"></span>[57] Barret Zoph and Quoc V. Le. "Neural Architecture Search with Reinforcement Learning". In: *International Conference on Learning Representations (ICLR)*. 2017. URL: [https://arxiv.](https://arxiv.org/abs/1611.01578) [org/abs/1611.01578](https://arxiv.org/abs/1611.01578).

# Checklist

- 1. For all authors...
  - (a) Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope? [Yes]
  - (b) Did you describe the limitations of your work? [Yes]
  - (c) Did you discuss any potential negative societal impacts of your work? [Yes]
  - (d) Have you read the ethics review guidelines and ensured that your paper conforms to them? [Yes]
- 2. If you are including theoretical results...
  - (a) Did you state the full set of assumptions of all theoretical results? [N/A]
  - (b) Did you include complete proofs of all theoretical results? [N/A]
- 3. If you ran experiments...
  - (a) Did you include the code, data, and instructions needed to reproduce the main experimental results (either in the supplemental material or as a URL)? [Yes]
  - (b) Did you specify all the training details (e.g., data splits, hyperparameters, how they were chosen)? [Yes]
  - (c) Did you report error bars (e.g., with respect to the random seed after running experiments multiple times)? [Yes] We report the full data in appendix
  - (d) Did you include the total amount of compute and the type of resources used (e.g., type of GPUs, internal cluster, or cloud provider)? [Yes] We report these data in appendix section
- 4. If you are using existing assets (e.g., code, data, models) or curating/releasing new assets...
  - (a) If your work uses existing assets, did you cite the creators? [Yes]
  - (b) Did you mention the license of the assets? [Yes] We report the license in appendix
  - (c) Did you include any new assets either in the supplemental material or as a URL? [Yes]
  - (d) Did you discuss whether and how consent was obtained from people whose data you're using/curating? [Yes]
  - (e) Did you discuss whether the data you are using/curating contains personally identifiable information or offensive content? [Yes]
- 5. If you used crowdsourcing or conducted research with human subjects...
  - (a) Did you include the full text of instructions given to participants and screenshots, if applicable? [N/A]
  - (b) Did you describe any potential participant risks, with links to Institutional Review Board (IRB) approvals, if applicable? [N/A]
  - (c) Did you include the estimated hourly wage paid to participants and the total amount spent on participant compensation? [N/A]

# A Appendix

## A.1 Hyperparameters

For all synthesis based methods, we use the default parameters given by the authors in their original paper. E.g. For DC and DSA, we run it for 1000 iterations. The only change we make in order to have them scale up to IPCs larger than 50 is setting inner loop and outer loop to both 10 which are not given by the original authors. For DM, we run the condensation method for 20,000 iterations which is the same as the author. For TM, we use the condensed dataset provided by the author whose exact settings can be found in [\[4\]](#page-10-3). For K-Center, we use the Kmeans implementation inside scikit-learn which is a publicly available library with maximum 300 iterations. In order to generate the embedding features for the image, we create a random ConvNet model and train it with 1 epoch using the whole dataset. For CIFAR10/100, we use a 3 layer ConvNet, for TinyImageNet, we use a 4 layer ConvNet as suggested by [\[54\]](#page-12-4). For the augmentation used to generate the synthesis dataset, we keep it the same as the original author, e.g. no augmentation for DC, DSA augmentation for DSA, DM and TM, ZCA preprocessing for KIP.

### A.2 Data augmentation

In Table [1,](#page-6-0) we report the average and best performance of different methods under different augmentation settings. Here we show the complete numerical results in Table [4](#page-18-0)

### A.3 Transferability

Besides Figure [3](#page-8-0) shown previously which includes the case when IPC equals 10, here we show Figure [5](#page-14-0) that contains IPC 1 and 50 for CIFAR-10, CIFAR-100, TinyImageNet. We also include all the numerical results in Table [5](#page-19-0) for reader's references.

<span id="page-14-0"></span>Image /page/14/Figure/7 description: This image displays a grid of six scatter plots, each comparing the testing accuracy of different neural network architectures (ConvNet, MLP, ResNet18, ResNet152, ViT) across various condensation methods (Random, K-Center, DC, DSA, DM, KIP, TM). The plots are organized into two rows and three columns. The top row shows results for IPC 1 on CIFAR10, CIFAR100, and TinyImageNet datasets, labeled (a), (b), and (c) respectively. The bottom row shows results for IPC 50 on the same datasets, labeled (d), (e), and (f). The y-axis for all plots represents testing accuracy in percentage, with scales varying between the datasets. The x-axis for all plots represents the condensation method.

Figure 5: Condensation method transferability for IPC 1 and 50

### A.4 Combining selection based methods with synthesis based methods

As shown in Figure [4](#page-9-1) for DM, if we combine synthesis based method with better selection method, it will not only converge faster but also achieve better performance. Similar results from DC and DSA can be seen from Figure [6](#page-15-0) on dataset CIFAR-10. We are not able to get the performance of TM under the computation resource limit(Out Of Memory) we set.

<span id="page-15-1"></span>

#### Algorithm 1 K-Center

Randomly initialize a ConvNet model M and train for 1 single epoch Compute the embedding features for each image using model M for i in 1 .. N do Select all the images belonging to class i and assign them to  $S_i$ Randomly select **P** images from  $S_i$  and assign to  $C_i$  as the centers for KM means.

while  $j \leq K$  do

update  $C_i$  based on the  $L_2$  distance in the embedding space.

end while

Based on the center  $C_i$ , get the nearest N images using  $L_2$  distance in the embedding space end for

```
Return \{C_i \mid i = 1..N\}
```

<span id="page-15-0"></span>Image /page/15/Figure/7 description: This figure displays six line graphs, arranged in two rows and three columns, illustrating testing accuracy over training epochs for different configurations. The top row shows graphs labeled (a) DC IPC 1, (b) DC IPC 10, and (c) DC IPC 50. The bottom row shows graphs labeled (d) DSA IPC 1, (e) DSA IPC 10, and (f) DSA IPC 50. Each graph plots testing accuracy on the y-axis against training epoch on the x-axis, ranging from 0 to 1000 epochs. Three lines are present in each graph, representing 'Random' (blue), 'KCenter' (orange), and 'Gaussian noise' (green) methods, with shaded areas indicating confidence intervals. The y-axis scales vary across the graphs, from approximately 20% to 30% for the first column, 36% to 46% for the second column, and 40% to 56% for the third column. The overall trend in all graphs shows an increase in testing accuracy as training progresses, with 'KCenter' and 'Gaussian noise' generally outperforming 'Random' at higher epoch counts.

Figure 6: Test accuracy comparison between initializing synthetic images with Random selection, K-Center and Gaussian noise on CIFAR-10

## A.5 Different compression ratios

Previously, we show the condensation method performances in Figure [2](#page-7-0) with IPC up to 1000, here we show the numeric results in Table [6](#page-20-0) for easier references.

## A.6 Impact of different optimizers

The choice of optimizer (e.g., SGD versus Adam [\[21\]](#page-10-20)) usually has minor impact to the performance according to our experiments. In Table [7,](#page-20-1) we show the results of using SGD optimizer versus Adam optimizer with all IPCs(1, 10, 50) on CIFAR-10. All performances are evaluated with DSA augmentation. We get similar performances with the 2 different optimizers.

## A.7 Computation resources

In order to perform a fair comparison, we run all the experiments on the same virtual machine with 1 NVIDIA A100 GPU with 40GB GPU memory on Google Cloud. If some method is not able to run on the machine within GPU memory limit, we report it as out-of-memory(OOM).

## A.8 Training time

Here we show the training time of each condensation methods in Table [8.](#page-20-2) For K-Center, we iterate 300 times to find the cluster center. The run time is computed by dividing the total time by 300. For

synthesis based methods, we run each method 100 epochs and report the mean and standard deviation of the data.

### A.9 K-Center

Prior condensation works have examined a variety of selection-based methods as the baseline, such as Herding, K-Center, and Forgetting [\[41\]](#page-11-22). But surprisingly, these methods often fail to outperform even the random selection baseline. For instance, K-Center, which pick data based on the centers of KMean [\[29\]](#page-11-23) algorithm, reports 16.4% (absolute difference) lower test accuracy on CIFAR-10 and 50 IPCs than random selection in the original DC paper [\[55\]](#page-12-2). After careful investigation, we find that the reason is probably that the model used to extract features is trained for too many epochs, causing the features to be too close for the data in the same class Our revised implementation leads to a substantial performance gain over both random selection and prior selection methods (Table [1](#page-6-0)[,9\)](#page-16-0). In addition, K-Center can even match the performance of some condensation methods in some cases (Table [1\)](#page-6-0) while being much faster to run. We show the flow of it in Algorithm [1](#page-15-1)

<span id="page-16-0"></span>Table 9: Kmeans test accuracy comparison using raw image feature and embedding feature generated by ConvNet on CIFAR10/100 and TinyImageNet. For CIFAR10/100, a 3-layer ConvNet is used. For TinyImageNet, a 4-layer ConveNet is used.

| Dataset      | IPC | Image raw input | Image Feature embedding |
|--------------|-----|-----------------|-------------------------|
| CIFAR-10     | 1   | 21.86           | 25.16                   |
|              | 10  | 32.21           | 41.49                   |
|              | 50  | 47.00           | 56.00                   |
| CIFAR-100    | 1   | 6.74            | 10.89                   |
|              | 10  | 20.08           | 25.04                   |
|              | 50  | 35.15           | 38.64                   |
| TinyImageNet | 1   | 1.98            | 3.03                    |
|              | 10  | 6.88            | 11.38                   |
|              | 50  | 17.85           | 22.02                   |

### A.10 Distribution bias of synthetic dataset

Since DC methods synthesize a small dataset, one natural question is whether it introduces any bias into the data distribution. Therefore, we plot the data distribution of the real dataset and the synthetic dataset in Figure [7](#page-16-1) for CIFAR-10 with IPC 50. The features are extracted with a ResNet18 model fully trained on the real dataset. ZCA is applied when extracting features for KIP. The figure is visualized using T-SNE [\[30\]](#page-11-24) for the first class. As we can see that the synthetic images learned by DC and DSA are more on the edge of the distribution. The data learned by DM, KIP and TM are more towards the center.

<span id="page-16-1"></span>Image /page/16/Figure/7 description: This image displays five scatter plots, labeled (a) DC, (b) DSA, (c) DM, (d) KIP, and (e) TM. Each scatter plot contains a dense distribution of small blue dots, overlaid with a sparser scattering of larger red stars. The overall shape of the point clouds in each plot is roughly circular or oval. The red stars appear to be concentrated more towards the edges of the point clouds in some plots, while in others they are more evenly distributed throughout. The figure is captioned as 'Figure 7. Synthetic data distribution with IPC 50'.

Figure 7: Synthetic dataset distribution with IPC 50

## A.11 Example real images and synthetic images

Here we show the selected real images generated by Random selection, K-Center and synthetic images generated by DC, DSA, DM and TM in Figure [8](#page-21-0) for reader's references.

### A.12 Source code and leaderboard

We open source our benchmark and evaluation library at [https://github.com/justincui03/](https://github.com/justincui03/dc_benchmark) [dc\\_benchmark](https://github.com/justincui03/dc_benchmark) as a contribution to the research community. At the same time, we build a leaderboard <https://justincui03.github.io/dcbench/> to track the most up-to-date progress in the field. All these will be maintained regularly and we will keep updating the benchmark to reflect the newest changes.

# A.13 Assets license

Code License:Our codebase is open sourced under the MIT license. Dataset License: The datasets(CIFAR10/100, TinyImageNet) used in this paper are not part of our assets, if readers are going to use the datasets, please follow the instructions below

- CIFAR10/100 Please refer to [requirements for usage](http://www.cs.toronto.edu/~kriz/cifar.html) for these 2 datasets.
- TinyImageNet Please refer to [term of access](https://image-net.org/download-images.php) for using TinyImageNet.

# Ethics Statements

The condensed dataset used in this paper are all generated from the following standard non-private dataset: CIFAR-10, CIFAR-100, and TinyImageNet. The library itself does not include any sensitive information or components. Therefore, we are not aware of any ethical concern of the benchmark. However, the end users should be aware of potential data leakage through condensed dataset, when they try to apply any condensation methods included in our benchmark to their tasks at hand.

# Appendix resumes on the next page.

| Dataset      | Method     | <b>IPC</b>         |                                      |                                      | Augmentation                         |                                      |                                      |
|--------------|------------|--------------------|--------------------------------------|--------------------------------------|--------------------------------------|--------------------------------------|--------------------------------------|
|              |            |                    | n/a                                  | imagenet_aug                         | randaug                              | autoaug                              | <b>DSA</b>                           |
|              |            | $\mathbf{1}$       | $15.06 \pm 0.60$                     | $15.07 \pm 0.27$                     | $14.58 \pm 0.54$                     | $13.78 \pm 0.69$                     | $15.4 \pm 0.28$                      |
|              | Random     | 10                 | $25.67 \pm 0.36$                     | $29.22 \pm 0.33$                     | $26.59 \pm 0.58$                     | $25.32 \pm 0.67$                     | $31.00\pm0.48$                       |
|              |            | 50                 | $44.59 \pm 0.32$                     | $50.00 \pm 0.48$                     | $47.36 \pm 0.52$                     | $42.47 \pm 0.57$                     | $50.55 \pm 0.32$                     |
|              |            | 1                  | $23.34 \pm 0.90$                     | $25.92 \pm 1.12$                     | $21.99 \pm 0.53$                     | $18.5 \pm 0.62$                      | $25.16 \pm 0.45$                     |
|              | K-Center   | 10                 | $36.43 \pm 0.63$                     | $39.46 \pm 0.59$                     | $38.18 \pm 1.52$                     | $33.01 \pm 0.69$                     | $41.49 \pm 0.73$                     |
|              |            | 50                 | $48.71 \pm 0.33$                     | $55.53 \pm 0.51$                     | $53.09 \pm 0.41$                     | $47.62 \pm 0.53$                     | $56.00 \pm 0.29$                     |
|              |            | $\mathbf{1}$       | $28.08 \pm 0.80$                     | $25.70 \pm 0.82$                     | $27.39 \pm 0.81$                     | $22.51 \pm 0.91$                     | $29.34 \pm 0.37$                     |
|              | DC         | 10                 | $44.43 \pm 0.85$                     | $46.67 \pm 0.43$                     | $47.65 \pm 0.36$                     | $43.13 \pm 0.80$                     | $50.99 \pm 0.62$                     |
| CIFAR-10     |            | 50                 | $53.29 \pm 0.92$                     | $55.04 \pm 0.20$                     | $54.82 \pm 0.31$                     | $51.49 \pm 0.42$                     | $56.81 \pm 0.44$                     |
|              |            |                    |                                      |                                      |                                      |                                      |                                      |
|              | <b>DSA</b> | $\mathbf{1}$<br>10 | $27.75 \pm 0.54$<br>$43.54 \pm 0.37$ | $25.81 \pm 0.66$<br>$47.84 \pm 0.41$ | $26.71 \pm 0.57$<br>$47.78 \pm 0.57$ | $24.75 \pm 0.98$<br>$41.75 \pm 0.81$ | $27.76 \pm 0.47$<br>$52.96 \pm 0.41$ |
|              |            | 50                 | $54.25 \pm 0.57$                     | $59.11 \pm 0.57$                     | $56.15 \pm 0.71$                     | $51.98 \pm 0.51$                     | $60.28 \pm 0.37$                     |
|              |            |                    |                                      |                                      |                                      |                                      |                                      |
|              |            | $\mathbf{1}$       | $26.15 \pm 0.80$                     | $24.54 \pm 0.67$                     | $24.96 \pm 0.89$                     | $21.06 \pm 2.03$                     | $26.45 \pm 0.39$                     |
|              | DM         | 10                 | $42.45 \pm 0.43$                     | $45.67 \pm 0.55$                     | $44.95 \pm 0.53$                     | $44.55 \pm 0.97$                     | $47.64 \pm 0.55$                     |
|              |            | 50                 | $56.54 \pm 0.41$                     | $60.42 \pm 0.42$                     | $60.17 \pm 0.43$                     | $57.56 \pm 0.42$                     | $61.99 \pm 0.33$                     |
|              |            | $\mathbf{1}$       | $24.30 \pm 1.89$                     | $28.34 \pm 2.16$                     | $25.79 \pm 1.23$                     | $40.55 \pm 1.34$                     | $35.78 \pm 1.03$                     |
|              | KIP        | 10                 | $32.75 \pm 0.91$                     | $40.13 \pm 1.29$                     | $36.59 \pm 0.83$                     | $47.23 \pm 0.40$                     | $46.14 \pm 0.68$                     |
|              |            | 50                 | $50.73 \pm 0.55$                     | $51.55 \pm 0.67$                     | $50.26 \pm 0.46$                     | $56.94 \pm 0.38$                     | $53.22\pm0.71$                       |
|              |            | $\mathbf{1}$       | $39.30 \pm 1.14$                     | $28.23 \pm 2.09$                     | $31.57 \pm 1.41$                     | $23.57 \pm 1.45$                     | $44.19 \pm 1.18$                     |
|              | TM         | 10                 | $53.49 \pm 0.74$                     | $56.86 \pm 0.57$                     | $57.87 \pm 0.93$                     | $47.11 \pm 0.98$                     | $63.66 \pm 0.38$                     |
|              |            | 50                 | $62.24 \pm 0.52$                     | $65.67 \pm 0.75$                     | $66.38 \pm 0.44$                     | $61.25 \pm 0.76$                     | $70.28 \pm 0.61$                     |
|              |            | $\mathbf{1}$       | $4.28 \pm 0.20$                      | $4.67 \pm 0.15$                      | $4.60 \pm 0.17$                      | $4.49 \pm 0.13$                      | $5.30 \pm 0.23$                      |
|              | Random     | 10                 | $14.53 \pm 0.27$                     | $17.1 \pm 0.36$                      | $16.44 \pm 0.30$                     | $14.93 \pm 0.24$                     | $18.64 \pm 0.25$                     |
|              |            | 50                 | $29.50\pm0.26$                       | $34.24\pm0.23$                       | $31.94 \pm 0.26$                     | $30.31 \pm 0.31$                     | $34.66 \pm 0.41$                     |
|              |            | $\mathbf{1}$       | $8.59 \pm 0.27$                      | $9.25 \pm 0.19$                      | $9.14 \pm 0.23$                      | $8.60 \pm 0.32$                      | $10.89 \pm 0.17$                     |
|              | K-Center   | 10                 | $20.73 \pm 0.22$                     | $23.56 \pm 0.19$                     | $23.48 \pm 0.19$                     | $20.67 \pm 0.23$                     | $25.04 \pm 0.30$                     |
|              |            | 50                 | $33.61 \pm 0.41$                     | $37.73 \pm 0.34$                     | $36.19 \pm 0.32$                     | $33.79 \pm 0.37$                     | $38.64 \pm 0.43$                     |
|              |            | $\mathbf{1}$       | $12.55 \pm 0.37$                     | $11.46 \pm 0.28$                     | $12.98 \pm 0.42$                     | $12.93 \pm 0.24$                     | $13.66 \pm 0.29$                     |
|              | DC         | 10                 | $25.36 \pm 0.28$                     | $25.51 \pm 0.36$                     | $27.96 \pm 0.30$                     | $25.87 \pm 0.53$                     | $28.42 \pm 0.29$                     |
| CIFAR-100    |            | 50                 | $29.74 \pm 0.34$                     | $24.72 \pm 0.34$                     | $27.54 \pm 0.42$                     | $25.87 \pm 0.18$                     | $30.56 \pm 0.56$                     |
|              |            | $\mathbf{1}$       | $13.03 \pm 0.14$                     | $11.50 \pm 0.15$                     | $12.61 \pm 0.33$                     | $13.03 \pm 0.33$                     | $13.73 \pm 0.45$                     |
|              | <b>DSA</b> | 10                 | $27.12 \pm 0.27$                     | $28.38 \pm 0.32$                     | $29.78 \pm 0.29$                     | $27.58 \pm 0.25$                     | $32.23 \pm 0.35$                     |
|              |            | 50                 | $38.58 \pm 0.28$                     | $40.26 \pm 0.34$                     | $40.81 \pm 0.27$                     | $38.11 \pm 0.54$                     | $43.13 \pm 0.33$                     |
|              |            |                    |                                      |                                      |                                      |                                      |                                      |
|              | ${\rm DM}$ | $\mathbf{1}$<br>10 | $10.79 \pm 0.31$<br>$25.40 \pm 0.22$ | $7.52 \pm 0.22$<br>$26.5 \pm 0.13$   | $8.75 \pm 0.30$<br>$28.15 \pm 0.32$  | $7.85 \pm 0.44$<br>$26.66 \pm 0.25$  | $11.20 \pm 0.27$<br>$29.23 \pm 0.26$ |
|              |            | 50                 | $37.7 \pm 0.26$                      | $40.45 \pm 0.32$                     | $40.69 \pm 0.35$                     | $38.81 \pm 0.31$                     | $42.32 \pm 0.37$                     |
|              |            |                    |                                      |                                      |                                      |                                      |                                      |
|              | KIP        | $\mathbf{1}$       | $8.16 \pm 0.29$                      | $7.02 \pm 0.28$<br>$24.52 \pm 0.42$  | $7.36 \pm 0.28$                      | $12.04 \pm 0.15$                     | $6.74 \pm 0.33$                      |
|              |            | 10                 | $22.45 \pm 0.44$                     |                                      | $23.53 \pm 0.20$                     | $29.04 \pm 0.34$                     | $22.45 \pm 0.28$                     |
|              |            | 1                  | $16.69 \pm 0.64$                     | $11.55 \pm 0.33$                     | $10.87 \pm 0.34$                     | $10.65 \pm 0.35$                     | $22.3 \pm 0.55$                      |
|              | TM         | 10                 | $31.76 \pm 0.54$                     | $30.46 \pm 0.37$                     | $34.37 \pm 0.36$                     | $29.53 \pm 0.41$                     | $38.18 \pm 0.42$                     |
|              |            | 50                 | $43.04 \pm 0.48$                     | $41.55 \pm 0.26$                     | $44.73 \pm 0.33$                     | $38.84 \pm 0.26$                     | $46.32 \pm 0.26$                     |
|              |            | 1                  | $1.42 \pm 0.08$                      | $1.45 \pm 0.05$                      | $1.50 \pm 0.08$                      | $1.37 \pm 0.08$                      | $1.65 \pm 0.11$                      |
|              | Random     | 10                 | $4.70 \pm 0.18$                      | $6.15 \pm 0.11$                      | $5.66 \pm 0.16$                      | $5.27 \pm 0.19$                      | $6.88 \pm 0.25$                      |
|              |            | 50                 | $13.98 \pm 0.28$                     | $17.39 \pm 0.21$                     | $16.44 \pm 0.25$                     | $15.00 \pm 0.32$                     | $18.62 \pm 0.22$                     |
|              |            | 1                  | $2.68 \pm 0.20$                      | $2.68 \pm 0.18$                      | $2.53 \pm 0.11$                      | $2.34 \pm 0.15$                      | $3.03 \pm 0.12$                      |
|              | K-Center   | 10                 | $7.83 \pm 0.35$                      | $10.17 \pm 0.20$                     | $9.40 \pm 0.22$                      | $8.63 \pm 0.18$                      | $11.38 \pm 0.26$                     |
|              |            | 50                 | $16.72 \pm 0.41$                     | $20.47 \pm 0.23$                     | $19.79 \pm 0.46$                     | $17.75 \pm 0.18$                     | $22.02 \pm 0.40$                     |
|              |            | 1                  | $5.26 \pm 0.19$                      | $4.02 \pm 0.13$                      | $4.80 \pm 0.09$                      | $4.18 \pm 0.21$                      | $5.27 \pm 0.10$                      |
|              | DC         | 10                 | $11.12 \pm 0.28$                     | $9.95 \pm 0.24$                      | $12.16 \pm 0.20$                     | $9.86 \pm 0.22$                      | $12.83 \pm 0.14$                     |
| TinyImageNet |            | 50                 | $11.19 \pm 0.27$                     | $9.26 \pm 0.37$                      | $12.303 \pm 0.19$                    | $9.33 \pm 0.13$                      | $12.66 \pm 0.36$                     |
|              |            | $\mathbf{1}$       | $5.48 \pm 0.14$                      | $4.19 \pm 0.10$                      | $5.23 \pm 0.32$                      | $5.38 \pm 0.11$                      | $5.67 \pm 0.14$                      |
|              | <b>DSA</b> | 10                 | $12.43 \pm 0.29$                     | $12.53 \pm 0.24$                     | $14.46 \pm 0.23$                     | $14.18 \pm 0.14$                     | $16.34 \pm 0.21$                     |
|              |            | 50                 | $21.41 \pm 0.25$                     | $22.47 \pm 0.37$                     | $22.98 \pm 0.35$                     | $20.00 \pm 0.29$                     | $25.31 \pm 0.22$                     |
|              |            | $\mathbf{1}$       | $3.73 \pm 0.22$                      | $3.15 \pm 0.19$                      | $3.65 \pm 0.21$                      | $3.70 \pm 0.16$                      | $3.82 \pm 0.21$                      |
|              | DM         | 10                 | $12.06 \pm 0.43$                     | $12.43 \pm 0.31$                     | $13.04 \pm 0.18$                     | $11.76 \pm 0.17$                     | $13.51 \pm 0.31$                     |
|              |            | 50                 | $20.93 \pm 0.32$                     | $22.19 \pm 0.34$                     | $22.03 \pm 0.22$                     | $19.31 \pm 0.33$                     | $22.76 \pm 0.28$                     |
|              |            |                    |                                      |                                      |                                      |                                      |                                      |
|              | TM         | 1<br>10            | $5.88\pm0.41$                        | $5.13 \pm 0.30$                      | $5.92 \pm 0.16$<br>$17.31 \pm 0.31$  | $5.44 \pm 0.23$<br>$14.97 \pm 0.38$  | $8.27\pm0.36$                        |
|              |            | 50                 | $13.6 \pm 0.47$<br>$20.12 \pm 0.30$  | $16.89 \pm 0.15$<br>$25.33 \pm 0.31$ | $26.49 \pm 0.32$                     | $23.30 \pm 0.18$                     | $20.11 \pm 0.16$<br>$28.16 \pm 0.45$ |
|              |            |                    |                                      |                                      |                                      |                                      |                                      |

<span id="page-18-0"></span>Table 4: Complete test accuracy with variance for Random selection, K-Center, DC, DSA, DM, TM under different augmentation settings on CIFAR-10, CIFAR-100 and TinyImageNet.

Whole training set performances are:  $85.95 \pm 0.09$  on CIFAR-10,  $56.69 \pm 0.18$  on CIFAR-100 and  $39.83 \pm 0.41$  on TinyImageNet with DSA augmentation. 19

| Dataset      | Method           | IPC                | ConvNet                              | MLP                                  | Network<br>ResNet18                  | ViT                                  |                                      |
|--------------|------------------|--------------------|--------------------------------------|--------------------------------------|--------------------------------------|--------------------------------------|--------------------------------------|
|              |                  | $\mathbf{1}$       | $15.40 \pm 0.28$                     | $14.37 \pm 0.38$                     | $16.56 \pm 0.46$                     | ResNet152<br>$12.15 \pm 1.80$        | 14.19 $\pm 0.99$                     |
|              | Random           | 10<br>50           | $31.00 \pm 0.48$<br>$50.55 \pm 0.32$ | $25.08 \pm 0.27$<br>$35.21 \pm 0.44$ | $29.52 \pm 0.87$<br>$47.26 \pm 0.27$ | $15.84 \pm 0.91$<br>$23.36 \pm 2.31$ | $26.21 \pm 0.49$<br>$39.73 \pm 0.52$ |
|              |                  | $\mathbf{1}$       | $25.16 \pm 0.45$                     | $24.01 \pm 0.32$                     | $25.99 \pm 0.57$                     | $14.64 \pm 1.30$                     | $21.54 \pm 0.55$                     |
|              | K-Center         | 10<br>50           | $41.49 \pm 0.73$<br>$56.00 \pm 0.29$ | $32.92 \pm 0.38$<br>$40.61 \pm 0.34$ | $40.08 \pm 0.88$<br>$52.69 \pm 0.70$ | $19.35 \pm 0.71$<br>$27.84 \pm 1.07$ | $31.95 \pm 0.57$<br>$44.65 \pm 0.39$ |
|              |                  | $\mathbf{1}$       | $29.34 \pm 0.37$                     | $29.02 \pm 0.52$                     | $27.43 \pm 0.71$                     | $15.31 \pm 0.36$                     | $28.14 \pm 1.11$                     |
| CIFAR-10     | DC               | 10<br>50           | $50.99 \pm 0.62$<br>$56.81 \pm 0.44$ | $34.06 \pm 0.40$<br>$31.63 \pm 0.55$ | $43.96 \pm 1.37$<br>$45.94 \pm 1.41$ | $16.51 \pm 0.89$<br>$17.98 \pm 1.06$ | $34.36 \pm 0.35$<br>$30.14 \pm 0.51$ |
|              |                  | $\mathbf{1}$       | $27.76 \pm 0.47$                     | $25.04 \pm 0.77$                     | $25.59 \pm 0.56$                     | $15.12 \pm 0.65$                     | $23.70 \pm 0.20$                     |
|              | DSA              | 10<br>50           | $52.96 \pm 0.41$<br>$60.28 \pm 0.37$ | $34.49 \pm 0.47$<br>$41.01 \pm 0.36$ | $42.11 \pm 0.56$<br>$49.52 \pm 0.72$ | $16.10 \pm 1.03$<br>$19.65 \pm 1.16$ | $31.88 \pm 0.35$<br>$43.30 \pm 0.43$ |
|              |                  | 1                  | $26.45 \pm 0.39$                     | $10.02 \pm 0.55$                     | $20.64 \pm 0.47$                     | $14.09 \pm 0.58$                     | $20.47 \pm 0.46$                     |
|              | DM               | 10<br>50           | $47.64 \pm 0.55$<br>$61.99 \pm 0.33$ | $34.44 \pm 0.30$<br>$40.49 \pm 0.38$ | $38.21 \pm 1.05$<br>$52.76 \pm 0.44$ | $15.60 \pm 1.51$<br>$21.67 \pm 1.34$ | $34.37 \pm 0.49$<br>$45.22 \pm 0.37$ |
|              |                  | 1                  | $40.55 \pm 1.34$                     | $26.31 \pm 0.35$                     | $27.63 \pm 1.06$                     | $14.16 \pm 0.84$                     | $17.31 \pm 1.63$                     |
|              | KIP              | 10<br>50           | $47.23 \pm 0.40$<br>$56.94 \pm 0.38$ | $23.58 \pm 0.38$<br>$25.25 \pm 0.28$ | $38.82 \pm 0.69$<br>$47.56 \pm 0.76$ | $15.90 \pm 0.21$<br>$18.44 \pm 0.34$ | $15.85 \pm 1.07$<br>$18.28 \pm 0.64$ |
|              | TM               | $\mathbf{1}$       | $44.19 \pm 1.18$                     | $10.40 \pm 0.48$                     | $34.17 \pm 1.41$                     | $13.40 \pm 0.86$                     | $21.53 \pm 0.44$                     |
|              |                  | 10<br>50           | $63.66 \pm 0.38$<br>$70.28 \pm 0.61$ | $30.77 \pm 0.60$<br>$38.45 \pm 0.27$ | $45.22 \pm 1.37$<br>$59.96 \pm 0.72$ | $16.61 \pm 1.37$<br>$20.90 \pm 1.60$ | $33.58 \pm 0.56$<br>$47.72 \pm 0.57$ |
|              |                  | $\mathbf{1}$       | $5.30 \pm 0.23$                      | $4.27 \pm 0.09$                      | $4.36 \pm 0.15$                      | $1.73 \pm 0.12$                      | $4.45 \pm 0.15$                      |
|              | Random           | 10<br>50           | $18.64 \pm 0.25$<br>$34.66 \pm 0.41$ | $10.20 \pm 0.18$<br>$16.80 \pm 0.31$ | $15.77 \pm 0.24$<br>$30.23 \pm 0.61$ | $5.19 \pm 0.46$<br>$18.55 \pm 1.29$  | $14.07 \pm 0.21$<br>$26.90 \pm 0.33$ |
|              |                  | $\mathbf{1}$       | $10.89 \pm 0.17$                     | $7.96 \pm 0.17$                      | $8.75 \pm 0.43$                      | $2.22 \pm 0.19$                      | $7.81 \pm 0.13$                      |
|              | K-Center         | 10<br>50           | $25.04 \pm 0.30$                     | $13.92 \pm 0.20$                     | $22.18 \pm 0.59$                     | $7.14 \pm 0.79$                      | $17.98 \pm 0.44$                     |
|              | DC<br><b>DSA</b> | $\mathbf{1}$       | $38.64 \pm 0.43$<br>$13.66 \pm 0.29$ | $19.32 \pm 0.36$<br>$9.78 \pm 0.27$  | $34.00 \pm 0.51$<br>$9.71 \pm 0.46$  | $21.25 \pm 1.46$<br>$2.67 \pm 0.16$  | $30.12 \pm 0.65$<br>$9.27 \pm 0.14$  |
| CIFAR-100    |                  | 10                 | $28.42 \pm 0.29$                     | $12.36 \pm 0.20$                     | $17.94 \pm 0.59$                     | $5.28 \pm 1.05$                      | $12.22 \pm 0.17$                     |
|              |                  | 50<br>$\mathbf{1}$ | $30.56 \pm 0.56$<br>$13.73 \pm 0.45$ | $13.29 \pm 0.30$<br>$10.56 \pm 0.22$ | $17.64 \pm 0.31$<br>$9.95 \pm 0.55$  | $11.36 \pm 0.95$<br>$2.95 \pm 0.44$  | $17.51 \pm 0.15$<br>$9.48 \pm 0.27$  |
|              |                  | 10                 | $32.23 \pm 0.35$                     | $16.17 \pm 0.26$                     | $21.86 \pm 0.43$                     | $5.45 \pm 1.04$                      | $19.61 \pm 0.15$                     |
|              |                  | 50<br>$\mathbf{1}$ | $43.13 \pm 0.33$<br>$11.20 \pm 0.27$ | $21.42 \pm 0.31$<br>$8.17 \pm 0.21$  | $34.34 \pm 0.44$<br>$5.36 \pm 0.31$  | $20.79 \pm 1.76$<br>$2.11 \pm 0.13$  | $31.89 \pm 0.49$<br>$4.59 \pm 0.26$  |
|              | DM               | 10                 | $29.23 \pm 0.26$                     | $14.68 \pm 0.18$                     | $18.72 \pm 0.49$                     | $3.91 \pm 0.73$                      | $17.06 \pm 0.25$                     |
|              |                  | 50<br>1            | $42.32 \pm 0.37$<br>$12.04 \pm 0.15$ | $20.14 \pm 0.24$<br>$5.55 \pm 0.25$  | $33.34 \pm 0.40$<br>$5.00 \pm 0.38$  | $17.29 \pm 2.41$<br>$1.95 \pm 0.12$  | $30.11 \pm 0.25$<br>$7.23 \pm 0.34$  |
|              | KIP              | 10                 | $29.04 \pm 0.34$                     | $9.00 \pm 0.21$                      | $20.99 \pm 0.53$                     | $4.53 \pm 0.18$                      | $12.05 \pm 0.65$                     |
|              |                  | $\mathbf{1}$       | $22.3 \pm 0.55$<br>$38.18 \pm 0.42$  | $8.69 \pm 0.33$<br>$14.35 \pm 0.24$  | $13.32 \pm 1.29$                     | $2.54 \pm 0.11$                      | $4.65 \pm 0.22$                      |
|              | TM               | 10<br>50           | $46.32 \pm 0.26$                     | $21.92 \pm 0.27$                     | $26.78 \pm 0.58$<br>$41.08 \pm 0.29$ | $5.74 \pm 0.60$<br>$27.87 \pm 1.99$  | $19.06 \pm 0.31$<br>$32.93 \pm 0.43$ |
|              |                  | $\mathbf{1}$       | $1.65 \pm 0.11$                      | $1.37 \pm 0.08$                      | $1.27 \pm 0.08$                      | $0.63 \pm 0.08$                      | $1.71 \pm 0.03$                      |
|              | Random           | 10<br>50           | $6.88 \pm 0.25$<br>$18.62 \pm 0.22$  | $3.12 \pm 0.13$<br>$5.28 \pm 0.2$    | $3.34 \pm 0.16$<br>$10.35 \pm 0.33$  | $1.01 \pm 0.15$<br>$2.90 \pm 0.40$   | $6.63 \pm 0.21$<br>$16.87 \pm 0.20$  |
|              |                  | 1                  | $3.03 \pm 0.12$                      | $2.53 \pm 0.13$                      | $2.29 \pm 0.10$                      | $0.82 \pm 0.10$                      | $2.27 \pm 0.02$                      |
|              | K-Center         | 10<br>50           | $11.38 \pm 0.26$<br>$22.02 \pm 0.40$ | $4.73 \pm 0.08$<br>$5.99 \pm 0.17$   | $5.46 \pm 0.24$<br>$13.51 \pm 0.34$  | $1.55 \pm 0.21$<br>$3.91 \pm 0.49$   | $9.92 \pm 0.34$<br>$19.70 \pm 0.41$  |
|              |                  | $\mathbf{1}$       | $5.27 \pm 0.10$                      | $2.67 \pm 0.17$                      | $3.17 \pm 0.21$                      | $0.90 \pm 0.14$                      | $2.00 \pm 0.12$                      |
| TinyImageNet | DC               | 10<br>50           | $12.83 \pm 0.14$<br>$12.66 \pm 0.36$ | $4.12 \pm 0.11$<br>$3.81 \pm 0.17$   | $5.44 \pm 0.21$<br>$7.05 \pm 0.21$   | $1.24 \pm 0.18$<br>$2.39 \pm 0.21$   | $4.17 \pm 0.10$<br>$5.22 \pm 0.23$   |
|              |                  | $\mathbf{1}$       | $5.67 \pm 0.14$                      | $3.90 \pm 0.16$                      | $3.20 \pm 0.13$                      | $0.84 \pm 0.12$                      | $3.17 \pm 0.03$                      |
|              | <b>DSA</b>       | 10<br>50           | $16.34 \pm 0.21$<br>$25.31 \pm 0.22$ | $6.31 \pm 0.21$<br>$6.72 \pm 0.20$   | $7.60 \pm 0.36$<br>$13.36 \pm 0.40$  | $1.90 \pm 0.21$<br>$3.78 \pm 0.56$   | $11.17 \pm 0.15$<br>$19.87 \pm 0.44$ |
|              |                  | $\mathbf{1}$       | $3.82 \pm 0.21$                      | $3.11 \pm 0.10$                      | $1.79 \pm 0.17$                      | $0.89 \pm 0.07$                      | $3.21 \pm 0.07$                      |
|              | DM               | 10<br>50           | $13.51 \pm 0.31$<br>$22.76 \pm 0.28$ | $4.24 \pm 0.13$<br>$5.74 \pm 0.27$   | $3.57 \pm 0.20$<br>$11.07 \pm 0.39$  | $1.06 \pm 0.19$<br>$3.33 \pm 0.46$   | $7.46 \pm 0.20$<br>$18.88 \pm 0.36$  |
|              |                  | 1                  | $8.27 \pm 0.36$                      | $3.33 \pm 0.13$                      | $4.45 \pm 0.23$                      | $0.95 \pm 0.09$                      | $2.25 \pm 0.07$                      |
|              | TM               | 10<br>50           | $20.11 \pm 0.16$<br>$28.16 \pm 0.45$ | $4.59 \pm 0.20$<br>$6.04 \pm 0.20$   | $10.16 \pm 0.43$<br>$20.65 \pm 0.44$ | $1.74 \pm 0.21$<br>$4.10 \pm 0.52$   | $11.14 \pm 0.24$<br>$21.43 \pm 0.25$ |

<span id="page-19-0"></span>Table 5: Transferability of different methods using 5 different networks with IPC 1, 10 and 50. All results are evaluated with DSA augmentation.

| <b>IPC</b> | Random           | K-Center         | Method<br>DC     | <b>DSA</b>       | DM               |
|------------|------------------|------------------|------------------|------------------|------------------|
| 1          | $15.40 \pm 0.28$ | $25.16 \pm 0.45$ | $29.34 \pm 0.37$ | $27.76 \pm 0.47$ | $26.45 \pm 0.39$ |
| 10         | $31.00 + 0.48$   | $41.49 + 0.73$   | $50.99 + 0.62$   | $52.96 + 0.41$   | $47.64 \pm 0.55$ |
| 50         | $50.55 \pm 0.32$ | $56.00 \pm 0.29$ | $56.81 + 0.44$   | $60.28 \pm 0.37$ | $61.99 \pm 0.33$ |
| 100        | $57.89 \pm 0.57$ | $62.18 \pm 0.18$ | $65.70 \pm 0.44$ | $66.18 \pm 0.30$ | $65.12 \pm 0.40$ |
| 200        | $64.70 + 0.44$   | $67.25 + 0.48$   | $68.41 + 0.45$   | $69.49 \pm 0.13$ | $69.15 + 0.17$   |
| 300        | $68.52 \pm 0.29$ | $70.12 \pm 0.09$ | $69.42 + 0.45$   | $71.46 \pm 0.27$ | $69.36 \pm 0.35$ |
| 400        | $70.28 + 0.39$   | $71.88 \pm 0.34$ | $70.86 + 0.42$   | $72.22 \pm 0.48$ | $72.61 + 0.54$   |
| 500        | $73.19 + 0.29$   | $74.31 + 0.33$   | $72.05 + 0.35$   | $73.62 \pm 0.28$ | $75.09 \pm 0.26$ |
| 600        | $74.00 \pm 0.29$ | $75.98 + 0.19$   | $72.84 + 0.40$   | $74.99 + 0.21$   | $76.07 \pm 0.20$ |
| 700        | $75.29 + 0.25$   | $76.74 + 0.34$   | $73.70 + 0.31$   | $76.07 + 0.26$   | $76.78 + 0.27$   |
| 800        | $75.52 \pm 0.10$ | $76.94 + 0.21$   | $74.80 + 0.29$   | $76.76 \pm 0.27$ | $77.41 + 0.19$   |
| 900        | $77.44 + 0.45$   | $78.21 \pm 0.20$ | $75.26 + 0.34$   | $77.77 \pm 0.44$ | $78.33 \pm 0.41$ |
| 1000       | $78.38 + 0.20$   | $79.47 + 0.29$   | $76.62 + 0.32$   | $78.68 \pm 0.25$ | $78.83 \pm 0.05$ |

<span id="page-20-0"></span>Table 6: Test accuracy of condensation methods under different IPCs. All numbers are recorded on ConvNet and CIFAR-10 dataset with DSA augmentation.

<span id="page-20-1"></span>Table 7: Test accuracy of different condensation methods with SGD and Adam optimizer on CIFAR-10. All results are evaluated with DSA augmentation.

|      |           | Optimizer IPC Random | K-Center       | DC                            | <b>DSA</b>                                                                                                                                                                                                                                                                                        | DM                            | KIP | TM                             |
|------|-----------|----------------------|----------------|-------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------|-----|--------------------------------|
| SGD  | 10<br>50. | $31.00 + 0.48$       | $41.49 + 0.73$ |                               | $15.40 + 0.28$ $25.16 + 0.45$ $29.34 + 0.37$ $27.76 + 0.47$ $26.45 + 0.39$ $40.55 + 1.34$ $44.19 + 1.18$<br>$50.99 + 0.62$ $52.96 + 0.41$ $47.64 + 0.55$ $47.23 + 0.40$<br>$50.55 \pm 0.32$ $56.00 \pm 0.29$ $56.81 \pm 0.44$ $60.28 \pm 0.37$ $61.99 \pm 0.33$ $56.94 \pm 0.38$ $70.28 \pm 0.61$ |                               |     | $6366 + 038$                   |
| Adam | 10<br>50. | $32.71 + 0.92$       | $43.26 + 0.41$ | $52.01 + 0.62$ $51.17 + 0.91$ | $15.39 + 0.38$ $25.40 + 1.21$ $28.76 + 0.71$ $27.83 + 1.27$ $26.22 + 0.40$ $30.56 + 3.07$<br>$-49.75 \pm 1.10$ $54.82 \pm 1.23$ $54.75 \pm 1.52$ $58.73 \pm 0.96$ $61.13 \pm 0.92$ $55.08 \pm 1.88$ $71.82 \pm 0.52$                                                                              | $48.64 + 1.02$ $44.20 + 0.62$ |     | $4662 + 139$<br>$64.94 + 0.61$ |

<span id="page-20-2"></span>Table 8: Training time of different condensation methods. All results for synthesis based methods are acquired by running 100 iterations and all results for K-Center are acquired by performing 300 iterations on CIFAR-10 with IPC 1, 10, 50. The average result and standard deviation per iteration are reported for each IPC

| Method     |                   | $GPU$ memory $(MB)$ |                  |      |      |            |
|------------|-------------------|---------------------|------------------|------|------|------------|
|            |                   | 10                  | 50               |      | 10   | 50         |
| K-Center   | $0.0012 \pm 0.00$ | $0.015 \pm 0.00$    | $0.05 \pm 0.00$  | 3575 | 3575 | 3575       |
| DC.        | $0.16 \pm 0.01$   | $3.31 \pm 0.02$     | $15.74 \pm 0.10$ | 3515 | 3621 | 4527       |
| <b>DSA</b> | $0.22 \pm 0.02$   | $4.47 \pm 0.12$     | $20.13 \pm 0.58$ | 3513 | 3639 | 4539       |
| DM         | $0.08 \pm 0.02$   | $0.08 \pm 0.02$     | $0.08 \pm 0.02$  | 3323 | 3455 | 3605       |
| <b>TM</b>  | $0.36 \pm 0.23$   | $0.40 \pm 0.20$     | OOM              | 2711 | 8049 | <b>OOM</b> |

Note: different methods need different iterations to converge, this table shows the run time and memory needed per iteration which reflect the bottleneck of the algorithm.

<span id="page-21-0"></span>Image /page/21/Figure/0 description: The image displays two grids of images, side-by-side. The left grid is labeled "(a) Randomly selected images" and the right grid is labeled "(b) K-Center selected images". Both grids contain 10 rows and 10 columns of smaller images, totaling 100 images per grid. The images in both grids depict a variety of subjects including airplanes, cars, various types of birds, cats, dogs, deer, horses, ships, and fire trucks. The arrangement suggests a comparison between randomly selected images and images selected using the K-Center method.

) Randomly selected images

(b) K-Center selected images

| Image: A grid of 100 small images. |
|------------------------------------|
| Image: A grid of 100 small images. |
| Image: A grid of 100 small images. |
| Image: A grid of 100 small images. |
| Image: A grid of 100 small images. |
| Image: A grid of 100 small images. |
| Image: A grid of 100 small images. |
| Image: A grid of 100 small images. |
| Image: A grid of 100 small images. |
| Image: A grid of 100 small images. |

c) Synthetic images by DC

(c) Synthetic images by DC (d) Synthetic images by DSA

Image /page/21/Picture/6 description: The image displays two grids of synthetic images, labeled (c) and (d). Grid (c) contains 64 images arranged in an 8x8 matrix, with each image appearing somewhat abstract and textured, predominantly in earthy tones with hints of blue and green. Grid (d) also contains 64 images in an 8x8 matrix, but these images are more recognizable, depicting various objects and animals such as birds, cars, cats, dogs, deer, horses, and boats. The images in grid (d) are generally clearer and more colorful than those in grid (c).

(e) Synthetic images by DM (f) Synthetic images by TM

Figure 8: Real images vs synthetic images
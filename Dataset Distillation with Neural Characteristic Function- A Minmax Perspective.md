# <span id="page-0-1"></span>Dataset Distillation with Neural Characteristic Function: A Minmax Perspective

<PERSON><PERSON><PERSON><sup>1,2</sup> <PERSON><PERSON><PERSON><sup>2</sup> <PERSON><PERSON><PERSON><sup>2</sup> <PERSON><PERSON> Sun<sup>2</sup><br><PERSON><PERSON><sup>3</sup> <PERSON><PERSON><PERSON><sup>4</sup> <PERSON><PERSON><sup>1,2\*</sup> <PERSON><PERSON><sup>3</sup> <PERSON><PERSON><PERSON><sup>4</sup> <PERSON><PERSON><sup>1,2\*</sup> <sup>1</sup>School of Artificial Intelligence, Shanghai Jiao Tong University <sup>2</sup>EPIC Lab, Shanghai Jiao Tong University <sup>3</sup>Hong Kong University of Science and Technology, Guangzhou <sup>4</sup>Shanghai Artificial Intelligence Laboratory {shaobowang1009,zhanglinfeng}@sjtu.edu.cn

# Abstract

*Dataset distillation has emerged as a powerful approach for reducing data requirements in deep learning. Among various methods, distribution matching-based approaches stand out for their balance of computational efficiency and strong performance. However, existing distance metrics used in distribution matching often fail to accurately capture distributional differences, leading to unreliable measures of discrepancy. In this paper, we reformulate dataset distillation as a minmax optimization problem and introduce Neural Characteristic Function Discrepancy (NCFD), a comprehensive and theoretically grounded metric for measuring distributional differences. NCFD leverages the Characteristic Function (CF) to encapsulate full distributional information, employing a neural network to optimize the sampling strategy for the CF's frequency arguments, thereby maximizing the discrepancy to enhance distance estimation. Simultaneously, we minimize the difference between real and synthetic data under this optimized NCFD measure. Our approach, termed Neural Characteristic Function Matching (NCFM), inherently aligns the phase and amplitude of neural features in the complex plane for both real and synthetic data, achieving a balance between realism and diversity in synthetic samples. Experiments demonstrate that our method achieves significant performance gains over state-of-the-art methods on both low- and high-resolution datasets. Notably, we achieve a 20.5% accuracy boost on ImageSquawk. Our method also reduces GPU memory usage by over 300*× *and achieves 20*× *faster processing speeds compared to state-of-the-art methods. To the best of our knowledge, this is the first work to achieve lossless compression of CIFAR-100 on a single NVIDIA 2080 Ti GPU using only 2.3 GB of memory.*

<span id="page-0-0"></span>Image /page/0/Figure/5 description: This figure illustrates two paradigms for optimizing generative models: the previous paradigm and the authors' proposed minmax paradigm. Both paradigms are shown for two different metrics: MSE (Mean Squared Error) and MMD (Maximum Mean Discrepancy). The previous paradigm, shown in panel (a), involves optimizing a discriminator (denoted by D-tilde) to minimize the distance between synthetic data (red circles) and real data (blue stars) in a latent space (Z). This is depicted for both MSE (Z\_R) and MMD (Z\_H). The authors' minmax paradigm, shown in panel (b), involves a two-stage process. First, a parameterized network (psi) is optimized to maximize the distance between synthetic and real data in the latent space (Z\_psi1). Then, the discriminator (D-tilde) is optimized to minimize this distance (Z\_psi2). This process is also illustrated for both MSE and MMD.

parameterized space  $Z_{\psi}$ , then optimize  $\tilde{D}$  to *minimize* the distance within  $Z_{\psi}$ .

Figure 1. Comparison of different paradigms for dataset distillation. (a) The MSE approach compares point-wise features within Euclidean space, denoted as  $\mathcal{Z}_{\mathbb{R}}$ , while MMD evaluates moment differences in Hilbert space,  $\mathcal{Z}_{H}$ . (b) Our method redefines distribution matching as a minmax optimization problem, where the distributional discrepancy is parameterized by a neural network  $\psi$ . We begin by optimizing  $\psi$  to maximize the discrepancy, thereby establishing the latent space  $\mathcal{Z}_{\psi}$ , and subsequently optimize the synthesized data  $\tilde{\mathcal{D}}$  to minimize this discrepancy within  $\mathcal{Z}_{\psi}$ .

### 1. Introduction

Deep neural networks (DNNs) have achieved remarkable progress across a range of tasks, largely due to the availability of vast amounts of training data. However, training effectively with limited data remains challenging and crucial, particularly when large-scale datasets become too voluminous for storage. To address this, dataset distillation has been proposed to condense a large, real dataset into a smaller, synthetic one [\[6,](#page-9-0) [49,](#page-10-0) [52,](#page-10-1) [55,](#page-10-2) [56\]](#page-10-3). Dataset distillation has been applied in various areas, including neural architecture search [\[33,](#page-10-4) [44\]](#page-10-5), continual learning [\[15,](#page-9-1) [51\]](#page-10-6), medical image computing [\[29\]](#page-9-2), and privacy protection [\[7,](#page-9-3) [8,](#page-9-4) [11\]](#page-9-5).

Among dataset distillation methods, feature or distribution matching (DM) approaches [\[47,](#page-10-7) [55\]](#page-10-2) have gained popu-

<sup>\*</sup>Corresponding Author.

<span id="page-1-2"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays a comparison between two methods for generating synthetic data that matches real data, focusing on their complex plane distributions. Panel (a) illustrates the "From Real to Complex" process, showing a "Feature Net" that transforms real data samples (x ~ D) and synthetic data samples (x̃ ~ D̃) into complex representations e^{jt, f(x)} and e^{jt, f(x̃)}. It also includes Euler's formula and the definition of the characteristic function Φ\_x(t) in terms of amplitude and phase. Panels (b) and (c) present polar histograms showing the distribution of phases for real (blue) and synthetic (pink) data at different training iterations. Panel (b), "Distribution Matching (DM)", shows that minimizing MMD does not effectively make the distributions similar across iterations 0, 5000, and 10000. Panel (c), "Characteristic Function Matching (Ours)", demonstrates that minimizing CF makes the distributions similar, with the synthetic data's phase distribution becoming much closer to the real data's distribution by iteration 10000.

Figure 2. Comparison of different distribution matching methods. (a) Illustration of embedded features from the real domain to complex-plane features using Euler's formula [\[13\]](#page-9-6). The latent neural feature  $\Phi_x(t)$  captures the amplitude and phase information. (b) MMD-based methods align feature moments in the embedded domain but may not effectively align the overall distributions. (c) CF-based methods directly compare distributions by balancing the amplitude and phase in the complex plane, enhancing distributional similarity.

<span id="page-1-1"></span>Image /page/1/Figure/2 description: This is a line graph comparing the test accuracy (%) of NCFM (Ours) and DATM models against Image Per Class (IPC). The x-axis represents Image Per Class (IPC) with values 1, 10, 50, and 100. The y-axis represents Test Accuracy (%) with values ranging from 20 to 60. The NCFM (Ours) line, shown in dashed pink, starts at approximately 35% accuracy at 1 IPC and increases to about 58% at 100 IPC. The DATM line, shown in dashed cyan, starts at approximately 27% accuracy at 1 IPC and increases to about 55% at 100 IPC. A horizontal dashed yellow line indicates the accuracy of the 'Full Dataset' at approximately 60%. The graph also includes data points represented by circles of varying sizes, indicating GPU Memory usage (2G, 20G, 200G, 300G). Larger circles correspond to larger GPU memory. For NCFM, points are shown at 1 IPC (26G, ~35% accuracy), 10 IPC (229G, ~45% accuracy), 50 IPC (310G, ~55% accuracy), and 100 IPC (730G, ~58% accuracy). For DATM, points are shown at 1 IPC (26G, ~27% accuracy), 10 IPC (2G, ~30% accuracy), 50 IPC (20G, ~40% accuracy), and 100 IPC (200G, ~50% accuracy). Additionally, there is a table showing the speed in seconds per iteration for NCFM and DATM at different IPC values (1, 10, 50, 100), along with the speedup factor. NCFM has speeds of 0.8, 0.7, 1.0, and 0.8 seconds/iteration. DATM has speeds of 8.8, 14.8, 15.2, and 15.6 seconds/iteration. The speedup for DATM over NCFM is 11x, 21x, 15x, and 20x respectively.

Figure 3. Comparison of performance, peak GPU memory usage, and distillation speed between the state-of-the-art (SOTA) distillation method and our NCFM on CIFAR-100 across various IPC values, evaluated on 8 NVIDIA H100 GPUs. Notably, NCFM reduces GPU memory usage by over  $300\times$ , achieves  $20\times$  faster distillation, and delivers better performance. We also successfully demonstrated lossless distillation using only 2.3GB GPU memory.

larity for their effective balance between high performance and computational efficiency. Unlike bi-level optimizationbased distillation approaches [\[6,](#page-9-0) [20,](#page-9-7) [24,](#page-9-8) [54,](#page-10-8) [56\]](#page-10-3), DM-based methods bypass the need for nested optimization. For instance, when learning with 50 images per class (IPC) on CIFAR-10 dataset, DM methods achieve higher test accuracy than gradient matching methods [\[24,](#page-9-8) [54,](#page-10-8) [56\]](#page-10-3), while requiring only a tenth of the computation time.

A key challenge in DM lies in defining an effective metric to measure distributional discrepancies between real and synthetic datasets. Early methods primarily employed Mean Squared Error (MSE) to compare point-wise fea-tures [\[10,](#page-9-9) [38,](#page-10-9) [47\]](#page-10-7), which operates in Euclidean space,  $\mathcal{Z}_{\mathbb{R}}$ , as illustrated on the left of Figure [1\(](#page-0-0)a). However, MSE directly matches pixel-level or patch-level information without capturing the semantic structures embedded in highdimensional manifolds, which falls short for distribution comparison. Later methods [\[53,](#page-10-10) [55,](#page-10-2) [57\]](#page-10-11) employ Maximum Mean Discrepancy (MMD) as a metric. Nevertheless, research in generative modeling  $[4, 25]$  $[4, 25]$  $[4, 25]$  has shown that MMD aligns moments of distributions in a latent Hilbert space,  $\mathcal{Z}_{H}$ , as shown on the right of Figure [1\(](#page-0-0)a). While distributional equivalence implies moment equivalence, the converse is not necessarily true: aligning moments alone does not guarantee full distributional matching. As illustrated in Figure [2\(](#page-1-0)b), MMD-based methods may fail to capture overall distributional alignment between real and synthetic data, resulting in suboptimal synthesized image quality.

To overcome these limitations, we propose a novel approach that reformulates distribution matching as an *adversarial minmax optimization problem*, as depicted in Figure  $1(b)$  $1(b)$ . By leveraging the minmax paradigm, we adaptively learn the discrepancy metric, enabling it to *maximize* the separability between real and synthetic data distributions. This dynamic adjustment addresses the rigidity of fixed metrics like MSE and MMD. Meanwhile, the synthetic data is iteratively optimized to *minimize* the dynamically refined discrepancy measure. Building upon this foundation, we introduce Neural Characteristic Discrepancy (NCFD), a parameterized metric based on the Characteristic Function (CF), which provides a precise and comprehensive representation of the underlying probability distribution. Defined as the Fourier transform of the probability density function, the CF encapsulates all relevant information about a distribution [\[3,](#page-9-12) [5,](#page-9-13) [14,](#page-9-14) [21,](#page-9-15) [31,](#page-9-16) [41\]](#page-10-12). The CF offers a one-to-one correspondence with the cumulative density function, ensuring the robustness and reliability.

In our framework, an auxiliary network embeds features while a lightweight sampling network is optimized to dynamically adjust its CF sampling strategy using a scale mixture of normals. During the distillation process, we iteratively minimize the NCFD to bring synthetic data closer to real data, while training the sampling network to maximize NCFD, thereby improving the metric's robustness and accuracy. Unlike MMD which has quadratic computational complexity, NCFD achieves linear time computational complexity. Our method, Neural Characteristic Function Matching (NCFM), aligns both the phase and amplitude of neural features in the complex plane, achieving a balanced synthesis of realism and diversity in the generated images. As shown in Figure  $2(c)$  $2(c)$ , NCFM effectively captures overall distributional information, leading to wellaligned synthetic and real data distributions after optimization. Our contributions are as follows:

1. We reformulate the distribution matching problem as a minmax optimization problem, where the sampling net<span id="page-2-0"></span>work maximizes the distributional discrepancy to learn a proper discrepancy metric, while the synthesized images are optimized to minimize such discrepancy.

- 2. We introduce Neural Characteristic Function Matching (NCFM), which aligns the phase and amplitude information of neural features in the complex plane for both real and synthetic data, achieving a balance between realism and diversity in synthetic data.
- 3. Extensive experiments across multiple benchmark datasets demonstrate the superior performance and efficiency of NCFM. Particularly, on high-resolution datasets, NCFM achieves significant accuracy gains of up to 20.5% on ImageSquawk and 17.8% on ImageMeow at 10 IPC compared to SOTA methods.
- 4. NCFM achieves unprecedented efficiency in computational resources. As shown in Figure [3,](#page-1-1) our method dramatically reduces resource requirements with better performance, achieving more than  $300 \times$  reduction in GPU memory usage compared with DATM [\[16\]](#page-9-17). Most remarkably, NCFM demonstrates lossless dataset distillation on both CIFAR-10 and CIFAR-100 using about merely 2GB GPU memory, enabling all experiments to be conducted on a single NVIDIA 2080 Ti GPU.

# 2. Related Work

Dataset Distillation Methods Based on Distribution and Feature Matching. Dataset distillation was proposed by [\[49\]](#page-10-0). Compared with various bi-level DD methods, DM [\[55\]](#page-10-2) is regarded as a efficient method that balances the performance and computational efficiency, without involving the nested model optimization. These methods can be classified into two directions, *i.e.*, point-wise and moment-wise matching. For moment-wise matching, DMbased methods [\[53,](#page-10-10) [55,](#page-10-2) [57\]](#page-10-11) propose to minimize the maximum mean discrepancy (MMD) between synthetic and real datasets. For point-wise feature matching, they typically design better strategies to match features extracted across layers in convolutional neural networks, and apply further adjustments to improve the performance [\[10,](#page-9-9) [38,](#page-10-9) [47\]](#page-10-7). However, moment-based and point-based matching methods may not capture the overall distributional discrepancy between synthetic and real data, as they are not sufficient conditions for distributional equivalence.

Characteristic Function as a Distributional Metric. The characteristic function is a unique and universal metric for measuring distributional discrepancy, defined as the Fourier transform of the probability density function [\[3\]](#page-9-12). The CF of any real-valued random variable completely defines its probability distribution, providing an alternative analytical approach compared to working directly with probability density or cumulative distribution functions. Unlike the moment-generating function, the CF always exists when treated as a function of a real-valued argument [\[5\]](#page-9-13). Recently, the CFD has been adopted in deep learning for various tasks, *e.g.*, several works have been proposed to use the CFD for generative modeling [\[1,](#page-9-18) [27\]](#page-9-19). However, none of prior works have considered the CFD for distillation.

### 3. Preliminaries: Distribution Matching

Distribution Matching (DM) was first introduced by [\[55\]](#page-10-2) as an alternative to traditional bi-level optimization techniques, such as gradient matching methods [\[20,](#page-9-7) [24,](#page-9-8) [54,](#page-10-8) [56\]](#page-10-3) and trajectory matching methods [\[6,](#page-9-0) [9,](#page-9-20) [12,](#page-9-21) [16\]](#page-9-17). Classical DM approaches focus on minimizing the discrepancy between the distributions of real and synthetic data, typically categorized into two main types: feature point matching and moment matching. Feature point matching methods [\[10,](#page-9-9) [38,](#page-10-9) [47\]](#page-10-7) directly compare point-wise features using Mean Square Error (MSE), as defined by:

$$
\mathcal{L}_{MSE} = \mathbb{E}_{\boldsymbol{x} \sim \mathcal{D}, \boldsymbol{\tilde{x}} \sim \tilde{\mathcal{D}}} \left[ \left\| f(\boldsymbol{x}) - f(\tilde{\boldsymbol{x}}) \right\|^2 \right], \qquad (1)
$$

where  $f$  denotes the feature extractor network,  $D$  and  $D$ represent the real and synthetic data distributions, respectively, x and  $\tilde{x}$  are samples drawn from  $\mathcal D$  and  $\tilde{\mathcal D}$ . However, MSE may not be ideal for comparing distributions, as it only considers direct feature comparisons in Euclidean space, neglecting important semantic information.

In another line, notable works employed Maximum Mean Discrepancy (MMD) to align high-order moments in the latent feature space [\[53,](#page-10-10) [55,](#page-10-2) [57\]](#page-10-11). Rigorously, MMD is defined to match moments within the Reproducing Kernel Hilbert Space (RKHS) induced by a selected kernel function. The MMD loss can be formulated as:

$$
\sup_{f \in \mathcal{F}} \left\| \mathbb{E}_{\mathbf{x} \sim \mathcal{D}} \left[ f(\mathbf{x}) \right] - \mathbb{E}_{\tilde{\mathbf{x}} \sim \tilde{\mathcal{D}}} \left[ f(\tilde{\mathbf{x}}) \right] \right\|^2,
$$
\n
$$
= \sup_{f \in \mathcal{F}} \left( \mathcal{K}_{\mathcal{D}, \mathcal{D}} + \mathcal{K}_{\tilde{\mathcal{D}}, \tilde{\mathcal{D}}} - 2\mathcal{K}_{\mathcal{D}, \tilde{\mathcal{D}}} \right),
$$
\n(2)

where  $\mathcal{K}_{\mathcal{D}, \tilde{\mathcal{D}}} = \mathbb{E}_{\boldsymbol{x} \sim \mathcal{D}, \tilde{\boldsymbol{x}} \sim \tilde{\mathcal{D}}} [\mathcal{K}_{f(\boldsymbol{x}), f(\tilde{\boldsymbol{x}})}]$  denotes the kernel function associated with feature extractor  $f$  in function class F. The choice of kernel function  $K$  is not unique and requires careful selection for MMD to be effective. However, instead of selecting certain kernel function, most DM-based methods [\[10,](#page-9-9) [55,](#page-10-2) [57\]](#page-10-11) align moments directly in the feature space, commonly approximated as:

$$
\mathcal{L}_{\text{MMD}} = \left\| \mathbb{E}_{\boldsymbol{x} \sim \mathcal{D}} \left[ f(\boldsymbol{x}) \right] - \mathbb{E}_{\boldsymbol{\tilde{x}} \sim \tilde{\mathcal{D}}} \left[ f(\boldsymbol{\tilde{x}}) \right] \right\|^2. \qquad (3)
$$

We argue that such empirical MMD estimates lack rigor, as they do not provide a maximal upper bound on the discrepancy, falling short of MMD's theoretical requirements.

### 4. Adversarial Distribution Matching

### 4.1. Minmax Framework

To address existing challenges with discrepancy measure selection, we propose a new approach that reformulates distribution matching as a minmax optimization problem. In

<span id="page-3-5"></span><span id="page-3-4"></span>Image /page/3/Figure/0 description: This figure illustrates a generative model that uses a feature network and a sampling network to learn a distribution. The process begins with a real dataset D and a synthetic dataset D-tilde. Both datasets are fed into a feature network, denoted by f, which outputs feature representations f(x) and f(x-tilde) respectively. These representations are then visualized as histograms in the complex plane, showing their distributions. The model defines a loss function L based on the difference between the characteristic functions of the real and synthetic data distributions, Phi\_x(t) and Phi\_x-tilde(t). The goal is to minimize L to match the real dataset distribution using the synthetic dataset. Simultaneously, a sampling network, denoted by psi, takes noise epsilon as input and generates samples t. The model aims to maximize L by learning a distribution that samples frequency arguments t. The figure also indicates the gradients used for updating the synthetic dataset and the sampling network. Specifically, a dashed red arrow represents the gradient for the synthetic dataset (nabla\_D-tilde L), and a dashed green arrow represents the gradient for the sampling network (nabla\_theta L).

Figure 4. Dataset Distillation with Neural Characteristic Function Matching (NCFM). Real and synthetic data points are sampled and embedded through a feature extractor network. The synthetic data is optimized by minimizing the distributional discrepancy between real and synthetic data, measured via the Neural Characteristic Function Discrepancy (NCFD) in the complex plane. Additionally, an auxiliary network learns an optimal sampling distribution for the frequency arguments of the characteristic function. Best viewed in color.

this framework, we maximize the discrepancy measure to define a robust discrepancy metric, parameterized by a neural network  $\psi$ . Concurrently, we minimize the discrepancy between the synthetic dataset  $\tilde{\mathcal{D}}$  and the real dataset  $\mathcal{D}$  by optimizing the synthetic data distribution  $\tilde{\mathcal{D}}$ . Formally, this minmax optimization problem is expressed as:

$$
\min_{\tilde{\mathcal{D}}} \max_{\psi} \mathcal{L}(\tilde{\mathcal{D}}, \mathcal{D}, f, \psi), \tag{4}
$$

where  $\mathcal L$  denotes the discrepancy measure,  $f$  is the feature extractor network, and  $\psi$  is the network learning the discrepancy metric. This minmax framework seeks the optimal synthetic data distribution  $\tilde{\mathcal{D}}$  that minimizes  $\mathcal{L}$  while network  $\psi$  maximizes  $\mathcal L$  to establish a robust metric.

#### 4.2. Neural Characteristic Function Matching

#### 4.2.1. Neural Characteristic Function Discrepancy

To define a suitable discrepancy metric within this minmax framework, we propose a novel discrepancy measure based on the characteristic function (CF), which enables direct and robust assessment of distributional discrepancies. Characteristic functions are a mainstay in probability theory, often used as an alternative to probability density functions due to their unique properties. Specifically, the CF of a random variable  $x$  is the expectation of its complex exponential transform, defined as:

$$
\Phi_{\boldsymbol{x}}(\boldsymbol{t}) = \mathbb{E}_{\boldsymbol{x}}\left[e^{j\langle \boldsymbol{t}, \boldsymbol{x}\rangle}\right] = \int_{\boldsymbol{x}} e^{j\langle \boldsymbol{t}, \boldsymbol{x}\rangle} dF(\boldsymbol{x}),\tag{5}
$$

where  $F(x)$  denotes the cumulative distribution function (cdf) of  $x, j = \sqrt{-1}$ , and t is the frequency argument. Since the cdf is not directly accessible in practice, we approximate the CF empirically as  $\Phi_{\bm{x}}(\bm{t}) = \frac{\hat{\bm{\lambda}}}{N} \sum_{i=1}^{N} e^{j\langle \bm{t}, \bm{x}_i \rangle},$ where  $N$  is the sample size in the dataset. The CF provides a theoretically principled description of a distribution, summarized in the following theorems.

#### Theorem 1 (Lévy's Convergence Theorem [[31\]](#page-9-16)) Let

 ${X_n}_{n=1}^{\infty}$  *be a sequence of random variables with char-* $\textit{acteristic functions} \ \Phi_n(t) \ = \ \mathbb{E}_{X_n} \left[ e^{j \langle t, X_n \rangle} \right]$ *.* Suppose

 $\Phi_n(t) \to \Phi(t)$  pointwise for each  $t \in \mathbb{R}^d$  as  $n \to \infty$ . If  $\Phi(t)$  *is continuous at*  $t = 0$ *, then there exists a random variable* X *with characteristic function*  $\Phi(t)$ *, and*  $X_n$ *converges in distribution to* X*.*

<span id="page-3-1"></span>Theorem 2 (Uniqueness for Characteristic Functions [\[14\]](#page-9-14)) *If two random variables* X *and* Y *have the same characteristic function,*  $\Phi_X(t) = \Phi_Y(t)$  *for all t, then* X *and* Y *are identically distributed. In other words, a characteristic function uniquely determines the distribution.*

By Theorems [1](#page-3-0) and [2,](#page-3-1) the empirical CF weakly converges to the population CF, ensuring that the CF serves as a reliable proxy for the underlying distribution. Based on the CF, we define our characteristic function discrepancy (CFD) as:

<span id="page-3-2"></span>
$$
\mathcal{C}_{\mathcal{T}}(\mathbf{x}, \tilde{\mathbf{x}}) = \int_{t} \sqrt{\frac{(\Phi_{\mathbf{x}}(t) - \Phi_{\tilde{\mathbf{x}}}(t))(\bar{\Phi}_{\mathbf{x}}(t) - \bar{\Phi}_{\tilde{\mathbf{x}}}(t))}{\text{Chf}(t)}} dF_{\mathcal{T}}(t),\tag{6}
$$

where  $\bar{\Phi}$  is the complex conjugate of  $\Phi$ , and  $F_{\mathcal{T}}(t)$  is the CDF of a sampling distribution on  $t$ . To simplify, we let  $\text{Chf}({\bm t}) = (\Phi_{\bm x}({\bm t}) {-} \Phi_{\bm{\tilde{x}}}(t)) (\bar{\Phi}_{\bm x}({\bm t}) {-} \bar{\Phi}_{\bm{\tilde{x}}}(t))$  for further analysis. We show that  $\mathcal{C}_{\mathcal{T}}(\mathbf{x}, \mathbf{x})$  defines a valid distance metric in the following theorem.

<span id="page-3-3"></span>Theorem 3 (CFD as a Distance Metric.) *The CF discrep*ancy  $C_{\mathcal{T}}(x, \tilde{x})$ , as defined in Eq. [\(6\)](#page-3-2), serves as a dis*tance metric between*  $x$  *and*  $\tilde{x}$  *when the support of*  $T$  *resides in Euclidean space. It satisfies the properties of nonnegativity, symmetry, and the triangle inequality.*

<span id="page-3-0"></span>Theorem [3](#page-3-3) establishes that CFD is a valid distance metric. Furthermore, we demonstrate that this formulation decomposes CFD into phase,  $a_x(t)$ , and amplitude,  $|\Phi_x(t)|$ , com<span id="page-4-1"></span>ponents through Euler's formula:

$$
Chf(t) = |\Phi_x(t)|^2 + |\Phi_{\tilde{x}}(t)|^2 - |\Phi_x(t)| |\Phi_{\tilde{x}}(t)| (2 \cos(a_x(t) - a_{\tilde{x}}(t))))
$$

$$
= \underbrace{(|\Phi_x(t) - \Phi_{\tilde{x}}(t)|)^2}_{\text{amplitude difference}} (7)
$$

$$
+ 2 |\Phi_x(t)| |\Phi_{\tilde{x}}(t)| \underbrace{(1 - \cos(a_x(t) - a_{\tilde{x}}(t)))}_{\text{phase difference}},
$$

• *Phase Information:* the term  $1 - \cos(a_x(t) - a_{\tilde{x}}(t))$  represents the phase, encoding data centres crucial for *realism*. • *Amplitude Information:* the term  $\left|\Phi_x(t) - \Phi_{\tilde{x}}(t)\right|^2$  captures the distribution scale, contributing to the *diversity*.

This phase-amplitude decomposition, supported in signal processing [\[32,](#page-10-13) [35\]](#page-10-14) and generative models [\[27\]](#page-9-19), provides insight into CFD's descriptive power. In practice, to reduce computational cost, we furehr introduce an additional feature extractor  $f$  to map input variables into a latent space, which is similar to previous works in distribution matching  $[10, 26, 55, 57]$  $[10, 26, 55, 57]$  $[10, 26, 55, 57]$  $[10, 26, 55, 57]$  $[10, 26, 55, 57]$  $[10, 26, 55, 57]$  $[10, 26, 55, 57]$ . We also use a parameterized sampling network  $\psi$  to obtain the distribution of frequency argument  $t$ , thereby extending the CFD to a more general parameterized form, *i.e.*, *Neural Characteristic Function Discrepancy (NCFD)* as  $\mathcal{C}_{\mathcal{T}}(\mathbf{x}, \tilde{\mathbf{x}}; f, \psi) =$  $\int_{t} \sqrt{\text{Chf}(t; f)} dF_{\mathcal{T}}(t; \psi)$ , where  $\text{Chf}(t; f)$  is defined as  $\left(\left|\Phi_{f(\bm{x})}(\bm{t})-\Phi_{f(\bm{\tilde{x}})}(\bm{t})\right|\right)^2 + 2\left|\Phi_{f(\bm{x})}(\bm{t})\right|\left|\Phi_{f(\bm{\tilde{x}})}(\bm{t})\right|\left(1-\right)$  $\cos(\boldsymbol{a}_{f(\boldsymbol{x})}(\boldsymbol{t}) - \boldsymbol{a}_{f(\boldsymbol{\tilde{x}})}(\boldsymbol{t}))).$ 

#### 4.2.2. Determining the sampling strategy for NCFD

The core aspect in optimizing  $\mathcal{C}_{\mathcal{T}}(\mathbf{x}, \tilde{\mathbf{x}}; f, \psi)$  lies in determining the form of  $F_{\mathcal{T}}(t; \psi)$ , *i.e.*, how to correctly and efficiently sample  $t$  from a carefully picked distribution. Similar with works in generative adversarial net-work [\[1,](#page-9-18) [28\]](#page-9-23), we define  $F_{\mathcal{T}}(t)$  as the cumulative distribution function (cdf) of a *scale mixture of normals*, as  $p_{\mathcal{T}}(t)$  =  $\int_{\Sigma} \mathcal{N}(t|0,\Sigma) p_{\Sigma}(\Sigma) d\Sigma$ , where  $p_{\mathcal{T}}(t)$  is the probability density function (pdf) of  $F_{\mathcal{T}}(t)$ ,  $\mathcal{N}(t|0, \Sigma)$  denotes a zeromean Gaussian distribution with covariance  $\Sigma$ , and  $p_{\Sigma}(\Sigma)$ represents the distribution of Σ. We observe that as the number of sampled frequency arguments increases, the approximation of the empirical CF improves, as guaranteed by Lévy's Convergence Theorem  $[31]$  $[31]$ , ultimately leading to higher quality synthetic data.

#### 4.2.3. Distribution Matching with NCFD

Given the NCFD measure  $\mathcal{C}_{\mathcal{T}}(\mathbf{x}, \tilde{\mathbf{x}}; f, \psi)$ , we now propose a method to utilize NCFD for distribution matching, termed as Neural Characteristic Function Matching (NCFM). A visual illustration of the NCFM pipeline is provided in Figure [4.](#page-3-4) On one hand, we maximize the NCFD to learn an effective discrepancy metric by optimizing the network  $\psi$ . On the other hand, we minimize this learned NCFD to obtain an optimal synthetic dataset,  $D$ . In practice, we introduce a hyper-parameter  $\alpha$  to balance the amplitude and

phase information in the NCFD, then the minmax optimization problem can be formulated as:

<span id="page-4-0"></span>
$$
\min_{\tilde{\mathcal{D}}} \max_{\psi} \mathcal{L}(\tilde{\mathcal{D}}, \mathcal{D}, f, \psi) = \min_{\tilde{\mathcal{D}}} \max_{\psi} \mathbb{E}_{\mathbf{x} \sim \mathcal{D}, \tilde{\mathbf{x}} \sim \tilde{\mathcal{D}}} \mathcal{C}_{\mathcal{T}}(\mathbf{x}, \tilde{\mathbf{x}}; f, \psi)
$$

$$
= \min_{\tilde{\mathcal{D}}} \max_{\psi} \mathbb{E}_{\mathbf{x} \sim \mathcal{D}, \tilde{\mathbf{x}} \sim \tilde{\mathcal{D}}} \int_{t} \sqrt{\text{Chf}(\mathbf{t}; f)} \, dF_{\mathcal{T}}(\mathbf{t}; \psi)
$$
where  $\text{Chf}(\mathbf{t}; f) = \alpha \left( \left( \left| \Phi_{f(\mathbf{x})}(\mathbf{t}) - \Phi_{f(\tilde{\mathbf{x}})}(\mathbf{t}) \right| \right)^{2} \right) + (1 - \alpha) \cdot (2 \left| \Phi_{f(\mathbf{x})}(\mathbf{t}) \right| \left| \Phi_{f(\tilde{\mathbf{x}})}(\mathbf{t}) \right|) \cdot (1 - \cos(\mathbf{a}_{f(\mathbf{x})}(\mathbf{t}) - \mathbf{a}_{f(\tilde{\mathbf{x}})}(\mathbf{t})))))$ (8)

For the design of  $f$ , we used a hybrid approach that combines a pre-trained model with a randomly initialized model, both selected from a subset of trained models. This ensures that the feature extractor remains moderately diverse yet discriminative. The hybrid feature extractor is constructed by  $\beta$ -blending the checkpoints of the initial and final models, where each model is chosen from a specific subset of available models. At each distillation step, the blending coefficient  $\beta \in (0, 1)$  is sampled from a uniform distribution  $U(0, 1)$ , providing a balanced combination of initial and final checkpoints. Our NCFM can be seamlessly integrated with additional data curation steps, such as generating soft labels with a pre-trained neural network and performing dataset finetuning. Unlike prior methods that focus on learning soft labels [\[16,](#page-9-17) [19,](#page-9-24) [36\]](#page-10-15), NCFM simply leverages a pre-trained network to efficiently generate soft labels for the distilled dataset, improving both efficiency and effectiveness. However, these additional curation steps are not essential for NCFM, as it can achieve SOTA performance within the pure minmax framework.

### 5. Experiments

### 5.1. Setup

Baseline methods. We compared NCFM with several representative approaches in dataset distillation and coreset selection. These include gradient-matching methods such as DC [\[56\]](#page-10-3), DCC [\[24\]](#page-9-8), DSA and DSAC [\[54\]](#page-10-8). Kernel-based methods like KIP [\[34\]](#page-10-16) and FrePo [\[58\]](#page-10-17) were also included. Distribution-matching methods like CAFE [\[47\]](#page-10-7), DM [\[55\]](#page-10-2), IDM [\[57\]](#page-10-11), M3D [\[53\]](#page-10-10), IID [\[10\]](#page-9-9), and DSDM [\[26\]](#page-9-22) were part of the evaluation. We also included trajectory-matching methods such as MTT [\[6\]](#page-9-0), FTD [\[12\]](#page-9-21), ATT [\[30\]](#page-9-25), and TESLA [\[9\]](#page-9-20). *State-of-the-art* methods like DATM [\[16\]](#page-9-17), G-VBSM [\[40\]](#page-10-18), and RDED [\[45\]](#page-10-19) were also considered in our comparisons. Additionally, we benchmarked our method against classical coreset selection techniques, including random selection, Herding [\[50\]](#page-10-20), and Forgetting [\[46\]](#page-10-21).

Datasets and Networks. Our evaluations were conducted on widely-used datasets, including CIFAR-10 and CIFAR-100 [\[22\]](#page-9-26) with resolution of 32×32, Tiny ImageNet [\[23\]](#page-9-27) with resolution of 64×64, and ImageNet subsets with resolution of 128×128, *i.e.*, ImageNette, ImageWoof, ImageFruit, Im-

<span id="page-5-2"></span><span id="page-5-1"></span>

| Dataset              |                | $CIFAR-10$                  |                |                | $CIFAR-100$    |                             | Tiny ImageNet  |                |                |  |
|----------------------|----------------|-----------------------------|----------------|----------------|----------------|-----------------------------|----------------|----------------|----------------|--|
| <b>IPC</b>           | $\mathbf{1}$   | 10                          | 50             | $\mathbf{1}$   | 10             | 50                          | $\mathbf{1}$   | 10             | 50             |  |
| Ratio $(\%)$         | 0.02           | 0.2                         | 1              | 0.2            | 2              | 10                          | 0.2            | $\overline{c}$ | 10             |  |
| Random               | $14.4 \pm 2.0$ | $26.0 \pm 1.2$              | $43.4 \pm 1.0$ | $4.2 \pm 0.3$  | $14.6 \pm 0.5$ | $30.0 + 0.4$                | $1.4 + 0.1$    | $5.0 + 0.2$    | $15.0 + 0.4$   |  |
| Herding              | $21.5 \pm 1.2$ | $31.6 \pm 0.7$              | $40.4 \pm 0.6$ | $8.4 + 0.3$    | $17.3 \pm 0.3$ | $33.7{\pm}0.5$              | $2.8 + 0.2$    | $6.3 + 0.2$    | $16.7{\pm}0.3$ |  |
| Forgetting           | $13.5 \pm 1.2$ | $23.3 \pm 1.0$              | $23.3 \pm 1.1$ | $4.5 \pm 0.2$  | $15.1 \pm 0.3$ | $30.5 \pm 0.3$              | $1.6 \pm 0.1$  | $5.1 \pm 0.2$  | $15.0 + 0.3$   |  |
| DC                   | $28.3 + 0.5$   | $44.9 \pm 0.5$              | $53.9 + 0.5$   | $12.8 + 0.3$   | $25.2 \pm 0.3$ |                             |                |                |                |  |
| <b>DSA</b>           | $28.8 + 0.7$   | $52.1 \pm 0.5$              | $60.6 \pm 0.5$ | $13.9 \pm 0.3$ | $32.3 \pm 0.3$ | $42.8 \pm 0.4$              | ۰              |                |                |  |
| <b>DCC</b>           | $32.9 \pm 0.8$ | $49.4 \pm 0.5$              | $61.6 \pm 0.4$ | $13.3 \pm 0.3$ | $30.6 + 0.4$   | $40.0 \pm 0.3$              |                |                |                |  |
| <b>DSAC</b>          | $34.0 \pm 0.7$ | $54.5 \pm 0.5$              | $64.2 \pm 0.4$ | $14.6 \pm 0.3$ | $14.6 \pm 0.3$ | $39.3{\pm}0.4$              |                |                |                |  |
| FrePo                | $46.8 \pm 0.7$ | $65.5 \pm 0.4$              | $71.7 \pm 0.2$ | $28.7 + 0.1$   | $42.5 \pm 0.2$ | $44.3 \pm 0.2$              | $15.4 \pm 0.3$ | $25.4 \pm 0.2$ |                |  |
| <b>MTT</b>           | $46.3 \pm 0.8$ | $65.3 \pm 0.7$              | $71.6 \pm 0.2$ | $24.3 \pm 0.3$ | $40.1 \pm 0.4$ | $47.7 \pm 0.2$              | $8.8 + 0.3$    | $23.2 \pm 0.2$ | $28.0 + 0.3$   |  |
| <b>ATT</b>           | $48.3 \pm 1.0$ | $67.7{\pm}0.6$              | $74.5 \pm 0.4$ | $26.1 \pm 0.3$ | $44.2 \pm 0.5$ | $51.2 \pm 0.3$              | $11.0 + 0.5$   | $25.8 \pm 0.4$ |                |  |
| <b>FTD</b>           | $46.8 + 0.3$   | $66.6{\scriptstyle \pm0.3}$ | $73.8 + 0.2$   | $25.2 \pm 0.2$ | $43.4 \pm 0.3$ | $48.5 \pm 0.3$              | $10.4 \pm 0.3$ | $24.5 \pm 0.2$ |                |  |
| <b>TESLA</b>         | $48.5 \pm 0.8$ | $66.4{\pm}0.8$              | $72.6 \pm 0.7$ | $24.8 \pm 0.4$ | $41.7 \pm 0.3$ | $47.9 \pm 0.3$              |                |                |                |  |
| <b>CAFE</b>          | $30.3 \pm 1.1$ | $46.3 \pm 0.6$              | $55.5 \pm 0.6$ | $12.9 + 0.3$   | $27.8 \pm 0.3$ | $37.9 \pm 0.3$              |                |                |                |  |
| DM                   | $26.0 + 0.8$   | $48.9 + 0.6$                | $63.0 + 0.4$   | $11.4 \pm 0.3$ | $29.7 \pm 0.3$ | $43.6 \pm 0.4$              | $3.9 + 0.2$    | $12.9 + 0.4$   | $24.1 \pm 0.3$ |  |
| <b>IDM</b>           | $45.6 \pm 0.7$ | $58.6 \pm 0.1$              | $67.5 \pm 0.1$ | $20.1 \pm 0.3$ | $45.1 \pm 0.1$ | $50.0 + 0.2$                | $10.1 \pm 0.2$ | $21.9 \pm 0.2$ | $27.7 \pm 0.3$ |  |
| M3D                  | $45.3 \pm 0.3$ | $63.5 \pm 0.2$              | $69.9 \pm 0.5$ | $26.2 \pm 0.3$ | $42.4 \pm 0.2$ | $50.9 + 0.7$                |                |                |                |  |
| $_{\text{IID}}$      | $47.1 \pm 0.1$ | $59.9 \pm 0.1$              | $69.0{\pm}0.3$ | $24.6 \pm 0.1$ | $45.7 \pm 0.4$ | $51.3 \pm 0.4$              | ٠              |                |                |  |
| <b>DSDM</b>          | $45.0 \pm 0.4$ | $66.5 \pm 0.3$              | $75.8 \pm 0.3$ | $19.5 \pm 0.2$ | $46.2 \pm 0.3$ | $54.0 \pm 0.2$              | ۰              |                |                |  |
| G-VBSM               |                | $46.5 \pm 0.7$              | $54.3 \pm 0.3$ | $16.4 \pm 0.7$ | $38.7{\pm}0.2$ | $45.7 \pm 0.4$              |                |                |                |  |
| <b>NCFM</b> (Ours)   | $49.5 \pm 0.3$ | $71.8 + 0.3$                | $77.4 \pm 0.3$ | $34.4 \pm 0.5$ | $48.7 \pm 0.3$ | $54.7{\scriptstyle \pm0.2}$ | $18.2 \pm 0.5$ | $26.8 + 0.6$   | $29.6 \pm 0.5$ |  |
| <b>Whole Dataset</b> |                | $84.8 \pm 0.1$              |                |                | $56.2 \pm 0.3$ |                             |                | $37.6 \pm 0.4$ |                |  |

Table 1. Results of NCFM on CIFAR-10/100, and Tiny ImageNet (resolution of 64×64) datasets.

ageMeow, ImageSquawk, and ImageYellow [\[18\]](#page-9-28). Following prior studies [\[16,](#page-9-17) [48\]](#page-10-22), we used networks with instance normalization as the default setting. Specifically, dataset distillation is performed with a 3-layer ConvNet for CIFAR-10/100, a 4-layer ConvNet for Tiny ImageNet, and a 5-layer ConvNet for ImageNet subsets. All experiments were conducted with 10 evaluations for fairness, primarily using a single NVIDIA 4090 GPU.

Other Settings. Following prior works, we implemented differential augmentation [\[47,](#page-10-7) [54\]](#page-10-8) and applied multiformation parameterization with a scale factor of  $\rho = 2$ for images, as in [\[20,](#page-9-7) [57\]](#page-10-11). We employed AdamW as our optimizer. In our setup, we set the number of sampled frequency arguments to 1024. The number of mixture Gaussian components in the sampling network is set to the number of frequency arguments divided by 16, balancing the sampling network diversity and computational efficiency. Further details are provided in the supplementary material.

#### 5.2. Main Results

We verified the effectiveness of NCFM on various bench-mark datasets of different image-per-class (IPC) settings<sup>[1](#page-5-0)</sup>. CIFAR-10/100 and Tiny ImageNet. As shown in Table [1,](#page-5-1) NCFM outperforms all state-of-the-art (SOTA) baselines. Specifically, it surpasses distribution matching methods using traditional metrics like MSE and MMD, achieving improvements of 23.5% and 23.0% on CIFAR-10 and CIFAR-100 with 1 IPC compared to DM [\[55\]](#page-10-2). Additionally, NCFM maintains SOTA performance even against computationally intensive methods like MTT [\[6\]](#page-9-0). Results for larger IPC settings and comparisons with other SOTA methods like DATM [\[16\]](#page-9-17) are in the supplementary material.

Higher-resolution Datasets. We also evaluated NCFM on larger datasets, specifically the ImageNet subsets. As shown in Table [2,](#page-6-0) NCFM demonstrates strong performance across these challenging benchmarks. In 10 IPC setting, our method achieves substantial improvements of 20.5% on ImageSquawk, compared to the baseline MTT [\[6\]](#page-9-0). Remarkably, NCFM exhibits robust performance under relatively small IPC. For instance, compared to RDED [\[45\]](#page-10-19), NCFM yields a significant improvement of 19.6% on ImageNette.

Computational Efficiency Evaluation. We tested the training speed and GPU memory of our NCFM compared with *strong* baseline methods on different datasets. As conventional recognition, trajectory matching based methods usually achieve better results than distribution matching in practice [\[6,](#page-9-0) [9,](#page-9-20) [12\]](#page-9-21). However, both superior training efficiency and GPU memory efficiency are observed in NCFM across all benchmark datasets, while achieving better results. Specifically, we measured the average training time over 1000 distillation iterations for each method, as summarized in Table [3.](#page-6-1) For CIFAR-100 at IPC 50, NCFM achieves nearly  $30\times$  faster speeds compared to TESLA [\[9\]](#page-9-20) without the sampling network, and maintains over  $20\times$  faster speeds with the sampling network included. Moreover, we conducted a comprehensive analysis of computational efficiency, where GPU memory is expressed as the peak memory usage during 1000 iterations of training, as shown in Table [3.](#page-6-1) While most existing methods encounter out of memory (OOM) issues at IPC = 50, our method requires only

<span id="page-5-0"></span><sup>&</sup>lt;sup>1</sup>We provide further results on continual learning, neural architecture search, and larger IPC datasets in the supplementary material.

<span id="page-6-3"></span><span id="page-6-0"></span>

| Dataset<br>IPC<br>Ratio (%) | ImageNette   |              | ImageWoof    |              | ImageFruit   |              | ImageMeow    |              | ImageSquawk  |              | ImageYellow  |              |
|-----------------------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
|                             | 1<br>0.105   | 10<br>1.050  | 1<br>0.110   | 10<br>1.100  | 1<br>0.077   | 10<br>0.77   | 1<br>0.077   | 10<br>0.77   | 1<br>0.077   | 10<br>0.77   | 1<br>0.077   | 10<br>0.77   |
| Random                      | $23.5  4.8$ | $47.7  2.4$ | $14.2  0.9$ | $27.0  1.9$ | $13.2  0.8$ | $21.4  1.2$ | $13.8  0.6$ | $29.0  1.1$ | $21.8  0.5$ | $40.2  0.4$ | $20.4  0.6$ | $37.4  0.5$ |
| MTT                         | $47.7  0.9$ | $63.0  1.3$ | $28.6  0.8$ | $35.8  1.8$ | $26.6  0.8$ | $40.3  1.3$ | $30.7  1.6$ | $40.4  2.2$ | $39.4  1.5$ | $52.3  1.0$ | $45.2  0.8$ | $60.0  1.5$ |
| DM                          | $32.8  0.5$ | $58.1  0.3$ | $21.1  1.2$ | $31.4  0.5$ | -            | -            | -            | -            | $31.2  0.7$ | $50.4  1.2$ | -            | -            |
| RDED                        | $33.8  0.8$ | $63.2  0.7$ | $18.5  0.9$ | $40.6  2.0$ | -            | -            | -            | -            | -            | -            | -            | -            |
| <b>NCFM</b> (Ours)          | $53.4  1.6$ | $77.6  1.0$ | $27.2  1.1$ | $48.4  1.3$ | $29.2  0.7$ | $44.8  1.5$ | $34.6  0.7$ | $58.2  1.2$ | $41.6  1.2$ | $72.8  0.9$ | $46.6  1.5$ | $74.2  1.4$ |
| Whole Dataset               |              | $87.4  1.0$ | $67.0  1.3$ |              |              | $63.9  2.0$ | $66.7  1.1$ |              |              | $87.5  0.3$ |              | $84.4  0.6$ |

Table 2. Results on ImageNet subsets (resolution of 128×128) when employing NCFM across different IPCs.

<span id="page-6-1"></span>Table 3. Training speed (s/iter) and peak GPU memory (GB) comparison on a single NVIDIA A100 80G. OOM marks out-ofmemory cases. 'Reduction' shows NCFM's speed and memory improvements over the best-performing baseline in the table.

| Resource        | Speed (s/iter)                      |                                     | GPU Memory (GB)                     |                                     |                                     |                                     | IPC                                 | Method                              | ConvNet                             | AlexNet     | VGG         | ResNet      |             |             |
|-----------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------|-------------|-------------|-------------|-------------|
|                 | Dataset                             | IPC                                 | CIFAR-100                           | Tiny ImageNet                       | CIFAR-100                           | Tiny ImageNet                       | 10                                  | DSA                                 | $52.1 ±0.4$                         | $35.9 ±1.3$ | $43.2 ±0.5$ | $35.9 ±1.3$ |             |             |
|                 | 10                                  | 50                                  | 10                                  | 50                                  | 10                                  | 50                                  |                                     | MTT                                 | $64.3 ±0.7$                         | $34.2 ±2.6$ | $50.3 ±0.8$ | $34.2 ±2.6$ |             |             |
| <b>MTT</b>      | 1.92                                | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | 61.6                                | <span style="color:red;">OOM</span> |                                     | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | KIP         | $47.6 ±0.9$ | $24.4 ±3.9$ | $42.1 ±0.4$ | $24.4 ±3.9$ |
| <b>FTD</b>      | 1.68                                | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | 61.4                                | <span style="color:red;">OOM</span> |                                     | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | <b>NCFM</b> | $71.8 ±0.3$ | $67.9 ±0.5$ | $68.0 ±0.3$ | $67.7 ±0.5$ |
| <b>TESLA</b>    | 5.71                                | 28.24                               | 42.01                               | <span style="color:red;">OOM</span> | 10.3                                | 44.2                                | 69.6                                | <span style="color:red;">OOM</span> | 50                                  | DSA         | $59.9 ±0.8$ | $53.3 ±0.7$ | $51.0 ±1.1$ | $47.3 ±1.0$ |
| <b>DATM</b>     | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> | <span style="color:red;">OOM</span> |                                     | DM          | $65.2 ±0.4$ | $61.3 ±0.6$ | $59.9 ±0.8$ | $57.0 ±0.9$ |
| NCFM w/o $\psi$ | 0.73                                | 0.96                                | 2.40                                | 5.67                                | 1.4                                 | 1.9                                 | 6.4                                 | 8.4                                 |                                     | <b>NCFM</b> | $77.4 ±0.3$ | $75.5 ±0.3$ | $75.5 ±0.3$ | $73.8 ±0.2$ |
| Reduction       | $2.3\times$                         | $29.4\times$                        | $17.5\times$                        | -                                   | $7.4\times$                         | $23.3\times$                        | $10.9\times$                        | -                                   |                                     |             |             |             |             |             |
| <b>NCFM</b>     | 1.33                                | 1.36                                | 3.27                                | 7.22                                | 1.6                                 | 2.0                                 | 6.5                                 | 8.7                                 |                                     |             |             |             |             |             |
| Reduction       | $1.3\times$                         | $20.8\times$                        | $12.8\times$                        | -                                   | $6.4\times$                         | $22.1\times$                        | $10.7\times$                        | -                                   |                                     |             |             |             |             |             |

about 1.9GB GPU memory on CIFAR-100. This further demonstrates the exceptional scalability of our approach under high IPC conditions. Further results on CIFAR-10 are provided in the supplementary material.

Cross-Architecture Generalization. We evaluated the cross-architecture generalization capability of our method by testing its performance on various network architectures, including AlexNet [\[22\]](#page-9-26), VGG-11 [\[42\]](#page-10-23), and ResNet-18 [\[17\]](#page-9-29). In this evaluation, synthetic data were condensed using a 3-layer ConvNet, and each method was subsequently tested across different architectures to assess robustness and adaptability. Tables [4](#page-6-2) summarize the results on CIFAR-10 with 10 and 50 IPC settings, respectively. In both cases, NCFM consistently outperformed other methods across all architectures, demonstrating its strong ability to generalize effectively even when trained on a different architecture. Results on other backbone networks beyond ConvNet are provided in the supplementary material.

### 5.3. Ablation Study

#### 5.3.1. Effect of the Sampling Network

To rigorously evaluate the impact of the sampling network,  $\psi$ , within the minmax paradigm of NCFM, we conducted performance comparisons with and without this component. To ensure a controlled and fair assessment, no additional data curation techniques were applied (such as fine-tuning or soft label integration). As shown in Table [5,](#page-7-0) employing the sampling network  $\psi$  yields substantial improvements in

<span id="page-6-2"></span>Table 4. Cross-architecture generalization performance (%) on CIFAR-10. The synthetic data is condensed using ConvNet, and each method is evaluated on different architectures.

synthetic data quality across various datasets. For example, integrating  $\psi$  into our method provides a 3.2% performance increase on CIFAR-10 at 50 IPC. Our method yields a 2.6% performance increase on Tiny ImageNet at 1 IPC and 10.1% at 10 IPC. Similar trends are observed across ImageNet subsets, including gains of 2.8% on ImageMeow and 2.0% on ImageSquawk. The strong performance benefits from sampling network  $\psi$  emphasize the effectiveness of the minmax paradigm compared to straightforward CFD minimization.

#### 5.3.2. Impact of Amplitude and Phase Components

We examine individual contributions of amplitude and phase alignment within the NCFD measure. By selectively adjusting amplitude or phase alignment, controlled by the hyperparameter  $\alpha$  that represents the ratio of amplitude to phase weight in the loss function, we find that both components are essential. To further evaluate the effect of  $\alpha$  on performance, we conducted ablation studies on the CIFAR-10 and CIFAR-100 datasets. As noted in prior works [\[32,](#page-10-13) [35\]](#page-10-14), the amplitude term primarily enhances the diversity of generated data, while the phase term contributes to realism by accurately capturing data centers. For example, as shown in Figure [5,](#page-7-1) on CIFAR-10 with 10 IPC, when the amplitude information dominates the loss (*e.g.*,  $\alpha = 0.999$ ), the test accuracy decreases about 3% compared to our best results. Conversely, when the phase information dominates (*e.g.*,  $\alpha = 0.001$ ), the test accuracy decreases by about 1%. Results demonstrate that a balanced integration of both components yields the highest accuracy.

<span id="page-7-4"></span><span id="page-7-0"></span>Table 5. Test Performance (%) on CIFAR-10, CIFAR-100, Tiny ImageNet and ImageNet subsets with and without the sampling network  $\psi$ . We find that sampling network  $\psi$  significantly improves performance, even without additional data curation steps.

| Dataset<br>IPC  | CIFAR-10 |      | CIFAR-100 |      | Tiny ImageNet |      |      | ImageFruit<br>10 | ImageMeow<br>10 | ImageSquawk<br>10 | ImageYellow<br>10 |
|-----------------|----------|------|-----------|------|---------------|------|------|------------------|-----------------|-------------------|-------------------|
|                 | 10       | 50   | 10        | 50   | 1             | 10   | 50   |                  |                 |                   |                   |
| NCFM w/o $\psi$ | 65.6     | 74.2 | 45.9      | 53.7 | 9.4           | 14.2 | 22.0 | 39.6             | 51.6            | 68.8              | 67.6              |
| <b>NCFM</b>     | 68.9     | 77.4 | 48.7      | 54.4 | 12.0          | 24.3 | 26.5 | 41.4             | 54.4            | 70.8              | 69.2              |

<span id="page-7-1"></span>Image /page/7/Figure/2 description: This figure displays four line graphs comparing accuracy percentages on the y-axis against the ratio alpha on the x-axis. The graphs are categorized by dataset (CIFAR-10 and CIFAR-100) and number of inference per class (1 IPC and 10 IPC). For CIFAR-10 with 1 IPC, the accuracy ranges from approximately 35% to 44%, with a baseline of 26.8 and an amplitude to phase ratio of 9/1. For CIFAR-10 with 10 IPC, the accuracy ranges from about 64% to 66.5%, with a baseline of 48.9 and an amplitude to phase ratio of 4/3. For CIFAR-100 with 1 IPC, the accuracy ranges from roughly 20% to 27%, with a baseline of 11.4 and an amplitude to phase ratio of 9/1. For CIFAR-100 with 10 IPC, the accuracy ranges from approximately 43% to 46.5%, with a baseline of 29.7 and an amplitude to phase ratio of 4/1. Arrows are present in each graph indicating a trend.

Figure 5. Impact of amplitude and phase components in the NCFD measure across various datasets and IPC settings. The figure illustrates the relationship between the amplitude-to-phase ratio  $\alpha$ in Eq. [\(8\)](#page-4-0). Results indicate that balancing amplitude (for diversity) and phase (for realism) information leads to improved performance. Baseline results were obtained using DM [\[55\]](#page-10-2).

#### 5.3.3. Effect of the Number of Sampled Frequency Arguments in NCFD

To assess the impact of the number of sampled frequency arguments, t, generated by the sampling network  $\psi$ , we varied the sample count and measured the corresponding performance. As illustrated in Figure [6,](#page-7-2) increasing the number of sampled arguments initially enhances the quality of synthetic data by facilitating finer distributional alignment. For example, accuracy on CIFAR-10 at 10 IPC improves from 62% with 16 sampled frequency arguments to approximately 67 % with 1024, indicating a positive correlation between the sampled number and accuracy. However, beyond 1024 arguments, performance gains plateau, with accuracy stabilizing around 67-68% even as the sampling number increases to 4096. This trend suggests that a moderate number achieves an optimal balance between computational efficiency and accuracy. We observed that additional cost remains *minimal* as the number of sampled arguments increases, underscoring NCFM's ability to produce highquality synthetic data with low computational cost.

<span id="page-7-2"></span>Image /page/7/Figure/6 description: A bar chart shows the relationship between the number of sampled items and accuracy. The x-axis is labeled "#Sampled t" and ranges from 16 to 4096, with values 16, 32, 64, 128, 256, 512, 1024, 2048, and 4096. The y-axis is labeled "Accuracy (%)" and ranges from 60 to 68. The bars increase in height as the number of sampled items increases. The accuracy for 16 sampled items is approximately 61.5%, for 32 items is approximately 63%, for 64 items is approximately 64.2%, for 128 items is approximately 65%, for 256 items is approximately 65.2%, for 512 items is approximately 65.7%, for 1024 items is approximately 66.8%, for 2048 items is approximately 67.2%, and for 4096 items is approximately 67.2%.

Figure 6. Impact of sampled frequency count in NCFD on accuracy across datasets and IPC. Increasing frequencies improves accuracy up to a threshold, beyond which gains diminish.

### 6. Discussion

### 6.1. Training stability of NCFD

The training stability of our minmax paradigm is crucial to its effectiveness. Unlike traditional discrepancy measures, NCFM operates within the complex plane to conduct minmax optimization. While instability is a common issue in minmax adversarial optimization, as seen in generative adversarial networks [\[2,](#page-9-30) [37,](#page-10-24) [39\]](#page-10-25), NCFM consistently maintains stable optimization throughout training, as illustrated in Figure [7.](#page-7-3) This stability is further supported by theoretical guarantees of weak convergence in Theorem [1,](#page-3-0) demonstrating the robustness of the CF-based discrepancy under diverse conditions and contributing to NCFM's reliable convergence across datasets.

<span id="page-7-3"></span>Image /page/7/Figure/11 description: This is a line graph showing the loss over iterations for CIFAR-100 and CIFAR-10 datasets with 10 and 50 IPC (images per class). The x-axis represents the iteration number, ranging from 0 to 10000. The y-axis represents the loss, ranging from 0.8 to 1.4. The graph displays four lines: CIFAR-100 (10 IPC) in teal, CIFAR-100 (50 IPC) in red, CIFAR-10 (10 IPC) in green, and CIFAR-10 (50 IPC) in orange. All lines show a decreasing trend in loss as the iterations increase, with CIFAR-100 generally having a higher loss than CIFAR-10. The CIFAR-10 (50 IPC) line shows the lowest loss, stabilizing around 0.8, while CIFAR-100 (10 IPC) shows the highest loss, stabilizing around 1.0.

Figure 7. Training dynamics of the minmax optimization process across different datasets and various IPC settings.

#### 6.2. Correlation between CFD and MMD

To better understand NCFM, we examine the relationship between the Characteristic Function Discrepancy (CFD) and Maximum Mean Discrepancy (MMD).

CF as Well-Behaved Kernels in the MMD Metric. The CF discrepancy term  $\int_{\mathbf{t}} \sqrt{\text{Chf}(\mathbf{t}; f)} dF_{\mathcal{T}}(\mathbf{t})$  in our loss can be viewed as a well-behaved kernel in MMD, specifically as a *Characteristic Kernel* [\[43\]](#page-10-26). Unlike MMD, which relies on fixed kernels, NCFM adaptively learns  $F_{\mathcal{T}}(t)$ , enabling flexible kernel selection for optimal distribution alignment. Furthermore, mixtures of Gaussian distributions within the CF framework produce well-defined characteristic kernels. When MMD employs a characteristic kernel of the form  $\int_{t} e^{-j\langle t, x - \tilde{x} \rangle} dF_{\mathcal{T}}(t)$ , it aligns with the structure of CFD, demonstrating that *MMD is a special case of CFD* when only specific moments are matched. This insight also explains the minimal memory overhead observed as IPC grows, highlighting the efficiency of our approach.

Computational Advantage of CFD over MMD. In contrast to MMD, which requires *quadratic* time in the number <span id="page-8-0"></span>of samples for approximate computation, CFD operates in *linear* time relative to the sampling number of frequency arguments, which aligns results in [\[1\]](#page-9-18). This efficiency makes CFD substantially faster and more scalable than MMD, offering a particular advantage for large-scale datasets.

# 7. Conclusion

In this work, we redefined distribution matching for dataset distillation as a minmax optimization problem and introduced Neural Characteristic Function Discrepancy (NCFD), a novel and theoretically grounded metric designed to maximize the separability between real and synthetic data. Leveraging the Characteristic Function (CF), our method dynamically adjusts NCFD to align both phase and amplitude information in the complex plane, achieving a balance between realism and diversity. Extensive experiments demonstrated the computational efficiency of our approach, which achieves state-of-the-art performance with minimal computational overhead, showcasing its scalability and practicality for large-scale applications.

# References

- <span id="page-9-18"></span>[1] Abdul Fatir Ansari, Jonathan Scarlett, and Harold Soh. A characteristic function approach to deep implicit generative modeling. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 7478–7487, 2020. [3,](#page-2-0) [5,](#page-4-1) [9](#page-8-0)
- <span id="page-9-30"></span>[2] Martin Arjovsky and Léon Bottou. Towards principled methods for training generative adversarial networks. *arXiv preprint arXiv:1701.04862*, 2017. [8](#page-7-4)
- <span id="page-9-12"></span>[3] Patrick Billingsley. *Probability and measure*. John Wiley & Sons, 2017. [2,](#page-1-2) [3](#page-2-0)
- <span id="page-9-10"></span>[4] Mikołaj Bińkowski, Danica J Sutherland, Michael Arbel, and Arthur Gretton. Demystifying mmd gans. *arXiv preprint arXiv:1801.01401*, 2018. [2](#page-1-2)
- <span id="page-9-13"></span>[5] Torben Maack Bisgaard and Zoltán Sasvári. *Characteristic functions and moment sequences: positive definiteness in probability*. Nova Publishers, 2000. [2,](#page-1-2) [3](#page-2-0)
- <span id="page-9-0"></span>[6] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-0) [5,](#page-4-1) [6](#page-5-2)
- <span id="page-9-3"></span>[7] Dingfan Chen, Raouf Kerkouche, and Mario Fritz. Private set generation with discriminative information. *Advances in Neural Information Processing Systems*, 35:14678–14690, 2022. [1](#page-0-1)
- <span id="page-9-4"></span>[8] Ming-Yu Chung, Sheng-Yen Chou, Chia-Mu Yu, Pin-Yu Chen, Sy-Yen Kuo, and Tsung-Yi Ho. Rethinking backdoor attacks on dataset distillation: A kernel method perspective. *arXiv preprint arXiv:2311.16646*, 2023. [1](#page-0-1)
- <span id="page-9-20"></span>[9] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [3,](#page-2-0) [5,](#page-4-1) [6](#page-5-2)
- <span id="page-9-9"></span>[10] Wenxiao Deng, Wenbin Li, Tianyu Ding, Lei Wang, Hongguang Zhang, Kuihua Huang, Jing Huo, and Yang Gao. Exploiting inter-sample and inter-feature relations in dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 17057– 17066, 2024. [2,](#page-1-2) [3,](#page-2-0) [5](#page-4-1)
- <span id="page-9-5"></span>[11] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, pages 5378–5396. PMLR, 2022. [1](#page-0-1)
- <span id="page-9-21"></span>[12] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3749–3758, 2023. [3,](#page-2-0) [5,](#page-4-1) [6](#page-5-2)
- <span id="page-9-6"></span>[13] Leonard Euler. On transcending quantities arising from the circle. *Chapter*, 8, 1748. [2](#page-1-2)
- <span id="page-9-14"></span>[14] Andrey Feuerverger and Roman A Mureika. The empirical characteristic function and its applications. *The annals of Statistics*, pages 88–97, 1977. [2,](#page-1-2) [4](#page-3-5)
- <span id="page-9-1"></span>[15] Jianyang Gu, Kai Wang, Wei Jiang, and Yang You. Summarizing stream data for memory-constrained online continual

learning. In *Proceedings of the AAAI Conference on Artificial Intelligence*, pages 12217–12225, 2024. [1](#page-0-1)

- <span id="page-9-17"></span>[16] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023. [3,](#page-2-0) [5,](#page-4-1) [6](#page-5-2)
- <span id="page-9-29"></span>[17] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [7](#page-6-3)
- <span id="page-9-28"></span>[18] Jeremy Howard and Sylvain Gugger. Fastai: a layered api for deep learning. *Information*, 11(2):108, 2020. [6](#page-5-2)
- <span id="page-9-24"></span>[19] Seoungyoon Kang, Youngsun Lim, and Hyunjung Shim. Label-augmented dataset distillation. *arXiv preprint arXiv:2409.16239*, 2024. [5](#page-4-1)
- <span id="page-9-7"></span>[20] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022. [2,](#page-1-2) [3,](#page-2-0) [6](#page-5-2)
- <span id="page-9-15"></span>[21] Stephen M Kogon and Douglas B Williams. Characteristic function based estimation of stable distribution parameters. *A practical guide to heavy tails: statistical techniques and applications*, pages 311–338, 1998. [2](#page-1-2)
- <span id="page-9-26"></span>[22] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [5,](#page-4-1) [7](#page-6-3)
- <span id="page-9-27"></span>[23] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [5](#page-4-1)
- <span id="page-9-8"></span>[24] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022. [2,](#page-1-2) [3,](#page-2-0) [5](#page-4-1)
- <span id="page-9-11"></span>[25] Chun-Liang Li, Wei-Cheng Chang, Yu Cheng, Yiming Yang, and Barnabás Póczos. Mmd gan: Towards deeper understanding of moment matching network. *Advances in neural information processing systems*, 30, 2017. [2](#page-1-2)
- <span id="page-9-22"></span>[26] Hongcheng Li, Yucan Zhou, Xiaoyan Gu, Bo Li, and Weiping Wang. Diversified semantic distribution matching for dataset distillation. In *ACM Multimedia 2024*, 2024. [5](#page-4-1)
- <span id="page-9-19"></span>[27] Shengxi Li, Zeyang Yu, Min Xiang, and Danilo Mandic. Reciprocal adversarial learning via characteristic functions. *Advances in Neural Information Processing Systems*, 33:217– 228, 2020. [3,](#page-2-0) [5](#page-4-1)
- <span id="page-9-23"></span>[28] Shengxi Li, Jialu Zhang, Yifei Li, Mai Xu, Xin Deng, and Li Li. Neural characteristic function learning for conditional image generation. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 7204–7214, 2023. [5](#page-4-1)
- <span id="page-9-2"></span>[29] Zhe Li and Bernhard Kainz. Image distillation for safe data sharing in histopathology. In *International Conference on Medical Image Computing and Computer-Assisted Intervention*, pages 459–469. Springer, 2024. [1](#page-0-1)
- <span id="page-9-25"></span>[30] Dai Liu, Jindong Gu, Hu Cao, Carsten Trinitis, and Martin Schulz. Dataset distillation by automatic training trajectories. *arXiv preprint arXiv:2407.14245*, 2024. [5](#page-4-1)
- <span id="page-9-16"></span>[31] Paul Lévy. Théorie de l'addition des variables aléatoires. Gauthier-Villars, Paris, 1937. [2,](#page-1-2) [4,](#page-3-5) [5](#page-4-1)

- <span id="page-10-13"></span>[32] Danilo P Mandic and Anthony G Constantinides. Complex valued nonlinear adaptive filters: state of the art. *Signal Processing*, 89(9):1704–1725, 2009. [5,](#page-4-1) [7](#page-6-3)
- <span id="page-10-4"></span>[33] Dmitry Medvedev and Alexander D'yakonov. Learning to generate synthetic training data using gradient matching and implicit differentiation. In *International Conference on Analysis of Images, Social Networks and Texts*, pages 138– 150. Springer, 2021. [1](#page-0-1)
- <span id="page-10-16"></span>[34] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [5](#page-4-1)
- <span id="page-10-14"></span>[35] Alan V Oppenheim and Jae S Lim. The importance of phase in signals. *Proceedings of the IEEE*, 69(5):529–541, 1981. [5,](#page-4-1) [7](#page-6-3)
- <span id="page-10-15"></span>[36] Tian Qin, Zhiwei Deng, and David Alvarez-Melis. A label is worth a thousand images in dataset distillation. *arXiv preprint arXiv:2406.10485*, 2024. [5](#page-4-1)
- <span id="page-10-24"></span>[37] Alec Radford. Unsupervised representation learning with deep convolutional generative adversarial networks. *arXiv preprint arXiv:1511.06434*, 2015. [8](#page-7-4)
- <span id="page-10-9"></span>[38] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 17097–17107, 2023. [2,](#page-1-2) [3](#page-2-0)
- <span id="page-10-25"></span>[39] Tim Salimans, Ian Goodfellow, Wojciech Zaremba, Vicki Cheung, Alec Radford, and Xi Chen. Improved techniques for training gans. *Advances in neural information processing systems*, 29, 2016. [8](#page-7-4)
- <span id="page-10-18"></span>[40] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 16709–16718, 2024. [5](#page-4-1)
- <span id="page-10-12"></span>[41] Neil G Shephard. From characteristic function to distribution function: a simple framework for the theory. *Econometric theory*, 7(4):519–529, 1991. [2](#page-1-2)
- <span id="page-10-23"></span>[42] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [7](#page-6-3)
- <span id="page-10-26"></span>[43] Bharath K Sriperumbudur, Arthur Gretton, Kenji Fukumizu, Bernhard Schölkopf, and Gert RG Lanckriet. Hilbert space embeddings and metrics on probability measures. *The Journal of Machine Learning Research*, 11:1517–1561, 2010. [8](#page-7-4)
- <span id="page-10-5"></span>[44] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *International Conference on Machine Learning*, pages 9206–9216. PMLR, 2020. [1](#page-0-1)
- <span id="page-10-19"></span>[45] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 9390– 9399, 2024. [5,](#page-4-1) [6](#page-5-2)
- <span id="page-10-21"></span>[46] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forget-

ting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018. [5](#page-4-1)

- <span id="page-10-7"></span>[47] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196– 12205, 2022. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-0) [5,](#page-4-1) [6](#page-5-2)
- <span id="page-10-22"></span>[48] Shaobo Wang, Yantai Yang, Shuaiyu Zhang, Chenghao Sun, Weiya Li, Xuming Hu, and Linfeng Zhang. Drupi: Dataset reduction using privileged information, 2024. [6](#page-5-2)
- <span id="page-10-0"></span>[49] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [3](#page-2-0)
- <span id="page-10-20"></span>[50] Max Welling. Herding dynamical weights to learn. In *Proceedings of the 26th annual international conference on machine learning*, pages 1121–1128, 2009. [5](#page-4-1)
- <span id="page-10-6"></span>[51] Enneng Yang, Li Shen, Zhenyi Wang, Tongliang Liu, and Guibing Guo. An efficient dataset condensation plugin and its application to continual learning. In *Thirty-seventh Conference on Neural Information Processing Systems*, 2023. [1](#page-0-1)
- <span id="page-10-1"></span>[52] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *Thirty-seventh Conference on Neural Information Processing Systems*, 2023. [1](#page-0-1)
- <span id="page-10-10"></span>[53] Hansong Zhang, Shikun Li, Pengju Wang, Dan Zeng, and Shiming Ge. M3d: Dataset condensation by minimizing maximum mean discrepancy. In *Proceedings of the AAAI Conference on Artificial Intelligence*, pages 9314– 9322, 2024. [2,](#page-1-2) [3,](#page-2-0) [5](#page-4-1)
- <span id="page-10-8"></span>[54] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [2,](#page-1-2) [3,](#page-2-0) [5,](#page-4-1) [6](#page-5-2)
- <span id="page-10-2"></span>[55] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching, 2022. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-0) [5,](#page-4-1) [6,](#page-5-2) [8](#page-7-4)
- <span id="page-10-3"></span>[56] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [1,](#page-0-1) [2,](#page-1-2) [3,](#page-2-0) [5](#page-4-1)
- <span id="page-10-11"></span>[57] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023. [2,](#page-1-2) [3,](#page-2-0) [5,](#page-4-1) [6](#page-5-2)
- <span id="page-10-17"></span>[58] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022. [5](#page-4-1)
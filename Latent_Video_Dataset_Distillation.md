# Latent Video Dataset Distillation

<span id="page-0-0"></span><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> of California, Los Angeles Los Angeles, CA 90095

{ning<PERSON><PERSON>, ant<PERSON><PERSON>, zhangjingran, justin<PERSON>i}@ucla.edu

#### Abstract

*Dataset distillation has demonstrated remarkable effectiveness in high-compression scenarios for image datasets. While video datasets inherently contain greater redundancy, existing video dataset distillation methods primarily focus on compression in the pixel space, overlooking advances in the latent space that have been widely adopted in modern text-to-image and text-to-video models. In this work, we bridge this gap by introducing a novel video dataset distillation approach that operates in the latent space using a state-of-the-art variational encoder. Furthermore, we employ a diversity-aware data selection strategy to select both representative and diverse samples. Additionally, we introduce a simple, trainingfree method to further compress the distilled latent dataset. By combining these techniques, our approach achieves a new state-of-the-art performance in dataset distillation, outperforming prior methods on all datasets, e.g. on HMDB51 IPC 1, we achieve a 2.6% performance increase; on MiniUCF IPC 5, we achieve a 7.8% performance increase. Our code is available at* [https://](https://github.com/liningresearch/Latent_Video_Dataset_Distillation) [github.com/liningresearch/Latent\\_Video\\_](https://github.com/liningresearch/Latent_Video_Dataset_Distillation) [Dataset\\_Distillation](https://github.com/liningresearch/Latent_Video_Dataset_Distillation)*.*

## 1. Introduction

Dataset distillation has emerged as a pivotal technique for compressing large-scale datasets into computationally efficient representations that retain their essential characteristics [\[38\]](#page-9-0). While this technique has seen remarkable success in compressing image datasets [\[4,](#page-8-0) [5,](#page-8-1) [22,](#page-8-2) [27,](#page-8-3) [36,](#page-9-1) [44\]](#page-9-2), applications onto video datasets remain an underexplored challenge. Videos inherently possess temporal redundancy, as characterized by consecutive frames often sharing substantial similarity, presenting the potential for optimization via dataset distillation.

Existing video distillation methods predominantly focus on pixel-space compression. VDSD [\[39\]](#page-9-3) addresses the temporal information redundancy by disentangling static

and dynamic information. Method IDTD [\[48\]](#page-9-4) tackles the within-sample and inter-sample redundancies by leveraging a joint-optimization framework. However, these frameworks overlook the potential of latent-space compressions, which have proven transformative in generative models for images and videos [\[34,](#page-9-5) [47\]](#page-9-6). Modern variational autoencoders (VAEs) [\[29,](#page-8-4) [40\]](#page-9-7) offer a pathway to address this gap by encoding videos into compact, disentangled representations in latent space.

In this work, we improve video distillation by operating entirely in the latent space of a VAE. Our framework distills videos into low-dimensional latent codes, leveraging the VAE's ability to model temporal dynamics [\[47\]](#page-9-6). Unlike previous methods, our approach encodes entire video sequences into coherent latent trajectories to model temporal dynamics through its hierarchical architecture. We compress the VAE itself through post-training quantization, largely reducing the model size, while retaining accuracy [\[5\]](#page-8-1). After distillation, we apply Diversity-Aware Data Selection using Determinantal Point Processes (DPPs) [\[17\]](#page-8-5) to select both representative and diverse instances. Unlike clustering-based or random sampling methods, DPPs inherently favor diversity by selecting samples that are wellspread in the latent space, reducing redundancy while ensuring comprehensive feature coverage [\[26\]](#page-8-6). This leads to a more informative distilled dataset that enhances downstream model generalization.

Our method further introduces a training-free latent compression strategy, which uses high-order singular value decomposition (HOSVD) to decompose spatiotemporal features into orthogonal subspaces [\[39\]](#page-9-3). This isolates dominant motion patterns and spatial structures, enabling further compression while preserving essential dynamics [\[34\]](#page-9-5). By factorizing latent tensors, we dynamically adjust the rank of the distilled representations, allowing denser instance packing under fixed storage limits. Experiments on the Mini-UCF dataset demonstrate that our method outperforms prior pixel-space approaches by 11.5% in absolute accuracy for IPC 1 and 7.8% for IPC 5.

Overall, our contributions are:

- <span id="page-1-0"></span>1. We propose the first video dataset distillation framework operating in the latent space, leveraging a state-of-the-art VAE to efficiently encode spatiotemporal dynamics.
- 2. We address the challenge of sparsity in the video latent space by integrating Diversity-Aware Data Selection using DPPs and High-Order Singular Value Decomposition (HOSVD) for structured compression.
- 3. Our method generalizes to both small-scale and largescale video datasets, achieving a new state-of-the-art performance on all settings compared to existing methods.

## 2. Related Work

Coreset Selection Coreset selection aims to identify a small but representative subset of data that preserves the essential properties of the full dataset, reducing computational complexity while maintaining model performance. One of the foundational approaches utilizes k-center clustering [\[30\]](#page-8-7) to formulate coreset selection as a geometric covering problem, where a subset of data points is chosen to maximize the minimum distance to previously selected points. By iteratively selecting the most distant samples in feature space, this method ensures that the coreset provides broad coverage of the dataset's distribution, making it a strong candidate for reducing redundancy in large-scale datasets. Herding methods [\[40\]](#page-9-7) take an optimization-driven approach to coreset selection by sequentially choosing samples that best approximate the mean feature representation of the dataset. Probabilistic techniques leverage Bayesian inference [\[24\]](#page-8-8) and divergence minimization [\[33\]](#page-9-8) to construct coresets that balance diversity and statistical representativeness. Influence-based selection methods [\[41\]](#page-9-9) instead focus on quantifying the contribution of individual samples to generalization performance, retaining only the most impactful data points.

Image Dataset Distillation Dataset distillation [\[38\]](#page-9-0) has emerged as a powerful paradigm for compressing largescale image datasets while preserving downstream task performance. Early gradient-based methods like Dataset Distillation (DD) [\[38\]](#page-9-0) optimized synthetic images by matching gradients between training trajectories on original and distilled datasets. Later works introduced dataset condensation with gradient matching [\[46\]](#page-9-10). Further, Meta-learning frameworks Like Matching Training Trajectories (MTT) [\[3\]](#page-8-9) and Kernel Inducing Points (KIP) [\[28\]](#page-8-10) advances performance by distilling datasets through bi-level optimization over neural architectures. Dataset condensation with Distribution Matching (DM) [\[45\]](#page-9-11) synthesizes condensed datasets by aligning feature distributions between original and synthetic data across various embedding spaces.

Representative Matching for Dataset Condensation (DREAM) [\[21\]](#page-8-11) improved sample efficiency by selecting representative instances that retained the most informative patterns from the original dataset, reducing redundancy in synthetic samples. Generative modeling techniques have also been explored, with Distilling Datasets into Generative Models (DiM) [\[37\]](#page-9-12) encoding datasets into latent generative spaces, allowing for smooth interpolation and novel sample generation. Similarly, Hybrid Generative-Discriminative Dataset Distillation (GDD) [\[19\]](#page-8-12) balanced global structural coherence with fine-grained detail preservation by combining adversarial generative models with traditional distillation objectives. However, temporal redundancy and frame sampling complexities, as noted in [\[11,](#page-8-13) [20\]](#page-8-14), highlight the unique difficulties of extending image-focused distillation to video datasets.

Video Dataset Distillation While dataset distillation has achieved significant success in static image datasets, direct application to videos presents unique challenges due to temporal redundancy and the need for efficient frame selection [\[34\]](#page-9-5). Recent attempts to address video dataset distillation have primarily focused on pixel-space compression. Video Distillation via Static-Dynamic Disentanglement (VDSD) [\[39\]](#page-9-3) tackles temporal redundancies between frames by separating static and dynamic components. VDSD partitions videos into smaller segments and employs learnable dynamic memory block that captures and synthesizes motion patterns, improving information retention while reducing redundancy. IDTD [\[48\]](#page-9-4) addresses the challenges of within-sample redundancy and inter-sample redundancy simultaneously. IDTD employs an architecture represented by a shared feature pool alongside multiple feature selectors to selectively condense video sequences while ensuring sufficient motion diversity. To retain the temporal information of synthesized videos, IDTD introduces a Temporal Fusor that integrates diverse features into the temporal dimension.

Text-to-Video Models and Their Role in Latent Space Learning Latent-space representations have become a cornerstone of modern video modeling, offering structured compression while maintaining high-level semantic integrity [\[34,](#page-9-5) [47\]](#page-9-6). Variational autoencoders provide to enable efficient storage and reconstruction [\[13\]](#page-8-15). Extending this concept, hierarchical autoregressive latent prediction [\[31\]](#page-9-13) introduces an autoregressive component that improves temporal coherence, leading to high-fidelity video reconstructions. Further enhancing latent representations, latent video diffusion transformers [\[23\]](#page-8-16) incorporate diffusion-based priors to refine video quality while minimizing storage demands.

Building upon these latent space techniques, recent textto-video models have demonstrated their capability to generate high-resolution video content from textual descriptions. These methods employ a combination of transformerbased encoders and diffusion models to synthesize realistic video sequences. Imagen Video leverages cascaded video diffusion models to progressively upsample spatial and tem-

<span id="page-2-1"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: This is a diagram illustrating a machine learning process for video analysis. On the left, an 'Input Video Dataset' is fed into a '3D Encoder'. The output of the encoder is a 'Latent Video Dataset'. Separately, 'Classifications' are processed by an 'Eval Model'. The 'Latent Video Dataset' is then processed through 'DPPs' (Distributional Polynomials) to produce 'Sampled Videos'. In the 'Latent Space', the 'Sampled Videos' are combined with 'Factor Matrices' via 'HOSVD' (Higher-Order Singular Value Decomposition) to reconstruct a 'Core Tensor'. This 'Core Tensor' is then fed back to the 'Eval Model'.

Figure 1. Our training-free latent video distillation pipeline. The entire video dataset is encoded into latent space with a VAE. We further employ the DPPs to select both representative and diverse samples, followed by latent space compression with HOSVD for efficient storage.

poral dimensions, ensuring high-quality output [\[9\]](#page-8-17). Meanwhile, zero-shot generation approaches utilize decoderonly transformer architectures to process multimodal inputs, such as text and images, without requiring explicit video-text training data [\[15\]](#page-8-18). Hybrid techniques combining pixel-space and latent-space diffusion modeling further enhance computational efficiency while maintaining visual fidelity by leveraging learned latent representations during synthesis [\[43\]](#page-9-14). These advancements in latent space learning not only improve video compression but also drive the development of scalable and high-quality text-driven video generation.

## 3. Methodology

In this section, we first introduce the variational autoencoder (VAE) used to encode video sequences into a compact latent space. We then discuss our Diversity-Aware Data Selection method. Next, we present our training-free latent space compression approach using High-Order Singular Value Decomposition (HOSVD). Finally, we describe our two-stage dynamic quantization strategy. The entire pipeline of our framework is shown in Fig. [1](#page-2-0)

### 3.1. Preliminary

Problem Definition In video dataset distillation, given a large dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  consisting of video samples  $x_i$  and their corresponding class labels  $y_i$ , the objective is to construct a significantly smaller distilled dataset  $S = {\{\tilde{x}_i, \tilde{y}_i\}}_{i=1}^{|S|}$ , where  $|S| \ll |\mathcal{T}|$ . The distilled dataset is expected to achieve comparable performance to the original dataset on action classification tasks while significantly reducing storage and computational requirements.

Latent image distillation has emerged as an effective

alternative to traditional dataset distillation methods. Instead of distilling datasets at the pixel level, latent distillation leverages pre-trained autoencoders or generative models to encode images into a compact latent space. Latent Dataset Distillation with Diffusion Models [\[25\]](#page-8-19), have demonstrated that distilling image datasets in the latent space of a pre-trained diffusion model improves generalization and enables higher compression ratios while maintaining image quality. Similarly, Dataset Distillation in Latent Space [\[6\]](#page-8-20) adapts conventional distillation methods like Gradient Matching, Feature Matching, and Parameter Matching to the latent space, significantly reducing computational overhead while achieving competitive performance. Different from these methods, we extend latent space distillation to video datasets by encoding both spatial and temporal information into the latent space.

### 3.2. Variational Autoencoder

Variational Autoencoders (VAEs) are a class of generative models that encode input data into a compact latent space while maintaining the ability to reconstruct the original data [\[13\]](#page-8-15). Unlike traditional autoencoders, VAEs enforce a probabilistic structure on the latent space by learning a distribution rather than a fixed mapping. This allows for better generalization and meaningful latent representations.

A VAE consists of an encoder and a decoder. The encoder maps the input x to a latent distribution  $q_{\phi}(z|x)$ , where  $z$  is the latent variable. Instead of producing a deterministic latent representation, the encoder outputs the mean and variance of a Gaussian distribution, from which samples are drawn. This ensures that the latent space remains continuous, facilitating smooth interpolation between data points  $[14]$ . The decoder then reconstructs the input x from

<span id="page-3-0"></span>a sampled latent variable  $z$ , following the learned distribution  $p_{\theta}(x|z)$ .

To ensure a structured latent space, VAEs introduce a regularization term that aligns the learned distribution with a prior distribution, typically a standard normal distribution  $p(z) = \mathcal{N}(0, 1)$ . This prevents the model from collapsing into a purely memorized representation of the data, encouraging better generalization.

The training objective of a VAE is to maximize the Evidence Lower Bound (ELBO) [\[18\]](#page-8-22), which consists of two terms: reconstruction loss and Kullback-Leibler (KL) divergence regularization [\[13\]](#page-8-15). The reconstruction loss ensures that the decoded output remains similar to the original input, while the KL divergence forces the learned latent distribution to be close to the prior, preventing overfitting and promoting smoothness in the latent space. The overall loss function is formulated as follows.

$$
\mathcal{L}_{\text{VAE}} = \mathbb{E}_{q_{\phi}(z|x)}[-\log p_{\theta}(x|z)] + \beta \cdot D_{\text{KL}}(q_{\phi}(z|x) \parallel p_{\theta}(z))
$$
\n(1)

#### 3.3. Diversity-Aware Data Selection

After encoding the entire video dataset into the latent space using a state-of-the-art VAE, an effective data selection strategy is crucial to maximize the diversity and representativeness of the distilled dataset. To this end, we employ Diversity-Aware Data Selection using Determinantal Point Processes (DPPs) [\[17\]](#page-8-5), a principled probabilistic framework that promotes diversity by favoring sets of samples that are well-spread in the latent space.

DPPs [\[17\]](#page-8-5) provide a natural mechanism for selecting a subset of latent embeddings that balance coverage and informativeness while reducing redundancy. Given the encoded latent representations of the dataset, we construct a similarity kernel matrix  $L$ , where each entry  $L_{ij}$  quantifies the pairwise similarity between latent samples  $z_i$  and  $z_j$ . The selection process then involves sampling from a determinantal distribution parameterized by  $L$ , ensuring that the chosen subset is both diverse and representative of the full latent dataset. We define a kernel matrix L using the following function:

$$
L_{ij} = \exp(-\frac{\|z_i - z_j\|^2}{2\sigma^2})
$$
 (2)

Then subset  $S$  is sampled according to:

$$
P(S) = \frac{\det(L_S)}{\det(L + I)}
$$
(3)

here  $L<sub>S</sub>$  is the submatrix of  $L$  that corresponds to the rows and columns indexed by S. The denominator  $\det(L + I)$ serves as a normalization factor, ensuring that the probabilities across all possible subsets sum to 1. This normalization

stabilizes the sampling process by incorporating an identity matrix I, which prevents numerical instability in cases where  $L$  is near-singular.

Our approach is motivated by the observation that naive random sampling or traditional clustering-based selection strategies [\[12\]](#page-8-23) tend to underperform in high-dimensional latent spaces [\[7\]](#page-8-24), where redundancy is prevalent. By leveraging DPPs, we effectively capture a more comprehensive distribution of video features, thereby improving the quality of the distilled dataset. Furthermore, the computational efficiency of DPPs sampling allows us to scale our selection process to large datasets without significant overhead.

Applying DPPs in the latent space instead of the pixel space offers several key advantages. First, latent representations encode high-level semantic features, making it possible to directly select samples that preserve meaningful variations in motion and structure, rather than relying on pixelwise differences that may be redundant or noisy. Second, the latent space is significantly more compact and disentangled, allowing DPPs to operate more effectively with reduced computational complexity compared to pixel-space selection [\[39\]](#page-9-3), which often involves large-scale feature extraction. Finally, in the latent space, similarity measures are inherently more structured, which makes DPPs better suited for ensuring diverse and representative selections that generalize well to downstream tasks.

#### 3.4. Training-free Latent Space Compression

While our Diversity-Aware Data Selection effectively distills a compact subset of the latent dataset, we observe that the selected latent representations remain sparse, leading to inefficiencies in storage and downstream processing.

Singular Value Decomposition (SVD) is a fundamental matrix factorization technique widely used in dimensionality reduction, data compression, and noise filtering. Given a matrix  $X \in \mathbb{R}^{m \times n}$ , SVD decomposes it into three components:

$$
X = U\Sigma V^T \tag{4}
$$

where U is an orthogonal matrix whose columns represent the left singular vectors,  $\Sigma$  is a diagonal matrix containing the singular values that indicate the importance of each corresponding singular vector, and  $V$  is an orthogonal matrix whose columns represent the right singular vectors. A key property of SVD is that truncating the smaller singular values allows for an effective low-rank approximation of the original matrix, reducing storage requirements while preserving essential information. This property makes SVD particularly useful in data compression and feature selection.

However, when applied to higher-dimensional data, such as video representations in latent space, SVD requires flattening the tensor into a 2D matrix, which disrupts spa-

<span id="page-4-1"></span><span id="page-4-0"></span>

| Dataset                  |                   | MiniUCF        |                | HMDB51         |                |                | Kinetics-400   |                          | SSv2          |  |
|--------------------------|-------------------|----------------|----------------|----------------|----------------|----------------|----------------|--------------------------|---------------|--|
| IPC.                     |                   |                |                |                |                |                |                |                          |               |  |
| <b>Full Dataset</b>      |                   | $57.2 \pm 0.1$ |                | $28.6 \pm 0.7$ |                | $34.6 \pm 0.5$ |                | $29.0 \pm 0.6$           |               |  |
|                          | Random            | $9.9 \pm 0.8$  | $22.9 \pm 1.1$ | $4.6 \pm 0.5$  | $6.6 \pm 0.7$  | $3.0 \pm 0.1$  | $5.6 \pm 0.0$  | $3.2 \pm 0.1$            | $3.7 \pm 0.0$ |  |
| <b>Coreset Selection</b> | Herding $[40]$    | $12.7 \pm 1.6$ | $25.8 \pm 0.3$ | $3.8 \pm 0.2$  | $8.5 \pm 0.4$  | $4.3 \pm 0.3$  | $8.0 \pm 0.1$  | $4.6 \pm 0.3$            | $6.8 \pm 0.2$ |  |
|                          | K-Center $[30]$   | $11.5 \pm 0.7$ | $23.0 \pm 1.3$ | $3.1 \pm 0.1$  | $5.2 \pm 0.3$  | $3.9 \pm 0.2$  | $5.9 \pm 0.4$  | $3.8 \pm 0.5$            | $4.0 \pm 0.1$ |  |
|                          | DM [45]           | $15.3 \pm 1.1$ | $25.7 \pm 0.2$ | $6.1 \pm 0.2$  | $8.0 \pm 0.2$  | $6.3 \pm 0.0$  | $9.1 \pm 0.9$  | $4.1 \pm 0.4$            | $4.5 \pm 0.3$ |  |
|                          | MTT[3]            | $19.0 \pm 0.1$ | $28.4 \pm 0.7$ | $6.6 \pm 0.5$  | $8.4 \pm 0.6$  | $3.8 \pm 0.2$  | $9.1 \pm 0.3$  | $3.9 + 0.2$              | $6.5 \pm 0.2$ |  |
|                          | <b>FRePo</b> [49] | $20.3 \pm 0.5$ | $30.2 \pm 1.7$ | $7.2 \pm 0.8$  | $9.6 \pm 0.7$  |                |                |                          |               |  |
| Dataset Distillation     | $DM+VDSD$ [39]    | $17.5 \pm 0.1$ | $27.2 \pm 0.4$ | $6.0 \pm 0.4$  | $8.2 \pm 0.1$  | $6.3 \pm 0.2$  | $7.0 \pm 0.1$  | $4.3 \pm 0.3$            | $4.0 \pm 0.3$ |  |
|                          | MTT+VDSD [39]     | $23.3 \pm 0.6$ | $28.3 \pm 0.0$ | $6.5 \pm 0.1$  | $8.9 \pm 0.6$  | $6.3 \pm 0.1$  | $11.5 \pm 0.5$ | $5.7 \pm 0.2$            | $8.4 \pm 0.1$ |  |
|                          | FRePo+VDSD [39]   | $22.0 \pm 1.0$ | $31.2 \pm 0.7$ | $8.6 \pm 0.5$  | $10.3 \pm 0.6$ |                |                |                          |               |  |
|                          | <b>IDTD</b> [48]  | $22.5 \pm 0.1$ | $33.3 \pm 0.5$ | $9.5 \pm 0.3$  | $16.2 \pm 0.9$ | $6.1 \pm 0.1$  | $12.1 \pm 0.2$ | $\overline{\phantom{a}}$ |               |  |
|                          | Ours              | $34.8\pm0.5$   | $41.1\pm0.6$   | $12.1\pm0.3$   | $17.6\pm0.4$   | $9.0 \pm 0.1$  | $13.8\pm0.1$   | $6.9\pm0.6$              | $10.5\pm0.4$  |  |

Table 1. Performance comparison between our method and existing baselines on both small-scale and large-scale datasets. Follow previous works, we report Top-1 test accuracies (%) for small-scale datasets and Top-5 test accuracies (%) for large-scale datasets.

tial and temporal correlations. This limitation motivates our adoption of High-Order Singular Value Decomposition (HOSVD), which extends SVD to multi-dimensional tensors while preserving their inherent structure.

HOSVD is a tensor decomposition technique that generalizes traditional SVD to higher-dimensional data. By treating the selected latent embeddings as a structured tensor rather than independent vectors, we exploit multi-modal correlations across feature dimensions to achieve more efficient compression. Specifically, given a set of selected latent embeddings  $Z \in \mathbb{R}^{d_1 \times d_2 \times \cdots \times d_n}$ , we decompose it into a core tensor  $G$  and a set of orthonormal factor matrices  $U_i$ , such that

$$
Z = \mathcal{G} \times_1 U_1 \times_2 U_2 \times \cdots \times_n U_n \tag{5}
$$

where  $\times_i$  denotes the mode- i tensor-matrix product. By truncating the singular values in each mode with a rank compression ratio, we discard low-energy components while preserving the most informative structures in the latent space.

A key advantage of HOSVD over traditional SVD is its ability to retain the original tensor structure, rather than requiring flattening into a 2D matrix. More importantly, truncating the singular values in the temporal mode directly reduces temporal redundancy, ensuring that only the most representative motion patterns are retained. This enables more efficient storage and reconstruction, while minimizing the loss of critical temporal information.

Unlike conventional post-hoc compression techniques that require fine-tuning or retraining, HOSVD operates in a completely training-free manner, making it highly efficient and scalable. Furthermore, our empirical analysis shows that applying HOSVD after DPPs-based selection leads to a substantial reduction in storage and computational requirements while maintaining near-optimal performance in downstream tasks.

By integrating HOSVD into our dataset distillation pipeline, we achieve an additional compression gain with

minimal loss of information, further pushing the boundaries of efficiency in video dataset distillation.

#### 3.5. VAE Quantization

To further improve storage efficiency, we apply a two-stage quantization process to the 3D-VAE [\[47\]](#page-9-6), combining dynamic quantization for fully connected layers and mixedprecision optimization for all other layers.

The first stage involves dynamic quantization, where all fully connected layers are reduced from 32-bit floatingpoint to 8-bit integer representations. Dynamic quantization works by scaling activations and weights dynamically during inference. Formally, given an activation  $x$  and weight matrix  $W$ , the quantized representation is computed as:

$$
W_q = \text{round}\left(\frac{W}{s_W}\right) + z_W, \quad x_q = \text{round}\left(\frac{x}{s_x}\right) + z_x \tag{6}
$$

where  $s_W$  and  $s_x$  are learned scaling factors, and  $z_W$  and  $z_x$  are zero points for weight and activation quantization, respectively. The dynamically scaled operation ensures that numerical stability is preserved while reducing the model size. This quantization is applied to all fully connected layers in the encoder and decoder, allowing for efficient memory compression without requiring retraining.

Unlike convolutional layers, fully connected layers primarily perform matrix multiplications, which exhibit high redundancy and are well-suited for integer quantization (INT8). Quantizing these layers from FP32 to INT8 significantly reduces memory consumption and improves computational efficiency while maintaining inference stability [\[10\]](#page-8-25). Since fully connected layers do not require the high dynamic range of floating-point precision, INT8 quantization achieves optimal storage and performance benefits.

In the second stage, we employ mixed-precision optimization, where all remaining convolutional and batch normalization layers undergo reduced-precision floating-point compression, scaling them from FP32 to FP16. Unlike integer quantization, FP16 maintains a wider dynamic range,

<span id="page-5-0"></span>preventing significant loss of information in convolutional layers, which are more sensitive to precision reduction [\[42\]](#page-9-16).

This hybrid quantization approach balances storage efficiency and numerical precision, ensuring that the 3D-VAE remains compact while preserving its ability to model spatiotemporal dependencies in video sequences. While applying post-training dynamic quantization on CV-VAE[\[47\]](#page-9-6), we achieve a more than 2.6× compression ratio while maintaining high reconstruction fidelity.

## 4. Experiments

#### 4.1. Datasets and Metrics

Following previous works VDSD [\[39\]](#page-9-3) and IDTD [\[48\]](#page-9-4), we evaluate our proposed video dataset distillation approach on both small-scale and large-scale benchmark datasets. For small-scale datasets, we utilize MiniUCF [\[39\]](#page-9-3) and HMDB51 [\[16\]](#page-8-26), while for large-scale datasets, we conduct experiments on Kinetics [\[2\]](#page-8-27) and Something-Something V2 (SSv2) [\[8\]](#page-8-28). MiniUCF is a miniaturized version of UCF101 [\[32\]](#page-9-17), consisting of the 50 most common action classes selected from the original UCF101 dataset. HMDB51 is a widely used human action recognition dataset containing 6,849 video clips across 51 action categories. Kinetics is a large-scale video action recognition dataset, available in different versions covering 400, 600, or 700 human action classes. SSv2 is a motion-centric video dataset comprising 174 action categories.

#### 4.2. Baselines

Based on previous work, we include the following baseline: (1) coreset selection methods such as random selection, Herding [\[40\]](#page-9-7), and K-Center [\[30\]](#page-8-7), and (2) dataset distillation methods including DM [\[45\]](#page-9-11), MTT [\[3\]](#page-8-9), FRePo [\[49\]](#page-9-15), VDSD [\[39\]](#page-9-3), and IDTD [\[48\]](#page-9-4). DM [\[45\]](#page-9-11) ensures that the models trained on the distilled dataset produce gradient updates similar to those trained on the full dataset. MTT [\[3\]](#page-8-9) improves distillation by aligning model parameter trajectories between the synthetic and original datasets. FRePo [\[49\]](#page-9-15) focuses on generating compact datasets that allow pretrained models to quickly recover their original performance with minimal training. VDSD [\[39\]](#page-9-3) introduces a static-dynamic disentanglement approach for video dataset distillation. IDTD [\[48\]](#page-9-4) enhances video dataset distillation by increasing feature diversity across samples while densifying temporal information within instances.

#### 4.3. Implementation Details

Dataset Details For small-scale datasets, MiniUCF and HMDB51, we follow the settings from previous work [\[39,](#page-9-3) [48\]](#page-9-4), where videos are dynamically sampled to 16 frames with a sampling interval of 4. Each sampled frame is then cropped and resized to 112×112 resolution. We adopt the same settings as prior work [\[39,](#page-9-3) [48\]](#page-9-4) for Kinetics-400, each video is sampled to 8 frames and resized to 64×64, maintaining a compact representation suitable for large-scale dataset distillation. In Something-Something V2 (SSv2), which is relatively smaller among the two largescale datasets, we sample 16 frames per video and resize them to 112×112, demonstrating the scalability of our method across datasets of varying sizes.

Evaluation Network Following the previous works, we use a 3D convolutional network, C3D [\[35\]](#page-9-18) as the evaluation network. C3D [\[35\]](#page-9-18) is trained on the distilled datasets generated by our method. Similar to previous works, we assess the performance of our distilled datasets by measuring the top-1 accuracy on small-scale datasets and top-5 accuracy on large-scale datasets.

Fair Comparison Throughout our experiments, we rigorously ensure that the total storage space occupied by the quantized VAE model and the decomposed matrices remain within the constraints of the corresponding Instance Per Class (IPC) budget. Specifically, on SSv2, our method utilizes no more than 68% of the storage space allocated to the baseline methods DM and MTT, guaranteeing a fair and consistent comparison. A comprehensive analysis of fair comparisons across all four video datasets is provided in the supplementary material.

#### 4.4. Experimental Results

In Tab. [1,](#page-4-0) we present the performance of our method across MiniUCF [\[39\]](#page-9-3), HMDB51 [\[16\]](#page-8-26), Kinetics-400 [\[2\]](#page-8-27), and SSv2 [\[8\]](#page-8-28) under both IPC 1 and IPC 5 settings.

On MiniUCF, our approach outperforms the best baseline (IDTD) by 12.3% under IPC 1, achieving 34.8% accuracy compared to 22.5%, and by 7.8% under IPC 5, reaching 41.1% accuracy. Similarly, on HMDB51, our method achieves 12.1% accuracy under IPC 1, surpassing the strongest baseline by 2.6%, while under IPC 5, it reaches 17.6%, a 1.4% improvement. These results highlight the effectiveness of our latent-space distillation framework, which provides superior compression efficiency and classification performance compared to pixel-space-based approaches. The consistent performance gains across both IPC settings demonstrate the robustness of our method in preserving essential video representations while achieving high compression efficiency.

Furthermore, the results in Kinetics-400 and SSv2 reinforce our findings, as our approach consistently outperforms all baselines. Improvements in low-IPC regimes (IPC 1) suggest that our training-free latent compression and diversity-aware data selection are particularly effective when dealing with extreme data reduction. Our method achieves 9.0% accuracy on Kinetics-400 IPC 1, outperforming the strongest baseline (IDTD) by 2.9%, and 6.9% accuracy on SSv2 IPC 1, surpassing VDSD by 2.2%. The

<span id="page-6-3"></span><span id="page-6-1"></span>Image /page/6/Figure/0 description: A bar chart displays the accuracy of different methods. The x-axis lists the methods: Random, DM + VDSD, MTT + VDSD, IDTD, Kmeans, DPP only, and Ours. The y-axis represents Accuracy, ranging from 0 to 10. The bars show the following accuracies: Random (4), DM + VDSD (4), MTT + VDSD (8.2), IDTD (9.7), Kmeans (7.2), DPP only (9.5), and Ours (10.3). A dotted line connects the tops of the bars, illustrating the trend in accuracy across the methods. The bar for 'Ours' is colored orange, while the others are green.

Figure 2. Comparison between different dataset distillation methods and data sampling methods on the SSv2 when IPC is 1.

trend continues in IPC 5, where our model achieves 13.8% on Kinetics-400 and 10.5% on SSv2, both establishing new state-of-the-art results in video dataset distillation.

#### 4.5. Ablation Study

In this section, we systematically analyze the key components of our method to understand their contributions to overall performance. We evaluate cross-architecture generalization, various sampling methods, different rank compression ratios in HOSVD, and different latent space compression techniques.

Cross Architecture Generalization To further evaluate the generalization capability of our method, we conduct experiments on cross-architecture generalization, as presented in Tab [2.](#page-6-0) The results demonstrate that datasets distilled using our method consistently achieve superior performance across different evaluation models—ConvNet3D, CNN+GRU, and CNN+LSTM—compared to previous state-of-the-art methods.

Our approach achieves 34.8% accuracy with ConvNet3D, significantly surpassing all baselines, including MTT+VDSD (23.3%) and DM+VDSD (17.5%). Notably, our method also outperforms all baselines when evaluated on recurrent-based architectures (CNN+GRU and CNN+LSTM), obtaining 19.9% and 18.3% accuracy, respectively. This highlights the robustness of our distilled dataset in preserving spatiotemporal coherence, which is crucial for models that leverage sequential dependencies.

Sampling Methods We evaluate the impact of different sampling strategies on dataset distillation, comparing our Diversity-Aware Data Selection using Determinantal Point Processes (DPPs) against random sampling, Kmeans clustering  $[12]$ , and prior dataset distillation methods (DM + VDSD, MTT + VDSD, IDTD). As shown in Fig. [2,](#page-6-1) our method achieves the highest performance, demonstrating the effectiveness of DPP-based selection in video dataset distillation.

Among sampling strategies, DPPs-only selection outperforms Kmeans and random sampling, indicating that DPPs promote a more diverse and representative subset of the latent space. Compared to Kmeans (7.2%), DPPs selection achieves 9.3% accuracy, validating its ability to reduce redundancy and improve feature coverage. Furthermore, our full method, which integrates DPPs-based selection with HOSVD, achieves the best overall performance at 10.5%, surpassing both previous dataset distillation methods and other alternative sampling techniques. The complete evaluation accuracies are detailed in the supplementary material.

These results highlight the importance of an effective data selection strategy in video dataset distillation. Our approach leverages DPPs to maximize diversity while retaining representative samples, leading to superior generalization in downstream tasks.

<span id="page-6-0"></span>

|                 | <b>Evaluation Model</b>          |                                  |                                  |
|-----------------|----------------------------------|----------------------------------|----------------------------------|
|                 | ConvNet3D                        | CNN+GRU                          | CNN+LSTM                         |
| Random          | $9.9 \pm 0.8$                    | $6.2 \pm 0.8$                    | $6.5 \pm 0.3$                    |
| DM [45]         | $15.3 \pm 1.1$                   | $9.9 \pm 0.7$                    | $9.2 \pm 0.3$                    |
| DM + VDSD [39]  | $17.5 \pm 0.1$                   | $12.0 \pm 0.7$                   | $10.3 \pm 0.2$                   |
| MTT [3]         | $19.0 \pm 0.1$                   | $8.4 \pm 0.5$                    | $7.3 \pm 0.4$                    |
| MTT + VDSD [39] | $23.3 \pm 0.6$                   | $14.8 \pm 0.1$                   | $13.4 \pm 0.2$                   |
| <b>Ours</b>     | <b><math>34.8 \pm 0.5</math></b> | <b><math>19.9 \pm 0.7</math></b> | <b><math>18.3 \pm 0.7</math></b> |

Table 2. Result of experiment on cross-architecture generalization for MiniUCF when IPC is 1.

#### Rank Compression Ratio

We evaluate the impact of different rank compression ratios in HOSVD on overall performance in Tab. [3.](#page-6-2) Empirical results show that a rank compression ratio of r=0.75 consistently provides a strong balance between storage efficiency and model accuracy across datasets. While increasing the compression ratio reduces storage requirements, overly aggressive compression can lead to significant information loss, negatively affecting downstream tasks. Notably, as shown in Fig. [3,](#page-7-0) when the rank compression ratio is set to r  $= 0.1$ , both datasets exhibit classification accuracy around 4.0%, suggesting that excessive compression leads to degraded latent representations, making the distilled dataset nearly indistinguishable from random noise.

<span id="page-6-2"></span>

| Dataset | Rank Compression Ratio |              |              |              |              |
|---------|------------------------|--------------|--------------|--------------|--------------|
|         | 0.10                   | 0.25         | 0.50         | 0.75         | 1.00         |
| MiniUCF | 4.1 \pm 0.1            | 19.0 \pm 1.3 | 31.5 \pm 0.7 | 34.8 \pm 0.5 | 28.9 \pm 0.5 |
| HMDB51  | 3.9 \pm 0.6            | 7.6 \pm 1.0  | 11.5 \pm 0.1 | 12.1 \pm 0.3 | 8.9 \pm 0.5  |

Table 3. Accuracies under different rank compression ratios. Both MiniUCF and HMDB51 datasets are evaluated under IPC 1.

HOSVD vs Classic SVD To evaluate the effectiveness of our latent-space compression strategy, we compare truncated SVD with HOSVD under the same storage budget at

<span id="page-7-0"></span>Image /page/7/Figure/0 description: A line graph displays the accuracy of two models, MiniUCF and HMDB51, against the rank compression ratio. The x-axis represents the rank compression ratio, with values ranging from 0.1 to 1.0. The y-axis represents accuracy, ranging from 0 to 35. The MiniUCF model (blue line) shows an increasing trend in accuracy as the compression ratio increases, reaching a peak of 34.8 at a ratio of 0.75, and then slightly decreasing to 29.2 at a ratio of 1.0. The HMDB51 model (orange line) shows a lower accuracy overall, with a peak of 12.1 at a ratio of 0.75, and then decreasing to 9.5 at a ratio of 1.0. Error bars are present for each data point, indicating the variability or uncertainty in the measurements.

Figure 3. Accuracies of HMDB51 (IPC 1) and MiniUCF (IPC 1) under different rank compression ratios utilized in HOSVD.

IPC 5. Truncated SVD is a matrix factorization technique that approximates a data matrix by keeping only its largest singular values, thereby reducing dimensionality while retaining the most informative components. However, SVD operates on flattened data matrices, leading to a loss of structural information, particularly in spatiotemporal representations.

As shown in Tab. [4,](#page-7-1) HOSVD consistently outperforms truncated SVD across all datasets, demonstrating its ability to better preserve spatial and temporal dependencies in the latent space. The performance gains are especially notable on MiniUCF (+2.6%) and HMDB51 (+1.8%). Similarly, on Kinetics-400 and SSv2, HOSVD achieves higher classification accuracy (+1.4% and +1.2%, respectively), highlighting its advantage in handling large-scale datasets. These results confirm that HOSVD's tensor-based decomposition provides a more compact yet expressive representation.

<span id="page-7-1"></span>

| <b>Dataset</b> | MiniUCF        | HMDB51         | Kinetics-400   | SSv2           |
|----------------|----------------|----------------|----------------|----------------|
| <b>SVD</b>     | $38.5 \pm 0.4$ | $15.8 \pm 0.2$ | $12.4 \pm 0.3$ | $9.3 \pm 0.2$  |
| <b>HOSVD</b>   | $41.1 \pm 0.6$ | $17.6 \pm 0.4$ | $13.8 \pm 0.1$ | $10.5 \pm 0.4$ |

Table 4. Classification accuracies comparison between different latent compression techniques under the same storage budget for each dataset at IPC 5.

### 4.6. Visualization

Following previous works, we provide an inter-frame contrast between DM and our method to illustrate the differences in temporal consistency in Fig. [4.](#page-7-2) Specifically, we sample three representative classes (CleanAndJerk, Playing Violin, and Skiing) from the MiniUCF dataset and visualize the temporal evolution of distilled instances. The results clearly demonstrate that our method retains more temporal information, preserving smooth motion transitions across frames. These visualizations further validate the effectiveness of our latent-space video distillation framework in preserving critical spatiotemporal dynamics.

<span id="page-7-2"></span>Image /page/7/Figure/9 description: This image displays a comparison of image generation results from two methods, labeled 'DM' and 'Ours', across three different scenarios: (a) CleanAndJerk, (b) PlayingViolin, and (c) Skiing. Each scenario features two rows of images. The top row, labeled 'DM', shows images that appear noisy and somewhat abstract. The bottom row, labeled 'Ours', presents clearer and more defined images for each scenario. In (a) CleanAndJerk, the 'Ours' row shows figures of people in what looks like an indoor setting. In (b) PlayingViolin, the 'Ours' row depicts a person playing a violin with a warm, blurred aesthetic. In (c) Skiing, the 'Ours' row shows figures in motion, likely skiing, with a focus on movement and a blurred background.

Figure 4. Inter-frame comparison between DM and our method. Our frames are reconstructed from saved tensors and decoded by a 3D-VAE.

## 5. Conclusion

In this work, we introduce a novel latent-space video dataset distillation framework that leverages VAE encoding, Diversity-Aware Data Selection, and High-Order Singular Value Decomposition (HOSVD) to achieve state-ofthe-art performance with efficient storage. By applying training-free latent compression, our method preserves essential spatiotemporal dynamics while significantly reducing redundancy. Extensive experiments demonstrate that our approach outperforms prior pixel-space methods across multiple datasets, achieving higher accuracy. We believe our method provides an effective and scalable solution for video dataset distillation, enabling improved efficiency in training deep learning models.

Future Work While our selection-based, training-free methods have shown strong performance, there is still room for improvement. In future work, we plan to explore learning-based approaches to enhance dataset distillation, aiming to improve both efficiency and generalization. We also intend to investigate non-linear decomposition techniques for latent-space compression, which could offer a more compact and expressive representation than linear methods like HOSVD, further boosting storage efficiency while preserving key video dynamics.

## References

- <span id="page-8-29"></span>[1] stabilityai/sd-vae-ft-mse · Hugging Face — huggingface.co. [https://huggingface.co/stabilityai/sd](https://huggingface.co/stabilityai/sd-vae-ft-mse)[vae-ft-mse](https://huggingface.co/stabilityai/sd-vae-ft-mse). [Accessed 07-03-2025]. [11](#page-10-0)
- <span id="page-8-27"></span>[2] João Carreira and Andrew Zisserman. Quo vadis, action recognition? a new model and the kinetics dataset. In *CVPR*, pages 4724–4733, 2017. [6](#page-5-0)
- <span id="page-8-9"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022. [2,](#page-1-0) [5,](#page-4-1) [6,](#page-5-0) [7](#page-6-3)
- <span id="page-8-0"></span>[4] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dcbench: Dataset condensation benchmark. *arXiv preprint arXiv:2207.09639*, 2022. [1](#page-0-0)
- <span id="page-8-1"></span>[5] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *Proceedings of the International Conference on Machine Learning (ICML)*, pages 6565–6590, 2023. [1](#page-0-0)
- <span id="page-8-20"></span>[6] Yuxuan Duan, Jianfu Zhang, and Liqing Zhang. Dataset distillation in latent space. *arXiv preprint arXiv:2311.15547*, 2023. [3](#page-2-1)
- <span id="page-8-24"></span>[7] Lorenzo Ghilotti, Mario Beraha, and Alessandra Guglielmi. Bayesian clustering of high-dimensional data via latent repulsive mixtures. *arXiv preprint arXiv:2303.02438*, 2023. [4](#page-3-0)
- <span id="page-8-28"></span>[8] Raghav Goyal, Samira Ebrahimi Kahou, Vincent Michalski, Joanna Materzyńska, Susanne Westphal, Heuna Kim, Valentin Haenel, Ingo Fruend, Peter Yianilos, Moritz Mueller-Freitag, Florian Hoppe, Christian Thurau, Ingo Bax, and Roland Memisevic. The "something something" video database for learning and evaluating visual common sense, 2017. [6](#page-5-0)
- <span id="page-8-17"></span>[9] Jonathan Ho, William Chan, Chitwan Saharia, Jay Whang, Ruiqi Gao, Alexey Gritsenko, Diederik P Kingma, Ben Poole, Mohammad Norouzi, David J Fleet, et al. Imagen video: High definition video generation with diffusion models. *arXiv preprint arXiv:2210.02303*, 2022. [3](#page-2-1)
- <span id="page-8-25"></span>[10] Xing Hu, Yuan Cheng, Dawei Yang, Zhihang Yuan, Jiangyong Yu, Chen Xu, and Sifan Zhou. I-llm: Efficient integeronly inference for fully-quantized low-bit large language models. *arXiv preprint arXiv:2405.17849*, 2024. [5](#page-4-1)
- <span id="page-8-13"></span>[11] De-An Huang, Vignesh Ramanathan, Dhruv Mahajan, Lorenzo Torresani, Manohar Paluri, Li Fei-Fei, and Juan Carlos Niebles. What makes a video a video: Analyzing temporal information in video understanding models and datasets. In *CVPR*, pages 7366–7375, 2018. [2](#page-1-0)
- <span id="page-8-23"></span>[12] Abiodun M Ikotun, Absalom E Ezugwu, Laith Abualigah, Belal Abuhaija, and Jia Heming. K-means clustering algorithms: A comprehensive review, variants analysis, and advances in the era of big data. *Information Sciences*, 622: 178–210, 2023. [4,](#page-3-0) [7](#page-6-3)
- <span id="page-8-15"></span>[13] Diederik P Kingma, Max Welling, et al. Auto-encoding variational bayes, 2013. [2,](#page-1-0) [3,](#page-2-1) [4](#page-3-0)
- <span id="page-8-21"></span>[14] Diederik P Kingma, Max Welling, et al. An introduction to variational autoencoders. *Foundations and Trends® in Machine Learning*, 12(4):307–392, 2019. [3](#page-2-1)
- <span id="page-8-18"></span>[15] Dan Kondratyuk, Lijun Yu, Xiuye Gu, Jose Lezama, ´ Jonathan Huang, Rachel Hornung, Hartwig Adam, Hassan

Akbari, Yair Alon, Vighnesh Birodkar, et al. Videopoet: A large language model for zero-shot video generation. *arXiv preprint arXiv:2312.14125*, 2023. [3](#page-2-1)

- <span id="page-8-26"></span>[16] H. Kuehne, H. Jhuang, E. Garrote, T. Poggio, and T. Serre. Hmdb: A large video database for human motion recognition. In *ICCV*, pages 2556–2563, 2011. [6](#page-5-0)
- <span id="page-8-5"></span>[17] Alex Kulesza and Ben Taskar. Determinantal point processes for machine learning. *Foundations and Trends in Machine Learning*, 5(2-3):123–286, 2012. [1,](#page-0-0) [4](#page-3-0)
- <span id="page-8-22"></span>[18] Carlotta Langer, Yasmin Kim Georgie, Ilja Porohovoj, Verena Vanessa Hafner, and Nihat Ay. Analyzing multimodal integration in the variational autoencoder from an informationtheoretic perspective. *arXiv preprint arXiv:2411.00522*, 2024. Accessed: 2024-11-01. [4](#page-3-0)
- <span id="page-8-12"></span>[19] Longzhen Li, Guang Li, Ren Togo, Keisuke Maeda, Takahiro Ogawa, and Miki Haseyama. Generative Dataset Distillation: Balancing global structure and local details. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), Workshop*, pages 7664–7671, 2024. [2](#page-1-0)
- <span id="page-8-14"></span>[20] Xin Liu, Silvia L. Pintea, Fatemeh Karimi Nejadasl, Olaf Booij, and Jan C. van Gemert. No frame left behind: Full video action recognition. In *CVPR*, pages 14892–14901, 2021. [2](#page-1-0)
- <span id="page-8-11"></span>[21] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *arXiv preprint arXiv:2302.14416*, 2023. [2](#page-1-0)
- <span id="page-8-2"></span>[22] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022. [1](#page-0-0)
- <span id="page-8-16"></span>[23] Xin Ma, Yaohui Wang, Gengyun Jia, Xinyuan Chen, Ziwei Liu, Yuan-Fang Li, Cunjian Chen, and Yu Qiao. Latte: Latent diffusion transformer for video generation. *arXiv preprint arXiv:2401.03048*, 2024. [2](#page-1-0)
- <span id="page-8-8"></span>[24] Dionysis Manousakas, Zuheng Xu, Cecilia Mascolo, and Trevor Campbell. Bayesian pseudocoresets. In *NeurIPS*, 2020. [2](#page-1-0)
- <span id="page-8-19"></span>[25] Brian B Moser, Federico Raue, Sebastian Palacio, Stanislav Frolov, and Andreas Dengel. Latent dataset distillation with diffusion models. *arXiv preprint arXiv:2403.03881*, 2024. [3](#page-2-1)
- <span id="page-8-6"></span>[26] Elvis Nava, Mojmir Mutny, and Andreas Krause. Diversified sampling for batched bayesian optimization with determinantal point processes. In *International Conference on Artificial Intelligence and Statistics*, pages 7031–7054. PMLR, 2022. [1](#page-0-0)
- <span id="page-8-3"></span>[27] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [1](#page-0-0)
- <span id="page-8-10"></span>[28] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021. [2](#page-1-0)
- <span id="page-8-4"></span>[29] Rajesh Ranganath, Sean Gerrish, and David Blei. Black box variational inference. In *AISTATS*, 2014. [1](#page-0-0)
- <span id="page-8-7"></span>[30] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018. [2,](#page-1-0) [5,](#page-4-1) [6](#page-5-0)

- <span id="page-9-13"></span>[31] Younggyo Seo, Kimin Lee, Fangchen Liu, Stephen James, and Pieter Abbeel. Harp: Autoregressive latent video prediction with high-fidelity image generator. In *2022 IEEE International Conference on Image Processing (ICIP)*, pages 3943–3947. IEEE, 2022. [2](#page-1-0)
- <span id="page-9-17"></span>[32] Khurram Soomro, Amir Roshan Zamir, and Mubarak Shah. UCF101: A dataset of 101 human actions classes from videos in the wild. *CoRR*, abs/1212.0402, 2012. [6](#page-5-0)
- <span id="page-9-8"></span>[33] Piyush Tiwary, Kumar Shubham, Vivek Kashyap, et al. Constructing bayesian pseudo-coresets using contrastive divergence. *arXiv preprint arXiv:2303.11278*, 2023. [2](#page-1-0)
- <span id="page-9-5"></span>[34] Zhan Tong, Yibing Song, Jue Wang, and Limin Wang. Videomae: Masked autoencoders are data-efficient learners for self-supervised video pre-training. In *NeurIPS*, pages 10078–10093. Curran Associates, Inc., 2022. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-9-18"></span>[35] Du Tran, Lubomir Bourdev, Rob Fergus, Lorenzo Torresani, and Manohar Paluri. Learning spatiotemporal features with 3d convolutional networks. In *2015 IEEE International Conference on Computer Vision (ICCV)*, pages 4489–4497, 2015. [6](#page-5-0)
- <span id="page-9-1"></span>[36] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022. [1](#page-0-0)
- <span id="page-9-12"></span>[37] Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023. [2](#page-1-0)
- <span id="page-9-0"></span>[38] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-9-3"></span>[39] Ziyu Wang, Yue Xu, Cewu Lu, and Yong-Lu Li. Dancing with still images: Video distillation via static-dynamic disentanglement. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 6296–6304, 2024. [1,](#page-0-0) [2,](#page-1-0) [4,](#page-3-0) [5,](#page-4-1) [6,](#page-5-0) [7](#page-6-3)
- <span id="page-9-7"></span>[40] Max Welling. Herding dynamical weights to learn. In *ICML*, 2009. [1,](#page-0-0) [2,](#page-1-0) [5,](#page-4-1) [6](#page-5-0)
- <span id="page-9-9"></span>[41] Shuo Yang, Zeke Xie, Hanyu Peng, Minjing Xu, Mingming Sun, and P. Li. Dataset pruning: Reducing training data by examining generalization influence. *ArXiv*, abs/2205.09329, 2022. [2](#page-1-0)
- <span id="page-9-16"></span>[42] Juyoung Yun, Sol Choi, Francois Rameau, Byungkon Kang, and Zhoulai Fu. Standalone 16-bit training: Missing study for hardware-limited deep learning practitioners. *arXiv preprint arXiv:2305.10947*, 2023. [6](#page-5-0)
- <span id="page-9-14"></span>[43] David Junhao Zhang, Jay Zhangjie Wu, Jia-Wei Liu, Rui Zhao, Lingmin Ran, Yuchao Gu, Difei Gao, and Mike Zheng Shou. Show-1: Marrying pixel and latent diffusion models for text-to-video generation. *arXiv preprint arXiv:2309.15818*, 2023. [3](#page-2-1)
- <span id="page-9-2"></span>[44] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021. [1](#page-0-0)
- <span id="page-9-11"></span>[45] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023. [2,](#page-1-0) [5,](#page-4-1) [6,](#page-5-0) [7](#page-6-3)
- <span id="page-9-10"></span>[46] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [2](#page-1-0)

- <span id="page-9-6"></span>[47] Sijie Zhao, Yong Zhang, Xiaodong Cun, Shaoshu Yang, Muyao Niu, Xiaoyu Li, Wenbo Hu, and Ying Shan. Cvvae: A compatible video vae for latent generative video models. *Advances in Neural Information Processing Systems*, 37: 12847–12871, 2025. [1,](#page-0-0) [2,](#page-1-0) [5,](#page-4-1) [6,](#page-5-0) [11](#page-10-0)
- <span id="page-9-4"></span>[48] Yinjie Zhao, Heng Zhao, Bihan Wen, Yew-Soon Ong, and Joey Tianyi Zhou. Video set distillation: Information diversification and temporal densification. *arXiv preprint arXiv:2412.00111*, 2024. [1,](#page-0-0) [2,](#page-1-0) [5,](#page-4-1) [6](#page-5-0)
- <span id="page-9-15"></span>[49] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022. [5,](#page-4-1) [6](#page-5-0)

<span id="page-10-0"></span>

## A. VAE

### A.1. 2D-VAE Quantization

Variational Autoencoders (VAEs) enable significant data compression by encoding each image as a probability distribution in a learned latent space, having the architecture like in Fig. [5.](#page-10-1) The 2D-VAE used in this paper optimizes the following loss function:

$$
\mathcal{L}_{\text{VAE}} = \mathbb{E}_{q_{\phi}(\mathbf{z}|\mathbf{x})} [\log p_{\theta}(\mathbf{x} \mid \mathbf{z})] - D_{\text{KL}}(q_{\phi}(\mathbf{z} \mid \mathbf{x}) \parallel p_{\theta}(\mathbf{z}))
$$
\n(7)

The first term minimizes the reconstruction loss when decoding the latent representation of an image, while the second term, the KL divergence, ensures each encoded distribution aligns with a normal prior distribution. Combined, the objective balances the quality of decoded images and the smoothness of the latent distribution.

In order to ensure a fair comparison with previous work, the weights of the VAE are quantized through post-training static quantization, reducing the bid-width from 32 to 8 bits:

$$
x_q = \text{round}\left(\frac{x}{s}\right) + z \tag{8}
$$

Where  $s$  is the scaling factor, and  $z$  is the zero point.

By applying linear quantization, the size of the pretrained model is reduced to one-fourth of its original size. Empirically, the quantized VAE continues to yield high accuracy during experimentation. Compared to other methods such as quantization-aware training, static quantization has the advantage of retaining a high level of accuracy while offering lower computational complexity during the quantization phase.

### B. Implementation Details

In this section, we provide implementation details of our experiments, including the selection of VAEs, the preprocessing steps applied to video datasets, and the measures taken to ensure a fair comparison.

#### B.1. Additional VAE Selection

We have adopted and quantized SD-VAE-FT-MSE[\[1\]](#page-8-29) and CV-VAE[\[47\]](#page-9-6) in our experiments. The variational autoencoders are used to encode video sequences into a compact latent space, enabling efficient dataset distillation. When dealing with IPC 1, where storage constraints are particularly strict, we employ SD-VAE-FT-MSE, a 2D-VAE, which compresses videos as independent frames, allowing for highly compact storage. In contrast, for IPC 5, we utilize CV-VAE, a 3D-VAE, which explicitly models temporal dependencies in video sequences. Unlike 2D-VAEs, which treat frames as separate entities, 3D-VAEs capture motion continuity and temporal redundancy, effectively reducing

<span id="page-10-1"></span>Image /page/10/Figure/13 description: This is a diagram illustrating the architecture of a variational autoencoder. On the left, labeled 'Input Data', is a green rectangle labeled 'X'. An arrow points from 'X' to a blue trapezoid labeled 'Encoder'. From the 'Encoder', an arrow points to an orange rectangle labeled 'Z' and 'Latent Space'. Inside the orange rectangle, a curve resembling a probability distribution is shown. An arrow points from the 'Latent Space' to a blue trapezoid labeled 'Decoder'. Finally, an arrow points from the 'Decoder' to a green rectangle labeled 'X'' and 'Output Data'.

Figure 5. Architecture of Variational Autoencoder(VAE).

redundant information across consecutive frames. This results in a more structured latent representation, ensuring that only the most informative motion features are retained, leading to improved efficiency in video dataset distillation. This selective choice of VAE architectures ensures that our distilled datasets achieve the optimal balance between compression efficiency and information retention across different IPC levels.

#### B.2. Quantized VAE Model Size

We apply post-training static quantization on SD-VAE-FT-MSE, compressing the model from original 335MB to 80MB, achieving around 76% compression rate.

#### B.3. Fair Comparison

Throughout our experiments across four video datasets under two IPC settings (1 and 5), we rigorously ensure that the storage used by our method does not exceed predefined storage constraints. For example, in MiniUCF IPC 1, previous methods allocate a storage limit of 115MB. Under the same setting, we sample 24 instances per class and apply HOSVD with a compression rate of 0.75, saving the core tensor and factor matrices. The resulting distilled dataset occupies 27MB, while the quantized 2D-VAE requires 80MB, leading to a total memory consumption of 107MB, which remains within the 115MB storage budget. The detailed storage consumption can be found in Tab. [5.](#page-10-2)

<span id="page-10-2"></span>

| Dataset | MiniUCF | HMDB51 | Kinetics-400 | SSv2   |
|---------|---------|--------|--------------|--------|
| IPC 1   | 107 MB  | 107 MB | 148 MB       | 223 MB |
| IPC 5   | 475 MB  | 475 MB | 455 MB       | 458 MB |

Table 5. Storage consumed by our method for each dataset. Storage represents the total size of the distilled tensors and the associated VAE model.

## B.4. Sampling Methods

In Tab. [6,](#page-11-0) we have provided a detailed accuracies on different sampling and dataset distillation techniques evaluating on the dataset SSv2 when IPC is 5.

<span id="page-11-0"></span>

|                             | $\overline{\text{Random}}$ $\overline{\text{DM} + \text{VDSD}}$ $\overline{\text{MTT} + \text{VDSD}}$ $\overline{\text{IDTD}}$ $\overline{\text{Kmeans}}$ $\overline{\text{DPPs only}}$ |  | <b>Ours</b>                                       |
|-----------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|---------------------------------------------------|
| $3.9 \pm 0.1$ $4.0 \pm 0.1$ | $8.3 + 0.1$                                                                                                                                                                             |  | $19.5 + 0.3$ $7.2 + 0.3$ $9.3 + 0.1$ $10.5 + 0.2$ |

Table 6. Performance of different dataset distilation and data sampling methods on the SSv2 dataset under IPC 1.

## C. Peak Memory Analysis

To assess the efficiency of our method in terms of memory consumption, we compare the peak GPU memory usage during dataset distillation with other methods: DM and VDSD. As shown in Tab. [7,](#page-11-1) our method achieves the lowest peak memory consumption at 11,085 MiB, significantly reducing memory usage compared to DM (20,457 MiB) and VDSD (12,545 MiB).

<span id="page-11-1"></span>

| Method     | DM          | VDSD        | Ours        |
|------------|-------------|-------------|-------------|
| GPU Memory | 20, 457 MiB | 12, 545 MiB | 11, 085 MiB |

Table 7. Peak memory comparsion between different dataset distillation methods on MiniUCF when IPC is 5.

Our method minimizes peak memory usage by operating in the latent space and leveraging training-free compression via HOSVD, significantly reducing redundant memory allocation during dataset distillation. This lower memory footprint allows our approach to scale to larger datasets and higher IPC settings while maintaining efficiency.

## D. Runtime Analysis

To assess the computational efficiency of our method, we compare its distillation runtime with VDSD across different datasets. All experiments are conducted on an NVIDIA H100 SXM GPU. Our training-free method demonstrates a significant speed advantage, particularly on large-scale datasets, due to its latent-space processing and training-free compression strategy.

On small-scale datasets, such as HMDB51 and Mini-UCF, our method completes the dataset distillation process in under 10 minutes, whereas VDSD requires 2.5 hours. The efficiency gain is even more pronounced on large-scale datasets, where our method finishes in approximately 1 hour on Kinetics-400 and SSv2, while VDSD exceeds 5 hours.

These results confirm that our latent-space approach significantly reduces computational overhead compared to pixel-space distillation methods like VDSD. By leveraging structured compression techniques such as HOSVD and eliminating costly iterative optimization steps, our method achieves faster dataset distillation without compromising performance. This makes our approach highly scalable and practical for real-world applications, especially in largescale video analysis scenarios.

# 12

## E. Visualization

We provide the reconstructed and decoded frames of our method for MiniUCF across 20 classes in Fig. [6.](#page-12-0)

<span id="page-12-0"></span>Image /page/12/Picture/0 description: This image displays a grid of video frames, likely representing reconstructed and decoded sequences from a 3D VAE model. The frames are arranged in rows and columns, showcasing various scenes and subjects. The top rows appear to depict horses in motion, followed by frames showing people in different settings, some with blurred backgrounds. Several rows feature abstract or heavily processed visuals, possibly indicating the model's interpretation or generation capabilities. The bottom rows show more distinct imagery, including what looks like outdoor scenes with greenery and water, and figures in motion. The overall impression is a visual representation of video data processed or generated by an artificial intelligence model.

Figure 6. Reconstructed and decoded frames of our method for MiniUCF with a 3D-VAE.
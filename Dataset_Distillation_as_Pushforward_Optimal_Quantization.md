# Dataset Distillation as Pushforward Optimal Quantization

**<PERSON>**

*Department of Applied Mathematics and Theoretical Physics University of Cambridge, UK*

<PERSON>

*GSK.ai*

<span id="page-0-0"></span><PERSON> **<EMAIL>** 

<NAME_EMAIL>

## Abstract

Dataset distillation aims to find a synthetic training set such that training on the synthetic data achieves similar performance to training on real data, with orders of magnitude less computational requirements. Existing methods can be broadly categorized as either bi-level optimization problems that have neural network training heuristics as the lower level problem, or disentangled methods that bypass the bi-level optimization by matching distributions of data. The latter method has the major advantages of speed and scalability in terms of size of both training and distilled datasets. We demonstrate that when equipped with an encoder-decoder structure, the empirically successful disentangled methods can be reformulated as an optimal quantization problem, where a finite set of points is found to approximate the underlying probability measure by minimizing the expected projection distance. In particular, we link existing disentangled dataset distillation methods to the classical optimal quantization and Wasserstein barycenter problems, demonstrating consistency of distilled datasets for diffusion-based generative priors. We propose a simple extension of the state-ofthe-art data distillation method D4M, achieving better performance on the ImageNet-1K dataset with trivial additional computation, and state-of-the-art performance in higher image-per-class settings. Keywords: dataset distillation, optimal quantization, Wasserstein optimality, diffusion models, consistency, convergence

## 1. Introduction

Training powerful neural networks requires a large amount of data, and thus induces high computational requirements. *Dataset distillation* (DD) targets this computational difficulty by optimizing over the data, as opposed to other parts of training such as optimization or architecture [\(Wang et al.,](#page-23-0) [2018\)](#page-23-0). This consists of finding a synthetic training set, such that training a neural network on the synthetic data yields similar performance.

There are several closely related notions of reducing computational load when training new models on datasets. Core-set methods find a subset of training data (as opposed to synthetic data) such that a model trained on said subset will have similar performance [\(Mirzasoleiman et al.,](#page-22-0) [2020;](#page-22-0) [Feldman,](#page-20-0) [2020\)](#page-20-0). Model distillation, sometimes known as knowledge distillation, aims to train a smaller (student) model that predicts the output of a pre-trained (teacher) model [\(Gou et al.,](#page-20-1) [2021;](#page-20-1) [Polino et al.,](#page-22-1) [2018\)](#page-22-1). Importance sampling methods accelerate training by weighting training data, finding examples that are more influential for training [\(Paul et al.,](#page-22-2) [2021\)](#page-22-2). For more detailed surveys on dataset distillation methods and techniques, we refer to [\(Yu et al.,](#page-24-0) [2023;](#page-24-0) [Sachdeva and McAuley,](#page-22-3) [2023\)](#page-22-3).

Dataset distillation remains one of the most promising approaches to distilling very large scale datasets due to its focus on data, rather than optimizer or network architecture. However, it has not yet received much theoretical interpretation. In this work, we give a theoretical justification for the disentangled formulation, as well as propose a new state-of-the-art large scale DD method based on these insights. We first outline two major paradigms of DD, namely the bi-level formulation and its extensions using generative priors, and then the disentangled formulation which decouples the bi-level formulation.

### 1.1. Bi-level formulation of dataset distillation.

We first present the DD problem statement as given in [Sachdeva and McAuley](#page-22-3) [\(2023\)](#page-22-3). Denote a training set by  $\mathcal T$  (more generally, distribution of training data), and the expected and empirical risks (test and training loss) by  $R$  and  $\mathcal L$  respectively, which each take some network parameters  $\theta$ . Given a learning algorithm  $\Phi = \Phi_S$ , which takes a dataset S and returns an L-optimal parameter, the goal of DD is to find a synthetic dataset  $S$  (of given size) minimizing the test loss discrepancy:

<span id="page-1-0"></span>
$$
S = \underset{S}{\arg\min} \underset{(x,y)\sim\mathcal{T}}{\sup} |\mathcal{R}(\Phi_S) - \mathcal{R}(\Phi_T)|. \tag{1}
$$

This formulation is computationally intractable. Typical approximations include evaluating the expected risk R over a test dataset, replacing the learning algorithm  $\Phi$  with a finite-length approximate algorithm such as unrolled gradient descent on its dataset, and solving the outer minimization problem using gradient methods. There exist various heuristic relaxations to the bi-level formulation [\(1\)](#page-1-0), which we summarize.

1. (*Meta-learning.*) This uses the assumption that more data produces better results, replacing the risk matching objective with a risk minimization objective and removing the dependence on  $\tau$ [\(Wang et al.,](#page-23-0) [2018;](#page-23-0) [Deng and Russakovsky,](#page-20-2) [2022\)](#page-20-2). The learning algorithm  $\Phi_S = \Phi_S(\theta_0)$  with initial parameter  $\theta_0$  is given by unrolling gradient steps  $\theta_{t+1} = \theta_t - \eta \nabla \mathcal{L}_S(\theta_t)$ . The distilled dataset minimizes the training loss as trained on the distilled set up to  $T$  iterations:

$$
\underset{S}{\arg\min} \mathbb{E}_{\theta_0 \sim p_\theta} \left[ \mathcal{L}_{\mathcal{T}}(\theta_T) \right]. \tag{2}
$$

2. (*Distribution matching.*) Inspired by reproducing kernel Hilbert spaces and the empirical approximations using random neural network features, [Zhao and Bilen](#page-24-1) [\(2023\)](#page-24-1) proposes an alternate distillation objective that is independent of target loss. The minimization objective is

$$
\underset{\mathcal{S}}{\arg\min} \mathbb{E}_{\theta \sim p_{\theta}} \| \frac{1}{|\mathcal{T}|} \sum_{x \in \mathcal{T}} \psi_{\theta}(x) - \frac{1}{|\mathcal{S}|} \sum_{x \in \mathcal{S}} \psi_{\theta}(x) \|^{2}, \tag{3}
$$

where  $\psi_{\theta}$  are randomly initialized neural networks. This can be intuitively interpreted as matching the (first moment of) neural network features over the synthetic data.

3. (*Trajectory matching.*) For a *fixed network architecture*, this method aims to match the gradient information of the synthetic and true training datasets from different initializations [\(Cazenavette](#page-20-3) [et al.,](#page-20-3) [2022\)](#page-20-3). The heuristic is that similar network parameters will give similar performance. In addition, the gradients are allowed to be accelerated by matching a small number of synthetic training steps with a large number of real data training steps: for  $N \ll M$  steps, the Matching

Training Trajectory (MTT) objective is (with abuse of notation):

\

$$
\arg\min_{\mathcal{S}} \mathbb{E}_{\theta_0 \sim p_{\theta}} \sum_{t=1}^{T-M} \frac{\|\theta_{t+N}^{\mathcal{S}} - \theta_{t+M}^{\mathcal{T}}\|^2}{\|\theta_{t+M}^{\mathcal{T}} - \theta_t^{\mathcal{T}}\|^2} \quad (4)
$$
where  $\theta_{t+1}^{\mathcal{T}} = \theta_t^{\mathcal{T}} - \eta \nabla \mathcal{L}_{\mathcal{T}}(\theta_t^{\mathcal{T}})$ ,  $\theta_{t+i+1}^{\mathcal{S}} = \theta_{t+i}^{\mathcal{S}} - \eta \nabla \mathcal{L}_{\mathcal{S}}(\theta_{t+i}^{\mathcal{S}})$ ,  $\theta_{t+0}^{\mathcal{S}} = \theta_t^{\mathcal{T}}.$ 

Practical computational approximations include pre-training a large number of teacher networks from different initializations.

Various other methods include neural feature matching [\(Zhou et al.,](#page-24-2) [2022;](#page-24-2) [Loo et al.,](#page-22-4) [2022\)](#page-22-4) and the corresponding neural tangent kernel methods [\(Nguyen et al.,](#page-22-5) [2021\)](#page-22-5), representative matching [\(Liu](#page-21-0) [et al.,](#page-21-0) [2023b\)](#page-21-0), and group robustness [\(Vahidian et al.,](#page-23-1) [2024\)](#page-23-1). While the bi-level formulation is intuitive and follows naturally from the formal problem statement [\(1\)](#page-1-0), there are two main drawbacks:

- 1. Computational complexity. Solving bi-level optimization problems are difficult and timeconsuming, especially when the inner optimization problem requires training a neural network. In particular, backpropagating through neural network optimization steps carries significant memory requirements, and is infeasible for very large scale datasets such as ImageNet [\(Yin](#page-24-3) [et al.,](#page-24-3) [2023\)](#page-24-3). Moreover, the memory scales linearly with the number of desired distilled images, becoming infeasible at around 10 images per class (IPC) on smaller CIFAR datasets.
- 2. Model architecture dependence. In the bi-level optimization formulation, the neural network architecture is usually fixed when constructing the synthetic dataset. Many distilled datasets fail when considering new model architectures [\(Cazenavette et al.,](#page-20-3) [2022\)](#page-20-3).

#### 1.1.1. GENERATIVE PRIORS.

As opposed to optimizing over the image space, an alternative paradigm is to use generative models to directly generate feasible samples, such as with diffusion models or GANs. The seminal work of [\(Cazenavette et al.,](#page-20-4) [2023\)](#page-20-4) proposes Generative Latent Distillation (GLaD), which incorporates a powerful (differentiable) generative prior into one of the DD objectives given above. The usage of the generative model leads to more visually coherent distilled images, and allows for optimization in a lower-dimensional latent space.

[Gu et al.](#page-21-1) [\(2024\)](#page-21-1) employs parameter efficient fine tuning on pre-trained diffusion models, using loss terms that balance faithfulness and diversity. They demonstrate better test-accuracy when training on various network archiectures compared to pre-trained models, distribution matching and GLaD, as well as lower maximum mean discrepancy of extracted features at varying IPCs.

A more recent paradigm considers using powerful pre-trained latent diffusion models (LDMs), which utilize autoencoders for dimensionality reduction [\(Rombach et al.,](#page-22-6) [2022\)](#page-22-6). While standard diffusion models perform noising in the image space directly, an LDM instead performs scorematching on the latent space (given by downsampled multi-channel images) of an autoencoder. This replaces a significant amount of training time with a relatively cheap additional call to a decoder during inference, while maintaining competitive performance with diffusion models trained directly on image space.

[Moser et al.](#page-22-7) [\(2024\)](#page-22-7) considers replacing the GAN with a pair of autoencoders and latent diffusion model. This allows initializing the latent codes using a simple encoder pass, rather than having to rely on expensive GAN-inversion techniques [\(Xia et al.,](#page-23-2) [2022\)](#page-23-2), and significantly increases DD performance when using gradient matching or distribution matching.

The first prototypical example of using a generative model to alleviate the aforementioned drawbacks of the bi-level formulation is by "factorizing" the image problem, using a generative "hallucinator" network to generate samples from a common set of latent "base" points [\(Liu et al.,](#page-21-2) [2022\)](#page-21-2). This was able to increase cross-architecture performance when compared to previous imagespace methods.

### 1.2. Disentangled methods.

While the above generative prior models significantly reduce the amount of computational power, the underlying algorithm still relies on some bilevel optimization method, typically MTT. This limits the applicability of such methods on large scale datasets, on which dataset distillation would be particularly useful for computationally limited applications. For example, the ImageNet-1K dataset consists of 1.2M training images, totalling over 120GB of memory [\(Deng et al.,](#page-20-5) [2009\)](#page-20-5). The full dataset ImageNet-21K consists of over 14M images and takes up around 1.2TB of memory, which is generally infeasible to train expert models on, and impossible to backpropagate through gradient steps.

[Yin et al.](#page-24-3) [\(2023\)](#page-24-3) is the first work to target the fundamental algorithmic change by "disentangling" the bi-level optimization framework into three separate optimization problems, named *Squeeze, Recover and Relabel* (SRe<sup>2</sup>L). First, a model is trained on the whole dataset in the usual manner. Using statistics from batch-normalizing layers, a synthetic dataset is then optimized by matching the desired target label and class statistics [\(Yin et al.,](#page-23-3) [2020\)](#page-23-3). The final step is to *relabel* the synthetic dataset using a trained model similarly to some knowledge distillation methods [\(Shen and Xing,](#page-23-4) [2022\)](#page-23-4), typically done with a pre-trained ResNet-18 model. The final distilled dataset is then given by the synthetic images, plus the soft labels as given by outputs of the pre-trained networks. The authors then extend this method to the larger ImageNet-21K dataset using curriculum learning, giving the *Curriculum Data Learning* (CDA) method [\(Yin and Shen,](#page-23-5) [2024\)](#page-23-5).

[Liu et al.](#page-21-3) [\(2023a\)](#page-21-3) considers using a pre-trained feature model to extract features from the dataset. The Wasserstein barycenters are then computed from the feature space and stored, which is computationally cheaper than storing images. This is then lifted to optimization in the image space by matching the features of the synthetic images and adding batch-norm regularization as in [Yin et al.](#page-24-3) [\(2023\)](#page-24-3), bypassing the inner network optimization loop of bi-level DD methods.

[Su et al.](#page-23-6) [\(2024\)](#page-23-6) uses a pre-trained latent diffusion model to perform the disentangling, using the autoencoder to replace the network inversion step of  $SRe^{2}L$  [\(Yin et al.,](#page-24-3) [2023\)](#page-24-3). Their proposed method considers first distilling synthetic latent codes using classical  $k$ -means clustering, then reconstructing the distilled images from the distilled latents using a diffusion model. This avoids any backpropagation over large datasets and allows for constant memory usage. We replicate their *Dataset Distillation via Disentangled Diffusion Model* (D<sup>4</sup>M) algorithm in Algorithm [3.](#page-16-0) Note that the  $k$ -means clusters are computed using the minibatch  $k$ -means algorithm [\(Sculley,](#page-23-7) [2010\)](#page-23-7), as implemented in the scikit-learn Python package [\(Pedregosa et al.,](#page-22-8) [2011\)](#page-22-8).

[Sun et al.](#page-23-8) [\(2024\)](#page-23-8) proposes *Realistic Diverse and Efficient Dataset Distillation* (RDED), which replaces the latent clustering objective with a patch-based adversarial objective, which heuristically balances the realism and diversity of the generated patches. This is related to the information-theoretic concept of rate reduction, which is a method of explaining orthogonal features in feature layers [\(Chan](#page-20-6)

[et al.,](#page-20-6) [2022\)](#page-20-6). The resulting images consist of multiple down-sampled images stitched together, which can be thought of as artificially increasing the IPC.

While dataset distillation has had extensive experimental effort, few proper theoretical justifications exist in the literature. [Sachdeva and McAuley](#page-22-3) [\(2023\)](#page-22-3) proposes a high-level formulation based on minimizing the difference in test loss between learning on the full dataset versus the synthetic dataset. [Kungurtsev et al.](#page-21-4) [\(2024\)](#page-21-4) considers dataset distillation as dependent on the desired inference task (typically classification with cross-entropy loss for image data). This allows them to interpret trajectory-matching as a mean-field control problem, and find analagous procedures for non-imaging examples like finding collocation points for training physics informed neural networks. However, they do not consider the problem of consistency or convergence of the distilled datasets, which we aim to address in this work.

### 1.3. Contributions.

We summarize the contributions of this work as follows.

- 1. We provide a theoretical justification for disentangled dataset distillation using classical notions of optimal quantization and Wasserstein distance. Motivated by the empirical usage of clustering in latent spaces, we show in Theorem [13](#page-13-0) that optimal quantizations induce convergent approximations of gradients of population risk, and furthermore the rate is given by  $\mathcal{O}(K^{-1/d})$ , where d is the dimension of the latent space and K is the number of quantization points.
- 2. From the aforementioned classical literature, we propose a simple extension of the SOTA method  $D<sup>4</sup>M$  to address a possible performance gap in Wasserstein distance, given in Algorithm [4.](#page-17-0) The full algorithm is given in Section [3.2,](#page-15-0) which we name *Dataset Distillation by Optimal Quantization* (DDOQ). Similarly to D<sup>4</sup>M, this includes latent space clustering, image synthesis, soft label synthesis and an alternate training regime for training net networks.
- 3. We compare our proposed method with  $D^4M$  on the ImageNet-1K dataset, demonstrating significantly better classification accuracy at various IPC budgets. Moreover, we provide a central comparison with various SOTA disentangled distillation methods, demonstrating that our proposed method outperforms others at high IPC.

This paper is structured as follows. Section [2](#page-5-0) covers background on Wasserstein spaces, the optimal quantization and Wasserstein barycenter problems and some algorithms to solve them, score-based diffusion models, and the disentangled DD method  $D<sup>4</sup>M$ . We present various classical convergence results for optimal quantizations and relate them to Wasserstein distances and approximating function expectations. Motivated by these relations, Section [3](#page-12-0) demonstrates consistency of the optimal quantizers when passed through diffusion-based generative priors to the image space, and proposes a simple extension of the  $D<sup>4</sup>M$  method by adding automatically learned weights in the prototyping phase, and fully explains the data distillation pipeline in a sequential and systematic manner. Section [4](#page-17-1) contains experiments of our proposed method against the SOTA  $D<sup>4</sup>M$  method amongst other SOTA baselines on the large-scale ImageNet-1K dataset.

<span id="page-5-0"></span>

## 2. Background

This section covers the necessary background and concepts required for the theoretical interpretation of DD. Section [2.1](#page-5-1) first covers necessary notation. Section [2.2](#page-5-2) introduces the optimal quantization problem, the concept of quadratic distortion, and links it to Wasserstein optimality. Section [2.2.1](#page-8-0) gives some classical algorithms to find quantizations, and provides some classical convergence results for converging measures and distortion bounds. Section [2.3](#page-9-0) provides a short exposition of score-based diffusion models, covering well-posedness and classical divergence bounds.

<span id="page-5-1"></span>

### 2.1. Definitions and Notation

We will need the following notation for our analysis. Define  $P_2(\mathbb{R}^d)$  to be the set of probability measures on  $\mathbb{R}^d$  with finite second moment, not necessarily admitting a density with respect to the Lebesgue measure. We use  $\mathcal{W}_2$  to denote the Wasserstein-2 distance between two probability distributions in  $\mathcal{P}_2(\mathbb{R}^d)$  [\(Santambrogio,](#page-23-9) [2015\)](#page-23-9), defined as

<span id="page-5-3"></span>
$$
\mathcal{W}_2(\mu,\nu) = \left(\inf_{\gamma \in \Gamma(\mu,\nu)} \iint_{\mathbb{R}^d \times \mathbb{R}^d} ||x - y||^2 \gamma(x,y) \,dx \,dy\right)^{1/2},\tag{5}
$$

where  $\Gamma(\mu, \nu)$  denotes the set of all *couplings*, i.e. joint probability measures on  $\mathbb{R}^d \times \mathbb{R}^d$  with

$$
\int_{\mathbb{R}^d} \gamma(x, y) \, \mathrm{d}y = \mu(x), \quad \int_{\mathbb{R}^d} \gamma(x, y) \, \mathrm{d}x = \nu(y).
$$

<span id="page-5-2"></span>

### 2.2. Optimal Quantization

For a probability measure  $\mu \in \mathcal{P}_2(\mathbb{R}^d)$ , the *optimal quantization* (or vector quantization) at level K is set of points  $\{x_1, ..., x_K\} \subset \mathbb{R}^d$  such that the  $\mu$ -average distance to the quantized points is minimal. For the sake of exposition, we will consider the distance to be the  $\ell_2$  distance to be consistent with the above definitions of the Wasserstein-2 distance, but this definition can be generalized to arbitrary norms. This can be formulated as the minimizer of the (quadratic) distortion, defined as follows.

**Definition 1 (Quadratic distortion.)** For a quantization grid  $(x_1, ..., x_K) \in (\mathbb{R}^d)^K$ , the corre*sponding* Voronoi cells *are*

$$
C_i = \{ y \in \mathbb{R}^d \mid |||y - x_i|| = \min_j ||y - x_j|| \}, \quad i = 1, ..., K.
$$
 (6)

*Given a measure*  $\mu \in \mathcal{P}_2(\mathbb{R}^d)$ , the (quadratic) distortion *function*  $\mathcal{G} = \mathcal{G}_\mu$  takes a tuple of points  $(x_1, ..., x_K)$  *and outputs the average squared distance to the set:* 

$$
\mathcal{G} : (x_1, ..., x_K) \mapsto \int_{\mathbb{R}^d} \min_i \|x - x_i\|^2 \, \mu(\mathrm{d}x) = \mathbb{E}_{X \sim \mu}[\min_i \|X - x_i\|^2]. \tag{7}
$$

We will write  $\mathcal{G}_{K,\mu}$  to mean the distortion function at level K, i.e. with domain  $(\mathbb{R}^d)^K$ , and drop the *subscripts where it is clear.*

Voronoi cells are partitions of the space  $\mathbb{R}^d$  into sets of minimal distance to each  $x_i$  (the Voronoi centers), except on their boundaries. If the underlying norm is Euclidean, the Voronoi cells are (non-empty) polyhedral closed convex sets. Note that the quadratic distortion can be extended to arbitrary  $L^p$  versions. From here onwards, we assume that  $\mu \in \mathcal{P}(\mathbb{R}^d)$  has finite second moments, so that the quadratic distortion is finite for any set of points. This implies that an optimal quantizer exists (Pagès,  $2015$ ).

Intuitively, the optimal quantization problem can be thought of a clustering algorithm, where centroids are placed to be close to as much data as possible. Indeed, the k-means clustering algorithm (with a small modification) approximately solves the optimal quantization problem, as the underlying objective is equivalent to minimizing the quadratic distortion for empirical measures. Numerically, it is very similar to clustering algorithms in the case where the underlying measure is known or can be sampled from. However, we note that the optimal quantization weights are uniquely determined by the quantization points, which shows equivalence of the finite-support-constrained Wasserstein minimization problem and the distortion minimization problem (Pages, [2015\)](#page-22-9).

**Proposition 2** *Suppose we have a quantization*  $\mathbf{x} = \{x_1, ..., x_K\}$ *. Assume that the (probability) measure*  $\mu$  *is null on the boundaries of the Voronoi clusters*  $\mu(\partial(C_i)) = 0$ . Then the measure *v* that minimizes the Wasserstein-2 distance [\(5\)](#page-5-3) and satisfies  $\text{supp }\nu \subset \{x_1, ..., x_K\}$  is  $\nu_K =$  $\sum_{i=1}^K \mu(C_i) \delta(x_i)$ *. Moreover, the optimal coupling is given by the projection onto the centroids.* 

**Proof** For any coupling  $\gamma \in \Gamma(\nu, \mu)$  between  $\nu$  and  $\mu$ , we certainly have that

<span id="page-6-0"></span>
$$
\iint \|x-y\|^2 d\gamma(x,y) \ge \iint \text{dist}(\mathbf{x},y)^2 d\gamma(x,y) = \int \text{dist}(\mathbf{x},y)^2 d\mu(y).
$$

where the first inequality comes from definition of distance to a set ( $\gamma$ -a.s.) and the support condition on  $\nu$ , and the equality from the marginal property of couplings. The final term is attained when  $\nu$ has the prescribed form: the coupling is  $c(x_i, y) = \mathbf{1}_{y \in C_i}$  and the corresponding transport map is projection onto the set x (defined  $\mu$ -a.s. from the null condition). This shows a lower bound of [\(5\)](#page-5-3) that is attained. ш

This implies that for a given quantization x, the quadratic distortion satisfies

<span id="page-6-1"></span>
$$
\mathcal{G}(\mathbf{x})^{1/2} = \inf \{ \mathcal{W}_2(\nu, \mu) \mid \text{probability measures } \nu, \text{ supp } \nu \subset \mathbf{x} \},\tag{8}
$$

and furthermore, at quantization level K,

$$
\arg\min_{\mathbf{x}, |\mathbf{x}|=K} \mathcal{G}(\mathbf{x}) = \arg\min \left\{ \mathcal{W}_2(\nu, \mu) \mid \text{probability measures } \nu, |\operatorname{supp} \nu| \le K \right\} \tag{9}
$$

In other words, minimizing the quadratic distortion is equivalent to finding a Wasserstein-2 optimal approximation with a  $K$ -finitely supported (probability) measure. We note that in the case where the approximating measure is a uniform Dirac mixture, this is called the *Wasserstein barycenter* problem [\(Cuturi and Doucet,](#page-20-7) [2014\)](#page-20-7). The Wasserstein distance of the Wasserstein barycenter is generally worse than that of the optimal quantization, but it admits an easily computable dual representation.

Much like how an empirical distribution of i.i.d. variables converges to the underlying distribution, optimal quantizers converge in Wasserstein distance at a rate  $\Theta(K^{-1/d})$  to their respective distributions as the number of particles  $K$  increases [\(Graf and Luschgy,](#page-20-8) [2000\)](#page-20-8). Moreover, any limit point of the optimal quantizers is an optimal quantizer of the limiting distribution [\(Pollard,](#page-22-10) [1982\)](#page-22-10). Moreover, it can be shown that if a sequence of distributions converges in Wasserstein distance  $\mu_n \to \mu$ , then so do the errors in quantization for a fixed quantization level (Liu and Pages, [2020\)](#page-21-5).

**Theorem 3 (Liu and Pages [2020,](#page-21-5) Thm 4)** *Fix a quantization level*  $K \ge 1$ *. Let*  $\mu_n, \mu \in \mathcal{P}_2(\mathbb{R}^d)$ *with support having at least* K *points, such that*  $W_2(\mu_n, \mu) \to 0$  *as*  $n \to \infty$ *. For each*  $n \in \mathbb{N}$ *, let*  $\mathbf{x}^{(n)}$  be an optimal quantizer of  $\mu_n$ . Then

$$
\mathcal{G}_{K,\mu}(\mathbf{x}^{(n)}) - \inf_{\mathbf{x}} \mathcal{G}_{K,\mu}(\mathbf{x}) \le 4e_{K,\mu}^* \mathcal{W}_2(\mu_n, \mu) + 4\mathcal{W}_2^2(\mu_n, \mu),\tag{10}
$$

where  $e_{K,\mu}^* = [\inf_{\mathbf{x}} \mathcal{G}_{K,\mu}(\mathbf{x})]^{1/2}$  is the optimal error.

The following result gives a convergence rate, assuming slightly higher regularity.

**Proposition 4 (Liu and Pages [2020\)](#page-21-5)** Let  $\eta > 0$ , and suppose  $\mu \in \mathcal{P}_{2+\eta}(\mathbb{R}^d)$ . There exists a *universal constant*  $C_{d,\eta} \in (0, +\infty)$  *such that for every quantization level,* 

<span id="page-7-1"></span>
$$
e_{K,\mu}^* \le C_{d,\eta} \cdot \sigma_{2+\eta}(\mu) K^{-1/d},\tag{11}
$$

*where*  $\sigma_r(\mu) = \min_{a \in \mathbb{R}^d} \mathbb{E}_{\mu}[\Vert x - a \Vert^r]^{1/r}$ *.* 

<span id="page-7-0"></span>The quantizer can also be shown to have nice approximation properties when taking expectations of functions.

**Theorem 5** Let  $f : \mathbb{R}^d \to \mathbb{R}$  be an L-Lipschitz function. For a probability measure  $\mu \in \mathcal{P}_2(\mathbb{R}^d)$ *that assigns no mass to hyperplanes, and a quantization*  $\mathbf{x} = (x_1, ..., x_k)$ *, let*  $\nu = \sum_{i=1}^K \mu(C_i) \delta(x_i)$ *be the corresponding Wasserstein-optimal measure with support in* x*, as in Theorem [2.](#page-6-0) The difference between the population risk*  $\mathbb{E}_{\mu}[f]$  *and the weighted empirical risk*  $\mathbb{E}_{\nu}[f]$  *is bounded as* 

$$
\mathbb{E}_{\mu}[f] - \mathbb{E}_{\nu}[f] \le L\mathcal{G}(\mathbf{x})^{1/2}.
$$
 (12)

**Proof** Since  $\mu$  assigns no mass to hyperplanes, we may decompose into Voronoi cells.

$$
\left| \int_{\mathbb{R}^d} f \, \mathrm{d}\mu - \int_{\mathbb{R}^d} f \, \mathrm{d}\nu \right| = \left| \sum_{i=1}^K \int_{C_i} f(x) - f(x_i) \, \mathrm{d}\mu(x) \right|
$$
  
\n
$$
\leq \sum_{i=1}^K \int_{C_i} L \|x - x_i\| \, \mathrm{d}\mu(x)
$$
  
\n
$$
= L \int_{\mathbb{R}^d} \min_i \|x - x_i\| \, \mathrm{d}\mu(x)
$$
  
\n
$$
\leq L \left( \int_{\mathbb{R}^d} \min_i \|x - x_i\| \, \mathrm{d}\mu(x) \right)^{1/2}
$$
  
\n
$$
= L \mathcal{G}(\mathbf{x})^{1/2}.
$$

The first equality comes from definition of  $\nu$ , and the inequalities from the Lipschitz condition and Hölder's inequality respectively. ٠

In the context of dataset distillation,  $f$  can be chosen to be the gradient of a neural network with respect to some loss function. Then, as the number of synthetic data points increases, this bound gives asymptotic convergence of the gradients on the synthetic data, to the gradients on the true training distribution. This can further be combined with the convergence rates given above.

<span id="page-8-0"></span>

#### 2.2.1. SOLVING THE OPTIMAL QUANTIZATION PROBLEM

We now introduce two commonly used quantization algorithms: the competitive learning vector quantization (CLVQ) algorithm [\(Ahalt et al.,](#page-19-0) [1990\)](#page-19-0) and Lloyd's algorithm.

CLVQ. This is derived directly from gradient descent on the quadratic distortion, starting from an initialization in the convex hull of the support of  $\mu$ . For a given quantization, it has a representation in terms of  $\mu$ -centroids of the corresponding Voronoi cells.

Proposition 6 (Differentiability of distortion (Pages, [2015,](#page-22-9) Prop. 3.1)) *Let*  $x = (x_1, ..., x_K) \in$  $(\mathbb{R}^d)^K$  be such that the  $x_i$  are pairwise distinct, and assume that  $\mu(\partial C_i) = 0$ . Then, the quadratic *distortion is differentiable with derivative*

<span id="page-8-1"></span>
$$
\nabla \mathcal{G}(\mathbf{x}) = \left(2 \int_{C_i(x)} (x_i - \xi) \,\mu(\mathrm{d}\xi)\right)_{i=1,\dots,K},\tag{13}
$$

*i.e., the gradient for quantization point* x<sup>i</sup> *points away from the* µ*-centroid of its Voronoi cell.*

The gradient step for some step-sizes  $\gamma_k \in (0,1)$  reads

$$
\mathbf{x}^{(k+1)} = \mathbf{x}^{(k)} - \gamma_k \nabla \mathcal{G}(\mathbf{x}^{(k)}), \quad \mathbf{x}^{(0)} \in \text{Hull}(\text{supp}\,\mu)^K,\tag{14}
$$

where Hull denotes the convex hull. Recalling that the gradient [\(13\)](#page-8-1) is a  $\mu$ -expectation over  $C_i(x)$ , the corresponding stochastic Monte Carlo version of the above gradient descent reads

$$
\mathbf{x}^{(k+1)} = \mathbf{x}^{(k)} - \gamma_k \left( \mathbf{1}_{X_k \in C_i^{(k)}} x_i^{(k)} - X_k \right)_{1 \le i \le K}, \quad X_k \sim \mu. \tag{15}
$$

Note that the computation of this requires the ability to sample from  $\mu$ , as well as being able to compute the nearest neighbor of  $X_k$  to the quantization set  $\mathbf{x}$  (equivalent to the inclusion  $X_k \in C_i^{(K)}$  $i^{(n)}$ ). Further observe that this is precisely equal to the mini-batch  $k$ -means as used in Algorithm [3.](#page-16-0)

While this algorithm produces points  $\mathbf{x} = (x_1, ..., x_K)$ , it remains to compute the associated weights that approximate the measures of the Voronoi cells  $\mu(C_i)$ . This can be done in an online manner using a similar Monte Carlo approach, referred to as the *companion parameters* (Pagès, [2015\)](#page-22-9). The full CLVQ algorithm is presented in Algorithm [1.](#page-9-1)

Convergence to an optimal quantizer is only guaranteed in the case of a log-concave distribution in one dimension (?Liu and Pages, [2020\)](#page-21-5). However, in higher dimensions, convergence to a (not necessarily optimal) stationary grid is possible, see e.g. [Pages and Yu](#page-22-11) [\(2016\)](#page-22-11); Pages [\(2015\)](#page-22-9) and references therein.

<span id="page-8-2"></span>**Proposition 7 (Bally and Pages [2003,](#page-20-9) Prop. 7)** *Assume that the measure*  $\mu \in \mathcal{P}_{2+\eta}$  *for some*  $\eta > 0$ , and that it assigns no mass to hyperplanes. Assume further that the grids  $\mathbf{x}^{(t)}$  produced by CLVQ Algorithm [1](#page-9-1) converge to a stationary grid  $\mathbf{x}^*$ , i.e.  $\nabla \mathcal{G}(\mathbf{x}^*)$  $\sum$ ) = 0*, and that the step-sizes satisfy*  $\gamma_k = +\infty$  and  $\sum \gamma_k^{1+\delta} < \infty$  for some  $\delta > 0$ . Then,

*1. The companion weights* w<sup>i</sup> *converge almost surely to the measures of the limiting Voronoi cells*  $\mu(C_i^*)$ .

## Algorithm 1: CLVQ

<span id="page-9-1"></span>**Data:** initial cluster centers  $x_1^{(0)}$  $x_1^{(0)},...,x_K^{(0)},$  step-sizes  $(\gamma_i)_{i\geq 0}$ 1 Initialize weights  $w = (w_1, ..., w_K) = (1/K, ..., 1/K);$  $2 \; k \leftarrow 0;$ 3 while *not converged* do 4 Sample  $X_k \sim \mu$ ; 5 Select "winner"  $i_{\text{win}} \in \arg \min_{1 \le i \le K} \|X_k - x_i^{(K)}\|$  $i^{(K)}$ ||; 6 Update  $x_i^{(K+1)} \leftarrow (1 - \gamma_i)x_i^{(K)} + \gamma_i X_k$  if  $i = i_{\text{win}}$ , otherwise  $x_i^{(K+1)} \leftarrow x_i^{(K)}$  $\binom{n}{i}$ ; 7 Update weights  $w_i \leftarrow (1 - \gamma_i) w_i + \gamma_i \mathbf{1}_{i=i_{\min}};$  $\mathbf{s} \mid k \leftarrow k + 1;$ 9 end **Result:** quantization  $\nu_K = \sum_{i=1}^K w_i \delta(x_i^*)$ 

*2. The moving average of the empirical quadratic distortion converges to the limiting distortion:*

$$
\frac{1}{t} \sum_{k=1}^{t} \min_{1 \le i \le K} \|X_k - x_i^{(k)}\|^2 \to \mathcal{G}(\mathbf{x}^*).
$$

**Lloyd I.** This consists of iteratively updating the centroids with the  $\mu$ -centroids, given by the  $\mu$ -average of the Voronoi cells. Clearly, if this algorithm converges, then the centroids are equal to the  $\mu$ -centroids and the grid is stationary. This is more commonly known as the k-means clustering algorithm, employed in common numerical software packages such as scikit-learn [\(Pedregosa](#page-22-8) [et al.,](#page-22-8) [2011\)](#page-22-8). Convergence of the Lloyd-I algorithm can be found in e.g. [\(Pages and Yu,](#page-22-11) [2016\)](#page-22-11).

Algorithm 2: Lloyd I  $(k$ -means)

**Data:** Probability distribution  $\mu$  with finite first moment, initial cluster centers  $x_1^{(0)}$  $x_1^{(0)},...,x_K^{(0)}$ K  $1 \; k \leftarrow 0;$ 2 while *not converged* do 3 Compute Voronoi cells  $C_i^{(k)} = \{ y \in \mathbb{R}^d \mid ||x_i^{(k)} - y|| = \min_j ||x_j^{(k)} - y|| \};$ 4 Replace cluster centers with  $\mu$ -centroids  $x_i^{(k+1)} \leftarrow (\int_{C_i^{(k)}} x \mu(dx))/\mu(C_i^{(k)})$  $\binom{\binom{\kappa}{j}}{i}$ ;  $\mathbf{5} \mid k \leftarrow k + 1;$ 6 end

<span id="page-9-0"></span>

# 2.3. Score-based diffusion.

To connect the quantization error on the latent space with the quantization error in the image space, we need to consider properties of the latent-to-image process. In particular, we focus on score-based diffusion models.

[Song et al.](#page-23-10) [\(2020\)](#page-23-10) interprets denoising diffusion models as a discretization of a particular noising SDE. Consider the SDE as follows, where  $W$  is a standard Wiener process:

<span id="page-9-2"></span>
$$
dx = f(x, t) dt + g(t) dW.
$$
 (16)

Then, where the density of x at time t is given by  $p_t$ , the reverse of the diffusion process is given by the reverse-time SDE,

<span id="page-10-0"></span>
$$
\mathrm{d}x = \left[f(x,t) - g(t)^2 \nabla_x \log p_t(x)\right] \mathrm{d}t + g(t) \mathrm{d}\bar{W},\tag{17}
$$

where  $\bar{W}$  is the standard Wiener process running in reverse time from time T to 0. For an increasing noising schedule  $\sigma(t) \in [0, +\infty)$  or noise-scale  $\beta(t) \in [0, 1)$ , the variance-exploding SDE (VESDE, or Brownian motion) is given by

$$
\mathrm{d}x = \sqrt{\frac{\mathrm{d}[\sigma^2(t)]}{\mathrm{d}t}} \,\mathrm{d}W\,,\tag{18}
$$

and the variance-preserving SDE (VPSDE, or Ornstein–Uhlenbeck process) is given by

$$
dx = -\frac{1}{2}\beta(t)x dt + \sqrt{\beta(t)} dW.
$$
 (19)

The VPSDE and VESDE are commonly used when training diffusion models due to their simple discretization. Indeed, they both arise as continuous limits of earlier proposed diffusion models. Specifically, VPSDE from denoising score matching with Langevin dynamics [\(Song and Ermon,](#page-23-11) [2019\)](#page-23-11), and VESDE from denoising diffusion probabilistic models [\(Sohl-Dickstein et al.,](#page-23-12) [2015;](#page-23-12) [Ho](#page-21-6) [et al.,](#page-21-6) [2020\)](#page-21-6).

We note that there is a relationship between score-matching and the Wasserstein proximal map using the language of mean field theory [\(Zhang et al.,](#page-24-4) [2024;](#page-24-4) [Zhang and Katsoulakis,](#page-24-5) [2023\)](#page-24-5). In particular, under integrability assumptions, the optimal score will perfectly recover the training data [\(Pidstrigach,](#page-22-12) [2022\)](#page-22-12). For analysis purposes, we instead consider the case where the score is given by a true underlying data distribution, from which training data is sampled from.

Suppose that the true data distribution on the image space is given by  $\mu \in \mathcal{P}(\mathbb{R}^d)$ , assumed to have bounded support. Then, by the Hörmander condition, the law of a random variable  $(X_t)_{t\geq0}$ evolving under either the VPSDE or the VESDE will admit a density  $p_t(x)$  for all  $t > 0$  with respect to the Lebesgue measure, that is smooth with respect to both x and t (Hörmander, [1967\)](#page-21-7). Using the following proposition, we have well-definedness of the backward SDE [\(Anderson,](#page-20-10) [1982;](#page-20-10) [Haussmann](#page-21-8) [and Pardoux,](#page-21-8) [1986\)](#page-21-8).

<span id="page-10-1"></span>Proposition 8 *For the forward SDE* [\(16\)](#page-9-2)*, assume that there exists some* K > 0 *such that*

- *1.*  $f(x,t)$ ,  $g(t)$  *are measurable, and*  $f$  *is uniformly Lipschitz:*  $||f(x,t) f(y,t)|| \le K||x-y||$ *for all*  $x, y \in \mathbb{R}^d$ ;
- 2.  $||f(x,t)|| + |g(t)| \leq K(1 + ||x||)$ ;
- 3. *The solution*  $X_t$  *of* [\(16\)](#page-9-2) *has a*  $C^1$  *density*  $p_t(x)$  *for all*  $t > 0$ *, and*

$$
\int_{t_0}^{T} \int_{\|x\| < R} \|p_t\|^2 + \|\nabla_x p_t(x)\|^2 < +\infty \quad \forall t_0 \in (0, T], \, R > 0;
$$

*4. The score*  $\nabla \log p_t(x)$  *is locally Lipschitz on*  $(0, T] \times \mathbb{R}^d$ .

*Then the reverse process*  $X_{T-t}$  *is a solution of the* [\(17\)](#page-10-0)*, and moreover, the solutions of* (17) *are unique in law.*

Now given that the backwards SDE is indeed a diffusion, the data processing inequality uses the Markov property and states that the divergence after diffusion is less than the divergence before diffusion [\(Liese and Vajda,](#page-21-9) [2006\)](#page-21-9). This is summarized in [Pidstrigach](#page-22-12) [\(2022,](#page-22-12) Thm. 1).

**Theorem 9** Denote the initial data distribution by  $\mu_0 = \mu$ , and let  $\mu_T$  be the distribution of a *random variable*  $X_t$  *satisfing the forward SDE* [\(16\)](#page-9-2) *on* [0, T]. Assume the above assumptions, and *let*  $Y_t$  *satisfy the backwards SDE* [\(17\)](#page-10-0) *on* [0, T] *with terminal condition*  $Y_T \sim \nu_T$ *. Denote by*  $\mu_t$  *and*  $\nu_t$  the marginal distributions at time  $t \in [0,T]$  of  $X_t$  and  $Y_t$ , and assume that  $\nu_T \ll \mu_T$ . Then:

- *1. The limit*  $Y_0 := \lim_{t \to 0^+} Y_t$  *exists a.s., with distribution*  $\nu_0 \ll \mu_0$ *.*
- *2. For any*  $f$ -divergence  $D_f$ ,

$$
D_f(\mu_0, \nu_0) \le D_f(\mu_T, \nu_T)
$$
 and  $D_f(\nu_0, \mu_0) \le D_f(\nu_T, \mu_T)$ . (20)

This theorem shows that for an f-divergence, such as total variation distance or Kullback–Leibler divergence, convergence of the marginals at time  $T$  implies convergence of the backwards-diffused marginals at time 0 (also at any  $t < T$ ). However, this requires absolute continuity of the initial marginal distribution  $\nu_T$  with respect to  $\mu_T$ , equivalently, w.r.t. Lebesgue measure. This rules out singular initializations of  $\nu_T$ , such as empirical measures.

To combat this, we need to work with appropriately chosen test functions. The goal in question: given Wasserstein-2 convergence of the marginals  $\nu_T^{(k)} \to \mu_T$ , to derive a bound on evaluations of the form  $\mathbb{E}_{\nu_s^{(k)}}[f] \stackrel{?}{\to} \mathbb{E}_{\mu_\delta}[f]$  for some fixed  $\delta \in (0,T)$  and  $f : \mathbb{R}^d \to \mathbb{R}$  satisfying some regularity conditions. We note that having  $\delta = 0$  may not be well defined because of non-smoothness of the score at time 0, and thus we restrict to  $\delta > 0$  as in [Zhang et al.](#page-24-4) [\(2024\)](#page-24-4). Such a bound directly links to training neural networks with surrogate data, by taking  $f$  to be the gradient of a loss function.

#### 2.3.1. STRONG LOCAL LIPSCHITZ CONTINUITY.

Strong local Lipschitz continuity refers to finding sufficient conditions on the drift and diffusion terms such that the following holds. Given a diffusion of the form  $(16)$ , define  $X_t^x$  to be the diffusion of a particle at initial point  $X_0^x = x \in \mathbb{R}^d$ , and let  $\Omega$  be the underlying filtered probability space of the Wiener processes. The desired property is that for any  $t, p > 0$ , to have a continuous function  $\varphi_{t,p}:\mathbb{R}^d\times\mathbb{R}^d\to[0,+\infty)$  such that for every  $x,y\in\mathbb{R}^d,$ 

<span id="page-11-0"></span>
$$
||X_t^x - X_t^y||_{L^p(\Omega; \mathbb{R}^d)} \le \varphi_{t, p}(x, y)||x - y||. \tag{21}
$$

This can be interpreted as Lipschitz continuity of sample paths with respect to the initial condition of the diffusion. Our goal is to "pushforward" the convergence of the Wasserstein distance from the latent space (of optimal quantizations) through the diffusion model (backwards SDE [\(17\)](#page-10-0)) into the image space/manifold: if  $\varphi_{t,p}$  is constant in x, y, then we can derive a uniform bound on expectations of regular test functions.

In the particular case of  $p = 2$ , *(weak) monotonicity* of the drift and diffusion terms gives a sufficient condition for local Lipschitz continuity [\(Cox et al.,](#page-20-11) [2024\)](#page-20-11).

Proposition 10 (Prévôt and Röckner [2007,](#page-22-13) Prop. 4.2.10) *Suppose we have a diffusion* 

$$
dx = f(x, t) dt + g(x, t) dW.
$$
\n(22)

*Under some integrability conditions similar to Theorem [8,](#page-10-1) further assume that there exists*  $c \in \mathbb{R}$ *such that for every*  $t \in [0, T]$ ,

$$
\langle x-y, f(x,t) - f(x,t) \rangle + \frac{1}{2} \|g(x,t) - g(y,t)\|_{\text{HS}}^2 \le c \|x - y\|^2,
$$

where  $\|\cdot\|_{\text{HS}}$  is the Hilbert-Schmidt norm<sup>[1](#page-12-1)</sup>. Then the diffusion satisfies [\(21\)](#page-11-0) with  $\varphi_{t,2}=e^{ct}$ .

A crucial part of this proof uses Gronwall's inequality when integrating the deviation between paths. For a slightly stronger bound, [Hudde et al.](#page-21-10) [\(2021\)](#page-21-10) uses the stochastic Gronwall's inequality. We present a simple version of the stronger result available in [Hudde et al.](#page-21-10) [\(2021\)](#page-21-10), extending the previous result to the case where the monotonicity constant can be controlled.

<span id="page-12-3"></span>Proposition 11 [\(Hudde et al.](#page-21-10) [2021,](#page-21-10) Lem. 3.6) *Consider the diffusion*

<span id="page-12-2"></span>
$$
dx = f(x, t) dt + g(x, t) dW.
$$
 (23)

Suppose that there exists a measurable  $\phi:[0,T]\to[0,\infty]$  satsfying  $\int_0^T\phi(t)\,\mathrm{d}t<+\infty$ , and that for  $all t \in [0, T]$  and  $x, y \in \mathbb{R}^d$ ,

$$
\langle x - y, f(x, t) - f(y, t) \rangle + \frac{1}{2} \| g(x, t) - g(y, t) \|_{\text{HS}}^2 \le \phi(t) \| x - y \|^2. \tag{24}
$$

*Then for processes*  $X_t^x$ ,  $X_t^y$  *starting at*  $x, y$  *respectively under* [\(23\)](#page-12-2)*, it holds for all*  $t \in (0,T]$  *that* 

$$
||X_t^x - X_t^y||_{L^1(\Omega; \mathbb{R}^d)} \le ||x - y|| \exp\left(\int_0^t \phi(s) \, ds\right).
$$
 (25)

<span id="page-12-0"></span>

### 3. Dataset Distillation as Optimal Quantization

Now equipped with the machinery of the previous section, we now present our main result Theorem [13,](#page-13-0) which is consistency of dataset distillation for a score-based diffusion prior in the image space. Later, we use this in Section [3.2](#page-15-0) to present a modification of the  $D<sup>4</sup>M$  method, based on changing the clustering from a Wasserstein barycenter objective to an optimal quantization objective.

From a connection with the mini-batch k-means algorithm and CLVQ, we then derive a simple weighting to the distilled latent points that is asymptotically consistent, i.e. converges to the target distribution as the number of points increases. We conclude this section with a full explanation of the 3-step distillation method to produce distilled data plus soft labels, plus modified training setting for training new neural networks on the distilled data.

We first require a lemma that controls diffusions for compact measures. In particular, the score is (weakly) monotonic.

**Lemma 12** Let  $\mu \in \mathcal{P}(\mathbb{R}^d)$  be a probability measure with compact support, say bounded by R. Let  $g_t(x)=\frac{1}{(2\pi t)^{-d/2}}\exp\bigl(-\|x\|^2/2t\bigr)$  be the density of the standard normal distribution in  $\mathbb{R}^d$ . Then if  $p_t$  is the density of  $\mu \ast g_t$ , it satisfies

<span id="page-12-4"></span>
$$
\langle x-y, \nabla \log p_t(x) - \nabla \log p_t(y) \rangle \le \left(\frac{R^2}{t^2} - \frac{1}{t}\right) \|x-y\|^2.
$$

<span id="page-12-1"></span><sup>1.</sup> For a linear operator  $A: U \to H$  between real separable Hilbert spaces, the HS norm is  $||A||_{\text{HS}}^2 = \sum_{u \in U} ||Au||_H^2$ , where  $U$  is an orthonormal basis of  $U$ .

**Proof** [Sketch.] From [Bardet et al.](#page-20-12) [\(2018\)](#page-20-12),  $p_t$  can be shown to be a perturbation of a Gaussian measure. Taking the Hessian of  $\log p_t$  gives the result, see [A](#page-24-6)ppendix A for more details. Note that R can be strengthened to the radius of the smallest ball containing supp  $\mu$  (not necessarily centered at 0).

We now state our main result, which transforms convergence of the terminal distribution into convergence of the backwards-diffused distributions at small time. The result is presented for two common SDEs for diffusion models, namely VESDE/Brownian motion and VPSDE/Ornstein– Uhlenbeck process. As an application, taking the terminal approximations to be optimal quantizers of increasing level, we get consistency of dataset distillation methods for gradient-based optimization.

<span id="page-13-0"></span>Theorem 13 *Consider the VESDE/Brownian motion*

$$
dx = dW \tag{26}
$$

*or the VPSDE/Ornstein–Uhlenbeck process*

$$
\mathrm{d}x = -\frac{1}{2}x\,\mathrm{d}t + \mathrm{d}W\,. \tag{27}
$$

Then, for any initial data distribution  $\mu \in \mathcal{P}_2(\mathbb{R}^d)$  with compact support bounded by  $R > 0$ , the *assumptions for Theorem [8](#page-10-1) hold and the backwards diffusion process is well posed.*

*Suppose further that there are two distributions*  $\mu_T, \nu_T$  *at time* T *that undergo the reverse diffusion process (with fixed initial reference measure*  $\mu$ ) *up to time*  $t = \delta \in (0, T)$  *to produce* distributions  $\mu_\delta, \nu_\delta.$  Then there exists a constant  $C=C(\delta,R,d)\in(0,+\infty)$  such that if  $f:\R^d\to\R$ *is an* L*-Lipschitz function, then the difference in expectation satisfies*

$$
\|\mathbb{E}_{\mu_{\delta}}[f] - \mathbb{E}_{\nu_{\delta}}[f]\| \leq CL\mathcal{W}_2(\mu_T, \nu_T). \tag{28}
$$

**Proof** The main idea is to push a Wasserstein-optimal coupling through the backwards SDE, then using Theorem [11](#page-12-3) and Theorem [5.](#page-7-0) First fix a  $\delta \in (0, T]$ , and let supp  $\mu$  be bounded by R. Let  $g_t = \frac{1}{(2\pi t)^{-d/2}} \exp(-\|x\|^2/2t)$  denote the distribution of the Gaussian  $\mathcal{N}(0, tI_d)$  in d dimensions. By the Hörmander condition, the densities of a random variable  $X_t$  with initial distribution  $X_0 \sim \mu$ undergoing the Brownian motion or Ornstein–Uhlenbeck process exist, and furthermore the forward and backward SDE processes are diffusions [\(Malliavin,](#page-22-14) [1978;](#page-22-14) [Hairer,](#page-21-11) [2011;](#page-21-11) [Pidstrigach,](#page-22-12) [2022\)](#page-22-12).

Step 1. (*Monotonicity of the drifts.*) For the Brownian motion,

$$
p_t(x) = (\mu(z) * g_t)(x).
$$

For the Ornstein-Uhlenbeck process, the solution from initial condition  $X_0 = x_0$  is

$$
x_t = x_0 e^{-t/2} + W_{1-e^{-t}}.
$$

The law of  $X_t$  is thus  $\mu(e^{t/2}x) * g_{1-\exp(-t)}$ , where  $\mu(e^{t/2}x)$  has support bounded by  $Re^{-t/2}$ . Moreover, they are  $\mathcal{C}^{\infty}$ , and thus uniformly Lipschitz. Alternative conditions under which this holds are given in [\(Kusuoka,](#page-21-12) [2017\)](#page-21-12).

Apply Lemma [12](#page-12-4) with  $\mu$  for the Brownian motion, and  $\mu(e^{t/2}x)$  for the Ornstein-Uhlenbeck process. The corresponding backward SDEs (in forward time) for the Brownian motion and Ornstein– Uhlenbeck process as given by the forward time versions of  $(17)$  are

$$
dx = \nabla \log p_{T-t}(x)dt + dW \quad \text{for Brownian motion;}
$$
$$
dx = \left[\frac{x}{2} + \nabla \log p_{T-t}(x)\right]dt + dW \quad \text{for the OU process,}
$$

where  $p_{T-t}(x)$  is the law of  $X_{T-t}$  for  $t \in [0, T)$ .

Step 2. (*Lipschitz w.r.t. initial condition after diffusion.*) For the backwards SDEs, since the score is Lipschitz and the diffusion term is constant, the backwards SDEs satisfy the monotonicity condition in Proposition [11.](#page-12-3) Hence, there exists a constant C such that for any  $Y_t^x$ ,  $Y_t^y$  evolving according to the backwards SDE,

$$
\mathbb{E}||Y_{T-\delta}^x - Y_{T-\delta}^y|| \le C||x - y||. \tag{29}
$$

From the monotonicity condition and Proposition [11,](#page-12-3) the constants can be chosen to be as follows, From the monotonicity condition and Prop<br>noting the Hilbert-Schmidt norm of  $I_d$  is  $\sqrt{d}$ :

$$
\log C_{\text{Brownian}} = \int_{\delta}^{T} \left[ \frac{R^2}{t^2} - \frac{1}{t} + \frac{d}{2} \right] dt, \quad \log C_{\text{OU}} = \int_{\delta}^{T} \left[ \frac{R^2 e^{-t}}{(1 - e^{-t})^2} - \frac{1}{1 - e^{-t}} + \frac{d + 1}{2} \right] dt.
$$
\n(30)

**Step 3.** (*Lift to function expectation*.) Now let  $Y_t$ ,  $\hat{Y}_t$  be two diffusions, initialized with distributions  $Y_0^x \sim \mu_T$ ,  $\hat{Y}_t^x \sim \nu_T$ . Let  $\gamma \in \Gamma(\mu_T, \nu_T)$  be any coupling. Define the "lifted coupling"  $\hat{\gamma}$  on the measurable space  $((\mathbb{R}^d \times \mathbb{R}^d) \times \Omega, \mathcal{B}(\mathbb{R}^d \times \mathbb{R}^d) \otimes \mathcal{F})$ , where  $(\Omega, \mathcal{F}, \mathbb{P})$  is the underlying (filtered) probability space of the diffusion, as the pushforward of the diffusion:

$$
\hat{\gamma} = (Y_0 \mapsto Y_{T-\delta}, \hat{Y}_0 \mapsto \hat{Y}_{T-\delta}, \iota_{\Omega})_{\sharp} (\gamma \otimes \mathbb{P})
$$
\n(31)

**Tale** 

Marginalizing over  $\mathbb P$ , this is a (probability) measure on  $\mathbb R^d\times\mathbb R^d$  since the backward SDE paths are continuous: the backward SDEs admit the following integral formulation, where  $h(r, Y_r)$  is the drift term of the backward SDE:

$$
Y_t = Y_0 + \int_0^t h(r, Y_r) dr + W_t.
$$

Moreover,  $\hat{\gamma}$  is a coupling between  $Y_{T-\delta} \sim \mu_{\delta}$  and  $\hat{Y}_{T-\delta} \sim \nu_{\delta}$ . We compute:

$$
\|\mathbb{E}_{\mu_{\delta}}[f] - \mathbb{E}_{\nu_{\delta}}[f]\| = \|\iint \mathbb{E}[f(x) - f(y)] d\hat{\gamma}(x, y) \|
$$
  
\n
$$
\leq \iint \mathbb{E}[\|f(Y_{T-\delta}^x) - f(\hat{Y}_{T-\delta}^y)\|] d\gamma(x, y)
$$
  
\n
$$
\leq L \iint \|Y_{T-\delta}^x - \hat{Y}_{T-\delta}^y\| d\gamma(x, y)
$$
  
\n
$$
\leq CL \iint \|Y_0^x - \hat{Y}_0^y\| d\gamma(x, y)
$$
  
\n
$$
= CL \iint \|x - y\| d\gamma(x, y) \leq CL \left(\iint \|x - y\|^2 d\gamma(x, y)\right)^{1/2}.
$$

The desired inequality follows by taking infimums over admissible couplings  $\gamma \in \Gamma(\mu_T, \nu_T)$ .

**Remark 14** *This theorem does not hold if*  $\delta = 0$ *, theoretically or empirically. This is due to blowup of*  $∇$   $log p_t$  *as*  $t$   $→$  0 *[\(Pidstrigach,](#page-22-12) [2022;](#page-22-12) [Yang et al.,](#page-23-13) [2023\)](#page-23-13). Intuitively, if the data distribution is concentrated on a smooth low-dimensional manifold, the score of a Gaussian mollification of the density (as given by Brownian motion) will explode along the normal directions of the manifold.*

In dataset distillation terms, f will typically be replaced by the gradient of a loss function. The above result states that for a sequence of distributions that converge to the noisy distribution  $\mu_T$ , the gradients of a loss function evaluated at the reverse-diffused distributions will converge to something that is close to the gradient of the population risk. In other words, *dataset distillation from quantizing the latent (noise) distribution produces consistent gradient estimates when training neural networks*. Theorem [13](#page-13-0) combined with Theorem [4](#page-7-1) gives convergence rates as the number of quantization points increases.

**Corollary 15** Suppose  $\mu \in \mathcal{P}(\mathbb{R}^d)$  has compact support and is diffused through either the Brownian motion or Ornstein–Uhlenbeck process up to time  $T$  to produce marginal  $\mu_T$ *. Let*  $\nu_T^{(K)}$  $T^{(R)}$  be optimal *quantizers of*  $\mu_T$  *at level K for*  $K \in \mathbb{N}$ *. For fixed*  $\delta \in (0,T)$ *, let*  $\nu_{\delta}^{(K)}$  $\delta_{\delta}^{(K)}$  denote the corresponding *backwards diffusion at time*  $T - \delta$ . Then, for any L-Lipschitz function f and as  $K \to \infty$ ,

$$
\|\mathbb{E}_{\mu_{\delta}}[f] - \mathbb{E}_{\nu_{\delta}^{(K)}}[f]\| = L\mathcal{O}(K^{-1/d}).\tag{32}
$$

We note that this can be further be combined with the convergence of optimal quantization rates of empirical measures Theorem [3,](#page-6-1) but leave the details to future work.

#### 3.1. Preliminary: $D^4M$ method

Algorithm [3](#page-16-0) details the latent-space prototyping and synthesis process of the  $D<sup>4</sup>M$  method. It is a two-step process: using  $k$ -means on the latent encodings of the training data to produce some latent code centroids, then using these centroids in a diffusion model to synthesize distilled images.

**Main observation:** Note that the  $k$ -means method does not take into account the companion weights of the quantization. When these weights are chosen to be uniform, the corresponding Wasserstein minimization problem is the *Wasserstein barycenter* problem (though the algorithm is different). This has a higher Wasserstein distance compared to the optimal quantization for the same number of points. Therefore, we *propose to add the weights to the corresponding quantized points*, done using the CLVQ (or mini-batch  $k$ -means) algorithm.

<span id="page-15-0"></span>

#### 3.2. Proposed method: Dataset Distillation by Optimal Quantization

In addition to the synthesis of the distilled dataset, [Su et al.](#page-23-6)  $(2024)$  argue that it is also imperative that the training regime on the distilled dataset also be modified. We now summarize all the steps from distillation to training and give some informal insights into each step. The proposed algorithm, dataset distillation by optimal quantization (DDOQ), is given in Algorithm [4.](#page-17-0)

Suppose we are given a (text-conditional) encoder-decoder and a diffusion-based model on the latent space, as in the setting of a latent diffusion model [\(Rombach et al.,](#page-22-6)  $2022$ ). Let K be the target number of images per class.

1. Latent space clustering. We use the encoder of the LDM to map samples from the image space to the latent space, giving empirical samples from the SDE marginal  $\mu_T$ . We then use

```
Algorithm 3: D^4M(Su et al.,2024)
   Data: Training data and labels (\mathcal{T}, \mathcal{L}), pre-trained encoder-decoder pair (\mathcal{E}, \mathcal{D}), text encoder \tau,
           latent diffusion model \mathcal{U}_t, target IPC count K1 Initialize latent variables Z = \mathcal{E}(\mathcal{T});2 for label L \in \mathcal{L} do
 3 | Initialize latent centroids z_L^k, k = 1, ..., K;
 4 | Initialize update counts v_L^k = 1, k = 1, ..., K;/* Compute prototypes with k-means * /
5 for minibatch z \in Z|_L do
 6 for z \in \mathbf{z} do
 7
                 \hat{k} \leftarrow \argmin_k ||z_L^k - z||;/* Update learning rate \star/\begin{array}{|c|c|c|}\n\text{s} & \begin{array}{|c|c|c|}\n\text{s} & v_L^{\hat{k}} & \leftarrow v_L^{\hat{k}} + 1;\n\end{array}\n\end{array}/* Update centroid */\mathbf{9} \quad \Big\vert \quad \Big\vert \quad z_L^{\hat k} \leftarrow (1-1/v_L^{\hat k}) z_L^{\hat k} + (1/v_L^{\hat k}) z;10 end
11 end
12 Compute class embedding y = \tau(L);
13 Compute class distilled images S_L = \{ \mathcal{D} \circ \mathcal{U}_t(z_L^k, y) \mid k = 1, ..., K \};14 end
   Result: distilled images S = \bigcup_{L \in \mathcal{L}} S_L
```

the CLVQ (mini-batch  $k$ -means) algorithm on these samples to compute the  $K$  centroids and correpsonding weights, as in Theorem [7.](#page-8-2) This gives an empirical distribution  $\nu_{T}^{(K)}$  $T^{(R)}$  that approximates  $\mu_T$ , which is ideally an optimal quantization (of the training data in the latent space). (New:) This moreover produces weights w for each distilled latent point, iteratively updated during the k-means update step with minimal overhead.

- 2. Image synthesis. Given the centroids, which are points in the latent space, the generative part of the LDM is employed to reconstruct images. This comprises the distilled dataset. The weights of the latent points are assigned directly to the weights of the corresponding generated image.
- 3. Soft label synthesis. Given the distilled images, *soft labels* are computed using a pre-trained model, such as a ResNet. This is evaluated on seeded batches with possible image augmentation. This is referred to as "training-time matching" by [Su et al.](#page-23-6) [\(2024\)](#page-23-6). They demonstrate better performance when training using soft labels, rather than cross entropy on the corresponding classes of the distilled images. The use of soft labels is now standard in the literature [\(Sun](#page-23-8) [et al.,](#page-23-8) [2024;](#page-23-8) [Yin et al.,](#page-24-3) [2023\)](#page-24-3).
- 4. **Training.** Training a network  $\phi_{\theta}$  from scratch requires: distilled images x, corresponding soft labels  $\Psi(x)$ , and the weights w to each image. The loss for a single image is given by  $wD_{\text{KL}}(\phi_{\theta}(x)\|\Psi(x))$ , averaged over the seeded batches used previously.

<span id="page-17-0"></span>

| Algorithm 4: Dataset Distillation by Optimal Quantization (DDOQ)                                                                                                                  |             |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|
| <b>Data:</b> Training data and labels $(\	ext{T}, \	ext{L})$ , pre-trained encoder-decoder pair $(\	ext{E}, \	ext{D})$ , text encoder $\	au$ ,                                    |             |
| latent diffusion model $\	ext{U}_t$ , target IPC count K, pre-trained classifier $\	ext{Psi}$                                                                                     |             |
| $/*$ Step 1: latent k-means                                                                                                                                                       | $\	ext{*/}$ |
| 1 Initialize latent variables $Z = \	ext{E}(\	ext{T});$                                                                                                                           |             |
| 2 Compute and save k-means cluster centers $z_k^{(L)}$ and cluster counts $v_k^{(L)}$ , $k = 1, ..., K$ , $L \	ext{in} \	ext{L}$ ;                                                |             |
| 3 Compute weights $w_k^{(L)} \	ext{<-} K v_k^{(L)} / \	ext{sum}_j v_j^{(L)}$ ;                                                                                                    |             |
| $/*$ Step 2: Synthesize images                                                                                                                                                    | $\	ext{*/}$ |
| 4 Compute class embeddings $y = \	au(L)$ , $L \	ext{in} \	ext{L}$ ;                                                                                                               |             |
| 5 Compute and save class distilled images $S_L = \{x_k^{(L)} = \	ext{D} \	ext{o} \	ext{U}_t(z_k^{(L)}, y) \mid k = 1, ..., K\};$                                                  |             |
| <b>Result:</b> Distilled images $S = \	ext{bigcup}_{L \	ext{in} \	ext{L}} S_L$                                                                                                    |             |
| /* To train a new network $\	ext{phi}_{\	ext{theta}}$ on the distilled data:                                                                                                      | $\	ext{*/}$ |
| /* Step 3: Pre-compute soft labels (FKD)                                                                                                                                          | $\	ext{*/}$ |
| 6 Compute labels $\	ext{Psi}(x_b) \	ext{subset} \	ext{R}^{ \	ext{L} }$ for augmented image batches $\	ext{B} = \{(x_b, v_b)\	ext{_b}\	ext{}}$ up to target                        |             |
| number of batches $B$ ;                                                                                                                                                           |             |
| /* Step 4: Train new model (Validation)                                                                                                                                           | $\	ext{*/}$ |
| 7 Train network $\	ext{phi}_	ext{theta}$ on batch B by minimizing $\	ext{sum}_{(x,w)\	ext{in}\	ext{B}} w D_{\	ext{KL}}(\	ext{phi}_	ext{theta}(x)    \	ext{Psi}(x))$ for B batches |             |

Variance reduction (heuristic). We note that the variance of the number of cluster assignments can vary significantly, sometimes up to two orders of magnitude, such as in Figure [1.](#page-18-0) After normalizing the cluster counts in Step 3 of Algorithm [4](#page-17-0) to give weights  $w \in (0, 1)$ , most weights are very small, and do not contribute much to the neural network training. To reduce this effect, we use the weights

$$
w_k^{(L)} = \sqrt{K} \sqrt{v_k^{(L)} / \sum_{j=1}^K v_j^{(L)}}.
$$
\n(33)

We note that other choices of variance reduction can also be used, and leave a more thorough search to future work.

<span id="page-17-1"></span>

### 4. Experiments

To validate the proposed DDOQ algorithm, we directly compare with the previous state of the art methods  $D^4M$  [\(Su et al.,](#page-23-6) [2024\)](#page-23-8) and RDED [\(Sun et al.,](#page-23-8) [2](#page-17-2)024) on the publically available ImageNet-1K<sup>2</sup> dataset. The latent diffusion model chosen for latent generation and image synthesis is the publically available pre-trained Stable Diffusion V1.5 model. We also compare with the SRe<sup>2</sup>L method [\(Yin](#page-24-3) [et al.,](#page-24-3) [2023\)](#page-24-3) as reported in [Su et al.](#page-23-6) [\(2024\)](#page-23-6), which uses internal network batch-normalization statistics rather than encoded latent spaces. For low IPC, we also report the TESLA method, which is a SOTA bi-level method based on MTT utilizing gradient checkpointing to reduce memory footprint [\(Cui](#page-20-13) [et al.,](#page-20-13) [2023\)](#page-20-13). We additionally report results for RDED [\(Sun et al.,](#page-23-8) [2024\)](#page-23-8) and CDA [\(Yin and Shen,](#page-23-5) [2024\)](#page-23-5) as given. RDED achieves SOTA results for low IPC using its aggregated images and special training schedule, while CDA improves upon  $SRe<sup>2</sup>L$  using time-varying augmentation.

<span id="page-17-2"></span><sup>2.</sup> Downloadable from <https://www.image-net.org/>

<span id="page-18-0"></span>Image /page/18/Figure/1 description: A collage of Jeep Wranglers, with a line graph below. The graph has a y-axis labeled with powers of 10, from 10^-2 to 10^-1. The line graph starts at 10^-2, rises to approximately 3x10^-2, drops to approximately 7x10^-3, rises to approximately 4x10^-2, drops to 10^-2, rises to approximately 5x10^-2, and then drops to approximately 10^-2.

Figure 1: Example distilled images of the "jeep" class in ImageNet-1K along with their  $k$ -means weights. There is little to no features that can be used to differentiate the low and high weighted images, mainly due to the high fidelity of the diffusion model. However, the weights are indicative of the distribution of the training data in the latent space of the diffusion model.

For consistency and a more direct comparison with previous methods, we use the pre-trained PyTorch ResNet-18 model<sup>[3](#page-18-1)</sup> to compute the soft labels, using the same protocol as [Su et al.](#page-23-6) [\(2024\)](#page-23-6). After computing the soft labels using the pre-trained ResNet-18 model, we train new ResNet-18, ResNet-50 and ResNet-101 models to match the soft labels. The data augmentation is also identical, with the only difference being the addition of the weights to the training objective and minimal optimizer hyperparameter tuning for the new objective.

We provide a direct comparison of the distilled data performance in Table [1](#page-19-1) for the IPCs  $K \in \{10, 50, 100, 200\}$ . Our figures are averaged over five models trained on the same distilled data. We observe that our performance is uniformly better than  $D<sup>4</sup>M$ , with the most significant increase in the low IPC setting. This is reasonable, as for a low number of quantization points, the gap in Wasserstein distance of the Wasserstein barycenter and the optimal quantizer to the data distribution may be large. As indicated in Theorem [13,](#page-13-0) a lower Wasserstein distance means more faithful gradient computations on the synthetic data.

We observe that while RDED is very powerful in the low IPC setting due to the image aggregation in the distilled images, which effectively gives the information of 4 (down-sampled) images in one training sample. However, the gap quickly reduces for IPC 50, getting outperformed by the clusteringbased  $D<sup>4</sup>M$  and proposed DDOQ methods with more powerful models like ResNet-101. Results for IPC 100 and 200 are not available online for RDED and we omit them due to computational restriction.

To illustrate the weights, Figure [1](#page-18-0) plots ten example images from the "jeep" class when distilled using  $K = 10$  IPC. We observe that there is a very large variance in the weights  $v_k^{(L)}$  $_{k}^{\left( L\right)}/\sum_{j=1}^{K}v_{j}^{\left( L\right) }$  $j^{(L)},$ indicating the presence of strong clustering in the latent space. Nonetheless, there is no qualitative evidence that the weights indicate "better or worse" training examples. Indeed, among other classes, there are synthetic images that do not lie in the appropriate class, but are still weighted highly.

### 5. Conclusion

This work proposes DDOQ, a dataset distillation method based on optimal quantization. Inspired by optimal quantization and Wasserstein theory, we theoretically demonstrate consistency of the distilled datasets in Theorem [13](#page-13-0) when using standard diffusion-based generative models to generate

<span id="page-18-1"></span><sup>3.</sup> Publically available from <https://pytorch.org/vision/main/models/resnet.html>

<span id="page-19-1"></span>

| <b>IPC</b>          | Method | ResNet-18       | ResNet-50                | ResNet-101               |
|---------------------|--------|-----------------|--------------------------|--------------------------|
| <b>Full Dataset</b> |        | 69.8            | 80.9                     | 81.9                     |
|                     | TESLA  | 7.7             | -                        | -                        |
|                     | SRe2L  | 21.3            | 28.4                     | 30.9                     |
| 10                  | RDED   | $42.0 \pm 0.1$  | -                        | $48.3 \pm 1.0$           |
|                     | D4M    | 27.9            | 33.5                     | 34.2                     |
|                     | DDOQ   | $33.1 \pm 0.60$ | $34.4 \pm 0.99$          | $36.7 \pm 0.80$          |
|                     | SRe2L  | 46.8            | 55.6                     | 60.8                     |
|                     | CDA    | 53.5            | 61.3                     | 61.6                     |
| 50                  | RDED   | $56.5 \pm 0.1$  | -                        | $61.2 \pm 0.4$           |
|                     | D4M    | 55.2            | 62.4                     | 63.4                     |
|                     | DDOQ   | $56.2 \pm 0.07$ | $62.5 \pm 0.24$          | $63.6 \pm 0.13$          |
|                     | SRe2L  | 52.8            | 61.0                     | 65.8                     |
| 100                 | CDA    | 58.0            | 65.1                     | 65.9                     |
|                     | D4M    | 59.3            | 65.4                     | 66.5                     |
|                     | DDOQ   | $60.1 \pm 0.15$ | $65.9 \pm 0.15$          | $\textbf{66.7} \pm 0.06$ |
|                     | SRe2L  | 57.0            | 64.6                     | 65.9                     |
| 200                 | CDA    | 63.3            | 67.6                     | 68.4                     |
|                     | D4M    | 62.6            | 67.8                     | 68.1                     |
|                     | DDOQ   | $63.4 \pm 0.08$ | $\textbf{68.0} \pm 0.05$ | $68.6 \pm 0.08$          |

Table 1: Comparison of top-1 classification performance on the ImageNet-1K dataset for baselines versus our method (DDOQ) at various IPCs. We observe that DDOQ outperforms the previous SOTA method  $D<sup>4</sup>M$ , due to the addition of weights to the synthetic data. Full dataset refers to performance of the PyTorch pre-trained models. Note the maximum performance for all methods should be 69.8 as the soft labels are computed using a pre-trained ResNet-18 model. Figures for TESLA, SRe<sup>2</sup>L and  $D<sup>4</sup>M$  are taken directly from [Su et al.](#page-23-6) [\(2024\)](#page-23-8), RDED from [Sun et al.](#page-23-8) (2024), and CDA from [Yin](#page-23-5) [and Shen](#page-23-5) [\(2024\)](#page-23-5).

the synthetic data. We empirically demonstrate performance higher than previous state-of-the-art methods on a large-scale ImageNet-1K dataset.

We have presented theoretical justification for one framework of dataset distillation. Future work could include sharper bounds in Theorem [13](#page-13-0) that exploit the sub-Gaussianity of the diffused distributions, alternate diffusion processes that provide similar bounds such as in [Kusuoka](#page-21-12) [\(2017\)](#page-21-12), or investingating alternative choices of weight variance reduction. Another interesting direction could be relating the weightings of the synthetic data to the hardness of learning the data, such as in [\(Joshi](#page-21-13) [and Mirzasoleiman,](#page-21-13) [2023\)](#page-21-13).

### References

<span id="page-19-0"></span>Stanley C Ahalt, Ashok K Krishnamurthy, Prakoon Chen, and Douglas E Melton. Competitive learning algorithms for vector quantization. *Neural networks*, 3(3):277–290, 1990.

- <span id="page-20-10"></span>Brian DO Anderson. Reverse-time diffusion equation models. *Stochastic Processes and their Applications*, 12(3):313–326, 1982.
- <span id="page-20-9"></span>Vlad Bally and Gilles Pages. A quantization algorithm for solving multidimensional discrete-time ` optimal stopping problems. *Bernoulli*, 9(6):1003–1049, 2003.
- <span id="page-20-12"></span>Jean-Baptiste Bardet, Nathaël Gozlan, Florent Malrieu, and Pierre-André Zitt. Functional inequalities for Gaussian convolutions of compactly supported measures: Explicit bounds and dimension dependence. *Bernoulli*, 24(1):333–353, 2018.
- <span id="page-20-3"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-20-4"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3739–3748, 2023.
- <span id="page-20-6"></span>Kwan Ho Ryan Chan, Yaodong Yu, Chong You, Haozhi Qi, John Wright, and Yi Ma. Redunet: A white-box deep network from the principle of maximizing rate reduction. *Journal of machine learning research*, 23(114):1–103, 2022.
- <span id="page-20-11"></span>Sonja Cox, Martin Hutzenthaler, and Arnulf Jentzen. *Local Lipschitz continuity in the initial value and strong completeness for nonlinear stochastic differential equations*, volume 296. American Mathematical Society, 2024.
- <span id="page-20-13"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023.
- <span id="page-20-7"></span>Marco Cuturi and Arnaud Doucet. Fast computation of wasserstein barycenters. In *International conference on machine learning*, pages 685–693. PMLR, 2014.
- <span id="page-20-5"></span>Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. IEEE, 2009.
- <span id="page-20-2"></span>Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. *Advances in Neural Information Processing Systems*, 35:34391– 34404, 2022.
- <span id="page-20-0"></span>Dan Feldman. Core-sets: Updated survey. *Sampling techniques for supervised or unsupervised tasks*, pages 23–44, 2020.
- <span id="page-20-1"></span>Jianping Gou, Baosheng Yu, Stephen J Maybank, and Dacheng Tao. Knowledge distillation: A survey. *International Journal of Computer Vision*, 129(6):1789–1819, 2021.
- <span id="page-20-8"></span>Siegfried Graf and Harald Luschgy. *Foundations of quantization for probability distributions*. Springer Science & Business Media, 2000.

- <span id="page-21-1"></span>Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 15793–15803, 2024.
- <span id="page-21-11"></span>Martin Hairer. On malliavin's proof of hörmander's theorem. Bulletin des sciences mathematiques, 135(6-7):650–666, 2011.
- <span id="page-21-8"></span>Ulrich G Haussmann and Etienne Pardoux. Time reversal of diffusions. *The Annals of Probability*, pages 1188–1205, 1986.
- <span id="page-21-6"></span>Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising diffusion probabilistic models. *Advances in neural information processing systems*, 33:6840–6851, 2020.
- <span id="page-21-7"></span>Lars Hörmander. Hypoelliptic second order differential equations. Acta Mathematica, 119(1): 147–171, 1967.
- <span id="page-21-10"></span>Anselm Hudde, Martin Hutzenthaler, and Sara Mazzonetto. A stochastic gronwall inequality and applications to moments, strong completeness, strong local lipschitz continuity, and perturbations. In *Annales de l'Institut Henri Poincaré-Probabilités et Statistiques*, volume 57, pages 603–626, 2021.
- <span id="page-21-13"></span>Siddharth Joshi and Baharan Mirzasoleiman. Data-efficient contrastive self-supervised learning: Most beneficial examples for supervised learning contribute the least. In *International conference on machine learning*, pages 15356–15370. PMLR, 2023.
- <span id="page-21-4"></span>Vyacheslav Kungurtsev, Yuanfang Peng, Jianyang Gu, Saeed Vahidian, Anthony Quinn, Fadwa Idlahcen, and Yiran Chen. Dataset distillation from first principles: Integrating core information extraction and purposeful learning. *arXiv preprint arXiv:2409.01410*, 2024.
- <span id="page-21-12"></span>Seiichiro Kusuoka. Continuity and gaussian two-sided bounds of the density functions of the solutions to path-dependent stochastic differential equations via perturbation. *Stochastic Processes and their Applications*, 127(2):359–384, 2017.
- <span id="page-21-9"></span>Friedrich Liese and Igor Vajda. On divergences and informations in statistics and information theory. *IEEE Transactions on Information Theory*, 52(10):4394–4412, 2006.
- <span id="page-21-3"></span>Haoyang Liu, Yijiang Li, Tiancheng Xing, Vibhu Dalal, Luwei Li, Jingrui He, and Haohan Wang. Dataset distillation via the wasserstein metric. *arXiv preprint arXiv:2311.18531*, 2023a.
- <span id="page-21-2"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *Advances in neural information processing systems*, 35:1100–1113, 2022.
- <span id="page-21-0"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 17314–17324, 2023b.
- <span id="page-21-5"></span>Yating Liu and Gilles Pages. Convergence rate of optimal quantization and application to the ` clustering performance of the empirical measure. *Journal of Machine Learning Research*, 21(86): 1–36, 2020.

- <span id="page-22-4"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *Advances in Neural Information Processing Systems*, 35:13877– 13891, 2022.
- <span id="page-22-14"></span>Paul Malliavin. Stochastic calculus of variation and hypoelliptic operators. In *Proc. Intern. Symp. SDE Kyoto 1976*, pages 195–263. Kinokuniya, 1978.
- <span id="page-22-0"></span>Baharan Mirzasoleiman, Jeff Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In *International Conference on Machine Learning*, pages 6950–6960. PMLR, 2020.
- <span id="page-22-7"></span>Brian B Moser, Federico Raue, Sebastian Palacio, Stanislav Frolov, and Andreas Dengel. Latent dataset distillation with diffusion models. *arXiv preprint arXiv:2403.03881*, 2024.
- <span id="page-22-5"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021.
- <span id="page-22-9"></span>Gilles Pagès. Introduction to vector quantization and its applications for numerics. *ESAIM: proceedings and surveys*, 48:29–79, 2015.
- <span id="page-22-11"></span>Gilles Pages and Jun Yu. Pointwise convergence of the lloyd i algorithm in higher dimension. *SIAM Journal on Control and Optimization*, 54(5):2354–2382, 2016.
- <span id="page-22-2"></span>Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. *Advances in neural information processing systems*, 34: 20596–20607, 2021.
- <span id="page-22-8"></span>F. Pedregosa, G. Varoquaux, A. Gramfort, V. Michel, B. Thirion, O. Grisel, M. Blondel, P. Prettenhofer, R. Weiss, V. Dubourg, J. Vanderplas, A. Passos, D. Cournapeau, M. Brucher, M. Perrot, and E. Duchesnay. Scikit-learn: Machine learning in Python. *Journal of Machine Learning Research*, 12:2825–2830, 2011.
- <span id="page-22-12"></span>Jakiw Pidstrigach. Score-based generative models detect manifolds. *Advances in Neural Information Processing Systems*, 35:35852–35865, 2022.
- <span id="page-22-1"></span>Antonio Polino, Razvan Pascanu, and Dan Alistarh. Model compression via distillation and quantization. *arXiv preprint arXiv:1802.05668*, 2018.
- <span id="page-22-10"></span>David Pollard. Quantization and the method of k-means. *IEEE Transactions on Information theory*, 28(2):199–205, 1982.
- <span id="page-22-13"></span>Claudia Prévôt and Michael Röckner. A *concise course on stochastic partial differential equations*, volume 1905. Springer, 2007.
- <span id="page-22-6"></span>Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Bjorn Ommer. High- ¨ resolution image synthesis with latent diffusion models. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 10684–10695, 2022.
- <span id="page-22-3"></span>Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *Transactions on Machine Learning Research*, 2023.

- <span id="page-23-9"></span>Filippo Santambrogio. Optimal transport for applied mathematicians. *Birkäuser, NY*, 55(58-63):94, 2015.
- <span id="page-23-7"></span>David Sculley. Web-scale k-means clustering. In *Proceedings of the 19th international conference on World wide web*, pages 1177–1178, 2010.
- <span id="page-23-4"></span>Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In *European conference on computer vision*, pages 673–690. Springer, 2022.
- <span id="page-23-12"></span>Jascha Sohl-Dickstein, Eric Weiss, Niru Maheswaranathan, and Surya Ganguli. Deep unsupervised learning using nonequilibrium thermodynamics. In *International conference on machine learning*, pages 2256–2265. PMLR, 2015.
- <span id="page-23-11"></span>Yang Song and Stefano Ermon. Generative modeling by estimating gradients of the data distribution. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-23-10"></span>Yang Song, Jascha Sohl-Dickstein, Diederik P Kingma, Abhishek Kumar, Stefano Ermon, and Ben Poole. Score-based generative modeling through stochastic differential equations. *arXiv preprint arXiv:2011.13456*, 2020.
- <span id="page-23-6"></span>Duo Su, Junjie Hou, Weizhi Gao, Yingjie Tian, and Bowen Tang. Dˆ 4: Dataset distillation via disentangled diffusion model. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 5809–5818, 2024.
- <span id="page-23-8"></span>Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 9390–9399, 2024.
- <span id="page-23-1"></span>Saeed Vahidian, Mingyu Wang, Jianyang Gu, Vyacheslav Kungurtsev, Wei Jiang, and Yiran Chen. Group distributionally robust dataset distillation with risk minimization. *arXiv preprint arXiv:2402.04676*, 2024.
- <span id="page-23-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-23-2"></span>Weihao Xia, Yulun Zhang, Yujiu Yang, Jing-Hao Xue, Bolei Zhou, and Ming-Hsuan Yang. GAN inversion: A survey. *IEEE transactions on pattern analysis and machine intelligence*, 45(3): 3121–3138, 2022.
- <span id="page-23-13"></span>Zhantao Yang, Ruili Feng, Han Zhang, Yujun Shen, Kai Zhu, Lianghua Huang, Yifei Zhang, Yu Liu, Deli Zhao, Jingren Zhou, et al. Lipschitz singularities in diffusion models. In *The Twelfth International Conference on Learning Representations*, 2023.
- <span id="page-23-3"></span>Hongxu Yin, Pavlo Molchanov, Jose M Alvarez, Zhizhong Li, Arun Mallya, Derek Hoiem, Niraj K Jha, and Jan Kautz. Dreaming to distill: Data-free knowledge transfer via deepinversion. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 8715–8724, 2020.
- <span id="page-23-5"></span>Zeyuan Yin and Zhiqiang Shen. Dataset distillation via curriculum data synthesis in large data era. *Transactions on Machine Learning Research*, 2024.

- <span id="page-24-3"></span>Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *Thirty-seventh Conference on Neural Information Processing Systems*, 2023.
- <span id="page-24-0"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023.
- <span id="page-24-5"></span>Benjamin J Zhang and Markos A Katsoulakis. A mean-field games laboratory for generative modeling. *arXiv preprint arXiv:2304.13534*, 2023.
- <span id="page-24-4"></span>Benjamin J Zhang, Siting Liu, Wuchen Li, Markos A Katsoulakis, and Stanley J Osher. Wasserstein proximal operators describe score-based generative models and resolve memorization. *arXiv preprint arXiv:2402.06162*, 2024.
- <span id="page-24-1"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023.
- <span id="page-24-2"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022.

<span id="page-24-6"></span>

#### Appendix A. Proof of Lemma [12](#page-12-4)

**Lemma** Let  $\mu \in \mathcal{P}(\mathbb{R}^d)$  be a probability measure with compact support, say bounded by R. Let  $g_t(x)=\frac{1}{(2\pi t)^{-d/2}}\exp\bigl(-\|x\|^2/2t\bigr)$  be the density of the standard normal distribution in  $\mathbb{R}^d$ . Then if  $p_t$  is the density of  $\mu \ast g_t$ , it satisfies

$$
\langle x-y, \nabla \log p_t(x) - \nabla \log p_t(y) \rangle \le \left(\frac{R^2}{t^2} - \frac{1}{t}\right) \|x-y\|^2.
$$

**Proof** From [\(Bardet et al.,](#page-20-12) [2018,](#page-20-12) Sec. 2.1), the density  $p_t$  can be written as

$$
p_t(x) = \frac{1}{(2\pi t)^{d/2}} \exp\left(-\left(\frac{\|x\|^2}{2t} + W_t(x)\right)\right),\,
$$

where

$$
W_t(x) = -\log \int_{\mathbb{R}^d} \exp\left(\frac{\langle x, z \rangle}{t}\right) \nu(\mathrm{d}z) - \log C_{\nu},
$$

with  $C_{\nu}(x) = \int_{\mathbb{R}^d} \exp(-\|x\|^2/2t) \mu(\mathrm{d}x)$  and  $\nu(\mathrm{d}x) = C_{\nu}^{-1} \exp(-\|x\|^2/2t) \mu(\mathrm{d}x)$ . Moreover,

$$
0 \le -\nabla^2 W_t \le \frac{R^2}{t^2} I_d. \tag{34}
$$

 $\blacksquare$ 

Therefore,

$$
\log p_t(x) = -\frac{d}{2}\log(2\pi t) - \frac{\|x\|^2}{2t} - W_t(x)
$$

has Hessian satisfying

$$
\nabla^2 \log p_t(x) \le \left(\frac{R^2}{t^2} - \frac{1}{t}\right) I_d.
$$

Therefore,  $\nabla \log p_t$  satisfies the desired monotonicity condition.

## Appendix B. Approximate Timings

All times are done on Nvidia A6000 GPUs with 48GB of VRAM. We note that synthesis time as reported in [Su et al.](#page-23-6) [\(2024\)](#page-23-6); [Sun et al.](#page-23-8) [\(2024\)](#page-23-8) do not include the time required to generate the latent variables, and thus are not sufficiently representative of the end-to-end time required to distill the dataset.

Table 2: Time required for each step of dataset distillation on ImageNet-1K. Synthesis requires application of the Stable Diffusion V1.5 model to each distilled latent variable, and soft label requires application of the pre-trained ResNet-18 model to each distilled image. Memory usage is constant between IPCs due to equal batch size.

| Step                  | Time (IPC $10$ ) | Time (IPC $100$ ) |
|-----------------------|------------------|-------------------|
| 1 (Latent clustering) | 8 hours          | 8 hours           |
| 2 (Synthesis)         | 2 hours          | 1 day             |
| 3 (Soft label)        | 1 hour           | 16 hours          |
| 4 (Training ResNet18) | 2.5 hours        | 9 hours           |

## Appendix C. Experiment Hyperparameters

We detail the parameters when training the student networks from the distilled data. They are mostly similar to [Su et al.](#page-23-6) [\(2024\)](#page-23-6).

Table 3: Hyperparameter setting for ImageNet-1K experiments.

<span id="page-25-0"></span>

| Setting                | Value                                |
|------------------------|--------------------------------------|
| Network                | ResNet                               |
| Input size             | 224                                  |
| Batch size             | 1024                                 |
| Training epochs        | 300                                  |
| Augmentation           | RandomResizedCrop                    |
| Min scale              | 0.08                                 |
| Max scale              | 1                                    |
| Temperature            | 20                                   |
| Optimizer              | AdamW                                |
| Learning rate          | 2e-3 for Resnet18, 1e-3 otherwise    |
| Weight decay           | 0.01                                 |
| Learning rate schedule | $\eta_{k+1} = \eta_k/4$ at epoch 250 |
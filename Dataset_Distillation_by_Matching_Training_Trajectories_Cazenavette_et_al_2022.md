# Dataset Distillation by Matching Training Trajectories

<span id="page-0-1"></span><PERSON><sup>1</sup> <PERSON><PERSON><sup>2</sup> <PERSON><sup>2</sup> <PERSON><sup>3</sup> <PERSON><PERSON><PERSON><sup>1</sup> <sup>1</sup>Carnegie Mellon University  $2^{\text{Mass}}$ achusetts Institute of Technology  $3^{\text{U}}C$  Berkeley

Image /page/0/Figure/2 description: The image displays a grid of images categorized into three datasets: CIFAR-100, Tiny ImageNet, and ImageNet. Each image within the grid is labeled with its corresponding object or scene name. The CIFAR-100 dataset includes images of an Apple, Camel, Clock, Fox, Kangaroo, Orange, Orchid, Pear, Pine Tree, and Tulip. The Tiny ImageNet dataset features images of a Guinea Pig, Goose, Guacamole, Hourglass, German Shepard, Red Panda, Potpie, Sewing Machine, Ladybug, and Triumphal Arch. The ImageNet dataset contains images of a Banana, Church, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> Horn, <PERSON> Retriever, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. The images are generally low-resolution and appear to be generated or representative samples from these datasets.

<span id="page-0-0"></span>Figure 1. Example distilled images from 32x32 CIFAR-100 (top), 64x64 Tiny ImageNet (middle), and 128x128 ImageNet subsets (bottom). Training a standard CNN using only such distilled images (as few as one per category) yields a trained model capable of test accuracy significantly better than previous methods of dataset distillation. Please see more results at [https://georgecazenavette.github.io/mtt-distillation/.](https://georgecazenavette.github.io/mtt-distillation/)

## Abstract

*Dataset distillation is the task of synthesizing a small dataset such that a model trained on the synthetic set will match the test accuracy of the model trained on the full dataset. In this paper, we propose a new formulation that optimizes our distilled data to guide networks to a similar state as those trained on real data across many training steps. Given a network, we train it for several iterations on our distilled data and optimize the distilled data with respect to the distance between the synthetically trained parameters and the parameters trained on real data. To efficiently obtain the initial and target network parameters for large-scale datasets, we pre-compute and store training trajectories of expert networks trained on the real dataset. Our method handily outperforms existing methods and also allows us to distill higher-resolution visual data.*

## 1. Introduction

In the seminal 2015 paper, Hinton et al. [\[15\]](#page-8-0) proposed *model distillation*, which aims to distill the knowledge of a complex model into a simpler one. *Dataset distillation*, proposed by Wang et al. [\[44\]](#page-9-0), is a related but orthogonal task: rather than distilling the model, the idea is to distill the dataset. As shown in Figure [2,](#page-1-0) the goal is to distill the knowledge from a large training dataset into a very small set of synthetic training images (as low as one image per class) such that training a model on the distilled data would give a similar test performance as training one on the original dataset. Dataset distillation has become a lively research topic in machine learning [\[2,](#page-8-1) [25,](#page-8-2) [26,](#page-8-3) [38,](#page-8-4) [45,](#page-9-1) [46,](#page-9-2) [47\]](#page-9-3) with various applications, such as continual learning, neural architecture search, and privacy-preserving ML. Still, the problem has so far been of mainly theoretical interest, since most prior methods focus on toy datasets, like MNIST and CIFAR, while struggling on real, higher-resolution images. In this work, we present a new approach to dataset distillation that not only outperforms previous work in performance, but is also applicable to large-scale datasets, as shown in Figure [1.](#page-0-0)

Unlike classical data compression, dataset distillation aims for a small synthetic dataset that still retains adequate task-related information so that models trained on it can generalize to unseen test data, as shown in Figure [2.](#page-1-0) Thus, the distilling algorithm must strike a delicate balance by heavily compressing information without completely obliterating

<span id="page-1-1"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: This diagram illustrates the concept of dataset distillation. On the left, a large 'Full training dataset' of size 50,000 is shown, represented by a grid of diverse images. An arrow labeled 'Train' points from this dataset to a neural network diagram. On the right, a smaller 'Distilled dataset' of size 10 is depicted, containing abstract, blurry images. Another arrow labeled 'Train' points from this distilled dataset to a similar neural network diagram. A curved arrow connects the outputs of the two neural networks, with the text 'Similar Test Performance' indicating that the model trained on the distilled dataset achieves comparable results to the one trained on the full dataset.

Figure 2. Dataset distillation aims to generate a small synthetic dataset for which a model trained on it can achieve a similar test performance as a model trained on the whole real train set.

the discriminative features. To do this, dataset distillation methods attempt to discover exactly which aspects of the real data are critical for learning said discrimination. Several methods consider end-to-end training [\[25,](#page-8-2) [26,](#page-8-3) [44\]](#page-9-0) but often require huge compute and memory and suffer from inexact relaxation  $[25, 26]$  $[25, 26]$  $[25, 26]$  or training instability of unrolling many iterations [\[23,](#page-8-5) [44\]](#page-9-0). To reduce the optimization difficulty, other methods [\[45,](#page-9-1) [47\]](#page-9-3) focus on short-range behavior, enforcing a single training step on distilled data to match that on real data. However, error may accumulate in evaluation, where the distilled data is applied over many steps. We confirm this hypothesis experimentally in Section [4.2.](#page-5-0)

To address the above challenges, we sought to directly imitate the long-range training dynamics of networks trained on real datasets. In particular, we match segments of parameter trajectories trained on synthetic data with segments of pre-recorded trajectories from models trained on real data and thus avoid being short-sighted (i.e., focusing on single steps) or difficult to optimize (i.e., modeling the full trajectories). Treating the real dataset as the gold standard for guiding the network's training dynamics, we can consider the induced sequence of network parameters to be an *expert trajectory*. If our distilled dataset were to induce a network's training dynamics to follow these expert trajectories, then the synthetically trained network would land at a place close to the model trained on real data (in the parameter space) and achieve similar test performance.

In our method, our loss function *directly* encourages the

**Dataset** random time step from a randomly chosen expert trajectory **Distillation** and train for several iterations on the *synthetic* dataset. Fidistilled dataset to guide the network optimization along a similar trajectory (Figure [3\)](#page-2-0). We first train a set of models from scratch on the real dataset and record their expert training trajectories. We then initialize a new model with a nally, we penalize the distilled data based on how far this synthetically trained network deviated from the expert trajectory and back-propagate through the training iterations. Essentially, we transfer the knowledge from many expert training trajectories to the distilled images.

Train coreset selection methods on standard datasets, including Extensive experiments show that our method handily outperforms existing dataset distillation methods as well as CIFAR-10, CIFAR-100, and Tiny ImageNet. For example, we achieve 46.3% with a *single* image per class and 71.5% with 50 images per class on CIFAR-10, compared to the previous state of the art (28.8% / 63.0% from [\[45,](#page-9-1) [46\]](#page-9-2) and 36.1% / 46.5% from  $[26]$ ). Furthermore, our method also generalizes well to larger data, allowing us to see high  $128 \times 128$ resolution images distilled from ImageNet [\[6\]](#page-8-6) for the first time. Finally, we analyze our method through additional ablation studies and visualizations. [Code](https://github.com/GeorgeCazenavette/mtt-distillation) and models are also available on our [webpage.](https://georgecazenavette.github.io/mtt-distillation/)

## 2. Related Work

Dataset Distillation. Dataset distillation was first introduced by Wang et al. [\[44\]](#page-9-0), who proposed expressing the model weights as a function of distilled images and optimized them using gradient-based hyperparameter optimization [\[23\]](#page-8-5), which is also widely used in meta-learning research [\[8,](#page-8-7) [27\]](#page-8-8). Subsequently, several works significantly improved the results by learning soft labels  $[2, 38]$  $[2, 38]$  $[2, 38]$ , amplifying learning signal via gradient matching [\[47\]](#page-9-3), adopting augmentations [\[45\]](#page-9-1), and optimizing with respect to the infinite-width kernel limit [\[25,](#page-8-2) [26\]](#page-8-3). Dataset distillation has enabled various applications including continual learning [\[44,](#page-9-0) [45,](#page-9-1) [47\]](#page-9-3), efficient neural architecture search [\[45,](#page-9-1) [47\]](#page-9-3), federated learning  $[11, 37, 50]$  $[11, 37, 50]$  $[11, 37, 50]$  $[11, 37, 50]$  $[11, 37, 50]$ , and privacy-preserving ML  $[22, 37]$  $[22, 37]$  $[22, 37]$  for images, text, and medical imaging data. As mentioned in the introduction, our method does not rely on single-step behavior matching [\[45,](#page-9-1) [47\]](#page-9-3), costly unrolling of full optimization trajectories [\[38,](#page-8-4) [44\]](#page-9-0), or large-scale Neural Tangent Kernel computation [\[25,](#page-8-2) [26\]](#page-8-3). Instead, our method achieves longrange trajectory matching by transferring the knowledge from pre-trained experts.

Concurrent with our work, the method of Zhao and Bilen [\[46\]](#page-9-2) completely disregards optimization steps, instead focusing on distribution matching between synthetic and real data. While this method is applicable to higher-resolution datasets (e.g., Tiny ImageNet) due to reduced memory requirements, it attains inferior performance in most cases (e.g., when compared to previous works  $[45, 47]$  $[45, 47]$  $[45, 47]$ ). In con-

<span id="page-2-2"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: This diagram illustrates a network optimization trajectory. It shows two paths: one for training on real data (expert) and another for training on distilled data. The real data path starts with parameters \( heta^\*\_{t-1}\), moves to \( heta^\*\_t\) (represented by a neural network), then to \( heta^\*\_{t+1}\), \( heta^\*\_{t+2}\), \( heta^\*\_{t+3}\), and so on, indicated by blue arrows. The distilled data path starts with a switch to distilled data at Iteration 1, leading to parameters \(\" heta\_{t+1}\"), \(\" heta\_{t+2}\"), and eventually \(\" heta\_{t+N}\") (highlighted in orange), indicated by orange arrows. The diagram also shows a constraint at Iteration t+M, where \( heta^\*\_{t+M}\) is linked to \(\" heta\_{t+N}\") with the text 'Constraint: Similar weights Loss(Dsyn) := Distance(\" heta\_{t+N}\", \( heta^\*\_{t+M}\))'. The legend clarifies that blue arrows represent training on real data (expert) and orange arrows represent training on distilled data. The text 'Iteration N \(\ll\) M' is also present.

Figure 3. We perform long-range parameter matching between training on distilled synthetic data and training on real data. Starting from the same initial parameters, we train distilled data  $\mathcal{D}_{syn}$  such that N training steps on them match the same result (in parameter space) from much more M steps on real data.

trast, our method simultaneously reduces memory costs while outperforming existing works [\[45,](#page-9-1) [47\]](#page-9-3) and the concurrent method [\[46\]](#page-9-2) on both standard benchmarks and higherresolution datasets.

A related line of research learns a generative model to synthesize training data [\[24,](#page-8-12) [36\]](#page-8-13). However, such methods do not generate a small-size dataset, and are thus not directly comparable with dataset distillation methods.

Imitation Learning. Imitation learning attempts to learn a good policy by observing a collection of expert demonstrations [\[29,](#page-8-14) [30,](#page-8-15) [31\]](#page-8-16). Behavior cloning trains the learning policy to act the same as expert demonstrations. Some more sophisticated formulations involve on-policy learning with labeling from the expert [\[33\]](#page-8-17), while other approaches avoid any label at all, e.g., via distribution matching [\[16\]](#page-8-18). Such methods (behavior cloning in particular) have been shown to work well in offline settings [\[9,](#page-8-19) [12\]](#page-8-20). Our method can be viewed as imitating a collection of expert network training trajectories, which are obtained via training on real datasets. Therefore, it can be considered as doing imitation learning over optimization trajectories.

Coreset and Instance Selection. Similar to dataset distillation, coreset  $[1, 4, 13, 34, 41]$  $[1, 4, 13, 34, 41]$  $[1, 4, 13, 34, 41]$  $[1, 4, 13, 34, 41]$  $[1, 4, 13, 34, 41]$  $[1, 4, 13, 34, 41]$  $[1, 4, 13, 34, 41]$  $[1, 4, 13, 34, 41]$  $[1, 4, 13, 34, 41]$  and instance selection  $[28]$  aim to select a subset of the entire training dataset, where training on this small subset achieves good performance. Most of such methods do not apply to modern deep learning, but new formulations based on bi-level optimization have shown promising results on applications like continual learning [\[3\]](#page-8-26). Related to coreset, other lines of research aim to understand which training samples are "valuable" for modern machine learning, including measuring single-example accuracy [\[20\]](#page-8-27) and counting misclassification rates [\[39\]](#page-9-6). In fact, dataset distillation is a generalization of such ideas, as the distilled data do not need to be realistic or come from the training set.

## 3. Method

*Dataset Distillation* refers to the curation of a small, synthetic training set  $\mathcal{D}_{syn}$  such that a model trained on this synthetic data will have similar performance on the real test set as a model trained on the large, real training set  $\mathcal{D}_{\text{real}}$ . In this section, we describe our method that directly mimics the long-range behavior of real-data training, matching multiple training steps on distilled data to *many* more steps on the real data.

In Section [3.1,](#page-2-1) we discuss how we obtain expert trajectories of networks trained on real datasets. In Section [3.2,](#page-3-0) we describe a new dataset distillation method that explicitly encourages the distilled dataset to induce similar long-range network parameter trajectories as the real dataset, resulting in a synthetically-trained network that performs similarly to a network trained on real data. Finally, Section [3.3](#page-3-1) describes our techniques to reduce memory consumption.

#### <span id="page-2-1"></span>3.1. Expert Trajectories

The core of our method involves using *expert trajectories*  $\tau^*$  to guide the distillation of our synthetic dataset. By expert trajectories, we mean the time sequence of parameters  $\{\theta_t^*\}_0^T$  obtained during the training of a neural network on the *full, real dataset*. To generate these expert trajectories, we simply train a large number of networks on the real dataset and save their snapshot parameters at every epoch. We call these sequences of parameters "expert trajectories" because they represent the theoretical upper bound for the dataset distillation task: the performance of a network trained on the full, real dataset. Similarly, we define student parameters  $\theta_t$ as the network parameters trained on synthetic images at the training step  $t$ . Our goal is to distill a dataset that will induce a similar trajectory (given the same starting point) as that

<span id="page-3-7"></span><span id="page-3-6"></span>

### Algorithm 1 Dataset Distillation via Trajectory Matching

<span id="page-3-3"></span>**Input:**  $\{\tau_i^*\}$ : set of expert parameter trajectories trained on  $\mathcal{D}_{\text{real}}$ . Input: M: # of updates between starting and target expert params. Input: N: # of updates to student network per distillation step. Input: A: Differentiable augmentation function. **Input:**  $T^+ < T$ : Maximum start epoch. 1: Initialize distilled data  $\mathcal{D}_{syn} \sim \mathcal{D}_{real}$ 2: Initialize trainable learning rate  $\alpha := \alpha_0$  for apply  $\mathcal{D}_{syn}$ 3: for each distillation step... do 4:  $\triangleright$  Sample expert trajectory:  $\tau^* \sim {\{\tau_i^*\}}$  with  $\tau^* = {\{\theta_t^*\}}_0^T$ 5:  $\triangleright$  Choose random start epoch,  $t \leq T^+$ 6:  $\triangleright$  Initialize student network with expert params: 7:  $\hat{\theta}_t := \theta_t^*$ 8: for  $n = 0 \rightarrow N - 1$  do 9:  $\triangleright$  Sample a mini-batch of distilled images: 10:  $b_{t+n} \sim \mathcal{D}_{\text{syn}}$ 11:  $\triangleright$  Update student network w.r.t. classification loss: 12:  $\hat{\theta}_{t+n+1} = \hat{\theta}_{t+n} - \alpha \nabla \ell(\mathcal{A}(b_{t+n}); \hat{\theta}_{t+n})$ 13: end for 14:  $\triangleright$  Compute loss between ending student and expert params: 15:  $\mathcal{L} = \|\hat{\theta}_{t+N} - \theta_{t+M}^*\|_2^2 \big/ \|\theta_t^* - \theta_{t+M}^*\|_2^2$ 16:  $\triangleright$  Update  $\mathcal{D}_{syn}$  and  $\alpha$  with respect to  $\mathcal{L}$ 17: end for **Output:** distilled data  $\mathcal{D}_{syn}$  and learning rate  $\alpha$ 

<span id="page-3-4"></span><span id="page-3-2"></span>induced by the real training set such that we end up with a similar model.

Since these expert trajectories are computed using only real data, we can pre-compute them before distillation. All of our experiments for a given dataset were performed using the same pre-computed set of expert trajectories, allowing for rapid distillation and experimentation.

#### <span id="page-3-0"></span>3.2. Long-Range Parameter Matching

Our distillation process learns from the generated sequences of parameters making up our expert trajectories  $\{\theta_t^*\}_{0}^T$ . Unlike previous work, our method directly encourages the long-range training dynamics induced by our synthetic dataset to match those of networks trained on the real data.

At each distillation step, we first sample parameters from one of our expert trajectories at a random timestep  $\theta_t^*$  and use these to initialize our student parameters  $\hat{\theta}_t := \theta_t^*$ . Placing an upper bound  $T^+$  on t lets us ignore the less informative later parts of the expert trajectories where the parameters do not change much.

With our student network initialized, we then perform N gradient descent updates on the student parameters with respect to the classification loss of the *synthetic* data:

$$
\hat{\theta}_{t+n+1} = \hat{\theta}_{t+n} - \alpha \nabla \ell(\mathcal{A}(\mathcal{D}_{\text{syn}}); \hat{\theta}_{t+n}), \quad (1)
$$

where  $\mathcal A$  is the differentiable augmentation technique [\[17,](#page-8-28) [40,](#page-9-7) [48,](#page-9-8) [49\]](#page-9-9) used in previous work [\[45\]](#page-9-1), and  $\alpha$  is the (trainable)

learning rate used to update the student network. Any data augmentation used during distillation must be differentiable so that we can back-propagate through the augmentation layer to our synthetic data. Our method does not use differentiable *Siamese* augmentation since there is no real data used during the distillation process; we are only applying the augmentations to synthetic data at this time. However, we do use the same types of differentiable augmentations on real data during the generation of the expert trajectories.

From this point, we return to our expert trajectory and retrieve the expert parameters from  $M$  training updates after those used to initialize the student network  $\theta_{t+M}^*$ . Finally, we update our distilled images according to the weight matching loss: i.e., the normalized squared  $L_2$  error between the updated student parameters  $\hat{\theta}_{t+N}$  and the known future expert parameters  $\theta_{t+M}^*$ :

<span id="page-3-5"></span>
$$
\mathcal{L} = \frac{\|\hat{\theta}_{t+N} - \theta_{t+M}^*\|_2^2}{\|\theta_t^* - \theta_{t+M}^*\|_2^2},\tag{2}
$$

where we normalize the  $L_2$  error by the expert distance traveled so that we still get a strong signal from later training epochs where the expert does not move as much. This normalization also helps self-calibrate the magnitude difference across neurons and layers. We have also experimented with other choices of loss functions such as a cosine distance, but find our simple  $L_2$  loss works better empirically. We also tried to match the network's output logits between expert trajectory and student network but did not see a clear improvement. We speculate that backpropagating from the network output to the weights introduces additional optimization difficulty.

We then minimize this objective to update the pixels of our distilled dataset, along with our trainable learning rate  $\alpha$ , by back-propagating through all N updates to the student network. The optimization of trainable learning rate  $\alpha$  serves as automatic adjusting for the number of student and expert updates (hyperparameters  $M$  and  $N$ ). We use SGD with momentum to optimize  $\mathcal{D}_{syn}$  and  $\alpha$  with respect to the above objective. Algorithm [1](#page-3-2) illustrates our main algorithm.

## <span id="page-3-1"></span>3.3. Memory Constraints

Given that we are back-propagating through many gradient descent updates, memory consumption quickly becomes an issue when our distilled dataset is sufficiently large, as we have to jointly optimize all the images of all the classes at each optimization step. To reduce memory consumption and ease the learning problem, previous methods distill one class at a time  $[45, 46, 47]$  $[45, 46, 47]$  $[45, 46, 47]$  $[45, 46, 47]$  $[45, 46, 47]$ , but this may not be an ideal strategy for our method since the expert trajectories are trained on all classes simultaneously.

We could potentially circumvent this memory constraint by sampling a new mini-batch at every distillation step (the

<span id="page-4-2"></span><span id="page-4-1"></span>

|               | Img/Cls Ratio $%$ |             | Random      | <b>Coreset Selection</b><br>Herding | Forgetting                                                                                                                                   | $DD$ <sup>†</sup> [44] | $LD$ <sup>†</sup> [2]    | DC [47]                                                                    | DSA [45]                                                       | Training Set Synthesis<br>DM [46]                 |                                                                                                                                                                         | CAFE $[43]$ CAFE+DSA $[43]$                        | <b>Full Dataset</b><br>Ours                                                         |
|---------------|-------------------|-------------|-------------|-------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------|------------------------|--------------------------|----------------------------------------------------------------------------|----------------------------------------------------------------|---------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------|-------------------------------------------------------------------------------------|
| CIFAR-10      | 10<br>50          | 0.02<br>0.2 |             |                                     | $14.4 \pm 2.0$ $21.5 \pm 1.2$ $13.5 \pm 1.2$<br>$26.0 \pm 1.2$ 31.6 $\pm$ 0.7 23.3 $\pm$ 1.0<br>$43.4 \pm 1.0$ $40.4 \pm 0.6$ $23.3 \pm 1.1$ | $\sim$                 |                          | $25.7 \pm 0.7$ $28.3 \pm 0.5$ $28.8 \pm 0.7$ $26.0 \pm 0.8$ $30.3 \pm 1.1$ |                                                                |                                                   | $36.8 \pm 1.2$ $38.3 \pm 0.4$ $44.9 \pm 0.5$ $52.1 \pm 0.5$ $48.9 \pm 0.6$ $46.3 \pm 0.6$<br>$42.5 \pm 0.4$ 53.9 $\pm$ 0.5 60.6 $\pm$ 0.5 63.0 $\pm$ 0.4 55.5 $\pm$ 0.6 | $31.6 \pm 0.8$<br>$50.9 \pm 0.5$<br>$62.3 \pm 0.4$ | $46.3 \pm 0.8^*$<br>$65.3 \pm 0.7$ <sup>*</sup><br>$84.8 \pm 0.1$<br>$71.6 \pm 0.2$ |
| $CIFAR-100$   | 10<br>50          | 0.2<br>10   |             |                                     | $4.2 \pm 0.3$ $8.4 \pm 0.3$ $4.5 \pm 0.2$<br>$14.6 \pm 0.5$ 17.3 $\pm$ 0.3 15.1 $\pm$ 0.3<br>$30.0 \pm 0.4$ 33.7 $\pm$ 0.5 30.5 $\pm$ 0.3    |                        | $\overline{\phantom{a}}$ | $11.5 \pm 0.4$ $12.8 \pm 0.3$ $13.9 \pm 0.3$ $11.4 \pm 0.3$ $12.9 \pm 0.3$ |                                                                |                                                   | $25.2 \pm 0.3$ 32.3 $\pm$ 0.3 29.7 $\pm$ 0.3 27.8 $\pm$ 0.3<br>$42.8 \pm 0.4$ $43.6 \pm 0.4$ $37.9 \pm 0.3$                                                             | $14.0 \pm 0.3$<br>$31.5 \pm 0.2$<br>$42.9 \pm 0.2$ | $24.3 \pm 0.3^*$<br>$40.1 \pm 0.4$<br>$56.2 \pm 0.3$<br>$47.7 \pm 0.2^*$            |
| Tiny ImageNet | 10<br>50          | 0.2<br>10   | $1.4 + 0.1$ | $2.8 \pm 0.2$                       | $1.6 + 0.1$<br>$5.0 \pm 0.2$ $6.3 \pm 0.2$ $5.1 \pm 0.2$<br>$15.0 \pm 0.4$ 16.7 $\pm$ 0.3 15.0 $\pm$ 0.3                                     |                        |                          | $\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$                       | $\sim$<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$ | $3.9 \pm 0.2$<br>$12.9 \pm 0.4$<br>$24.1 \pm 0.3$ |                                                                                                                                                                         |                                                    | $8.8 \pm 0.3$<br>$23.2 \pm 0.2$<br>$37.6 + 0.4$<br>$28.0 \pm 0.3$                   |

Table 1. Comparing distillation and coreset selection methods. As in previous work, we distill the given number of images per class using the training set, train a neural network on the synthetic set, and evaluate on the test set. To get  $\bar{x} \pm s$ , we train 5 networks from scratch on the distilled dataset. Note that the earlier works  $DD^{\dagger}$  and  $LD^{\dagger}$  use different architectures, i.e., LeNet [\[21\]](#page-8-29) for MNIST and AlexNet [\[19\]](#page-8-30) for CIFAR-10. All others use a 128-width ConvNet. CIFAR values marked by (∗) signify best results were obtained with ZCA whitening.

outer loop in Line [3](#page-3-3) of Algorithm [1\)](#page-3-2). Unfortunately, this comes with its own issues, as redundant information could be distilled into multiple images across the synthetic dataset, degrading to catastrophic mode collapse in the worst case.

Instead, we can sample a new mini-batch  $b$  for every update of the *student network* (i.e., the inner loop in Line [10](#page-3-4) of Algorithm [1\)](#page-3-2) such that all distilled images will have been seen by the time the final weight matching loss (Eqn. [2\)](#page-3-5) is calculated. The mini-batch  $b$  still contains images from different classes but has much fewer images per class. In this case, our student network update then becomes

$$
b_{t+n} \sim \mathcal{D}_{\text{syn}}
$$
  
\n
$$
\hat{\theta}_{t+n+1} = \hat{\theta}_{t+n} - \alpha \nabla \ell(\mathcal{A}(b_{t+n}); \hat{\theta}_{t+n}).
$$
\n(3)

This method of batching allows us to distill a much larger synthetic dataset while ensuring some amount of heterogeneity among the distilled images of the same class.

## 4. Experiments

We evaluate our method on various datasets, including

- $32 \times 32$  CIFAR-10 and CIFAR-100 (Section [4.1\)](#page-4-0), two commonly used datasets in dataset distillation literature,
- 64  $\times$  64 Tiny ImageNet (Section [4.3\)](#page-6-0), a recent benchmark by the concurrent work  $[46]$ , and
- our new  $128 \times 128$  ImageNet subsets (Section [4.4\)](#page-6-1).

We provide additional visualizations and ablation studies in the supplementary material.

Evaluation and Baselines. We evaluate various methods according to the standard protocol: training a randomly initialized neural network from scratch on *distilled* data and evaluating on the validation set.

To generate the distilled images for our method, we employ the distillation process detailed in the previous section and Algorithm [1,](#page-3-6) using the same suite of differentiable augmentations as done in previous work [\[45,](#page-9-1) [46\]](#page-9-2). The hyperparameters used for each setting (real epochs per iteration, synthetic updates per iteration, image learning rate, etc.) can be found in the supplemental material.

We compare to several recent methods including Dataset Distillation [\[44\]](#page-9-0) (DD), Flexible Dataset Distillation [\[2\]](#page-8-1) (LD), Dataset Condensation [\[47\]](#page-9-3) (DC), and Differentiable Siamese Augmentation  $[45]$  (DSA), along with a method based on the infnite-width kernel limit  $[25, 26]$  $[25, 26]$  $[25, 26]$  (KIP) and concurrent works Distribution Matching [\[46\]](#page-9-2) (DM) and Aligning Features [\[43\]](#page-9-10) (CAFE). We also compare our methods with instance selection algorithms including random selection (random), herding methods [\[4\]](#page-8-22) (herding), and example forgetting [\[39\]](#page-9-6) (forgetting).

Network Architectures. Staying with precedent [\[26,](#page-8-3) [45,](#page-9-1) [46,](#page-9-2) [47\]](#page-9-3), we mainly employ a simple ConvNet architecture designed by Gidaris and Komodakis [\[10\]](#page-8-31) for our distillation tasks. The architecture consists of several convolutional blocks, each containing a  $3 \times 3$  convolution layer with 128 filters, Instance normalization [\[42\]](#page-9-11), RELU, and  $2 \times 2$  average pooling with stride 2. After the convoluation blocks, a single linear layer produces the logits. The exact number of such blocks is decided by the dataset resolution and is specified below for each dataset. Staying with this simple architecture allows us to directly analyze the effectiveness of our core method and remain comparable with previous works.

#### <span id="page-4-0"></span>4.1. Low-Resolution Data (32×32)

For low-resolution tasks, we begin with the  $32\times32$ CIFAR-10 and CIFAR-100 datasets [\[18\]](#page-8-32). For these datasets, we employ ZCA whitening as done in previous work  $[25, 26]$  $[25, 26]$  $[25, 26]$ , using the Kornia [\[32\]](#page-8-33) implementation with default parameters. Staying with precedent, we use a depth-3 ConvNet taken directly from the open-source code [\[45,](#page-9-1) [47\]](#page-9-3).

As seen in Table [1,](#page-4-1) our method significantly outperforms all baselines in every setting. In fact, on the one image per class setting, we improve the next best method  $(DSA [45])$  $(DSA [45])$  $(DSA [45])$  to almost twice test accuracy, on both datasets. For CIFAR-10, these distilled images can be seen in Figure [4.](#page-5-1) CIFAR-100 images are visualized in the supplementary material

In Table [2,](#page-5-2) we also compare with a recent method KIP  $[25, 26]$  $[25, 26]$  $[25, 26]$ , where the distilled data is learned with respect to the Neural Tangent Kernel. Because KIP training is agnostic to actual network width, we test their result on both a ConvNet of the same width as us and other methods (128) and a ConvNet of larger width (1024) (which is shown

<span id="page-5-4"></span><span id="page-5-2"></span>

|           | Img/Cls | Ratio % | KIP to NN<br>(1024-width) | KIP to NN<br>(128-width) | Ours<br>(128-width) |
|-----------|---------|---------|---------------------------|--------------------------|---------------------|
| CIFAR-10  | 1       | 0.02    | 49.9                      | 38.3                     | <b>46.3</b>         |
|           | 10      | 0.1     | 62.7                      | 57.6                     | <b>65.3</b>         |
|           | 50      | 1       | 68.6                      | 65.8                     | <b>71.5</b>         |
| CIFAR-100 | 1       | 0.2     | 15.7                      | 18.2                     | <b>24.3</b>         |
|           | 10      | 2       | 28.3                      | 32.8                     | <b>39.4</b>         |

Table 2. Kernel Inducing Point (KIP) [\[26\]](#page-8-3) performs distillation using the infinite-width network limit. We consistently outperform KIP when evaluating on the same finite-width network, and almost always outperform KIP applied on wider networks.

<span id="page-5-3"></span>

| Method | Evaluation Model                 |                                  |                                  |                                  |
|--------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|
|        | ConvNet                          | ResNet                           | VGG                              | AlexNet                          |
| Ours   | <b><math>64.3 \pm 0.7</math></b> | <b><math>46.4 \pm 0.6</math></b> | <b><math>50.3 \pm 0.8</math></b> | <b><math>34.2 \pm 2.6</math></b> |
| DSA    | $52.1 \pm 0.4$                   | $42.8 \pm 1.0$                   | $43.2 \pm 0.5$                   | <b><math>35.9 \pm 1.3</math></b> |
| KIP    | $47.6 \pm 0.9$                   | $36.8 \pm 1.0$                   | $42.1 \pm 0.4$                   | $24.4 \pm 3.9$                   |

Table 3. Despite being trained for a specific architecture, our synthetic images do not seem to suffer from much over-fitting to that model. This is evaluated on CIFAR-10 with 10 images per class.

in KIP paper  $[26]$ ). Based on the the infinite-width network limit, KIP may exhibit a gap with practical finite-width networks. Our method does not suffer from this limitation and generally achieves better performance. In all settings, our method, trained on the 128-width network, outperforms KIP results evaluated on both widths, except for just one setting where KIP is applied on the much wider 1024-width network.

As noted in the previous methods  $[44]$ , we also see significant diminishing returns when allowing more images in our synthetic datasets. For instance, on CIFAR-10, we see an increase from 46.3% to 65.3% classification accuracy when increasing from 1 to 10 images per class, but only an increase from 65.3% to 71.5% when increasing the number of distilled images per class from 10 to 50.

If we look at the one image per class visualizations in Figure [4](#page-5-1) (top), we see very abstract, yet still recognizable, representations of each class. When we limit the task to just one synthetic image per class, the optimization is forced to squeeze as much of the class's distinguishing information as possible into just one sample. When we allow more images in which to disperse the class's information, the optimization has the freedom to spread the class's discriminative features among the multiple samples, resulting in a diverse set of structured images we see in Figure [4](#page-5-1) (bottom) (e.g., different types of cars and horses with different poses).

Cross-Architecture Generalization. We also evaluate how well our synthetic data performs on architectures different from the one used to distill it on the CIFAR-10, 1 image per class task. In Table [3,](#page-5-3) we show our baseline ConvNet performance and evaluate on ResNet [\[14\]](#page-8-34), VGG [\[35\]](#page-8-35), and AlexNet [\[19\]](#page-8-30).

For KIP, instead of the Kornia [\[32\]](#page-8-33) ZCA implementation, we use the authors' custom ZCA implementation for evaluation of their method. Our method is solidly the top per-

<span id="page-5-1"></span>

| Plane | Car | Bird | Cat | Deer | Dog | Frog | Horse | Ship | Truck |
|-------|-----|------|-----|------|-----|------|-------|------|-------|
|-------|-----|------|-----|------|-----|------|-------|------|-------|

1 image per class  $\Gamma$  mage per erass

Image /page/5/Picture/12 description: A grid of 100 images, arranged in 10 rows and 10 columns. Each image appears to be a generated or stylized representation of various objects and animals. The images include airplanes, cars, birds, cats, deer, dogs, horses, and boats. The overall aesthetic is somewhat abstract and painterly, with soft edges and blended colors. The grid is presented on a white background, and there is a line of text below the grid that reads "10 images per class".

10 images per class

Figure 4. CIFAR-10: The 1 image per class images are more abstract but also more information-dense while the 10 images per class images are more expressive and contain more structure.

former on all the transfer models except for AlexNet where we lie within one standard deviation of DSA. This could be attributed to our higher baseline performance, but it still shows that our method is robust to changes in architecture.

#### <span id="page-5-0"></span>4.2. Short-Range vs. Long-Range Matching

Unlike some prior works (DC and DSA), our method performs long-range parameter matching, where  $N$  training steps on distilled data match a much larger M steps on real data. Methods that optimize over entire training processes (e.g., DD and KIP) can be viewed as even longer range matching. However, their performances fall short of our method (e.g., in Table [1\)](#page-4-1), likely due to related instabilities or inexact approximations. Here, we experimentally confirm our hypothesis that long-range matching achieved by larger  $M$  and  $N$  in our method is superior to the short-range counterparts (such as small  $M$  and  $N$  and DSA).

In Figure [5](#page-6-2) (left), we evaluate our method on different settings of M and N. Really short-range matching (with  $N = 1$ ) and small M) generally exhibits worse performance than long-range matching, with the best performance attained when both  $N$  and  $M$  are relatively large. Furthermore, as we increase  $N$ , the power of  $N$  combined steps (on distilled data) becomes stronger and can approximate longer-range behavior, leading to the optimal  $M$  values shifting to greater values correspondingly.

In Figure [5](#page-6-2) (right), we evaluate our method and a short-

<span id="page-6-6"></span><span id="page-6-2"></span>Image /page/6/Figure/0 description: The image contains two line graphs. The left graph is titled "Validation Acc. %" on the y-axis and "M: Expert Steps" on the x-axis. It shows four lines representing different values of N: N=1 (blue circles), N=5 (red pentagons), N=10 (yellow triangles), and N=60 (green squares). The validation accuracy for N=1 decreases from approximately 15% at 200 steps to about 5% at 600 steps and beyond. For N=5, the accuracy starts at about 22% at 200 steps, drops to 20% at 400 steps, then to 14% at 600 steps, and further to 13% at 800 steps. For N=10 and N=60, the accuracy remains relatively stable between 20% and 24% across the range of expert steps. The right graph is titled "Param. Distance to Target" on the y-axis and "∆t: Expert Steps between Init. and Target" on the x-axis. It compares two methods: "Ours" (blue circles) and "DSA" (red pentagons). The DSA method shows an increasing parameter distance to the target, starting at approximately 0.68 at 500 steps, rising to 0.81 at 2000 steps, and reaching 0.83 at 3500 steps. The "Ours" method shows a more stable parameter distance, starting at about 0.65 at 500 steps, increasing slightly to 0.66 at 1000 steps, then to 0.68 at 2000 steps, and finally to 0.71 at 3500 steps.

Figure 5. CIFAR-100 (1 image / class). Left: Smaller M and N match shorter-range behavior, which performs worse than longerrange matching. Right: Over 1000 training steps on distilled data, we track the closest distance in parameter space (normalized MSE in Eqn. [2\)](#page-3-5) to a target set of parameters, obtained with  $\Delta_t$  training steps on real data. Matching long-range behavior, our method better approximate real data training for longer ranges (large  $\Delta_t$ ).

<span id="page-6-5"></span>

|              | ImageNette     | ImageWoof      | ImageFruit     | ImageMeow      | ImageSquawk    | ImageYellow    |
|--------------|----------------|----------------|----------------|----------------|----------------|----------------|
| 1 Img/Cls    | $47.7 \pm 0.9$ | $28.6 \pm 0.8$ | $26.6 \pm 0.8$ | $30.7 \pm 1.6$ | $39.4 \pm 1.5$ | $45.2 \pm 0.8$ |
| 10 Img/Cls   | $63.0 \pm 1.3$ | $35.8 \pm 1.8$ | $40.3 \pm 1.3$ | $40.4 \pm 2.2$ | $52.3 \pm 1.0$ | $60.0 \pm 1.5$ |
| Full Dataset | $87.4 \pm 1.0$ | $67.0 \pm 1.3$ | $63.9 \pm 2.0$ | $66.7 \pm 1.1$ | $87.5 \pm 0.3$ | $84.4 \pm 0.6$ |

Table 4. Applying our method to  $128 \times 128$  resolution ImageNet subsets. On this higher resolution, across various subsets, our method continues to produce high-quality distilled images.

range matching work (DSA) on their abilities to approximate real training behavior over short and long ranges. Starting from a set of initial parameters, we set the target parameters to be the result of  $\Delta_t$  training steps on real data (i.e., the longrange behavior that distilled data should mimic). A small (or large)  $\Delta_t$  means evaluating matching over a short (or long) range. For both methods, we test how close they can train the network (using distilled data) from the same initial parameters to the target parameters. DSA is only optimized to match short-range behavior, and thus errors may accumulate during longer training. Indeed, as  $\Delta_t$  grows larger, DSA fails to mimic the real data behavior over longer ranges. In comparison, our method is optimized for long-range matching and thus performs much better.

### <span id="page-6-0"></span>4.3. Tiny ImageNet $(64\times64)$

Introduced to the dataset distillation task by the concurrent work, Distribution Matching (DM) [\[46\]](#page-9-2), we also show the effectiveness of our algorithm on the 200 class,  $64\times64$ Tiny ImageNet [\[5\]](#page-8-36) dataset (a downscaled subset of ImageNet [\[6\]](#page-8-6)). To account for the higher image resolution, we move up to a depth-4 ConvNet, similar to DM [\[46\]](#page-9-2).

Most dataset distillation methods (other than DM) are unable to handle this larger resolution due to extensive memory or time requirement, as the DM authors also observed [\[46\]](#page-9-2). In Table [1,](#page-4-1) our method consistently outperforms the only viable such baseline, DM. Notably, on the 10 images per class task, our method improves the concurrent work DM from 12.9% and 23.2%. A subset of our results is shown in Figure [6.](#page-6-3) The supplementary material contains the rest of the images.

At 200 classes and  $64\times64$  resolution, Tiny ImageNet certainly poses a much harder task than previous datasets. Despite this, many of our distilled images are still recogniz-

<span id="page-6-3"></span>Image /page/6/Picture/9 description: The image displays a grid of ten images, arranged in two rows and five columns. The top row features a group of elephants, a swirling blue pattern, a person in a colorful dress, a lamp, and a close-up of a flower. The bottom row shows an interior scene with vertical lines, two pizzas, a glass teapot, and a teddy bear.

Figure 6. Selected samples distilled from Tiny ImageNet, one image per class. Despite the higher resolution, our method still produces high-fidelity images. (Can you guess which classes these images represent? Check your answers in the footnote!<sup>[1](#page-6-4)</sup>)

able, with a clear color, texture, or shape pattern.

#### <span id="page-6-1"></span>4.4. ImageNet Subsets (128×128)

Next, we push the boundaries of dataset distillation even further by running our method on yet higher resolution images in the form of  $128 \times 128$  subsets of ImageNet [\[6\]](#page-8-6). Again, due to the higher resolution, we increase the depth of our architecture and use a depth-5 ConvNet for the  $128\times128$ ImageNet subsets.

ImageNette (assorted objects) and ImageWoof (dog breeds) are existing subsets [\[7\]](#page-8-37) designed to be easy and hard to learn respectively. We also introduce ImageFruit (fruits), ImageMeow (cats), ImageSquawk (birds), and ImageYellow (yellow-ish things) to further illustrate our algorithm.

Similar to Tiny ImageNet, most dataset distillation baselines do not scale up to our ImageNet subset settings. As the code of DM [\[46\]](#page-9-2) is not publicly available now, we choose to only compare to the networks trained on the full dataset. We wish to show that our method transfers well to large images and still produces meaningful results at a higher resolution. Validation set accuracies are presented in Table [4.](#page-6-5)

While all of the generated images are devoid of highfrequency noise, the tasks still differ in the type of distilled image they induce. For tasks where all the classes have a similar structure but unique textures like ImageSquawk (Figure [7\)](#page-7-0), the distilled images may not have much structure but instead store discriminating information in the textures.

Conversely, for tasks where all classes have similar color or textures like ImageYellow (Figure [7\)](#page-7-0), the distilled images seem to diverge from their common trait and accentuate the structure or secondary color that makes them unique. Specifically, note the differences between the distilled "Banana" images for the ImageFruit and ImageYellow (bottom row, Figure [7\)](#page-7-0). Although the expert trajectory-generating networks saw the same "Banana" training images, the distilled images differ between the two tasks. The distilled "Banana" for the ImageYellow task is clearly much "greener" than the

<span id="page-6-4"></span><sup>&</sup>lt;sup>1</sup>Answers for Figure [6:](#page-6-3) First Row: African Elephant, Jellyfish, Kimono, Lampshade, Monarch Second Row: Organ, Pizza, Pretzel, Teapot, Teddy

<span id="page-7-1"></span><span id="page-7-0"></span>Image /page/7/Picture/0 description: This is a grid of images, organized into four categories: ImageNette, ImageWoof, ImageSquawk, ImageMeow, ImageFruit, and ImageYellow. Each category contains multiple rows and columns of images, with labels above and below each column. The top row of labels includes 'Cassette Player', 'Chainsaw', 'Church', 'English Springer', 'French Horn', 'Australian Terrier', 'Beagle', 'Border Terrier', 'Dingo', and 'English Foxhound'. The second row of labels includes 'Garbage Truck', 'Gas Pump', 'Golf Ball', 'Parachute', 'Tench', 'English Sheepdog', 'Golden Retriever', 'Rhodesian Ridgeback', 'Samoyed', and 'Shih-Tzu'. The third row of labels includes 'Bald Eagle', 'Black Swan', 'Cockatoo', 'Flamingo', 'Penguin', 'Bengal Cat', 'Egyptian Cat', 'Jaguar', 'Lion', and 'Lynx'. The fourth row of labels includes 'Macaw', 'Ostrich', 'Peacock', 'Pelican', 'Toucan', 'Persian Cat', 'Siamese Cat', 'Snow Leopard', 'Tabby Cat', and 'Tiger'. The fifth row of labels includes 'Banana', 'Bell Pepper', 'Cucumber', 'Fig', 'Green Apple', 'Banana', 'Bee', 'Corn', 'Garden Spider', and 'Goldfinch'. The bottom row of labels includes 'Lemon', 'Orange', 'Pineapple', 'Pomegranate', 'Strawberry', 'Honeycomb', 'Lady's Slipper', 'Lemon', 'Lion', and 'School Bus'. The images themselves appear to be generated or stylized representations of the objects or animals named in the labels.

Figure 7. Our method is the first capable of distilling higher-resolution ( $128 \times 128$ ) images, allowing us to explore the ImageNet [\[6\]](#page-8-6) dataset.

equivalent image for the ImageFruit task. This implies that the expert networks identify different features by which to identify classes based on the other classes in the task.

## 5. Discussion and Limitations

In this work, we introduced a dataset distillation algorithm by means of directly optimizing the synthetic data to induce similar network training dynamics as the real data. The main difference between ours and prior approaches is that we are neither limited to the short-range single-step matching nor subject to instability and compute intensity of optimizing over the full training process. Our method balances these two regimes and shows improvement over both.

Unlike prior methods, ours is the first to scale to  $128 \times 128$ ImageNet images, which not only allows us to gain interesting insights of the dataset (e.g., in Section [4.4\)](#page-6-1) but also may serve as an important step towards practical applications of dataset distillation on real-world datasets.

Limitations. Our use of pre-computed trajectories allows

for significant memory saving, at the cost of additional disk storage and computational cost for expert model training. The computational overhead of training and storing expert trajectories is quite high. For example, CIFAR experts took ∼3 seconds per epoch (8 GPU hours total for all 200 CIFAR experts) while each ImageNet (subset) expert took ∼11 seconds per epoch (15 GPU hours total for all 100 ImageNet experts). Storage-wise, each CIFAR expert took up ∼60MB of storage while each ImageNet expert took up ∼120MB.

Acknowledgements. We would like to thank Alexander Li, Assaf Shocher, Gokul Swamy, Kangle Deng, Ruihan Gao, Nupur Kumari, Muyang Li, Garuav Parmar, Chonghyuk Song, Sheng-Yu Wang, and Bingliang Zhang as well as Simon Lucey's Vision Group at the University of Adelaide for their valuable feedback. This work is supported, in part, by the NSF Graduate Research Fellowship under Grant No. DGE1745016 and grants from J.P. Morgan Chase, IBM, and SAP.

## References

- <span id="page-8-21"></span>[1] Olivier Bachem, Mario Lucic, and Andreas Krause. Practical coreset constructions for machine learning. *arXiv preprint arXiv:1703.06476*, 2017. [3](#page-2-2)
- <span id="page-8-1"></span>[2] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [1,](#page-0-1) [2,](#page-1-1) [5](#page-4-2)
- <span id="page-8-26"></span>[3] Zalán Borsos, Mojmir Mutny, and Andreas Krause. Coresets via bilevel optimization for continual learning and streaming. *Advances in Neural Information Processing Systems*, 33:14879–14890, 2020. [3](#page-2-2)
- <span id="page-8-22"></span>[4] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. In *UAI*, 2010. [3,](#page-2-2) [5](#page-4-2)
- <span id="page-8-36"></span>[5] cs231n.stanford.edu. Cs231n: Convolutional neural networks for visual recognition. [7](#page-6-6)
- <span id="page-8-6"></span>[6] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009. [2,](#page-1-1) [7,](#page-6-6) [8](#page-7-1)
- <span id="page-8-37"></span>[7] Fastai. Fastai/imagenette: A smaller subset of 10 easily classified classes from imagenet, and a little more french. [7](#page-6-6)
- <span id="page-8-7"></span>[8] Chelsea Finn, Pieter Abbeel, and Sergey Levine. Modelagnostic meta-learning for fast adaptation of deep networks. In *ICML*, 2017. [2](#page-1-1)
- <span id="page-8-19"></span>[9] Justin Fu, Aviral Kumar, Ofir Nachum, George Tucker, and Sergey Levine. D4rl: Datasets for deep data-driven reinforcement learning. *arXiv preprint arXiv:2004.07219*, 2020. [3](#page-2-2)
- <span id="page-8-31"></span>[10] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *CVPR*, 2018. [5](#page-4-2)
- <span id="page-8-9"></span>[11] Jack Goetz and Ambuj Tewari. Federated learning via synthetic data. *arXiv preprint arXiv:2008.04489*, 2020. [2](#page-1-1)
- <span id="page-8-20"></span>[12] Caglar Gulcehre, Ziyu Wang, Alexander Novikov, Tom Le Paine, Sergio Gómez Colmenarejo, Konrad Zolna, Rishabh Agarwal, Josh Merel, Daniel Mankowitz, Cosmin Paduraru, Gabriel Dulac-Arnold, Jerry Li, Mohammad Norouzi, Matt Hoffman, Ofir Nachum, George Tucker, Nicolas Heess, and Nando deFreitas. Rl unplugged: Benchmarks for offline reinforcement learning, 2020. [3](#page-2-2)
- <span id="page-8-23"></span>[13] Sariel Har-Peled and Akash Kushal. Smaller coresets for k-median and k-means clustering. *Discrete & Computational Geometry*, 37(1):3–19, 2007. [3](#page-2-2)
- <span id="page-8-34"></span>[14] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, 2016. [6](#page-5-4)
- <span id="page-8-0"></span>[15] Geoffrey Hinton, Oriol Vinyals, and Jeffrey Dean. Distilling the knowledge in a neural network. In *NIPS Deep Learning and Representation Learning Workshop*, 2015. [1](#page-0-1)
- <span id="page-8-18"></span>[16] Jonathan Ho and Stefano Ermon. Generative adversarial imitation learning. In *NeurIPS*, 2016. [3](#page-2-2)
- <span id="page-8-28"></span>[17] Tero Karras, Miika Aittala, Janne Hellsten, Samuli Laine, Jaakko Lehtinen, and Timo Aila. Training generative adversarial networks with limited data. In *NeurIPS*, 2020. [4](#page-3-7)
- <span id="page-8-32"></span>[18] Alex Krizhevsky and Geoffrey Hinton. Learning multiple layers of features from tiny images. Technical report, Citeseer, 2009. [5](#page-4-2)
- <span id="page-8-30"></span>[19] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. In *NeurIPS*, 2012. [5,](#page-4-2) [6](#page-5-4)
- <span id="page-8-27"></span>[20] Agata Lapedriza, Hamed Pirsiavash, Zoya Bylinskii, and Antonio Torralba. Are all training examples equally valuable?

*arXiv preprint arXiv:1311.6510*, 2013. [3](#page-2-2)

- <span id="page-8-29"></span>[21] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998. [5](#page-4-2)
- <span id="page-8-11"></span>[22] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Soft-label anonymous gastric x-ray image distillation. In *ICIP*, 2020. [2](#page-1-1)
- <span id="page-8-5"></span>[23] Dougal Maclaurin, David Duvenaud, and Ryan Adams. Gradient-based hyperparameter optimization through reversible learning. In *ICML*, 2015. [2](#page-1-1)
- <span id="page-8-12"></span>[24] Wojciech Masarczyk and Ivona Tautkute. Reducing catastrophic forgetting with learning on synthetic data. In *CVPR Workshops*, 2020. [3](#page-2-2)
- <span id="page-8-2"></span>[25] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *ICLR*, 2020. [1,](#page-0-1) [2,](#page-1-1) [5](#page-4-2)
- <span id="page-8-3"></span>[26] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021. [1,](#page-0-1) [2,](#page-1-1) [5,](#page-4-2) [6](#page-5-4)
- <span id="page-8-8"></span>[27] Alex Nichol, Joshua Achiam, and John Schulman. On first-order meta-learning algorithms. *arXiv preprint arXiv:1803.02999*, 2018. [2](#page-1-1)
- <span id="page-8-25"></span>[28] J Arturo Olvera-López, J Ariel Carrasco-Ochoa, J Francisco Martínez-Trinidad, and Josef Kittler. A review of instance selection methods. *Artificial Intelligence Review*, 34(2):133– 143, 2010. [3](#page-2-2)
- <span id="page-8-14"></span>[29] Takayuki Osa, Joni Pajarinen, Gerhard Neumann, J Andrew Bagnell, Pieter Abbeel, and Jan Peters. An algorithmic perspective on imitation learning. *arXiv preprint arXiv:1811.06711*, 2018. [3](#page-2-2)
- <span id="page-8-15"></span>[30] Xue Bin Peng, Pieter Abbeel, Sergey Levine, and Michiel van de Panne. Deepmimic: Example-guided deep reinforcement learning of physics-based character skills. *ACM Transactions on Graphics (TOG)*, 37(4):1–14, 2018. [3](#page-2-2)
- <span id="page-8-16"></span>[31] Xue Bin Peng, Ze Ma, Pieter Abbeel, Sergey Levine, and Angjoo Kanazawa. Amp: Adversarial motion priors for stylized physics-based character control. *ACM Transactions on Graphics (TOG)*, 40(4):1–20, 2021. [3](#page-2-2)
- <span id="page-8-33"></span>[32] E. Riba, D. Mishkin, D. Ponsa, E. Rublee, and G. Bradski. Kornia: an open source differentiable computer vision library for pytorch. In *WACV*, 2020. [5,](#page-4-2) [6](#page-5-4)
- <span id="page-8-17"></span>[33] Stéphane Ross, Geoffrey Gordon, and Drew Bagnell. A reduction of imitation learning and structured prediction to no-regret online learning. In *AISTATS*, 2011. [3](#page-2-2)
- <span id="page-8-24"></span>[34] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018. [3](#page-2-2)
- <span id="page-8-35"></span>[35] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [6](#page-5-4)
- <span id="page-8-13"></span>[36] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *ICML*, 2020. [3](#page-2-2)
- <span id="page-8-10"></span>[37] Ilia Sucholutsky and Matthias Schonlau. Secdd: Efficient and secure method for remotely training neural networks. *arXiv preprint arXiv:2009.09155*, 2020. [2](#page-1-1)
- <span id="page-8-4"></span>[38] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *IJCNN*, 2021. [1,](#page-0-1) [2](#page-1-1)

- <span id="page-9-6"></span>[39] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. In *ICLR*, 2018. [3,](#page-2-2) [5](#page-4-2)
- <span id="page-9-7"></span>[40] Ngoc-Trung Tran, Viet-Hung Tran, Ngoc-Bao Nguyen, Trung-Kien Nguyen, and Ngai-Man Cheung. Towards good practices for data augmentation in gan training. *IEEE Transactions on Image Processing*, 2020. [4](#page-3-7)
- <span id="page-9-5"></span>[41] Ivor W Tsang, James T Kwok, and Pak-Ming Cheung. Core vector machines: Fast svm training on very large data sets. *JMLR*, 6:363–392, 2005. [3](#page-2-2)
- <span id="page-9-11"></span>[42] Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*, 2016. [5](#page-4-2)
- <span id="page-9-10"></span>[43] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. *arXiv preprint arXiv:2203.01531*, 2022. [5](#page-4-2)
- <span id="page-9-0"></span>[44] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2,](#page-1-1) [5,](#page-4-2) [6](#page-5-4)
- <span id="page-9-1"></span>[45] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-2) [4,](#page-3-7) [5](#page-4-2)
- <span id="page-9-2"></span>[46] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-2) [4,](#page-3-7) [5,](#page-4-2) [7](#page-6-6)
- <span id="page-9-3"></span>[47] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2020. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-2) [4,](#page-3-7) [5](#page-4-2)
- <span id="page-9-8"></span>[48] Shengyu Zhao, Zhijian Liu, Ji Lin, Jun-Yan Zhu, and Song Han. Differentiable augmentation for data-efficient gan training. In *NeurIPS*, 2020. [4](#page-3-7)
- <span id="page-9-9"></span>[49] Zhengli Zhao, Zizhao Zhang, Ting Chen, Sameer Singh, and Han Zhang. Image augmentations for gan training. *arXiv preprint arXiv:2006.02595*, 2020. [4](#page-3-7)
- <span id="page-9-4"></span>[50] Yanlin Zhou, George Pu, Xiyao Ma, Xiaolin Li, and Dapeng Wu. Distilled one-shot federated learning. *arXiv preprint arXiv:2009.07999*, 2020. [2](#page-1-1)

## A. Appendix

## A.1. Additional Visualizations

We first include some additional visualizations here. CIFAR-100 (1 image per class) can be seen in Figure [11.](#page-11-0) All of Tiny ImageNet (1 image per class) is broken up into Figures [18](#page-14-0) and [19.](#page-15-0) We specifically show the 10 best and worst-performing distilled classes in Figures [12](#page-12-0) and [13](#page-12-1) respectively. We include 10 image per class visualizations of all our  $128 \times 128$  ImageNet subsets in Figures [20](#page-16-0)[-25.](#page-21-0)

#### A.2. Additional Quantitative Results

Analysis of learned learning rates  $\alpha$ . In Figure [10,](#page-10-0) we explore the effect of our learnable synthetic step size  $\alpha$ . The left plot confirms that we learn different values of  $\alpha$  for different combinations of  $M$  and  $N$ . The logic here is that different numbers of synthetic steps N require a different step size  $\alpha$ to cover the same distance as M real steps. The right plot illustrates the practical benefits of our adaptive learning rate; instead of yet another hyper-parameter to tune, our adaptive learning rate works from a wide range of initializations.

Effects of ZCA Whitening Note that DC, DSA, and DM do not use ZCA normalization, while KIP started using ZCA as it was a "crucial ingredient for [their] strong results." We report our results w/o ZCA in Figure [8](#page-10-1) (Left). We find that ZCA normalization is *not* critical to our performance. However, the expert models trained without ZCA normalization take significantly longer to converge. Thus, when distilling using these models as experts, we must use a larger value of  $T^+$  (and therefore save more model snapshots). When we use a larger value of  $T^+$  for non-ZCA distillations, we get results comparable to or even better than those of the ZCA distillations. In short, ZCA helps expert convergence but does not notably improve distillation performance.

<span id="page-10-1"></span>Image /page/10/Figure/6 description: The image contains a table and a line graph. The table compares the performance of "Yes-ZCA" and "No-ZCA" on CIFAR-10 and CIFAR-100 datasets with varying "Img/Cls" values (1, 10, 50). For CIFAR-10, "Yes-ZCA" achieved accuracies of 46.3%, 65.3%, and 71.5% for 1, 10, and 50 Img/Cls respectively, while "No-ZCA" achieved 45.2%, 62.8%, and 71.6%. For CIFAR-100, "Yes-ZCA" achieved 24.3%, 39.4%, and 47.7%, and "No-ZCA" achieved 22.7%, 40.1%, and 47.2%. The line graph, titled "CIFAR-10, 10 img/cls", plots "Validation Acc. %" against "Distilling Time (mins)". The graph shows two lines: "Ours" (blue) and "DSA" (red). The "Ours" line starts at approximately 60% accuracy at 0 minutes, rises to about 64% at 10 minutes, and then fluctuates between 64% and 66% until 80 minutes. The "DSA" line starts at approximately 30% accuracy at 0 minutes, rises to about 48% at 10 minutes, and then slowly increases to about 50% by 40 minutes, remaining around 50% until 80 minutes.

Figure 8. Left: ZCA Ablation. Right: Distillation Time.

#### A.2.1 Additional Ablation Studies

Initialization, normalization, and augmentation. In the main paper, we show ablations over several hyperparameters. Here, we study the role of initialization, data normalization, and data augmentation for CIFAR-100 (1 image per class) in Table [5.](#page-10-2) For initialization in particular, recall that we typically initialize our synthetic images with real samples. Here, we evaluate initializing with Gaussian

<span id="page-10-3"></span>Image /page/10/Figure/10 description: The image contains two line graphs side-by-side, both titled "CIFAR-100 (1 Image / Class)". The left graph plots "Validation Acc. %" on the y-axis against "# Expert Trajectories" on the x-axis. The line starts at approximately 14% at 0 trajectories, rises sharply to about 20% at 10 trajectories, then to 22% at 20 trajectories, and continues to increase slightly to around 24% at 50 trajectories, eventually plateauing at approximately 24.5% from 100 to 500 trajectories. The right graph plots "Validation Acc. %" on the y-axis against "T+: Max Start Epoch" on the x-axis. The line starts at approximately 17% at 0 epochs, rises to about 19.5% at 5 epochs, then to 22% at 10 epochs, peaking at around 23.5% at 15 epochs, and then shows a fluctuating trend, decreasing to about 21% at 25 epochs, then rising slightly to 22% at 35 epochs, and finally decreasing to about 20% at 45 epochs.

Figure 9. Left: We see logarithmic performance improvement with respect to the number of expert trajectories used, quickly saturating near 200. Right: The upper bound on the expert epoch at which the synthetic data starts working cannot be too high or low to ensure quality learning signal.

<span id="page-10-0"></span>Image /page/10/Figure/12 description: The image contains two plots related to CIFAR-100 with 1 image per class. The left plot is a line graph titled "α: Learned Step Size" versus "M: Expert Steps". The x-axis ranges from 200 to 800, and the y-axis ranges from 0.00 to 0.15. There are two lines: a yellow line labeled "N=10" showing values around 0.03, 0.05, 0.075, 0.09, and 0.105 for M values of 200, 400, 600, 800, and 900 respectively. A green line labeled "N=60" shows values around 0.01, 0.02, 0.025, and 0.035 for M values of 200, 400, 600, and 800 respectively. The right plot is a bar chart titled "Validation Acc. %" versus "α0: Initial Synthetic Step Size". The x-axis shows values of 0.0001, 0.001, 0.01, and 0.1. The y-axis ranges from 0 to 25. There are two sets of bars for each x-axis value: blue bars labeled "Learned" and red bars labeled "Fixed". For "Learned", the approximate heights are 23, 23, 23, and 23 for the respective x-axis values. For "Fixed", the approximate heights are 3, 3, 10, and 22 for the respective x-axis values.

Figure 10. Left: Our learned synthetic step size  $\alpha$  seems to scale inversely with the number of synthetic steps  $N$  to cover the same distance in parameter space as the expert steps  $M$ . Right: Having a learnable step size  $\alpha$  saves us from having to search for an appropriate fixed  $\alpha_0$ .

noise instead. Visualizations of these distilled sets can be seen in Figures [14](#page-13-0)[-16.](#page-13-1) We also include a visualization of a set distilled with only one expert trajectory in Figure [17.](#page-13-2)

<span id="page-10-2"></span>

|  |                                                                  | Setting   Ours Gauss. Init. w/o ZCA w/o Diff. Aug. |
|--|------------------------------------------------------------------|----------------------------------------------------|
|  | Acc. $24.3 \pm 0.3$ $22.9 \pm 0.4$ $19.1 \pm 0.6$ $20.7 \pm 0.7$ |                                                    |

Table 5. As we ablate our categorical hyper-parameters, we still achieve state-of-the-art performance (compared to DSA: 13.9%). This is evaluated on CIFAR-100 with 1 image per class. Each design choice in our final method improves the performance of distilled images. Here we use the default set of hyper-parameters for these ablations.

Performance w.r.t. the number of expert trajectories. Since they effectively make up our method's "training set," it is reasonable to assume that having more expert trajectories would lead to better performance. We see that this is indeed the case for the CIFAR-100, 1 image per class setting in Figure [9](#page-10-3) (left). However, what's most interesting is the sharp, logarithmic increase in validation accuracy w.r.t. the number of experts. We note the most amount of improvement when increasing from 1 to 20 experts but see almost complete saturation by the time we reach 200. Given how high-dimensional the parameter space of a neural network is, it is remarkable that we can achieve such high performance with so few expert trajectories.

Performance w.r.t. expert time-step range. When we

<span id="page-11-0"></span>Image /page/11/Picture/0 description: The image displays a grid of 8x8 images, totaling 64 individual images. Each image is a small, square, and appears to be a sample from a dataset, possibly related to machine learning or image recognition. The images themselves are varied, depicting a range of objects and scenes, including animals (like elephants, fish, and a wolf), plants (flowers, trees, grass), vehicles (a bicycle, a motorcycle), buildings, and abstract patterns. The overall impression is a collage of diverse visual content, with each small image having a slightly blurry or stylized appearance, suggesting they might be generated or processed samples.

Figure 11. CIFAR-100, 1 Image Per Class

initialize our student networks, we do so at a randomly selected time-step from an expert trajectory. We find that it is important to put an upper bound on this starting time-step (Figure [9,](#page-10-3) right). If the upper bound is too high, the synthetic data receives gradients from points where the experts movements are small and uninformative. If it is too low, the synthetic data is never exposed to mid and later points in the trajectories, missing out on a significant portion of the training dynamics.

## A.3. Experiment Details

Hyper-Parameters. In Table [6,](#page-11-1) we enumerate the hyperparameters used for our results reported in the main text. Limited compute forced us to batch our synthetic data for some of the larger sets. The "ConvNet" architectures are as explained in the main text.

Compute resources. We had a relatively limited compute budget for our experiments, using any GPU we could access. As such, our experiments were run on a mixture of RTX2080ti, RTX3090, and RTX6000 GPUs. The largest amount of VRAM we used for a single experiment was 144GB over 6xRTX6000 GPUs.

Training Time. Distillation time varied based on dataset and type and number of GPUs used. Regardless of dataset or compute resources, time per distillation iteration scaled linearly with the number of synthetic steps N. For CIFAR-100, 1 image per class with  $N = 20$ , we had an average time of 0.6 seconds per distillation step when using a single RTX3090. We ran our experiments for 10000 distillation steps but saw the most improvement within the first 1000.

<span id="page-11-1"></span>

| Dataset                  | Model                 | Img/Cls  | Synthetic<br><b>Steps</b><br>(N) | Epochs<br>$(M^{\dagger})$                          | Expert Max Start<br>Epoch<br>$(T^+)$ | Synthetic<br>Batch Size ZCA<br>( b ) |             |
|--------------------------|-----------------------|----------|----------------------------------|----------------------------------------------------|--------------------------------------|--------------------------------------|-------------|
| $CIFAR-10$               | ConvNet <sub>D3</sub> | 10<br>50 | 50<br>30<br>30                   | $\overline{c}$<br>$\overline{2}$<br>$\mathfrak{D}$ | $\overline{c}$<br>20<br>40           |                                      | Y<br>Y<br>N |
| $CIFAR-100$              | ConvNet <sub>D3</sub> | 10<br>50 | 20<br>20<br>80                   | 3<br>$\overline{2}$<br>$\mathfrak{D}$              | 20<br>20<br>40                       |                                      | Y<br>N<br>Y |
| Tiny ImageNet ConvNetD4  |                       | 10<br>50 | 10<br>20<br>20                   | $\overline{c}$<br>$\overline{2}$<br>$\mathfrak{D}$ | 10<br>40<br>40                       | 200<br>300                           |             |
| ImageNet (All) ConvNetD5 |                       | 10       | 20<br>20                         | $\overline{c}$<br>$\overline{c}$                   | 10<br>10                             | 20                                   |             |

Table 6. Hyper-parameters used for our best-performing distillation experiments. A synthetic batch size of "-" indicates that we used the full support set at each synthetic step. Note: instead of number of expert *updates* (M), here we list number of expert *epochs* (M† ) for simplicity across datasets.

Our distillation time, in general, is comparable to DC/DSA, as they also utilize a bi-level optimization. In the 10 img/class setting (for example), DC/DSA trains on the synthetic data for 50 epochs on the between each update. We include a sample distillation curve in Figure [8](#page-10-1) (Right). Both experiments were run on RTX3090. Note that KIP requires over 1,000 GPU hours.

Regarding the distillation time for learning different sets on CIFAR10/100 and TinyImageNet, we report them in Table [7.](#page-11-2) Note that most improvement occurs within the first 1k iterations, but we continue training for 10k.

<span id="page-11-2"></span>

| Dataset       | Img/Cls | 1 Iter.<br>(sec) | 1k Iter.<br>(min) | 5k Iter.<br>(min) | 10k Iter.<br>(min) |
|---------------|---------|------------------|-------------------|-------------------|--------------------|
| CIFAR-10      | 1       | 0.5              | 8                 | 42                | 83                 |
| CIFAR-10      | 10      | 0.6              | 10                | 50                | 100                |
| CIFAR-10      | 50      | 0.8              | 13                | 67                | 133                |
| CIFAR-100     | 1       | 0.6              | 10                | 50                | 100                |
| CIFAR-100     | 10      | 0.8              | 13                | 67                | 133                |
| CIFAR-100     | 50      | 1.9              | 32                | 158               | 317                |
| Tiny ImageNet | 1       | 1.1              | 18                | 92                | 183                |
| Tiny ImageNet | 10      | 2.3              | 38                | 192               | 383                |
| Tiny ImageNet | 50      | 2.6              | 43                | 217               | 433                |

Table 7. Distillation time for each dataset and support size.

KIP to NN In the KIP paper, results are presented for images distilled using the neural tangent kernel method and then evaluated by training a modified width-1024 ConvNetD3. Aside from the increased width of the finite model, the ConvNet architecture used in the KIP paper also has an additional 1-layer convolutional stem.

Using the training notebook provided with the KIP paper, we perform an exhaustive search over a reasonable set of hyper-parameters for the KIP to width-128 NN problem: checkpoint  $\in \{112, 335, 1000\}$ , weight\_decay ∈ {0, 0.0001, 0.001, 0.01}, aug ∈ {True, False}, zca ∈ {True, False}, label learn ∈ {True, False}, and norm ∈ {none, instance}. The architecture originally used for KIP to NN in the KIP paper contained no normaliza-

<span id="page-12-2"></span>

|           | Img/Cls | Learn<br>Labels | Aug. | ZCA | Norm | Weight<br>Decay | Ckpt. |
|-----------|---------|-----------------|------|-----|------|-----------------|-------|
| CIFAR-10  | 1       | N               | Y    | Y   | N    | 0.001           | 1000  |
|           | 10      | N               | N    | Y   | N    | 0.001           | 112   |
|           | 50      | N               | N    | Y   | I    | 0.01            | 112   |
| CIFAR-100 | 1       | N               | N    | Y   | I    | 0.001           | 1000  |
|           | 10      | N               | N    | N   | I    | 0.001           | 1000  |

Table 8. Optimal hyper-parameters for our reported width-128 KIP to NN results. These were obtained via grid search using the notebook provided by the KIP authors.

<span id="page-12-0"></span>Image /page/12/Figure/2 description: The image displays a grid of ten images, each labeled with a word. The top row, from left to right, is labeled "Bison", "Black Widow", "Espresso", "Freight Car", and "Goldfish". The bottom row, from left to right, is labeled "Lifeboat", "Police Van", "School Bus", "Sea Slug", and "Trollybus". Each image in the grid appears to be a visualization or representation related to the corresponding label, possibly generated by a machine learning model.

Figure 12. Most-correct distilled images for Tiny ImageNet ( $\geq 30\%$ )

<span id="page-12-1"></span>Image /page/12/Picture/4 description: The image displays a grid of ten images, arranged in two rows and five columns. Each image is labeled with a word above or below it. The top row is labeled with 'Bannister', 'Birdhouse', 'Boa Constrictor', 'Bucket', and 'Dumbell' from left to right. The bottom row is labeled with 'Grasshopper', 'Labrador', 'Plunger', 'Pop Bottle', and 'Standard' from left to right. The images themselves appear to be abstract visualizations, possibly generated by a neural network, with various colors and patterns.

Retriever Poodle

Figure 13. Least-correct distilled images for Tiny ImageNet ( $\leq 4\%$ )

tion layers. However, we found that with the smaller width, this model could not even converge on the synthetic training data for CIFAR-100, so we added instance normalization layers as found in the ConvNets we and DC, DSA, and DM use.

In Table [8,](#page-12-2) we include the optimal hyper-parameters from this search that were used to obtain the KIP to NN (128-width) values reported in the main text.

<span id="page-13-0"></span>Image /page/13/Picture/0 description: A grid of 100 images, arranged in 10 rows and 10 columns. Each image is a small, blurry square, likely representing a class from a dataset. The images depict a variety of objects and scenes, including animals (like a dog, cat, deer, wolf), vehicles (like a bicycle, motorcycle), food items (like an apple, pear, sunflower), plants (like trees, flowers), buildings, and abstract patterns. The overall impression is a collage of diverse visual elements, possibly generated or visualized from a machine learning model.

Figure 14. CIFAR-100, Initialized as Random Noise

<span id="page-13-1"></span>Image /page/13/Picture/2 description: The image is a grid of 8x8 images, each displaying a different object or scene. The objects are varied and include animals, plants, food items, and everyday objects. Some of the recognizable items include an apple, a bottle, a bicycle, a tree, a flower, a rose, a sunflower, a strawberry, a tiger, a wolf, and a person's face. The images appear to be generated or stylized, with a soft, somewhat abstract quality.

Figure 16. CIFAR-100, No Differentiable Augmentation

Image /page/13/Picture/4 description: A grid of 100 images, arranged in 10 rows and 10 columns. Each image is a small, blurry representation of a common object or animal. The images appear to be generated by a machine learning model, possibly for visualization or data augmentation purposes. Some recognizable subjects include a red apple, a goldfish, a bicycle, a motorcycle, a lion, a sunflower, a tulip, a pine tree, and a wolf.

Figure 15. CIFAR-100, No ZCA Whitening

<span id="page-13-2"></span>Image /page/13/Picture/6 description: A grid of 100 small square images, arranged in 10 rows and 10 columns. Each small image is a colorful, abstract pattern or texture. The colors vary widely, including reds, blues, greens, purples, oranges, and yellows. Some images appear to have swirling patterns, while others have more geometric or textured appearances. The overall impression is a mosaic of diverse visual elements.

Figure 17. CIFAR-100, Only 1 Expert Trajectory

<span id="page-14-0"></span>Image /page/14/Picture/0 description: The image is a grid of 100 small images, arranged in 10 rows and 10 columns. Each small image is a colorful, abstract visualization, likely representing features or patterns learned by a neural network. The overall impression is a mosaic of diverse, often surreal, visual elements.

Figure 18. Tiny ImageNet, 1 Image Per Class (Classes 1-100)

<span id="page-15-0"></span>Image /page/15/Picture/0 description: This is a grid of 100 images, arranged in 10 rows and 10 columns. Each image is a small square with a black border. The images appear to be visualizations of different classes or concepts, possibly from a machine learning model, as they are abstract and varied in color and pattern. Some images show recognizable shapes like animals (penguins, elephants, cats), objects (bottles, cups), or natural elements (flowers, water, grass), while others are highly abstract patterns of colors and textures. The overall impression is a diverse collection of visual representations.

Figure 19. Tiny ImageNet, 1 Image Per Class (Classes 101-200)

<span id="page-16-0"></span>Image /page/16/Picture/0 description: This is a grid of 60 images, arranged in 10 rows and 6 columns. Each image appears to be a generated visualization, possibly from a machine learning model, with abstract patterns and colors. Some images show what might be interpreted as objects or scenes, such as blurry figures resembling animals, architectural structures, or natural landscapes, but they are highly stylized and abstract. The overall impression is a collection of diverse, digitally rendered artistic or experimental images.

Figure 20. ImageNette, 10 Images Per Class

Image /page/17/Picture/0 description: The image is a grid of 60 images, arranged in 6 rows and 10 columns. Each image appears to be a generated image of a dog's face or a dog in a natural setting. The images vary in quality and style, with some appearing more realistic than others. The overall impression is a collection of AI-generated dog images.

Figure 21. ImageWoof, 10 Images Per Class

Image /page/18/Picture/0 description: This is a grid of 60 small images, arranged in 6 rows and 10 columns. Each small image appears to be a generated image of a bird or a group of birds. The first row shows eagles, some in flight and some perched. The second row shows black swans swimming. The third row shows white birds, possibly cockatoos or swans. The fourth row shows flamingos. The fifth row shows penguins. The sixth row shows colorful birds, possibly parrots or toucans. The images are somewhat abstract and stylized, with a focus on color and form rather than photorealistic detail. The overall impression is a diverse collection of avian imagery.

Figure 22. ImageSquawk, 10 Images Per Class

Image /page/19/Figure/0 description: This is a grid of 60 images, arranged in 6 rows and 10 columns. Each image appears to be a stylized or abstract representation of a cat's face, with varying colors and textures. Some images show more distinct feline features like ears and eyes, while others are more abstract and colorful, with swirling patterns and vibrant hues. The overall impression is a collection of artistic interpretations of cats.

Figure 23. ImageMeow, 10 Images Per Class

Image /page/20/Figure/0 description: The image is a grid of 10 rows and 10 columns of small images, totaling 100 images. Each small image appears to be a generated or processed image of fruit. The rows seem to represent different classes or categories of fruit, with variations within each row. The colors and textures vary across the rows, suggesting different types of fruits such as yellow/orange fruits, red fruits, green fruits, and fruits with a spiky texture. The overall arrangement suggests a visualization of generated images categorized by fruit type.

Figure 24. ImageFruit, 10 Images Per Class

<span id="page-21-0"></span>Image /page/21/Figure/0 description: The image is a grid of 60 smaller images, arranged in 10 rows and 6 columns. Each smaller image appears to be a visualization of a neural network's understanding of a particular object or concept. The top rows show abstract patterns with hints of floral or leafy textures in various colors like green, yellow, pink, and purple. The middle rows transition to more distinct shapes, with some images clearly depicting birds, possibly finches, with yellow and black plumage, perched on branches. Other rows show what appear to be abstract representations of animals, possibly lions or big cats, with textured fur and facial features. The bottom rows display scenes that resemble landscapes, with water, reflections, and possibly buildings or structures, rendered in yellow and blue hues. The overall impression is a collection of feature visualizations from a deep learning model, showcasing its learned representations across different categories.

Figure 25. ImageYellow, 10 Images Per Class